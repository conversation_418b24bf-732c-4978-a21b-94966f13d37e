import React, { useState } from 'react';
import {
  X,
  Shield,
  Building2,
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  <PERSON><PERSON>,
  Eye,
  FileText
} from 'lucide-react';
import Modal from '../ui/Modal';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';
import { formatAmount } from '../../utils/formatUtils';
import { SubscriptionCode, SubscriptionCodeService } from '../../services/subscriptionCodeService';

interface SubscriptionCodeModalProps {
  code: SubscriptionCode | null;
  isOpen: boolean;
  onClose: () => void;
  onValidate: (codeId: string, reason?: string) => void;
  onReject: (codeId: string, reason: string) => void;
}

const SubscriptionCodeModal: React.FC<SubscriptionCodeModalProps> = ({
  code,
  isOpen,
  onClose,
  onValidate,
  onReject
}) => {
  const [action, setAction] = useState<'validate' | 'reject' | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const predefinedReasons = [
    'Informations d\'entreprise incomplètes',
    'Documents de vérification manquants',
    'Activité commerciale non conforme',
    'Tentative de fraude détectée',
    'Code généré en erreur',
    'Demande de l\'entreprise',
    'Vérifications supplémentaires requises',
    'Autre (préciser ci-dessous)'
  ];

  const handleSubmit = async () => {
    if (!code || !action) return;

    if (action === 'reject' && !rejectionReason.trim()) {
      alert('Veuillez préciser la raison du rejet.');
      return;
    }

    setIsSubmitting(true);
    try {
      if (action === 'validate') {
        await onValidate(code.id);
      } else {
        await onReject(code.id, rejectionReason);
      }
      onClose();
      resetForm();
    } catch (error) {
      console.error('Erreur lors du traitement:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setAction(null);
    setRejectionReason('');
  };

  const copyCodeToClipboard = () => {
    if (code) {
      navigator.clipboard.writeText(code.code);
      alert('Code copié dans le presse-papiers !');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'validated':
        return 'text-green-600 bg-green-100';
      case 'rejected':
        return 'text-red-600 bg-red-100';
      case 'expired':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock size={16} />;
      case 'validated':
        return <CheckCircle size={16} />;
      case 'rejected':
        return <XCircle size={16} />;
      case 'expired':
        return <AlertTriangle size={16} />;
      default:
        return <AlertTriangle size={16} />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'validated':
        return 'Validé';
      case 'rejected':
        return 'Rejeté';
      case 'expired':
        return 'Expiré';
      default:
        return status;
    }
  };

  const getPlanLabel = (planType: string) => {
    switch (planType) {
      case 'trial':
        return 'Essai gratuit (7 jours)';
      case 'monthly':
        return 'Mensuel (30 jours)';
      case 'yearly':
        return 'Annuel (365 jours)';
      default:
        return planType;
    }
  };

  const isExpired = () => {
    return code ? SubscriptionCodeService.isCodeExpired(code) : false;
  };

  const getTimeUntilExpiration = () => {
    return code ? SubscriptionCodeService.getTimeUntilExpiration(code) : '';
  };

  if (!code) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <div className="p-6">
        {/* En-tête */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-full">
              <Shield className="text-blue-600" size={20} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Code d'abonnement</h2>
              <p className="text-gray-600">Validation de sécurité requise</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="space-y-6">
          {/* Informations du code */}
          <Card>
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Détails du code</h3>
                <span className={`inline-flex items-center px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(code.status)}`}>
                  {getStatusIcon(code.status)}
                  <span className="ml-1">{getStatusLabel(code.status)}</span>
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div>
                    <div className="text-sm text-gray-600">Code de validation</div>
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl font-mono font-bold text-blue-600">
                        {SubscriptionCodeService.formatCode(code.code)}
                      </span>
                      <button
                        onClick={copyCodeToClipboard}
                        className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                        title="Copier le code"
                      >
                        <Copy size={16} />
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm text-gray-600">Entreprise</div>
                    <div className="flex items-center space-x-2">
                      <Building2 size={16} className="text-gray-400" />
                      <span className="font-medium text-gray-900">{code.business_name}</span>
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">Type d'abonnement</div>
                    <div className="font-medium text-gray-900">{getPlanLabel(code.plan_type)}</div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <div className="text-sm text-gray-600">Montant équivalent</div>
                    <div className="flex items-center space-x-2">
                      <DollarSign size={16} className="text-gray-400" />
                      <span className="text-xl font-bold text-green-600">{formatAmount(code.amount)}</span>
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">Généré le</div>
                    <div className="flex items-center space-x-2">
                      <Calendar size={16} className="text-gray-400" />
                      <span className="font-medium text-gray-900">
                        {new Date(code.generated_at).toLocaleDateString('fr-FR', {
                          day: '2-digit',
                          month: 'long',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">Expiration</div>
                    <div className="flex items-center space-x-2">
                      <Clock size={16} className={isExpired() ? 'text-red-400' : 'text-gray-400'} />
                      <span className={`font-medium ${isExpired() ? 'text-red-600' : 'text-gray-900'}`}>
                        {isExpired() ? 'Expiré' : `Dans ${getTimeUntilExpiration()}`}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Informations de validation/rejet */}
          {(code.status === 'validated' || code.status === 'rejected') && (
            <Card>
              <CardBody className="p-4">
                <h4 className="font-medium text-gray-900 mb-3">
                  {code.status === 'validated' ? 'Informations de validation' : 'Informations de rejet'}
                </h4>
                <div className="space-y-2 text-sm">
                  {code.validated_at && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Date de traitement:</span>
                      <span className="font-medium">
                        {new Date(code.validated_at).toLocaleDateString('fr-FR', {
                          day: '2-digit',
                          month: 'long',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>
                  )}
                  {code.validated_by && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Traité par:</span>
                      <span className="font-medium">{code.validated_by}</span>
                    </div>
                  )}
                  {code.rejection_reason && (
                    <div>
                      <div className="text-gray-600 mb-1">Raison du rejet:</div>
                      <div className="font-medium text-red-600">{code.rejection_reason}</div>
                    </div>
                  )}
                </div>
              </CardBody>
            </Card>
          )}

          {/* Actions de validation/rejet */}
          {code.status === 'pending' && !isExpired() && (
            <Card>
              <CardBody className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Action requise</h3>
                
                {!action && (
                  <div className="flex space-x-4">
                    <Button
                      variant="primary"
                      leftIcon={<CheckCircle size={16} />}
                      onClick={() => setAction('validate')}
                      className="flex-1"
                    >
                      Valider le code
                    </Button>
                    <Button
                      variant="outline"
                      leftIcon={<XCircle size={16} />}
                      onClick={() => setAction('reject')}
                      className="flex-1 text-red-600 border-red-300 hover:bg-red-50"
                    >
                      Rejeter le code
                    </Button>
                  </div>
                )}

                {action === 'validate' && (
                  <div className="space-y-4">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <CheckCircle className="text-green-500 mt-0.5" size={16} />
                        <div>
                          <h4 className="font-medium text-green-800 mb-1">Validation du code</h4>
                          <p className="text-sm text-green-700">
                            En validant ce code, l'abonnement sera automatiquement activé pour l'entreprise 
                            <strong> {code.business_name}</strong>. L'abonnement 
                            <strong> {getPlanLabel(code.plan_type).toLowerCase()}</strong> démarrera immédiatement.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-3">
                      <Button
                        variant="primary"
                        onClick={handleSubmit}
                        disabled={isSubmitting}
                        leftIcon={isSubmitting ? <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div> : <CheckCircle size={16} />}
                      >
                        {isSubmitting ? 'Validation...' : 'Confirmer la validation'}
                      </Button>
                      <Button variant="outline" onClick={() => setAction(null)}>
                        Annuler
                      </Button>
                    </div>
                  </div>
                )}

                {action === 'reject' && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Raison du rejet *
                      </label>
                      <div className="space-y-2">
                        {predefinedReasons.map((reason) => (
                          <label key={reason} className="flex items-center">
                            <input
                              type="radio"
                              name="rejectionReason"
                              value={reason}
                              checked={rejectionReason === reason}
                              onChange={(e) => setRejectionReason(e.target.value)}
                              className="mr-2"
                            />
                            <span className="text-sm">{reason}</span>
                          </label>
                        ))}
                      </div>
                      
                      {rejectionReason === 'Autre (préciser ci-dessous)' && (
                        <textarea
                          value={rejectionReason}
                          onChange={(e) => setRejectionReason(e.target.value)}
                          placeholder="Veuillez préciser la raison du rejet..."
                          className="mt-2 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          rows={3}
                          required
                        />
                      )}
                    </div>

                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <XCircle className="text-red-500 mt-0.5" size={16} />
                        <div>
                          <h4 className="font-medium text-red-800 mb-1">Rejet du code</h4>
                          <p className="text-sm text-red-700">
                            En rejetant ce code, l'entreprise sera notifiée et devra générer un nouveau code 
                            si elle souhaite souscrire à un abonnement.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="flex space-x-3">
                      <Button
                        variant="danger"
                        onClick={handleSubmit}
                        disabled={!rejectionReason.trim() || isSubmitting}
                        leftIcon={isSubmitting ? <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div> : <XCircle size={16} />}
                      >
                        {isSubmitting ? 'Rejet...' : 'Confirmer le rejet'}
                      </Button>
                      <Button variant="outline" onClick={() => setAction(null)}>
                        Annuler
                      </Button>
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          )}

          {/* Message si expiré */}
          {isExpired() && code.status === 'pending' && (
            <Card>
              <CardBody className="p-4">
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="text-gray-500 mt-0.5" size={16} />
                    <div>
                      <h4 className="font-medium text-gray-800 mb-1">Code expiré</h4>
                      <p className="text-sm text-gray-700">
                        Ce code a expiré et ne peut plus être validé. L'entreprise doit générer un nouveau code 
                        pour souscrire à un abonnement.
                      </p>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}
        </div>

        {/* Actions du modal */}
        <div className="flex justify-between items-center mt-6 pt-6 border-t border-gray-200">
          <div className="flex space-x-3">
            <Button
              variant="outline"
              leftIcon={<FileText size={16} />}
            >
              Voir l'entreprise
            </Button>
            <Button
              variant="outline"
              leftIcon={<Eye size={16} />}
            >
              Historique
            </Button>
          </div>
          <Button variant="outline" onClick={onClose}>
            Fermer
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default SubscriptionCodeModal;
