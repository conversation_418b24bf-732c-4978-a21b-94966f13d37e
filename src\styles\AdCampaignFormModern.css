/* Formulaire de création de publicité moderne */
.ad-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.ad-form-container {
  background: white;
  border-radius: 20px;
  width: 95%;
  max-width: 1000px;
  max-height: 95vh;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  animation: slideUp 0.4s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(20px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Header moderne */
.ad-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: sticky;
  top: 0;
  z-index: 50;
}

.header-content h2 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
}

.header-content p {
  margin: 0.5rem 0 0 0;
  font-size: 0.95rem;
  opacity: 0.9;
  color: white;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  color: white;
  padding: 0.75rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  width: 44px;
  height: 44px;
  backdrop-filter: blur(10px);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

/* Indicateur de progression */
.progress-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  overflow-x: auto;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  padding: 0.5rem;
}

.progress-step.active .step-icon {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.progress-step.current .step-icon {
  background: #667eea;
  color: white;
  border-color: #667eea;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(102, 126, 234, 0);
  }
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e2e8f0;
  color: #64748b;
  border: 2px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.step-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  text-align: center;
  transition: color 0.3s ease;
}

.progress-step.active .step-title,
.progress-step.current .step-title {
  color: #4f46e5;
  font-weight: 600;
}

.step-connector {
  position: absolute;
  top: 20px;
  left: 100%;
  width: 100%;
  height: 2px;
  background: #e2e8f0;
  z-index: -1;
}

.progress-step.active .step-connector {
  background: #4f46e5;
}

/* Contenu du formulaire */
.ad-form {
  height: calc(95vh - 200px);
  overflow-y: auto;
}

.form-content {
  padding: 2rem;
  min-height: 500px;
}

.step-content {
  animation: slideInRight 0.4s ease;
}

@keyframes slideInRight {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.step-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f5f9;
}

.step-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.step-header p {
  font-size: 1rem;
  color: #64748b;
  margin: 0.25rem 0 0 0;
}

/* Groupes de formulaire */
.form-group {
  margin-bottom: 2rem;
}

.form-group label {
  display: block;
  font-size: 0.95rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.75rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  transform: translateY(-1px);
}

.form-group input.error,
.form-group textarea.error,
.form-group select.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

/* Messages d'aide et d'erreur */
.field-hint {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.error-message {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-message::before {
  content: "⚠";
  font-size: 1rem;
}

/* Suggestions IA */
.ai-suggestions {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.suggestions-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #0369a1;
  margin-bottom: 1rem;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #0c4a6e;
}

/* Navigation du formulaire */
.form-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  position: sticky;
  bottom: 0;
}

.nav-left,
.nav-right {
  display: flex;
  gap: 1rem;
}

.nav-btn,
.submit-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.prev-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.prev-btn:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
}

.next-btn {
  background: #4f46e5;
  color: white;
}

.next-btn:hover {
  background: #4338ca;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.submit-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.submit-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.loading-text::after {
  content: "";
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Upload d'image moderne */
.image-upload-container {
  border: 2px dashed #d1d5db;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  background: #fafbfc;
  position: relative;
  overflow: hidden;
}

.image-upload-container:hover {
  border-color: #4f46e5;
  background: #f8faff;
}

.upload-placeholder {
  position: relative;
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.upload-content {
  pointer-events: none;
}

.upload-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: white;
}

.upload-content h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.upload-content p {
  font-size: 0.95rem;
  color: #6b7280;
  margin: 0 0 1rem 0;
}

.upload-specs {
  font-size: 0.875rem;
  color: #9ca3af;
}

.image-preview {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  max-width: 400px;
  margin: 0 auto;
}

.image-preview img {
  width: 100%;
  height: auto;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  gap: 0.5rem;
  padding: 0.75rem;
}

.remove-image,
.edit-image {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-image:hover {
  background: #ef4444;
}

.edit-image:hover {
  background: #4f46e5;
}

/* Placeholder et erreurs d'image */
.preview-image-placeholder {
  background: #f8fafc;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  color: #6b7280;
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.preview-image-placeholder p {
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #374151;
}

.preview-image-placeholder span {
  font-size: 0.875rem;
  color: #9ca3af;
}

.image-error {
  background: #fef2f2;
  border: 2px solid #fecaca;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  color: #dc2626;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.image-error p {
  font-weight: 600;
  margin: 0;
  color: #991b1b;
}

/* Emplacements de diffusion */
.placement-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.placement-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.placement-card:hover {
  border-color: #4f46e5;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.placement-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.placement-header span {
  font-weight: 600;
  color: #374151;
}

.placement-card p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 1rem 0;
}

.placement-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.placement-toggle input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  width: 48px;
  height: 24px;
  background: #d1d5db;
  border-radius: 12px;
  position: relative;
  transition: all 0.3s ease;
}

.toggle-slider::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.placement-toggle input[type="checkbox"]:checked + .toggle-slider {
  background: #4f46e5;
}

.placement-toggle input[type="checkbox"]:checked + .toggle-slider::after {
  transform: translateX(24px);
}

/* Ciblage et audience */
.audience-estimate {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border: 1px solid #a7f3d0;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.estimate-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #065f46;
  margin-bottom: 1rem;
}

.estimate-content {
  text-align: center;
}

.estimate-number {
  font-size: 2rem;
  font-weight: 700;
  color: #059669;
}

.estimate-label {
  font-size: 0.875rem;
  color: #047857;
}

.targeting-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #f1f5f9;
}

.targeting-section h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.age-ranges,
.gender-options,
.cities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.age-checkbox,
.gender-radio,
.city-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.city-checkbox {
  flex-direction: row;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
}

.city-checkbox input[type="checkbox"] {
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.city-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  width: 100%;
}

.city-name {
  font-weight: 500;
  color: #374151;
}

.city-users {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 400;
}

.age-checkbox:hover,
.gender-radio:hover,
.city-checkbox:hover {
  border-color: #4f46e5;
  background: #f8faff;
}

/* Styles pour les tranches d'âge */
.age-checkbox {
  flex-direction: row;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
}

.age-checkbox input[type="checkbox"] {
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.age-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  width: 100%;
}

.age-range {
  font-weight: 500;
  color: #374151;
}

.age-users {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 400;
}

.age-checkbox input:checked + .age-info .age-range {
  color: #4f46e5;
  font-weight: 600;
}

/* Styles pour les genres */
.gender-radio {
  flex-direction: row;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
}

.gender-radio input[type="radio"] {
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.gender-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  width: 100%;
}

.gender-label {
  font-weight: 500;
  color: #374151;
}

.gender-users {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 400;
}

.gender-radio input:checked + .gender-info .gender-label,
.city-checkbox input:checked + .city-info .city-name {
  color: #4f46e5;
  font-weight: 600;
}

.loading-cities {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #64748b;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

/* Styles pour les suggestions de budget */
.budget-suggestions {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.budget-suggestions h4 {
  margin: 0 0 1rem 0;
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
}

.suggestion-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.suggestion-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.suggestion-card:hover {
  border-color: #4f46e5;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
}

.suggestion-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.suggestion-content h5 {
  margin: 0 0 0.25rem 0;
  color: #1e293b;
  font-weight: 600;
  font-size: 0.95rem;
}

.suggestion-content p {
  margin: 0 0 0.25rem 0;
  color: #4f46e5;
  font-weight: 700;
  font-size: 1.1rem;
}

.suggestion-content span {
  color: #64748b;
  font-size: 0.8rem;
  line-height: 1.3;
}

/* Styles pour le calcul automatique */
.auto-calculated {
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 500;
  margin-left: 0.5rem;
  background: #ecfdf5;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.budget-breakdown {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.75rem;
  background: #f1f5f9;
  border-radius: 6px;
  color: #475569;
  font-size: 0.875rem;
  font-family: 'Courier New', monospace;
}

.device-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.device-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.device-card:hover {
  border-color: #4f46e5;
  transform: translateY(-2px);
}

.device-card input[type="checkbox"] {
  display: none;
}

.device-card input[type="checkbox"]:checked + .device-content {
  color: #4f46e5;
}

.device-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  transition: color 0.3s ease;
}

/* Budget et recommandations */
.budget-recommendations {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.recommendations-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #92400e;
  margin-bottom: 1rem;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.recommendation-card {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.card-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #92400e;
}

.card-label {
  font-size: 0.875rem;
  color: #78350f;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #78350f;
}

.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.75rem;
}

.time-slot {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-slot:hover {
  border-color: #4f46e5;
  background: #f8faff;
}

.time-slot input[type="checkbox"]:checked + span {
  color: #4f46e5;
  font-weight: 600;
}

/* Portée estimée */
.reach-estimate {
  background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
  border: 1px solid #c4b5fd;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.reach-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #5b21b6;
  margin-bottom: 1rem;
}

.reach-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.reach-stat {
  text-align: center;
  background: white;
  border-radius: 8px;
  padding: 1rem;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #5b21b6;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b21a8;
}

/* Aperçu et validation */
.preview-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.ad-preview h4,
.campaign-summary h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.preview-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.preview-image {
  position: relative;
}

.preview-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.discount-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #ef4444;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.875rem;
}

.preview-content {
  padding: 1.5rem;
}

.preview-content h5 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.75rem 0;
}

.preview-content p {
  font-size: 0.95rem;
  color: #6b7280;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.preview-cta {
  text-align: center;
}

.cta-button {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-button:hover {
  background: #4338ca;
  transform: translateY(-1px);
}

.campaign-summary {
  background: #f9fafb;
  border-radius: 12px;
  padding: 1.5rem;
  height: fit-content;
}

.summary-section {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.summary-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.summary-section h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.summary-item .label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.summary-item .value {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 600;
  text-align: right;
  max-width: 60%;
}

.value.status-draft {
  color: #f59e0b;
}

.value.status-active {
  color: #10b981;
}

.value.status-paused {
  color: #6b7280;
}

.placement-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.placement-tag {
  background: #4f46e5;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.placement-tag.inactive {
  background: #9ca3af;
}

.performance-estimates {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
}

.estimate-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.estimate-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: #4f46e5;
}

.estimate-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

/* Aperçu de publicité */
.preview-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.ad-preview {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
}

.ad-preview h4 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-weight: 600;
}

.preview-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-image {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.discount-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #ef4444;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.875rem;
}

.preview-content {
  padding: 1.5rem;
}

.preview-content h5 {
  margin: 0 0 0.75rem 0;
  color: #1f2937;
  font-weight: 700;
  font-size: 1.125rem;
}

.preview-content p {
  margin: 0 0 1rem 0;
  color: #6b7280;
  line-height: 1.5;
}

.preview-cta {
  margin-top: 1rem;
}

.cta-button {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-button:hover {
  background: #4338ca;
  transform: translateY(-1px);
}

/* Résumé de campagne */
.campaign-summary {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
}

.campaign-summary h4 {
  margin: 0 0 1.5rem 0;
  color: #374151;
  font-weight: 600;
}

.summary-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f3f4f6;
}

.summary-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.summary-section h5 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-weight: 600;
  font-size: 1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.5rem 0;
}

.summary-item .label {
  color: #6b7280;
  font-weight: 500;
}

.summary-item .value {
  color: #1f2937;
  font-weight: 600;
  text-align: right;
  max-width: 60%;
}

.placement-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.placement-tag {
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.placement-tag.inactive {
  background: #f3f4f6;
  color: #6b7280;
}

.performance-estimates {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.estimate-item {
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.estimate-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.estimate-label {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .ad-form-container {
    width: 98%;
    max-height: 98vh;
  }

  .progress-indicator {
    padding: 1rem;
    overflow-x: auto;
  }

  .progress-step {
    min-width: 100px;
  }

  .step-title {
    font-size: 0.75rem;
  }

  .form-content {
    padding: 1.5rem;
  }

  .preview-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .ad-form-header {
    padding: 1.5rem;
  }

  .header-content h2 {
    font-size: 1.5rem;
  }

  .progress-indicator {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .progress-step {
    flex-direction: row;
    min-width: auto;
    width: 100%;
    justify-content: flex-start;
  }

  .step-connector {
    display: none;
  }

  .form-content {
    padding: 1rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .placement-options {
    grid-template-columns: 1fr;
  }

  .age-ranges,
  .gender-options,
  .cities-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .device-options {
    grid-template-columns: 1fr;
  }

  .time-slots {
    grid-template-columns: 1fr;
  }

  .recommendations-grid {
    grid-template-columns: 1fr;
  }

  .reach-stats {
    grid-template-columns: 1fr;
  }

  .performance-estimates {
    grid-template-columns: 1fr;
  }

  .form-navigation {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .nav-left,
  .nav-right {
    width: 100%;
    justify-content: center;
  }

  .nav-btn,
  .submit-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .ad-form-container {
    border-radius: 12px;
    margin: 0.5rem;
    width: calc(100% - 1rem);
  }

  .ad-form-header {
    padding: 1rem;
  }

  .header-content h2 {
    font-size: 1.25rem;
  }

  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .step-header h3 {
    font-size: 1.25rem;
  }

  .upload-icon {
    width: 48px;
    height: 48px;
  }

  .upload-content h4 {
    font-size: 1rem;
  }

  .placement-card {
    padding: 1rem;
  }

  .device-card {
    padding: 1rem;
  }

  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .summary-item .value {
    max-width: 100%;
    text-align: left;
  }
}

/* Section wallet dans le formulaire */
.wallet-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  color: white;
}

.wallet-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  font-size: 0.875rem;
  opacity: 0.9;
}

.wallet-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.wallet-balance {
  display: flex;
  flex-direction: column;
}

.balance-amount {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.balance-label {
  font-size: 0.875rem;
  opacity: 0.8;
}

.budget-check {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  font-size: 0.875rem;
}

.check-success {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #10b981;
}

.check-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #f59e0b;
}

@media (max-width: 768px) {
  .wallet-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .budget-check {
    width: 100%;
    justify-content: center;
  }
}
