import React, { useState, useEffect } from 'react';
import { TrendingUp, Users, Clock, DollarSign, Target, AlertTriangle, CheckCircle } from 'lucide-react';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';

// Types pour la gestion d'inventaire
interface AdSlot {
  id: string;
  name: string;
  description: string;
  maxAdsPerHour: number;
  currentCompetition: number;
  averageCPC: number;
  suggestedBid: number;
  availability: 'high' | 'medium' | 'low';
}

interface BidRecommendation {
  placement: string;
  minBid: number;
  suggestedBid: number;
  competitionLevel: 'low' | 'medium' | 'high';
  estimatedPosition: number;
}

interface AdInventoryManagerProps {
  selectedPlacements: string[];
  budget: number;
  onPlacementChange: (placements: string[], bidAmounts: Record<string, number>) => void;
}

const AdInventoryManager: React.FC<AdInventoryManagerProps> = ({
  selectedPlacements,
  budget,
  onPlacementChange
}) => {
  const [bidAmounts, setBidAmounts] = useState<Record<string, number>>({});
  const [recommendations, setRecommendations] = useState<BidRecommendation[]>([]);

  // Données d'inventaire publicitaire (en temps réel)
  const adSlots: AdSlot[] = [
    {
      id: 'newsfeed',
      name: 'Fil d\'actualité',
      description: 'Apparaît entre les posts des utilisateurs',
      maxAdsPerHour: 12, // 1 pub toutes les 5 minutes
      currentCompetition: 8, // 8 annonceurs actifs
      averageCPC: 250,
      suggestedBid: 300,
      availability: 'medium'
    },
    {
      id: 'sidebar',
      name: 'Barre latérale droite',
      description: 'Carrousel rotatif dans la sidebar',
      maxAdsPerHour: 6, // Rotation toutes les 10 minutes
      currentCompetition: 3, // 3 annonceurs actifs
      averageCPC: 150,
      suggestedBid: 180,
      availability: 'high'
    },
    {
      id: 'offers',
      name: 'Offres et promotions',
      description: 'Page dédiée aux promotions',
      maxAdsPerHour: 24, // Plus d'espace disponible
      currentCompetition: 5, // 5 annonceurs actifs
      averageCPC: 200,
      suggestedBid: 220,
      availability: 'high'
    },
    {
      id: 'marketplace',
      name: 'Marketplace',
      description: 'Produits sponsorisés dans les résultats',
      maxAdsPerHour: 20,
      currentCompetition: 12, // Très compétitif
      averageCPC: 400,
      suggestedBid: 500,
      availability: 'low'
    },
    {
      id: 'search',
      name: 'Résultats de recherche',
      description: 'En haut des résultats de recherche',
      maxAdsPerHour: 30,
      currentCompetition: 6,
      averageCPC: 350,
      suggestedBid: 400,
      availability: 'medium'
    }
  ];

  // Calculer les recommandations d'enchères
  useEffect(() => {
    const newRecommendations: BidRecommendation[] = selectedPlacements.map(placementId => {
      const slot = adSlots.find(s => s.id === placementId);
      if (!slot) return null;

      const competitionLevel = slot.currentCompetition > slot.maxAdsPerHour * 0.8 ? 'high' :
                              slot.currentCompetition > slot.maxAdsPerHour * 0.5 ? 'medium' : 'low';

      const currentBid = bidAmounts[placementId] || 0;
      const estimatedPosition = calculateEstimatedPosition(currentBid, slot);

      return {
        placement: slot.name,
        minBid: Math.ceil(slot.averageCPC * 0.8),
        suggestedBid: slot.suggestedBid,
        competitionLevel,
        estimatedPosition
      };
    }).filter(Boolean) as BidRecommendation[];

    setRecommendations(newRecommendations);
  }, [selectedPlacements, bidAmounts]);

  const calculateEstimatedPosition = (bid: number, slot: AdSlot): number => {
    if (bid >= slot.suggestedBid * 1.2) return 1;
    if (bid >= slot.suggestedBid) return 2;
    if (bid >= slot.averageCPC) return 3;
    return Math.min(slot.currentCompetition + 1, 5);
  };

  const handlePlacementToggle = (placementId: string) => {
    const newPlacements = selectedPlacements.includes(placementId)
      ? selectedPlacements.filter(p => p !== placementId)
      : [...selectedPlacements, placementId];

    // Initialiser l'enchère avec la valeur suggérée
    if (!selectedPlacements.includes(placementId)) {
      const slot = adSlots.find(s => s.id === placementId);
      if (slot) {
        setBidAmounts(prev => ({
          ...prev,
          [placementId]: slot.suggestedBid
        }));
      }
    }

    onPlacementChange(newPlacements, bidAmounts);
  };

  const handleBidChange = (placementId: string, amount: number) => {
    const newBidAmounts = {
      ...bidAmounts,
      [placementId]: amount
    };
    setBidAmounts(newBidAmounts);
    onPlacementChange(selectedPlacements, newBidAmounts);
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'high': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getAvailabilityLabel = (availability: string) => {
    switch (availability) {
      case 'high': return 'Disponible';
      case 'medium': return 'Compétitif';
      case 'low': return 'Très compétitif';
      default: return 'Inconnu';
    }
  };

  const totalBidAmount = Object.values(bidAmounts).reduce((sum, bid) => sum + bid, 0);

  return (
    <div className="ad-inventory-manager">
      <div className="inventory-header">
        <h3>Sélection d'emplacements et enchères</h3>
        <p>Choisissez vos emplacements et définissez vos enchères pour maximiser votre visibilité</p>
      </div>

      {/* Résumé du budget */}
      <Card className="budget-summary">
        <CardBody>
          <div className="budget-info">
            <div className="budget-item">
              <DollarSign size={16} />
              <span>Budget total: {budget.toLocaleString()} F CFA</span>
            </div>
            <div className="budget-item">
              <Target size={16} />
              <span>Enchères totales: {totalBidAmount.toLocaleString()} F CFA</span>
            </div>
            <div className="budget-item">
              {totalBidAmount <= budget ? (
                <>
                  <CheckCircle size={16} className="text-green-600" />
                  <span className="text-green-600">Budget suffisant</span>
                </>
              ) : (
                <>
                  <AlertTriangle size={16} className="text-red-600" />
                  <span className="text-red-600">Budget dépassé</span>
                </>
              )}
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Grille des emplacements */}
      <div className="placements-grid">
        {adSlots.map(slot => (
          <Card key={slot.id} className={`placement-card ${selectedPlacements.includes(slot.id) ? 'selected' : ''}`}>
            <CardBody>
              <div className="placement-header">
                <div className="placement-info">
                  <h4>{slot.name}</h4>
                  <p>{slot.description}</p>
                </div>
                <div className={`availability-badge ${getAvailabilityColor(slot.availability)}`}>
                  {getAvailabilityLabel(slot.availability)}
                </div>
              </div>

              <div className="placement-stats">
                <div className="stat">
                  <Users size={14} />
                  <span>{slot.currentCompetition}/{slot.maxAdsPerHour} annonceurs</span>
                </div>
                <div className="stat">
                  <DollarSign size={14} />
                  <span>CPC moyen: {slot.averageCPC} F CFA</span>
                </div>
                <div className="stat">
                  <Clock size={14} />
                  <span>Max: {slot.maxAdsPerHour} pubs/heure</span>
                </div>
              </div>

              <div className="placement-controls">
                <label className="placement-toggle">
                  <input
                    type="checkbox"
                    checked={selectedPlacements.includes(slot.id)}
                    onChange={() => handlePlacementToggle(slot.id)}
                  />
                  <span>Sélectionner cet emplacement</span>
                </label>

                {selectedPlacements.includes(slot.id) && (
                  <div className="bid-controls">
                    <label>Enchère par clic (F CFA)</label>
                    <input
                      type="number"
                      value={bidAmounts[slot.id] || slot.suggestedBid}
                      onChange={(e) => handleBidChange(slot.id, parseInt(e.target.value) || 0)}
                      min={Math.ceil(slot.averageCPC * 0.8)}
                      placeholder={`Min: ${Math.ceil(slot.averageCPC * 0.8)}`}
                    />
                    <div className="bid-recommendation">
                      <TrendingUp size={12} />
                      <span>Suggéré: {slot.suggestedBid} F CFA</span>
                    </div>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      {/* Recommandations */}
      {recommendations.length > 0 && (
        <Card className="recommendations">
          <CardBody>
            <h4>Recommandations d'enchères</h4>
            <div className="recommendations-list">
              {recommendations.map((rec, index) => (
                <div key={index} className="recommendation-item">
                  <div className="rec-placement">{rec.placement}</div>
                  <div className="rec-details">
                    <span>Position estimée: #{rec.estimatedPosition}</span>
                    <span>Concurrence: {rec.competitionLevel}</span>
                    <span>Enchère min: {rec.minBid} F CFA</span>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default AdInventoryManager;
