import { supabase } from '../lib/supabase';

// Types pour les interactions avec les offres
export interface OfferLike {
  id: string;
  offer_id: string;
  user_id: string;
  created_at: string;
}

export interface OfferShare {
  id: string;
  offer_id: string;
  user_id: string;
  share_type: 'facebook' | 'twitter' | 'whatsapp' | 'email' | 'copy_link' | 'internal';
  shared_at: string;
}

export interface OfferComment {
  id: string;
  offer_id: string;
  user_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  // Données jointes
  username?: string;
  profile_picture?: string;
  user_role?: string;
}

class OfferInteractionService {
  
  /**
   * Ajouter un like à une offre
   */
  async likeOffer(offerId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('offer_likes')
        .insert([{
          offer_id: offerId,
          user_id: userId
        }]);

      if (error) {
        // Si c'est une erreur de contrainte unique, l'utilisateur a déjà liké
        if (error.code === '23505') {
          console.log('Utilisateur a déjà liké cette offre');
          return false;
        }
        // Si la table n'existe pas, utiliser le système de campagnes publicitaires
        if (error.code === '42P01') {
          console.log('Table offer_likes non trouvée, utilisation du système ad_campaigns');
          return this.likeOfferFallback(offerId, userId);
        }
        throw error;
      }

      console.log(`👍 Like ajouté pour l'offre ${offerId} par l'utilisateur ${userId}`);
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'ajout du like sur l\'offre:', error);
      // Fallback vers le système de campagnes publicitaires
      return this.likeOfferFallback(offerId, userId);
    }
  }

  /**
   * Fallback : utiliser le système de campagnes publicitaires
   */
  private async likeOfferFallback(offerId: string, userId: string): Promise<boolean> {
    try {
      // Vérifier si une campagne existe pour cette offre
      const { data: campaign, error: campaignError } = await supabase
        .from('ad_campaigns')
        .select('id')
        .eq('id', offerId)
        .single();

      if (campaignError || !campaign) {
        // Créer une campagne temporaire pour cette offre
        const success = await this.createOfferCampaign(offerId);
        if (!success) {
          console.error('Impossible de créer une campagne pour l\'offre:', offerId);
          return false;
        }
      }

      // Utiliser le service de campagnes publicitaires
      const { adInteractionService } = await import('./adInteractionService');
      return await adInteractionService.likeAd(offerId, userId);
    } catch (error) {
      console.error('Erreur dans le fallback de like d\'offre:', error);
      return false;
    }
  }

  /**
   * Créer une campagne publicitaire temporaire pour une offre
   */
  private async createOfferCampaign(offerId: string): Promise<boolean> {
    try {
      // Récupérer les données de l'offre depuis realOffersData
      const { realCameroonianOffers } = await import('../data/realOffersData');
      const offer = realCameroonianOffers.find(o => o.id === offerId);
      
      if (!offer) {
        console.error('Offre non trouvée dans realOffersData:', offerId);
        return false;
      }

      // Créer la campagne
      const { error } = await supabase
        .from('ad_campaigns')
        .insert([{
          id: offerId,
          business_id: offer.businessId,
          title: offer.title,
          description: offer.description,
          image_url: offer.imageUrl,
          target_url: '#', // URL par défaut
          bid_amount: 100, // Valeur par défaut
          daily_budget: 1000, // Valeur par défaut
          total_budget: 10000, // Valeur par défaut
          status: 'active',
          placements: '["newsfeed"]',
          start_date: offer.startDate.toISOString(),
          end_date: offer.endDate.toISOString()
        }]);

      if (error) {
        console.error('Erreur lors de la création de la campagne pour l\'offre:', error);
        return false;
      }

      console.log(`✅ Campagne créée pour l'offre ${offerId}`);
      return true;
    } catch (error) {
      console.error('Erreur lors de la création de la campagne d\'offre:', error);
      return false;
    }
  }

  /**
   * Retirer un like d'une offre
   */
  async unlikeOffer(offerId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('offer_likes')
        .delete()
        .eq('offer_id', offerId)
        .eq('user_id', userId);

      if (error) {
        // Si la table n'existe pas, utiliser le système de campagnes publicitaires
        if (error.code === '42P01') {
          const { adInteractionService } = await import('./adInteractionService');
          return await adInteractionService.unlikeAd(offerId, userId);
        }
        throw error;
      }

      console.log(`👎 Like retiré pour l'offre ${offerId} par l'utilisateur ${userId}`);
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression du like sur l\'offre:', error);
      // Fallback vers le système de campagnes publicitaires
      const { adInteractionService } = await import('./adInteractionService');
      return await adInteractionService.unlikeAd(offerId, userId);
    }
  }

  /**
   * Vérifier si un utilisateur a liké une offre
   */
  async hasUserLikedOffer(offerId: string, userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('offer_likes')
        .select('id')
        .eq('offer_id', offerId)
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === '42P01') {
          // Fallback vers le système de campagnes publicitaires
          const { adInteractionService } = await import('./adInteractionService');
          return await adInteractionService.hasUserLikedAd(offerId, userId);
        }
        if (error.code === 'PGRST116') {
          return false; // Pas de like trouvé
        }
        throw error;
      }

      return !!data;
    } catch (error) {
      console.error('Erreur lors de la vérification du like d\'offre:', error);
      return false;
    }
  }

  /**
   * Obtenir le nombre de likes d'une offre
   */
  async getOfferLikesCount(offerId: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('offer_likes')
        .select('*', { count: 'exact', head: true })
        .eq('offer_id', offerId);

      if (error) {
        if (error.code === '42P01') {
          // Fallback vers le système de campagnes publicitaires
          const { adInteractionService } = await import('./adInteractionService');
          return await adInteractionService.getAdLikesCount(offerId);
        }
        throw error;
      }

      return count || 0;
    } catch (error) {
      console.error('Erreur lors de la récupération du nombre de likes d\'offre:', error);
      return 0;
    }
  }

  /**
   * Partager une offre
   */
  async shareOffer(offerId: string, userId: string, shareType: OfferShare['share_type']): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('offer_shares')
        .insert([{
          offer_id: offerId,
          user_id: userId,
          share_type: shareType
        }]);

      if (error) {
        if (error.code === '42P01') {
          // Fallback vers le système de campagnes publicitaires
          const { adInteractionService } = await import('./adInteractionService');
          return await adInteractionService.shareAd(offerId, userId, shareType);
        }
        throw error;
      }

      console.log(`📤 Partage ${shareType} enregistré pour l'offre ${offerId} par l'utilisateur ${userId}`);
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du partage d\'offre:', error);
      return false;
    }
  }

  /**
   * Obtenir le nombre de partages d'une offre
   */
  async getOfferSharesCount(offerId: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('offer_shares')
        .select('*', { count: 'exact', head: true })
        .eq('offer_id', offerId);

      if (error) {
        if (error.code === '42P01') {
          // Fallback vers le système de campagnes publicitaires
          const { adInteractionService } = await import('./adInteractionService');
          return await adInteractionService.getAdSharesCount(offerId);
        }
        throw error;
      }

      return count || 0;
    } catch (error) {
      console.error('Erreur lors de la récupération du nombre de partages d\'offre:', error);
      return 0;
    }
  }
}

export const offerInteractionService = new OfferInteractionService();
