# Système de Types de Posts par Rôle Utilisateur

## 🎯 Règles Définies

### **Entreprises (UserRole.BUSINESS)**
- ✅ **Peuvent créer** : "Demande d'avis" uniquement
- ❌ **Ne peuvent PAS créer** : "Coup de coeur" ou "Coup de gueule"
- 🎯 **Objectif** : Présenter leurs produits/services et recueillir les avis de la communauté

### **Utilisateurs Standards (UserRole.STANDARD)**
- ✅ **Peuvent créer** : "Coup de coeur" et "Coup de gueule" uniquement
- ❌ **Ne peuvent PAS créer** : "Demande d'avis"
- 🎯 **Objectif** : Partager leurs expériences avec les produits/services qu'ils ont testés

### **Administrateurs (UserRole.ADMIN)**
- ✅ **Peuvent créer** : Tous les types de posts
- 🎯 **Objectif** : Modération et gestion de la plateforme

## 🛠️ Implémentation Technique

### **1. Validation Côté Client (CreatePostForm.tsx)**

```typescript
// Interface différente selon le rôle
if (currentUser.role === UserRole.BUSINESS) {
  // Formulaire pour "Demande d'avis" uniquement
  // - Pas de sélection de type
  // - Pas de rating requis
  // - Focus sur la présentation du produit/service
}

if (currentUser.role === UserRole.STANDARD) {
  // Formulaire pour "Coup de coeur" et "Coup de gueule"
  // - Sélection entre les deux types
  // - Rating obligatoire
  // - Focus sur l'expérience utilisateur
}
```

### **2. Validation Côté Serveur (PostsContext.tsx)**

```typescript
// Validation stricte avant création
if (userRole === UserRole.BUSINESS && postType !== PostType.REVIEW) {
  alert('Les entreprises peuvent uniquement créer des demandes d\'avis.');
  return;
}

if (userRole === UserRole.STANDARD && postType === PostType.REVIEW) {
  alert('Les utilisateurs standards ne peuvent pas créer de demandes d\'avis.');
  return;
}
```

### **3. Utilitaires de Validation (postTypeValidation.ts)**

```typescript
export const canUserCreatePostType = (userRole: UserRole, postType: PostType): boolean => {
  switch (userRole) {
    case UserRole.BUSINESS:
      return postType === PostType.REVIEW;
    case UserRole.STANDARD:
      return postType === PostType.FAVORITE || postType === PostType.COMPLAINT;
    case UserRole.ADMIN:
      return true;
    default:
      return false;
  }
};
```

## 📊 Système de Monitoring

### **Hook usePostTypeRoleValidation**
- **Détection automatique** des incohérences
- **Statistiques en temps réel** sur la cohérence
- **Logs d'alerte** pour les posts invalides

### **Interface de Debug**
- 🏢 **Demandes d'avis** : Nombre de posts créés par les entreprises
- 👤 **Avis utilisateurs** : Nombre de coups de coeur/gueule des utilisateurs
- 📊 **Taux de cohérence** : Pourcentage de posts respectant les règles

## 🎨 Différences Visuelles

### **Demande d'avis (Entreprises)**
- **Couleur** : Bleu (`bg-blue-50`)
- **Titre** : "Demander l'avis des utilisateurs"
- **Description** : "Présentez votre produit ou service..."
- **Champs** : Nom entreprise, produit/service, catégorie, description, images
- **Pas de rating** : Les utilisateurs donneront leur avis

### **Coup de coeur/gueule (Utilisateurs)**
- **Couleur** : Vert (`bg-green-50`) / Rouge (`bg-red-50`)
- **Titre** : "Partagez un Coup de Coeur/Gueule"
- **Description** : "Partagez votre expérience..."
- **Champs** : Entreprise, produit, catégorie, description, rating, images
- **Rating obligatoire** : Note de 1 à 5

## 🔒 Sécurité et Validation

### **Validation Multi-Niveaux**
1. **Interface** : Formulaires adaptés selon le rôle
2. **Client** : Validation avant envoi
3. **Serveur** : Validation stricte côté backend
4. **Base de données** : Contraintes de cohérence

### **Messages d'Erreur Explicites**
- **Entreprises** : "Les entreprises peuvent uniquement créer des demandes d'avis..."
- **Utilisateurs** : "Les utilisateurs standards ne peuvent pas créer de demandes d'avis..."

## 📈 Avantages du Système

### **Pour les Entreprises**
- ✅ **Interface dédiée** pour présenter leurs produits
- ✅ **Recueil d'avis** structuré de la communauté
- ✅ **Pas de confusion** avec les avis utilisateurs

### **Pour les Utilisateurs**
- ✅ **Expression libre** de leurs expériences
- ✅ **Notation des produits** testés
- ✅ **Distinction claire** entre avis et demandes

### **Pour la Plateforme**
- ✅ **Contenu structuré** et cohérent
- ✅ **Rôles bien définis** et respectés
- ✅ **Monitoring automatique** des incohérences

## 🚀 Utilisation

### **Pour Tester**
1. **Connectez-vous** avec un compte entreprise → Seule option "Demande d'avis"
2. **Connectez-vous** avec un compte utilisateur → Options "Coup de coeur/gueule"
3. **Vérifiez les statistiques** dans l'interface de debug

### **Pour Déboguer**
- **Console** : Logs automatiques des incohérences
- **Interface** : Statistiques de cohérence en temps réel
- **Validation** : Messages d'erreur explicites

Ce système garantit une **séparation claire** entre les contenus créés par les entreprises et ceux créés par les utilisateurs, tout en maintenant une **expérience utilisateur** adaptée à chaque rôle.
