-- Migration pour empêcher les entreprises de commenter les publicités
-- Cette migration ajoute une contrainte pour s'assurer que seuls les utilisateurs réguliers peuvent commenter les publicités

-- =====================================================
-- 1. FONCTION DE VALIDATION DES COMMENTAIRES PUBLICITAIRES
-- =====================================================

-- Fonction pour vérifier si un utilisateur peut commenter une publicité
CREATE OR REPLACE FUNCTION check_ad_comment_permissions()
RETURNS TRIGGER AS $$
BEGIN
  -- Vérifier le rôle de l'utilisateur
  IF EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = NEW.user_id 
    AND role = 'business'
  ) THEN
    RAISE EXCEPTION 'Les comptes entreprise ne peuvent pas commenter les publicités. Seuls les utilisateurs réguliers peuvent interagir avec les publicités.';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 2. TRIGGER POUR VALIDER LES COMMENTAIRES
-- =====================================================

-- Créer le trigger qui s'exécute avant l'insertion d'un commentaire
DROP TRIGGER IF EXISTS validate_ad_comment_permissions ON ad_comments;

CREATE TRIGGER validate_ad_comment_permissions
  BEFORE INSERT ON ad_comments
  FOR EACH ROW
  EXECUTE FUNCTION check_ad_comment_permissions();

-- =====================================================
-- 3. POLITIQUE RLS POUR RENFORCER LA SÉCURITÉ
-- =====================================================

-- Activer RLS sur la table ad_comments si ce n'est pas déjà fait
ALTER TABLE ad_comments ENABLE ROW LEVEL SECURITY;

-- Politique pour empêcher les entreprises d'insérer des commentaires
DROP POLICY IF EXISTS "business_cannot_comment_ads" ON ad_comments;

CREATE POLICY "business_cannot_comment_ads"
  ON ad_comments
  FOR INSERT
  TO authenticated
  WITH CHECK (
    NOT EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'business'
    )
  );

-- Politique pour permettre aux utilisateurs réguliers de commenter
DROP POLICY IF EXISTS "regular_users_can_comment_ads" ON ad_comments;

CREATE POLICY "regular_users_can_comment_ads"
  ON ad_comments
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('standard', 'admin')
    )
  );

-- Politique pour permettre à tous de lire les commentaires
DROP POLICY IF EXISTS "anyone_can_read_ad_comments" ON ad_comments;

CREATE POLICY "anyone_can_read_ad_comments"
  ON ad_comments
  FOR SELECT
  TO authenticated
  USING (true);

-- Politique pour permettre aux auteurs de modifier leurs commentaires
DROP POLICY IF EXISTS "users_can_update_own_ad_comments" ON ad_comments;

CREATE POLICY "users_can_update_own_ad_comments"
  ON ad_comments
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Politique pour permettre aux auteurs de supprimer leurs commentaires
DROP POLICY IF EXISTS "users_can_delete_own_ad_comments" ON ad_comments;

CREATE POLICY "users_can_delete_own_ad_comments"
  ON ad_comments
  FOR DELETE
  TO authenticated
  USING (user_id = auth.uid());

-- =====================================================
-- 4. COMMENTAIRES POUR DOCUMENTATION
-- =====================================================

COMMENT ON FUNCTION check_ad_comment_permissions() IS 'Fonction de validation qui empêche les comptes entreprise de commenter les publicités';
COMMENT ON TRIGGER validate_ad_comment_permissions ON ad_comments IS 'Trigger qui valide les permissions avant l''insertion d''un commentaire publicitaire';

-- =====================================================
-- 5. MISE À JOUR DE LA VUE AVEC INFORMATIONS UTILISATEUR
-- =====================================================

-- Recréer la vue pour inclure les informations de rôle
DROP VIEW IF EXISTS ad_comments_with_user;

CREATE OR REPLACE VIEW ad_comments_with_user AS
SELECT
  ac.*,
  p.username,
  p.profile_picture,
  p.role as user_role
FROM ad_comments ac
JOIN profiles p ON ac.user_id = p.id
WHERE ac.is_approved = true
  AND p.role != 'business' -- Exclure les commentaires d'entreprises (sécurité supplémentaire)
ORDER BY ac.created_at DESC;

-- =====================================================
-- 6. LOGS ET AUDIT
-- =====================================================

-- Ajouter un log pour tracer les tentatives de commentaires bloquées
CREATE TABLE IF NOT EXISTS ad_comment_violations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id),
  campaign_id uuid NOT NULL,
  attempted_content text,
  violation_reason text NOT NULL DEFAULT 'Business account attempted to comment on ad',
  ip_address inet,
  user_agent text,
  created_at timestamptz DEFAULT now()
);

-- Politique RLS pour la table de violations
ALTER TABLE ad_comment_violations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "only_admins_can_read_violations"
  ON ad_comment_violations
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

COMMENT ON TABLE ad_comment_violations IS 'Table d''audit pour tracer les tentatives de commentaires non autorisées sur les publicités';
