import React, { useState, useEffect } from 'react';
import {
  Users,
  Search,
  Filter,
  MoreVertical,
  UserCheck,
  UserX,
  Shield,
  Ban,
  Eye,
  Edit,
  Trash2,
  Download,
  Plus,
  Calendar,
  MapPin,
  Mail,
  Phone,
  Globe,
  Building,
  Star,
  MessageSquare,
  Heart,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Crown,
  Award,
  Target
} from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import VerificationBadge from '../ui/VerificationBadge';
import { AdminService } from '../../services/adminService';
import { UserManagementAction, AdminFilters } from '../../types/admin';
import { UserRole, UserStatus } from '../../types';
import { supabase } from '../../lib/supabase';

// Fonction simple pour formater les dates
const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'il y a quelques secondes';
  if (diffInSeconds < 3600) return `il y a ${Math.floor(diffInSeconds / 60)} minutes`;
  if (diffInSeconds < 86400) return `il y a ${Math.floor(diffInSeconds / 3600)} heures`;
  if (diffInSeconds < 2592000) return `il y a ${Math.floor(diffInSeconds / 86400)} jours`;
  if (diffInSeconds < 31536000) return `il y a ${Math.floor(diffInSeconds / 2592000)} mois`;
  return `il y a ${Math.floor(diffInSeconds / 31536000)} ans`;
};

interface User {
  id: string;
  username: string;
  email: string;
  role: UserRole;
  status: UserStatus;
  profile_picture?: string;
  created_at: string;
  last_login?: string;
  is_verified?: boolean;
  post_count?: number;
  followers_count?: number;
  following_count?: number;
  comment_count?: number;
  recommendations_count?: number;
  country?: string;
  city?: string;
  age?: number;
  gender?: string;
  bio?: string;
  website?: string;
  phone?: string;
  profession?: string;
  interests?: string;
  profile_completed_at?: string;
  suspended_until?: string;
  suspension_reason?: string;
  // Champs business (si applicable)
  business_name?: string;
  business_status?: string;
  business_category?: string;
  business_description?: string;
  business_verified?: boolean;
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState<UserRole | 'all'>('all');
  const [selectedStatus, setSelectedStatus] = useState<UserStatus | 'all'>('all');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [showActionModal, setShowActionModal] = useState(false);
  const [showUserDetailModal, setShowUserDetailModal] = useState(false);
  const [showCreateUserModal, setShowCreateUserModal] = useState(false);
  const [showBulkActionModal, setShowBulkActionModal] = useState(false);
  const [actionUser, setActionUser] = useState<User | null>(null);
  const [selectedCountry, setSelectedCountry] = useState<string>('all');
  const [selectedVerification, setSelectedVerification] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({ start: '', end: '' });
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    suspended: 0,
    verified: 0,
    businesses: 0,
    newToday: 0
  });

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);

      // Charger les utilisateurs avec toutes les données
      const { data: usersData, error: usersError } = await supabase
        .from('profiles')
        .select(`
          id,
          username,
          email,
          role,
          status,
          profile_picture,
          created_at,
          last_login,
          is_verified,
          post_count,
          followers_count,
          following_count,
          comment_count,
          recommendations_count,
          country,
          city,
          age,
          gender,
          bio,
          website,
          phone,
          profession,
          interests,
          profile_completed_at,
          suspended_until,
          suspension_reason
        `)
        .order(sortBy, { ascending: sortOrder === 'asc' });

      if (usersError) {
        console.error('Erreur lors du chargement des utilisateurs:', usersError);
        return;
      }

      // Charger les données business pour les comptes business
      const { data: businessData, error: businessError } = await supabase
        .from('business_profiles')
        .select(`
          id,
          business_name,
          business_status,
          business_category,
          business_description,
          business_verified
        `);

      if (businessError) {
        console.error('Erreur lors du chargement des données business:', businessError);
      }

      // Fusionner les données
      const enrichedUsers = (usersData || []).map(user => {
        const businessInfo = businessData?.find(b => b.id === user.id);
        return {
          ...user,
          business_name: businessInfo?.business_name,
          business_status: businessInfo?.business_status,
          business_category: businessInfo?.business_category,
          business_description: businessInfo?.business_description,
          business_verified: businessInfo?.business_verified
        };
      });

      setUsers(enrichedUsers);

      // Calculer les statistiques
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const newStats = {
        total: enrichedUsers.length,
        active: enrichedUsers.filter(u => u.status !== 'suspended').length,
        suspended: enrichedUsers.filter(u => u.status === 'suspended' || u.suspended_until).length,
        verified: enrichedUsers.filter(u => u.is_verified).length,
        businesses: enrichedUsers.filter(u => u.role === 'business').length,
        newToday: enrichedUsers.filter(u => new Date(u.created_at) >= today).length
      };

      setStats(newStats);

    } catch (error) {
      console.error('Erreur dans loadUsers:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (user.business_name && user.business_name.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesRole = selectedRole === 'all' || user.role === selectedRole;
    const matchesStatus = selectedStatus === 'all' || user.status === selectedStatus;
    const matchesCountry = selectedCountry === 'all' || user.country === selectedCountry;
    const matchesVerification = selectedVerification === 'all' ||
                               (selectedVerification === 'verified' && user.is_verified) ||
                               (selectedVerification === 'unverified' && !user.is_verified);

    let matchesDateRange = true;
    if (dateRange.start && dateRange.end) {
      const userDate = new Date(user.created_at);
      const startDate = new Date(dateRange.start);
      const endDate = new Date(dateRange.end);
      matchesDateRange = userDate >= startDate && userDate <= endDate;
    }

    return matchesSearch && matchesRole && matchesStatus && matchesCountry && matchesVerification && matchesDateRange;
  });

  // Pagination
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedUsers = filteredUsers.slice(startIndex, startIndex + itemsPerPage);

  const handleUserAction = async (action: UserManagementAction) => {
    try {
      const success = await AdminService.executeUserAction(action);
      if (success) {
        await loadUsers();
        setShowActionModal(false);
        setActionUser(null);
      }
    } catch (error) {
      console.error('Erreur lors de l\'action utilisateur:', error);
    }
  };

  const getUserStatusLabel = (status: UserStatus): string => {
    const labels: Record<UserStatus, string> = {
      [UserStatus.NEWBIE]: 'Débutant',
      [UserStatus.MEMBER]: 'Membre',
      [UserStatus.CONTRIBUTOR]: 'Contributeur',
      [UserStatus.DISCOVERER]: 'Découvreur',
      [UserStatus.INFLUENCER]: 'Influenceur',
      [UserStatus.LEADER]: 'Leader'
    };
    return labels[status] || status;
  };

  const getUserStatusColor = (status: UserStatus): string => {
    const colors: Record<UserStatus, string> = {
      [UserStatus.NEWBIE]: 'bg-gray-100 text-gray-800',
      [UserStatus.MEMBER]: 'bg-blue-100 text-blue-800',
      [UserStatus.CONTRIBUTOR]: 'bg-green-100 text-green-800',
      [UserStatus.DISCOVERER]: 'bg-yellow-100 text-yellow-800',
      [UserStatus.INFLUENCER]: 'bg-purple-100 text-purple-800',
      [UserStatus.LEADER]: 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return <Shield size={16} className="text-red-600" />;
      case UserRole.BUSINESS:
        return <Building size={16} className="text-blue-600" />;
      default:
        return <Users size={16} className="text-gray-600" />;
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedUsers.length === 0) {
      alert('Veuillez sélectionner au moins un utilisateur');
      return;
    }

    const confirmMessage = `Êtes-vous sûr de vouloir ${action} ${selectedUsers.length} utilisateur(s) ?`;
    if (!window.confirm(confirmMessage)) return;

    try {
      for (const userId of selectedUsers) {
        await AdminService.executeUserAction({
          userId,
          action: action as any,
          reason: `Action en lot: ${action}`
        });
      }

      setSelectedUsers([]);
      await loadUsers();
      setShowBulkActionModal(false);
    } catch (error) {
      console.error('Erreur lors de l\'action en lot:', error);
      alert('Erreur lors de l\'exécution de l\'action en lot');
    }
  };

  const handleSelectUser = (userId: string) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === paginatedUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(paginatedUsers.map(user => user.id));
    }
  };

  const exportUsers = () => {
    const csvContent = [
      ['ID', 'Nom d\'utilisateur', 'Email', 'Rôle', 'Statut', 'Pays', 'Ville', 'Inscription', 'Dernière connexion'],
      ...filteredUsers.map(user => [
        user.id,
        user.username,
        user.email,
        user.role,
        user.status,
        user.country || '',
        user.city || '',
        new Date(user.created_at).toLocaleDateString('fr-FR'),
        user.last_login ? new Date(user.last_login).toLocaleDateString('fr-FR') : 'Jamais'
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `utilisateurs_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return 'bg-red-100 text-red-800';
      case UserRole.BUSINESS:
        return 'bg-blue-100 text-blue-800';
      case UserRole.STANDARD:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: UserStatus) => {
    switch (status) {
      case UserStatus.LEADER:
        return 'bg-purple-100 text-purple-800';
      case UserStatus.INFLUENCER:
        return 'bg-pink-100 text-pink-800';
      case UserStatus.DISCOVERER:
        return 'bg-orange-100 text-orange-800';
      case UserStatus.CONTRIBUTOR:
        return 'bg-yellow-100 text-yellow-800';
      case UserStatus.MEMBER:
        return 'bg-blue-100 text-blue-800';
      case UserStatus.NEWBIE:
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const UserActionModal: React.FC<{ user: User; onClose: () => void }> = ({ user, onClose }) => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Actions pour {user.username}</h3>
        <div className="space-y-2">
          <Button
            variant="outline"
            className="w-full justify-start"
            leftIcon={<Eye size={16} />}
            onClick={() => {
              // Ouvrir la modal de détails
              setShowUserDetailModal(true);
              onClose();
            }}
          >
            Voir le profil
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start"
            leftIcon={<UserCheck size={16} />}
            onClick={() => {
              if (user.is_verified) {
                alert('Cet utilisateur est déjà vérifié');
                return;
              }

              if (window.confirm(`Êtes-vous sûr de vouloir vérifier le compte de ${user.username} ?`)) {
                handleUserAction({
                  userId: user.id,
                  action: 'verify',
                  reason: 'Vérification manuelle par administrateur'
                });
                onClose();
              }
            }}
          >
            Vérifier le compte
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start"
            leftIcon={<Ban size={16} />}
            onClick={() => {
              if (user.suspended_until && new Date(user.suspended_until) > new Date()) {
                alert('Cet utilisateur est déjà suspendu');
                return;
              }

              const duration = prompt('Durée de suspension en jours (1-365):', '7');
              if (!duration || isNaN(Number(duration)) || Number(duration) < 1 || Number(duration) > 365) {
                alert('Veuillez entrer une durée valide entre 1 et 365 jours');
                return;
              }

              const reason = prompt('Raison de la suspension (optionnel):', 'Suspension administrative');

              if (window.confirm(`Êtes-vous sûr de vouloir suspendre ${user.username} pour ${duration} jour(s) ?`)) {
                handleUserAction({
                  userId: user.id,
                  action: 'suspend',
                  reason: reason || 'Suspension administrative',
                  duration: Number(duration)
                });
                onClose();
              }
            }}
          >
            Suspendre (7 jours)
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start"
            leftIcon={<Shield size={16} />}
            onClick={() => {
              if (user.role === UserRole.ADMIN) {
                alert('Cet utilisateur est déjà administrateur');
                return;
              }

              const confirmMessage = `⚠️ ATTENTION ⚠️\n\nVous êtes sur le point de promouvoir ${user.username} au rang d'administrateur.\n\nCette action lui donnera des privilèges étendus sur la plateforme.\n\nÊtes-vous absolument certain de vouloir continuer ?`;

              if (window.confirm(confirmMessage)) {
                const reason = prompt('Raison de la promotion (optionnel):', 'Promotion en administrateur');

                handleUserAction({
                  userId: user.id,
                  action: 'change_role',
                  newRole: UserRole.ADMIN,
                  reason: reason || 'Promotion en administrateur'
                });
                onClose();
              }
            }}
          >
            Promouvoir Admin
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start text-red-600 hover:text-red-700"
            leftIcon={<Trash2 size={16} />}
            onClick={() => {
              const confirmMessage = `🚨 SUPPRESSION DÉFINITIVE 🚨\n\nVous êtes sur le point de SUPPRIMER DÉFINITIVEMENT le compte de ${user.username}.\n\n⚠️ Cette action est IRRÉVERSIBLE ⚠️\n\nToutes les données de l'utilisateur seront perdues :\n- Profil et informations personnelles\n- Publications et commentaires\n- Historique d'activité\n- Connexions et followers\n\nTapez "SUPPRIMER" pour confirmer :`;

              const confirmation = prompt(confirmMessage);

              if (confirmation === 'SUPPRIMER') {
                const reason = prompt('Raison de la suppression (obligatoire) :', '');

                if (!reason || reason.trim().length < 10) {
                  alert('Veuillez fournir une raison détaillée (minimum 10 caractères)');
                  return;
                }

                handleUserAction({
                  userId: user.id,
                  action: 'delete',
                  reason: reason.trim()
                });
                onClose();
              } else if (confirmation !== null) {
                alert('Suppression annulée. Vous devez taper exactement "SUPPRIMER" pour confirmer.');
              }
            }}
          >
            Supprimer le compte
          </Button>

          {/* Boutons supplémentaires selon le statut */}
          {user.suspended_until && new Date(user.suspended_until) > new Date() && (
            <Button
              variant="outline"
              className="w-full justify-start text-green-600 hover:text-green-700"
              leftIcon={<CheckCircle size={16} />}
              onClick={() => {
                if (window.confirm(`Êtes-vous sûr de vouloir réactiver le compte de ${user.username} ?`)) {
                  handleUserAction({
                    userId: user.id,
                    action: 'activate',
                    reason: 'Réactivation par administrateur'
                  });
                  onClose();
                }
              }}
            >
              Réactiver le compte
            </Button>
          )}

          {user.role === UserRole.BUSINESS && (
            <Button
              variant="outline"
              className="w-full justify-start text-blue-600 hover:text-blue-700"
              leftIcon={<Building size={16} />}
              onClick={() => {
                // Ouvrir les détails business
                setShowUserDetailModal(true);
                onClose();
              }}
            >
              Détails Entreprise
            </Button>
          )}

          <Button
            variant="outline"
            className="w-full justify-start text-gray-600 hover:text-gray-700"
            leftIcon={<MessageSquare size={16} />}
            onClick={() => {
              const message = prompt('Message à envoyer à l\'utilisateur (optionnel):', '');
              if (message && message.trim()) {
                // Logique pour envoyer un message/notification
                alert(`Message envoyé à ${user.username}: "${message.trim()}"`);
                onClose();
              }
            }}
          >
            Envoyer un message
          </Button>

          <Button
            variant="outline"
            className="w-full justify-start text-indigo-600 hover:text-indigo-700"
            leftIcon={<Globe size={16} />}
            onClick={() => {
              // Ouvrir le profil public dans un nouvel onglet
              window.open(`/profile/${user.username}`, '_blank');
            }}
          >
            Voir profil public
          </Button>
        </div>

        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 mb-3">
            <strong>Informations rapides:</strong><br/>
            • Inscrit le: {new Date(user.created_at).toLocaleDateString('fr-FR')}<br/>
            • Statut: {user.status}<br/>
            • Posts: {user.post_count || 0} | Followers: {user.followers_count || 0}<br/>
            {user.country && `• Localisation: ${user.city ? user.city + ', ' : ''}${user.country}`}<br/>
            {user.suspended_until && new Date(user.suspended_until) > new Date() &&
              `• Suspendu jusqu'au: ${new Date(user.suspended_until).toLocaleDateString('fr-FR')}`
            }
            {user.business_name && `• Entreprise: ${user.business_name}`}
          </div>

          <Button variant="outline" onClick={onClose} className="w-full">
            Fermer
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gestion des Utilisateurs</h2>
          <p className="text-gray-600 mt-1">Gérer les comptes utilisateurs de la plateforme</p>
        </div>
        <div className="flex space-x-2">
          {selectedUsers.length > 0 && (
            <Button
              variant="outline"
              leftIcon={<Target size={16} />}
              onClick={() => setShowBulkActionModal(true)}
              className="text-orange-600 border-orange-600 hover:bg-orange-50"
            >
              Actions ({selectedUsers.length})
            </Button>
          )}
          <Button
            variant="outline"
            leftIcon={<Download size={16} />}
            onClick={exportUsers}
          >
            Exporter
          </Button>
          <Button
            onClick={() => loadUsers()}
            leftIcon={<Users size={16} />}
          >
            Actualiser
          </Button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users size={20} className="text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-lg font-semibold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle size={20} className="text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Actifs</p>
                <p className="text-lg font-semibold text-gray-900">{stats.active}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <XCircle size={20} className="text-red-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Suspendus</p>
                <p className="text-lg font-semibold text-gray-900">{stats.suspended}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Award size={20} className="text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Vérifiés</p>
                <p className="text-lg font-semibold text-gray-900">{stats.verified}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <Building size={20} className="text-indigo-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Entreprises</p>
                <p className="text-lg font-semibold text-gray-900">{stats.businesses}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <TrendingUp size={20} className="text-yellow-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Nouveaux</p>
                <p className="text-lg font-semibold text-gray-900">{stats.newToday}</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Filtres et recherche avancés */}
      <Card>
        <CardBody className="p-4">
          <div className="space-y-4">
            {/* Première ligne : Recherche et filtres principaux */}
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Rechercher par nom, email ou entreprise..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <select
                  value={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value as UserRole | 'all')}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Tous les rôles</option>
                  <option value={UserRole.STANDARD}>Standard</option>
                  <option value={UserRole.BUSINESS}>Business</option>
                  <option value={UserRole.ADMIN}>Admin</option>
                </select>

                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value as UserStatus | 'all')}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Tous les statuts</option>
                  <option value={UserStatus.NEWBIE}>Débutant</option>
                  <option value={UserStatus.MEMBER}>Membre</option>
                  <option value={UserStatus.CONTRIBUTOR}>Contributeur</option>
                  <option value={UserStatus.DISCOVERER}>Découvreur</option>
                  <option value={UserStatus.INFLUENCER}>Influenceur</option>
                  <option value={UserStatus.LEADER}>Leader</option>
                </select>
              </div>
            </div>

            {/* Deuxième ligne : Filtres avancés */}
            <div className="flex flex-col lg:flex-row gap-4">
              <select
                value={selectedVerification}
                onChange={(e) => setSelectedVerification(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Tous (vérifiés/non vérifiés)</option>
                <option value="verified">Vérifiés uniquement</option>
                <option value="unverified">Non vérifiés uniquement</option>
              </select>

              <input
                type="text"
                placeholder="Filtrer par pays..."
                value={selectedCountry === 'all' ? '' : selectedCountry}
                onChange={(e) => setSelectedCountry(e.target.value || 'all')}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />

              <input
                type="date"
                placeholder="Date de début"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />

              <input
                type="date"
                placeholder="Date de fin"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />

              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  setSortBy(field);
                  setSortOrder(order as 'asc' | 'desc');
                  loadUsers(); // Recharger avec le nouveau tri
                }}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="created_at-desc">Plus récents</option>
                <option value="created_at-asc">Plus anciens</option>
                <option value="username-asc">Nom A-Z</option>
                <option value="username-desc">Nom Z-A</option>
                <option value="post_count-desc">Plus actifs</option>
                <option value="followers_count-desc">Plus suivis</option>
              </select>
            </div>

            {/* Troisième ligne : Résultats et pagination */}
            <div className="flex justify-between items-center pt-2 border-t">
              <div className="text-sm text-gray-600">
                {filteredUsers.length} utilisateur(s) trouvé(s) sur {users.length} total
                {selectedUsers.length > 0 && ` • ${selectedUsers.length} sélectionné(s)`}
              </div>

              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Afficher:</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => {
                    setItemsPerPage(Number(e.target.value));
                    setCurrentPage(1);
                  }}
                  className="px-2 py-1 border border-gray-300 rounded text-sm"
                >
                  <option value={10}>10</option>
                  <option value={20}>20</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
                <span className="text-sm text-gray-600">par page</span>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>



      {/* Liste des utilisateurs */}
      <Card>
        <CardBody className="p-0">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : paginatedUsers.length === 0 ? (
            <div className="text-center py-12">
              <Users size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun utilisateur trouvé</h3>
              <p className="text-gray-600">Aucun utilisateur ne correspond à vos critères de recherche.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectedUsers.length === paginatedUsers.length && paginatedUsers.length > 0}
                        onChange={handleSelectAll}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Utilisateur
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rôle & Statut
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Localisation
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Activité
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Inscription
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedUsers.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedUsers.includes(user.id)}
                          onChange={() => handleSelectUser(user.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <img
                              className="h-10 w-10 rounded-full object-cover"
                              src={user.profile_picture || '/default-avatar.png'}
                              alt={user.username}
                            />
                          </div>
                          <div className="ml-4">
                            <div className="flex items-center space-x-2">
                              <div className="text-sm font-medium text-gray-900">
                                {user.username}
                              </div>
                              <VerificationBadge
                                isVerified={user.is_verified || false}
                                userRole={user.role}
                                businessVerified={user.role === UserRole.BUSINESS ? user.business_verified || false : false}
                                size="xs"
                                variant="minimal"
                                showTooltip={true}
                              />
                              {getRoleIcon(user.role)}
                            </div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                            {user.business_name && (
                              <div className="text-xs text-blue-600 flex items-center">
                                <Building size={12} className="mr-1" />
                                {user.business_name}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            user.role === UserRole.ADMIN ? 'bg-red-100 text-red-800' :
                            user.role === UserRole.BUSINESS ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {user.role === UserRole.ADMIN ? 'Admin' :
                             user.role === UserRole.BUSINESS ? 'Business' : 'Standard'}
                          </span>
                          <br />
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getUserStatusColor(user.status)}`}>
                            {getUserStatusLabel(user.status)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          {user.country && (
                            <div className="flex items-center">
                              <MapPin size={12} className="mr-1 text-gray-400" />
                              {user.country}
                            </div>
                          )}
                          {user.city && (
                            <div className="text-xs text-gray-500">{user.city}</div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="space-y-1">
                          <div className="flex items-center">
                            <MessageSquare size={12} className="mr-1 text-gray-400" />
                            {user.post_count || 0} posts
                          </div>
                          <div className="flex items-center">
                            <Heart size={12} className="mr-1 text-gray-400" />
                            {user.followers_count || 0} followers
                          </div>
                          {user.last_login && (
                            <div className="text-xs text-gray-500">
                              Dernière connexion: {formatTimeAgo(user.last_login)}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>
                          {new Date(user.created_at).toLocaleDateString('fr-FR')}
                        </div>
                        <div className="text-xs">
                          {formatTimeAgo(user.created_at)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setActionUser(user);
                              setShowUserDetailModal(true);
                            }}
                            title="Voir les détails"
                          >
                            <Eye size={14} />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setActionUser(user);
                              setShowActionModal(true);
                            }}
                            title="Actions"
                          >
                            <MoreVertical size={14} />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Page {currentPage} sur {totalPages} • {filteredUsers.length} résultat(s)
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Précédent
                </Button>

                {/* Pages */}
                <div className="flex space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? "primary" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                        className="w-8 h-8 p-0"
                      >
                        {page}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Suivant
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Modal d'actions */}
      {showActionModal && actionUser && (
        <UserActionModal
          user={actionUser}
          onClose={() => {
            setShowActionModal(false);
            setActionUser(null);
          }}

        />
      )}

      {/* Modal de détails utilisateur */}
      {showUserDetailModal && actionUser && (
        <UserDetailModal
          user={actionUser}
          onClose={() => {
            setShowUserDetailModal(false);
            setActionUser(null);
          }}
        />
      )}

      {/* Modal d'actions en lot */}
      {showBulkActionModal && (
        <BulkActionModal
          selectedCount={selectedUsers.length}
          onClose={() => setShowBulkActionModal(false)}
          onAction={handleBulkAction}
        />
      )}
    </div>
  );
};

// Modal d'actions utilisateur améliorée
interface UserActionModalProps {
  user: User;
  onClose: () => void;
  onAction: (action: UserManagementAction) => void;
}

const UserActionModal: React.FC<UserActionModalProps> = ({ user, onClose, onAction }) => {
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [reason, setReason] = useState('');
  const [duration, setDuration] = useState<number>(7);

  const handleSubmit = () => {
    if (!selectedAction) return;

    onAction({
      userId: user.id,
      action: selectedAction as any,
      reason: reason || `Action ${selectedAction} par admin`,
      duration: selectedAction === 'suspend' ? duration : undefined
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Actions pour {user.username}</h3>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Action à effectuer
            </label>
            <select
              value={selectedAction}
              onChange={(e) => setSelectedAction(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Sélectionner une action</option>
              <option value="verify">Vérifier le compte</option>
              <option value="suspend">Suspendre temporairement</option>
              <option value="activate">Activer le compte</option>
              <option value="change_role">Changer le rôle</option>
            </select>
          </div>

          {selectedAction === 'suspend' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Durée de suspension (jours)
              </label>
              <input
                type="number"
                value={duration}
                onChange={(e) => setDuration(Number(e.target.value))}
                min="1"
                max="365"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Raison (optionnel)
            </label>
            <textarea
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Expliquez la raison de cette action..."
            />
          </div>
        </div>

        <div className="flex justify-end space-x-2 mt-6">
          <Button variant="outline" onClick={onClose}>
            Annuler
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!selectedAction}
          >
            Exécuter
          </Button>
        </div>
      </div>
    </div>
  );
};

// Modal de détails utilisateur
interface UserDetailModalProps {
  user: User;
  onClose: () => void;
}

const UserDetailModal: React.FC<UserDetailModalProps> = ({ user, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold">Détails de {user.username}</h3>
          <Button variant="outline" size="sm" onClick={onClose}>
            <XCircle size={16} />
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Informations de base */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Informations de base</h4>
            <div className="space-y-2">
              <div className="flex items-center">
                <img
                  className="h-16 w-16 rounded-full object-cover"
                  src={user.profile_picture || '/default-avatar.png'}
                  alt={user.username}
                />
                <div className="ml-4">
                  <div className="flex items-center space-x-2">
                    <div className="font-medium">{user.username}</div>
                    <VerificationBadge
                      isVerified={user.is_verified || false}
                      userRole={user.role}
                      businessVerified={user.role === UserRole.BUSINESS ? user.business_verified || false : false}
                      size="sm"
                      variant="default"
                      showTooltip={true}
                    />
                  </div>
                  <div className="text-sm text-gray-500">{user.email}</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2 text-sm">
                <div><strong>Rôle:</strong> {user.role}</div>
                <div><strong>Statut:</strong> {user.status}</div>
                <div><strong>Vérifié:</strong> {user.is_verified ? 'Oui' : 'Non'}</div>
                <div><strong>Âge:</strong> {user.age || 'Non renseigné'}</div>
                <div><strong>Genre:</strong> {user.gender || 'Non renseigné'}</div>
                <div><strong>Profession:</strong> {user.profession || 'Non renseignée'}</div>
              </div>
            </div>
          </div>

          {/* Localisation */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Localisation</h4>
            <div className="space-y-2 text-sm">
              <div><strong>Pays:</strong> {user.country || 'Non renseigné'}</div>
              <div><strong>Ville:</strong> {user.city || 'Non renseignée'}</div>
            </div>
          </div>

          {/* Activité */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Activité</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div><strong>Posts:</strong> {user.post_count || 0}</div>
              <div><strong>Commentaires:</strong> {user.comment_count || 0}</div>
              <div><strong>Followers:</strong> {user.followers_count || 0}</div>
              <div><strong>Following:</strong> {user.following_count || 0}</div>
              <div><strong>Recommandations:</strong> {user.recommendations_count || 0}</div>
            </div>
          </div>

          {/* Dates importantes */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Dates importantes</h4>
            <div className="space-y-2 text-sm">
              <div><strong>Inscription:</strong> {new Date(user.created_at).toLocaleDateString('fr-FR')}</div>
              <div><strong>Dernière connexion:</strong> {user.last_login ? new Date(user.last_login).toLocaleDateString('fr-FR') : 'Jamais'}</div>
              <div><strong>Profil complété:</strong> {user.profile_completed_at ? new Date(user.profile_completed_at).toLocaleDateString('fr-FR') : 'Non complété'}</div>
            </div>
          </div>

          {/* Informations business (si applicable) */}
          {user.role === UserRole.BUSINESS && (
            <div className="col-span-2 space-y-4">
              <h4 className="font-medium text-gray-900">Informations Business</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div><strong>Nom de l'entreprise:</strong> {user.business_name || 'Non renseigné'}</div>
                <div><strong>Statut business:</strong> {user.business_status || 'Non défini'}</div>
                <div><strong>Catégorie:</strong> {user.business_category || 'Non renseignée'}</div>
                <div><strong>Vérifié business:</strong> {user.business_verified ? 'Oui' : 'Non'}</div>
              </div>
              {user.business_description && (
                <div>
                  <strong>Description:</strong>
                  <p className="mt-1 text-gray-600">{user.business_description}</p>
                </div>
              )}
            </div>
          )}

          {/* Bio et informations supplémentaires */}
          {(user.bio || user.website || user.phone || user.interests) && (
            <div className="col-span-2 space-y-4">
              <h4 className="font-medium text-gray-900">Informations supplémentaires</h4>
              {user.bio && (
                <div>
                  <strong>Bio:</strong>
                  <p className="mt-1 text-gray-600">{user.bio}</p>
                </div>
              )}
              {user.website && (
                <div className="flex items-center">
                  <Globe size={16} className="mr-2 text-gray-400" />
                  <a href={user.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                    {user.website}
                  </a>
                </div>
              )}
              {user.phone && (
                <div className="flex items-center">
                  <Phone size={16} className="mr-2 text-gray-400" />
                  <span>{user.phone}</span>
                </div>
              )}
              {user.interests && (
                <div>
                  <strong>Centres d'intérêt:</strong>
                  <p className="mt-1 text-gray-600">{user.interests}</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Modal d'actions en lot
interface BulkActionModalProps {
  selectedCount: number;
  onClose: () => void;
  onAction: (action: string) => void;
}

const BulkActionModal: React.FC<BulkActionModalProps> = ({ selectedCount, onClose, onAction }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Actions en lot ({selectedCount} utilisateurs)</h3>

        <div className="space-y-2">
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => onAction('verify')}
          >
            <UserCheck size={16} className="mr-2" />
            Vérifier tous les comptes
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => onAction('suspend')}
          >
            <Ban size={16} className="mr-2" />
            Suspendre tous les comptes
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => onAction('activate')}
          >
            <CheckCircle size={16} className="mr-2" />
            Activer tous les comptes
          </Button>
        </div>

        <div className="flex justify-end space-x-2 mt-6">
          <Button variant="outline" onClick={onClose}>
            Annuler
          </Button>
        </div>
      </div>
    </div>
  );
};

export default UserManagement;
