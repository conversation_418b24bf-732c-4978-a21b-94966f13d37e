import React from 'react';
import { User, Store, Bookmark, ShoppingBag, Heart, MessageSquare, Settings, Brain, Globe, Star, TrendingUp, Tag, Building2, Megaphone, Package } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { UserRole, IBusinessUser, BusinessStatus } from '../../types'; // Removed unused UserStatus import
import Avatar from '../ui/Avatar';
import UserStatusBadge from '../ui/UserStatusBadge'; // Added
import BusinessStatusBadge from '../ui/BusinessStatusBadge'; // Added

interface SidebarProps {
  isOpen: boolean;
  currentPage: 'home' | 'profile' | 'marketplace' | 'recommendations' | 'support' | 'my-recommendations' | 'top-products' | 'trending' | 'businesses' | 'search-results' | 'ai-recommendations' | 'business-orders' | 'business-sales' | 'business-products' | 'business-rankings' | 'business-reviews' | 'business-ads' | 'orders' | 'user-reviews' | 'user-status-levels' | 'users' | 'settings' | 'product-management'; // Added 'product-management'
  onProfileClick?: () => void;
  onMarketplaceClick?: () => void;
  onMyRecommendationsClick?: () => void;
  onTopProductsClick?: () => void;
  onTrendingClick?: () => void;
  onBusinessesClick?: () => void;
  onAIRecommendationsClick?: () => void; // Added for AI recommendations navigation
  onOrdersClick?: () => void; // Added for user orders navigation
  onUserReviewsClick?: () => void; // Added for user reviews navigation
  onUserStatusLevelsClick?: () => void; // Added for user status levels navigation
  onUsersClick?: () => void; // Added for users page navigation
  onSettingsClick?: () => void; // Added for settings navigation
  onBusinessOrdersClick?: () => void; // Added for business orders navigation
  onBusinessSalesClick?: () => void; // Added for business sales navigation
  onBusinessProductsClick?: () => void; // Added for business products navigation
  onBusinessRankingsClick?: () => void; // Added for business rankings navigation
  onBusinessReviewsClick?: () => void; // Added for business reviews navigation
  onBusinessAdsClick?: () => void; // Added for business ads navigation
  // onSearchSubmit is not typically handled by sidebar, but currentPage needs to know about 'search-results' for active state if any sidebar link could lead there indirectly.
  // For now, assuming no direct search link in sidebar.
}

const Sidebar: React.FC<SidebarProps> = ({
  isOpen,
  currentPage,
  onProfileClick,
  onMarketplaceClick,
  onMyRecommendationsClick,
  onTopProductsClick,
  onTrendingClick,
  onBusinessesClick,
  onAIRecommendationsClick,
  onOrdersClick,
  onUserReviewsClick,
  onUserStatusLevelsClick,
  onUsersClick,
  onSettingsClick,
  onBusinessOrdersClick,
  onBusinessSalesClick,
  onBusinessProductsClick,
  onBusinessRankingsClick,
  onBusinessReviewsClick,
  onBusinessAdsClick
  // No onSearchSubmit needed here directly
}) => {
  const { currentUser } = useAuth();

  // Add a 'page' identifier to links that navigate
  const standardUserLinks = [
    { icon: <User size={20} />, label: 'Mon profil', onClick: onProfileClick, page: 'profile' },
    { icon: <Brain size={20} />, label: 'Recommandations IA', onClick: onAIRecommendationsClick, page: 'ai-recommendations' },
    { icon: <Bookmark size={20} />, label: 'Mes commandes', onClick: onOrdersClick, page: 'orders' },
    { icon: <Heart size={20} />, label: 'Mes recommandations', onClick: onMyRecommendationsClick, page: 'my-recommendations' },
    { icon: <MessageSquare size={20} />, label: 'Mes avis', onClick: onUserReviewsClick, page: 'user-reviews' },
    { icon: <Settings size={20} />, label: 'Paramètres', onClick: onSettingsClick, page: 'settings' },
  ];

  const businessUserLinks = [
    { icon: <Store size={20} />, label: 'Mon entreprise', onClick: onProfileClick, page: 'profile' }, // Assuming business profile is also 'profile'
    { icon: <Package size={20} />, label: 'Gestion des Produits', onClick: onBusinessProductsClick, page: 'product-management' },
    { icon: <Bookmark size={20} />, label: 'Mes commandes', onClick: onBusinessOrdersClick, page: 'business-orders' },
    { icon: <ShoppingBag size={20} />, label: 'Mes ventes', onClick: onBusinessSalesClick, page: 'business-sales' },
    { icon: <Globe size={20} />, label: 'Mes classements', onClick: onBusinessRankingsClick, page: 'business-rankings' },
    { icon: <Megaphone size={20} />, label: 'Mes publicités', onClick: onBusinessAdsClick, page: 'business-ads' },
    { icon: <Settings size={20} />, label: 'Paramètres', onClick: onSettingsClick, page: 'settings' },
  ];

  const adminLinks = [
    { icon: <User size={20} />, label: 'Gestion utilisateurs', onClick: onUsersClick, page: 'users' },
    { icon: <Store size={20} />, label: 'Gestion entreprises', onClick: onBusinessesClick, page: 'businesses' },
    { icon: <MessageSquare size={20} />, label: 'Modération contenus', onClick: undefined, page: 'moderation' },
    { icon: <Globe size={20} />, label: 'Gestion publicités', onClick: undefined, page: 'ads-management' },
    { icon: <Settings size={20} />, label: 'Paramètres', onClick: onSettingsClick, page: 'settings' },
  ];

  let links = standardUserLinks;

  if (currentUser?.role === UserRole.BUSINESS) {
    links = businessUserLinks;
  } else if (currentUser?.role === UserRole.ADMIN) {
    links = adminLinks;
  }

  return (
    <aside
      className={`bg-white border-r border-gray-200 w-64 fixed h-full overflow-y-auto transition-transform duration-300 ease-in-out transform ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      } md:translate-x-0 z-30`}
    >
      {currentUser ? (
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Avatar
              src={currentUser.profilePicture}
              alt={currentUser.role === UserRole.BUSINESS && (currentUser as any).businessName ? (currentUser as any).businessName : currentUser.username}
              size="md"
              status="online"
            />
            <div className="flex-1">
              <h3 className="font-medium text-gray-900 mb-1">
                {currentUser.role === UserRole.BUSINESS && (currentUser as any).businessName
                  ? (currentUser as any).businessName
                  : currentUser.username}
              </h3>
              <p className="text-xs text-gray-500 mb-2">
                {currentUser.role === UserRole.BUSINESS
                  ? `@${currentUser.username.toLowerCase()}`
                  : `@${currentUser.username.toLowerCase()}`
                }
              </p>
              {/* Badge de statut - Entreprise (priorité) ou Utilisateur normal */}
              {currentUser.role === UserRole.BUSINESS ? (
                'businessStatus' in currentUser && (currentUser as IBusinessUser).businessStatus ? (
                  <BusinessStatusBadge
                    status={(currentUser as IBusinessUser).businessStatus}
                    size="sm"
                    variant="gradient"
                    className="shadow-sm"
                  />
                ) : (
                  /* Badge par défaut pour les entreprises sans businessStatus */
                  <BusinessStatusBadge
                    status={BusinessStatus.NEW}
                    size="sm"
                    variant="gradient"
                    className="shadow-sm"
                  />
                )
              ) : currentUser.status ? (
                /* Badge de statut - Utilisateur normal */
                <UserStatusBadge
                  status={currentUser.status}
                  size="sm"
                  variant="gradient"
                  className="shadow-sm"
                />
              ) : null}
            </div>
          </div>
        </div>
      ) : (
        <div className="p-4 border-b border-gray-200">
          <p className="text-sm text-gray-700">Connectez-vous pour accéder à toutes les fonctionnalités</p>
        </div>
      )}

      <nav className="mt-4">
        <div className="px-4 mb-2">
          <h5 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">Menu</h5>
        </div>
        <ul className="space-y-1">
          {links.map((link, index) => (
            <li key={index}>
              {link.onClick ? (
                <button
                  onClick={link.onClick}
                  className={`w-full flex items-center px-4 py-2 text-sm rounded-md
                              ${link.page && currentPage === link.page
                                ? 'bg-blue-100 text-blue-700 font-semibold'
                                : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'}`}
                >
                  <span className={`mr-3 ${link.page && currentPage === link.page ? 'text-blue-600' : 'text-gray-500'}`}>{link.icon}</span>
                  {link.label}
                </button>
              ) : (
                <div className="flex items-center px-4 py-2 text-sm text-gray-400 cursor-not-allowed rounded-md">
                  <span className="mr-3 text-gray-400">{link.icon}</span>
                  {link.label} <span className="ml-2 text-xs">(Bientôt)</span>
                </div>
              )}
            </li>
          ))}
        </ul>
      </nav>

      <div className="px-4 mt-8 mb-2">
        <h5 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">Découvrir</h5>
      </div>
      <ul className="px-2 space-y-1">
        <li>
          <button
            onClick={onMarketplaceClick}
            className={`w-full flex items-center px-4 py-2 text-sm rounded-md
                      ${currentPage === 'marketplace'
                        ? 'bg-blue-100 text-blue-700 font-semibold'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'}`}
          >
            <span className={`mr-3 ${currentPage === 'marketplace' ? 'text-blue-600' : 'text-gray-500'}`}>
              <ShoppingBag size={20} />
            </span>
            Marketplace
          </button>
        </li>
        <li>
          <button
            onClick={onTopProductsClick}
            className={`w-full flex items-center px-4 py-2 text-sm rounded-md
                      ${currentPage === 'top-products'
                        ? 'bg-blue-100 text-blue-700 font-semibold'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'}`}
          >
            <span className={`mr-3 ${currentPage === 'top-products' ? 'text-blue-600' : 'text-gray-500'}`}>
              <Star size={20} />
            </span>
            Top produits
          </button>
        </li>
        <li>
          <button
            onClick={onTrendingClick}
            className={`w-full flex items-center px-4 py-2 text-sm rounded-md
                      ${currentPage === 'trending'
                        ? 'bg-blue-100 text-blue-700 font-semibold'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'}`}
          >
            <span className={`mr-3 ${currentPage === 'trending' ? 'text-blue-600' : 'text-gray-500'}`}>
              <TrendingUp size={20} />
            </span>
            Tendances
          </button>
        </li>
        <li>
          <button
            onClick={onBusinessesClick}
            className={`w-full flex items-center px-4 py-2 text-sm rounded-md
                      ${currentPage === 'businesses'
                        ? 'bg-blue-100 text-blue-700 font-semibold'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'}`}
          >
            <span className={`mr-3 ${currentPage === 'businesses' ? 'text-blue-600' : 'text-gray-500'}`}>
              <Building2 size={20} />
            </span>
            Entreprises
          </button>
        </li>
        <li>
          <button
            onClick={onUserStatusLevelsClick}
            className={`w-full flex items-center px-4 py-2 text-sm rounded-md
                      ${currentPage === 'user-status-levels'
                        ? 'bg-blue-100 text-blue-700 font-semibold'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'}`}
          >
            <span className={`mr-3 ${currentPage === 'user-status-levels' ? 'text-blue-600' : 'text-gray-500'}`}>
              <TrendingUp size={20} />
            </span>
            Niveaux de statut
          </button>
        </li>
        <li>
          <button
            onClick={onUsersClick}
            className={`w-full flex items-center px-4 py-2 text-sm rounded-md
                      ${currentPage === 'users'
                        ? 'bg-blue-100 text-blue-700 font-semibold'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'}`}
          >
            <span className={`mr-3 ${currentPage === 'users' ? 'text-blue-600' : 'text-gray-500'}`}>
              <User size={20} />
            </span>
            Utilisateurs
          </button>
        </li>
      </ul>
    </aside>
  );
};

export default Sidebar;
