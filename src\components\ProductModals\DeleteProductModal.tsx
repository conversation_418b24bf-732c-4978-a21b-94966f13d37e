import React, { useState } from 'react';
import { 
  X, 
  Trash2, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  BarChart3
} from 'lucide-react';
import './ProductModals.css';

interface Product {
  id: string;
  name: string;
  price: number;
  stock: number;
  salesCount: number;
  revenue: number;
}

interface DeleteProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (productId: string) => void;
  product: Product | null;
}

const DeleteProductModal: React.FC<DeleteProductModalProps> = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  product 
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmText, setConfirmText] = useState('');

  if (!isOpen || !product) return null;

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('fr-FR').format(amount) + ' F CFA';
  };

  const handleConfirm = async () => {
    if (confirmText !== 'SUPPRIMER') return;

    setIsDeleting(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulation
      onConfirm(product.id);
      onClose();
      setConfirmText('');
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    if (!isDeleting) {
      setConfirmText('');
      onClose();
    }
  };

  const isConfirmValid = confirmText === 'SUPPRIMER';
  const hasImpact = product.salesCount > 0 || product.stock > 0;

  return (
    <div className="modal-overlay">
      <div className="modal-container">
        <div className="modal-header">
          <div className="modal-title">
            <Trash2 size={24} className="text-red-500" />
            <h2>Supprimer le produit</h2>
          </div>
          <button 
            className="modal-close" 
            onClick={handleClose}
            disabled={isDeleting}
          >
            <X size={20} />
          </button>
        </div>

        <div className="modal-content">
          {/* Avertissement principal */}
          <div className="warning-section">
            <div className="warning-icon">
              <AlertTriangle size={48} className="text-red-500" />
            </div>
            <div className="warning-content">
              <h3>Attention : Action irréversible</h3>
              <p>
                Vous êtes sur le point de supprimer définitivement le produit 
                <strong> "{product.name}"</strong>. Cette action ne peut pas être annulée.
              </p>
            </div>
          </div>

          {/* Informations sur l'impact */}
          {hasImpact && (
            <div className="impact-section">
              <h4>Impact de la suppression :</h4>
              <div className="impact-items">
                {product.stock > 0 && (
                  <div className="impact-item warning">
                    <AlertTriangle size={16} />
                    <span>
                      <strong>{product.stock} unités</strong> en stock seront perdues
                    </span>
                  </div>
                )}
                
                {product.salesCount > 0 && (
                  <div className="impact-item info">
                    <BarChart3 size={16} />
                    <span>
                      Historique de <strong>{product.salesCount} ventes</strong> 
                      ({formatAmount(product.revenue)}) sera conservé
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Alternatives suggérées */}
          <div className="alternatives-section">
            <h4>Alternatives suggérées :</h4>
            <div className="alternatives-list">
              <div className="alternative-item">
                <CheckCircle size={16} className="text-green-500" />
                <span>
                  <strong>Désactiver le produit</strong> - Le produit sera masqué mais les données conservées
                </span>
              </div>
              <div className="alternative-item">
                <CheckCircle size={16} className="text-green-500" />
                <span>
                  <strong>Marquer en rupture</strong> - Le produit sera indisponible temporairement
                </span>
              </div>
            </div>
          </div>

          {/* Confirmation de suppression */}
          <div className="confirmation-section">
            <h4>Confirmation requise :</h4>
            <p>
              Pour confirmer la suppression, tapez <strong>SUPPRIMER</strong> dans le champ ci-dessous :
            </p>
            <input
              type="text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value.toUpperCase())}
              placeholder="Tapez SUPPRIMER"
              className={`confirmation-input ${isConfirmValid ? 'valid' : ''}`}
              disabled={isDeleting}
              autoComplete="off"
            />
            {confirmText && !isConfirmValid && (
              <div className="confirmation-error">
                Vous devez taper exactement "SUPPRIMER"
              </div>
            )}
          </div>

          <div className="modal-actions">
            <button 
              type="button" 
              className="btn-secondary" 
              onClick={handleClose}
              disabled={isDeleting}
            >
              Annuler
            </button>
            <button 
              type="button" 
              className="btn-danger"
              onClick={handleConfirm}
              disabled={!isConfirmValid || isDeleting}
            >
              {isDeleting ? (
                <>
                  <BarChart3 size={16} className="spinning" />
                  Suppression...
                </>
              ) : (
                <>
                  <Trash2 size={16} />
                  Supprimer définitivement
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeleteProductModal;
