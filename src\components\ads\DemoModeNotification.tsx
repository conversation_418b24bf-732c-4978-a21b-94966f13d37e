import React, { useState } from 'react';
import { AlertTriangle, X, Info, Database } from 'lucide-react';
import { useAdBilling } from '../../context/AdBillingContext';
import '../../styles/DemoModeNotification.css';

const DemoModeNotification: React.FC = () => {
  const { wallet } = useAdBilling();
  const [isVisible, setIsVisible] = useState(true);

  // Afficher seulement si on est en mode démonstration
  const isDemoMode = wallet && wallet.id.startsWith('demo-wallet-');

  if (!isDemoMode || !isVisible) {
    return null;
  }

  return (
    <div className="demo-mode-notification">
      <div className="demo-notification-content">
        <div className="demo-icon">
          <Database size={20} />
        </div>
        <div className="demo-message">
          <h4>Mode Démonstration</h4>
          <p>
            Les tables de facturation ne sont pas encore créées. Vous utilisez actuellement 
            le mode démonstration avec des données fictives. 
            <strong> Toutes les transactions sont simulées.</strong>
          </p>
          <div className="demo-instructions">
            <Info size={14} />
            <span>
              Pour activer le système complet, exécutez le script SQL dans Supabase : 
              <code>scripts/setup-billing-tables.sql</code>
            </span>
          </div>
        </div>
        <button 
          className="demo-close-btn"
          onClick={() => setIsVisible(false)}
          title="Fermer cette notification"
        >
          <X size={16} />
        </button>
      </div>
    </div>
  );
};

export default DemoModeNotification;
