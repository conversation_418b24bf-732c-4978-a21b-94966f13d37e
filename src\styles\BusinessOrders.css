.business-orders-container {
  width: 100%;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.orders-header {
  margin-bottom: 24px;
}

.orders-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.orders-subtitle {
  font-size: 1rem;
  color: #6b7280;
}

.orders-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  border: 1px solid #e5e7eb;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
}

.orders-filters {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-input {
  width: 100%;
  padding: 10px 10px 10px 40px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.filter-container {
  display: flex;
  gap: 12px;
}

.filter-dropdown, .sort-dropdown {
  position: relative;
  display: inline-block;
}

.filter-button, .sort-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
  transition: background-color 0.2s;
}

.filter-button:hover, .sort-button:hover {
  background-color: #f3f4f6;
}

.filter-dropdown-content, .sort-dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  min-width: 180px;
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 4px;
}

.filter-dropdown:hover .filter-dropdown-content,
.sort-dropdown:hover .sort-dropdown-content {
  display: block;
}

.filter-option, .sort-option {
  padding: 8px 16px;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
  transition: background-color 0.2s;
}

.filter-option:hover, .sort-option:hover {
  background-color: #f9fafb;
}

.filter-option.selected, .sort-option.selected {
  background-color: #eff6ff;
  color: #3b82f6;
  font-weight: 500;
}

.no-orders {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: #9ca3af;
  text-align: center;
}

.no-orders p {
  margin-top: 16px;
  font-size: 1rem;
}

.orders-table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.orders-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.orders-table th {
  background-color: #f9fafb;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
}

.orders-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  color: #1f2937;
}

.orders-table tr:last-child td {
  border-bottom: none;
}

.orders-table tr:hover {
  background-color: #f9fafb;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-confirmed {
  background-color: #e0f2fe;
  color: #0369a1;
}

.status-shipped {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.status-delivered {
  background-color: #dcfce7;
  color: #15803d;
}

.status-cancelled {
  background-color: #fee2e2;
  color: #b91c1c;
}

.status-paid {
  background-color: #dcfce7;
  color: #15803d;
}

.status-failed {
  background-color: #fee2e2;
  color: #b91c1c;
}

.actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
}

.action-button:hover {
  background-color: #f3f4f6;
}

.view-button:hover {
  color: #3b82f6;
}

/* Modal des détails de commande */
.order-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: 16px;
}

.order-details-content {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 10px 15px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.order-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.order-details-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.close-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  transition: color 0.2s;
}

.close-button:hover {
  color: #1f2937;
}

.order-details-body {
  padding: 24px;
  flex: 1;
}

.order-details-section {
  margin-bottom: 24px;
}

.order-details-section h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.order-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 0.75rem;
  color: #6b7280;
}

.detail-value {
  font-size: 0.875rem;
  color: #1f2937;
}

.order-items-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.order-items-table th {
  background-color: #f9fafb;
  padding: 8px 12px;
  text-align: left;
  font-weight: 600;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
}

.order-items-table td {
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
  color: #1f2937;
}

.order-items-table tfoot td {
  padding: 12px;
  font-weight: 600;
}

.total-label {
  text-align: right;
  color: #4b5563;
}

.total-value {
  color: #1f2937;
  font-weight: 700;
}

.status-update-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.current-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-update {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-button {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: opacity 0.2s;
}

.status-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.order-timeline {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.timeline-item {
  display: flex;
  gap: 16px;
}

.timeline-date {
  min-width: 150px;
  font-size: 0.75rem;
  color: #6b7280;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.timeline-description {
  font-size: 0.75rem;
  color: #6b7280;
}

.order-details-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
}

.cancel-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button:hover {
  background-color: #e5e7eb;
}

.print-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #ffffff;
  background-color: #3b82f6;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.print-button:hover {
  background-color: #2563eb;
}

/* Responsive */
@media (max-width: 768px) {
  .orders-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-container {
    flex-direction: column;
  }

  .order-details-grid {
    grid-template-columns: 1fr;
  }

  .status-buttons {
    flex-direction: column;
  }
}

@media print {
  .order-details-modal {
    position: static;
    background: none;
    display: block;
  }

  .order-details-content {
    box-shadow: none;
    max-height: none;
  }

  .close-button,
  .order-details-footer,
  .status-update {
    display: none;
  }
}
