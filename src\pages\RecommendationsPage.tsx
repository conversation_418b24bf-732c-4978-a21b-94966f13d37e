import React, { useMemo } from 'react';
import { usePosts } from '../context/PostsContext';
import { IPost } from '../types';
import ProductCard from '../components/marketplace/ProductCard'; // Re-use ProductCard for display if suitable
// Or create a new RecommendedItemCard if the display needs to be different

interface RankedPost extends IPost {
  recommendationCount: number;
}

const RecommendationsPage: React.FC = () => {
  const { posts, loading } = usePosts();

  const rankedPostsByCategory = useMemo(() => {
    if (loading || posts.length === 0) {
      return {};
    }

    const postsWithRecommendationCount: RankedPost[] = posts.map(post => ({
      ...post,
      recommendationCount: post.recommendations?.length || 0,
    }));

    const categories: { [key: string]: RankedPost[] } = {};
    postsWithRecommendationCount.forEach(post => {
      if (!post.category) return; // Skip posts without a category
      if (!categories[post.category]) {
        categories[post.category] = [];
      }
      categories[post.category].push(post);
    });

    for (const category in categories) {
      categories[category].sort((a, b) => b.recommendationCount - a.recommendationCount);
    }
    return categories;
  }, [posts, loading]);

  if (loading) {
    return <div className="text-center py-10">Chargement des recommandations...</div>;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Produits Recommandés</h1>
        <p className="mt-2 text-gray-600">Découvrez les produits les plus recommandés par notre communauté.</p>
      </div>

      {Object.keys(rankedPostsByCategory).length === 0 && !loading && (
        <p className="text-gray-600">Aucune recommandation disponible pour le moment.</p>
      )}

      {Object.entries(rankedPostsByCategory).map(([category, rankedPosts]) => (
        <section key={category} className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6 border-b pb-2">
            Top Recommandations: {category}
          </h2>
          {rankedPosts.length === 0 ? (
            <p className="text-gray-500">Aucun produit recommandé dans cette catégorie pour le moment.</p>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {rankedPosts.map((post, index) => (
                // Assuming IPost can be cast to IProduct for ProductCard, or adapt ProductCard/create new card
                // For now, we might need to adjust what ProductCard expects or how we pass data.
                // ProductCard expects onNegotiateClick, which might not be relevant here.
                // Let's display a simplified version for now.
                <div key={post.id} className="border p-4 rounded-lg shadow-lg bg-white">
                  <img src={post.images[0] || 'https://via.placeholder.com/300x200?text=No+Image'} alt={post.productName} className="w-full h-48 object-cover rounded-md mb-3" />
                  <h3 className="text-lg font-semibold text-gray-800">{post.productName}</h3>
                  <p className="text-sm text-gray-600">{post.businessName}</p>
                  <p className="text-sm text-indigo-600 font-medium mt-1">Rang: #{index + 1}</p>
                  <p className="text-xs text-gray-500">Recommandations: {post.recommendationCount}</p>
                </div>
              ))}
            </div>
          )}
        </section>
      ))}
    </div>
  );
};

export default RecommendationsPage;
