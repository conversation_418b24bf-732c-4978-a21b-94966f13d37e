import React from 'react';
import { 
  X, 
  <PERSON>, 
  Trash2, 
  CheckSquare, 
  Square,
  DollarSign,
  Tag,
  Package,
  BarChart3,
  Percent
} from 'lucide-react';
import './BulkActionsBar.css';

interface BulkActionsBarProps {
  selectedCount: number;
  totalCount: number;
  onSelectAll: () => void;
  onDeselectAll: () => void;
  onBulkEdit: () => void;
  onBulkDelete: () => void;
  isAllSelected: boolean;
}

const BulkActionsBar: React.FC<BulkActionsBarProps> = ({
  selectedCount,
  totalCount,
  onSelectAll,
  onDeselectAll,
  onBulkEdit,
  onBulkDelete,
  isAllSelected
}) => {
  if (selectedCount === 0) return null;

  return (
    <div className="bulk-actions-bar">
      <div className="bulk-actions-content">
        <div className="selection-info">
          <div className="selection-count">
            <CheckSquare size={16} className="text-blue-600" />
            <span>
              <strong>{selectedCount}</strong> produit{selectedCount > 1 ? 's' : ''} sélectionné{selectedCount > 1 ? 's' : ''}
            </span>
          </div>
          
          <div className="selection-controls">
            {!isAllSelected ? (
              <button 
                className="select-all-btn"
                onClick={onSelectAll}
                title="Tout sélectionner"
              >
                <Square size={14} />
                Tout sélectionner ({totalCount})
              </button>
            ) : (
              <button 
                className="deselect-all-btn"
                onClick={onDeselectAll}
                title="Tout désélectionner"
              >
                <X size={14} />
                Tout désélectionner
              </button>
            )}
          </div>
        </div>

        <div className="bulk-actions">
          <div className="quick-actions">
            <button
              className="bulk-action-btn price"
              onClick={onBulkEdit}
              title="Modifier les prix"
            >
              <DollarSign size={16} />
              <span>Prix</span>
            </button>
            
            <button
              className="bulk-action-btn category"
              onClick={onBulkEdit}
              title="Changer la catégorie"
            >
              <Tag size={16} />
              <span>Catégorie</span>
            </button>
            
            <button
              className="bulk-action-btn stock"
              onClick={onBulkEdit}
              title="Gérer le stock"
            >
              <Package size={16} />
              <span>Stock</span>
            </button>
            
            <button
              className="bulk-action-btn status"
              onClick={onBulkEdit}
              title="Modifier le statut"
            >
              <BarChart3 size={16} />
              <span>Statut</span>
            </button>
            
            <button
              className="bulk-action-btn discount"
              onClick={onBulkEdit}
              title="Appliquer une remise"
            >
              <Percent size={16} />
              <span>Remise</span>
            </button>
          </div>

          <div className="main-actions">
            <button
              className="bulk-action-btn edit"
              onClick={onBulkEdit}
              title="Actions en lot"
            >
              <Edit size={16} />
              <span>Actions en lot</span>
            </button>
            
            <button
              className="bulk-action-btn delete"
              onClick={onBulkDelete}
              title="Supprimer la sélection"
            >
              <Trash2 size={16} />
              <span>Supprimer</span>
            </button>
          </div>
        </div>

        <button 
          className="close-bulk-actions"
          onClick={onDeselectAll}
          title="Fermer les actions en lot"
        >
          <X size={16} />
        </button>
      </div>
    </div>
  );
};

export default BulkActionsBar;
