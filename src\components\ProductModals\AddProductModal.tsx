import React, { useState, useRef, useEffect } from 'react';
import {
  X,
  Upload,
  Image as ImageIcon,
  Trash2,
  Plus,
  Tag,
  Package,
  DollarSign,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Edit
} from 'lucide-react';
import './ProductModals.css';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  category: string;
  subcategory: string;
  brand: string;
  sku: string;
  stock: number;
  minStock: number;
  images: string[];
  tags: string[];
  status: 'active' | 'inactive' | 'out_of_stock';
  weight?: number;
  dimensions?: { length: number; width: number; height: number };
  seoTitle?: string;
  seoDescription?: string;
}

interface AddProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (product: Omit<Product, 'id'>) => void;
}

const AddProductModal: React.FC<AddProductModalProps> = ({ isOpen, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    originalPrice: '',
    category: '',
    subcategory: '',
    brand: '',
    sku: '',
    stock: '',
    minStock: '5',
    tags: '',
    weight: '',
    length: '',
    width: '',
    height: '',
    seoTitle: '',
    seoDescription: ''
  });

  const [images, setImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const categories = [
    'Beauté', 'Cosmétiques', 'Hygiène', 'Parfums', 'Alimentation',
    'Boissons', 'Vêtements', 'Accessoires', 'Électronique', 'Maison'
  ];

  const subcategories: Record<string, string[]> = {
    'Beauté': ['Soins du visage', 'Soins du corps', 'Maquillage', 'Cheveux'],
    'Cosmétiques': ['Hydratation', 'Anti-âge', 'Protection solaire', 'Nettoyage'],
    'Hygiène': ['Nettoyage', 'Déodorants', 'Soins intimes', 'Bucco-dentaire'],
    'Parfums': ['Homme', 'Femme', 'Unisexe', 'Enfant'],
    'Alimentation': ['Épices', 'Conserves', 'Produits frais', 'Bio'],
    'Boissons': ['Jus', 'Sodas', 'Alcools', 'Thés et cafés']
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Auto-générer le SKU basé sur le nom
    if (field === 'name' && value) {
      const sku = value.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 8) + '-' + Date.now().toString().slice(-4);
      setFormData(prev => ({ ...prev, sku }));
    }

    // Auto-générer le titre SEO
    if (field === 'name' && value) {
      setFormData(prev => ({ ...prev, seoTitle: value }));
    }

    // Effacer l'erreur du champ
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);

    if (images.length + files.length > 5) {
      setErrors(prev => ({ ...prev, images: 'Maximum 5 images autorisées' }));
      return;
    }

    files.forEach(file => {
      if (file.size > 5 * 1024 * 1024) { // 5MB
        setErrors(prev => ({ ...prev, images: 'Taille maximum 5MB par image' }));
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });

    setImages(prev => [...prev, ...files]);
    setErrors(prev => ({ ...prev, images: '' }));
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Nom requis';
    if (!formData.description.trim()) newErrors.description = 'Description requise';
    if (!formData.price || parseFloat(formData.price) <= 0) newErrors.price = 'Prix valide requis';
    if (!formData.category) newErrors.category = 'Catégorie requise';
    if (!formData.brand.trim()) newErrors.brand = 'Marque requise';
    if (!formData.stock || parseInt(formData.stock) < 0) newErrors.stock = 'Stock valide requis';
    if (images.length === 0) newErrors.images = 'Au moins une image requise';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Simuler l'upload des images (à remplacer par vraie logique)
      const imageUrls = imagePreviews.map((_, index) => `/images/products/product-${Date.now()}-${index}.jpg`);

      const productData: Omit<Product, 'id'> = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        price: parseFloat(formData.price),
        originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,
        category: formData.category,
        subcategory: formData.subcategory || '',
        brand: formData.brand.trim(),
        sku: formData.sku || `PRD-${Date.now()}`,
        stock: parseInt(formData.stock),
        minStock: parseInt(formData.minStock),
        images: imageUrls,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        status: parseInt(formData.stock) > 0 ? 'active' : 'out_of_stock',
        weight: formData.weight ? parseFloat(formData.weight) : undefined,
        dimensions: formData.length && formData.width && formData.height ? {
          length: parseFloat(formData.length),
          width: parseFloat(formData.width),
          height: parseFloat(formData.height)
        } : undefined,
        seoTitle: formData.seoTitle || formData.name,
        seoDescription: formData.seoDescription || formData.description
      };

      onSave(productData);
      onClose();

      // Reset form
      setFormData({
        name: '', description: '', price: '', originalPrice: '', category: '',
        subcategory: '', brand: '', sku: '', stock: '', minStock: '5',
        tags: '', weight: '', length: '', width: '', height: '',
        seoTitle: '', seoDescription: ''
      });
      setImages([]);
      setImagePreviews([]);
      setErrors({});
    } catch (error) {
      setErrors({ submit: 'Erreur lors de la création du produit' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-container large">
        <div className="modal-header">
          <div className="modal-title">
            <Plus size={24} />
            <h2>Ajouter un nouveau produit</h2>
          </div>
          <button className="modal-close" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modal-content">
          {/* Informations de base */}
          <div className="form-section">
            <h3 className="section-title">
              <Package size={18} />
              Informations de base
            </h3>

            <div className="form-grid">
              <div className="form-group">
                <label>Nom du produit *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={errors.name ? 'error' : ''}
                  placeholder="Ex: Huile de beauté naturelle"
                />
                {errors.name && <span className="error-text">{errors.name}</span>}
              </div>

              <div className="form-group">
                <label>Marque *</label>
                <input
                  type="text"
                  value={formData.brand}
                  onChange={(e) => handleInputChange('brand', e.target.value)}
                  className={errors.brand ? 'error' : ''}
                  placeholder="Ex: Olgane"
                />
                {errors.brand && <span className="error-text">{errors.brand}</span>}
              </div>

              <div className="form-group">
                <label>SKU</label>
                <input
                  type="text"
                  value={formData.sku}
                  onChange={(e) => handleInputChange('sku', e.target.value)}
                  placeholder="Généré automatiquement"
                />
              </div>
            </div>

            <div className="form-group">
              <label>Description *</label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className={errors.description ? 'error' : ''}
                placeholder="Description détaillée du produit..."
                rows={3}
              />
              {errors.description && <span className="error-text">{errors.description}</span>}
            </div>
          </div>

          {/* Catégorisation */}
          <div className="form-section">
            <h3 className="section-title">
              <Tag size={18} />
              Catégorisation
            </h3>

            <div className="form-grid">
              <div className="form-group">
                <label>Catégorie *</label>
                <select
                  value={formData.category}
                  onChange={(e) => {
                    handleInputChange('category', e.target.value);
                    setFormData(prev => ({ ...prev, subcategory: '' }));
                  }}
                  className={errors.category ? 'error' : ''}
                >
                  <option value="">Sélectionner une catégorie</option>
                  {categories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
                {errors.category && <span className="error-text">{errors.category}</span>}
              </div>

              <div className="form-group">
                <label>Sous-catégorie</label>
                <select
                  value={formData.subcategory}
                  onChange={(e) => handleInputChange('subcategory', e.target.value)}
                  disabled={!formData.category}
                >
                  <option value="">Sélectionner une sous-catégorie</option>
                  {formData.category && subcategories[formData.category]?.map(subcat => (
                    <option key={subcat} value={subcat}>{subcat}</option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label>Tags (séparés par des virgules)</label>
                <input
                  type="text"
                  value={formData.tags}
                  onChange={(e) => handleInputChange('tags', e.target.value)}
                  placeholder="naturel, bio, hydratant"
                />
              </div>
            </div>
          </div>

          {/* Prix et stock */}
          <div className="form-section">
            <h3 className="section-title">
              <DollarSign size={18} />
              Prix et stock
            </h3>

            <div className="form-grid">
              <div className="form-group">
                <label>Prix de vente (F CFA) *</label>
                <input
                  type="number"
                  value={formData.price}
                  onChange={(e) => handleInputChange('price', e.target.value)}
                  className={errors.price ? 'error' : ''}
                  placeholder="15000"
                  min="0"
                  step="100"
                />
                {errors.price && <span className="error-text">{errors.price}</span>}
              </div>

              <div className="form-group">
                <label>Prix original (F CFA)</label>
                <input
                  type="number"
                  value={formData.originalPrice}
                  onChange={(e) => handleInputChange('originalPrice', e.target.value)}
                  placeholder="18000"
                  min="0"
                  step="100"
                />
              </div>

              <div className="form-group">
                <label>Stock disponible *</label>
                <input
                  type="number"
                  value={formData.stock}
                  onChange={(e) => handleInputChange('stock', e.target.value)}
                  className={errors.stock ? 'error' : ''}
                  placeholder="50"
                  min="0"
                />
                {errors.stock && <span className="error-text">{errors.stock}</span>}
              </div>

              <div className="form-group">
                <label>Stock minimum</label>
                <input
                  type="number"
                  value={formData.minStock}
                  onChange={(e) => handleInputChange('minStock', e.target.value)}
                  placeholder="5"
                  min="0"
                />
              </div>
            </div>
          </div>

          {/* Images */}
          <div className="form-section">
            <h3 className="section-title">
              <ImageIcon size={18} />
              Images du produit
            </h3>

            <div className="image-upload-area">
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*"
                onChange={handleImageUpload}
                style={{ display: 'none' }}
              />

              <button
                type="button"
                className="upload-button"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload size={20} />
                Ajouter des images (max 5)
              </button>

              {errors.images && <span className="error-text">{errors.images}</span>}
            </div>

            {imagePreviews.length > 0 && (
              <div className="image-previews">
                {imagePreviews.map((preview, index) => (
                  <div key={index} className="image-preview">
                    <img src={preview} alt={`Preview ${index + 1}`} />
                    <button
                      type="button"
                      className="remove-image"
                      onClick={() => removeImage(index)}
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {errors.submit && (
            <div className="error-message">
              <AlertCircle size={16} />
              {errors.submit}
            </div>
          )}

          <div className="modal-actions">
            <button type="button" className="btn-secondary" onClick={onClose}>
              Annuler
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <BarChart3 size={16} className="spinning" />
                  Création...
                </>
              ) : (
                <>
                  <CheckCircle size={16} />
                  Créer le produit
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddProductModal;

// Modal d'édition de produit
interface EditProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (productId: string, updates: Partial<Product>) => void;
  product: Product | null;
}

export const EditProductModal: React.FC<EditProductModalProps> = ({
  isOpen,
  onClose,
  onSave,
  product
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    originalPrice: '',
    category: '',
    subcategory: '',
    brand: '',
    stock: '',
    minStock: '',
    tags: '',
    status: 'active' as Product['status']
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Charger les données du produit
  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        description: product.description,
        price: product.price.toString(),
        originalPrice: product.originalPrice?.toString() || '',
        category: product.category,
        subcategory: product.subcategory,
        brand: product.brand,
        stock: product.stock.toString(),
        minStock: product.minStock.toString(),
        tags: product.tags.join(', '),
        status: product.status
      });
    }
  }, [product]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Nom requis';
    if (!formData.description.trim()) newErrors.description = 'Description requise';
    if (!formData.price || parseFloat(formData.price) <= 0) newErrors.price = 'Prix valide requis';
    if (!formData.category) newErrors.category = 'Catégorie requise';
    if (!formData.brand.trim()) newErrors.brand = 'Marque requise';
    if (!formData.stock || parseInt(formData.stock) < 0) newErrors.stock = 'Stock valide requis';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !product) return;

    setIsSubmitting(true);

    try {
      const updates: Partial<Product> = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        price: parseFloat(formData.price),
        originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,
        category: formData.category,
        subcategory: formData.subcategory,
        brand: formData.brand.trim(),
        stock: parseInt(formData.stock),
        minStock: parseInt(formData.minStock),
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        status: formData.status
      };

      onSave(product.id, updates);
      onClose();
    } catch (error) {
      setErrors({ submit: 'Erreur lors de la modification du produit' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen || !product) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-container">
        <div className="modal-header">
          <div className="modal-title">
            <Edit size={24} />
            <h2>Modifier le produit</h2>
          </div>
          <button className="modal-close" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modal-content">
          <div className="form-grid">
            <div className="form-group">
              <label>Nom du produit *</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={errors.name ? 'error' : ''}
              />
              {errors.name && <span className="error-text">{errors.name}</span>}
            </div>

            <div className="form-group">
              <label>Marque *</label>
              <input
                type="text"
                value={formData.brand}
                onChange={(e) => handleInputChange('brand', e.target.value)}
                className={errors.brand ? 'error' : ''}
              />
              {errors.brand && <span className="error-text">{errors.brand}</span>}
            </div>

            <div className="form-group">
              <label>Prix (F CFA) *</label>
              <input
                type="number"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                className={errors.price ? 'error' : ''}
                min="0"
                step="100"
              />
              {errors.price && <span className="error-text">{errors.price}</span>}
            </div>

            <div className="form-group">
              <label>Stock *</label>
              <input
                type="number"
                value={formData.stock}
                onChange={(e) => handleInputChange('stock', e.target.value)}
                className={errors.stock ? 'error' : ''}
                min="0"
              />
              {errors.stock && <span className="error-text">{errors.stock}</span>}
            </div>

            <div className="form-group">
              <label>Statut</label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
              >
                <option value="active">Actif</option>
                <option value="inactive">Inactif</option>
                <option value="out_of_stock">Rupture de stock</option>
              </select>
            </div>
          </div>

          <div className="form-group">
            <label>Description *</label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className={errors.description ? 'error' : ''}
              rows={3}
            />
            {errors.description && <span className="error-text">{errors.description}</span>}
          </div>

          {errors.submit && (
            <div className="error-message">
              <AlertCircle size={16} />
              {errors.submit}
            </div>
          )}

          <div className="modal-actions">
            <button type="button" className="btn-secondary" onClick={onClose}>
              Annuler
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <BarChart3 size={16} className="spinning" />
                  Modification...
                </>
              ) : (
                <>
                  <CheckCircle size={16} />
                  Modifier
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
