import React from 'react';
import { Search, Filter, RefreshCw } from 'lucide-react';
import Button from '../ui/Button';

interface OrderFiltersProps {
  filterStatus: string;
  searchQuery: string;
  onFilterStatusChange: (status: string) => void;
  onSearchQueryChange: (query: string) => void;
  onRefresh?: () => void;
}

const OrderFilters: React.FC<OrderFiltersProps> = ({
  filterStatus,
  searchQuery,
  onFilterStatusChange,
  onSearchQueryChange,
  onRefresh
}) => {
  return (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 mb-6">
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Filtre par statut */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2 text-gray-700">
            <Filter size={16} />
            <span className="font-medium">Statut:</span>
          </div>
          <select
            value={filterStatus}
            onChange={e => onFilterStatusChange(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 min-w-[160px]"
          >
            <option value="all">Tous les statuts</option>
            <option value="pending">En attente</option>
            <option value="processing">En traitement</option>
            <option value="shipped">Expédiée</option>
            <option value="delivered">Livrée</option>
            <option value="cancelled">Annulée</option>
          </select>
        </div>

        {/* Barre de recherche */}
        <div className="flex-1 relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Rechercher par numéro de commande, produit ou entreprise..."
            value={searchQuery}
            onChange={e => onSearchQueryChange(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 placeholder-gray-500"
          />
        </div>

        {/* Bouton de rafraîchissement */}
        {onRefresh && (
          <Button 
            variant="outline" 
            onClick={onRefresh}
            className="flex items-center space-x-2 whitespace-nowrap"
          >
            <RefreshCw size={16} />
            <span>Actualiser</span>
          </Button>
        )}
      </div>

      {/* Indicateurs de filtres actifs */}
      {(filterStatus !== 'all' || searchQuery) && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Filtres actifs:</span>
              {filterStatus !== 'all' && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Statut: {filterStatus === 'pending' ? 'En attente' :
                           filterStatus === 'processing' ? 'En traitement' :
                           filterStatus === 'shipped' ? 'Expédiée' :
                           filterStatus === 'delivered' ? 'Livrée' :
                           filterStatus === 'cancelled' ? 'Annulée' : filterStatus}
                </span>
              )}
              {searchQuery && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Recherche: "{searchQuery}"
                </span>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                onFilterStatusChange('all');
                onSearchQueryChange('');
              }}
              className="text-gray-500 hover:text-gray-700"
            >
              Effacer les filtres
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderFilters;
