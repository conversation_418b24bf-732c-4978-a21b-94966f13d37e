/* Business Sales Page - Interface Avancée */
.business-sales-page {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Header Section */
.sales-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.header-content h1.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.notification-btn {
  position: relative;
}

.notification-btn::after {
  content: '';
  position: absolute;
  top: 6px;
  right: 6px;
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  border: 2px solid white;
}

.refresh-btn:hover {
  color: #3b82f6;
  border-color: #3b82f6;
}

/* KPIs Dashboard */
.sales-kpis {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.kpi-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.kpi-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.kpi-card.revenue::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.kpi-card.commissions::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.kpi-card.sales-count::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.kpi-card.avg-order::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.kpi-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.kpi-card.revenue .kpi-icon {
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
  color: #059669;
}

.kpi-card.commissions .kpi-icon {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #d97706;
}

.kpi-card.sales-count .kpi-icon {
  background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
  color: #7c3aed;
}

.kpi-card.avg-order .kpi-icon {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #dc2626;
}

.kpi-content {
  flex: 1;
}

.kpi-content h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.kpi-value {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.kpi-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}

.kpi-trend.positive {
  color: #059669;
}

.kpi-trend.negative {
  color: #dc2626;
}

.kpi-trend.neutral {
  color: #64748b;
}

/* Analytics Section */
.sales-analytics {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.analytics-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.analytics-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.analytics-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.analytics-btn:hover {
  background: #0ea5e9;
  color: white;
  transform: translateY(-1px);
}

/* Insights Section */
.sales-insights {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.insights-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.insights-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-item {
  display: flex;
  gap: 16px;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid;
  transition: all 0.2s ease;
}

.insight-item.positive {
  background: #f0fdf4;
  border-left-color: #22c55e;
}

.insight-item.warning {
  background: #fefce8;
  border-left-color: #eab308;
}

.insight-item.info {
  background: #eff6ff;
  border-left-color: #3b82f6;
}

.insight-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.insight-item.positive .insight-icon {
  background: #dcfce7;
  color: #16a34a;
}

.insight-item.warning .insight-icon {
  background: #fef3c7;
  color: #ca8a04;
}

.insight-item.info .insight-icon {
  background: #dbeafe;
  color: #2563eb;
}

.insight-content {
  flex: 1;
}

.insight-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.insight-content p {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.insight-action {
  font-size: 0.8rem;
  font-weight: 500;
  color: #3b82f6;
  margin: 0;
}

/* Analytics Grid */
.analytics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-top: 24px;
}

.chart-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.chart-card h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

/* Chart Bars */
.chart-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  height: 160px;
  gap: 12px;
}

.bar {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 4px 4px 0 0;
  min-width: 40px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.bar:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: scale(1.05);
}

.chart-labels {
  display: flex;
  justify-content: space-around;
  margin-top: 8px;
}

.chart-labels span {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

/* Pie Chart */
.pie-chart {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin: 0 auto 16px;
  background: conic-gradient(
    #10b981 0deg 162deg,
    #f59e0b 162deg 288deg,
    #ef4444 288deg 360deg
  );
  position: relative;
}

.pie-chart::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: white;
  border-radius: 50%;
}

.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.beauty {
  background: #10b981;
}

.legend-color.cosmetics {
  background: #f59e0b;
}

.legend-color.hygiene {
  background: #ef4444;
}

/* Table Section */
.sales-table-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-container svg {
  position: absolute;
  left: 12px;
  color: #64748b;
  z-index: 1;
}

.search-input {
  padding: 10px 12px 10px 40px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.875rem;
  width: 250px;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

/* Filters Panel */
.filters-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Sales Table */
.sales-table-container {
  overflow-x: auto;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.sales-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.sales-table th {
  background: #f8fafc;
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  border-bottom: 1px solid #e5e7eb;
}

.sales-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #f3f4f6;
  font-size: 0.875rem;
}

.sales-table tr:hover {
  background: #f9fafb;
}

.client-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.client-name {
  font-weight: 600;
  color: #1f2937;
}

.client-email {
  color: #64748b;
  font-size: 0.8rem;
}

.sale-id {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
  color: #3b82f6;
}

.amount, .commission, .net-amount {
  font-weight: 600;
  color: #1f2937;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-completed {
  background: #dcfce7;
  color: #16a34a;
}

.status-pending {
  background: #fef3c7;
  color: #d97706;
}

.status-cancelled {
  background: #fee2e2;
  color: #dc2626;
}

.status-refunded {
  background: #e0e7ff;
  color: #3b82f6;
}

.view-btn {
  padding: 6px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* Table Footer */
.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.results-info {
  color: #64748b;
  font-size: 0.875rem;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #64748b;
  font-size: 0.875rem;
}

/* No Sales State */
.no-sales {
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
}

.no-sales svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-sales h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.no-sales p {
  margin: 0;
  font-size: 0.875rem;
}

/* Advanced Charts Section */
.advanced-charts-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.charts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.charts-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.charts-actions {
  display: flex;
  gap: 12px;
}

.charts-btn {
  padding: 8px 16px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.charts-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.charts-insights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.chart-insight {
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.chart-insight h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.chart-insight p {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* Chart Container Variants */
.chart-container.sales-chart {
  border-left: 4px solid #10b981;
}

.chart-container.products-chart {
  border-left: 4px solid #3b82f6;
}

.chart-container.analytics-chart {
  border-left: 4px solid #8b5cf6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .business-sales-page {
    padding: 16px;
  }

  .sales-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
  }

  .sales-kpis {
    grid-template-columns: 1fr;
  }

  .kpi-card {
    padding: 20px;
  }

  .kpi-value {
    font-size: 1.5rem;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .charts-insights {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .advanced-charts-section {
    padding: 16px;
    margin-bottom: 16px;
  }

  .table-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .table-actions {
    flex-direction: column;
    gap: 12px;
  }

  .search-input {
    width: 100%;
  }

  .filters-panel {
    grid-template-columns: 1fr;
  }

  .table-footer {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}

/* Notifications Panel */
.notifications-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.notifications-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-notifications {
  width: 32px;
  height: 32px;
  border: none;
  background: #e5e7eb;
  border-radius: 50%;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-notifications:hover {
  background: #dc2626;
  color: white;
}

.notifications-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.notification-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 12px;
  border-left: 4px solid;
  transition: all 0.2s ease;
}

.notification-item.success {
  background: #f0fdf4;
  border-left-color: #22c55e;
}

.notification-item.warning {
  background: #fefce8;
  border-left-color: #eab308;
}

.notification-item.info {
  background: #eff6ff;
  border-left-color: #3b82f6;
}

.notification-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-item.success .notification-icon {
  background: #dcfce7;
  color: #16a34a;
}

.notification-item.warning .notification-icon {
  background: #fef3c7;
  color: #ca8a04;
}

.notification-item.info .notification-icon {
  background: #dbeafe;
  color: #2563eb;
}

.notification-content h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.notification-content p {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.notification-time {
  font-size: 0.75rem;
  color: #9ca3af;
  font-style: italic;
}

.notifications-footer {
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mark-all-read,
.view-all-notifications {
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mark-all-read:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.view-all-notifications {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.view-all-notifications:hover {
  background: #2563eb;
  border-color: #2563eb;
}

/* Performance Section */
.performance-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.performance-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.performance-actions {
  display: flex;
  gap: 12px;
}

.performance-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.performance-btn:hover {
  background: #0ea5e9;
  color: white;
  transform: translateY(-1px);
}

/* Objectives Grid */
.objectives-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.objective-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.objective-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.objective-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.objective-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.objective-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.objective-status.achieved {
  background: #dcfce7;
  color: #16a34a;
}

.objective-status.in-progress {
  background: #fef3c7;
  color: #d97706;
}

.objective-status.on-track {
  background: #dbeafe;
  color: #2563eb;
}

.objective-progress {
  margin-bottom: 12px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 4px;
  transition: width 0.5s ease;
}

.objective-card.weekly .progress-fill {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.objective-card.quarterly .progress-fill {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
}

.current {
  font-weight: 600;
  color: #1f2937;
}

.target {
  color: #64748b;
}

.objective-details {
  font-size: 0.8rem;
  color: #64748b;
}

.achievement {
  color: #16a34a;
  font-weight: 500;
}

.remaining {
  color: #d97706;
  font-weight: 500;
}

.projection {
  color: #3b82f6;
  font-weight: 500;
}

/* Performance Insights */
.performance-insights h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.insight-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  transition: all 0.2s ease;
}

.insight-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.insight-metric {
  margin-bottom: 12px;
}

.metric-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 0.875rem;
  color: #64748b;
}

.insight-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.insight-trend.positive {
  color: #16a34a;
}

.insight-trend.warning {
  color: #d97706;
}

/* Export Menu Styles */
.export-dropdown {
  position: relative;
}

.export-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  z-index: 100;
  padding: 20px;
  margin-top: 8px;
  animation: slideDown 0.2s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.export-section {
  margin-bottom: 20px;
}

.export-section:last-child {
  margin-bottom: 0;
}

.export-section h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.export-option {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.export-option:hover {
  background: #f9fafb;
  border-color: #3b82f6;
}

.export-option.pdf {
  border-left: 4px solid #ef4444;
}

.export-option.excel {
  border-left: 4px solid #10b981;
}

.export-option div {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.export-option span {
  font-weight: 600;
  color: #1f2937;
}

.export-option small {
  color: #64748b;
  font-size: 0.75rem;
}

.export-period {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
}

.export-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  color: #374151;
}

.export-checkbox input {
  margin: 0;
}

/* Schedule Menu */
.schedule-dropdown {
  position: relative;
}

.schedule-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  z-index: 100;
  padding: 20px;
  margin-top: 8px;
  animation: slideDown 0.2s ease;
}

.schedule-menu h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.schedule-option {
  margin-bottom: 16px;
}

.schedule-option label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.schedule-option select,
.schedule-option input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
}

.schedule-save {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 10px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.schedule-save:hover {
  background: #2563eb;
}

/* Chart Selector */
.chart-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.chart-tabs {
  display: flex;
  gap: 8px;
}

.chart-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-tab:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.chart-tab.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.timeframe-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  color: #374151;
}

/* Interactive Charts */
.chart-card.interactive {
  position: relative;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-action {
  padding: 6px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-action:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.chart-bars.interactive .bar.interactive {
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-bars.interactive .bar.interactive:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

.pie-chart.interactive {
  cursor: pointer;
  transition: all 0.3s ease;
}

.pie-chart.interactive:hover {
  transform: scale(1.05);
}

.legend-item.interactive {
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 4px 8px;
  border-radius: 6px;
}

.legend-item.interactive:hover {
  background: #f3f4f6;
}

/* Client Segments Chart */
.clients-chart {
  padding: 20px 0;
}

.client-segments {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.client-segment {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.segment-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.segment-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
}

.segment-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.segment-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

/* Real-time Insights */
.chart-card.insights {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.chart-card.insights h3 {
  color: white;
  margin-bottom: 20px;
}

.real-time-insights {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-metric {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.metric-icon {
  font-size: 1.5rem;
}

.metric-title {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 2px;
}

.metric-desc {
  display: block;
  font-size: 0.8rem;
  opacity: 0.9;
}

.ai-chat-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.ai-chat-trigger:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* AI Chat Styles */
.ai-chat-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.ai-chat-container {
  width: 500px;
  height: 600px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ai-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ai-chat-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.8rem;
  opacity: 0.9;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.close-chat {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-chat:hover {
  background: rgba(255, 255, 255, 0.3);
}

.ai-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message {
  display: flex;
  gap: 12px;
  animation: messageSlide 0.3s ease;
}

@keyframes messageSlide {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.message.ai .message-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.message.user .message-avatar {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.message-content {
  flex: 1;
  max-width: 80%;
}

.message-text {
  padding: 12px 16px;
  border-radius: 16px;
  font-size: 0.875rem;
  line-height: 1.5;
}

.message.ai .message-text {
  background: #f3f4f6;
  color: #374151;
  border-bottom-left-radius: 4px;
}

.message.user .message-text {
  background: #3b82f6;
  color: white;
  border-bottom-right-radius: 4px;
}

.message-time {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 4px;
  text-align: right;
}

.message.ai .message-time {
  text-align: left;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px 16px;
  background: #f3f4f6;
  border-radius: 16px;
  border-bottom-left-radius: 4px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background: #9ca3af;
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

.ai-chat-input {
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.quick-questions {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.quick-btn {
  padding: 6px 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  font-size: 0.8rem;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-btn:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.chat-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 24px;
  font-size: 0.875rem;
  outline: none;
  transition: all 0.2s ease;
}

.chat-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.send-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.send-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: scale(1.05);
}

.send-btn:disabled {
  background: #d1d5db;
  cursor: not-allowed;
}

/* Floating Action Button */
.ai-chat-fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  z-index: 999;
}

.ai-chat-fab:hover {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
}

.fab-notification {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
}

/* Mobile Responsive for new sections */
@media (max-width: 768px) {
  .notifications-panel {
    width: 100%;
  }

  .objectives-grid {
    grid-template-columns: 1fr;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .performance-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .export-menu,
  .schedule-menu {
    width: 280px;
    right: -20px;
  }

  .chart-selector {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .chart-tabs {
    justify-content: center;
  }

  .ai-chat-container {
    width: 95%;
    height: 90%;
    margin: 20px;
  }

  .ai-chat-fab {
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
  }

  .quick-questions {
    flex-direction: column;
  }

  .quick-btn {
    text-align: center;
  }
}
