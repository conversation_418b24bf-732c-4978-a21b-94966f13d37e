import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';

export enum NotificationType {
  MENTION = 'mention',
  COMMENT = 'comment',
  LIKE = 'like',
  RECOMMENDATION = 'recommendation',
  REVIEW = 'review',
  FOLLOW = 'follow',
  FOLLOW_REQUEST = 'follow_request',
  FOLLOW_ACCEPTED = 'follow_accepted',
  FOLLOW_REJECTED = 'follow_rejected',
  SYSTEM = 'system'
}

export interface INotification {
  id: string;
  userId: string;
  senderId?: string;
  senderName?: string;
  senderProfilePicture?: string;
  type: NotificationType;
  message: string;
  relatedItemId?: string; // ID du post, commentaire, etc.
  relatedItemType?: string; // Type d'élément (post, commentaire, etc.)
  read: boolean;
  createdAt: Date;
}

interface NotificationsContextType {
  notifications: INotification[];
  unreadCount: number;
  loading: boolean;
  createNotification: (notification: Omit<INotification, 'id' | 'read' | 'createdAt'>) => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  getBusinessNotifications: (businessName: string) => INotification[];
}

const NotificationsContext = createContext<NotificationsContextType>({
  notifications: [],
  unreadCount: 0,
  loading: true,
  createNotification: async () => {},
  markAsRead: async () => {},
  markAllAsRead: async () => {},
  getBusinessNotifications: () => []
});

export const useNotifications = () => useContext(NotificationsContext);

export const NotificationsProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [notifications, setNotifications] = useState<INotification[]>([]);
  const [loading, setLoading] = useState(true);
  const { currentUser } = useAuth();

  const fetchNotifications = async () => {
    if (!currentUser) {
      setNotifications([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error("Error fetching notifications:", error);
        setNotifications([]);
        return;
      }

      if (data) {
        const fetchedNotifications: INotification[] = data.map((notification: any) => ({
          id: notification.id,
          userId: notification.user_id,
          senderId: notification.sender_id,
          senderName: notification.sender_name,
          senderProfilePicture: notification.sender_profile_picture,
          type: notification.type as NotificationType,
          message: notification.message,
          relatedItemId: notification.related_item_id,
          relatedItemType: notification.related_item_type,
          read: notification.read,
          createdAt: new Date(notification.created_at),
        }));
        setNotifications(fetchedNotifications);
      } else {
        setNotifications([]);
      }
    } catch (err) {
      console.error("Error in fetchNotifications function:", err);
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser) {
      fetchNotifications();
      
      // Abonnement aux nouvelles notifications en temps réel
      const subscription = supabase
        .channel('notifications-channel')
        .on('postgres_changes', { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'notifications',
          filter: `user_id=eq.${currentUser.id}`
        }, (payload) => {
          const newNotification: INotification = {
            id: payload.new.id,
            userId: payload.new.user_id,
            senderId: payload.new.sender_id,
            senderName: payload.new.sender_name,
            senderProfilePicture: payload.new.sender_profile_picture,
            type: payload.new.type as NotificationType,
            message: payload.new.message,
            relatedItemId: payload.new.related_item_id,
            relatedItemType: payload.new.related_item_type,
            read: payload.new.read,
            createdAt: new Date(payload.new.created_at),
          };
          setNotifications(prev => [newNotification, ...prev]);
        })
        .subscribe();

      return () => {
        subscription.unsubscribe();
      };
    }
  }, [currentUser]);

  const unreadCount = notifications.filter(notification => !notification.read).length;

  const createNotification = async (notification: Omit<INotification, 'id' | 'read' | 'createdAt'>) => {
    try {
      const notificationToInsert = {
        user_id: notification.userId,
        sender_id: notification.senderId,
        sender_name: notification.senderName,
        sender_profile_picture: notification.senderProfilePicture,
        type: notification.type,
        message: notification.message,
        related_item_id: notification.relatedItemId,
        related_item_type: notification.relatedItemType,
        read: false
      };

      const { data, error } = await supabase
        .from('notifications')
        .insert([notificationToInsert])
        .select()
        .single();

      if (error) {
        console.error("Error creating notification:", error);
        return;
      }

      // Si l'utilisateur actuel est le destinataire, mettre à jour l'état local
      if (currentUser && data.user_id === currentUser.id) {
        const newNotification: INotification = {
          id: data.id,
          userId: data.user_id,
          senderId: data.sender_id,
          senderName: data.sender_name,
          senderProfilePicture: data.sender_profile_picture,
          type: data.type as NotificationType,
          message: data.message,
          relatedItemId: data.related_item_id,
          relatedItemType: data.related_item_type,
          read: data.read,
          createdAt: new Date(data.created_at),
        };
        setNotifications(prev => [newNotification, ...prev]);
      }
    } catch (error) {
      console.error("Error in createNotification:", error);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      // Mise à jour optimiste de l'état local
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, read: true } 
            : notification
        )
      );

      // Mise à jour dans la base de données
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);

      if (error) {
        console.error("Error marking notification as read:", error);
        // Annuler la mise à jour optimiste en cas d'erreur
        fetchNotifications();
      }
    } catch (error) {
      console.error("Error in markAsRead:", error);
      fetchNotifications();
    }
  };

  const markAllAsRead = async () => {
    if (!currentUser) return;
    
    try {
      // Mise à jour optimiste de l'état local
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, read: true }))
      );

      // Mise à jour dans la base de données
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', currentUser.id)
        .eq('read', false);

      if (error) {
        console.error("Error marking all notifications as read:", error);
        // Annuler la mise à jour optimiste en cas d'erreur
        fetchNotifications();
      }
    } catch (error) {
      console.error("Error in markAllAsRead:", error);
      fetchNotifications();
    }
  };

  // Fonction pour récupérer les notifications d'une entreprise spécifique
  const getBusinessNotifications = (businessName: string) => {
    return notifications.filter(notification => 
      (notification.type === NotificationType.MENTION || notification.type === NotificationType.REVIEW) && 
      notification.message.includes(businessName)
    );
  };

  return (
    <NotificationsContext.Provider value={{
      notifications,
      unreadCount,
      loading,
      createNotification,
      markAsRead,
      markAllAsRead,
      getBusinessNotifications
    }}>
      {children}
    </NotificationsContext.Provider>
  );
};
