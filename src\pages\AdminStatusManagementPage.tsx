import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { UserStatusUpdateService } from '../services/userStatusUpdateService';
import { UserRole } from '../types';
import Card, { CardBody } from '../components/ui/Card';
import { RefreshCw, Users, TrendingUp, CheckCircle, AlertCircle, Play } from 'lucide-react';

const AdminStatusManagementPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [userMetrics, setUserMetrics] = useState<any>(null);
  const [selectedUserId, setSelectedUserId] = useState('');

  // Vérifier que l'utilisateur est admin
  if (!currentUser || currentUser.role !== UserRole.ADMIN) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Card className="border-red-200">
          <CardBody>
            <div className="text-center">
              <AlertCircle className="mx-auto text-red-500 mb-4" size={48} />
              <h1 className="text-2xl font-bold text-red-600 mb-2">Accès Refusé</h1>
              <p className="text-gray-600">Cette page est réservée aux administrateurs.</p>
            </div>
          </CardBody>
        </Card>
      </div>
    );
  }

  const handleUpdateAllUsers = async () => {
    setLoading(true);
    setResults(null);
    try {
      const result = await UserStatusUpdateService.updateAllUsersStatus();
      setResults(result);
    } catch (error) {
      console.error('Erreur lors de la mise à jour globale:', error);
      alert('Erreur lors de la mise à jour globale des statuts');
    } finally {
      setLoading(false);
    }
  };

  const handleCheckUserMetrics = async () => {
    if (!selectedUserId.trim()) {
      alert('Veuillez entrer un ID utilisateur');
      return;
    }

    setLoading(true);
    setUserMetrics(null);
    try {
      const metrics = await UserStatusUpdateService.getUserMetrics(selectedUserId.trim());
      const newStatus = UserStatusUpdateService.determineUserStatus(metrics);
      setUserMetrics({ ...metrics, recommendedStatus: newStatus });
    } catch (error) {
      console.error('Erreur lors de la récupération des métriques:', error);
      alert('Erreur lors de la récupération des métriques utilisateur');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSingleUser = async () => {
    if (!selectedUserId.trim()) {
      alert('Veuillez entrer un ID utilisateur');
      return;
    }

    setLoading(true);
    try {
      const result = await UserStatusUpdateService.updateUserStatusIfNeeded(selectedUserId.trim());
      if (result.updated) {
        alert(`Statut mis à jour: ${result.oldStatus} → ${result.newStatus}`);
      } else {
        alert('Aucune mise à jour nécessaire');
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      alert('Erreur lors de la mise à jour du statut utilisateur');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Gestion des Statuts Utilisateur
        </h1>
        <p className="text-lg text-gray-600">
          Outils d'administration pour gérer et mettre à jour les statuts des utilisateurs
        </p>
      </div>

      {/* Mise à jour globale */}
      <Card className="mb-8">
        <CardBody>
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Mise à jour globale
              </h2>
              <p className="text-gray-600">
                Met à jour les statuts de tous les utilisateurs selon leurs métriques actuelles
              </p>
            </div>
            <Users className="text-blue-600" size={32} />
          </div>
          
          <button
            onClick={handleUpdateAllUsers}
            disabled={loading}
            className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <RefreshCw className="animate-spin mr-2" size={20} />
            ) : (
              <Play className="mr-2" size={20} />
            )}
            {loading ? 'Mise à jour en cours...' : 'Lancer la mise à jour globale'}
          </button>

          {results && (
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">Résultats:</h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{results.processed}</div>
                  <div className="text-sm text-gray-600">Traités</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{results.updated}</div>
                  <div className="text-sm text-gray-600">Mis à jour</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{results.errors}</div>
                  <div className="text-sm text-gray-600">Erreurs</div>
                </div>
              </div>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Gestion utilisateur individuel */}
      <Card className="mb-8">
        <CardBody>
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Gestion utilisateur individuel
              </h2>
              <p className="text-gray-600">
                Vérifier et mettre à jour le statut d'un utilisateur spécifique
              </p>
            </div>
            <TrendingUp className="text-green-600" size={32} />
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ID Utilisateur
              </label>
              <input
                type="text"
                value={selectedUserId}
                onChange={(e) => setSelectedUserId(e.target.value)}
                placeholder="Entrez l'ID de l'utilisateur"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="flex space-x-4">
              <button
                onClick={handleCheckUserMetrics}
                disabled={loading || !selectedUserId.trim()}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <TrendingUp className="mr-2" size={16} />
                Vérifier métriques
              </button>

              <button
                onClick={handleUpdateSingleUser}
                disabled={loading || !selectedUserId.trim()}
                className="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <CheckCircle className="mr-2" size={16} />
                Mettre à jour statut
              </button>
            </div>
          </div>

          {userMetrics && (
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-4">Métriques utilisateur:</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">
                    {userMetrics.profileComplete ? '✅' : '❌'}
                  </div>
                  <div className="text-sm text-gray-600">Profil complet</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{userMetrics.postCount}</div>
                  <div className="text-sm text-gray-600">Posts</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{userMetrics.commentCount}</div>
                  <div className="text-sm text-gray-600">Commentaires</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{userMetrics.likesReceived}</div>
                  <div className="text-sm text-gray-600">Likes reçus</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{userMetrics.followersCount}</div>
                  <div className="text-sm text-gray-600">Followers</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{userMetrics.recommendationsCount}</div>
                  <div className="text-sm text-gray-600">Recommandations</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{userMetrics.accountAgeDays}</div>
                  <div className="text-sm text-gray-600">Jours d'ancienneté</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">{userMetrics.recommendedStatus}</div>
                  <div className="text-sm text-gray-600">Statut recommandé</div>
                </div>
              </div>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Informations système */}
      <Card>
        <CardBody>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Informations système
          </h2>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Le système vérifie automatiquement les statuts lors des actions utilisateur</p>
            <p>• Les mises à jour sont progressives (pas de régression de statut)</p>
            <p>• Les notifications sont envoyées automatiquement lors des changements</p>
            <p>• Les métriques sont calculées en temps réel depuis la base de données</p>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default AdminStatusManagementPage;
