import React from 'react';
import FeedAdCard, { FeedAd } from './FeedAdCard';
import { DEFAULT_IMAGES_SIZED } from '../../constants/defaultImages';
import Card, { CardBody, CardHeader } from '../ui/Card';
import { TestTube } from 'lucide-react';

// Publicité de test simple avec UUID valide
const testAd: FeedAd = {
  id: '00000000-0000-0000-0000-000000000001',
  title: 'Test des Boutons d\'Interaction',
  description: 'Cette publicité sert à tester le fonctionnement des boutons J\'aime, Commenter et Partager.',
  imageUrl: DEFAULT_IMAGES_SIZED.PROMO_BANNER,
  targetUrl: 'https://example.com',
  businessName: 'Test Business',
  businessLogo: DEFAULT_IMAGES_SIZED.BUSINESS_LOGO,
  discount: 15
};

const FeedAdCardTest: React.FC = () => {
  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      {/* En-tête */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <TestTube className="text-blue-600" size={24} />
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                Test des Boutons d'Interaction - Publicités
              </h1>
              <p className="text-gray-600">
                Testez les boutons J'aime, Commenter et Partager
              </p>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">Instructions de Test</h2>
        </CardHeader>
        <CardBody>
          <div className="space-y-3 text-sm">
            <div>
              <h3 className="font-medium text-blue-700">🖱️ Test des Clics</h3>
              <p>Cliquez sur chaque bouton et vérifiez les logs dans la console (F12)</p>
            </div>
            
            <div>
              <h3 className="font-medium text-green-700">✅ Comportements Attendus</h3>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li><strong>J'aime :</strong> Doit changer de couleur et incrémenter le compteur</li>
                <li><strong>Commenter :</strong> Doit ouvrir/fermer le formulaire de commentaire</li>
                <li><strong>Partager :</strong> Doit ouvrir/fermer le menu de partage</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-orange-700">🔍 Logs à Surveiller</h3>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li><code>🖱️ CLIC DÉTECTÉ sur le bouton Like</code></li>
                <li><code>🖱️ CLIC DÉTECTÉ sur le bouton Commenter</code></li>
                <li><code>🖱️ CLIC DÉTECTÉ sur le bouton Partager</code></li>
              </ul>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Publicité de test */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">Publicité de Test</h2>
        </CardHeader>
        <CardBody>
          <FeedAdCard ad={testAd} />
        </CardBody>
      </Card>

      {/* Conseils de dépannage */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">Dépannage</h2>
        </CardHeader>
        <CardBody>
          <div className="space-y-3 text-sm">
            <div>
              <h3 className="font-medium text-red-700">❌ Si les boutons ne fonctionnent pas :</h3>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Vérifiez que vous êtes connecté</li>
                <li>Ouvrez la console (F12) pour voir les erreurs</li>
                <li>Vérifiez les logs de debug sous la publicité</li>
                <li>Essayez de rafraîchir la page</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-purple-700">🔧 Problèmes Courants :</h3>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li><strong>Boutons grisés :</strong> Utilisateur non connecté ou permissions insuffisantes</li>
                <li><strong>Pas de réaction :</strong> Problème de z-index ou pointer-events</li>
                <li><strong>Erreurs console :</strong> Problème avec les services ou la base de données</li>
              </ul>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default FeedAdCardTest;
