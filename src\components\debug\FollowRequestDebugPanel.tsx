import React, { useState } from 'react';
import { <PERSON>, Refresh<PERSON><PERSON>, Trash2, <PERSON><PERSON><PERSON>cle, X<PERSON>ircle, AlertCircle } from 'lucide-react';
import Button from '../ui/Button';
import { FollowRequestDiagnostic } from '../../utils/followRequestDiagnostic';
import { useAuth } from '../../context/AuthContext';
import { useFollowRequests } from '../../context/FollowRequestContext';

interface DiagnosticResult {
  tablesExist: {
    followRequests: boolean;
    privacySettings: boolean;
    userBlocks: boolean;
  };
  users: Array<{
    id: string;
    username: string;
    role: string;
  }>;
  timestamp: string;
}

const FollowRequestDebugPanel: React.FC = () => {
  const { currentUser } = useAuth();
  const { sendFollowRequest, receivedRequests, sentRequests, pendingRequestsCount } = useFollowRequests();
  const [diagnosticResult, setDiagnosticResult] = useState<DiagnosticResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [testResult, setTestResult] = useState<string>('');

  const runDiagnostic = async () => {
    setLoading(true);
    setTestResult('🚀 Démarrage du diagnostic...\n');
    
    try {
      const result = await FollowRequestDiagnostic.runFullDiagnostic();
      setDiagnosticResult(result);
      setTestResult(prev => prev + '✅ Diagnostic terminé avec succès!\n');
    } catch (error) {
      setTestResult(prev => prev + `❌ Erreur: ${error}\n`);
    } finally {
      setLoading(false);
    }
  };

  const testFollowRequest = async () => {
    if (!currentUser) {
      setTestResult('❌ Utilisateur non connecté\n');
      return;
    }

    setLoading(true);
    setTestResult('🧪 Test de demande de suivi...\n');

    try {
      // Trouver un autre utilisateur pour tester
      const users = await FollowRequestDiagnostic.checkUsers();
      const targetUser = users.find(u => u.id !== currentUser.id);
      
      if (!targetUser) {
        setTestResult(prev => prev + '❌ Aucun utilisateur cible trouvé\n');
        return;
      }

      setTestResult(prev => prev + `📤 Envoi de demande à ${targetUser.username}...\n`);
      
      const result = await sendFollowRequest(targetUser.id, 'Test de demande depuis le panel de debug');
      
      if (result.success) {
        if (result.directFollow) {
          setTestResult(prev => prev + '✅ Suivi direct réussi!\n');
        } else {
          setTestResult(prev => prev + '✅ Demande de suivi envoyée!\n');
        }
      } else {
        setTestResult(prev => prev + `❌ Erreur: ${result.error}\n`);
      }
      
    } catch (error) {
      setTestResult(prev => prev + `❌ Erreur lors du test: ${error}\n`);
    } finally {
      setLoading(false);
    }
  };

  const cleanupTestData = async () => {
    setLoading(true);
    setTestResult('🧹 Nettoyage des données de test...\n');
    
    try {
      await FollowRequestDiagnostic.cleanupTestData();
      setTestResult(prev => prev + '✅ Nettoyage terminé!\n');
    } catch (error) {
      setTestResult(prev => prev + `❌ Erreur lors du nettoyage: ${error}\n`);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle size={16} className="text-green-500" />
    ) : (
      <XCircle size={16} className="text-red-500" />
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto">
      <div className="flex items-center space-x-2 mb-6">
        <AlertCircle className="text-orange-500" size={24} />
        <h2 className="text-xl font-bold text-gray-900">
          Panel de Debug - Système de Demandes de Suivi
        </h2>
      </div>

      {/* État actuel */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">État Actuel du Contexte</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Demandes reçues:</span>
            <span className="ml-2 font-semibold">{receivedRequests.length}</span>
          </div>
          <div>
            <span className="text-gray-600">Demandes envoyées:</span>
            <span className="ml-2 font-semibold">{sentRequests.length}</span>
          </div>
          <div>
            <span className="text-gray-600">En attente:</span>
            <span className="ml-2 font-semibold">{pendingRequestsCount}</span>
          </div>
          <div>
            <span className="text-gray-600">Utilisateur:</span>
            <span className="ml-2 font-semibold">{currentUser?.username || 'Non connecté'}</span>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex flex-wrap gap-3 mb-6">
        <Button
          onClick={runDiagnostic}
          disabled={loading}
          leftIcon={loading ? <RefreshCw className="animate-spin" size={16} /> : <Play size={16} />}
          variant="primary"
        >
          Diagnostic Complet
        </Button>
        
        <Button
          onClick={testFollowRequest}
          disabled={loading || !currentUser}
          leftIcon={<Play size={16} />}
          variant="outline"
        >
          Test Demande de Suivi
        </Button>
        
        <Button
          onClick={cleanupTestData}
          disabled={loading}
          leftIcon={<Trash2 size={16} />}
          variant="outline"
        >
          Nettoyer Tests
        </Button>
      </div>

      {/* Résultats du diagnostic */}
      {diagnosticResult && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold mb-3">Résultats du Diagnostic</h3>
          
          <div className="space-y-3">
            <div>
              <h4 className="font-medium mb-2">Tables de Base de Données</h4>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(diagnosticResult.tablesExist.followRequests)}
                  <span>follow_requests</span>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(diagnosticResult.tablesExist.privacySettings)}
                  <span>privacy_settings</span>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(diagnosticResult.tablesExist.userBlocks)}
                  <span>user_blocks</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Utilisateurs Trouvés</h4>
              <div className="space-y-1 text-sm">
                {diagnosticResult.users.map(user => (
                  <div key={user.id} className="flex items-center space-x-2">
                    <CheckCircle size={14} className="text-green-500" />
                    <span>{user.username}</span>
                    <span className="text-gray-500">({user.role})</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Console de sortie */}
      <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm h-64 overflow-y-auto">
        <div className="mb-2 text-gray-400">Console de Debug:</div>
        <pre className="whitespace-pre-wrap">{testResult || 'Prêt pour les tests...'}</pre>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
        <h3 className="font-semibold mb-2 text-yellow-800">Instructions de Debug</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm text-yellow-700">
          <li>Cliquez sur "Diagnostic Complet" pour vérifier l'état du système</li>
          <li>Vérifiez que toutes les tables existent (icônes vertes)</li>
          <li>Testez une demande de suivi avec "Test Demande de Suivi"</li>
          <li>Vérifiez les logs dans la console pour identifier les erreurs</li>
          <li>Nettoyez les données de test après les tests</li>
        </ol>
      </div>
    </div>
  );
};

export default FollowRequestDebugPanel;
