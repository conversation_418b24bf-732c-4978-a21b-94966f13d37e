import React, { useState } from 'react';
import {
  Shield,
  DollarSign,
  Calendar,
  Clock,
  CheckCircle,
  Copy,
  AlertTriangle,
  <PERSON>freshCw,
  CreditCard,
  Building2
} from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import { formatAmount } from '../../utils/formatUtils';
import { SubscriptionCodeService } from '../../services/subscriptionCodeService';

interface SubscriptionPlan {
  id: string;
  type: 'trial' | 'monthly' | 'yearly';
  name: string;
  description: string;
  price: number;
  duration: string;
  features: string[];
  popular?: boolean;
}

interface SubscriptionCodeGeneratorProps {
  businessId: string;
  businessName: string;
  onCodeGenerated?: (code: string) => void;
}

const SubscriptionCodeGenerator: React.FC<SubscriptionCodeGeneratorProps> = ({
  businessId,
  businessName,
  onCodeGenerated
}) => {
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [generatedCode, setGeneratedCode] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [codeExpiration, setCodeExpiration] = useState<Date | null>(null);

  const subscriptionPlans: SubscriptionPlan[] = [
    {
      id: 'trial',
      type: 'trial',
      name: 'Essai Gratuit',
      description: 'Découvrez toutes les fonctionnalités',
      price: 0,
      duration: '7 jours',
      features: [
        'Accès complet à la plateforme',
        'Publication d\'offres limitées',
        'Support par email',
        'Statistiques de base'
      ]
    },
    {
      id: 'monthly',
      type: 'monthly',
      name: 'Plan Mensuel',
      description: 'Parfait pour commencer',
      price: 25000,
      duration: '30 jours',
      features: [
        'Publications illimitées',
        'Gestion des avis clients',
        'Statistiques avancées',
        'Support prioritaire',
        'Outils de promotion'
      ]
    },
    {
      id: 'yearly',
      type: 'yearly',
      name: 'Plan Annuel',
      description: 'Le meilleur rapport qualité-prix',
      price: 240000,
      duration: '365 jours',
      features: [
        'Toutes les fonctionnalités mensuelles',
        'Économie de 20%',
        'Support téléphonique',
        'Formations personnalisées',
        'API avancée',
        'Rapports personnalisés'
      ],
      popular: true
    }
  ];

  const handlePlanSelection = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setGeneratedCode(null);
    setCodeExpiration(null);

    // Générer automatiquement le code après sélection du plan
    setTimeout(() => {
      handleGenerateCode(plan);
    }, 500);
  };

  const handleGenerateCode = async (planToUse?: SubscriptionPlan) => {
    const plan = planToUse || selectedPlan;
    if (!plan) return;

    setIsGenerating(true);
    try {
      // Simuler un délai de génération
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Générer le code via le service
      const codeData = await SubscriptionCodeService.createSubscriptionCode(
        businessId,
        businessName,
        plan.type,
        plan.price
      );

      if (codeData) {
        setGeneratedCode(codeData.code);
        setCodeExpiration(new Date(codeData.expires_at));
        
        // Callback optionnel
        if (onCodeGenerated) {
          onCodeGenerated(codeData.code);
        }
      } else {
        alert('Erreur lors de la génération du code. Veuillez réessayer.');
      }
    } catch (error) {
      console.error('Erreur lors de la génération du code:', error);
      alert('Erreur lors de la génération du code. Veuillez réessayer.');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyCodeToClipboard = () => {
    if (generatedCode) {
      navigator.clipboard.writeText(generatedCode);
      alert('Code copié dans le presse-papiers !');
    }
  };

  const getTimeUntilExpiration = () => {
    if (!codeExpiration) return '';
    
    const now = new Date();
    const diffMs = codeExpiration.getTime() - now.getTime();
    
    if (diffMs <= 0) return 'Expiré';
    
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m`;
    } else {
      return `${diffMinutes}m`;
    }
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-blue-100 rounded-full">
            <Shield className="text-blue-600" size={32} />
          </div>
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Choisir un abonnement</h2>
        <p className="text-gray-600">
          Sélectionnez votre plan pour générer automatiquement un code de validation sécurisé
        </p>
      </div>

      {/* Étapes du processus */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-4 text-sm">
          <div className={`flex items-center space-x-2 ${!selectedPlan ? 'text-blue-600' : 'text-green-600'}`}>
            <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold ${
              !selectedPlan ? 'bg-blue-600' : 'bg-green-600'
            }`}>
              {!selectedPlan ? '1' : '✓'}
            </div>
            <span>Sélectionner un plan</span>
          </div>
          <div className="w-8 h-px bg-gray-300"></div>
          <div className={`flex items-center space-x-2 ${
            selectedPlan && !generatedCode ? 'text-blue-600' :
            generatedCode ? 'text-green-600' : 'text-gray-400'
          }`}>
            <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold ${
              selectedPlan && !generatedCode ? 'bg-blue-600' :
              generatedCode ? 'bg-green-600' : 'bg-gray-400'
            }`}>
              {generatedCode ? '✓' : '2'}
            </div>
            <span>Code généré</span>
          </div>
          <div className="w-8 h-px bg-gray-300"></div>
          <div className="flex items-center space-x-2 text-gray-400">
            <div className="w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center text-white text-xs font-bold">
              3
            </div>
            <span>Validation admin</span>
          </div>
        </div>
      </div>

      {/* Plans d'abonnement */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {subscriptionPlans.map((plan) => (
          <Card 
            key={plan.id} 
            className={`relative cursor-pointer transition-all duration-200 ${
              selectedPlan?.id === plan.id 
                ? 'ring-2 ring-blue-500 shadow-lg' 
                : 'hover:shadow-md'
            } ${plan.popular ? 'border-blue-500' : ''}`}
            onClick={() => handlePlanSelection(plan)}
          >
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-500 text-white px-3 py-1 text-xs font-semibold rounded-full">
                  Populaire
                </span>
              </div>
            )}
            <CardBody className="p-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{plan.name}</h3>
                <p className="text-gray-600 text-sm mb-4">{plan.description}</p>
                
                <div className="mb-4">
                  <div className="flex items-center justify-center space-x-2">
                    <DollarSign size={20} className="text-gray-400" />
                    <span className="text-3xl font-bold text-gray-900">
                      {plan.price === 0 ? 'Gratuit' : formatAmount(plan.price)}
                    </span>
                  </div>
                  <div className="flex items-center justify-center space-x-1 text-sm text-gray-500 mt-1">
                    <Calendar size={14} />
                    <span>{plan.duration}</span>
                  </div>
                </div>

                <ul className="space-y-2 text-sm text-gray-600 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <CheckCircle size={14} className="text-green-500 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>

                {selectedPlan?.id === plan.id && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-center justify-center space-x-2 text-blue-700">
                      {isGenerating ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                          <span className="font-medium">Génération du code...</span>
                        </>
                      ) : generatedCode ? (
                        <>
                          <CheckCircle size={16} />
                          <span className="font-medium">Code généré !</span>
                        </>
                      ) : (
                        <>
                          <CheckCircle size={16} />
                          <span className="font-medium">Plan sélectionné</span>
                        </>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      {/* Résumé et code généré */}
      {selectedPlan && (
        <Card>
          <CardBody className="p-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {generatedCode ? 'Code de validation généré' : 'Génération du code de validation'}
              </h3>

              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-center space-x-3 mb-3">
                  <Building2 size={20} className="text-gray-400" />
                  <span className="font-medium text-gray-900">{businessName}</span>
                </div>
                <div className="text-sm text-gray-600">
                  Plan sélectionné: <span className="font-medium">{selectedPlan.name}</span>
                </div>
                <div className="text-sm text-gray-600">
                  Montant: <span className="font-medium">
                    {selectedPlan.price === 0 ? 'Gratuit' : formatAmount(selectedPlan.price)}
                  </span>
                </div>
              </div>

              {isGenerating ? (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <div className="flex items-center justify-center space-x-3 mb-3">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <span className="font-medium text-blue-800">Génération du code en cours...</span>
                  </div>
                  <p className="text-sm text-blue-700">
                    Veuillez patienter pendant que nous générons votre code de validation sécurisé.
                  </p>
                </div>
              ) : generatedCode ? (
                <div className="space-y-4">
                  {/* Code généré */}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                    <div className="flex items-center justify-center space-x-2 mb-3">
                      <CheckCircle className="text-green-500" size={20} />
                      <span className="font-medium text-green-800">Code généré avec succès !</span>
                    </div>
                    
                    <div className="bg-white border border-green-300 rounded-lg p-4 mb-4">
                      <div className="text-sm text-gray-600 mb-2">Votre code de validation :</div>
                      <div className="flex items-center justify-center space-x-3">
                        <span className="text-3xl font-mono font-bold text-blue-600">
                          {SubscriptionCodeService.formatCode(generatedCode)}
                        </span>
                        <button
                          onClick={copyCodeToClipboard}
                          className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                          title="Copier le code"
                        >
                          <Copy size={20} />
                        </button>
                      </div>
                    </div>

                    {/* Informations d'expiration */}
                    <div className="flex items-center justify-center space-x-2 text-sm text-orange-600">
                      <Clock size={14} />
                      <span>Expire dans {getTimeUntilExpiration()}</span>
                    </div>
                  </div>

                  {/* Instructions */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <AlertTriangle className="text-blue-500 mt-0.5" size={16} />
                      <div className="text-left">
                        <h4 className="font-medium text-blue-800 mb-2">Instructions importantes</h4>
                        <ul className="text-sm text-blue-700 space-y-1">
                          <li>• Ce code doit être validé par un administrateur</li>
                          <li>• Le code expire dans 24 heures</li>
                          <li>• Conservez ce code en lieu sûr</li>
                          <li>• Votre abonnement sera activé après validation</li>
                          <li>• Vous recevrez une confirmation par email</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button
                      variant="outline"
                      leftIcon={<RefreshCw size={16} />}
                      onClick={() => {
                        setGeneratedCode(null);
                        setCodeExpiration(null);
                        setTimeout(() => handleGenerateCode(), 500);
                      }}
                      disabled={isGenerating}
                    >
                      Générer un nouveau code
                    </Button>
                    <Button
                      variant="outline"
                      leftIcon={<CreditCard size={16} />}
                    >
                      Voir l'historique des codes
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center justify-center space-x-2 text-yellow-800 mb-3">
                    <Clock size={16} />
                    <span className="font-medium">En attente de génération</span>
                  </div>
                  <p className="text-sm text-yellow-700 mb-4">
                    Le code sera généré automatiquement. Si cela prend trop de temps, vous pouvez essayer de générer manuellement.
                  </p>
                  <Button
                    onClick={() => handleGenerateCode()}
                    disabled={isGenerating}
                    leftIcon={<Shield size={16} />}
                    className="w-full md:w-auto"
                  >
                    Générer le code maintenant
                  </Button>
                </div>
              )}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Informations de sécurité */}
      <Card>
        <CardBody className="p-4">
          <div className="flex items-start space-x-3">
            <Shield className="text-blue-500 mt-0.5" size={16} />
            <div>
              <h4 className="font-medium text-gray-900 mb-1">Sécurité des paiements</h4>
              <p className="text-sm text-gray-600">
                Nos codes de validation garantissent la sécurité de vos transactions. 
                Chaque code est unique, temporaire et doit être validé par notre équipe 
                avant l'activation de votre abonnement.
              </p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default SubscriptionCodeGenerator;
