import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../lib/supabase';
import Card, { CardBody } from '../components/ui/Card';
import UserStatusBadge from '../components/ui/UserStatusBadge';
import {
  Settings,
  User,
  Lock,
  Bell,
  Shield,
  Eye,
  Globe,
  Trash2,
  Save,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { UserRole } from '../types';

interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  marketingEmails: boolean;
  statusUpdates: boolean;
  newFollowers: boolean;
  postLikes: boolean;
  comments: boolean;
}

interface PrivacySettings {
  profileVisibility: 'public' | 'private' | 'friends';
  showEmail: boolean;
  showLocation: boolean;
  showActivity: boolean;
  allowMessages: boolean;
  allowFollows: boolean;
}

const SettingsPage: React.FC = () => {
  const { currentUser, logout } = useAuth();
  const [loading, setLoading] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');
  const [activeTab, setActiveTab] = useState('profile');

  // États pour les différents paramètres
  const [notifications, setNotifications] = useState<NotificationSettings>({
    emailNotifications: true,
    pushNotifications: true,
    marketingEmails: false,
    statusUpdates: true,
    newFollowers: true,
    postLikes: true,
    comments: true
  });

  const [privacy, setPrivacy] = useState<PrivacySettings>({
    profileVisibility: 'public',
    showEmail: false,
    showLocation: true,
    showActivity: true,
    allowMessages: true,
    allowFollows: true
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  useEffect(() => {
    if (currentUser) {
      loadUserSettings();
    }
  }, [currentUser]);

  const loadUserSettings = async () => {
    try {
      // Charger les paramètres depuis la base de données
      const { data, error } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', currentUser?.id)
        .single();

      if (data && !error) {
        setNotifications(data.notifications || notifications);
        setPrivacy(data.privacy || privacy);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des paramètres:', error);
    }
  };

  const saveSettings = async (settingsType: 'notifications' | 'privacy') => {
    if (!currentUser) return;

    setLoading(true);
    try {
      const settingsData = {
        user_id: currentUser.id,
        [settingsType]: settingsType === 'notifications' ? notifications : privacy,
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('user_settings')
        .upsert(settingsData);

      if (error) throw error;

      setSaveMessage('Paramètres sauvegardés avec succès !');
      setTimeout(() => setSaveMessage(''), 3000);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      setSaveMessage('Erreur lors de la sauvegarde');
    } finally {
      setLoading(false);
    }
  };

  const changePassword = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setSaveMessage('Les mots de passe ne correspondent pas');
      return;
    }

    if (passwordData.newPassword.length < 6) {
      setSaveMessage('Le mot de passe doit contenir au moins 6 caractères');
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (error) throw error;

      setSaveMessage('Mot de passe modifié avec succès !');
      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
    } catch (error) {
      console.error('Erreur lors du changement de mot de passe:', error);
      setSaveMessage('Erreur lors du changement de mot de passe');
    } finally {
      setLoading(false);
    }
  };

  const deleteAccount = async () => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer votre compte ? Cette action est irréversible.')) {
      return;
    }

    const confirmation = window.prompt('Tapez "SUPPRIMER" pour confirmer la suppression de votre compte:');
    if (confirmation !== 'SUPPRIMER') {
      return;
    }

    setLoading(true);
    try {
      // Marquer le compte comme supprimé (soft delete)
      const { error } = await supabase
        .from('profiles')
        .update({
          deleted_at: new Date().toISOString(),
          status: 'deleted'
        })
        .eq('id', currentUser?.id);

      if (error) throw error;

      await logout();
    } catch (error) {
      console.error('Erreur lors de la suppression du compte:', error);
      setSaveMessage('Erreur lors de la suppression du compte');
    } finally {
      setLoading(false);
    }
  };

  if (!currentUser) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="mx-auto text-red-500 mb-4" size={48} />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Accès refusé</h2>
          <p className="text-gray-600">Vous devez être connecté pour accéder aux paramètres.</p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'profile', label: 'Profil', icon: User },
    { id: 'security', label: 'Sécurité', icon: Lock },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'privacy', label: 'Confidentialité', icon: Shield },
    { id: 'account', label: 'Compte', icon: Settings }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Settings className="text-blue-600 mr-3" size={32} />
          <h1 className="text-3xl font-bold text-gray-900">Paramètres</h1>
        </div>
        <p className="text-lg text-gray-600">
          Gérez vos préférences et paramètres de compte
        </p>
      </div>

      {/* Message de sauvegarde */}
      {saveMessage && (
        <div className={`mb-6 p-4 rounded-lg flex items-center ${
          saveMessage.includes('succès')
            ? 'bg-green-100 text-green-700 border border-green-200'
            : 'bg-red-100 text-red-700 border border-red-200'
        }`}>
          {saveMessage.includes('succès') ? (
            <CheckCircle className="mr-2" size={20} />
          ) : (
            <AlertTriangle className="mr-2" size={20} />
          )}
          {saveMessage}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Navigation des onglets */}
        <div className="lg:col-span-1">
          <Card>
            <CardBody className="p-0">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const IconComponent = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-600'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                      }`}
                    >
                      <IconComponent className="mr-3" size={18} />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>
            </CardBody>
          </Card>
        </div>

        {/* Contenu des onglets */}
        <div className="lg:col-span-3">
          {/* Onglet Profil */}
          {activeTab === 'profile' && (
            <Card>
              <CardBody>
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Informations du profil</h2>

                <div className="space-y-6">
                  {/* Informations de base */}
                  <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <img
                      src={currentUser.profilePicture || '/default-avatar.png'}
                      alt={currentUser.username}
                      className="w-16 h-16 rounded-full object-cover"
                    />
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-gray-900">
                        {currentUser.role === UserRole.BUSINESS && 'businessName' in currentUser
                          ? (currentUser as any).businessName
                          : currentUser.username}
                      </h3>
                      <p className="text-gray-600">@{currentUser.username}</p>
                      <div className="mt-2">
                        <UserStatusBadge status={currentUser.status} size="sm" variant="gradient" />
                      </div>
                    </div>
                  </div>

                  {/* Statistiques */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {(currentUser as any).post_count || 0}
                      </div>
                      <div className="text-sm text-gray-600">Publications</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {(currentUser as any).followers_count || 0}
                      </div>
                      <div className="text-sm text-gray-600">Abonnés</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">
                        {(currentUser as any).following_count || 0}
                      </div>
                      <div className="text-sm text-gray-600">Abonnements</div>
                    </div>
                    <div className="text-center p-4 bg-orange-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">
                        {(currentUser as any).comment_count || 0}
                      </div>
                      <div className="text-sm text-gray-600">Commentaires</div>
                    </div>
                  </div>

                  {/* Informations du compte */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                      <input
                        type="email"
                        value={currentUser.email}
                        disabled
                        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Rôle</label>
                      <input
                        type="text"
                        value={currentUser.role === UserRole.BUSINESS ? 'Entreprise' : 'Utilisateur'}
                        disabled
                        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Membre depuis</label>
                      <input
                        type="text"
                        value={new Date(currentUser.createdAt).toLocaleDateString('fr-FR', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                        disabled
                        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                      />
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}

          {/* Onglet Sécurité */}
          {activeTab === 'security' && (
            <Card>
              <CardBody>
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Sécurité du compte</h2>

                <div className="space-y-6">
                  {/* Changement de mot de passe */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Changer le mot de passe</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Mot de passe actuel
                        </label>
                        <input
                          type="password"
                          value={passwordData.currentPassword}
                          onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Entrez votre mot de passe actuel"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Nouveau mot de passe
                        </label>
                        <input
                          type="password"
                          value={passwordData.newPassword}
                          onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Entrez votre nouveau mot de passe"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Confirmer le nouveau mot de passe
                        </label>
                        <input
                          type="password"
                          value={passwordData.confirmPassword}
                          onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Confirmez votre nouveau mot de passe"
                        />
                      </div>
                      <button
                        onClick={changePassword}
                        disabled={loading || !passwordData.currentPassword || !passwordData.newPassword}
                        className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Lock className="mr-2" size={16} />
                        {loading ? 'Modification...' : 'Changer le mot de passe'}
                      </button>
                    </div>
                  </div>

                  {/* Sessions actives */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Sessions actives</h3>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Session actuelle</p>
                          <p className="text-sm text-gray-600">Navigateur web • Maintenant</p>
                        </div>
                        <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                          Actif
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}

          {/* Onglet Notifications */}
          {activeTab === 'notifications' && (
            <Card>
              <CardBody>
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Préférences de notification</h2>

                <div className="space-y-6">
                  {/* Notifications générales */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Notifications générales</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Notifications par email</p>
                          <p className="text-sm text-gray-600">Recevoir des notifications importantes par email</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notifications.emailNotifications}
                            onChange={(e) => setNotifications(prev => ({ ...prev, emailNotifications: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Notifications push</p>
                          <p className="text-sm text-gray-600">Recevoir des notifications push dans le navigateur</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notifications.pushNotifications}
                            onChange={(e) => setNotifications(prev => ({ ...prev, pushNotifications: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Emails marketing</p>
                          <p className="text-sm text-gray-600">Recevoir des informations sur les nouveautés et promotions</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notifications.marketingEmails}
                            onChange={(e) => setNotifications(prev => ({ ...prev, marketingEmails: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Notifications d'activité */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Notifications d'activité</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Mises à jour de statut</p>
                          <p className="text-sm text-gray-600">Être notifié lors des changements de niveau</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notifications.statusUpdates}
                            onChange={(e) => setNotifications(prev => ({ ...prev, statusUpdates: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Nouveaux abonnés</p>
                          <p className="text-sm text-gray-600">Être notifié quand quelqu'un vous suit</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notifications.newFollowers}
                            onChange={(e) => setNotifications(prev => ({ ...prev, newFollowers: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Likes sur vos publications</p>
                          <p className="text-sm text-gray-600">Être notifié quand vos posts reçoivent des likes</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notifications.postLikes}
                            onChange={(e) => setNotifications(prev => ({ ...prev, postLikes: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Nouveaux commentaires</p>
                          <p className="text-sm text-gray-600">Être notifié des commentaires sur vos publications</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notifications.comments}
                            onChange={(e) => setNotifications(prev => ({ ...prev, comments: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4">
                    <button
                      onClick={() => saveSettings('notifications')}
                      disabled={loading}
                      className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Save className="mr-2" size={16} />
                      {loading ? 'Sauvegarde...' : 'Sauvegarder les préférences'}
                    </button>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}

          {/* Onglet Confidentialité */}
          {activeTab === 'privacy' && (
            <Card>
              <CardBody>
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Paramètres de confidentialité</h2>

                <div className="space-y-6">
                  {/* Visibilité du profil */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Visibilité du profil</h3>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="profileVisibility"
                          value="public"
                          checked={privacy.profileVisibility === 'public'}
                          onChange={(e) => setPrivacy(prev => ({ ...prev, profileVisibility: e.target.value as any }))}
                          className="mr-3 text-blue-600"
                        />
                        <div>
                          <p className="font-medium text-gray-900">Public</p>
                          <p className="text-sm text-gray-600">Tout le monde peut voir votre profil</p>
                        </div>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="profileVisibility"
                          value="friends"
                          checked={privacy.profileVisibility === 'friends'}
                          onChange={(e) => setPrivacy(prev => ({ ...prev, profileVisibility: e.target.value as any }))}
                          className="mr-3 text-blue-600"
                        />
                        <div>
                          <p className="font-medium text-gray-900">Abonnés uniquement</p>
                          <p className="text-sm text-gray-600">Seuls vos abonnés peuvent voir votre profil</p>
                        </div>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="profileVisibility"
                          value="private"
                          checked={privacy.profileVisibility === 'private'}
                          onChange={(e) => setPrivacy(prev => ({ ...prev, profileVisibility: e.target.value as any }))}
                          className="mr-3 text-blue-600"
                        />
                        <div>
                          <p className="font-medium text-gray-900">Privé</p>
                          <p className="text-sm text-gray-600">Profil visible uniquement par vous</p>
                        </div>
                      </label>
                    </div>
                  </div>

                  {/* Informations visibles */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Informations visibles</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Afficher l'email</p>
                          <p className="text-sm text-gray-600">Permettre aux autres de voir votre adresse email</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={privacy.showEmail}
                            onChange={(e) => setPrivacy(prev => ({ ...prev, showEmail: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Afficher la localisation</p>
                          <p className="text-sm text-gray-600">Permettre aux autres de voir votre ville et pays</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={privacy.showLocation}
                            onChange={(e) => setPrivacy(prev => ({ ...prev, showLocation: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Afficher l'activité</p>
                          <p className="text-sm text-gray-600">Permettre aux autres de voir vos statistiques d'activité</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={privacy.showActivity}
                            onChange={(e) => setPrivacy(prev => ({ ...prev, showActivity: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Interactions */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Interactions</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Autoriser les messages</p>
                          <p className="text-sm text-gray-600">Permettre aux autres utilisateurs de vous envoyer des messages</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={privacy.allowMessages}
                            onChange={(e) => setPrivacy(prev => ({ ...prev, allowMessages: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">Autoriser les abonnements</p>
                          <p className="text-sm text-gray-600">Permettre aux autres utilisateurs de vous suivre</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={privacy.allowFollows}
                            onChange={(e) => setPrivacy(prev => ({ ...prev, allowFollows: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4">
                    <button
                      onClick={() => saveSettings('privacy')}
                      disabled={loading}
                      className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Save className="mr-2" size={16} />
                      {loading ? 'Sauvegarde...' : 'Sauvegarder les paramètres'}
                    </button>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}

          {/* Onglet Compte */}
          {activeTab === 'account' && (
            <Card>
              <CardBody>
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Gestion du compte</h2>

                <div className="space-y-6">
                  {/* Informations du compte */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Informations du compte</h3>
                    <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">ID du compte:</span>
                        <span className="font-mono text-sm">{currentUser.id}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Date de création:</span>
                        <span>{new Date(currentUser.createdAt).toLocaleDateString('fr-FR')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Type de compte:</span>
                        <span>{currentUser.role === UserRole.BUSINESS ? 'Entreprise' : 'Personnel'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Statut:</span>
                        <UserStatusBadge status={currentUser.status} size="sm" />
                      </div>
                    </div>
                  </div>

                  {/* Données et statistiques */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Vos données</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-blue-50 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {(currentUser as any).post_count || 0}
                        </div>
                        <div className="text-sm text-gray-600">Publications</div>
                      </div>
                      <div className="bg-green-50 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {(currentUser as any).comment_count || 0}
                        </div>
                        <div className="text-sm text-gray-600">Commentaires</div>
                      </div>
                    </div>
                  </div>

                  {/* Actions de compte */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Actions du compte</h3>
                    <div className="space-y-4">
                      <button
                        onClick={() => window.open('/export-data', '_blank')}
                        className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <Globe className="mr-2" size={16} />
                        Exporter mes données
                      </button>

                      <button
                        onClick={logout}
                        className="w-full flex items-center justify-center px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        <Eye className="mr-2" size={16} />
                        Se déconnecter
                      </button>
                    </div>
                  </div>

                  {/* Zone de danger */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium text-red-600 mb-4">Zone de danger</h3>
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="flex items-start">
                        <AlertTriangle className="text-red-500 mr-3 mt-1" size={20} />
                        <div className="flex-1">
                          <h4 className="font-medium text-red-800 mb-2">Supprimer le compte</h4>
                          <p className="text-sm text-red-700 mb-4">
                            Cette action est irréversible. Toutes vos données, publications et commentaires seront définitivement supprimés.
                          </p>
                          <button
                            onClick={deleteAccount}
                            disabled={loading}
                            className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <Trash2 className="mr-2" size={16} />
                            {loading ? 'Suppression...' : 'Supprimer mon compte'}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
