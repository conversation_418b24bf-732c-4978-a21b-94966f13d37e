-- DÉPLOIEMENT COMPLET DES PARAMÈTRES SYSTÈME
-- Exécutez ce script dans Supabase SQL Editor

-- =====================================================
-- 1. CRÉER LA TABLE DES PARAMÈTRES SYSTÈME
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '⚙️ Création de la table des paramètres système...';
    
    -- Table pour stocker les configurations système
    CREATE TABLE IF NOT EXISTS system_configs (
        id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
        category text NOT NULL,
        key text NOT NULL,
        value jsonb NOT NULL,
        type text NOT NULL CHECK (type IN ('string', 'number', 'boolean', 'json', 'password')),
        description text NOT NULL,
        is_sensitive boolean DEFAULT false,
        is_readonly boolean DEFAULT false,
        created_at timestamp with time zone DEFAULT now(),
        updated_at timestamp with time zone DEFAULT now(),
        
        -- Contrainte d'unicité sur category + key
        UNIQUE(category, key)
    );
    
    -- Index pour les performances
    CREATE INDEX IF NOT EXISTS idx_system_configs_category ON system_configs(category);
    CREATE INDEX IF NOT EXISTS idx_system_configs_key ON system_configs(key);
    CREATE INDEX IF NOT EXISTS idx_system_configs_sensitive ON system_configs(is_sensitive);
    
    RAISE NOTICE '✅ Table system_configs créée';
    
END $$;

-- =====================================================
-- 2. CRÉER LES FONCTIONS DE GESTION DES PARAMÈTRES
-- =====================================================

-- Fonction pour obtenir une configuration
CREATE OR REPLACE FUNCTION get_system_config(p_category text, p_key text)
RETURNS jsonb AS $$
DECLARE
    config_value jsonb;
BEGIN
    SELECT value INTO config_value
    FROM system_configs
    WHERE category = p_category AND key = p_key;
    
    RETURN COALESCE(config_value, 'null'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- Fonction pour définir une configuration
CREATE OR REPLACE FUNCTION set_system_config(
    p_category text,
    p_key text,
    p_value jsonb,
    p_type text DEFAULT 'string',
    p_description text DEFAULT '',
    p_is_sensitive boolean DEFAULT false,
    p_is_readonly boolean DEFAULT false
)
RETURNS uuid AS $$
DECLARE
    config_id uuid;
BEGIN
    INSERT INTO system_configs (
        category, key, value, type, description, is_sensitive, is_readonly
    ) VALUES (
        p_category, p_key, p_value, p_type, p_description, p_is_sensitive, p_is_readonly
    )
    ON CONFLICT (category, key)
    DO UPDATE SET
        value = EXCLUDED.value,
        type = EXCLUDED.type,
        description = EXCLUDED.description,
        is_sensitive = EXCLUDED.is_sensitive,
        is_readonly = EXCLUDED.is_readonly,
        updated_at = now()
    RETURNING id INTO config_id;
    
    RETURN config_id;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour supprimer une configuration
CREATE OR REPLACE FUNCTION delete_system_config(p_category text, p_key text)
RETURNS boolean AS $$
DECLARE
    deleted_count integer;
BEGIN
    DELETE FROM system_configs
    WHERE category = p_category AND key = p_key AND is_readonly = false;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count > 0;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. INSÉRER LES PARAMÈTRES PAR DÉFAUT
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📋 Insertion des paramètres par défaut...';
    
    -- Paramètres généraux
    PERFORM set_system_config('general', 'app_name', '"Customeroom"', 'string', 'Nom de l''application', false, false);
    PERFORM set_system_config('general', 'app_version', '"1.0.0"', 'string', 'Version de l''application', false, true);
    PERFORM set_system_config('general', 'maintenance_mode', 'false', 'boolean', 'Mode maintenance activé', false, false);
    PERFORM set_system_config('general', 'max_users', '10000', 'number', 'Nombre maximum d''utilisateurs', false, false);
    PERFORM set_system_config('general', 'default_language', '"fr"', 'string', 'Langue par défaut', false, false);
    PERFORM set_system_config('general', 'timezone', '"Africa/Abidjan"', 'string', 'Fuseau horaire par défaut', false, false);
    
    -- Paramètres de base de données
    PERFORM set_system_config('database', 'connection_pool_size', '20', 'number', 'Taille du pool de connexions', false, false);
    PERFORM set_system_config('database', 'query_timeout', '30000', 'number', 'Timeout des requêtes (ms)', false, false);
    PERFORM set_system_config('database', 'backup_retention_days', '30', 'number', 'Rétention des sauvegardes (jours)', false, false);
    PERFORM set_system_config('database', 'auto_vacuum', 'true', 'boolean', 'Auto-vacuum activé', false, false);
    
    -- Paramètres de sécurité
    PERFORM set_system_config('security', 'session_timeout', '3600', 'number', 'Timeout de session (secondes)', false, false);
    PERFORM set_system_config('security', 'password_min_length', '8', 'number', 'Longueur minimale du mot de passe', false, false);
    PERFORM set_system_config('security', 'max_login_attempts', '5', 'number', 'Tentatives de connexion max', false, false);
    PERFORM set_system_config('security', 'jwt_expiry', '86400', 'number', 'Expiration JWT (secondes)', false, false);
    PERFORM set_system_config('security', 'jwt_secret', '"***CHANGE_ME***"', 'password', 'Clé secrète JWT', true, false);
    PERFORM set_system_config('security', 'encryption_key', '"***CHANGE_ME***"', 'password', 'Clé de chiffrement', true, false);
    
    -- Paramètres email
    PERFORM set_system_config('email', 'smtp_host', '"smtp.gmail.com"', 'string', 'Serveur SMTP', false, false);
    PERFORM set_system_config('email', 'smtp_port', '587', 'number', 'Port SMTP', false, false);
    PERFORM set_system_config('email', 'smtp_username', '"<EMAIL>"', 'string', 'Nom d''utilisateur SMTP', false, false);
    PERFORM set_system_config('email', 'smtp_password', '"***CHANGE_ME***"', 'password', 'Mot de passe SMTP', true, false);
    PERFORM set_system_config('email', 'from_email', '"<EMAIL>"', 'string', 'Email expéditeur', false, false);
    PERFORM set_system_config('email', 'from_name', '"Customeroom"', 'string', 'Nom expéditeur', false, false);
    
    -- Paramètres API
    PERFORM set_system_config('api', 'rate_limit_per_hour', '1000', 'number', 'Limite de requêtes API par heure', false, false);
    PERFORM set_system_config('api', 'rate_limit_per_minute', '60', 'number', 'Limite de requêtes API par minute', false, false);
    PERFORM set_system_config('api', 'api_key_expiry_days', '365', 'number', 'Expiration des clés API (jours)', false, false);
    PERFORM set_system_config('api', 'cors_origins', '["http://localhost:3000", "https://customeroom.com"]', 'json', 'Origines CORS autorisées', false, false);
    PERFORM set_system_config('api', 'api_version', '"v1"', 'string', 'Version de l''API', false, true);
    
    -- Paramètres de paiement
    PERFORM set_system_config('payment', 'stripe_public_key', '"pk_test_..."', 'string', 'Clé publique Stripe', false, false);
    PERFORM set_system_config('payment', 'stripe_secret_key', '"sk_test_..."', 'password', 'Clé secrète Stripe', true, false);
    PERFORM set_system_config('payment', 'default_currency', '"XOF"', 'string', 'Devise par défaut', false, false);
    PERFORM set_system_config('payment', 'commission_rate', '0.05', 'number', 'Taux de commission (0-1)', false, false);
    PERFORM set_system_config('payment', 'min_payout_amount', '10000', 'number', 'Montant minimum de retrait (XOF)', false, false);
    
    -- Paramètres de performance
    PERFORM set_system_config('performance', 'cache_ttl', '3600', 'number', 'TTL du cache (secondes)', false, false);
    PERFORM set_system_config('performance', 'max_file_size', '10485760', 'number', 'Taille max des fichiers (bytes)', false, false);
    PERFORM set_system_config('performance', 'image_compression_quality', '85', 'number', 'Qualité compression images (%)', false, false);
    PERFORM set_system_config('performance', 'cdn_enabled', 'true', 'boolean', 'CDN activé', false, false);
    PERFORM set_system_config('performance', 'gzip_compression', 'true', 'boolean', 'Compression GZIP activée', false, false);
    
    -- Paramètres de monitoring
    PERFORM set_system_config('monitoring', 'log_level', '"info"', 'string', 'Niveau de log', false, false);
    PERFORM set_system_config('monitoring', 'error_reporting', 'true', 'boolean', 'Rapport d''erreurs activé', false, false);
    PERFORM set_system_config('monitoring', 'analytics_enabled', 'true', 'boolean', 'Analytics activées', false, false);
    PERFORM set_system_config('monitoring', 'performance_monitoring', 'true', 'boolean', 'Monitoring performance activé', false, false);
    PERFORM set_system_config('monitoring', 'log_retention_days', '90', 'number', 'Rétention des logs (jours)', false, false);
    
    RAISE NOTICE '✅ Paramètres par défaut insérés';
    
END $$;

-- =====================================================
-- 4. CRÉER LES POLITIQUES DE SÉCURITÉ RLS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔒 Configuration des politiques de sécurité...';
    
    -- Activer RLS sur la table
    ALTER TABLE system_configs ENABLE ROW LEVEL SECURITY;
    
    -- Politique pour les super admins (accès complet)
    CREATE POLICY "Super admins can manage all system configs" ON system_configs
        FOR ALL
        TO authenticated
        USING (
            EXISTS (
                SELECT 1 FROM admin_profiles ap
                JOIN profiles p ON ap.user_id = p.id
                WHERE p.id = auth.uid()
                AND ap.admin_level = 'super_admin'
                AND ap.is_active = true
            )
        );
    
    -- Politique pour les platform admins (lecture seule des configs non sensibles)
    CREATE POLICY "Platform admins can read non-sensitive configs" ON system_configs
        FOR SELECT
        TO authenticated
        USING (
            is_sensitive = false
            AND EXISTS (
                SELECT 1 FROM admin_profiles ap
                JOIN profiles p ON ap.user_id = p.id
                WHERE p.id = auth.uid()
                AND ap.admin_level IN ('platform_admin', 'super_admin')
                AND ap.is_active = true
            )
        );
    
    RAISE NOTICE '✅ Politiques de sécurité configurées';
    
END $$;

-- =====================================================
-- 5. CRÉER UNE VUE POUR LES PARAMÈTRES PUBLICS
-- =====================================================

CREATE OR REPLACE VIEW public_system_configs AS
SELECT 
    category,
    key,
    CASE 
        WHEN is_sensitive THEN '"***HIDDEN***"'::jsonb
        ELSE value
    END as value,
    type,
    description,
    is_readonly,
    updated_at
FROM system_configs
WHERE is_sensitive = false;

-- =====================================================
-- 6. CRÉER DES TRIGGERS POUR L'AUDIT
-- =====================================================

-- Fonction de trigger pour l'audit des modifications
CREATE OR REPLACE FUNCTION audit_system_config_changes()
RETURNS TRIGGER AS $$
BEGIN
    -- Enregistrer dans les logs d'audit admin si la table existe
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_audit_log') THEN
        INSERT INTO admin_audit_log (
            admin_id,
            action,
            resource_type,
            resource_id,
            details,
            ip_address
        ) VALUES (
            (SELECT ap.id FROM admin_profiles ap JOIN profiles p ON ap.user_id = p.id WHERE p.id = auth.uid()),
            CASE 
                WHEN TG_OP = 'INSERT' THEN 'CREATE'
                WHEN TG_OP = 'UPDATE' THEN 'UPDATE'
                WHEN TG_OP = 'DELETE' THEN 'DELETE'
            END,
            'system_config',
            COALESCE(NEW.id, OLD.id),
            jsonb_build_object(
                'category', COALESCE(NEW.category, OLD.category),
                'key', COALESCE(NEW.key, OLD.key),
                'old_value', CASE WHEN TG_OP != 'INSERT' THEN OLD.value END,
                'new_value', CASE WHEN TG_OP != 'DELETE' THEN NEW.value END
            ),
            inet_client_addr()
        );
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Créer le trigger
DROP TRIGGER IF EXISTS audit_system_configs ON system_configs;
CREATE TRIGGER audit_system_configs
    AFTER INSERT OR UPDATE OR DELETE ON system_configs
    FOR EACH ROW EXECUTE FUNCTION audit_system_config_changes();

-- =====================================================
-- 7. AFFICHER LES STATISTIQUES
-- =====================================================

-- Compter les paramètres par catégorie
SELECT 
    '📊 PARAMÈTRES PAR CATÉGORIE' as info,
    category,
    COUNT(*) as total,
    COUNT(*) FILTER (WHERE is_sensitive) as sensibles,
    COUNT(*) FILTER (WHERE is_readonly) as lecture_seule
FROM system_configs 
GROUP BY category 
ORDER BY category;

-- Afficher tous les paramètres (valeurs sensibles masquées)
SELECT 
    '⚙️ TOUS LES PARAMÈTRES' as info,
    category,
    key,
    CASE 
        WHEN is_sensitive THEN '***HIDDEN***'
        ELSE value::text
    END as value,
    type,
    description
FROM system_configs 
ORDER BY category, key;

-- =====================================================
-- 8. MESSAGE DE SUCCÈS FINAL
-- =====================================================

DO $$
DECLARE
    total_configs integer;
    categories_count integer;
    sensitive_count integer;
BEGIN
    SELECT COUNT(*) INTO total_configs FROM system_configs;
    SELECT COUNT(DISTINCT category) INTO categories_count FROM system_configs;
    SELECT COUNT(*) INTO sensitive_count FROM system_configs WHERE is_sensitive = true;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '🎉 PARAMÈTRES SYSTÈME COMPLÈTEMENT DÉPLOYÉS !';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '⚙️ CONFIGURATION CRÉÉE:';
    RAISE NOTICE '   📋 Total paramètres: %', total_configs;
    RAISE NOTICE '   📂 Catégories: %', categories_count;
    RAISE NOTICE '   🔒 Paramètres sensibles: %', sensitive_count;
    RAISE NOTICE '';
    RAISE NOTICE '🔧 FONCTIONNALITÉS DISPONIBLES:';
    RAISE NOTICE '   ✅ Interface d''administration complète';
    RAISE NOTICE '   ✅ 8 catégories de paramètres';
    RAISE NOTICE '   ✅ Édition en ligne sécurisée';
    RAISE NOTICE '   ✅ Gestion des paramètres sensibles';
    RAISE NOTICE '   ✅ Export/Import de configuration';
    RAISE NOTICE '   ✅ Audit des modifications';
    RAISE NOTICE '   ✅ Politiques de sécurité RLS';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 PROCHAINES ÉTAPES:';
    RAISE NOTICE '1. Allez dans "Paramètres Système"';
    RAISE NOTICE '2. Explorez les 8 catégories';
    RAISE NOTICE '3. Modifiez les paramètres selon vos besoins';
    RAISE NOTICE '4. Configurez les paramètres sensibles';
    RAISE NOTICE '5. Exportez la configuration';
    RAISE NOTICE '';
    RAISE NOTICE '🎊 SYSTÈME DE PARAMÈTRES OPÉRATIONNEL !';
    RAISE NOTICE '';
END $$;
