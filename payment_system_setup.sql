-- =====================================================
-- SCRIPT COMPLET: Système de Paiement et d'Abonnement
-- Version: 1.0.0
-- Date: 2025-05-30
-- Description: Script complet pour créer toutes les tables,
--              fonctions, procédures et optimisations du
--              système de paiement
-- =====================================================

-- Extension pour UUID si pas déjà activée
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. TABLE DES PLANS D'ABONNEMENT
-- =====================================================
CREATE TABLE subscription_plans (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'XOF',
    duration_days INTEGER NOT NULL DEFAULT 30,
    features JSONB NOT NULL DEFAULT '[]',
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_trial BOOLEAN NOT NULL DEFAULT false,
    max_reviews INTEGER,
    max_products INTEGER,
    has_ai_advisor BOOLEAN NOT NULL DEFAULT false,
    has_advanced_analytics BOOLEAN NOT NULL DEFAULT false,
    has_api_access BOOLEAN NOT NULL DEFAULT false,
    has_priority_support BOOLEAN NOT NULL DEFAULT false,
    has_phone_support BOOLEAN NOT NULL DEFAULT false,
    has_dedicated_manager BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les requêtes fréquentes
CREATE INDEX idx_subscription_plans_active ON subscription_plans(is_active);
CREATE INDEX idx_subscription_plans_price ON subscription_plans(price);

-- =====================================================
-- 2. TABLE DES ABONNEMENTS BUSINESS
-- =====================================================
CREATE TABLE business_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL,
    plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(id),
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_trial BOOLEAN NOT NULL DEFAULT false,
    auto_renew BOOLEAN NOT NULL DEFAULT false,
    trial_used BOOLEAN NOT NULL DEFAULT false,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
    payment_method VARCHAR(50),
    stripe_subscription_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les requêtes fréquentes
CREATE INDEX idx_business_subscriptions_business_id ON business_subscriptions(business_id);
CREATE INDEX idx_business_subscriptions_active ON business_subscriptions(business_id, is_active);
CREATE INDEX idx_business_subscriptions_end_date ON business_subscriptions(end_date);
CREATE INDEX idx_business_subscriptions_plan ON business_subscriptions(plan_id);

-- =====================================================
-- 3. TABLE DES MÉTHODES DE PAIEMENT
-- =====================================================
CREATE TABLE payment_methods (
    id VARCHAR(50) PRIMARY KEY,
    business_id UUID NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('card', 'mobile_money', 'bank_transfer', 'crypto')),
    provider VARCHAR(50) NOT NULL, -- Visa, Mastercard, Orange Money, MTN Money, etc.

    -- Pour les cartes bancaires
    card_last4 VARCHAR(4),
    card_brand VARCHAR(20),
    card_exp_month INTEGER,
    card_exp_year INTEGER,
    card_fingerprint VARCHAR(100),

    -- Pour mobile money
    phone_number VARCHAR(20),
    phone_country_code VARCHAR(5),

    -- Pour virements bancaires
    bank_name VARCHAR(100),
    bank_account_last4 VARCHAR(4),
    bank_routing_number VARCHAR(20),

    -- Métadonnées
    is_default BOOLEAN NOT NULL DEFAULT false,
    is_verified BOOLEAN NOT NULL DEFAULT false,
    metadata JSONB DEFAULT '{}',

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les requêtes fréquentes
CREATE INDEX idx_payment_methods_business_id ON payment_methods(business_id);
CREATE INDEX idx_payment_methods_type ON payment_methods(type);
CREATE INDEX idx_payment_methods_default ON payment_methods(business_id, is_default);

-- =====================================================
-- 4. TABLE DES INTENTIONS DE PAIEMENT
-- =====================================================
CREATE TABLE payment_intents (
    id VARCHAR(50) PRIMARY KEY,
    business_id UUID NOT NULL,
    plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(id),
    payment_method_id VARCHAR(50) REFERENCES payment_methods(id),

    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'XOF',

    status VARCHAR(20) NOT NULL DEFAULT 'pending'
        CHECK (status IN ('pending', 'processing', 'succeeded', 'failed', 'canceled', 'requires_action')),

    -- Détails du traitement
    processor VARCHAR(50), -- stripe, paypal, orange_money, mtn_money, etc.
    processor_payment_id VARCHAR(100),
    processor_response JSONB,

    -- Métadonnées
    description TEXT,
    metadata JSONB DEFAULT '{}',

    -- Gestion des erreurs
    failure_code VARCHAR(50),
    failure_message TEXT,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Index pour les requêtes fréquentes
CREATE INDEX idx_payment_intents_business_id ON payment_intents(business_id);
CREATE INDEX idx_payment_intents_status ON payment_intents(status);
CREATE INDEX idx_payment_intents_created_at ON payment_intents(created_at);
CREATE INDEX idx_payment_intents_processor ON payment_intents(processor);

-- =====================================================
-- 5. TABLE DES REÇUS DE PAIEMENT
-- =====================================================
CREATE TABLE payment_receipts (
    id VARCHAR(50) PRIMARY KEY,
    payment_intent_id VARCHAR(50) NOT NULL REFERENCES payment_intents(id),
    business_id UUID NOT NULL,
    plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(id),

    -- Informations de facturation
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    plan_name VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'XOF',

    -- Détails du paiement
    payment_method JSONB NOT NULL, -- Copie des détails de la méthode de paiement
    transaction_id VARCHAR(100) NOT NULL,

    status VARCHAR(20) NOT NULL DEFAULT 'paid'
        CHECK (status IN ('paid', 'refunded', 'partially_refunded', 'disputed')),

    -- Informations business (snapshot au moment du paiement)
    business_info JSONB NOT NULL,

    -- URLs et fichiers
    receipt_url TEXT,
    pdf_path TEXT,

    -- Gestion des remboursements
    refunded_amount DECIMAL(10,2) DEFAULT 0.00,
    refunded_at TIMESTAMP WITH TIME ZONE,
    refund_reason TEXT,

    -- Timestamps
    paid_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les requêtes fréquentes
CREATE INDEX idx_payment_receipts_business_id ON payment_receipts(business_id);
CREATE INDEX idx_payment_receipts_invoice_number ON payment_receipts(invoice_number);
CREATE INDEX idx_payment_receipts_payment_intent ON payment_receipts(payment_intent_id);
CREATE INDEX idx_payment_receipts_paid_at ON payment_receipts(paid_at);
CREATE INDEX idx_payment_receipts_status ON payment_receipts(status);

-- =====================================================
-- 6. TABLE DES ÉVÉNEMENTS DE PAIEMENT (AUDIT TRAIL)
-- =====================================================
CREATE TABLE payment_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_intent_id VARCHAR(50) NOT NULL REFERENCES payment_intents(id),
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB NOT NULL DEFAULT '{}',

    -- Métadonnées
    source VARCHAR(50), -- api, webhook, manual, system
    user_id UUID,
    ip_address INET,
    user_agent TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour l'audit trail
CREATE INDEX idx_payment_events_payment_intent ON payment_events(payment_intent_id);
CREATE INDEX idx_payment_events_type ON payment_events(event_type);
CREATE INDEX idx_payment_events_created_at ON payment_events(created_at);

-- =====================================================
-- 7. TABLE DES WEBHOOKS DE PAIEMENT
-- =====================================================
CREATE TABLE payment_webhooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    provider VARCHAR(50) NOT NULL,
    event_id VARCHAR(100) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    payment_intent_id VARCHAR(50) REFERENCES payment_intents(id),

    -- Données du webhook
    raw_data JSONB NOT NULL,
    processed BOOLEAN NOT NULL DEFAULT false,
    processed_at TIMESTAMP WITH TIME ZONE,

    -- Gestion des erreurs
    error_count INTEGER NOT NULL DEFAULT 0,
    last_error TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les webhooks
CREATE INDEX idx_payment_webhooks_provider ON payment_webhooks(provider);
CREATE INDEX idx_payment_webhooks_event_id ON payment_webhooks(provider, event_id);
CREATE INDEX idx_payment_webhooks_processed ON payment_webhooks(processed);
CREATE INDEX idx_payment_webhooks_payment_intent ON payment_webhooks(payment_intent_id);

-- =====================================================
-- 8. TABLE DES COUPONS ET RÉDUCTIONS
-- =====================================================
CREATE TABLE subscription_coupons (
    id VARCHAR(50) PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,

    -- Type de réduction
    discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount')),
    discount_value DECIMAL(10,2) NOT NULL,

    -- Restrictions
    min_amount DECIMAL(10,2),
    max_discount DECIMAL(10,2),
    applicable_plans TEXT[], -- Array des plan_ids applicables

    -- Limites d'utilisation
    usage_limit INTEGER,
    usage_count INTEGER NOT NULL DEFAULT 0,
    usage_limit_per_customer INTEGER,

    -- Validité
    valid_from TIMESTAMP WITH TIME ZONE,
    valid_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN NOT NULL DEFAULT true,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les coupons
CREATE INDEX idx_subscription_coupons_code ON subscription_coupons(code);
CREATE INDEX idx_subscription_coupons_active ON subscription_coupons(is_active);
CREATE INDEX idx_subscription_coupons_valid ON subscription_coupons(valid_from, valid_until);

-- =====================================================
-- 9. TABLE D'UTILISATION DES COUPONS
-- =====================================================
CREATE TABLE coupon_usages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    coupon_id VARCHAR(50) NOT NULL REFERENCES subscription_coupons(id),
    business_id UUID NOT NULL,
    payment_intent_id VARCHAR(50) NOT NULL REFERENCES payment_intents(id),

    discount_amount DECIMAL(10,2) NOT NULL,
    original_amount DECIMAL(10,2) NOT NULL,
    final_amount DECIMAL(10,2) NOT NULL,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour l'utilisation des coupons
CREATE INDEX idx_coupon_usages_coupon_id ON coupon_usages(coupon_id);
CREATE INDEX idx_coupon_usages_business_id ON coupon_usages(business_id);
CREATE INDEX idx_coupon_usages_payment_intent ON coupon_usages(payment_intent_id);

-- =====================================================
-- 10. FONCTIONS ET TRIGGERS
-- =====================================================

-- Fonction pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour updated_at
CREATE TRIGGER update_subscription_plans_updated_at
    BEFORE UPDATE ON subscription_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_business_subscriptions_updated_at
    BEFORE UPDATE ON business_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_methods_updated_at
    BEFORE UPDATE ON payment_methods
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_intents_updated_at
    BEFORE UPDATE ON payment_intents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_receipts_updated_at
    BEFORE UPDATE ON payment_receipts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscription_coupons_updated_at
    BEFORE UPDATE ON subscription_coupons
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Fonction pour générer un numéro de facture unique
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
    year_part TEXT;
    sequence_part TEXT;
    invoice_number TEXT;
BEGIN
    year_part := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;

    -- Générer un numéro séquentiel basé sur le timestamp
    sequence_part := LPAD((EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000)::BIGINT::TEXT, 10, '0');
    sequence_part := RIGHT(sequence_part, 6);

    invoice_number := 'INV-' || year_part || '-' || sequence_part;

    -- Vérifier l'unicité et incrémenter si nécessaire
    WHILE EXISTS (SELECT 1 FROM payment_receipts WHERE invoice_number = invoice_number) LOOP
        sequence_part := LPAD((sequence_part::INTEGER + 1)::TEXT, 6, '0');
        invoice_number := 'INV-' || year_part || '-' || sequence_part;
    END LOOP;

    RETURN invoice_number;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour vérifier si un business peut utiliser un coupon
CREATE OR REPLACE FUNCTION can_use_coupon(
    p_coupon_code TEXT,
    p_business_id UUID,
    p_plan_id TEXT,
    p_amount DECIMAL
)
RETURNS BOOLEAN AS $$
DECLARE
    coupon_record subscription_coupons%ROWTYPE;
    usage_count_for_customer INTEGER;
BEGIN
    -- Récupérer le coupon
    SELECT * INTO coupon_record
    FROM subscription_coupons
    WHERE code = p_coupon_code AND is_active = true;

    -- Vérifier si le coupon existe
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- Vérifier la validité temporelle
    IF coupon_record.valid_from IS NOT NULL AND CURRENT_TIMESTAMP < coupon_record.valid_from THEN
        RETURN FALSE;
    END IF;

    IF coupon_record.valid_until IS NOT NULL AND CURRENT_TIMESTAMP > coupon_record.valid_until THEN
        RETURN FALSE;
    END IF;

    -- Vérifier la limite d'utilisation globale
    IF coupon_record.usage_limit IS NOT NULL AND coupon_record.usage_count >= coupon_record.usage_limit THEN
        RETURN FALSE;
    END IF;

    -- Vérifier la limite d'utilisation par client
    IF coupon_record.usage_limit_per_customer IS NOT NULL THEN
        SELECT COUNT(*) INTO usage_count_for_customer
        FROM coupon_usages
        WHERE coupon_id = coupon_record.id AND business_id = p_business_id;

        IF usage_count_for_customer >= coupon_record.usage_limit_per_customer THEN
            RETURN FALSE;
        END IF;
    END IF;

    -- Vérifier le montant minimum
    IF coupon_record.min_amount IS NOT NULL AND p_amount < coupon_record.min_amount THEN
        RETURN FALSE;
    END IF;

    -- Vérifier les plans applicables
    IF coupon_record.applicable_plans IS NOT NULL AND NOT (p_plan_id = ANY(coupon_record.applicable_plans)) THEN
        RETURN FALSE;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour calculer la réduction d'un coupon
CREATE OR REPLACE FUNCTION calculate_coupon_discount(
    p_coupon_code TEXT,
    p_amount DECIMAL
)
RETURNS DECIMAL AS $$
DECLARE
    coupon_record subscription_coupons%ROWTYPE;
    discount_amount DECIMAL;
BEGIN
    -- Récupérer le coupon
    SELECT * INTO coupon_record
    FROM subscription_coupons
    WHERE code = p_coupon_code AND is_active = true;

    IF NOT FOUND THEN
        RETURN 0;
    END IF;

    -- Calculer la réduction
    IF coupon_record.discount_type = 'percentage' THEN
        discount_amount := p_amount * (coupon_record.discount_value / 100);
    ELSE
        discount_amount := coupon_record.discount_value;
    END IF;

    -- Appliquer la limite maximale de réduction
    IF coupon_record.max_discount IS NOT NULL AND discount_amount > coupon_record.max_discount THEN
        discount_amount := coupon_record.max_discount;
    END IF;

    -- S'assurer que la réduction ne dépasse pas le montant
    IF discount_amount > p_amount THEN
        discount_amount := p_amount;
    END IF;

    RETURN discount_amount;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 11. PROCÉDURES STOCKÉES
-- =====================================================

-- Procédure pour créer un essai gratuit
CREATE OR REPLACE FUNCTION create_free_trial(
    p_business_id UUID
)
RETURNS TABLE(
    subscription_id UUID,
    plan_id TEXT,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    success BOOLEAN,
    message TEXT
) AS $$
DECLARE
    v_subscription_id UUID;
    v_start_date TIMESTAMP WITH TIME ZONE;
    v_end_date TIMESTAMP WITH TIME ZONE;
    v_trial_exists BOOLEAN;
BEGIN
    -- Vérifier si l'entreprise a déjà utilisé son essai gratuit
    SELECT EXISTS(
        SELECT 1 FROM business_subscriptions 
        WHERE business_id = p_business_id AND trial_used = true
    ) INTO v_trial_exists;
    
    IF v_trial_exists THEN
        RETURN QUERY SELECT 
            NULL::UUID, 
            NULL::TEXT, 
            NULL::TIMESTAMP WITH TIME ZONE, 
            NULL::TIMESTAMP WITH TIME ZONE,
            false, 
            'Essai gratuit déjà utilisé pour cette entreprise'::TEXT;
        RETURN;
    END IF;
    
    -- Désactiver tout abonnement actuel
    UPDATE business_subscriptions 
    SET is_active = false, updated_at = CURRENT_TIMESTAMP
    WHERE business_id = p_business_id AND is_active = true;
    
    -- Créer le nouvel abonnement d'essai
    v_start_date := CURRENT_TIMESTAMP;
    v_end_date := v_start_date + INTERVAL '7 days';
    
    INSERT INTO business_subscriptions (
        business_id, plan_id, start_date, end_date, 
        is_active, is_trial, trial_used, auto_renew,
        payment_status, payment_method, stripe_subscription_id
    ) VALUES (
        p_business_id, 'free-trial', v_start_date, v_end_date,
        true, true, true, false,
        'paid', NULL, NULL
    ) RETURNING id INTO v_subscription_id;
    
    -- Enregistrer l'événement
    INSERT INTO payment_events (payment_intent_id, event_type, event_data, source)
    VALUES (
        'trial_' || v_subscription_id::TEXT,
        'trial_created',
        jsonb_build_object(
            'business_id', p_business_id,
            'subscription_id', v_subscription_id,
            'plan_id', 'free-trial'
        ),
        'system'
    );
    
    RETURN QUERY SELECT 
        v_subscription_id, 
        'free-trial'::TEXT, 
        v_start_date, 
        v_end_date,
        true, 
        'Essai gratuit créé avec succès'::TEXT;
END;
$$ LANGUAGE plpgsql;

-- Procédure pour traiter un paiement
CREATE OR REPLACE FUNCTION process_payment(
    p_payment_intent_id TEXT,
    p_processor_payment_id TEXT DEFAULT NULL,
    p_processor_response JSONB DEFAULT NULL
)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT,
    receipt_id TEXT
) AS $$
DECLARE
    v_payment_intent payment_intents%ROWTYPE;
    v_plan subscription_plans%ROWTYPE;
    v_receipt_id TEXT;
    v_invoice_number TEXT;
    v_subscription_id UUID;
    v_business_info JSONB;
BEGIN
    -- Récupérer l'intention de paiement
    SELECT * INTO v_payment_intent 
    FROM payment_intents 
    WHERE id = p_payment_intent_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'Intention de paiement non trouvée'::TEXT, NULL::TEXT;
        RETURN;
    END IF;
    
    IF v_payment_intent.status != 'pending' THEN
        RETURN QUERY SELECT false, 'Intention de paiement déjà traitée'::TEXT, NULL::TEXT;
        RETURN;
    END IF;
    
    -- Récupérer le plan
    SELECT * INTO v_plan 
    FROM subscription_plans 
    WHERE id = v_payment_intent.plan_id;
    
    -- Mettre à jour l'intention de paiement
    UPDATE payment_intents 
    SET 
        status = 'succeeded',
        processor_payment_id = p_processor_payment_id,
        processor_response = p_processor_response,
        processed_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_payment_intent_id;
    
    -- Créer l'abonnement
    INSERT INTO business_subscriptions (
        business_id, plan_id, 
        start_date, end_date,
        is_active, is_trial, auto_renew,
        payment_status, payment_method, stripe_subscription_id
    ) VALUES (
        v_payment_intent.business_id, 
        v_payment_intent.plan_id,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP + (v_plan.duration_days || ' days')::INTERVAL,
        true, false, true,
        'paid', v_payment_intent.payment_method_id, p_processor_payment_id
    ) RETURNING id INTO v_subscription_id;
    
    -- Récupérer les informations business pour le reçu
    SELECT jsonb_build_object(
        'name', COALESCE(bp.business_name, 'Entreprise'),
        'email', COALESCE(bp.email, u.email, ''),
        'address', bp.address,
        'tax_id', bp.tax_id
    ) INTO v_business_info
    FROM users u
    LEFT JOIN business_profiles bp ON u.id = bp.id
    WHERE u.id = v_payment_intent.business_id;
    
    -- Générer le reçu
    v_receipt_id := 'receipt_' || EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::BIGINT || '_' || 
                    substring(md5(random()::text), 1, 8);
    v_invoice_number := generate_invoice_number();
    
    INSERT INTO payment_receipts (
        id, payment_intent_id, business_id, plan_id,
        invoice_number, plan_name, amount, currency,
        payment_method, transaction_id, status,
        business_info, paid_at
    ) VALUES (
        v_receipt_id,
        p_payment_intent_id,
        v_payment_intent.business_id,
        v_payment_intent.plan_id,
        v_invoice_number,
        v_plan.name,
        v_payment_intent.amount,
        v_payment_intent.currency,
        (SELECT to_jsonb(pm.*) FROM payment_methods pm WHERE pm.id = v_payment_intent.payment_method_id),
        COALESCE(p_processor_payment_id, 'txn_' || EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::BIGINT),
        'paid',
        v_business_info,
        CURRENT_TIMESTAMP
    );
    
    -- Enregistrer l'événement
    INSERT INTO payment_events (payment_intent_id, event_type, event_data, source)
    VALUES (
        p_payment_intent_id,
        'payment_succeeded',
        jsonb_build_object(
            'receipt_id', v_receipt_id,
            'subscription_id', v_subscription_id,
            'amount', v_payment_intent.amount,
            'currency', v_payment_intent.currency
        ),
        'system'
    );
    
    RETURN QUERY SELECT true, 'Paiement traité avec succès'::TEXT, v_receipt_id;
END;
$$ LANGUAGE plpgsql;

-- Procédure pour échec de paiement
CREATE OR REPLACE FUNCTION fail_payment(
    p_payment_intent_id TEXT,
    p_failure_code TEXT,
    p_failure_message TEXT,
    p_processor_response JSONB DEFAULT NULL
)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT
) AS $$
BEGIN
    -- Mettre à jour l'intention de paiement
    UPDATE payment_intents 
    SET 
        status = 'failed',
        failure_code = p_failure_code,
        failure_message = p_failure_message,
        processor_response = p_processor_response,
        processed_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_payment_intent_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'Intention de paiement non trouvée'::TEXT;
        RETURN;
    END IF;
    
    -- Enregistrer l'événement
    INSERT INTO payment_events (payment_intent_id, event_type, event_data, source)
    VALUES (
        p_payment_intent_id,
        'payment_failed',
        jsonb_build_object(
            'failure_code', p_failure_code,
            'failure_message', p_failure_message
        ),
        'system'
    );
    
    RETURN QUERY SELECT true, 'Échec de paiement enregistré'::TEXT;
END;
$$ LANGUAGE plpgsql;

-- Procédure pour annuler un abonnement
CREATE OR REPLACE FUNCTION cancel_subscription(
    p_business_id UUID,
    p_reason TEXT DEFAULT NULL,
    p_immediate BOOLEAN DEFAULT false
)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT,
    cancelled_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    v_subscription business_subscriptions%ROWTYPE;
    v_cancelled_at TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Récupérer l'abonnement actif
    SELECT * INTO v_subscription
    FROM business_subscriptions
    WHERE business_id = p_business_id AND is_active = true
    ORDER BY created_at DESC
    LIMIT 1;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'Aucun abonnement actif trouvé'::TEXT, NULL::TIMESTAMP WITH TIME ZONE;
        RETURN;
    END IF;
    
    -- Déterminer la date d'annulation
    IF p_immediate THEN
        v_cancelled_at := CURRENT_TIMESTAMP;
        -- Annulation immédiate
        UPDATE business_subscriptions
        SET 
            is_active = false,
            auto_renew = false,
            cancelled_at = v_cancelled_at,
            cancellation_reason = p_reason,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = v_subscription.id;
    ELSE
        v_cancelled_at := v_subscription.end_date;
        -- Annulation à la fin de la période
        UPDATE business_subscriptions
        SET 
            auto_renew = false,
            cancelled_at = v_cancelled_at,
            cancellation_reason = p_reason,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = v_subscription.id;
    END IF;
    
    -- Enregistrer l'événement
    INSERT INTO payment_events (payment_intent_id, event_type, event_data, source)
    VALUES (
        'cancel_' || v_subscription.id::TEXT,
        'subscription_cancelled',
        jsonb_build_object(
            'subscription_id', v_subscription.id,
            'business_id', p_business_id,
            'reason', p_reason,
            'immediate', p_immediate
        ),
        'system'
    );
    
    RETURN QUERY SELECT 
        true, 
        CASE 
            WHEN p_immediate THEN 'Abonnement annulé immédiatement'
            ELSE 'Abonnement sera annulé à la fin de la période'
        END::TEXT,
        v_cancelled_at;
END;
$$ LANGUAGE plpgsql;

-- Procédure pour appliquer un coupon
CREATE OR REPLACE FUNCTION apply_coupon(
    p_payment_intent_id TEXT,
    p_coupon_code TEXT
)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT,
    discount_amount DECIMAL,
    final_amount DECIMAL
) AS $$
DECLARE
    v_payment_intent payment_intents%ROWTYPE;
    v_coupon subscription_coupons%ROWTYPE;
    v_discount_amount DECIMAL;
    v_final_amount DECIMAL;
    v_can_use BOOLEAN;
BEGIN
    -- Récupérer l'intention de paiement
    SELECT * INTO v_payment_intent 
    FROM payment_intents 
    WHERE id = p_payment_intent_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'Intention de paiement non trouvée'::TEXT, 0::DECIMAL, 0::DECIMAL;
        RETURN;
    END IF;
    
    -- Vérifier si le coupon peut être utilisé
    SELECT can_use_coupon(p_coupon_code, v_payment_intent.business_id, v_payment_intent.plan_id, v_payment_intent.amount)
    INTO v_can_use;
    
    IF NOT v_can_use THEN
        RETURN QUERY SELECT false, 'Coupon non valide ou non applicable'::TEXT, 0::DECIMAL, v_payment_intent.amount;
        RETURN;
    END IF;
    
    -- Récupérer le coupon
    SELECT * INTO v_coupon 
    FROM subscription_coupons 
    WHERE code = p_coupon_code AND is_active = true;
    
    -- Calculer la réduction
    SELECT calculate_coupon_discount(p_coupon_code, v_payment_intent.amount) INTO v_discount_amount;
    v_final_amount := v_payment_intent.amount - v_discount_amount;
    
    -- Mettre à jour l'intention de paiement
    UPDATE payment_intents
    SET 
        amount = v_final_amount,
        metadata = COALESCE(metadata, '{}'::jsonb) || jsonb_build_object(
            'coupon_code', p_coupon_code,
            'original_amount', v_payment_intent.amount,
            'discount_amount', v_discount_amount
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_payment_intent_id;
    
    -- Enregistrer l'utilisation du coupon
    INSERT INTO coupon_usages (
        coupon_id, business_id, payment_intent_id,
        discount_amount, original_amount, final_amount
    ) VALUES (
        v_coupon.id, v_payment_intent.business_id, p_payment_intent_id,
        v_discount_amount, v_payment_intent.amount, v_final_amount
    );
    
    -- Mettre à jour le compteur d'utilisation du coupon
    UPDATE subscription_coupons
    SET usage_count = usage_count + 1
    WHERE id = v_coupon.id;
    
    RETURN QUERY SELECT 
        true, 
        'Coupon appliqué avec succès'::TEXT, 
        v_discount_amount, 
        v_final_amount;
END;
$$ LANGUAGE plpgsql;

-- Procédure pour nettoyer les abonnements expirés
CREATE OR REPLACE FUNCTION cleanup_expired_subscriptions()
RETURNS TABLE(
    expired_count INTEGER,
    message TEXT
) AS $$
DECLARE
    v_expired_count INTEGER;
BEGIN
    -- Désactiver les abonnements expirés
    UPDATE business_subscriptions
    SET 
        is_active = false,
        updated_at = CURRENT_TIMESTAMP
    WHERE 
        is_active = true 
        AND end_date < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS v_expired_count = ROW_COUNT;
    
    -- Enregistrer l'événement de nettoyage
    IF v_expired_count > 0 THEN
        INSERT INTO payment_events (payment_intent_id, event_type, event_data, source)
        VALUES (
            'cleanup_' || EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::BIGINT,
            'subscriptions_expired',
            jsonb_build_object('expired_count', v_expired_count),
            'system'
        );
    END IF;
    
    RETURN QUERY SELECT 
        v_expired_count, 
        v_expired_count || ' abonnements expirés désactivés'::TEXT;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour obtenir le statut d'abonnement
CREATE OR REPLACE FUNCTION get_subscription_status(p_business_id UUID)
RETURNS TABLE(
    is_active BOOLEAN,
    plan_id TEXT,
    plan_name TEXT,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    days_remaining INTEGER,
    is_trial BOOLEAN,
    auto_renew BOOLEAN,
    features JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bs.is_active,
        bs.plan_id,
        sp.name as plan_name,
        bs.start_date,
        bs.end_date,
        GREATEST(0, EXTRACT(DAYS FROM (bs.end_date - CURRENT_TIMESTAMP))::INTEGER) as days_remaining,
        bs.is_trial,
        bs.auto_renew,
        sp.features
    FROM business_subscriptions bs
    JOIN subscription_plans sp ON bs.plan_id = sp.id
    WHERE bs.business_id = p_business_id
    AND bs.is_active = true
    ORDER BY bs.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 12. OPTIMISATIONS DE PERFORMANCE
-- =====================================================

-- Index composites pour performance
CREATE INDEX idx_business_subscriptions_business_active_end 
ON business_subscriptions(business_id, is_active, end_date);

CREATE INDEX idx_business_subscriptions_plan_active 
ON business_subscriptions(plan_id, is_active) 
WHERE is_active = true;

-- Index pour les intentions de paiement par business et statut
CREATE INDEX idx_payment_intents_business_status_created 
ON payment_intents(business_id, status, created_at);

-- Index pour les reçus par business et date
CREATE INDEX idx_payment_receipts_business_paid_at 
ON payment_receipts(business_id, paid_at DESC);

-- Index pour les événements de paiement par date
CREATE INDEX idx_payment_events_created_at_desc 
ON payment_events(created_at DESC);

-- Index pour les webhooks non traités
CREATE INDEX idx_payment_webhooks_unprocessed 
ON payment_webhooks(provider, processed, created_at) 
WHERE processed = false;

-- Index pour les méthodes de paiement par défaut
CREATE INDEX idx_payment_methods_business_default 
ON payment_methods(business_id) 
WHERE is_default = true;

-- Index pour les coupons actifs par code
CREATE INDEX idx_subscription_coupons_code_active 
ON subscription_coupons(code) 
WHERE is_active = true;

-- =====================================================
-- 13. CONTRAINTES AVANCÉES
-- =====================================================

-- Contrainte : Un seul abonnement actif par business
CREATE UNIQUE INDEX idx_unique_active_subscription 
ON business_subscriptions(business_id) 
WHERE is_active = true;

-- Contrainte : Une seule méthode de paiement par défaut par business
CREATE UNIQUE INDEX idx_unique_default_payment_method 
ON payment_methods(business_id) 
WHERE is_default = true;

-- Contrainte : Code de coupon unique
ALTER TABLE subscription_coupons 
ADD CONSTRAINT uk_subscription_coupons_code 
UNIQUE (code);

-- Contrainte : Numéro de facture unique
ALTER TABLE payment_receipts 
ADD CONSTRAINT uk_payment_receipts_invoice_number 
UNIQUE (invoice_number);

-- =====================================================
-- 14. CONTRAINTES DE VALIDATION
-- =====================================================

-- Validation des montants positifs
ALTER TABLE subscription_plans 
ADD CONSTRAINT chk_subscription_plans_price_positive 
CHECK (price >= 0);

ALTER TABLE payment_intents 
ADD CONSTRAINT chk_payment_intents_amount_positive 
CHECK (amount > 0);

ALTER TABLE payment_receipts 
ADD CONSTRAINT chk_payment_receipts_amount_positive 
CHECK (amount > 0);

-- Validation des dates d'abonnement
ALTER TABLE business_subscriptions 
ADD CONSTRAINT chk_business_subscriptions_dates 
CHECK (end_date > start_date);

-- Validation des coupons
ALTER TABLE subscription_coupons 
ADD CONSTRAINT chk_subscription_coupons_discount_positive 
CHECK (discount_value > 0);

ALTER TABLE subscription_coupons 
ADD CONSTRAINT chk_subscription_coupons_usage_limits 
CHECK (usage_limit IS NULL OR usage_limit > 0);

ALTER TABLE subscription_coupons 
ADD CONSTRAINT chk_subscription_coupons_dates 
CHECK (valid_until IS NULL OR valid_from IS NULL OR valid_until > valid_from);

-- =====================================================
-- 15. VUES UTILES
-- =====================================================

-- Vue pour les abonnements actifs avec détails du plan
CREATE VIEW active_subscriptions AS
SELECT
    bs.*,
    sp.name as plan_name,
    sp.price as plan_price,
    sp.currency as plan_currency,
    sp.duration_days,
    sp.features,
    (bs.end_date - CURRENT_TIMESTAMP) as time_remaining,
    EXTRACT(DAYS FROM (bs.end_date - CURRENT_TIMESTAMP)) as days_remaining
FROM business_subscriptions bs
JOIN subscription_plans sp ON bs.plan_id = sp.id
WHERE bs.is_active = true;

-- Vue pour les statistiques de paiement par business
CREATE VIEW business_payment_stats AS
SELECT
    pr.business_id,
    COUNT(*) as total_payments,
    SUM(pr.amount) as total_amount,
    AVG(pr.amount) as average_amount,
    MIN(pr.paid_at) as first_payment,
    MAX(pr.paid_at) as last_payment,
    COUNT(CASE WHEN pr.status = 'paid' THEN 1 END) as successful_payments,
    COUNT(CASE WHEN pr.status = 'refunded' THEN 1 END) as refunded_payments,
    SUM(CASE WHEN pr.status = 'refunded' THEN pr.amount ELSE 0 END) as total_refunded
FROM payment_receipts pr
GROUP BY pr.business_id;

-- Vue pour les revenus par plan
CREATE VIEW plan_revenue_stats AS
SELECT
    sp.id as plan_id,
    sp.name as plan_name,
    COUNT(pr.id) as total_subscriptions,
    SUM(pr.amount) as total_revenue,
    AVG(pr.amount) as average_revenue,
    COUNT(CASE WHEN pr.paid_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as subscriptions_last_30_days,
    SUM(CASE WHEN pr.paid_at >= CURRENT_DATE - INTERVAL '30 days' THEN pr.amount ELSE 0 END) as revenue_last_30_days
FROM subscription_plans sp
LEFT JOIN payment_receipts pr ON sp.id = pr.plan_id AND pr.status = 'paid'
GROUP BY sp.id, sp.name
ORDER BY total_revenue DESC;

-- =====================================================
-- 16. VUES MATÉRIALISÉES POUR PERFORMANCE
-- =====================================================

-- Vue matérialisée pour les statistiques de revenus mensuels
CREATE MATERIALIZED VIEW monthly_revenue_stats AS
SELECT 
    DATE_TRUNC('month', pr.paid_at) as month,
    COUNT(*) as transaction_count,
    SUM(pr.amount) as total_revenue,
    AVG(pr.amount) as average_transaction,
    COUNT(DISTINCT pr.business_id) as unique_customers,
    jsonb_object_agg(pr.plan_name, plan_stats.plan_revenue) as revenue_by_plan
FROM payment_receipts pr
JOIN (
    SELECT 
        DATE_TRUNC('month', paid_at) as month,
        plan_name,
        SUM(amount) as plan_revenue
    FROM payment_receipts
    WHERE status = 'paid'
    GROUP BY DATE_TRUNC('month', paid_at), plan_name
) plan_stats ON DATE_TRUNC('month', pr.paid_at) = plan_stats.month
WHERE pr.status = 'paid'
GROUP BY DATE_TRUNC('month', pr.paid_at)
ORDER BY month DESC;

-- Index sur la vue matérialisée
CREATE UNIQUE INDEX idx_monthly_revenue_stats_month 
ON monthly_revenue_stats(month);

-- Vue matérialisée pour les métriques d'abonnement
CREATE MATERIALIZED VIEW subscription_metrics AS
SELECT 
    sp.id as plan_id,
    sp.name as plan_name,
    sp.price,
    COUNT(bs.id) as total_subscriptions,
    COUNT(CASE WHEN bs.is_active THEN 1 END) as active_subscriptions,
    COUNT(CASE WHEN bs.is_trial THEN 1 END) as trial_subscriptions,
    AVG(EXTRACT(DAYS FROM (bs.end_date - bs.start_date))) as average_duration_days,
    COUNT(CASE WHEN bs.cancelled_at IS NOT NULL THEN 1 END) as cancelled_subscriptions,
    ROUND(
        COUNT(CASE WHEN bs.cancelled_at IS NOT NULL THEN 1 END)::DECIMAL / 
        NULLIF(COUNT(bs.id), 0) * 100, 2
    ) as churn_rate_percent
FROM subscription_plans sp
LEFT JOIN business_subscriptions bs ON sp.id = bs.plan_id
GROUP BY sp.id, sp.name, sp.price
ORDER BY sp.price;

-- Index sur la vue matérialisée
CREATE UNIQUE INDEX idx_subscription_metrics_plan_id 
ON subscription_metrics(plan_id);

-- =====================================================
-- 17. FONCTIONS D'OPTIMISATION ET MAINTENANCE
-- =====================================================

-- Fonction pour calculer les statistiques de revenus
CREATE OR REPLACE FUNCTION calculate_revenue_stats(
    p_start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    p_end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE(
    total_revenue DECIMAL,
    total_transactions INTEGER,
    average_transaction DECIMAL,
    revenue_by_plan JSONB,
    daily_revenue JSONB
) AS $$
DECLARE
    v_revenue_by_plan JSONB;
    v_daily_revenue JSONB;
BEGIN
    -- Calculer les revenus par plan
    SELECT jsonb_object_agg(plan_name, plan_revenue)
    INTO v_revenue_by_plan
    FROM (
        SELECT 
            pr.plan_name,
            SUM(pr.amount) as plan_revenue
        FROM payment_receipts pr
        WHERE pr.paid_at::DATE BETWEEN p_start_date AND p_end_date
        AND pr.status = 'paid'
        GROUP BY pr.plan_name
    ) plan_stats;
    
    -- Calculer les revenus quotidiens
    SELECT jsonb_object_agg(revenue_date, daily_amount)
    INTO v_daily_revenue
    FROM (
        SELECT 
            pr.paid_at::DATE as revenue_date,
            SUM(pr.amount) as daily_amount
        FROM payment_receipts pr
        WHERE pr.paid_at::DATE BETWEEN p_start_date AND p_end_date
        AND pr.status = 'paid'
        GROUP BY pr.paid_at::DATE
        ORDER BY pr.paid_at::DATE
    ) daily_stats;
    
    -- Retourner les statistiques globales
    RETURN QUERY
    SELECT 
        COALESCE(SUM(pr.amount), 0) as total_revenue,
        COUNT(*)::INTEGER as total_transactions,
        COALESCE(AVG(pr.amount), 0) as average_transaction,
        COALESCE(v_revenue_by_plan, '{}'::jsonb) as revenue_by_plan,
        COALESCE(v_daily_revenue, '{}'::jsonb) as daily_revenue
    FROM payment_receipts pr
    WHERE pr.paid_at::DATE BETWEEN p_start_date AND p_end_date
    AND pr.status = 'paid';
END;
$$ LANGUAGE plpgsql;

-- Fonction pour nettoyer les anciennes données
CREATE OR REPLACE FUNCTION cleanup_old_data(
    p_days_to_keep INTEGER DEFAULT 365
)
RETURNS TABLE(
    events_deleted INTEGER,
    webhooks_deleted INTEGER,
    message TEXT
) AS $$
DECLARE
    v_events_deleted INTEGER;
    v_webhooks_deleted INTEGER;
    v_cutoff_date TIMESTAMP WITH TIME ZONE;
BEGIN
    v_cutoff_date := CURRENT_TIMESTAMP - (p_days_to_keep || ' days')::INTERVAL;
    
    -- Supprimer les anciens événements (garder ceux liés aux paiements récents)
    DELETE FROM payment_events 
    WHERE created_at < v_cutoff_date
    AND payment_intent_id NOT IN (
        SELECT id FROM payment_intents 
        WHERE created_at >= v_cutoff_date
    );
    
    GET DIAGNOSTICS v_events_deleted = ROW_COUNT;
    
    -- Supprimer les anciens webhooks traités
    DELETE FROM payment_webhooks 
    WHERE created_at < v_cutoff_date
    AND processed = true;
    
    GET DIAGNOSTICS v_webhooks_deleted = ROW_COUNT;
    
    RETURN QUERY SELECT 
        v_events_deleted,
        v_webhooks_deleted,
        format('Nettoyage terminé: %s événements et %s webhooks supprimés', 
               v_events_deleted, v_webhooks_deleted);
END;
$$ LANGUAGE plpgsql;

-- Fonction pour rafraîchir toutes les vues matérialisées
CREATE OR REPLACE FUNCTION refresh_payment_stats()
RETURNS TEXT AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY monthly_revenue_stats;
    REFRESH MATERIALIZED VIEW CONCURRENTLY subscription_metrics;
    
    RETURN 'Vues matérialisées rafraîchies avec succès';
END;
$$ LANGUAGE plpgsql;

-- Fonction pour les tâches de maintenance quotidiennes
CREATE OR REPLACE FUNCTION daily_maintenance()
RETURNS TEXT AS $$
DECLARE
    v_expired_count INTEGER;
    v_cleanup_result TEXT;
BEGIN
    -- Nettoyer les abonnements expirés
    SELECT expired_count INTO v_expired_count
    FROM cleanup_expired_subscriptions();
    
    -- Rafraîchir les statistiques
    PERFORM refresh_payment_stats();
    
    -- Nettoyer les anciennes données (garder 2 ans)
    SELECT message INTO v_cleanup_result
    FROM cleanup_old_data(730);
    
    RETURN format('Maintenance terminée: %s abonnements expirés, %s', 
                  v_expired_count, v_cleanup_result);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 18. POLITIQUES DE SÉCURITÉ (RLS)
-- =====================================================

-- Activer RLS sur les tables sensibles
ALTER TABLE business_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_intents ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_receipts ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;

-- Politique pour les abonnements (les entreprises ne voient que leurs données)
CREATE POLICY business_subscriptions_policy ON business_subscriptions
    FOR ALL TO authenticated
    USING (business_id = auth.uid());

-- Politique pour les intentions de paiement
CREATE POLICY payment_intents_policy ON payment_intents
    FOR ALL TO authenticated
    USING (business_id = auth.uid());

-- Politique pour les reçus de paiement
CREATE POLICY payment_receipts_policy ON payment_receipts
    FOR ALL TO authenticated
    USING (business_id = auth.uid());

-- Politique pour les méthodes de paiement
CREATE POLICY payment_methods_policy ON payment_methods
    FOR ALL TO authenticated
    USING (business_id = auth.uid());

-- =====================================================
-- 19. DONNÉES INITIALES
-- =====================================================

-- Insertion des plans d'abonnement par défaut
INSERT INTO subscription_plans (id, name, description, price, duration_days, features, is_trial, max_reviews, has_ai_advisor, has_advanced_analytics, has_api_access, has_priority_support, has_phone_support, has_dedicated_manager) VALUES
('free-trial', 'Essai Gratuit', 'Découvrez toutes nos fonctionnalités pendant 7 jours', 0.00, 7,
 '["Accès complet à l''IA Conseiller", "Tableaux de bord interactifs", "Gestion des avis clients", "Notifications automatiques", "Support par email", "Jusqu''à 100 avis analysés"]'::jsonb,
 true, 100, true, false, false, false, false, false),

('starter', 'Starter', 'Parfait pour les petites entreprises qui démarrent', 15000.00, 30,
 '["Toutes les fonctionnalités de l''essai", "Jusqu''à 500 avis analysés/mois", "Recommandations IA illimitées", "Export des rapports PDF", "Support prioritaire", "Intégration API basique"]'::jsonb,
 false, 500, true, false, true, true, false, false),

('professional', 'Professional', 'Pour les entreprises en croissance', 35000.00, 30,
 '["Toutes les fonctionnalités Starter", "Avis illimités", "Analyses prédictives avancées", "Tableaux de bord personnalisés", "Support téléphonique 24/7", "Intégration API complète", "Formation personnalisée", "Gestionnaire de compte dédié"]'::jsonb,
 false, null, true, true, true, true, true, true),

('enterprise', 'Enterprise', 'Solution complète pour les grandes entreprises', 75000.00, 30,
 '["Toutes les fonctionnalités Professional", "Multi-comptes et équipes", "Analyses personnalisées", "SLA garanti 99.9%", "Support dédié premium", "Intégrations sur mesure", "Formations d''équipe", "Rapports exécutifs mensuels"]'::jsonb,
 false, null, true, true, true, true, true, true);

-- =====================================================
-- 20. COMMENTAIRES ET DOCUMENTATION
-- =====================================================

COMMENT ON TABLE subscription_plans IS 'Plans d''abonnement disponibles avec leurs fonctionnalités et tarifs';
COMMENT ON TABLE business_subscriptions IS 'Abonnements actifs et historiques des entreprises';
COMMENT ON TABLE payment_methods IS 'Méthodes de paiement enregistrées par les entreprises';
COMMENT ON TABLE payment_intents IS 'Intentions de paiement avec leur statut de traitement';
COMMENT ON TABLE payment_receipts IS 'Reçus de paiement générés après transactions réussies';
COMMENT ON TABLE payment_events IS 'Journal d''audit de tous les événements de paiement';
COMMENT ON TABLE payment_webhooks IS 'Webhooks reçus des processeurs de paiement';
COMMENT ON TABLE subscription_coupons IS 'Coupons de réduction pour les abonnements';
COMMENT ON TABLE coupon_usages IS 'Historique d''utilisation des coupons';

COMMENT ON FUNCTION calculate_revenue_stats IS 'Calcule les statistiques de revenus pour une période donnée';
COMMENT ON FUNCTION cleanup_old_data IS 'Nettoie les anciennes données pour optimiser les performances';
COMMENT ON FUNCTION refresh_payment_stats IS 'Rafraîchit toutes les vues matérialisées de statistiques';
COMMENT ON FUNCTION daily_maintenance IS 'Effectue les tâches de maintenance quotidiennes automatiques';

COMMENT ON MATERIALIZED VIEW monthly_revenue_stats IS 'Statistiques de revenus mensuels agrégées';
COMMENT ON MATERIALIZED VIEW subscription_metrics IS 'Métriques d''abonnement par plan';

-- =====================================================
-- FIN DU SCRIPT
-- =====================================================
