-- DÉ<PERSON>OIEMENT DES ABONNEMENTS ENTREPRISE CUSTOMEROOM
-- Ex<PERSON>cutez ce script dans Supabase SQL Editor

-- =====================================================
-- 1. CRÉER/METTRE À JOUR LES TABLES D'ABONNEMENT
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '💳 Configuration des abonnements entreprise...';
    
    -- Table des plans d'abonnement (mise à jour ou création)
    CREATE TABLE IF NOT EXISTS subscription_plans (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        currency VARCHAR(3) NOT NULL DEFAULT 'XOF',
        duration_days INTEGER NOT NULL DEFAULT 30,
        features JSONB NOT NULL DEFAULT '[]',
        is_active BOOLEAN NOT NULL DEFAULT true,
        is_trial BOOLEAN NOT NULL DEFAULT false,
        plan_type VARCHAR(20) NOT NULL DEFAULT 'monthly' CHECK (plan_type IN ('trial', 'monthly', 'yearly')),
        original_price DECIMAL(10,2),
        savings_text VARCHAR(100),
        is_popular BOOLEAN NOT NULL DEFAULT false,
        max_reviews INTEGER,
        max_products INTEGER,
        has_ai_advisor BOOLEAN NOT NULL DEFAULT false,
        has_advanced_analytics BOOLEAN NOT NULL DEFAULT false,
        has_api_access BOOLEAN NOT NULL DEFAULT false,
        has_priority_support BOOLEAN NOT NULL DEFAULT false,
        has_phone_support BOOLEAN NOT NULL DEFAULT false,
        has_dedicated_manager BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Table des abonnements business (mise à jour ou création)
    CREATE TABLE IF NOT EXISTS business_subscriptions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        business_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
        plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(id),
        start_date TIMESTAMP WITH TIME ZONE NOT NULL,
        end_date TIMESTAMP WITH TIME ZONE NOT NULL,
        is_active BOOLEAN NOT NULL DEFAULT true,
        is_trial BOOLEAN NOT NULL DEFAULT false,
        auto_renew BOOLEAN NOT NULL DEFAULT false,
        trial_used BOOLEAN NOT NULL DEFAULT false,
        cancelled_at TIMESTAMP WITH TIME ZONE,
        cancellation_reason TEXT,
        payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
        payment_method VARCHAR(50),
        stripe_subscription_id VARCHAR(100),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Index pour les performances
    CREATE INDEX IF NOT EXISTS idx_business_subscriptions_business_id ON business_subscriptions(business_id);
    CREATE INDEX IF NOT EXISTS idx_business_subscriptions_active ON business_subscriptions(is_active);
    CREATE INDEX IF NOT EXISTS idx_business_subscriptions_trial ON business_subscriptions(is_trial);
    CREATE INDEX IF NOT EXISTS idx_business_subscriptions_end_date ON business_subscriptions(end_date);

    -- Table des codes d'abonnement (nouvelle)
    CREATE TABLE IF NOT EXISTS subscription_codes (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        code VARCHAR(8) NOT NULL UNIQUE,
        business_id UUID NOT NULL,
        business_name VARCHAR(255) NOT NULL,
        plan_type VARCHAR(20) NOT NULL CHECK (plan_type IN ('trial', 'monthly', 'yearly')),
        amount INTEGER NOT NULL DEFAULT 0,
        status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'validated', 'rejected', 'expired')),
        generated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        validated_at TIMESTAMP WITH TIME ZONE,
        validated_by UUID,
        rejection_reason TEXT,
        admin_notes TEXT,
        payment_reference VARCHAR(100),
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

        -- Contraintes
        CONSTRAINT fk_subscription_codes_business FOREIGN KEY (business_id) REFERENCES profiles(id) ON DELETE CASCADE
    );

    -- Index pour optimiser les requêtes des codes
    CREATE INDEX IF NOT EXISTS idx_subscription_codes_business_id ON subscription_codes(business_id);
    CREATE INDEX IF NOT EXISTS idx_subscription_codes_status ON subscription_codes(status);
    CREATE INDEX IF NOT EXISTS idx_subscription_codes_expires_at ON subscription_codes(expires_at);
    CREATE INDEX IF NOT EXISTS idx_subscription_codes_code ON subscription_codes(code);
    CREATE INDEX IF NOT EXISTS idx_subscription_codes_generated_at ON subscription_codes(generated_at);

    RAISE NOTICE '✅ Tables d''abonnement et codes configurées';
    
END $$;

-- =====================================================
-- 2. INSÉRER/METTRE À JOUR LES PLANS D'ABONNEMENT
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📋 Configuration des plans d''abonnement...';

    -- Supprimer d'abord les abonnements qui référencent les anciens plans
    DELETE FROM business_subscriptions WHERE plan_id IN ('trial-7days', 'monthly-plan', 'yearly-plan');

    -- Puis supprimer les anciens plans s'ils existent
    DELETE FROM subscription_plans WHERE id IN ('trial-7days', 'monthly-plan', 'yearly-plan');
    
    -- Plan Essai Gratuit 7 jours
    INSERT INTO subscription_plans (
        id, name, description, price, currency, duration_days, 
        plan_type, is_trial, is_popular, features,
        has_ai_advisor, has_advanced_analytics, has_api_access, 
        has_priority_support, has_phone_support, has_dedicated_manager
    ) VALUES (
        'trial-7days',
        'Essai Gratuit',
        'Découvrez toutes nos fonctionnalités pendant 7 jours',
        0.00,
        'XOF',
        7,
        'trial',
        true,
        false,
        '[
            "Accès complet à toutes les fonctionnalités",
            "Gestion illimitée des avis clients", 
            "Tableaux de bord interactifs",
            "Notifications en temps réel",
            "Support par email",
            "Aucun engagement"
        ]'::jsonb,
        true, true, false, false, false, false
    );
    
    -- Plan Mensuel
    INSERT INTO subscription_plans (
        id, name, description, price, currency, duration_days,
        plan_type, is_trial, is_popular, features,
        has_ai_advisor, has_advanced_analytics, has_api_access,
        has_priority_support, has_phone_support, has_dedicated_manager
    ) VALUES (
        'monthly-plan',
        'Abonnement Mensuel', 
        'Plan mensuel flexible pour votre entreprise',
        25000.00,
        'XOF',
        30,
        'monthly',
        false,
        false,
        '[
            "Toutes les fonctionnalités incluses",
            "Gestion illimitée des avis",
            "Analytics avancées", 
            "Support prioritaire",
            "Intégrations API",
            "Sauvegarde automatique",
            "Rapports personnalisés"
        ]'::jsonb,
        true, true, true, true, false, false
    );
    
    -- Plan Annuel (avec réduction)
    INSERT INTO subscription_plans (
        id, name, description, price, original_price, currency, duration_days,
        plan_type, is_trial, is_popular, savings_text, features,
        has_ai_advisor, has_advanced_analytics, has_api_access,
        has_priority_support, has_phone_support, has_dedicated_manager
    ) VALUES (
        'yearly-plan',
        'Abonnement Annuel',
        'Économisez 20% avec notre plan annuel', 
        240000.00,
        300000.00,
        'XOF',
        365,
        'yearly',
        false,
        true,
        'Économisez 60 000 F CFA',
        '[
            "Toutes les fonctionnalités Premium",
            "Gestion illimitée des avis",
            "Analytics avancées + IA",
            "Support prioritaire 24/7", 
            "Intégrations API complètes",
            "Sauvegarde automatique",
            "Rapports personnalisés",
            "Conseiller dédié",
            "Formation personnalisée"
        ]'::jsonb,
        true, true, true, true, true, true
    );
    
    RAISE NOTICE '✅ Plans d''abonnement configurés';
    
END $$;

-- =====================================================
-- 3. CRÉER LES FONCTIONS DE GESTION DES ABONNEMENTS
-- =====================================================

-- Fonction pour vérifier si un utilisateur a déjà utilisé l'essai
CREATE OR REPLACE FUNCTION has_used_trial(p_business_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    trial_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO trial_count
    FROM business_subscriptions
    WHERE business_id = p_business_id AND is_trial = true;
    
    RETURN trial_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour obtenir l'abonnement actuel d'une entreprise
CREATE OR REPLACE FUNCTION get_current_subscription(p_business_id UUID)
RETURNS TABLE(
    subscription_id UUID,
    plan_id VARCHAR(50),
    plan_name VARCHAR(100),
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN,
    is_trial BOOLEAN,
    days_remaining INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bs.id,
        bs.plan_id,
        sp.name,
        bs.start_date,
        bs.end_date,
        bs.is_active,
        bs.is_trial,
        GREATEST(0, EXTRACT(DAY FROM (bs.end_date - CURRENT_TIMESTAMP))::INTEGER)
    FROM business_subscriptions bs
    JOIN subscription_plans sp ON bs.plan_id = sp.id
    WHERE bs.business_id = p_business_id 
    AND bs.is_active = true
    ORDER BY bs.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour créer un nouvel abonnement
CREATE OR REPLACE FUNCTION create_subscription(
    p_business_id UUID,
    p_plan_id VARCHAR(50),
    p_auto_renew BOOLEAN DEFAULT true
)
RETURNS UUID AS $$
DECLARE
    subscription_id UUID;
    plan_duration INTEGER;
    plan_is_trial BOOLEAN;
    start_date TIMESTAMP WITH TIME ZONE;
    end_date TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Récupérer les informations du plan
    SELECT duration_days, is_trial INTO plan_duration, plan_is_trial
    FROM subscription_plans
    WHERE id = p_plan_id AND is_active = true;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Plan d''abonnement non trouvé ou inactif: %', p_plan_id;
    END IF;
    
    -- Vérifier si l'essai a déjà été utilisé
    IF plan_is_trial AND has_used_trial(p_business_id) THEN
        RAISE EXCEPTION 'L''essai gratuit a déjà été utilisé pour cette entreprise';
    END IF;
    
    -- Désactiver l'abonnement actuel s'il existe
    UPDATE business_subscriptions 
    SET is_active = false, updated_at = CURRENT_TIMESTAMP
    WHERE business_id = p_business_id AND is_active = true;
    
    -- Calculer les dates
    start_date := CURRENT_TIMESTAMP;
    end_date := start_date + (plan_duration || ' days')::INTERVAL;
    
    -- Créer le nouvel abonnement
    INSERT INTO business_subscriptions (
        business_id, plan_id, start_date, end_date,
        is_active, is_trial, auto_renew,
        payment_status, payment_method, stripe_subscription_id
    ) VALUES (
        p_business_id, p_plan_id, start_date, end_date,
        true, plan_is_trial, CASE WHEN plan_is_trial THEN false ELSE p_auto_renew END,
        'paid', NULL, NULL
    ) RETURNING id INTO subscription_id;
    
    RETURN subscription_id;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour annuler un abonnement
CREATE OR REPLACE FUNCTION cancel_subscription(
    p_subscription_id UUID,
    p_reason TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE business_subscriptions
    SET
        is_active = false,
        cancelled_at = CURRENT_TIMESTAMP,
        cancellation_reason = p_reason,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_subscription_id AND is_active = true;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTIONS POUR LA GESTION DES CODES D'ABONNEMENT
-- =====================================================

-- Fonction pour générer un code d'abonnement unique
CREATE OR REPLACE FUNCTION generate_subscription_code()
RETURNS VARCHAR(8) AS $$
DECLARE
    new_code VARCHAR(8);
    code_exists BOOLEAN;
BEGIN
    LOOP
        -- Générer un code de 8 chiffres
        new_code := LPAD(FLOOR(RANDOM() * 100000000)::TEXT, 8, '0');

        -- Vérifier si le code existe déjà
        SELECT EXISTS(SELECT 1 FROM subscription_codes WHERE code = new_code) INTO code_exists;

        -- Si le code n'existe pas, on peut l'utiliser
        IF NOT code_exists THEN
            EXIT;
        END IF;
    END LOOP;

    RETURN new_code;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour créer un code d'abonnement
CREATE OR REPLACE FUNCTION create_subscription_code(
    p_business_id UUID,
    p_business_name VARCHAR(255),
    p_plan_type VARCHAR(20),
    p_amount INTEGER
)
RETURNS TABLE(
    code_id UUID,
    code VARCHAR(8),
    expires_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    new_code VARCHAR(8);
    expiration_time TIMESTAMP WITH TIME ZONE;
    code_id UUID;
BEGIN
    -- Générer un nouveau code unique
    new_code := generate_subscription_code();

    -- Définir l'expiration (24 heures)
    expiration_time := NOW() + INTERVAL '24 hours';

    -- Insérer le code dans la base de données
    INSERT INTO subscription_codes (
        business_id, business_name, plan_type, amount, code, expires_at
    ) VALUES (
        p_business_id, p_business_name, p_plan_type, p_amount, new_code, expiration_time
    ) RETURNING id INTO code_id;

    -- Retourner les informations du code créé
    RETURN QUERY SELECT code_id, new_code, expiration_time;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour valider un code d'abonnement
CREATE OR REPLACE FUNCTION validate_subscription_code(
    p_code_id UUID,
    p_admin_id UUID DEFAULT NULL,
    p_admin_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    code_record subscription_codes%ROWTYPE;
    subscription_id UUID;
BEGIN
    -- Récupérer les informations du code
    SELECT * INTO code_record FROM subscription_codes WHERE id = p_code_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Code d''abonnement non trouvé';
    END IF;

    -- Vérifier si le code est encore valide
    IF code_record.status != 'pending' THEN
        RAISE EXCEPTION 'Code déjà traité (statut: %)', code_record.status;
    END IF;

    -- Vérifier si le code n'est pas expiré
    IF code_record.expires_at < NOW() THEN
        -- Marquer le code comme expiré
        UPDATE subscription_codes
        SET status = 'expired', updated_at = NOW()
        WHERE id = p_code_id;

        RAISE EXCEPTION 'Code expiré';
    END IF;

    -- Marquer le code comme validé
    UPDATE subscription_codes
    SET
        status = 'validated',
        validated_at = NOW(),
        validated_by = p_admin_id,
        admin_notes = p_admin_notes,
        updated_at = NOW()
    WHERE id = p_code_id;

    -- Créer l'abonnement correspondant
    SELECT create_subscription(
        code_record.business_id,
        CASE
            WHEN code_record.plan_type = 'trial' THEN 'trial-7days'
            WHEN code_record.plan_type = 'monthly' THEN 'monthly-plan'
            WHEN code_record.plan_type = 'yearly' THEN 'yearly-plan'
        END,
        CASE WHEN code_record.plan_type != 'trial' THEN true ELSE false END
    ) INTO subscription_id;

    RETURN subscription_id IS NOT NULL;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour rejeter un code d'abonnement
CREATE OR REPLACE FUNCTION reject_subscription_code(
    p_code_id UUID,
    p_admin_id UUID DEFAULT NULL,
    p_rejection_reason TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE subscription_codes
    SET
        status = 'rejected',
        validated_by = p_admin_id,
        rejection_reason = p_rejection_reason,
        admin_notes = p_rejection_reason,
        updated_at = NOW()
    WHERE id = p_code_id AND status = 'pending';

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour nettoyer automatiquement les codes expirés
CREATE OR REPLACE FUNCTION cleanup_expired_subscription_codes()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER;
BEGIN
    UPDATE subscription_codes
    SET status = 'expired', updated_at = NOW()
    WHERE status = 'pending'
    AND expires_at < NOW();

    GET DIAGNOSTICS expired_count = ROW_COUNT;

    RETURN expired_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. CRÉER LES POLITIQUES DE SÉCURITÉ RLS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔒 Configuration des politiques de sécurité...';
    
    -- Activer RLS sur les tables
    ALTER TABLE business_subscriptions ENABLE ROW LEVEL SECURITY;
    ALTER TABLE subscription_codes ENABLE ROW LEVEL SECURITY;

    -- Politique pour que les entreprises ne voient que leurs abonnements
    DROP POLICY IF EXISTS "business_subscriptions_policy" ON business_subscriptions;
    CREATE POLICY "business_subscriptions_policy" ON business_subscriptions
        FOR ALL TO authenticated
        USING (business_id = auth.uid());

    -- Politique pour que les entreprises ne voient que leurs codes
    DROP POLICY IF EXISTS "subscription_codes_business_policy" ON subscription_codes;
    CREATE POLICY "subscription_codes_business_policy" ON subscription_codes
        FOR ALL TO authenticated
        USING (business_id = auth.uid());

    -- Politique pour les admins (accès complet aux abonnements)
    DROP POLICY IF EXISTS "admin_business_subscriptions_policy" ON business_subscriptions;
    CREATE POLICY "admin_business_subscriptions_policy" ON business_subscriptions
        FOR ALL TO authenticated
        USING (
            EXISTS (
                SELECT 1 FROM admin_profiles ap
                JOIN profiles p ON ap.user_id = p.id
                WHERE p.id = auth.uid()
                AND ap.is_active = true
            )
        );

    -- Politique pour les admins (accès complet aux codes)
    DROP POLICY IF EXISTS "admin_subscription_codes_policy" ON subscription_codes;
    CREATE POLICY "admin_subscription_codes_policy" ON subscription_codes
        FOR ALL TO authenticated
        USING (
            EXISTS (
                SELECT 1 FROM admin_profiles ap
                JOIN profiles p ON ap.user_id = p.id
                WHERE p.id = auth.uid()
                AND ap.is_active = true
            )
        );
    
    RAISE NOTICE '✅ Politiques de sécurité configurées';
    
END $$;

-- =====================================================
-- 5. CRÉER DES TRIGGERS POUR L'AUDIT
-- =====================================================

-- Fonction de trigger pour mettre à jour updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers pour updated_at
DROP TRIGGER IF EXISTS update_subscription_plans_updated_at ON subscription_plans;
CREATE TRIGGER update_subscription_plans_updated_at
    BEFORE UPDATE ON subscription_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_business_subscriptions_updated_at ON business_subscriptions;
CREATE TRIGGER update_business_subscriptions_updated_at
    BEFORE UPDATE ON business_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_subscription_codes_updated_at ON subscription_codes;
CREATE TRIGGER update_subscription_codes_updated_at
    BEFORE UPDATE ON subscription_codes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 6. CRÉER DES VUES UTILES
-- =====================================================

-- Vue pour les abonnements actifs avec détails du plan
CREATE OR REPLACE VIEW active_subscriptions AS
SELECT 
    bs.id,
    bs.business_id,
    p.username as business_username,
    bp.business_name,
    bs.plan_id,
    sp.name as plan_name,
    sp.price,
    sp.currency,
    bs.start_date,
    bs.end_date,
    bs.is_trial,
    bs.auto_renew,
    EXTRACT(DAY FROM (bs.end_date - CURRENT_TIMESTAMP))::INTEGER as days_remaining,
    CASE 
        WHEN bs.end_date < CURRENT_TIMESTAMP THEN 'expired'
        WHEN bs.end_date < CURRENT_TIMESTAMP + INTERVAL '7 days' THEN 'expiring_soon'
        ELSE 'active'
    END as status
FROM business_subscriptions bs
JOIN subscription_plans sp ON bs.plan_id = sp.id
JOIN profiles p ON bs.business_id = p.id
LEFT JOIN business_profiles bp ON bs.business_id = bp.id
WHERE bs.is_active = true;

-- Vue pour les statistiques d'abonnements
CREATE OR REPLACE VIEW subscription_stats AS
SELECT
    sp.id as plan_id,
    sp.name as plan_name,
    sp.plan_type,
    COUNT(bs.id) as total_subscriptions,
    COUNT(CASE WHEN bs.is_active THEN 1 END) as active_subscriptions,
    COUNT(CASE WHEN bs.is_trial THEN 1 END) as trial_subscriptions,
    SUM(CASE WHEN bs.is_active AND NOT bs.is_trial THEN sp.price ELSE 0 END) as monthly_revenue
FROM subscription_plans sp
LEFT JOIN business_subscriptions bs ON sp.id = bs.plan_id
GROUP BY sp.id, sp.name, sp.plan_type
ORDER BY sp.plan_type, sp.price;

-- Vue pour les statistiques des codes d'abonnement
CREATE OR REPLACE VIEW subscription_codes_stats AS
SELECT
    plan_type,
    COUNT(*) as total_codes,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_codes,
    COUNT(CASE WHEN status = 'validated' THEN 1 END) as validated_codes,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_codes,
    COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_codes,
    SUM(CASE WHEN status = 'validated' THEN amount ELSE 0 END) as validated_revenue,
    AVG(CASE WHEN status = 'validated' THEN EXTRACT(EPOCH FROM (validated_at - generated_at))/3600 ELSE NULL END) as avg_validation_hours
FROM subscription_codes
GROUP BY plan_type
ORDER BY plan_type;

-- Vue pour les codes d'abonnement avec détails business
CREATE OR REPLACE VIEW subscription_codes_detailed AS
SELECT
    sc.id,
    sc.code,
    sc.business_id,
    sc.business_name,
    p.username as business_username,
    bp.email as business_email,
    sc.plan_type,
    sc.amount,
    sc.status,
    sc.generated_at,
    sc.expires_at,
    sc.validated_at,
    sc.validated_by,
    sc.rejection_reason,
    sc.admin_notes,
    CASE
        WHEN sc.expires_at < NOW() AND sc.status = 'pending' THEN true
        ELSE false
    END as is_expired,
    CASE
        WHEN sc.status = 'pending' AND sc.expires_at > NOW() THEN
            EXTRACT(EPOCH FROM (sc.expires_at - NOW()))/3600
        ELSE NULL
    END as hours_until_expiration
FROM subscription_codes sc
LEFT JOIN profiles p ON sc.business_id = p.id
LEFT JOIN business_profiles bp ON sc.business_id = bp.id
ORDER BY sc.generated_at DESC;

-- =====================================================
-- 7. AFFICHER LES STATISTIQUES
-- =====================================================

-- Statistiques des plans
SELECT 
    '📊 PLANS D''ABONNEMENT' as info,
    id,
    name,
    price || ' ' || currency as prix,
    duration_days || ' jours' as duree,
    plan_type,
    CASE WHEN is_trial THEN 'Oui' ELSE 'Non' END as essai,
    CASE WHEN is_popular THEN 'Oui' ELSE 'Non' END as populaire
FROM subscription_plans 
ORDER BY 
    CASE plan_type 
        WHEN 'trial' THEN 1 
        WHEN 'monthly' THEN 2 
        WHEN 'yearly' THEN 3 
    END;

-- Statistiques des abonnements
SELECT * FROM subscription_stats;

-- =====================================================
-- 8. MESSAGE DE SUCCÈS FINAL
-- =====================================================

DO $$
DECLARE
    total_plans integer;
    total_subscriptions integer;
    total_codes integer;
BEGIN
    SELECT COUNT(*) INTO total_plans FROM subscription_plans;
    SELECT COUNT(*) INTO total_subscriptions FROM business_subscriptions;
    SELECT COUNT(*) INTO total_codes FROM subscription_codes;

    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '🎉 ABONNEMENTS ENTREPRISE COMPLÈTEMENT DÉPLOYÉS !';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '💳 CONFIGURATION CRÉÉE:';
    RAISE NOTICE '   📋 Plans d''abonnement: %', total_plans;
    RAISE NOTICE '   📊 Abonnements existants: %', total_subscriptions;
    RAISE NOTICE '   🔐 Codes d''abonnement: %', total_codes;
    RAISE NOTICE '';
    RAISE NOTICE '🎯 PLANS DISPONIBLES:';
    RAISE NOTICE '   🎁 Essai Gratuit - 7 jours - 0 F CFA';
    RAISE NOTICE '   📅 Mensuel - 30 jours - 25 000 F CFA';
    RAISE NOTICE '   👑 Annuel - 365 jours - 240 000 F CFA (Économie: 60 000 F CFA)';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 FONCTIONNALITÉS DISPONIBLES:';
    RAISE NOTICE '   ✅ Interface d''abonnement complète';
    RAISE NOTICE '   ✅ Gestion des essais gratuits';
    RAISE NOTICE '   ✅ Plans mensuel et annuel';
    RAISE NOTICE '   ✅ Système de renouvellement automatique';
    RAISE NOTICE '   ✅ Codes d''abonnement à 8 chiffres';
    RAISE NOTICE '   ✅ Validation administrative des codes';
    RAISE NOTICE '   ✅ Expiration automatique (24h)';
    RAISE NOTICE '   ✅ Politiques de sécurité RLS';
    RAISE NOTICE '   ✅ Fonctions de gestion avancées';
    RAISE NOTICE '   ✅ Vues statistiques complètes';
    RAISE NOTICE '';
    RAISE NOTICE '🔐 SYSTÈME DE CODES D''ABONNEMENT:';
    RAISE NOTICE '   1️⃣ Entreprise clique "Choisir ce plan"';
    RAISE NOTICE '   2️⃣ Code à 8 chiffres généré automatiquement';
    RAISE NOTICE '   3️⃣ Entreprise effectue le paiement';
    RAISE NOTICE '   4️⃣ Admin valide le code après vérification';
    RAISE NOTICE '   5️⃣ Abonnement activé automatiquement';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 PROCHAINES ÉTAPES:';
    RAISE NOTICE '1. Intégrez le composant BusinessSubscription';
    RAISE NOTICE '2. Testez la génération de codes';
    RAISE NOTICE '3. Testez la validation admin des codes';
    RAISE NOTICE '4. Configurez les notifications de paiement';
    RAISE NOTICE '5. Testez l''activation automatique des abonnements';
    RAISE NOTICE '';
    RAISE NOTICE '🎊 SYSTÈME D''ABONNEMENTS + CODES OPÉRATIONNEL !';
    RAISE NOTICE '';
END $$;
