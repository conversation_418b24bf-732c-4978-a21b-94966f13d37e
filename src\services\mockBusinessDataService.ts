/**
 * Service de génération de données de test pour la démonstration
 * Simule des vraies données business pour tester les fonctionnalités
 */
export class MockBusinessDataService {
  
  /**
   * Génère des données de test complètes pour une entreprise
   */
  static generateMockBusinessData(businessId: string) {
    const mockData = {
      posts: this.generateMockPosts(),
      products: this.generateMockProducts(),
      ratings: this.generateMockRatings(),
      stats: this.generateMockStats(),
      metadata: {
        totalDataPoints: 45, // Assez pour une bonne qualité
        lastUpdated: new Date().toISOString(),
        businessId,
        isMockData: true
      }
    };

    console.log('🎭 Données de test générées:', {
      posts: mockData.posts.length,
      products: mockData.products.length,
      ratings: mockData.ratings.length,
      totalDataPoints: mockData.metadata.totalDataPoints
    });

    return mockData;
  }

  /**
   * Génère des posts/avis de test
   */
  private static generateMockPosts() {
    return [
      {
        id: 'post_1',
        business_name: 'Dexima',
        product_name: 'Service de livraison',
        category: 'Logistique',
        description: 'Excellent service de livraison, très rapide et professionnel',
        rating: 5,
        type: 'coup_de_coeur',
        created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: 'user_1'
      },
      {
        id: 'post_2',
        business_name: 'Dexima',
        product_name: 'Support client',
        category: 'Service',
        description: 'Le support client pourrait être amélioré, temps de réponse trop long',
        rating: 2,
        type: 'coup_de_gueule',
        created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: 'user_2'
      },
      {
        id: 'post_3',
        business_name: 'Dexima',
        product_name: 'Produits alimentaires',
        category: 'Alimentation',
        description: 'Très bonne qualité des produits, je recommande vivement',
        rating: 4,
        type: 'coup_de_coeur',
        created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: 'user_3'
      },
      {
        id: 'post_4',
        business_name: 'Dexima',
        product_name: 'Application mobile',
        category: 'Technologie',
        description: 'L\'application est intuitive mais manque quelques fonctionnalités',
        rating: 3,
        type: 'coup_de_coeur',
        created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: 'user_4'
      },
      {
        id: 'post_5',
        business_name: 'Dexima',
        product_name: 'Prix des produits',
        category: 'Pricing',
        description: 'Les prix sont un peu élevés par rapport à la concurrence',
        rating: 3,
        type: 'coup_de_gueule',
        created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: 'user_5'
      }
    ];
  }

  /**
   * Génère des produits de test
   */
  private static generateMockProducts() {
    return [
      {
        id: 'prod_1',
        name: 'Pack Livraison Express',
        description: 'Service de livraison en moins de 2h',
        category: 'Logistique',
        price: 15000,
        business_id: 'dexima',
        average_rating: 4.5,
        quality_badge: 'gold',
        stock: 100,
        created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        marketcomments: [
          {
            id: 'comment_1',
            comment: 'Service très rapide, je recommande !',
            rating: 5,
            created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
          }
        ],
        marketrating: [
          { rating: 5, created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString() },
          { rating: 4, created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString() }
        ]
      },
      {
        id: 'prod_2',
        name: 'Consultation Business',
        description: 'Conseil en stratégie d\'entreprise',
        category: 'Conseil',
        price: 50000,
        business_id: 'dexima',
        average_rating: 4.2,
        quality_badge: 'silver',
        stock: 50,
        created_at: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
        marketcomments: [],
        marketrating: [
          { rating: 4, created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString() },
          { rating: 4, created_at: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString() }
        ]
      }
    ];
  }

  /**
   * Génère des ratings de test
   */
  private static generateMockRatings() {
    const ratings = [];
    
    // Générer 20 ratings aléatoires
    for (let i = 0; i < 20; i++) {
      ratings.push({
        rating: Math.floor(Math.random() * 5) + 1, // 1-5
        created_at: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000).toISOString(), // 60 derniers jours
        user_id: `user_${i + 1}`,
        category: ['Logistique', 'Service', 'Alimentation', 'Technologie', 'Pricing'][Math.floor(Math.random() * 5)]
      });
    }
    
    return ratings;
  }

  /**
   * Génère des statistiques de test
   */
  private static generateMockStats() {
    return {
      businessProfile: {
        id: 'dexima',
        business_name: 'Dexima',
        description: 'Entreprise de services numériques',
        sales_count: 156,
        wallet: 2500000
      },
      postsCount: 5,
      productsCount: 2,
      followersCount: 1250,
      salesCount: 156,
      wallet: 2500000
    };
  }

  /**
   * Évalue la qualité des données de test
   */
  static getMockDataQuality() {
    return {
      hasEnoughData: true,
      dataQuality: 'good' as const,
      recommendations: [],
      dataPoints: {
        posts: 5,
        products: 2,
        ratings: 20,
        total: 27
      }
    };
  }

  /**
   * Génère des recommandations de test basées sur les données simulées
   */
  static generateMockRecommendations() {
    return [
      {
        id: 'rec_1',
        title: 'Améliorer le Support Client',
        description: 'Vos clients mentionnent des délais de réponse trop longs. Réduisez le temps de réponse moyen.',
        priority: 'urgent',
        confidence: 85,
        impact: 'high',
        effort: 'medium',
        category: 'customer-service',
        actionPlan: [
          'Embaucher un agent support supplémentaire',
          'Mettre en place un système de tickets automatisé',
          'Former l\'équipe aux bonnes pratiques',
          'Définir des SLA clairs (réponse < 2h)'
        ],
        estimatedROI: '25-40%',
        timeframe: '2-4 semaines'
      },
      {
        id: 'rec_2',
        title: 'Optimiser la Stratégie Prix',
        description: 'Vos prix sont perçus comme élevés. Analysez la concurrence et ajustez votre positionnement.',
        priority: 'medium',
        confidence: 75,
        impact: 'medium',
        effort: 'low',
        category: 'pricing',
        actionPlan: [
          'Étude de marché concurrentielle',
          'Segmentation des prix par service',
          'Offres promotionnelles ciblées',
          'Communication sur la valeur ajoutée'
        ],
        estimatedROI: '15-25%',
        timeframe: '3-6 semaines'
      },
      {
        id: 'rec_3',
        title: 'Développer l\'Application Mobile',
        description: 'Les utilisateurs apprécient l\'app mais demandent plus de fonctionnalités.',
        priority: 'medium',
        confidence: 70,
        impact: 'medium',
        effort: 'high',
        category: 'product-development',
        actionPlan: [
          'Collecter les demandes de fonctionnalités',
          'Prioriser selon l\'impact utilisateur',
          'Développer par itérations',
          'Tester avec un groupe pilote'
        ],
        estimatedROI: '20-35%',
        timeframe: '8-12 semaines'
      }
    ];
  }

  /**
   * Génère des suggestions de nouveaux produits
   */
  static generateMockProductSuggestions() {
    return [
      {
        id: 'sug_1',
        title: 'Service de Livraison Nocturne',
        description: 'Étendre les heures de livraison pour inclure les créneaux nocturnes (20h-23h).',
        category: 'Logistique',
        confidence: 80,
        marketDemand: 'high',
        estimatedROI: '30-45%',
        developmentTime: '4-6 semaines',
        investmentRequired: 'Moyen',
        riskLevel: 'Faible',
        targetAudience: 'Professionnels travaillant tard',
        keyFeatures: [
          'Créneaux 20h-23h disponibles',
          'Tarif premium justifié',
          'Équipe dédiée aux livraisons nocturnes'
        ]
      },
      {
        id: 'sug_2',
        title: 'Programme de Fidélité Premium',
        description: 'Créer un programme de fidélité avec avantages exclusifs pour les gros clients.',
        category: 'Service',
        confidence: 75,
        marketDemand: 'medium',
        estimatedROI: '25-35%',
        developmentTime: '6-8 semaines',
        investmentRequired: 'Faible',
        riskLevel: 'Très Faible',
        targetAudience: 'Clients réguliers et entreprises',
        keyFeatures: [
          'Points de fidélité cumulables',
          'Réductions progressives',
          'Support prioritaire',
          'Accès anticipé aux nouveaux services'
        ]
      }
    ];
  }
}
