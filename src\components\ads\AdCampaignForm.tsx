import React, { useState, useEffect } from 'react';
import {
  X, Plus, Image, Target, Users, MapPin, Clock, Smartphone,
  Monitor, Tablet, Globe, Calendar, DollarSign, TrendingUp,
  Eye, MousePointer, Zap, Brain, Settings, ChevronDown,
  ChevronUp, Info, AlertCircle, CheckCircle, Upload,
  Palette, Type, Layout, BarChart3, Calculator, Wallet
} from 'lucide-react';
import Button from '../ui/Button';
import { AdCampaign } from '../../types';
import { supabase } from '../../lib/supabase';
import {
  validateAdImageFile,
  createAdImageURL,
  revokeAdImageURL,
  handleAdImageUpload,
  isBlobURL
} from '../../utils/imageUtils';
import { useAdBilling } from '../../context/AdBillingContext';
import AdInventoryManager from './AdInventoryManager';
import '../../styles/AdCampaignFormModern.css';
import '../../styles/AdInventoryManager.css';

interface AdFormData {
  title: string;
  description: string;
  imageUrl: string;
  targetAudience: string[];
  budget: string;
  dailyBudget: string;
  startDate: string;
  endDate: string;
  status: 'active' | 'paused' | 'draft';
  showInOffersAndPromotions: boolean;
  showInRightSidebar: boolean;
  showInNewsFeed: boolean;
  discount: string;
  selectedPlacements: string[];
  bidAmounts: Record<string, number>;
  targeting: {
    demographics: {
      ageRanges: string[];
      genders: string[];
      interests: string[];
    };
    location: {
      countries: string[];
      cities: string[];
      radius: number;
    };
    timing: {
      daysOfWeek: string[];
      timeOfDay: string[];
    };
    devices: string[];
    languages: string[];
  };
}

interface AdCampaignFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (campaign: AdCampaign) => void;
  campaign?: AdCampaign | null;
  isEditing?: boolean;
}

const AdCampaignForm: React.FC<AdCampaignFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  campaign = null,
  isEditing = false
}) => {
  const [formData, setFormData] = useState<AdFormData>({
    title: '',
    description: '',
    imageUrl: '',
    targetAudience: [],
    budget: '',
    dailyBudget: '',
    startDate: '',
    endDate: '',
    status: 'draft',
    showInOffersAndPromotions: false,
    showInRightSidebar: false,
    showInNewsFeed: false,
    discount: '',
    selectedPlacements: [],
    bidAmounts: {},
    targeting: {
      demographics: {
        ageRanges: [],
        genders: [],
        interests: []
      },
      location: {
        countries: ['Sénégal'],
        cities: [],
        radius: 0
      },
      timing: {
        daysOfWeek: ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'],
        timeOfDay: ['morning', 'afternoon', 'evening', 'night']
      },
      devices: ['Mobile', 'Desktop', 'Tablet'],
      languages: ['Français']
    }
  });

  const [imagePreview, setImagePreview] = useState<string>('');
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showAdvancedTargeting, setShowAdvancedTargeting] = useState<boolean>(false);
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const [budgetRecommendations, setBudgetRecommendations] = useState<any>(null);
  const [audienceSize, setAudienceSize] = useState<number>(0);
  const [estimatedReach, setEstimatedReach] = useState<any>(null);
  const [creativeSuggestions, setCreativeSuggestions] = useState<string[]>([]);
  const [showAIAssistant, setShowAIAssistant] = useState<boolean>(false);
  const [availableCities, setAvailableCities] = useState<string[]>([]);
  const [cityUserCounts, setCityUserCounts] = useState<Record<string, number>>({});
  const [ageRangeStats, setAgeRangeStats] = useState<Record<string, number>>({});
  const [genderStats, setGenderStats] = useState<Record<string, number>>({});

  // Hook de facturation
  const {
    wallet,
    canAffordCampaign,
    chargeCampaign,
    loading: billingLoading
  } = useAdBilling();

  // Initialiser le formulaire avec les données de la campagne si elle existe
  useEffect(() => {
    if (campaign) {
      setFormData({
        title: campaign.title,
        description: campaign.description,
        imageUrl: campaign.imageUrl,
        targetAudience: campaign.targetAudience,
        budget: campaign.budget.toString(),
        dailyBudget: campaign.dailyBudget.toString(),
        startDate: formatDateForInput(campaign.startDate),
        endDate: formatDateForInput(campaign.endDate),
        status: campaign.status as 'active' | 'paused' | 'draft',
        showInOffersAndPromotions: campaign.showInOffersAndPromotions,
        showInRightSidebar: campaign.showInRightSidebar,
        showInNewsFeed: campaign.showInNewsFeed,
        discount: campaign.discount ? campaign.discount.toString() : '',
        targeting: campaign.targeting
      });
      setImagePreview(campaign.imageUrl);
    } else {
      // Initialiser avec des valeurs par défaut pour une nouvelle campagne
      const today = new Date();
      const thirtyDaysLater = new Date(today);
      thirtyDaysLater.setDate(today.getDate() + 30);

      setFormData({
        ...formData,
        startDate: formatDateForInput(today),
        endDate: formatDateForInput(thirtyDaysLater)
      });
    }
  }, [campaign]);

  // Mettre à jour les recommandations de budget en temps réel
  useEffect(() => {
    if (formData.budget) {
      const budget = parseFloat(formData.budget);
      if (budget > 0) {
        setBudgetRecommendations(generateBudgetRecommendations(budget));
        setEstimatedReach(calculateEstimatedReach());
      }
    }
  }, [formData.budget, formData.targeting]);

  // Mettre à jour les suggestions créatives
  useEffect(() => {
    if (formData.title || formData.description) {
      setCreativeSuggestions(generateCreativeSuggestions(formData.title, formData.description));
    }
  }, [formData.title, formData.description]);

  // Calculer la taille de l'audience
  useEffect(() => {
    setAudienceSize(calculateAudienceSize());
  }, [formData.targeting]);

  // Charger les villes et statistiques des utilisateurs au montage
  useEffect(() => {
    fetchUserCities();
    fetchAgeStatistics();
    fetchGenderStatistics();
  }, []);

  // Calculer les recommandations de budget quand le budget change
  useEffect(() => {
    const recommendations = calculateBudgetRecommendations();
    setBudgetRecommendations(recommendations);
  }, [formData.budget, formData.dailyBudget, audienceSize]);

  // Calculer la portée estimée quand le budget change
  useEffect(() => {
    if (formData.budget) {
      const reach = calculateEstimatedReach();
      setEstimatedReach(reach);
    }
  }, [formData.budget, audienceSize]);

  // Nettoyer les URLs blob au démontage du composant
  useEffect(() => {
    return () => {
      if (imagePreview && isBlobURL(imagePreview)) {
        revokeAdImageURL(imagePreview);
      }
    };
  }, [imagePreview]);

  // Fonction pour formater la date pour les champs input date
  const formatDateForInput = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Calculer la taille de l'audience estimée
  const calculateAudienceSize = () => {
    let baseSize = 75000; // Base pour la Côte d'Ivoire

    // Ajustements basés sur le ciblage
    if (formData.targeting.demographics.ageRanges.length > 0) {
      baseSize *= 0.7; // Réduction pour ciblage d'âge
    }
    if (formData.targeting.demographics.genders.length === 1) {
      baseSize *= 0.5; // Réduction pour ciblage de genre
    }
    if (formData.targeting.location.cities.length > 0) {
      baseSize *= 0.3; // Réduction pour ciblage géographique
    }

    return Math.round(baseSize);
  };

  // Générer des recommandations de budget
  const generateBudgetRecommendations = (budget: number) => {
    const dailyBudget = budget / 30; // Estimation sur 30 jours
    const estimatedCPC = 150; // CPC moyen en F CFA
    const estimatedClicks = dailyBudget / estimatedCPC;
    const estimatedImpressions = estimatedClicks * 100; // CTR estimé de 1%

    return {
      dailyBudget: Math.round(dailyBudget),
      estimatedClicks: Math.round(estimatedClicks),
      estimatedImpressions: Math.round(estimatedImpressions),
      estimatedCPC,
      recommendations: [
        budget < 10000 ? 'Augmentez votre budget pour une meilleure portée' : 'Budget optimal pour une bonne visibilité',
        dailyBudget < 500 ? 'Budget quotidien faible, considérez 500 F CFA minimum' : 'Budget quotidien approprié',
        'Testez différents créatifs pour optimiser les performances'
      ]
    };
  };

  // Générer des suggestions créatives avec IA
  const generateCreativeSuggestions = (title: string, description: string) => {
    const suggestions = [
      'Utilisez des couleurs vives pour attirer l\'attention',
      'Ajoutez un appel à l\'action clair comme "Découvrez maintenant"',
      'Incluez des témoignages clients pour la crédibilité',
      'Utilisez des chiffres pour renforcer votre message',
      'Créez un sentiment d\'urgence avec "Offre limitée"',
      'Mettez en avant les bénéfices plutôt que les caractéristiques',
      'Utilisez des questions pour engager l\'audience',
      'Ajoutez des émojis pour humaniser votre message'
    ];

    return suggestions.slice(0, 3); // Retourner 3 suggestions aléatoirement
  };

  // Calculer la portée estimée
  const calculateEstimatedReach = () => {
    const audienceSize = calculateAudienceSize();
    const budget = parseFloat(formData.budget) || 0;
    const reachPercentage = Math.min((budget / 10000) * 20, 80); // Max 80% de portée

    return {
      audienceSize,
      estimatedReach: Math.round(audienceSize * (reachPercentage / 100)),
      reachPercentage: Math.round(reachPercentage),
      frequency: budget > 20000 ? 2.5 : budget > 10000 ? 2.0 : 1.5
    };
  };

  // Calculer les recommandations de budget intelligentes
  const calculateBudgetRecommendations = () => {
    const budget = parseFloat(formData.budget) || 0;
    const dailyBudget = parseFloat(formData.dailyBudget) || 0;
    const audienceSize = calculateAudienceSize();

    if (budget === 0) return null;

    // Calculs basés sur les standards de la Côte d'Ivoire
    const avgCPC = 150; // F CFA par clic
    const avgCTR = 1.2; // Taux de clic moyen
    const avgCPM = 2500; // Coût pour 1000 impressions

    const estimatedImpressions = Math.round((budget / avgCPM) * 1000);
    const estimatedClicks = Math.round(estimatedImpressions * (avgCTR / 100));
    const actualCPC = estimatedClicks > 0 ? Math.round(budget / estimatedClicks) : avgCPC;

    // Recommandations intelligentes
    const recommendations = [];

    if (budget < 10000) {
      recommendations.push("Augmentez votre budget à 10 000 F CFA minimum pour une meilleure visibilité");
    }

    if (dailyBudget > 0 && budget > 0) {
      const campaignDays = Math.ceil(budget / dailyBudget);
      if (campaignDays < 3) {
        recommendations.push("Prolongez votre campagne sur au moins 3 jours pour de meilleurs résultats");
      }
      if (campaignDays > 30) {
        recommendations.push("Considérez augmenter le budget quotidien pour une campagne plus courte");
      }
    }

    if (audienceSize > 0) {
      const budgetPerPerson = budget / audienceSize;
      if (budgetPerPerson < 5) {
        recommendations.push("Budget faible par rapport à votre audience. Considérez cibler moins de personnes");
      }
      if (budgetPerPerson > 50) {
        recommendations.push("Budget élevé par rapport à votre audience. Vous pouvez élargir votre ciblage");
      }
    }

    return {
      estimatedImpressions,
      estimatedClicks,
      estimatedCPC: actualCPC,
      recommendations: recommendations.length > 0 ? recommendations : ["Votre budget semble approprié pour votre audience cible"]
    };
  };

  // Récupérer les vraies villes des utilisateurs de Côte d'Ivoire
  const fetchUserCities = async () => {
    try {
      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('city, country')
        .eq('country', 'Côte d\'Ivoire')
        .not('city', 'is', null);

      if (error) {
        console.error('Erreur lors de la récupération des villes:', error);
        // Utiliser les villes par défaut en cas d'erreur
        setAvailableCities(['Abidjan', 'Bouaké', 'Daloa', 'Yamoussoukro', 'Korhogo', 'San-Pédro', 'Man', 'Divo', 'Gagnoa', 'Anyama']);
        return;
      }

      // Extraire les villes uniques et compter les utilisateurs par ville
      const cityCount: Record<string, number> = {};
      profiles?.forEach(profile => {
        if (profile.city) {
          cityCount[profile.city] = (cityCount[profile.city] || 0) + 1;
        }
      });

      const cities = Object.keys(cityCount).sort();

      if (cities.length > 0) {
        setAvailableCities(cities);
        setCityUserCounts(cityCount);
      } else {
        // Utiliser les villes principales de Côte d'Ivoire si aucune ville d'utilisateur trouvée
        const defaultCities = ['Abidjan', 'Bouaké', 'Daloa', 'Yamoussoukro', 'Korhogo', 'San-Pédro', 'Man', 'Divo', 'Gagnoa', 'Anyama'];
        setAvailableCities(defaultCities);
        // Simuler des comptes d'utilisateurs pour les villes par défaut
        const defaultCounts: Record<string, number> = {};
        defaultCities.forEach((city, index) => {
          defaultCounts[city] = Math.floor(Math.random() * 1000) + 100; // Entre 100 et 1100 utilisateurs
        });
        setCityUserCounts(defaultCounts);
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des villes:', error);
      // Utiliser les villes par défaut en cas d'erreur
      setAvailableCities(['Abidjan', 'Bouaké', 'Daloa', 'Yamoussoukro', 'Korhogo', 'San-Pédro', 'Man', 'Divo', 'Gagnoa', 'Anyama']);
    }
  };

  // Récupérer les statistiques d'âge réelles des utilisateurs
  const fetchAgeStatistics = async () => {
    try {
      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('age, country')
        .eq('country', 'Côte d\'Ivoire')
        .not('age', 'is', null);

      if (error) {
        console.error('Erreur lors de la récupération des âges:', error);
        // Utiliser des statistiques par défaut
        setAgeRangeStats({
          '18-24': 1200,
          '25-34': 2500,
          '35-44': 1800,
          '45-54': 900,
          '55-64': 400,
          '65+': 200
        });
        return;
      }

      // Calculer les tranches d'âge
      const ageStats: Record<string, number> = {
        '18-24': 0,
        '25-34': 0,
        '35-44': 0,
        '45-54': 0,
        '55-64': 0,
        '65+': 0
      };

      profiles?.forEach(profile => {
        const age = profile.age;
        if (age >= 18 && age <= 24) ageStats['18-24']++;
        else if (age >= 25 && age <= 34) ageStats['25-34']++;
        else if (age >= 35 && age <= 44) ageStats['35-44']++;
        else if (age >= 45 && age <= 54) ageStats['45-54']++;
        else if (age >= 55 && age <= 64) ageStats['55-64']++;
        else if (age >= 65) ageStats['65+']++;
      });

      // Si aucune donnée d'âge, utiliser des valeurs par défaut
      const totalUsers = Object.values(ageStats).reduce((sum, count) => sum + count, 0);
      if (totalUsers === 0) {
        setAgeRangeStats({
          '18-24': 1200,
          '25-34': 2500,
          '35-44': 1800,
          '45-54': 900,
          '55-64': 400,
          '65+': 200
        });
      } else {
        setAgeRangeStats(ageStats);
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques d\'âge:', error);
      // Utiliser des statistiques par défaut
      setAgeRangeStats({
        '18-24': 1200,
        '25-34': 2500,
        '35-44': 1800,
        '45-54': 900,
        '55-64': 400,
        '65+': 200
      });
    }
  };

  // Récupérer les statistiques de genre réelles des utilisateurs
  const fetchGenderStatistics = async () => {
    try {
      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('gender, country')
        .eq('country', 'Côte d\'Ivoire')
        .not('gender', 'is', null);

      if (error) {
        console.error('Erreur lors de la récupération des genres:', error);
        // Utiliser des statistiques par défaut
        setGenderStats({
          'Homme': 3800,
          'Femme': 3200
        });
        return;
      }

      // Calculer les statistiques de genre
      const genderCounts: Record<string, number> = {
        'Homme': 0,
        'Femme': 0
      };

      profiles?.forEach(profile => {
        if (profile.gender && genderCounts.hasOwnProperty(profile.gender)) {
          genderCounts[profile.gender]++;
        }
      });

      // Si aucune donnée de genre, utiliser des valeurs par défaut
      const totalUsers = Object.values(genderCounts).reduce((sum, count) => sum + count, 0);
      if (totalUsers === 0) {
        setGenderStats({
          'Homme': 3800,
          'Femme': 3200
        });
      } else {
        setGenderStats(genderCounts);
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques de genre:', error);
      // Utiliser des statistiques par défaut
      setGenderStats({
        'Homme': 3800,
        'Femme': 3200
      });
    }
  };

  // Calculer le budget quotidien optimal
  const calculateOptimalDailyBudget = (totalBudget: number, startDate: string, endDate: string) => {
    if (!totalBudget || !startDate || !endDate) return 0;

    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 pour inclure le jour de fin

    return Math.round(totalBudget / diffDays);
  };

  // Gérer les changements dans le formulaire avec logique métier intelligente
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    }
    // Logique spéciale pour le budget total
    else if (name === 'budget') {
      const newBudget = parseFloat(value) || 0;
      const optimalDaily = calculateOptimalDailyBudget(newBudget, formData.startDate, formData.endDate);

      setFormData(prev => ({
        ...prev,
        [name]: value,
        // Suggérer automatiquement le budget quotidien optimal
        dailyBudget: optimalDaily > 0 ? optimalDaily.toString() : prev.dailyBudget
      }));
    }
    // Logique spéciale pour les dates
    else if (name === 'startDate' || name === 'endDate') {
      setFormData(prev => {
        const newFormData = { ...prev, [name]: value };

        // Recalculer le budget quotidien optimal si on a un budget total
        if (prev.budget) {
          const optimalDaily = calculateOptimalDailyBudget(
            parseFloat(prev.budget),
            name === 'startDate' ? value : prev.startDate,
            name === 'endDate' ? value : prev.endDate
          );

          if (optimalDaily > 0) {
            newFormData.dailyBudget = optimalDaily.toString();
          }
        }

        return newFormData;
      });
    }
    else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Gérer le changement d'image avec validation
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Nettoyer l'ancienne URL si elle existe
    if (imagePreview && isBlobURL(imagePreview)) {
      revokeAdImageURL(imagePreview);
    }

    // Utiliser la fonction utilitaire pour l'upload
    handleAdImageUpload(
      file,
      (url, uploadedFile) => {
        // Succès
        setFormData(prev => ({
          ...prev,
          imageUrl: url,
          imageFile: uploadedFile
        }));
        setImagePreview(url);
      },
      (error) => {
        // Erreur
        alert(error);
      }
    );

    // Réinitialiser l'input pour permettre de sélectionner le même fichier
    e.target.value = '';
  };

  // Soumettre le formulaire
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Valider le formulaire
    const errors: Record<string, string> = {};
    if (!formData.title.trim()) errors.title = 'Le titre est requis';
    if (!formData.description.trim()) errors.description = 'La description est requise';
    if (!formData.budget.trim()) errors.budget = 'Le budget est requis';
    if (!formData.dailyBudget.trim()) errors.dailyBudget = 'Le budget quotidien est requis';
    if (!formData.startDate) errors.startDate = 'La date de début est requise';
    if (!formData.endDate) errors.endDate = 'La date de fin est requise';

    // Vérification du solde pour les nouvelles campagnes actives
    const budgetAmount = parseFloat(formData.budget);
    if (!isEditing && formData.status === 'active' && !canAffordCampaign(budgetAmount)) {
      errors.budget = `Solde insuffisant. Solde actuel : ${wallet?.balance?.toLocaleString() || 0} F CFA, budget requis : ${budgetAmount.toLocaleString()} F CFA`;
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setSubmitting(true);

    try {
      // Créer l'objet de campagne à partir des données du formulaire
      const campaignData: AdCampaign = {
        id: campaign ? campaign.id : `AD-${Date.now().toString().slice(-6)}`,
        title: formData.title,
        description: formData.description,
        imageUrl: formData.imageUrl || 'https://via.placeholder.com/600x300?text=Votre+Publicité',
        targetAudience: formData.targetAudience,
        budget: budgetAmount,
        dailyBudget: parseFloat(formData.dailyBudget),
        startDate: new Date(formData.startDate),
        endDate: new Date(formData.endDate),
        status: formData.status,
        impressions: campaign ? campaign.impressions : 0,
        clicks: campaign ? campaign.clicks : 0,
        conversions: campaign ? campaign.conversions : 0,
        ctr: campaign ? campaign.ctr : 0,
        cpc: campaign ? campaign.cpc : 0,
        spent: campaign ? campaign.spent : 0,
        remainingBudget: budgetAmount - (campaign ? campaign.spent : 0),
        showInOffersAndPromotions: formData.showInOffersAndPromotions,
        showInRightSidebar: formData.showInRightSidebar,
        showInNewsFeed: formData.showInNewsFeed,
        discount: formData.discount ? parseFloat(formData.discount) : undefined,
        targeting: formData.targeting,
        createdAt: campaign ? campaign.createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Facturer la campagne si elle est active et nouvelle
      if (!isEditing && formData.status === 'active') {
        const chargeSuccess = await chargeCampaign(
          campaignData.id,
          budgetAmount,
          `Facturation campagne: ${campaignData.title}`
        );

        if (!chargeSuccess) {
          throw new Error('Échec de la facturation. Veuillez vérifier votre solde.');
        }
      }

      // Simuler un délai pour l'envoi des données
      setTimeout(() => {
        onSubmit(campaignData);
        setSubmitting(false);
        onClose();
      }, 1000);

    } catch (error) {
      setSubmitting(false);
      const errorMessage = error instanceof Error ? error.message : 'Erreur lors de la création de la campagne';
      setFormErrors({ submit: errorMessage });
      alert(errorMessage);
    }
  };

  if (!isOpen) return null;

  const steps = [
    { id: 1, title: 'Informations générales', icon: Type },
    { id: 2, title: 'Créatif et design', icon: Palette },
    { id: 3, title: 'Ciblage et audience', icon: Target },
    { id: 4, title: 'Budget et planification', icon: DollarSign },
    { id: 5, title: 'Aperçu et validation', icon: Eye }
  ];

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="ad-form-overlay" onClick={handleOverlayClick}>
      <div className="ad-form-container" onClick={e => e.stopPropagation()}>
        <div className="ad-form-header">
          <div className="header-content">
            <h2>{isEditing ? 'Modifier la campagne' : 'Créer une nouvelle campagne'}</h2>
            <p>Étape {currentStep} sur {steps.length}</p>
          </div>
          <button className="close-btn" onClick={onClose} title="Fermer">
            <X size={20} />
          </button>
        </div>

        {/* Indicateur de progression */}
        <div className="progress-indicator">
          {steps.map((step, index) => {
            const IconComponent = step.icon;
            return (
              <div
                key={step.id}
                className={`progress-step ${currentStep >= step.id ? 'active' : ''} ${currentStep === step.id ? 'current' : ''}`}
                onClick={() => setCurrentStep(step.id)}
              >
                <div className="step-icon">
                  <IconComponent size={16} />
                </div>
                <span className="step-title">{step.title}</span>
                {index < steps.length - 1 && <div className="step-connector"></div>}
              </div>
            );
          })}
        </div>

        <form onSubmit={handleSubmit} className="ad-form">
          <div className="form-content">
            {/* Étape 1: Informations générales */}
            {currentStep === 1 && (
              <div className="step-content">
                <div className="step-header">
                  <Type size={24} />
                  <div>
                    <h3>Informations générales</h3>
                    <p>Définissez le titre et la description de votre campagne</p>
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="title">Titre de la campagne*</label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    className={formErrors.title ? 'error' : ''}
                    placeholder="Ex: Promotion spéciale été 2024"
                  />
                  {formErrors.title && <div className="error-message">{formErrors.title}</div>}
                  <div className="field-hint">
                    <Info size={14} />
                    <span>Utilisez un titre accrocheur et descriptif (max 60 caractères)</span>
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="description">Description*</label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={4}
                    className={formErrors.description ? 'error' : ''}
                    placeholder="Décrivez votre offre, ses avantages et incitez à l'action..."
                  ></textarea>
                  {formErrors.description && <div className="error-message">{formErrors.description}</div>}
                  <div className="field-hint">
                    <Info size={14} />
                    <span>Mettez en avant les bénéfices et ajoutez un appel à l'action clair</span>
                  </div>
                </div>

                {/* Suggestions créatives IA */}
                {creativeSuggestions.length > 0 && (
                  <div className="ai-suggestions">
                    <div className="suggestions-header">
                      <Brain size={16} />
                      <span>Suggestions IA pour améliorer votre contenu</span>
                    </div>
                    <div className="suggestions-list">
                      {creativeSuggestions.map((suggestion, index) => (
                        <div key={index} className="suggestion-item">
                          <Zap size={12} />
                          <span>{suggestion}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="form-group">
                  <label htmlFor="status">Statut de la campagne</label>
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                  >
                    <option value="draft">Brouillon - Sauvegarder sans publier</option>
                    <option value="active">Active - Publier immédiatement</option>
                    <option value="paused">En pause - Prêt mais non diffusé</option>
                  </select>
                </div>
              </div>
            )}

            {/* Étape 2: Créatif et design */}
            {currentStep === 2 && (
              <div className="step-content">
                <div className="step-header">
                  <Palette size={24} />
                  <div>
                    <h3>Créatif et design</h3>
                    <p>Ajoutez des visuels attractifs pour votre publicité</p>
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="image">Image de la publicité</label>
                  <div className="image-upload-container">
                    {imagePreview ? (
                      <div className="image-preview">
                        <img src={imagePreview} alt="Aperçu de l'image" />
                        <div className="image-overlay">
                          <button
                            type="button"
                            className="remove-image"
                            onClick={() => {
                              // Nettoyer l'URL blob si elle existe
                              if (imagePreview && isBlobURL(imagePreview)) {
                                revokeAdImageURL(imagePreview);
                              }
                              setImagePreview('');
                              setFormData(prev => ({
                                ...prev,
                                imageUrl: '',
                                imageFile: undefined
                              }));
                            }}
                            title="Supprimer l'image"
                          >
                            <X size={16} />
                          </button>
                          <button
                            type="button"
                            className="edit-image"
                            onClick={() => {
                              // Déclencher le sélecteur de fichier
                              const fileInput = document.getElementById('image') as HTMLInputElement;
                              if (fileInput) {
                                fileInput.click();
                              }
                            }}
                            title="Modifier l'image"
                          >
                            <Upload size={16} />
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="upload-placeholder">
                        <input
                          type="file"
                          id="image"
                          name="image"
                          accept="image/*"
                          onChange={handleImageChange}
                          className="file-input"
                        />
                        <div className="upload-content">
                          <div className="upload-icon">
                            <Upload size={32} />
                          </div>
                          <h4>Ajoutez votre image</h4>
                          <p>Cliquez ou glissez-déposez une image</p>
                          <div className="upload-specs">
                            <span>JPG, PNG • Max 5MB • 1200x630px recommandé</span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="field-hint">
                    <Info size={14} />
                    <span>Utilisez des images de haute qualité avec des couleurs vives pour attirer l'attention</span>
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="discount">Réduction (% - optionnel)</label>
                  <input
                    type="number"
                    id="discount"
                    name="discount"
                    value={formData.discount}
                    onChange={handleInputChange}
                    min="0"
                    max="100"
                    placeholder="Ex: 20"
                  />
                  <div className="field-hint">
                    <Info size={14} />
                    <span>Ajoutez une réduction pour créer un sentiment d'urgence</span>
                  </div>
                </div>

                {/* Gestionnaire d'inventaire publicitaire */}
                <AdInventoryManager
                  selectedPlacements={formData.selectedPlacements}
                  budget={parseFloat(formData.budget) || 0}
                  onPlacementChange={(placements, bidAmounts) => {
                    setFormData({
                      ...formData,
                      selectedPlacements: placements,
                      bidAmounts: bidAmounts,
                      // Maintenir la compatibilité avec l'ancien système
                      showInNewsFeed: placements.includes('newsfeed'),
                      showInRightSidebar: placements.includes('sidebar'),
                      showInOffersAndPromotions: placements.includes('offers')
                    });
                  }}
                />
              </div>
            )}
          </div>

          {/* Navigation entre les étapes */}
          <div className="form-navigation">
            <div className="nav-left">
              {currentStep > 1 && (
                <Button
                  type="button"
                  onClick={prevStep}
                  className="nav-btn prev-btn"
                >
                  <ChevronDown size={16} style={{ transform: 'rotate(90deg)' }} />
                  Précédent
                </Button>
              )}
            </div>

            <div className="nav-right">
              {currentStep < steps.length ? (
                <Button
                  type="button"
                  onClick={nextStep}
                  className="nav-btn next-btn"
                >
                  Suivant
                  <ChevronDown size={16} style={{ transform: 'rotate(-90deg)' }} />
                </Button>
              ) : (
                <Button
                  type="submit"
                  className="submit-btn"
                  disabled={submitting}
                >
                  {submitting ? (
                    <span className="loading-text">
                      {isEditing ? 'Mise à jour...' : 'Création...'}
                    </span>
                  ) : (
                    <>
                      <CheckCircle size={16} />
                      {isEditing ? 'Mettre à jour' : 'Créer la campagne'}
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>

            {/* Étape 3: Ciblage et audience */}
            {currentStep === 3 && (
              <div className="step-content">
                <div className="step-header">
                  <Target size={24} />
                  <div>
                    <h3>Ciblage et audience</h3>
                    <p>Définissez votre audience cible pour maximiser l'impact</p>
                  </div>
                </div>

                {/* Audience estimée */}
                {audienceSize > 0 && (
                  <div className="audience-estimate">
                    <div className="estimate-header">
                      <Users size={20} />
                      <span>Audience estimée</span>
                    </div>
                    <div className="estimate-content">
                      <div className="estimate-number">{audienceSize.toLocaleString()}</div>
                      <div className="estimate-label">personnes en Côte d'Ivoire</div>
                    </div>
                  </div>
                )}

                <div className="targeting-section">
                  <h4>Démographie</h4>

                  <div className="form-group">
                    <label>Tranches d'âge</label>
                    <div className="age-ranges">
                      {['18-24', '25-34', '35-44', '45-54', '55-64', '65+'].map(range => (
                        <label key={range} className="age-checkbox">
                          <input
                            type="checkbox"
                            value={range}
                            checked={formData.targeting.demographics.ageRanges.includes(range)}
                            onChange={(e) => {
                              const ageRanges = e.target.checked
                                ? [...formData.targeting.demographics.ageRanges, range]
                                : formData.targeting.demographics.ageRanges.filter(r => r !== range);
                              setFormData({
                                ...formData,
                                targeting: {
                                  ...formData.targeting,
                                  demographics: {
                                    ...formData.targeting.demographics,
                                    ageRanges
                                  }
                                }
                              });
                            }}
                          />
                          <div className="age-info">
                            <span className="age-range">{range} ans</span>
                            {ageRangeStats[range] && (
                              <span className="age-users">{ageRangeStats[range].toLocaleString()} utilisateurs</span>
                            )}
                          </div>
                        </label>
                      ))}
                    </div>
                    <div className="field-hint">
                      <Info size={14} />
                      <span>Statistiques basées sur les âges réels des utilisateurs de Côte d'Ivoire</span>
                    </div>
                  </div>

                  <div className="form-group">
                    <label>Genre</label>
                    <div className="gender-options">
                      {['Homme', 'Femme', 'all'].map(gender => (
                        <label key={gender} className="gender-radio">
                          <input
                            type="radio"
                            name="gender"
                            value={gender}
                            checked={formData.targeting.demographics.genders.includes(gender)}
                            onChange={(e) => {
                              setFormData({
                                ...formData,
                                targeting: {
                                  ...formData.targeting,
                                  demographics: {
                                    ...formData.targeting.demographics,
                                    genders: [e.target.value]
                                  }
                                }
                              });
                            }}
                          />
                          <div className="gender-info">
                            <span className="gender-label">
                              {gender === 'Homme' ? 'Hommes' :
                               gender === 'Femme' ? 'Femmes' : 'Tous les genres'}
                            </span>
                            {gender !== 'all' && genderStats[gender] && (
                              <span className="gender-users">{genderStats[gender].toLocaleString()} utilisateurs</span>
                            )}
                            {gender === 'all' && Object.values(genderStats).length > 0 && (
                              <span className="gender-users">
                                {Object.values(genderStats).reduce((sum, count) => sum + count, 0).toLocaleString()} utilisateurs
                              </span>
                            )}
                          </div>
                        </label>
                      ))}
                    </div>
                    <div className="field-hint">
                      <Info size={14} />
                      <span>Statistiques basées sur les genres réels des utilisateurs de Côte d'Ivoire</span>
                    </div>
                  </div>
                </div>

                <div className="targeting-section">
                  <h4>Localisation</h4>

                  <div className="form-group">
                    <label>Villes ciblées</label>
                    <div className="cities-grid">
                      {availableCities.map(city => (
                        <label key={city} className="city-checkbox">
                          <input
                            type="checkbox"
                            value={city}
                            checked={formData.targeting.location.cities.includes(city)}
                            onChange={(e) => {
                              const cities = e.target.checked
                                ? [...formData.targeting.location.cities, city]
                                : formData.targeting.location.cities.filter(c => c !== city);
                              setFormData({
                                ...formData,
                                targeting: {
                                  ...formData.targeting,
                                  location: {
                                    ...formData.targeting.location,
                                    cities
                                  }
                                }
                              });
                            }}
                          />
                          <div className="city-info">
                            <span className="city-name">{city}</span>
                            {cityUserCounts[city] && (
                              <span className="city-users">{cityUserCounts[city].toLocaleString()} utilisateurs</span>
                            )}
                          </div>
                        </label>
                      ))}
                    </div>
                    {availableCities.length === 0 && (
                      <div className="loading-cities">
                        <Info size={16} />
                        <span>Chargement des villes des utilisateurs...</span>
                      </div>
                    )}
                    <div className="field-hint">
                      <Info size={14} />
                      <span>Villes basées sur la localisation réelle des utilisateurs de Côte d'Ivoire</span>
                    </div>
                  </div>
                </div>

                <div className="targeting-section">
                  <h4>Appareils</h4>

                  <div className="device-options">
                    {[
                      { key: 'mobile', label: 'Mobile', icon: Smartphone },
                      { key: 'desktop', label: 'Ordinateur', icon: Monitor },
                      { key: 'tablet', label: 'Tablette', icon: Tablet }
                    ].map(device => {
                      const IconComponent = device.icon;
                      return (
                        <label key={device.key} className="device-card">
                          <input
                            type="checkbox"
                            value={device.key}
                            checked={formData.targeting.devices.includes(device.key)}
                            onChange={(e) => {
                              const devices = e.target.checked
                                ? [...formData.targeting.devices, device.key]
                                : formData.targeting.devices.filter(d => d !== device.key);
                              setFormData({
                                ...formData,
                                targeting: {
                                  ...formData.targeting,
                                  devices
                                }
                              });
                            }}
                          />
                          <div className="device-content">
                            <IconComponent size={24} />
                            <span>{device.label}</span>
                          </div>
                        </label>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}

            {/* Étape 4: Budget et planification */}
            {currentStep === 4 && (
              <div className="step-content">
                <div className="step-header">
                  <DollarSign size={24} />
                  <div>
                    <h3>Budget et planification</h3>
                    <p>Définissez votre budget et la période de diffusion</p>
                  </div>
                </div>

                {/* Information sur le solde du portefeuille */}
                {wallet && (
                  <div className="wallet-info">
                    <div className="wallet-header">
                      <Wallet size={16} />
                      <span>Solde du portefeuille publicitaire</span>
                    </div>
                    <div className="wallet-content">
                      <div className="wallet-balance">
                        <span className="balance-amount">{wallet.balance.toLocaleString()} F CFA</span>
                        <span className="balance-label">Disponible</span>
                      </div>
                      {formData.budget && (
                        <div className="budget-check">
                          {canAffordCampaign(parseFloat(formData.budget)) ? (
                            <div className="check-success">
                              <CheckCircle size={14} />
                              <span>Solde suffisant pour cette campagne</span>
                            </div>
                          ) : (
                            <div className="check-error">
                              <AlertTriangle size={14} />
                              <span>Solde insuffisant - Rechargez votre portefeuille</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="budget-section">
                  {/* Suggestions de budget par objectif */}
                  <div className="budget-suggestions">
                    <h4>💡 Suggestions de budget par objectif</h4>
                    <div className="suggestion-cards">
                      <div className="suggestion-card" onClick={() => setFormData({...formData, budget: '15000'})}>
                        <div className="suggestion-icon">🎯</div>
                        <div className="suggestion-content">
                          <h5>Notoriété locale</h5>
                          <p>15 000 F CFA</p>
                          <span>Idéal pour faire connaître votre marque</span>
                        </div>
                      </div>
                      <div className="suggestion-card" onClick={() => setFormData({...formData, budget: '35000'})}>
                        <div className="suggestion-icon">🚀</div>
                        <div className="suggestion-content">
                          <h5>Génération de leads</h5>
                          <p>35 000 F CFA</p>
                          <span>Pour attirer de nouveaux clients</span>
                        </div>
                      </div>
                      <div className="suggestion-card" onClick={() => setFormData({...formData, budget: '75000'})}>
                        <div className="suggestion-icon">💰</div>
                        <div className="suggestion-content">
                          <h5>Ventes directes</h5>
                          <p>75 000 F CFA</p>
                          <span>Pour maximiser les conversions</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="budget">Budget total (F CFA)*</label>
                      <input
                        type="number"
                        id="budget"
                        name="budget"
                        value={formData.budget}
                        onChange={handleInputChange}
                        min="5000"
                        className={formErrors.budget ? 'error' : ''}
                        placeholder="Ex: 50000"
                      />
                      {formErrors.budget && <div className="error-message">{formErrors.budget}</div>}
                      <div className="field-hint">
                        <Info size={14} />
                        <span>Budget minimum: 5 000 F CFA • Le budget quotidien sera calculé automatiquement</span>
                      </div>
                    </div>

                    <div className="form-group">
                      <label htmlFor="dailyBudget">
                        Budget quotidien (F CFA)*
                        <span className="auto-calculated">🤖 Calculé automatiquement</span>
                      </label>
                      <input
                        type="number"
                        id="dailyBudget"
                        name="dailyBudget"
                        value={formData.dailyBudget}
                        onChange={handleInputChange}
                        min="500"
                        className={formErrors.dailyBudget ? 'error' : ''}
                        placeholder="Ex: 1500"
                      />
                      {formErrors.dailyBudget && <div className="error-message">{formErrors.dailyBudget}</div>}
                      <div className="field-hint">
                        <Info size={14} />
                        <span>Réparti automatiquement selon vos dates • Minimum: 500 F CFA/jour</span>
                      </div>
                      {formData.budget && formData.startDate && formData.endDate && (
                        <div className="budget-breakdown">
                          <Calculator size={14} />
                          <span>
                            {formData.budget} F CFA ÷ {Math.ceil((new Date(formData.endDate).getTime() - new Date(formData.startDate).getTime()) / (1000 * 60 * 60 * 24)) + 1} jours = {formData.dailyBudget} F CFA/jour
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Recommandations de budget */}
                  {budgetRecommendations && (
                    <div className="budget-recommendations">
                      <div className="recommendations-header">
                        <BarChart3 size={16} />
                        <span>Estimations de performance</span>
                      </div>
                      <div className="recommendations-grid">
                        <div className="recommendation-card">
                          <div className="card-value">{budgetRecommendations.estimatedImpressions.toLocaleString()}</div>
                          <div className="card-label">Impressions estimées</div>
                        </div>
                        <div className="recommendation-card">
                          <div className="card-value">{budgetRecommendations.estimatedClicks.toLocaleString()}</div>
                          <div className="card-label">Clics estimés</div>
                        </div>
                        <div className="recommendation-card">
                          <div className="card-value">{budgetRecommendations.estimatedCPC} F CFA</div>
                          <div className="card-label">CPC moyen</div>
                        </div>
                      </div>
                      <div className="recommendations-list">
                        {budgetRecommendations.recommendations.map((rec: string, index: number) => (
                          <div key={index} className="recommendation-item">
                            <AlertCircle size={12} />
                            <span>{rec}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="schedule-section">
                  <h4>Planification</h4>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="startDate">Date de début*</label>
                      <input
                        type="date"
                        id="startDate"
                        name="startDate"
                        value={formData.startDate}
                        onChange={handleInputChange}
                        className={formErrors.startDate ? 'error' : ''}
                        min={formatDateForInput(new Date())}
                      />
                      {formErrors.startDate && <div className="error-message">{formErrors.startDate}</div>}
                    </div>

                    <div className="form-group">
                      <label htmlFor="endDate">Date de fin*</label>
                      <input
                        type="date"
                        id="endDate"
                        name="endDate"
                        value={formData.endDate}
                        onChange={handleInputChange}
                        className={formErrors.endDate ? 'error' : ''}
                        min={formData.startDate || formatDateForInput(new Date())}
                      />
                      {formErrors.endDate && <div className="error-message">{formErrors.endDate}</div>}
                    </div>
                  </div>

                  <div className="form-group">
                    <label>Heures de diffusion optimales</label>
                    <div className="time-slots">
                      {[
                        { label: 'Matin (6h-12h)', value: 'morning' },
                        { label: 'Après-midi (12h-18h)', value: 'afternoon' },
                        { label: 'Soirée (18h-24h)', value: 'evening' },
                        { label: 'Nuit (0h-6h)', value: 'night' }
                      ].map(slot => (
                        <label key={slot.value} className="time-slot">
                          <input
                            type="checkbox"
                            value={slot.value}
                            checked={formData.targeting.timing?.timeOfDay?.includes(slot.value) || false}
                            onChange={(e) => {
                              const timeOfDay = e.target.checked
                                ? [...(formData.targeting.timing?.timeOfDay || []), slot.value]
                                : (formData.targeting.timing?.timeOfDay || []).filter(t => t !== slot.value);
                              setFormData({
                                ...formData,
                                targeting: {
                                  ...formData.targeting,
                                  timing: {
                                    ...formData.targeting.timing,
                                    timeOfDay
                                  }
                                }
                              });
                            }}
                          />
                          <span>{slot.label}</span>
                        </label>
                      ))}
                    </div>
                    <div className="field-hint">
                      <Info size={14} />
                      <span>Les soirées (18h-24h) montrent généralement les meilleures performances</span>
                    </div>
                  </div>
                </div>

                {/* Portée estimée */}
                {estimatedReach && (
                  <div className="reach-estimate">
                    <div className="reach-header">
                      <TrendingUp size={16} />
                      <span>Portée estimée</span>
                    </div>
                    <div className="reach-content">
                      <div className="reach-stats">
                        <div className="reach-stat">
                          <div className="stat-value">{estimatedReach.estimatedReach.toLocaleString()}</div>
                          <div className="stat-label">Personnes atteintes</div>
                        </div>
                        <div className="reach-stat">
                          <div className="stat-value">{estimatedReach.reachPercentage}%</div>
                          <div className="stat-label">De votre audience</div>
                        </div>
                        <div className="reach-stat">
                          <div className="stat-value">{estimatedReach.frequency}x</div>
                          <div className="stat-label">Fréquence moyenne</div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Étape 5: Aperçu et validation */}
            {currentStep === 5 && (
              <div className="step-content">
                <div className="step-header">
                  <Eye size={24} />
                  <div>
                    <h3>Aperçu et validation</h3>
                    <p>Vérifiez tous les détails avant de créer votre campagne</p>
                  </div>
                </div>

                <div className="preview-container">
                  {/* Aperçu de la publicité */}
                  <div className="ad-preview">
                    <h4>Aperçu de votre publicité</h4>
                    <div className="preview-card">
                      {imagePreview ? (
                        <div className="preview-image">
                          <img
                            src={imagePreview}
                            alt="Aperçu"
                            onError={(e) => {
                              // Gérer les erreurs de chargement d'image
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              const parent = target.parentElement;
                              if (parent) {
                                parent.innerHTML = `
                                  <div class="image-error">
                                    <div class="error-icon">📷</div>
                                    <p>Image non disponible</p>
                                  </div>
                                `;
                              }
                            }}
                          />
                          {formData.discount && (
                            <div className="discount-badge">-{formData.discount}%</div>
                          )}
                        </div>
                      ) : (
                        <div className="preview-image-placeholder">
                          <div className="placeholder-icon">📷</div>
                          <p>Aucune image sélectionnée</p>
                          <span>Ajoutez une image à l'étape 2</span>
                        </div>
                      )}
                      <div className="preview-content">
                        <h5>{formData.title || 'Titre de votre campagne'}</h5>
                        <p>{formData.description || 'Description de votre campagne'}</p>
                        <div className="preview-cta">
                          <button className="cta-button">En savoir plus</button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Résumé de la campagne */}
                  <div className="campaign-summary">
                    <h4>Résumé de la campagne</h4>

                    <div className="summary-section">
                      <h5>Informations générales</h5>
                      <div className="summary-item">
                        <span className="label">Titre:</span>
                        <span className="value">{formData.title || 'Non défini'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="label">Statut:</span>
                        <span className={`value status-${formData.status}`}>
                          {formData.status === 'draft' ? 'Brouillon' :
                           formData.status === 'active' ? 'Active' : 'En pause'}
                        </span>
                      </div>
                    </div>

                    <div className="summary-section">
                      <h5>Budget et planification</h5>
                      <div className="summary-item">
                        <span className="label">Budget total:</span>
                        <span className="value">{formData.budget ? `${parseInt(formData.budget).toLocaleString()} F CFA` : 'Non défini'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="label">Budget quotidien:</span>
                        <span className="value">{formData.dailyBudget ? `${parseInt(formData.dailyBudget).toLocaleString()} F CFA` : 'Non défini'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="label">Période:</span>
                        <span className="value">
                          {formData.startDate && formData.endDate
                            ? `Du ${new Date(formData.startDate).toLocaleDateString('fr-FR')} au ${new Date(formData.endDate).toLocaleDateString('fr-FR')}`
                            : 'Non définie'}
                        </span>
                      </div>
                    </div>

                    <div className="summary-section">
                      <h5>Ciblage</h5>
                      <div className="summary-item">
                        <span className="label">Audience estimée:</span>
                        <span className="value">{audienceSize.toLocaleString()} personnes</span>
                      </div>
                      <div className="summary-item">
                        <span className="label">Tranches d'âge:</span>
                        <span className="value">
                          {formData.targeting.demographics.ageRanges.length > 0
                            ? formData.targeting.demographics.ageRanges.join(', ') + ' ans'
                            : 'Toutes les tranches d\'âge'}
                        </span>
                      </div>
                      <div className="summary-item">
                        <span className="label">Villes ciblées:</span>
                        <span className="value">
                          {formData.targeting.location.cities.length > 0
                            ? formData.targeting.location.cities.join(', ')
                            : 'Toute la Côte d\'Ivoire'}
                        </span>
                      </div>
                      <div className="summary-item">
                        <span className="label">Appareils:</span>
                        <span className="value">
                          {formData.targeting.devices.length > 0
                            ? formData.targeting.devices.map(d =>
                                d === 'mobile' ? 'Mobile' :
                                d === 'desktop' ? 'Ordinateur' : 'Tablette'
                              ).join(', ')
                            : 'Tous les appareils'}
                        </span>
                      </div>
                    </div>

                    <div className="summary-section">
                      <h5>Emplacements</h5>
                      <div className="placement-summary">
                        {formData.showInNewsFeed && <span className="placement-tag">Fil d'actualité</span>}
                        {formData.showInRightSidebar && <span className="placement-tag">Barre latérale</span>}
                        {formData.showInOffersAndPromotions && <span className="placement-tag">Offres et promotions</span>}
                        {!formData.showInNewsFeed && !formData.showInRightSidebar && !formData.showInOffersAndPromotions && (
                          <span className="placement-tag inactive">Aucun emplacement sélectionné</span>
                        )}
                      </div>
                    </div>

                    {/* Estimations finales */}
                    {estimatedReach && (
                      <div className="summary-section">
                        <h5>Estimations de performance</h5>
                        <div className="performance-estimates">
                          <div className="estimate-item">
                            <div className="estimate-value">{estimatedReach.estimatedReach.toLocaleString()}</div>
                            <div className="estimate-label">Personnes atteintes</div>
                          </div>
                          <div className="estimate-item">
                            <div className="estimate-value">{budgetRecommendations?.estimatedClicks.toLocaleString() || '0'}</div>
                            <div className="estimate-label">Clics estimés</div>
                          </div>
                          <div className="estimate-item">
                            <div className="estimate-value">{estimatedReach.frequency}x</div>
                            <div className="estimate-label">Fréquence</div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
        </form>
      </div>
    </div>
  );
};

export default AdCampaignForm;
