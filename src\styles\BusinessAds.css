.business-ads-container {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.ads-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.header-content .ads-title {
  color: white;
  margin-bottom: 0.5rem;
}

.header-content .ads-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
}

/* Barr<PERSON> d'outils avancée */
.advanced-toolbar {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.toolbar-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.toolbar-btn.active {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
}

.toolbar-btn.ai-assistant {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: rgba(255, 255, 255, 0.3);
}

.toolbar-btn.wallet {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: rgba(255, 255, 255, 0.3);
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn.notifications {
  position: relative;
}

/* Notifications */
.notifications-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-width: 400px;
}

.notification {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  animation: slideIn 0.3s ease;
}

.notification.success {
  border-left-color: #10b981;
}

.notification.warning {
  border-left-color: #f59e0b;
}

.notification.error {
  border-left-color: #ef4444;
}

.notification.info {
  border-left-color: #3b82f6;
}

.notification-content {
  flex: 1;
}

.notification-content strong {
  display: block;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.notification-content p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.notification-close {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: color 0.2s;
}

.notification-close:hover {
  color: #6b7280;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Rapports rapides */
.quick-reports {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.reports-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.reports-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.reports-actions {
  display: flex;
  gap: 0.75rem;
}

.report-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #e5e7eb;
  background: white;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.report-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.report-btn.pdf {
  color: #dc2626;
  border-color: #fecaca;
}

.report-btn.excel {
  color: #059669;
  border-color: #a7f3d0;
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.report-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.report-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.report-card h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.report-card p {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.report-metrics {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.report-metrics span {
  background: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4f46e5;
  border: 1px solid #e0e7ff;
}

/* Mode sombre */
.business-ads-container.dark-mode {
  background: #1f2937;
  color: white;
}

.dark-mode .ads-stats-container .stats-card {
  background: #374151;
  border-color: #4b5563;
}

.dark-mode .quick-reports {
  background: #374151;
  border-color: #4b5563;
}

.dark-mode .report-card {
  background: #4b5563;
  border-color: #6b7280;
}

.dark-mode .toolbar-btn {
  background: rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .ads-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }

  .advanced-toolbar {
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .toolbar-btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .notifications-container {
    left: 1rem;
    right: 1rem;
    max-width: none;
  }

  .reports-grid {
    grid-template-columns: 1fr;
  }
}

.ads-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.25rem;
}

.ads-subtitle {
  font-size: 0.875rem;
  color: #666;
}

.create-ad-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #4f46e5;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.create-ad-button:hover {
  background-color: #4338ca;
}

.ads-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background-color: #f3f4f6;
  border-radius: 50%;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

.stat-label {
  font-size: 0.875rem;
  color: #666;
}

.ads-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-input {
  width: 100%;
  padding: 0.625rem 0.75rem 0.625rem 2.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.filter-container {
  display: flex;
  gap: 0.75rem;
}

.filter-dropdown, .sort-dropdown {
  position: relative;
}

.filter-button, .sort-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  padding: 0.625rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.filter-dropdown-content, .sort-dropdown-content {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  z-index: 10;
  margin-top: 0.25rem;
  display: none;
}

.filter-dropdown:hover .filter-dropdown-content,
.sort-dropdown:hover .sort-dropdown-content {
  display: block;
}

/* AI Assistant Styles */
.ai-assistant-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.ai-assistant-content {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.ai-assistant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ai-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.ai-icon {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.75rem;
  border-radius: 12px;
}

.ai-title h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.ai-title p {
  font-size: 0.875rem;
  opacity: 0.9;
  margin: 0;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.ai-assistant-tabs {
  display: flex;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.ai-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.ai-tab.active {
  color: #4f46e5;
  border-bottom-color: #4f46e5;
  background: white;
}

.ai-assistant-body {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-option, .sort-option {
  padding: 0.625rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.filter-option:hover, .sort-option:hover {
  background-color: #f3f4f6;
}

.filter-option.selected, .sort-option.selected {
  background-color: #f3f4f6;
  font-weight: 500;
}

.ads-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.ad-card {
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.ad-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.ad-image {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.ad-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

/* Placeholder et gestion d'erreur d'image */
.image-placeholder,
.image-error-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border: 2px dashed #d1d5db;
  color: #6b7280;
  text-align: center;
}

.image-error-placeholder {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.placeholder-icon,
.error-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.7;
}

.image-placeholder p,
.image-error-placeholder p {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 500;
}

.image-error-placeholder p {
  color: #991b1b;
}

.ad-status {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-active {
  background-color: #10b981;
  color: white;
}

.status-paused {
  background-color: #f59e0b;
  color: white;
}

.status-completed {
  background-color: #6b7280;
  color: white;
}

.status-draft {
  background-color: #e5e7eb;
  color: #4b5563;
}

.ad-content {
  padding: 1.25rem;
}

.ad-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.ad-description {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.ad-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.ad-detail {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 0.75rem;
  color: #6b7280;
}

.detail-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: #333;
}

.ad-metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
  margin-bottom: 1.25rem;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  padding: 0.75rem;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.metric-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
}

.metric-label {
  font-size: 0.75rem;
  color: #6b7280;
}

.ad-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
}

.pause-button {
  background-color: #f3f4f6;
  color: #4b5563;
}

.resume-button, .activate-button {
  background-color: #10b981;
  color: white;
}

.edit-button {
  background-color: #f3f4f6;
  color: #4b5563;
}

.delete-button {
  background-color: #f3f4f6;
  color: #ef4444;
}

.action-button:hover {
  opacity: 0.9;
}

.no-ads {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background-color: white;
  border-radius: 0.5rem;
  text-align: center;
  color: #6b7280;
  gap: 1rem;
}

.ad-form-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.ad-form-content {
  background-color: white;
  border-radius: 0.5rem;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.ad-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  border-bottom: 1px solid #e5e7eb;
}

.ad-form-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
}

.ad-form-body {
  padding: 1.25rem;
  max-height: 70vh;
  overflow-y: auto;
}

/* Styles pour le formulaire */
form {
  width: 100%;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.form-group.half {
  flex: 1;
}

label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 0.375rem;
}

.required {
  color: #ef4444;
  margin-left: 0.25rem;
}

input[type="text"],
input[type="number"],
input[type="date"],
textarea {
  width: 100%;
  padding: 0.625rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
textarea:focus {
  border-color: #4f46e5;
  outline: none;
}

input.error,
textarea.error {
  border-color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.image-input-container {
  position: relative;
}

.image-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.image-preview {
  margin-top: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  overflow: hidden;
  max-height: 200px;
}

.image-preview img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.target-audience-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  max-height: 200px;
  overflow-y: auto;
}

.target-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.target-option input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  cursor: pointer;
}

.target-option label {
  font-size: 0.875rem;
  color: #4b5563;
  margin-bottom: 0;
  cursor: pointer;
}

.input-with-icon {
  position: relative;
}

.input-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.status-options {
  display: flex;
  gap: 1.5rem;
  margin-top: 0.5rem;
}

.status-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-option input[type="radio"] {
  width: 1rem;
  height: 1rem;
  cursor: pointer;
}

.status-option label {
  font-size: 0.875rem;
  color: #4b5563;
  margin-bottom: 0;
  cursor: pointer;
}

.offers-promotion-option,
.sidebar-ad-option {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #f3f4f6;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.offers-promotion-option input[type="checkbox"],
.sidebar-ad-option input[type="checkbox"] {
  width: 1.25rem;
  height: 1.25rem;
  cursor: pointer;
  margin-top: 0.125rem;
}

.offers-promotion-option label,
.sidebar-ad-option label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0;
  cursor: pointer;
}

.sidebar-ad-option {
  background-color: #f0f9ff;
  border-left: 3px solid #0ea5e9;
}

.newsfeed-ad-option {
  background-color: #f0fdf4;
  border-left: 3px solid #22c55e;
}

.discount-input-container {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  border-left: 3px solid #4f46e5;
}

.input-suffix {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-weight: 500;
}

.help-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.5rem;
  font-style: italic;
}

.ad-form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.25rem;
  border-top: 1px solid #e5e7eb;
}

.cancel-button {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  color: #6b7280;
  font-weight: 500;
}

.submit-button {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  background-color: #4f46e5;
  color: white;
  font-weight: 500;
}

.submit-button:hover {
  background-color: #4338ca;
}

@media (max-width: 768px) {
  .ads-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .ads-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-container {
    justify-content: space-between;
  }

  .ad-metrics {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .ad-details {
    grid-template-columns: 1fr;
  }
}

/* Advanced Export Modal */
.advanced-export-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.export-modal-content {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.export-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.export-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.export-title h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.export-title p {
  font-size: 0.875rem;
  opacity: 0.9;
  margin: 0;
}

.export-modal-body {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.option-section h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.format-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.format-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.format-btn:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.format-btn.active {
  border-color: #4f46e5;
  background: #f0f9ff;
  color: #4f46e5;
}

.format-btn span {
  font-weight: 600;
  display: block;
  margin-bottom: 0.25rem;
}

.format-btn small {
  color: #6b7280;
  font-size: 0.75rem;
}

.content-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.option-checkbox {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.option-checkbox:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.option-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #4f46e5;
}

.export-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.cancel-btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid #e5e7eb;
  background: white;
  color: #374151;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.export-btn:hover {
  background: #4338ca;
}
