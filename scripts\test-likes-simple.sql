-- Script de test simple pour vérifier le système de likes

-- =====================================================
-- 1. VÉRIFICATION DE LA STRUCTURE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 VÉRIFICATION DE LA STRUCTURE';
    RAISE NOTICE '==============================';
    
    -- Vérifier que la colonne likes existe
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' 
        AND column_name = 'likes'
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '✅ Colonne "likes" trouvée';
    ELSE
        RAISE NOTICE '❌ Colonne "likes" manquante';
    END IF;
    
    -- Vérifier que la colonne shares existe
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' 
        AND column_name = 'shares'
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '✅ Colonne "shares" trouvée';
    ELSE
        RAISE NOTICE '❌ Colonne "shares" manquante';
    END IF;
END $$;

-- =====================================================
-- 2. VÉRIFICATION DES INDEX
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🚀 VÉRIFICATION DES INDEX';
    RAISE NOTICE '=========================';
    
    -- Vérifier l'index des likes
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'posts' 
        AND indexname = 'posts_likes_idx'
    ) THEN
        RAISE NOTICE '✅ Index "posts_likes_idx" trouvé';
    ELSE
        RAISE NOTICE '❌ Index "posts_likes_idx" manquant';
    END IF;
    
    -- Vérifier l'index des shares
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'posts' 
        AND indexname = 'posts_shares_idx'
    ) THEN
        RAISE NOTICE '✅ Index "posts_shares_idx" trouvé';
    ELSE
        RAISE NOTICE '❌ Index "posts_shares_idx" manquant';
    END IF;
END $$;

-- =====================================================
-- 3. VÉRIFICATION DES DONNÉES
-- =====================================================

DO $$
DECLARE
    total_posts INTEGER;
    posts_with_likes INTEGER;
    posts_with_shares INTEGER;
    sample_post RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📊 VÉRIFICATION DES DONNÉES';
    RAISE NOTICE '===========================';
    
    -- Compter le total de posts
    SELECT COUNT(*) INTO total_posts FROM posts;
    RAISE NOTICE 'Total posts: %', total_posts;
    
    -- Compter les posts avec likes initialisés
    SELECT COUNT(*) INTO posts_with_likes 
    FROM posts 
    WHERE likes IS NOT NULL;
    RAISE NOTICE 'Posts avec likes: %', posts_with_likes;
    
    -- Compter les posts avec shares initialisés
    SELECT COUNT(*) INTO posts_with_shares 
    FROM posts 
    WHERE shares IS NOT NULL;
    RAISE NOTICE 'Posts avec shares: %', posts_with_shares;
    
    -- Prendre un échantillon
    SELECT id, likes, shares, recommendations 
    INTO sample_post
    FROM posts 
    LIMIT 1;
    
    IF sample_post.id IS NOT NULL THEN
        RAISE NOTICE '';
        RAISE NOTICE '📋 ÉCHANTILLON:';
        RAISE NOTICE 'Post ID: %', sample_post.id;
        RAISE NOTICE 'Likes: %', sample_post.likes;
        RAISE NOTICE 'Shares: %', sample_post.shares;
        RAISE NOTICE 'Recommendations: %', sample_post.recommendations;
    END IF;
END $$;

-- =====================================================
-- 4. TEST FONCTIONNEL (SIMULATION)
-- =====================================================

DO $$
DECLARE
    test_post_id UUID;
    test_user_id UUID := '12345678-1234-1234-1234-123456789012'; -- UUID fictif pour test
    initial_likes UUID[];
    updated_likes UUID[];
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🧪 TEST FONCTIONNEL';
    RAISE NOTICE '===================';
    
    -- Prendre le premier post pour le test
    SELECT id, likes INTO test_post_id, initial_likes 
    FROM posts 
    LIMIT 1;
    
    IF test_post_id IS NOT NULL THEN
        RAISE NOTICE 'Test sur le post: %', test_post_id;
        RAISE NOTICE 'Likes initiaux: %', initial_likes;
        
        -- Simuler l'ajout d'un like
        UPDATE posts 
        SET likes = array_append(COALESCE(likes, ARRAY[]::UUID[]), test_user_id)
        WHERE id = test_post_id;
        
        -- Vérifier le résultat
        SELECT likes INTO updated_likes 
        FROM posts 
        WHERE id = test_post_id;
        
        RAISE NOTICE 'Likes après ajout: %', updated_likes;
        
        -- Remettre l'état initial
        UPDATE posts 
        SET likes = initial_likes
        WHERE id = test_post_id;
        
        RAISE NOTICE '✅ Test fonctionnel réussi - Le système peut ajouter des likes';
    ELSE
        RAISE NOTICE '⚠️ Aucun post disponible pour le test';
    END IF;
END $$;

-- =====================================================
-- 5. RÉSUMÉ FINAL
-- =====================================================

DO $$
DECLARE
    total_posts INTEGER;
    total_users INTEGER;
    structure_ok BOOLEAN := true;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎯 RÉSUMÉ FINAL';
    RAISE NOTICE '===============';
    
    -- Vérifications finales
    SELECT COUNT(*) INTO total_posts FROM posts;
    SELECT COUNT(*) INTO total_users FROM profiles;
    
    -- Vérifier la structure
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'likes'
    ) THEN
        structure_ok := false;
    END IF;
    
    RAISE NOTICE 'Posts disponibles: %', total_posts;
    RAISE NOTICE 'Utilisateurs disponibles: %', total_users;
    RAISE NOTICE 'Structure correcte: %', CASE WHEN structure_ok THEN 'OUI' ELSE 'NON' END;
    
    IF structure_ok AND total_posts > 0 THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 SYSTÈME DE LIKES PRÊT !';
        RAISE NOTICE '===========================';
        RAISE NOTICE '✅ Structure de base de données correcte';
        RAISE NOTICE '✅ Posts disponibles pour les tests';
        RAISE NOTICE '✅ Le bouton "J''aime" devrait fonctionner';
        RAISE NOTICE '';
        RAISE NOTICE '📱 TESTEZ MAINTENANT:';
        RAISE NOTICE '1. Redémarrez votre application';
        RAISE NOTICE '2. Connectez-vous';
        RAISE NOTICE '3. Cliquez sur "J''aime" sur un post';
        RAISE NOTICE '4. Vérifiez que le compteur change';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '❌ PROBLÈME DÉTECTÉ';
        RAISE NOTICE '===================';
        RAISE NOTICE 'Veuillez d''abord exécuter le script add-likes-simple.sql';
    END IF;
END $$;
