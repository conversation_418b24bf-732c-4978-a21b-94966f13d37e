-- CONFIGURATION COMPLÈTE DE LA GESTION DES UTILISATEURS
-- Exécutez ce script dans Supabase SQL Editor

-- =====================================================
-- 1. AJ<PERSON><PERSON><PERSON> LES COLONNES MANQUANTES DANS PROFILES
-- =====================================================

DO $$
BEGIN
    -- Ajouter les colonnes manquantes si elles n'existent pas
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'following_count') THEN
        ALTER TABLE profiles ADD COLUMN following_count INTEGER DEFAULT 0;
        RAISE NOTICE '✅ Colonne following_count ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'comment_count') THEN
        ALTER TABLE profiles ADD COLUMN comment_count INTEGER DEFAULT 0;
        RAISE NOTICE '✅ Colonne comment_count ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'recommendations_count') THEN
        ALTER TABLE profiles ADD COLUMN recommendations_count INTEGER DEFAULT 0;
        RAISE NOTICE '✅ Colonne recommendations_count ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'age') THEN
        ALTER TABLE profiles ADD COLUMN age INTEGER;
        RAISE NOTICE '✅ Colonne age ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'gender') THEN
        ALTER TABLE profiles ADD COLUMN gender TEXT;
        RAISE NOTICE '✅ Colonne gender ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'bio') THEN
        ALTER TABLE profiles ADD COLUMN bio TEXT;
        RAISE NOTICE '✅ Colonne bio ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'website') THEN
        ALTER TABLE profiles ADD COLUMN website TEXT;
        RAISE NOTICE '✅ Colonne website ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'phone') THEN
        ALTER TABLE profiles ADD COLUMN phone TEXT;
        RAISE NOTICE '✅ Colonne phone ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'profession') THEN
        ALTER TABLE profiles ADD COLUMN profession TEXT;
        RAISE NOTICE '✅ Colonne profession ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'interests') THEN
        ALTER TABLE profiles ADD COLUMN interests TEXT;
        RAISE NOTICE '✅ Colonne interests ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'profile_completed_at') THEN
        ALTER TABLE profiles ADD COLUMN profile_completed_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE '✅ Colonne profile_completed_at ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'suspended_until') THEN
        ALTER TABLE profiles ADD COLUMN suspended_until TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE '✅ Colonne suspended_until ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'suspension_reason') THEN
        ALTER TABLE profiles ADD COLUMN suspension_reason TEXT;
        RAISE NOTICE '✅ Colonne suspension_reason ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'last_login') THEN
        ALTER TABLE profiles ADD COLUMN last_login TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE '✅ Colonne last_login ajoutée';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'is_verified') THEN
        ALTER TABLE profiles ADD COLUMN is_verified BOOLEAN DEFAULT false;
        RAISE NOTICE '✅ Colonne is_verified ajoutée';
    END IF;
END $$;

-- =====================================================
-- 2. AJOUTER LES COLONNES MANQUANTES DANS BUSINESS_PROFILES
-- =====================================================

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'business_profiles' AND column_name = 'business_verified') THEN
        ALTER TABLE business_profiles ADD COLUMN business_verified BOOLEAN DEFAULT false;
        RAISE NOTICE '✅ Colonne business_verified ajoutée à business_profiles';
    END IF;
END $$;

-- =====================================================
-- 3. CRÉER UNE VUE POUR LES STATISTIQUES UTILISATEURS
-- =====================================================

CREATE OR REPLACE VIEW user_statistics AS
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN role = 'standard' THEN 1 END) as standard_users,
    COUNT(CASE WHEN role = 'business' THEN 1 END) as business_users,
    COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
    COUNT(CASE WHEN is_verified = true THEN 1 END) as verified_users,
    COUNT(CASE WHEN suspended_until IS NOT NULL AND suspended_until > NOW() THEN 1 END) as suspended_users,
    COUNT(CASE WHEN created_at >= CURRENT_DATE THEN 1 END) as new_today,
    COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as new_this_week,
    COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as new_this_month,
    COUNT(CASE WHEN last_login >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as active_this_week,
    COUNT(CASE WHEN last_login >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as active_this_month,
    AVG(post_count) as avg_posts_per_user,
    AVG(followers_count) as avg_followers_per_user,
    AVG(following_count) as avg_following_per_user
FROM profiles;

-- Donner les permissions sur la vue
GRANT SELECT ON user_statistics TO authenticated;

-- =====================================================
-- 4. CRÉER DES FONCTIONS UTILITAIRES
-- =====================================================

-- Fonction pour mettre à jour les compteurs d'activité
CREATE OR REPLACE FUNCTION update_user_activity_counts()
RETURNS void AS $$
BEGIN
    -- Mettre à jour les compteurs de posts
    UPDATE profiles SET post_count = (
        SELECT COUNT(*) FROM posts WHERE posts.user_id = profiles.id
    );
    
    -- Mettre à jour les compteurs de followers
    UPDATE profiles SET followers_count = (
        SELECT COUNT(*) FROM followers WHERE followers.following_id = profiles.id
    );
    
    -- Mettre à jour les compteurs de following
    UPDATE profiles SET following_count = (
        SELECT COUNT(*) FROM followers WHERE followers.follower_id = profiles.id
    );
    
    RAISE NOTICE 'Compteurs d''activité mis à jour pour tous les utilisateurs';
END;
$$ LANGUAGE plpgsql;

-- Fonction pour suspendre un utilisateur
CREATE OR REPLACE FUNCTION suspend_user(
    p_user_id uuid,
    p_duration_days integer DEFAULT 7,
    p_reason text DEFAULT 'Suspension administrative'
)
RETURNS boolean AS $$
BEGIN
    UPDATE profiles 
    SET 
        suspended_until = NOW() + (p_duration_days || ' days')::interval,
        suspension_reason = p_reason,
        updated_at = NOW()
    WHERE id = p_user_id;
    
    IF FOUND THEN
        RAISE NOTICE 'Utilisateur % suspendu pour % jours', p_user_id, p_duration_days;
        RETURN true;
    ELSE
        RAISE NOTICE 'Utilisateur % non trouvé', p_user_id;
        RETURN false;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour réactiver un utilisateur
CREATE OR REPLACE FUNCTION reactivate_user(p_user_id uuid)
RETURNS boolean AS $$
BEGIN
    UPDATE profiles 
    SET 
        suspended_until = NULL,
        suspension_reason = NULL,
        updated_at = NOW()
    WHERE id = p_user_id;
    
    IF FOUND THEN
        RAISE NOTICE 'Utilisateur % réactivé', p_user_id;
        RETURN true;
    ELSE
        RAISE NOTICE 'Utilisateur % non trouvé', p_user_id;
        RETURN false;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. CRÉER DES DONNÉES DE TEST
-- =====================================================

DO $$
DECLARE
    test_user_id uuid;
    i integer;
BEGIN
    -- Créer quelques utilisateurs de test avec des données variées
    FOR i IN 1..5 LOOP
        INSERT INTO profiles (
            username,
            email,
            role,
            status,
            country,
            city,
            age,
            gender,
            bio,
            profession,
            is_verified,
            post_count,
            followers_count,
            following_count,
            created_at,
            updated_at
        ) VALUES (
            'user_test_' || i,
            'test' || i || '@customeroom.demo',
            CASE 
                WHEN i = 1 THEN 'business'
                WHEN i = 2 THEN 'admin'
                ELSE 'standard'
            END,
            CASE 
                WHEN i <= 2 THEN 'leader'
                WHEN i <= 4 THEN 'contributor'
                ELSE 'member'
            END,
            CASE 
                WHEN i = 1 THEN 'France'
                WHEN i = 2 THEN 'Sénégal'
                WHEN i = 3 THEN 'Côte d''Ivoire'
                WHEN i = 4 THEN 'Mali'
                ELSE 'Burkina Faso'
            END,
            CASE 
                WHEN i = 1 THEN 'Paris'
                WHEN i = 2 THEN 'Dakar'
                WHEN i = 3 THEN 'Abidjan'
                WHEN i = 4 THEN 'Bamako'
                ELSE 'Ouagadougou'
            END,
            20 + (i * 5),
            CASE WHEN i % 2 = 0 THEN 'Homme' ELSE 'Femme' END,
            'Bio de test pour l''utilisateur ' || i,
            CASE 
                WHEN i = 1 THEN 'Entrepreneur'
                WHEN i = 2 THEN 'Développeur'
                WHEN i = 3 THEN 'Designer'
                WHEN i = 4 THEN 'Marketing'
                ELSE 'Étudiant'
            END,
            i <= 3,
            i * 10,
            i * 25,
            i * 15,
            NOW() - (i || ' days')::interval,
            NOW()
        )
        ON CONFLICT (email) DO NOTHING;
    END LOOP;
    
    RAISE NOTICE '✅ Utilisateurs de test créés';
END $$;

-- =====================================================
-- 6. MESSAGE FINAL ET STATISTIQUES
-- =====================================================

DO $$
DECLARE
    stats_record RECORD;
BEGIN
    -- Récupérer les statistiques
    SELECT * INTO stats_record FROM user_statistics;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '🎉 GESTION DES UTILISATEURS DÉPLOYÉE !';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '📊 STATISTIQUES ACTUELLES:';
    RAISE NOTICE '   - Total utilisateurs: %', stats_record.total_users;
    RAISE NOTICE '   - Utilisateurs standard: %', stats_record.standard_users;
    RAISE NOTICE '   - Entreprises: %', stats_record.business_users;
    RAISE NOTICE '   - Administrateurs: %', stats_record.admin_users;
    RAISE NOTICE '   - Vérifiés: %', stats_record.verified_users;
    RAISE NOTICE '   - Suspendus: %', stats_record.suspended_users;
    RAISE NOTICE '   - Nouveaux aujourd''hui: %', stats_record.new_today;
    RAISE NOTICE '';
    RAISE NOTICE '✅ Toutes les colonnes ajoutées';
    RAISE NOTICE '✅ Vue user_statistics créée';
    RAISE NOTICE '✅ Fonctions utilitaires créées';
    RAISE NOTICE '✅ Données de test créées';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 FONCTIONNALITÉS DISPONIBLES:';
    RAISE NOTICE '   - Recherche et filtres avancés';
    RAISE NOTICE '   - Tri par différents critères';
    RAISE NOTICE '   - Actions individuelles et en lot';
    RAISE NOTICE '   - Détails complets des utilisateurs';
    RAISE NOTICE '   - Suspension/Réactivation';
    RAISE NOTICE '   - Vérification des comptes';
    RAISE NOTICE '   - Export des données';
    RAISE NOTICE '   - Pagination intelligente';
    RAISE NOTICE '   - Statistiques en temps réel';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 ALLEZ DANS L''ONGLET "GESTION UTILISATEURS" !';
    RAISE NOTICE '';
END $$;
