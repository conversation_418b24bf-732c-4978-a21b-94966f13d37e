import React from 'react';
import { TrendingUp, TrendingDown, Calendar, Award } from 'lucide-react';
import { formatAmount } from '../../utils/formatUtils';

interface OrderInsightsProps {
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  deliveredOrders: number;
}

const OrderInsights: React.FC<OrderInsightsProps> = ({
  totalOrders,
  totalSpent,
  averageOrderValue,
  deliveredOrders
}) => {
  // Calculs pour les insights
  const deliveryRate = totalOrders > 0 ? (deliveredOrders / totalOrders) * 100 : 0;
  const monthlyAverage = totalSpent / 12; // Estimation mensuelle
  const isGoodCustomer = totalSpent > 50000; // Seuil pour être considéré comme bon client
  
  const insights = [
    {
      title: 'Taux de livraison',
      value: `${deliveryRate.toFixed(1)}%`,
      description: 'Commandes livrées avec succès',
      icon: Award,
      color: deliveryRate >= 80 ? 'green' : deliveryRate >= 60 ? 'yellow' : 'red',
      trend: deliveryRate >= 80 ? 'up' : 'down'
    },
    {
      title: 'Dépense mensuelle',
      value: formatAmount(monthlyAverage),
      description: 'Moyenne estimée par mois',
      icon: Calendar,
      color: 'blue',
      trend: monthlyAverage > 10000 ? 'up' : 'down'
    },
    {
      title: 'Statut client',
      value: isGoodCustomer ? 'Premium' : 'Standard',
      description: isGoodCustomer ? 'Client fidèle' : 'Nouveau client',
      icon: TrendingUp,
      color: isGoodCustomer ? 'purple' : 'gray',
      trend: isGoodCustomer ? 'up' : 'neutral'
    }
  ];

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'green':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'yellow':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'red':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'blue':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'purple':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getIconColorClasses = (color: string) => {
    switch (color) {
      case 'green':
        return 'text-green-600';
      case 'yellow':
        return 'text-yellow-600';
      case 'red':
        return 'text-red-600';
      case 'blue':
        return 'text-blue-600';
      case 'purple':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-3 h-3 text-green-500" />;
      case 'down':
        return <TrendingDown className="w-3 h-3 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Aperçu de vos achats</h3>
        <span className="text-sm text-gray-500">Basé sur votre historique</span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {insights.map((insight, index) => {
          const IconComponent = insight.icon;
          return (
            <div
              key={index}
              className={`p-4 rounded-lg border ${getColorClasses(insight.color)}`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <IconComponent className={`w-5 h-5 ${getIconColorClasses(insight.color)}`} />
                  <span className="font-medium text-sm">{insight.title}</span>
                </div>
                {getTrendIcon(insight.trend)}
              </div>
              <div className="mb-1">
                <span className="text-xl font-bold">{insight.value}</span>
              </div>
              <p className="text-xs opacity-75">{insight.description}</p>
            </div>
          );
        })}
      </div>

      {/* Conseils personnalisés */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Conseils personnalisés</h4>
        <div className="space-y-2">
          {deliveryRate < 80 && (
            <div className="flex items-start space-x-2 text-sm">
              <div className="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
              <p className="text-gray-600">
                Vérifiez vos informations de livraison pour améliorer le taux de succès
              </p>
            </div>
          )}
          {!isGoodCustomer && (
            <div className="flex items-start space-x-2 text-sm">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
              <p className="text-gray-600">
                Découvrez nos offres spéciales pour économiser sur vos prochains achats
              </p>
            </div>
          )}
          {totalOrders >= 5 && (
            <div className="flex items-start space-x-2 text-sm">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
              <p className="text-gray-600">
                Félicitations ! Vous êtes un client fidèle. Profitez de nos récompenses
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderInsights;
