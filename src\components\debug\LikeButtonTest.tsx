import React, { useState } from 'react';
import { ThumbsUp } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { usePosts } from '../../context/PostsContext';
import Card, { CardHeader, CardBody } from '../ui/Card';
import Button from '../ui/Button';

const LikeButtonTest: React.FC = () => {
  const { currentUser } = useAuth();
  const { posts, likePost } = usePosts();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testLikeButton = async () => {
    addTestResult('🧪 Début du test du bouton J\'aime');
    
    if (!currentUser) {
      addTestResult('❌ Aucun utilisateur connecté');
      return;
    }

    if (posts.length === 0) {
      addTestResult('❌ Aucun post disponible pour le test');
      return;
    }

    const firstPost = posts[0];
    addTestResult(`📝 Test sur le post: "${firstPost.title || 'Sans titre'}"`);
    addTestResult(`👤 Utilisateur: ${currentUser.username} (${currentUser.role})`);
    
    const initialLikes = firstPost.likes?.length || 0;
    addTestResult(`👍 Likes initiaux: ${initialLikes}`);
    
    try {
      await likePost(firstPost.id, currentUser.id);
      addTestResult('✅ Fonction likePost appelée avec succès');
      
      // Attendre un peu pour voir la mise à jour
      setTimeout(() => {
        const updatedPost = posts.find(p => p.id === firstPost.id);
        const newLikes = updatedPost?.likes?.length || 0;
        addTestResult(`👍 Likes après action: ${newLikes}`);
        
        if (newLikes !== initialLikes) {
          addTestResult('✅ Le nombre de likes a changé - Succès !');
        } else {
          addTestResult('❌ Le nombre de likes n\'a pas changé');
        }
      }, 1000);
      
    } catch (error) {
      addTestResult(`❌ Erreur lors du test: ${error}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <h2 className="text-xl font-bold text-gray-900 flex items-center">
          <ThumbsUp className="mr-2 text-blue-600" size={24} />
          Test du Bouton "J'aime"
        </h2>
      </CardHeader>
      <CardBody>
        <div className="space-y-4">
          {/* Informations utilisateur */}
          <div className="p-3 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Informations utilisateur</h3>
            {currentUser ? (
              <div className="text-sm text-blue-800">
                <p><strong>Nom:</strong> {currentUser.username}</p>
                <p><strong>Rôle:</strong> {currentUser.role}</p>
                <p><strong>ID:</strong> {currentUser.id}</p>
              </div>
            ) : (
              <p className="text-red-600">Aucun utilisateur connecté</p>
            )}
          </div>

          {/* Informations posts */}
          <div className="p-3 bg-green-50 rounded-lg">
            <h3 className="font-semibold text-green-900 mb-2">Informations posts</h3>
            <div className="text-sm text-green-800">
              <p><strong>Nombre de posts:</strong> {posts.length}</p>
              {posts.length > 0 && (
                <div className="mt-2">
                  <p><strong>Premier post:</strong></p>
                  <p className="ml-4">• Titre: {posts[0].title || 'Sans titre'}</p>
                  <p className="ml-4">• Likes: {posts[0].likes?.length || 0}</p>
                  <p className="ml-4">• ID: {posts[0].id}</p>
                </div>
              )}
            </div>
          </div>

          {/* Boutons de test */}
          <div className="flex space-x-3">
            <Button 
              onClick={testLikeButton}
              variant="primary"
              disabled={!currentUser || posts.length === 0}
            >
              🧪 Tester le bouton J'aime
            </Button>
            <Button 
              onClick={clearResults}
              variant="outline"
            >
              🗑️ Effacer les résultats
            </Button>
          </div>

          {/* Résultats du test */}
          {testResults.length > 0 && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">Résultats du test</h3>
              <div className="text-sm space-y-1 max-h-60 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="font-mono text-xs p-1 bg-white rounded">
                    {result}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardBody>
    </Card>
  );
};

export default LikeButtonTest;
