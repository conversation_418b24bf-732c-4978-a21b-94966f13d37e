import { supabase } from '../lib/supabase';

export interface PaymentMethod {
  id: string;
  type: 'card' | 'mobile_money' | 'bank_transfer';
  provider: string; // Visa, Mastercard, Orange Money, MTN Money, etc.
  last4?: string;
  expiryMonth?: number;
  expiryYear?: number;
  phoneNumber?: string;
  isDefault: boolean;
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'canceled';
  paymentMethod: PaymentMethod;
  businessId: string;
  planId: string;
  createdAt: string;
  metadata?: any;
}

export interface PaymentReceipt {
  id: string;
  paymentIntentId: string;
  businessId: string;
  planId: string;
  planName: string;
  amount: number;
  currency: string;
  paymentMethod: PaymentMethod;
  transactionId: string;
  status: 'paid' | 'refunded' | 'partially_refunded';
  paidAt: string;
  businessInfo: {
    name: string;
    email: string;
    address?: string;
    taxId?: string;
  };
  invoiceNumber: string;
  receiptUrl?: string;
}

export class PaymentService {
  
  /**
   * Crée une intention de paiement
   */
  static async createPaymentIntent(
    businessId: string, 
    planId: string, 
    amount: number,
    paymentMethodId: string
  ): Promise<PaymentIntent> {
    try {
      console.log('💳 Création d\'une intention de paiement:', { businessId, planId, amount });

      // Récupérer la méthode de paiement
      const paymentMethod = await this.getPaymentMethod(paymentMethodId);
      if (!paymentMethod) {
        throw new Error('Méthode de paiement non trouvée');
      }

      // Créer l'intention de paiement
      const paymentIntent: PaymentIntent = {
        id: `pi_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        amount,
        currency: 'XOF', // Franc CFA
        status: 'pending',
        paymentMethod,
        businessId,
        planId,
        createdAt: new Date().toISOString(),
        metadata: {
          planId,
          businessId,
          subscriptionType: 'monthly'
        }
      };

      // Sauvegarder en base de données
      const { error } = await supabase
        .from('payment_intents')
        .insert({
          id: paymentIntent.id,
          business_id: businessId,
          plan_id: planId,
          amount,
          currency: paymentIntent.currency,
          status: paymentIntent.status,
          payment_method_id: paymentMethodId,
          metadata: paymentIntent.metadata,
          created_at: paymentIntent.createdAt
        });

      if (error) {
        console.error('❌ Erreur lors de la sauvegarde de l\'intention de paiement:', error);
        throw error;
      }

      console.log('✅ Intention de paiement créée:', paymentIntent.id);
      return paymentIntent;

    } catch (error) {
      console.error('❌ Erreur dans createPaymentIntent:', error);
      throw error;
    }
  }

  /**
   * Confirme un paiement
   */
  static async confirmPayment(paymentIntentId: string): Promise<PaymentIntent> {
    try {
      console.log('🔄 Confirmation du paiement:', paymentIntentId);

      // Simuler le processus de paiement avec différents fournisseurs
      const success = await this.processPaymentWithProvider(paymentIntentId);

      const status = success ? 'succeeded' : 'failed';

      // Mettre à jour le statut
      const { error } = await supabase
        .from('payment_intents')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', paymentIntentId);

      if (error) {
        console.error('❌ Erreur lors de la mise à jour du paiement:', error);
        throw error;
      }

      // Récupérer l'intention mise à jour
      const updatedIntent = await this.getPaymentIntent(paymentIntentId);
      
      if (success && updatedIntent) {
        // Générer le reçu de paiement
        await this.generatePaymentReceipt(updatedIntent);
        
        // Activer l'abonnement
        await this.activateSubscription(updatedIntent);
      }

      console.log(`✅ Paiement ${success ? 'réussi' : 'échoué'}:`, paymentIntentId);
      return updatedIntent!;

    } catch (error) {
      console.error('❌ Erreur dans confirmPayment:', error);
      throw error;
    }
  }

  /**
   * Simule le traitement du paiement avec différents fournisseurs
   */
  private static async processPaymentWithProvider(paymentIntentId: string): Promise<boolean> {
    // Simuler un délai de traitement
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simuler un taux de succès de 95%
    const success = Math.random() > 0.05;
    
    console.log(`💳 Traitement du paiement ${paymentIntentId}: ${success ? 'Succès' : 'Échec'}`);
    return success;
  }

  /**
   * Génère un reçu de paiement
   */
  static async generatePaymentReceipt(paymentIntent: PaymentIntent): Promise<PaymentReceipt> {
    try {
      console.log('🧾 Génération du reçu de paiement pour:', paymentIntent.id);

      // Récupérer les informations de l'entreprise
      const { data: businessInfo, error: businessError } = await supabase
        .from('business_profiles')
        .select('business_name, email, address, tax_id')
        .eq('id', paymentIntent.businessId)
        .single();

      if (businessError) {
        console.error('❌ Erreur lors de la récupération des infos business:', businessError);
      }

      // Récupérer les informations du plan
      const { data: planInfo, error: planError } = await supabase
        .from('subscription_plans')
        .select('name')
        .eq('id', paymentIntent.planId)
        .single();

      if (planError) {
        console.error('❌ Erreur lors de la récupération des infos plan:', planError);
      }

      const receipt: PaymentReceipt = {
        id: `receipt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        paymentIntentId: paymentIntent.id,
        businessId: paymentIntent.businessId,
        planId: paymentIntent.planId,
        planName: planInfo?.name || 'Plan inconnu',
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        paymentMethod: paymentIntent.paymentMethod,
        transactionId: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        status: 'paid',
        paidAt: new Date().toISOString(),
        businessInfo: {
          name: businessInfo?.business_name || 'Entreprise inconnue',
          email: businessInfo?.email || '',
          address: businessInfo?.address,
          taxId: businessInfo?.tax_id
        },
        invoiceNumber: `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
        receiptUrl: `https://customeroom.com/receipts/${paymentIntent.id}.pdf`
      };

      // Sauvegarder le reçu
      const { error } = await supabase
        .from('payment_receipts')
        .insert({
          id: receipt.id,
          payment_intent_id: receipt.paymentIntentId,
          business_id: receipt.businessId,
          plan_id: receipt.planId,
          plan_name: receipt.planName,
          amount: receipt.amount,
          currency: receipt.currency,
          payment_method: receipt.paymentMethod,
          transaction_id: receipt.transactionId,
          status: receipt.status,
          paid_at: receipt.paidAt,
          business_info: receipt.businessInfo,
          invoice_number: receipt.invoiceNumber,
          receipt_url: receipt.receiptUrl
        });

      if (error) {
        console.error('❌ Erreur lors de la sauvegarde du reçu:', error);
        throw error;
      }

      console.log('✅ Reçu de paiement généré:', receipt.id);
      return receipt;

    } catch (error) {
      console.error('❌ Erreur dans generatePaymentReceipt:', error);
      throw error;
    }
  }

  /**
   * Active l'abonnement après paiement réussi
   */
  private static async activateSubscription(paymentIntent: PaymentIntent): Promise<void> {
    try {
      const { SubscriptionService } = await import('./subscriptionService');
      await SubscriptionService.subscribeToPlan(paymentIntent.businessId, paymentIntent.planId);
      console.log('✅ Abonnement activé pour:', paymentIntent.businessId);
    } catch (error) {
      console.error('❌ Erreur lors de l\'activation de l\'abonnement:', error);
    }
  }

  /**
   * Récupère une intention de paiement
   */
  static async getPaymentIntent(paymentIntentId: string): Promise<PaymentIntent | null> {
    try {
      const { data, error } = await supabase
        .from('payment_intents')
        .select('*')
        .eq('id', paymentIntentId)
        .single();

      if (error) {
        console.error('❌ Erreur lors de la récupération de l\'intention:', error);
        return null;
      }

      // Récupérer la méthode de paiement
      const paymentMethod = await this.getPaymentMethod(data.payment_method_id);

      return {
        id: data.id,
        amount: data.amount,
        currency: data.currency,
        status: data.status,
        paymentMethod: paymentMethod!,
        businessId: data.business_id,
        planId: data.plan_id,
        createdAt: data.created_at,
        metadata: data.metadata
      };

    } catch (error) {
      console.error('❌ Erreur dans getPaymentIntent:', error);
      return null;
    }
  }

  /**
   * Récupère une méthode de paiement
   */
  static async getPaymentMethod(paymentMethodId: string): Promise<PaymentMethod | null> {
    try {
      // Pour la démo, retourner une méthode de paiement simulée
      const mockPaymentMethods: { [key: string]: PaymentMethod } = {
        'pm_card_visa': {
          id: 'pm_card_visa',
          type: 'card',
          provider: 'Visa',
          last4: '4242',
          expiryMonth: 12,
          expiryYear: 2025,
          isDefault: true
        },
        'pm_orange_money': {
          id: 'pm_orange_money',
          type: 'mobile_money',
          provider: 'Orange Money',
          phoneNumber: '+225 07 XX XX XX XX',
          isDefault: false
        },
        'pm_mtn_money': {
          id: 'pm_mtn_money',
          type: 'mobile_money',
          provider: 'MTN Money',
          phoneNumber: '+225 05 XX XX XX XX',
          isDefault: false
        }
      };

      return mockPaymentMethods[paymentMethodId] || null;

    } catch (error) {
      console.error('❌ Erreur dans getPaymentMethod:', error);
      return null;
    }
  }

  /**
   * Récupère le reçu de paiement
   */
  static async getPaymentReceipt(paymentIntentId: string): Promise<PaymentReceipt | null> {
    try {
      const { data, error } = await supabase
        .from('payment_receipts')
        .select('*')
        .eq('payment_intent_id', paymentIntentId)
        .single();

      if (error) {
        console.error('❌ Erreur lors de la récupération du reçu:', error);
        return null;
      }

      return {
        id: data.id,
        paymentIntentId: data.payment_intent_id,
        businessId: data.business_id,
        planId: data.plan_id,
        planName: data.plan_name,
        amount: data.amount,
        currency: data.currency,
        paymentMethod: data.payment_method,
        transactionId: data.transaction_id,
        status: data.status,
        paidAt: data.paid_at,
        businessInfo: data.business_info,
        invoiceNumber: data.invoice_number,
        receiptUrl: data.receipt_url
      };

    } catch (error) {
      console.error('❌ Erreur dans getPaymentReceipt:', error);
      return null;
    }
  }

  /**
   * Liste les méthodes de paiement disponibles
   */
  static getAvailablePaymentMethods(): PaymentMethod[] {
    return [
      {
        id: 'pm_card_visa',
        type: 'card',
        provider: 'Visa',
        last4: '4242',
        expiryMonth: 12,
        expiryYear: 2025,
        isDefault: true
      },
      {
        id: 'pm_orange_money',
        type: 'mobile_money',
        provider: 'Orange Money',
        phoneNumber: '+225 07 XX XX XX XX',
        isDefault: false
      },
      {
        id: 'pm_mtn_money',
        type: 'mobile_money',
        provider: 'MTN Money',
        phoneNumber: '+225 05 XX XX XX XX',
        isDefault: false
      }
    ];
  }
}
