import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import { CheckCircle, XCircle, AlertCircle, RefreshCw, Database } from 'lucide-react';

interface CampaignInfo {
  id: string;
  title: string;
  status: string;
  impressions: number;
  clicks: number;
  created_at: string;
}

interface InteractionCounts {
  campaign_id: string;
  likes_count: number;
  comments_count: number;
  shares_count: number;
}

const AdCampaignDiagnostic: React.FC = () => {
  const [campaigns, setCampaigns] = useState<CampaignInfo[]>([]);
  const [interactions, setInteractions] = useState<InteractionCounts[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testCampaignIds = [
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000002',
    '00000000-0000-0000-0000-000000000003'
  ];

  const fetchCampaigns = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Récupérer les campagnes
      const { data: campaignsData, error: campaignsError } = await supabase
        .from('ad_campaigns')
        .select('id, title, status, impressions, clicks, created_at')
        .in('id', testCampaignIds);

      if (campaignsError) throw campaignsError;

      setCampaigns(campaignsData || []);

      // Récupérer les interactions pour chaque campagne
      const interactionPromises = testCampaignIds.map(async (campaignId) => {
        const [likesResult, commentsResult, sharesResult] = await Promise.all([
          supabase.from('ad_likes').select('id').eq('campaign_id', campaignId),
          supabase.from('ad_comments').select('id').eq('campaign_id', campaignId),
          supabase.from('ad_shares').select('id').eq('campaign_id', campaignId)
        ]);

        return {
          campaign_id: campaignId,
          likes_count: likesResult.data?.length || 0,
          comments_count: commentsResult.data?.length || 0,
          shares_count: sharesResult.data?.length || 0
        };
      });

      const interactionResults = await Promise.all(interactionPromises);
      setInteractions(interactionResults);

    } catch (err) {
      console.error('Erreur lors de la récupération des données:', err);
      setError(err instanceof Error ? err.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  };

  const createTestCampaigns = async () => {
    setLoading(true);
    setError(null);

    try {
      // Créer un profil business de test
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: '11111111-1111-1111-1111-111111111111',
          username: 'olgane_cosmetics',
          email: '<EMAIL>',
          role: 'business',
          full_name: 'Olgane Cosmetics'
        });

      if (profileError && profileError.code !== '23505') { // Ignorer l'erreur de duplication
        throw profileError;
      }

      // Créer les campagnes de test
      const campaignsToCreate = [
        {
          id: '00000000-0000-0000-0000-000000000001',
          business_id: '11111111-1111-1111-1111-111111111111',
          title: 'Promotion Été - Huile de Beauté',
          description: 'Profitez de notre offre spéciale été avec 20% de réduction sur notre huile de beauté Olgane.',
          image_url: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
          target_url: 'https://example.com/promo-ete',
          bid_amount: 250.00,
          daily_budget: 5000.00,
          total_budget: 50000.00,
          placements: ['newsfeed', 'sidebar'],
          status: 'active',
          impressions: 150,
          clicks: 8
        },
        {
          id: '00000000-0000-0000-0000-000000000002',
          business_id: '11111111-1111-1111-1111-111111111111',
          title: 'Lancement Crème Hydratante',
          description: 'Découvrez notre nouvelle crème hydratante visage pour une peau éclatante et revitalisée.',
          image_url: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
          target_url: 'https://example.com/creme-hydratante',
          bid_amount: 200.00,
          daily_budget: 4000.00,
          total_budget: 40000.00,
          placements: ['newsfeed', 'marketplace'],
          status: 'active',
          impressions: 120,
          clicks: 6
        },
        {
          id: '00000000-0000-0000-0000-000000000003',
          business_id: '11111111-1111-1111-1111-111111111111',
          title: 'Promo Flash Sérum Anti-âge',
          description: 'Offre flash de 48h : -30% sur notre sérum anti-âge révolutionnaire.',
          image_url: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
          target_url: 'https://example.com/serum-anti-age',
          bid_amount: 300.00,
          daily_budget: 7000.00,
          total_budget: 70000.00,
          placements: ['newsfeed', 'sidebar', 'marketplace'],
          status: 'active',
          impressions: 200,
          clicks: 12
        }
      ];

      const { error: campaignsError } = await supabase
        .from('ad_campaigns')
        .upsert(campaignsToCreate);

      if (campaignsError) throw campaignsError;

      // Rafraîchir les données
      await fetchCampaigns();

    } catch (err) {
      console.error('Erreur lors de la création des campagnes:', err);
      setError(err instanceof Error ? err.message : 'Erreur lors de la création');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCampaigns();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="text-green-500" size={16} />;
      case 'paused':
        return <AlertCircle className="text-yellow-500" size={16} />;
      default:
        return <XCircle className="text-red-500" size={16} />;
    }
  };

  const getInteractionForCampaign = (campaignId: string) => {
    return interactions.find(i => i.campaign_id === campaignId) || {
      campaign_id: campaignId,
      likes_count: 0,
      comments_count: 0,
      shares_count: 0
    };
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* En-tête */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Database className="text-blue-600" size={24} />
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  Diagnostic des Campagnes Publicitaires
                </h1>
                <p className="text-gray-600">
                  Vérification de l'état des campagnes de test et des interactions
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                leftIcon={<RefreshCw size={16} />}
                onClick={fetchCampaigns}
                disabled={loading}
              >
                Actualiser
              </Button>
              <Button
                variant="primary"
                onClick={createTestCampaigns}
                disabled={loading}
              >
                Créer Campagnes Test
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Erreur */}
      {error && (
        <Card>
          <CardBody>
            <div className="flex items-center gap-2 text-red-700">
              <XCircle size={16} />
              <span>Erreur : {error}</span>
            </div>
          </CardBody>
        </Card>
      )}

      {/* État des campagnes */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">État des Campagnes de Test</h2>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="text-center py-4">
              <RefreshCw className="animate-spin mx-auto mb-2" size={24} />
              <p>Chargement...</p>
            </div>
          ) : campaigns.length === 0 ? (
            <div className="text-center py-8">
              <XCircle className="mx-auto mb-2 text-red-500" size={48} />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Aucune campagne trouvée
              </h3>
              <p className="text-gray-600 mb-4">
                Les campagnes de test n'existent pas en base de données.
              </p>
              <Button onClick={createTestCampaigns} disabled={loading}>
                Créer les Campagnes de Test
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {campaigns.map((campaign) => {
                const interaction = getInteractionForCampaign(campaign.id);
                return (
                  <div key={campaign.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          {getStatusIcon(campaign.status)}
                          <h3 className="font-medium">{campaign.title}</h3>
                        </div>
                        <p className="text-sm text-gray-600">ID: {campaign.id}</p>
                      </div>
                      <span className={`px-2 py-1 rounded text-xs ${
                        campaign.status === 'active' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {campaign.status}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Impressions</p>
                        <p className="font-medium">{campaign.impressions}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Clics</p>
                        <p className="font-medium">{campaign.clicks}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Likes</p>
                        <p className="font-medium">{interaction.likes_count}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Commentaires</p>
                        <p className="font-medium">{interaction.comments_count}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Partages</p>
                        <p className="font-medium">{interaction.shares_count}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardBody>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">Instructions</h2>
        </CardHeader>
        <CardBody>
          <div className="space-y-3 text-sm">
            <div>
              <h3 className="font-medium text-green-700">✅ Si les campagnes existent :</h3>
              <p>Les boutons d'interaction devraient fonctionner. Testez sur la page de test des boutons.</p>
            </div>
            
            <div>
              <h3 className="font-medium text-red-700">❌ Si aucune campagne :</h3>
              <p>Cliquez sur "Créer Campagnes Test" pour initialiser les données nécessaires.</p>
            </div>
            
            <div>
              <h3 className="font-medium text-blue-700">🔧 Pour tester :</h3>
              <p>Allez sur <code>/ad-buttons-test</code> et testez les interactions avec les publicités.</p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default AdCampaignDiagnostic;
