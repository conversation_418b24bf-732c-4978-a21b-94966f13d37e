/* AI Chat Floating Action Button - Styles Ultra-Avancés */
.ai-chat-fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--context-color, #3b82f6), color-mix(in srgb, var(--context-color, #3b82f6) 80%, #000 20%));
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 999;
  overflow: visible;
  border: 3px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.ai-chat-fab:hover {
  transform: scale(1.1) translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

.ai-chat-fab.chat-open {
  transform: scale(0.9);
  opacity: 0.7;
  pointer-events: none;
}

/* Icône principale */
.fab-icon {
  color: white;
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

.ai-chat-fab:hover .fab-icon {
  transform: rotate(15deg) scale(1.1);
}

/* Badge de notification */
.fab-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  background: #ef4444;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
  border: 2px solid white;
  animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

/* Tooltip */
.fab-tooltip {
  position: absolute;
  right: 80px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  white-space: nowrap;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: tooltipSlide 0.3s ease;
  z-index: 1000;
}

.tooltip-content h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
}

.tooltip-content p {
  margin: 0;
  font-size: 12px;
  opacity: 0.8;
}

.tooltip-arrow {
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid rgba(0, 0, 0, 0.9);
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

@keyframes tooltipSlide {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* Effets de pulsation */
.fab-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--context-color, #3b82f6);
  opacity: 0.6;
  animation: pulse 2s infinite;
  z-index: 0;
}

.fab-pulse-2 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--context-color, #3b82f6);
  opacity: 0.4;
  animation: pulse 2s infinite 1s;
  z-index: 0;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.3);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.6);
    opacity: 0;
  }
}

/* États contextuels */
.ai-chat-fab[style*="--context-color: #10b981"] {
  /* Ventes - Vert */
}

.ai-chat-fab[style*="--context-color: #3b82f6"] {
  /* Produits - Bleu */
}

.ai-chat-fab[style*="--context-color: #f59e0b"] {
  /* Rankings - Orange */
}

.ai-chat-fab[style*="--context-color: #8b5cf6"] {
  /* Général - Violet */
}

/* Animation d'apparition */
.ai-chat-fab {
  animation: fabAppear 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes fabAppear {
  from {
    transform: scale(0) rotate(180deg);
    opacity: 0;
  }
  to {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* États d'interaction */
.ai-chat-fab:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
  .fab-tooltip {
    background: rgba(255, 255, 255, 0.9);
    color: #1f2937;
    border-color: rgba(0, 0, 0, 0.1);
  }
  
  .tooltip-arrow {
    border-left-color: rgba(255, 255, 255, 0.9);
  }
}

/* Responsive Mobile */
@media (max-width: 768px) {
  .ai-chat-fab {
    bottom: 20px;
    right: 20px;
    width: 56px;
    height: 56px;
  }
  
  .fab-icon {
    transform: scale(0.9);
  }
  
  .fab-tooltip {
    right: 70px;
    font-size: 12px;
    padding: 8px 12px;
  }
  
  .tooltip-content h4 {
    font-size: 12px;
  }
  
  .tooltip-content p {
    font-size: 10px;
  }
}

/* Animation de notification */
.ai-chat-fab.has-notification {
  animation: notification 3s ease-in-out infinite;
}

@keyframes notification {
  0%, 100% {
    transform: scale(1);
  }
  10%, 30% {
    transform: scale(1.05);
  }
  20% {
    transform: scale(1.1);
  }
}

/* Effet de brillance */
.ai-chat-fab::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.ai-chat-fab:hover::before {
  opacity: 1;
  animation: shine 1.5s ease-in-out infinite;
}

@keyframes shine {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Effet de particules (optionnel) */
.ai-chat-fab.particles::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
  opacity: 0;
  animation: particles 2s infinite;
}

@keyframes particles {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -150%) scale(1);
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -200%) scale(0);
    opacity: 0;
  }
}

/* Accessibilité */
.ai-chat-fab:focus {
  outline: 3px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

.ai-chat-fab:focus:not(:focus-visible) {
  outline: none;
}

/* Réduction de mouvement */
@media (prefers-reduced-motion: reduce) {
  .ai-chat-fab,
  .fab-pulse,
  .fab-pulse-2,
  .fab-badge {
    animation: none;
  }
  
  .ai-chat-fab:hover {
    transform: none;
  }
}
