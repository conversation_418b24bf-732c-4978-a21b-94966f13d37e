-- Script de test pour vérifier le système de likes
-- À exécuter après avoir appliqué la migration des likes

-- =====================================================
-- 1. VÉRIFICATION DE LA STRUCTURE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 VÉRIFICATION DE LA STRUCTURE DES TABLES';
    RAISE NOTICE '==========================================';
    
    -- Vérifier que la colonne likes existe
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' 
        AND column_name = 'likes'
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '✅ Colonne "likes" trouvée dans la table posts';
    ELSE
        RAISE NOTICE '❌ Colonne "likes" manquante dans la table posts';
    END IF;
    
    -- Vérifier que la colonne shares existe
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' 
        AND column_name = 'shares'
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '✅ Colonne "shares" trouvée dans la table posts';
    ELSE
        RAISE NOTICE '❌ Colonne "shares" manquante dans la table posts';
    END IF;
    
    -- Vérifier que la vue existe
    IF EXISTS (
        SELECT 1 FROM information_schema.views 
        WHERE table_name = 'posts_with_author_details'
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '✅ Vue "posts_with_author_details" trouvée';
    ELSE
        RAISE NOTICE '❌ Vue "posts_with_author_details" manquante';
    END IF;
END $$;

-- =====================================================
-- 2. VÉRIFICATION DES DONNÉES
-- =====================================================

DO $$
DECLARE
    total_posts INTEGER;
    posts_with_empty_likes INTEGER;
    posts_with_empty_shares INTEGER;
    sample_post_id UUID;
    sample_likes UUID[];
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📊 VÉRIFICATION DES DONNÉES';
    RAISE NOTICE '===========================';
    
    -- Compter le total de posts
    SELECT COUNT(*) INTO total_posts FROM posts;
    RAISE NOTICE 'Total posts: %', total_posts;
    
    -- Compter les posts avec likes vides
    SELECT COUNT(*) INTO posts_with_empty_likes 
    FROM posts 
    WHERE likes = ARRAY[]::UUID[] OR likes IS NULL;
    RAISE NOTICE 'Posts avec likes vides: %', posts_with_empty_likes;
    
    -- Compter les posts avec shares vides
    SELECT COUNT(*) INTO posts_with_empty_shares 
    FROM posts 
    WHERE shares = ARRAY[]::UUID[] OR shares IS NULL;
    RAISE NOTICE 'Posts avec shares vides: %', posts_with_empty_shares;
    
    -- Prendre un échantillon de post pour tester
    SELECT id, likes INTO sample_post_id, sample_likes 
    FROM posts 
    LIMIT 1;
    
    IF sample_post_id IS NOT NULL THEN
        RAISE NOTICE 'Échantillon post ID: %', sample_post_id;
        RAISE NOTICE 'Likes de l''échantillon: %', sample_likes;
    END IF;
END $$;

-- =====================================================
-- 3. TEST DE LA VUE posts_with_author_details
-- =====================================================

DO $$
DECLARE
    view_count INTEGER;
    sample_record RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 TEST DE LA VUE posts_with_author_details';
    RAISE NOTICE '==========================================';
    
    -- Compter les enregistrements dans la vue
    SELECT COUNT(*) INTO view_count FROM posts_with_author_details;
    RAISE NOTICE 'Enregistrements dans la vue: %', view_count;
    
    -- Prendre un échantillon
    SELECT id, likes, shares, recommendations 
    INTO sample_record
    FROM posts_with_author_details 
    LIMIT 1;
    
    IF sample_record.id IS NOT NULL THEN
        RAISE NOTICE 'Échantillon vue - ID: %', sample_record.id;
        RAISE NOTICE 'Échantillon vue - Likes: %', sample_record.likes;
        RAISE NOTICE 'Échantillon vue - Shares: %', sample_record.shares;
        RAISE NOTICE 'Échantillon vue - Recommendations: %', sample_record.recommendations;
    END IF;
END $$;

-- =====================================================
-- 4. VÉRIFICATION DES INDEX
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 VÉRIFICATION DES INDEX';
    RAISE NOTICE '=========================';
    
    -- Vérifier l'index des likes
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'posts' 
        AND indexname = 'posts_likes_idx'
    ) THEN
        RAISE NOTICE '✅ Index "posts_likes_idx" trouvé';
    ELSE
        RAISE NOTICE '❌ Index "posts_likes_idx" manquant';
    END IF;
    
    -- Vérifier l'index des recommendations
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'posts' 
        AND indexname = 'posts_recommendations_idx'
    ) THEN
        RAISE NOTICE '✅ Index "posts_recommendations_idx" trouvé';
    ELSE
        RAISE NOTICE '❌ Index "posts_recommendations_idx" manquant';
    END IF;
    
    -- Vérifier l'index des shares
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'posts' 
        AND indexname = 'posts_shares_idx'
    ) THEN
        RAISE NOTICE '✅ Index "posts_shares_idx" trouvé';
    ELSE
        RAISE NOTICE '❌ Index "posts_shares_idx" manquant';
    END IF;
END $$;

-- =====================================================
-- 5. RÉSUMÉ FINAL
-- =====================================================

DO $$
DECLARE
    total_posts INTEGER;
    total_users INTEGER;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎯 RÉSUMÉ FINAL';
    RAISE NOTICE '===============';
    
    SELECT COUNT(*) INTO total_posts FROM posts;
    SELECT COUNT(*) INTO total_users FROM profiles;
    
    RAISE NOTICE 'Posts disponibles pour les tests: %', total_posts;
    RAISE NOTICE 'Utilisateurs disponibles: %', total_users;
    RAISE NOTICE '';
    RAISE NOTICE '✅ Le système de likes est prêt à être testé !';
    RAISE NOTICE '';
    RAISE NOTICE '📝 PROCHAINES ÉTAPES:';
    RAISE NOTICE '1. Connectez-vous à l''application';
    RAISE NOTICE '2. Cliquez sur le bouton "J''aime" d''un post';
    RAISE NOTICE '3. Vérifiez que le compteur se met à jour';
    RAISE NOTICE '4. Consultez les logs dans la console du navigateur';
END $$;
