import React, { useState, useEffect } from 'react';
import { X, Check, AlertCircle } from 'lucide-react';
import { useFollow } from '../../context/FollowContext';
import { ISubscriptionPlan } from '../../types';
import Button from '../ui/Button';

interface SubscriptionPlansModalProps {
  isOpen: boolean;
  onClose: () => void;
  targetId: string;
  targetType: 'user' | 'business';
  targetName: string;
}

const SubscriptionPlansModal: React.FC<SubscriptionPlansModalProps> = ({
  isOpen,
  onClose,
  targetId,
  targetType,
  targetName
}) => {
  const { getSubscriptionPlans, subscribeToUser, subscribeToBusinesss } = useFollow();
  const [plans, setPlans] = useState<ISubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [subscribing, setSubscribing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const loadPlans = async () => {
      try {
        const allPlans = await getSubscriptionPlans();
        const filteredPlans = allPlans.filter(plan => plan.targetType === targetType && plan.isActive);
        setPlans(filteredPlans);
      } catch (err) {
        setError("Erreur lors du chargement des plans d'abonnement");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    if (isOpen) {
      loadPlans();
    }
  }, [isOpen, targetType, getSubscriptionPlans]);

  const handleSubscribe = async () => {
    if (!selectedPlan) return;
    
    setSubscribing(true);
    setError(null);
    setSuccess(null);
    
    try {
      const plan = plans.find(p => p.id === selectedPlan);
      if (!plan) throw new Error("Plan sélectionné non trouvé");
      
      if (targetType === 'user') {
        await subscribeToUser(targetId, plan.level);
      } else {
        await subscribeToBusinesss(targetId, plan.level);
      }
      
      setSuccess(`Vous êtes maintenant abonné à ${targetName} avec le plan ${plan.name}`);
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      setError("Erreur lors de l'abonnement. Veuillez réessayer.");
      console.error(err);
    } finally {
      setSubscribing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">S'abonner à {targetName}</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={24} />
          </button>
        </div>
        
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
                <AlertCircle size={20} className="mr-2" />
                {error}
              </div>
            )}
            
            {success && (
              <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
                <Check size={20} className="mr-2" />
                {success}
              </div>
            )}
            
            <div className="space-y-4 mb-6">
              {plans.map(plan => (
                <div 
                  key={plan.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-all ${
                    selectedPlan === plan.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-blue-300'
                  }`}
                  onClick={() => setSelectedPlan(plan.id)}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="font-medium text-gray-900">{plan.name}</h3>
                      <p className="text-sm text-gray-500">
                        {plan.price === 0 
                          ? 'Gratuit' 
                          : `${plan.price.toLocaleString()} F CFA / ${plan.duration} jours`}
                      </p>
                    </div>
                    <div className={`w-6 h-6 rounded-full border flex items-center justify-center ${
                      selectedPlan === plan.id 
                        ? 'border-blue-500 bg-blue-500 text-white' 
                        : 'border-gray-300'
                    }`}>
                      {selectedPlan === plan.id && <Check size={16} />}
                    </div>
                  </div>
                  
                  <div className="mt-3">
                    <h4 className="text-sm font-medium text-gray-700 mb-1">Fonctionnalités :</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center">
                          <Check size={14} className="text-green-500 mr-2" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={subscribing}
              >
                Annuler
              </Button>
              <Button
                variant="primary"
                onClick={handleSubscribe}
                disabled={!selectedPlan || subscribing}
                loading={subscribing}
              >
                S'abonner
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default SubscriptionPlansModal;