import React, { useEffect, useState } from 'react';
import SubscriptionActivationTest from '../components/test/SubscriptionActivationTest';
import { supabase } from '../lib/supabase';
import Button from '../components/ui/Button';

const SubscriptionActivationTestPage: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [testCodeId, setTestCodeId] = useState<string | null>(null);
  const [isCreatingCode, setIsCreatingCode] = useState(false);
  const [isValidatingCode, setIsValidatingCode] = useState(false);

  useEffect(() => {
    const checkTables = async () => {
      try {
        // Récupérer l'utilisateur connecté
        const { data: { user } } = await supabase.auth.getUser();
        const currentUserId = user?.id;

        // Vérifier si la table subscription_notifications existe
        const { data: notificationsTable, error: notifError } = await supabase
          .from('subscription_notifications')
          .select('*')
          .limit(1);

        // Vérifier les abonnements business pour l'utilisateur connecté
        const { data: subscriptions, error: subError } = await supabase
          .from('business_subscriptions')
          .select('*')
          .eq('business_id', currentUserId || '')
          .limit(5);

        // Vérifier les codes d'abonnement pour l'utilisateur connecté
        const { data: codes, error: codesError } = await supabase
          .from('subscription_codes')
          .select('*')
          .eq('business_id', currentUserId || '')
          .limit(5);

        // Vérifier les notifications pour l'utilisateur connecté
        const { data: userNotifications, error: userNotifError } = await supabase
          .from('subscription_notifications')
          .select('*')
          .eq('business_id', currentUserId || '')
          .order('created_at', { ascending: false })
          .limit(5);

        // Tester la procédure stockée
        let procedureTest = 'Non testé';
        try {
          const { error: procError } = await supabase.rpc('create_subscription_from_code', {
            p_code_id: '00000000-0000-0000-0000-000000000000' // UUID fictif pour test
          });
          procedureTest = procError ? `Erreur: ${procError.message}` : 'Procédure accessible';
        } catch (procErr) {
          procedureTest = `Erreur procédure: ${procErr instanceof Error ? procErr.message : 'Inconnue'}`;
        }

        setDebugInfo({
          currentUserId,
          notificationsTable: notifError ? `Erreur: ${notifError.message}` : `OK (${notificationsTable?.length || 0} entrées)`,
          subscriptions: subError ? `Erreur: ${subError.message}` : `OK (${subscriptions?.length || 0} entrées)`,
          codes: codesError ? `Erreur: ${codesError.message}` : `OK (${codes?.length || 0} entrées)`,
          userNotifications: userNotifError ? `Erreur: ${userNotifError.message}` : `OK (${userNotifications?.length || 0} notifications)`,
          procedureTest,
          lastSubscriptions: subscriptions?.slice(0, 3),
          lastCodes: codes?.slice(0, 3),
          lastNotifications: userNotifications?.slice(0, 3)
        });

      } catch (error) {
        console.error('Erreur lors de la vérification des tables:', error);
        setDebugInfo({ error: error instanceof Error ? error.message : 'Erreur inconnue' });
      }
    };

    checkTables();
  }, []);

  const createTestCode = async () => {
    setIsCreatingCode(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        alert('Vous devez être connecté');
        return;
      }

      // Importer le service des codes d'abonnement
      const { SubscriptionCodeService } = await import('../services/subscriptionCodeService');

      const codeId = await SubscriptionCodeService.createSubscriptionCode(
        user.id,
        'Test Business',
        'monthly',
        2500
      );

      if (codeId) {
        setTestCodeId(codeId);
        alert(`Code de test créé avec succès: ${codeId}`);

        // Recharger les informations de debug
        const checkTables = async () => {
          try {
            const { data: codes, error: codesError } = await supabase
              .from('subscription_codes')
              .select('*')
              .eq('business_id', user.id)
              .limit(5);

            setDebugInfo((prev: any) => ({
              ...prev,
              codes: codesError ? `Erreur: ${codesError.message}` : `OK (${codes?.length || 0} entrées)`,
              lastCodes: codes?.slice(0, 3)
            }));
          } catch (error) {
            console.error('Erreur lors du rechargement:', error);
          }
        };
        await checkTables();
      } else {
        alert('Erreur lors de la création du code');
      }
    } catch (error) {
      console.error('Erreur:', error);
      alert('Erreur lors de la création du code: ' + (error instanceof Error ? error.message : 'Erreur inconnue'));
    } finally {
      setIsCreatingCode(false);
    }
  };

  const validateTestCode = async () => {
    if (!testCodeId) {
      alert('Aucun code de test à valider');
      return;
    }

    setIsValidatingCode(true);
    try {
      // Importer le service des codes d'abonnement
      const { SubscriptionCodeService } = await import('../services/subscriptionCodeService');

      const success = await SubscriptionCodeService.validateCode({
        codeId: testCodeId,
        action: 'validate',
        reason: 'Test de validation automatique',
        adminId: 'test-admin'
      });

      if (success) {
        alert('Code validé avec succès ! Vérifiez les notifications et l\'abonnement.');

        // Recharger toutes les informations
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          const checkTables = async () => {
            try {
              const [subscriptionsResult, codesResult, notificationsResult] = await Promise.all([
                supabase.from('business_subscriptions').select('*').eq('business_id', user.id).limit(5),
                supabase.from('subscription_codes').select('*').eq('business_id', user.id).limit(5),
                supabase.from('subscription_notifications').select('*').eq('business_id', user.id).order('created_at', { ascending: false }).limit(5)
              ]);

              setDebugInfo((prev: any) => ({
                ...prev,
                subscriptions: subscriptionsResult.error ? `Erreur: ${subscriptionsResult.error.message}` : `OK (${subscriptionsResult.data?.length || 0} entrées)`,
                codes: codesResult.error ? `Erreur: ${codesResult.error.message}` : `OK (${codesResult.data?.length || 0} entrées)`,
                userNotifications: notificationsResult.error ? `Erreur: ${notificationsResult.error.message}` : `OK (${notificationsResult.data?.length || 0} notifications)`,
                lastSubscriptions: subscriptionsResult.data?.slice(0, 3),
                lastCodes: codesResult.data?.slice(0, 3),
                lastNotifications: notificationsResult.data?.slice(0, 3)
              }));
            } catch (error) {
              console.error('Erreur lors du rechargement:', error);
            }
          };
          await checkTables();
        }
      } else {
        alert('Erreur lors de la validation du code');
      }
    } catch (error) {
      console.error('Erreur:', error);
      alert('Erreur lors de la validation: ' + (error instanceof Error ? error.message : 'Erreur inconnue'));
    } finally {
      setIsValidatingCode(false);
    }
  };

  return (
    <div>
      {/* Informations de debug */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <h3 className="font-bold text-yellow-800 mb-2">🔍 Diagnostic des tables</h3>
        <div className="text-sm space-y-1">
          <div><strong>Utilisateur connecté:</strong> {debugInfo.currentUserId || 'Non connecté'}</div>
          <div><strong>Table notifications:</strong> {debugInfo.notificationsTable}</div>
          <div><strong>Table abonnements:</strong> {debugInfo.subscriptions}</div>
          <div><strong>Table codes:</strong> {debugInfo.codes}</div>
          <div><strong>Notifications utilisateur:</strong> {debugInfo.userNotifications}</div>
          <div><strong>Procédure stockée:</strong> {debugInfo.procedureTest}</div>
        </div>

        {debugInfo.lastSubscriptions && (
          <details className="mt-3">
            <summary className="cursor-pointer text-yellow-700 font-medium">Derniers abonnements</summary>
            <pre className="text-xs bg-white p-2 rounded mt-2 overflow-auto">
              {JSON.stringify(debugInfo.lastSubscriptions, null, 2)}
            </pre>
          </details>
        )}

        {debugInfo.lastCodes && (
          <details className="mt-3">
            <summary className="cursor-pointer text-yellow-700 font-medium">Derniers codes</summary>
            <pre className="text-xs bg-white p-2 rounded mt-2 overflow-auto">
              {JSON.stringify(debugInfo.lastCodes, null, 2)}
            </pre>
          </details>
        )}

        {debugInfo.lastNotifications && (
          <details className="mt-3">
            <summary className="cursor-pointer text-yellow-700 font-medium">Dernières notifications</summary>
            <pre className="text-xs bg-white p-2 rounded mt-2 overflow-auto">
              {JSON.stringify(debugInfo.lastNotifications, null, 2)}
            </pre>
          </details>
        )}
      </div>

      {/* Boutons de test */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h3 className="font-bold text-blue-800 mb-4">🧪 Tests d'activation d'abonnement</h3>
        <div className="flex flex-wrap gap-3">
          <Button
            onClick={createTestCode}
            disabled={isCreatingCode}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {isCreatingCode ? '⏳ Création...' : '🎫 Créer un code de test'}
          </Button>

          {testCodeId && (
            <Button
              onClick={validateTestCode}
              disabled={isValidatingCode}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isValidatingCode ? '⏳ Validation...' : '✅ Valider le code'}
            </Button>
          )}

          <div className="text-sm text-gray-600 flex items-center">
            {testCodeId ? (
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                Code créé: {testCodeId.substring(0, 8)}...
              </span>
            ) : (
              <span>Aucun code de test créé</span>
            )}
          </div>
        </div>

        <div className="mt-3 text-sm text-gray-600">
          <p><strong>Instructions:</strong></p>
          <ol className="list-decimal list-inside space-y-1 mt-1">
            <li>Cliquez sur "Créer un code de test" pour générer un code d'abonnement</li>
            <li>Cliquez sur "Valider le code" pour simuler la validation par un admin</li>
            <li>Vérifiez que l'abonnement est créé et qu'une notification est envoyée</li>
          </ol>
        </div>
      </div>

      <SubscriptionActivationTest />
    </div>
  );
};

export default SubscriptionActivationTestPage;
