-- <PERSON><PERSON>er la table des notifications d'abonnement
CREATE TABLE IF NOT EXISTS public.subscription_notifications (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('activation', 'expiration_warning', 'expired', 'renewal')),
  title text NOT NULL,
  message text NOT NULL,
  plan_name text NOT NULL,
  days_remaining integer,
  is_read boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- <PERSON><PERSON>er un index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_subscription_notifications_business_id ON public.subscription_notifications(business_id);
CREATE INDEX IF NOT EXISTS idx_subscription_notifications_created_at ON public.subscription_notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_subscription_notifications_is_read ON public.subscription_notifications(is_read);

-- Activer RLS
ALTER TABLE public.subscription_notifications ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre aux utilisateurs de voir leurs propres notifications
CREATE POLICY "Users can view their own subscription notifications" ON public.subscription_notifications
  FOR SELECT USING (auth.uid() = business_id);

-- Politique pour permettre aux utilisateurs de mettre à jour leurs propres notifications
CREATE POLICY "Users can update their own subscription notifications" ON public.subscription_notifications
  FOR UPDATE USING (auth.uid() = business_id);

-- Politique pour permettre l'insertion de notifications (pour le système)
CREATE POLICY "System can insert subscription notifications" ON public.subscription_notifications
  FOR INSERT WITH CHECK (true);

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_subscription_notifications_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour automatiquement updated_at
CREATE TRIGGER update_subscription_notifications_updated_at
  BEFORE UPDATE ON public.subscription_notifications
  FOR EACH ROW
  EXECUTE FUNCTION update_subscription_notifications_updated_at();

-- Donner les permissions nécessaires
GRANT SELECT, INSERT, UPDATE ON public.subscription_notifications TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.subscription_notifications TO service_role;
