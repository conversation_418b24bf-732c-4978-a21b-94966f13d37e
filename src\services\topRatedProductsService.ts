import { supabase } from '../lib/supabase';

export interface TopRatedProduct {
  id: string;
  name: string;
  category: string;
  rating: number;
  reviewCount: number;
  image?: string;
  businessName: string;
  businessId: string;
  price?: number;
  description?: string;
  rank: number;
}

export interface ProductCategory {
  id: string;
  name: string;
  count: number;
}

export class TopRatedProductsService {
  /**
   * Calcule la note moyenne d'un produit basée sur les posts et commentaires
   */
  static async calculateProductRating(productName: string): Promise<{ average: number; count: number }> {
    try {
      // Récupérer tous les posts pour ce produit
      const { data: posts, error: postsError } = await supabase
        .from('posts')
        .select('rating')
        .ilike('product_name', productName)
        .not('rating', 'is', null);

      if (postsError) throw postsError;

      // Récupérer tous les commentaires avec rating pour ce produit
      const { data: comments, error: commentsError } = await supabase
        .from('comments')
        .select(`
          rating,
          post_id,
          posts!inner(product_name)
        `)
        .eq('has_used_product', true)
        .not('rating', 'is', null);

      if (commentsError) throw commentsError;

      // Filtrer les commentaires pour ce produit spécifique
      const productComments = comments?.filter(comment =>
        (comment.posts as any)?.product_name?.toLowerCase() === productName.toLowerCase()
      ) || [];

      // Combiner toutes les notes
      const allRatings = [
        ...(posts?.map(p => p.rating) || []),
        ...(productComments.map(c => c.rating) || [])
      ].filter(rating => rating !== null && rating >= 1 && rating <= 5);

      if (allRatings.length === 0) {
        return { average: 0, count: 0 };
      }

      const average = allRatings.reduce((sum, rating) => sum + rating, 0) / allRatings.length;
      return { average: Math.round(average * 10) / 10, count: allRatings.length };
    } catch (error) {
      console.error('Erreur lors du calcul de la note:', error);
      return { average: 0, count: 0 };
    }
  }

  /**
   * Récupère les produits les mieux notés basés sur les posts et commentaires
   */
  static async getTopRatedProducts(limit?: number): Promise<TopRatedProduct[]> {
    try {
      // Récupérer tous les produits uniques depuis les posts
      const { data: posts, error: postsError } = await supabase
        .from('posts')
        .select(`
          product_name,
          business_name,
          category,
          images,
          description
        `)
        .not('product_name', 'is', null)
        .not('product_name', 'eq', '');

      if (postsError) throw postsError;

      if (!posts || posts.length === 0) return [];

      // Grouper par produit unique
      const uniqueProducts = posts.reduce((acc, post) => {
        const key = `${post.product_name}-${post.business_name}`;
        if (!acc[key]) {
          acc[key] = {
            name: post.product_name,
            businessName: post.business_name,
            category: post.category,
            images: post.images,
            description: post.description
          };
        }
        return acc;
      }, {} as Record<string, any>);

      // Calculer les notes pour chaque produit
      const productsWithRatings = await Promise.all(
        Object.values(uniqueProducts).map(async (product: any, index) => {
          const ratingInfo = await this.calculateProductRating(product.name);

          return {
            id: `${product.name}-${product.businessName}`.replace(/\s+/g, '-').toLowerCase(),
            name: product.name,
            category: product.category || 'Autre',
            rating: ratingInfo.average,
            reviewCount: ratingInfo.count,
            image: product.images && product.images.length > 0 ? product.images[0] : undefined,
            businessName: product.businessName,
            businessId: '', // Pas d'ID business dans les posts
            price: undefined, // Pas de prix dans les posts
            description: product.description,
            rank: index + 1
          } as TopRatedProduct;
        })
      );

      // Filtrer les produits avec au moins une note et trier par note
      const filteredProducts = productsWithRatings
        .filter(product => product.rating > 0)
        .sort((a, b) => {
          // Trier par note moyenne, puis par nombre d'avis
          if (b.rating !== a.rating) {
            return b.rating - a.rating;
          }
          return b.reviewCount - a.reviewCount;
        })
        .slice(0, limit || 50)
        .map((product, index) => ({
          ...product,
          rank: index + 1
        }));

      return filteredProducts;
    } catch (error) {
      console.error('Erreur lors de la récupération des produits mieux notés:', error);
      return [];
    }
  }

  /**
   * Récupère les catégories de produits avec le nombre de produits par catégorie
   */
  static async getProductCategories(): Promise<ProductCategory[]> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select('category')
        .not('product_name', 'is', null)
        .not('product_name', 'eq', '');

      if (error) throw error;

      if (!data) return [];

      // Compter les produits par catégorie
      const categoryCount = data.reduce((acc, post) => {
        const category = post.category || 'Autre';
        acc[category] = (acc[category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Convertir en tableau et ajouter "Toutes catégories"
      const categories: ProductCategory[] = [
        {
          id: 'all',
          name: 'Toutes catégories',
          count: data.length
        },
        ...Object.entries(categoryCount).map(([name, count]) => ({
          id: name.toLowerCase().replace(/\s+/g, '-'),
          name,
          count
        }))
      ];

      return categories.sort((a, b) => {
        if (a.id === 'all') return -1;
        if (b.id === 'all') return 1;
        return b.count - a.count;
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des catégories:', error);
      return [];
    }
  }

  /**
   * Recherche des produits par nom ou nom d'entreprise
   */
  static async searchProducts(query: string): Promise<TopRatedProduct[]> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          product_name,
          business_name,
          category,
          images,
          description
        `)
        .or(`product_name.ilike.%${query}%,business_name.ilike.%${query}%`)
        .not('product_name', 'is', null)
        .not('product_name', 'eq', '');

      if (error) throw error;

      if (!data) return [];

      // Grouper par produit unique
      const uniqueProducts = data.reduce((acc, post) => {
        const key = `${post.product_name}-${post.business_name}`;
        if (!acc[key]) {
          acc[key] = {
            name: post.product_name,
            businessName: post.business_name,
            category: post.category,
            images: post.images,
            description: post.description
          };
        }
        return acc;
      }, {} as Record<string, any>);

      // Calculer les notes pour chaque produit
      const productsWithRatings = await Promise.all(
        Object.values(uniqueProducts).map(async (product: any, index) => {
          const ratingInfo = await this.calculateProductRating(product.name);

          return {
            id: `${product.name}-${product.businessName}`.replace(/\s+/g, '-').toLowerCase(),
            name: product.name,
            category: product.category || 'Autre',
            rating: ratingInfo.average,
            reviewCount: ratingInfo.count,
            image: product.images && product.images.length > 0 ? product.images[0] : undefined,
            businessName: product.businessName,
            businessId: '',
            price: undefined,
            description: product.description,
            rank: index + 1
          } as TopRatedProduct;
        })
      );

      // Filtrer et trier par pertinence
      return productsWithRatings
        .filter(product => product.rating > 0)
        .sort((a, b) => b.rating - a.rating)
        .map((product, index) => ({
          ...product,
          rank: index + 1
        }));
    } catch (error) {
      console.error('Erreur lors de la recherche de produits:', error);
      return [];
    }
  }

  /**
   * Filtre les produits par catégorie
   */
  static async getProductsByCategory(category: string): Promise<TopRatedProduct[]> {
    try {
      if (category === 'all') {
        return this.getTopRatedProducts();
      }

      const { data, error } = await supabase
        .from('posts')
        .select(`
          product_name,
          business_name,
          category,
          images,
          description
        `)
        .eq('category', category)
        .not('product_name', 'is', null)
        .not('product_name', 'eq', '');

      if (error) throw error;

      if (!data) return [];

      // Grouper par produit unique
      const uniqueProducts = data.reduce((acc, post) => {
        const key = `${post.product_name}-${post.business_name}`;
        if (!acc[key]) {
          acc[key] = {
            name: post.product_name,
            businessName: post.business_name,
            category: post.category,
            images: post.images,
            description: post.description
          };
        }
        return acc;
      }, {} as Record<string, any>);

      // Calculer les notes pour chaque produit
      const productsWithRatings = await Promise.all(
        Object.values(uniqueProducts).map(async (product: any, index) => {
          const ratingInfo = await this.calculateProductRating(product.name);

          return {
            id: `${product.name}-${product.businessName}`.replace(/\s+/g, '-').toLowerCase(),
            name: product.name,
            category: product.category || 'Autre',
            rating: ratingInfo.average,
            reviewCount: ratingInfo.count,
            image: product.images && product.images.length > 0 ? product.images[0] : undefined,
            businessName: product.businessName,
            businessId: '',
            price: undefined,
            description: product.description,
            rank: index + 1
          } as TopRatedProduct;
        })
      );

      // Filtrer et trier par note
      return productsWithRatings
        .filter(product => product.rating > 0)
        .sort((a, b) => b.rating - a.rating)
        .map((product, index) => ({
          ...product,
          rank: index + 1
        }));
    } catch (error) {
      console.error('Erreur lors de la récupération des produits par catégorie:', error);
      return [];
    }
  }

  /**
   * Récupère les statistiques générales des produits mieux notés
   */
  static async getTopRatedProductsStats() {
    try {
      // Récupérer tous les produits avec leurs notes calculées
      const products = await this.getTopRatedProducts();

      // Compter le total des avis (posts + commentaires)
      const { data: posts, error: postsError } = await supabase
        .from('posts')
        .select('id')
        .not('product_name', 'is', null)
        .not('rating', 'is', null);

      if (postsError) throw postsError;

      const { data: comments, error: commentsError } = await supabase
        .from('comments')
        .select('id')
        .eq('has_used_product', true)
        .not('rating', 'is', null);

      if (commentsError) throw commentsError;

      const totalReviews = (posts?.length || 0) + (comments?.length || 0);
      const totalProducts = products.length;
      const averageRating = totalProducts > 0
        ? products.reduce((sum, p) => sum + p.rating, 0) / totalProducts
        : 0;
      const topRatedCount = products.filter(p => p.rating >= 4.5).length;

      return {
        totalProducts,
        totalReviews,
        averageRating: Math.round(averageRating * 10) / 10,
        topRatedCount
      };
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      return {
        totalProducts: 0,
        totalReviews: 0,
        averageRating: 0,
        topRatedCount: 0
      };
    }
  }
}
