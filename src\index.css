@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles pour line-clamp */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Styles pour les annonces publicitaires */
.sidebar-ads-carousel {
  @apply relative;
}

.sidebar-ads-carousel .ad-image {
  @apply transition-transform duration-300 hover:scale-105;
}

.sidebar-ads-carousel .ad-card {
  @apply transform transition-all duration-300 hover:shadow-lg;
}

/* Animation pour le carousel */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ad-fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Styles pour les images de fallback */
.fallback-image {
  @apply bg-gradient-to-br from-blue-500 to-purple-600 text-white flex items-center justify-center;
}

/* Amélioration des boutons de navigation */
.nav-button {
  @apply transition-all duration-200 hover:scale-110 active:scale-95;
}

/* Prévention du flash du footer */
main {
  min-height: 60vh;
}

/* Animation de chargement de page */
.page-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

/* =====================================================
   ANIMATIONS POUR LES NOTIFICATIONS D'ABONNEMENT
   ===================================================== */

/* Animation de scale-in pour les modals */
@keyframes scaleIn {
  from {
    transform: scale(0.8) translateY(50px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

/* Animation de bounce pour les icônes */
@keyframes bounceIn {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Animation de slide-up pour les textes */
@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Animation de confettis */
@keyframes confetti {
  0% {
    transform: translateY(-10px) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100px) rotate(360deg);
    opacity: 0;
  }
}

/* Classes d'animation */
.animate-scale-in {
  animation: scaleIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.animate-bounce-in {
  animation: bounceIn 0.6s ease-out 0.3s both;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out 0.4s both;
}

.animate-slide-up-delay {
  animation: slideUp 0.5s ease-out 0.5s both;
}

.animate-confetti {
  animation: confetti 2s ease-out forwards;
}

/* Amélioration de l'animation fadeIn existante */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}
