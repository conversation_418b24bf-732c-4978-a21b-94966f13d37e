@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles pour line-clamp */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Styles pour les annonces publicitaires */
.sidebar-ads-carousel {
  @apply relative;
}

.sidebar-ads-carousel .ad-image {
  @apply transition-transform duration-300 hover:scale-105;
}

.sidebar-ads-carousel .ad-card {
  @apply transform transition-all duration-300 hover:shadow-lg;
}

/* Animation pour le carousel */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ad-fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Styles pour les images de fallback */
.fallback-image {
  @apply bg-gradient-to-br from-blue-500 to-purple-600 text-white flex items-center justify-center;
}

/* Amélioration des boutons de navigation */
.nav-button {
  @apply transition-all duration-200 hover:scale-110 active:scale-95;
}

/* Prévention du flash du footer */
main {
  min-height: 60vh;
}

/* Animation de chargement de page */
.page-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}
