-- Script pour créer les fonctions de comptage de commentaires
-- À exécuter dans l'éditeur SQL de Supabase

-- =====================================================
-- 1. AJOUTER LA COLONNE COMMENT_COUNT À LA TABLE POSTS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Ajout de la colonne comment_count à la table posts...';
    
    -- Ajouter la colonne comment_count si elle n'existe pas
    ALTER TABLE posts
    ADD COLUMN IF NOT EXISTS comment_count INTEGER DEFAULT 0 CHECK (comment_count >= 0);
    
    RAISE NOTICE '✅ Colonne comment_count ajoutée';
END $$;

-- Commentaire explicatif sur l'utilisation de cette colonne
COMMENT ON COLUMN posts.comment_count IS 'Nombre de commentaires sur ce post';

-- Initialiser la colonne pour tous les posts existants
DO $$
BEGIN
    RAISE NOTICE '🔧 Initialisation des compteurs de commentaires...';
    
    UPDATE posts 
    SET comment_count = (
      SELECT COUNT(*) 
      FROM comments 
      WHERE comments.post_id = posts.id
    )
    WHERE comment_count IS NULL OR comment_count = 0;
    
    RAISE NOTICE '✅ Compteurs de commentaires initialisés';
END $$;

-- =====================================================
-- 2. FONCTION POUR INCRÉMENTER LE COMPTEUR DE COMMENTAIRES
-- =====================================================

CREATE OR REPLACE FUNCTION public.increment_comment_count(post_id uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Incrémenter le compteur de commentaires dans la table posts
  UPDATE posts 
  SET comment_count = COALESCE(comment_count, 0) + 1,
      updated_at = now()
  WHERE id = post_id;
  
  -- Si le post n'existe pas, on ne fait rien (pas d'erreur)
  -- Cela permet d'éviter les erreurs si le post a été supprimé entre temps
END;
$$;

-- =====================================================
-- 3. FONCTION POUR DÉCRÉMENTER LE COMPTEUR DE COMMENTAIRES
-- =====================================================

CREATE OR REPLACE FUNCTION public.decrement_comment_count(post_id uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Décrémenter le compteur de commentaires dans la table posts
  UPDATE posts 
  SET comment_count = GREATEST(COALESCE(comment_count, 0) - 1, 0),
      updated_at = now()
  WHERE id = post_id;
  
  -- Utiliser GREATEST pour s'assurer que le compteur ne devient jamais négatif
END;
$$;

-- =====================================================
-- 4. FONCTION POUR RECALCULER LE COMPTEUR DE COMMENTAIRES
-- =====================================================

CREATE OR REPLACE FUNCTION public.recalculate_comment_count(post_id uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  actual_count integer;
BEGIN
  -- Compter le nombre réel de commentaires pour ce post
  SELECT COUNT(*) INTO actual_count
  FROM comments 
  WHERE post_id = recalculate_comment_count.post_id;
  
  -- Mettre à jour le compteur avec la valeur réelle
  UPDATE posts 
  SET comment_count = actual_count,
      updated_at = now()
  WHERE id = recalculate_comment_count.post_id;
END;
$$;

-- =====================================================
-- 5. PERMISSIONS
-- =====================================================

-- Donner les permissions d'exécution aux utilisateurs authentifiés
GRANT EXECUTE ON FUNCTION public.increment_comment_count(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.decrement_comment_count(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.recalculate_comment_count(uuid) TO authenticated;

-- Donner les permissions d'exécution au service role (pour les triggers)
GRANT EXECUTE ON FUNCTION public.increment_comment_count(uuid) TO service_role;
GRANT EXECUTE ON FUNCTION public.decrement_comment_count(uuid) TO service_role;
GRANT EXECUTE ON FUNCTION public.recalculate_comment_count(uuid) TO service_role;

-- =====================================================
-- 6. VÉRIFICATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 Vérification des fonctions créées...';
    
    -- Vérifier que les fonctions existent
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'increment_comment_count') THEN
        RAISE NOTICE '✅ Fonction increment_comment_count créée';
    ELSE
        RAISE NOTICE '❌ Erreur: Fonction increment_comment_count non créée';
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'decrement_comment_count') THEN
        RAISE NOTICE '✅ Fonction decrement_comment_count créée';
    ELSE
        RAISE NOTICE '❌ Erreur: Fonction decrement_comment_count non créée';
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'recalculate_comment_count') THEN
        RAISE NOTICE '✅ Fonction recalculate_comment_count créée';
    ELSE
        RAISE NOTICE '❌ Erreur: Fonction recalculate_comment_count non créée';
    END IF;
    
    RAISE NOTICE '🎉 Migration terminée avec succès !';
END $$;
