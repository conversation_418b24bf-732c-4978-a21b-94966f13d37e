-- =====================================================
-- SCRIPT DE CRÉATION DES NOTIFICATIONS D'ABONNEMENT
-- =====================================================

-- Table pour les notifications d'abonnement
CREATE TABLE IF NOT EXISTS subscription_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    business_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL CHECK (type IN ('activation', 'expiration_warning', 'expired', 'renewal')),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    plan_name VARCHAR(100) NOT NULL,
    days_remaining INTEGER,
    is_read BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_subscription_notifications_business_id 
ON subscription_notifications(business_id);

CREATE INDEX IF NOT EXISTS idx_subscription_notifications_type 
ON subscription_notifications(type);

CREATE INDEX IF NOT EXISTS idx_subscription_notifications_created_at 
ON subscription_notifications(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_subscription_notifications_is_read 
ON subscription_notifications(is_read);

-- Trigger pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION update_subscription_notifications_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_subscription_notifications_updated_at
    BEFORE UPDATE ON subscription_notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_subscription_notifications_updated_at();

-- =====================================================
-- POLITIQUES RLS (Row Level Security)
-- =====================================================

-- Activer RLS sur la table
ALTER TABLE subscription_notifications ENABLE ROW LEVEL SECURITY;

-- Politique pour que les entreprises ne voient que leurs propres notifications
CREATE POLICY "Businesses can view their own notifications" ON subscription_notifications
    FOR SELECT USING (business_id = auth.uid());

-- Politique pour que les entreprises puissent mettre à jour leurs notifications (marquer comme lues)
CREATE POLICY "Businesses can update their own notifications" ON subscription_notifications
    FOR UPDATE USING (business_id = auth.uid());

-- Politique pour que le système puisse insérer des notifications
CREATE POLICY "System can insert notifications" ON subscription_notifications
    FOR INSERT WITH CHECK (true);

-- =====================================================
-- FONCTIONS UTILITAIRES
-- =====================================================

-- Fonction pour marquer toutes les notifications d'une entreprise comme lues
CREATE OR REPLACE FUNCTION mark_all_subscription_notifications_as_read(p_business_id UUID)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE subscription_notifications 
    SET is_read = true, updated_at = NOW()
    WHERE business_id = p_business_id AND is_read = false;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour nettoyer les anciennes notifications (plus de 30 jours)
CREATE OR REPLACE FUNCTION cleanup_old_subscription_notifications()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM subscription_notifications 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour obtenir le nombre de notifications non lues
CREATE OR REPLACE FUNCTION get_unread_subscription_notifications_count(p_business_id UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)
        FROM subscription_notifications
        WHERE business_id = p_business_id AND is_read = false
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- FONCTION D'ENVOI DE NOTIFICATION D'ACTIVATION
-- =====================================================

-- Fonction pour envoyer une notification d'activation automatiquement
CREATE OR REPLACE FUNCTION send_subscription_activation_notification(
    p_business_id UUID,
    p_plan_name VARCHAR(100),
    p_days_remaining INTEGER
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO subscription_notifications (
        business_id,
        type,
        title,
        message,
        plan_name,
        days_remaining,
        is_read
    ) VALUES (
        p_business_id,
        'activation',
        '🎉 Abonnement Activé !',
        'Votre abonnement ' || p_plan_name || ' a été activé avec succès. Vous avez ' || 
        p_days_remaining || ' jour' || CASE WHEN p_days_remaining > 1 THEN 's' ELSE '' END || 
        ' d''accès complet à toutes les fonctionnalités.',
        p_plan_name,
        p_days_remaining,
        false
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- DONNÉES DE TEST (OPTIONNEL)
-- =====================================================

-- Insérer quelques notifications de test (décommentez si nécessaire)
/*
INSERT INTO subscription_notifications (
    business_id,
    type,
    title,
    message,
    plan_name,
    days_remaining,
    is_read
) VALUES 
(
    (SELECT id FROM profiles WHERE email = '<EMAIL>' LIMIT 1),
    'activation',
    '🎉 Abonnement Activé !',
    'Votre abonnement Plan Professionnel a été activé avec succès. Vous avez 30 jours d''accès complet à toutes les fonctionnalités.',
    'Plan Professionnel',
    30,
    false
),
(
    (SELECT id FROM profiles WHERE email = '<EMAIL>' LIMIT 1),
    'expiration_warning',
    '⚠️ Abonnement expire bientôt',
    'Votre abonnement Plan Professionnel expire dans 7 jours. Pensez à le renouveler pour continuer à profiter de toutes les fonctionnalités.',
    'Plan Professionnel',
    7,
    false
);
*/

-- =====================================================
-- VÉRIFICATIONS FINALES
-- =====================================================

-- Vérifier que la table a été créée correctement
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'subscription_notifications'
ORDER BY ordinal_position;

-- Vérifier les index
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'subscription_notifications';

-- Vérifier les politiques RLS
SELECT 
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'subscription_notifications';

COMMENT ON TABLE subscription_notifications IS 'Table pour stocker les notifications d''abonnement des entreprises';
COMMENT ON COLUMN subscription_notifications.business_id IS 'ID de l''entreprise qui reçoit la notification';
COMMENT ON COLUMN subscription_notifications.type IS 'Type de notification: activation, expiration_warning, expired, renewal';
COMMENT ON COLUMN subscription_notifications.title IS 'Titre de la notification';
COMMENT ON COLUMN subscription_notifications.message IS 'Message détaillé de la notification';
COMMENT ON COLUMN subscription_notifications.plan_name IS 'Nom du plan d''abonnement concerné';
COMMENT ON COLUMN subscription_notifications.days_remaining IS 'Nombre de jours restants (pour les notifications d''expiration)';
COMMENT ON COLUMN subscription_notifications.is_read IS 'Indique si la notification a été lue';

-- Message de confirmation
SELECT 'Table subscription_notifications créée avec succès !' as status;
