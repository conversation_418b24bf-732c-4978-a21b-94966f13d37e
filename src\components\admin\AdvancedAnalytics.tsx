import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  Line,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  ComposedChart
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Users,
  Building2,
  FileText,
  ShoppingCart,
  DollarSign,
  Eye,
  MessageSquare,
  Star,
  Calendar,
  Download,
  RefreshCw,
  Filter,
  Settings,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Activity,
  Target,
  Zap,
  Globe,
  Clock,
  AlertTriangle,
  CheckCircle,
  ArrowUpRight,
  ArrowDownRight,
  Minus
} from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import { supabase } from '../../lib/supabase';

// Types pour les analytics
interface AnalyticsData {
  overview: OverviewMetrics;
  userMetrics: UserMetrics;
  businessMetrics: BusinessMetrics;
  contentMetrics: ContentMetrics;
  revenueMetrics: RevenueMetrics;
  engagementMetrics: EngagementMetrics;
  performanceMetrics: PerformanceMetrics;
  trends: TrendData[];
  realTimeData: RealTimeMetrics;
}

interface OverviewMetrics {
  totalUsers: number;
  totalBusinesses: number;
  totalPosts: number;
  totalRevenue: number;
  activeUsers: number;
  newUsersToday: number;
  conversionRate: number;
  retentionRate: number;
  growthRate: number;
}

interface UserMetrics {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  usersByRole: { role: string; count: number; percentage: number }[];
  usersByCountry: { country: string; count: number }[];
  userGrowth: { date: string; users: number; active: number }[];
  retentionCohorts: { cohort: string; retention: number[] }[];
  userEngagement: { date: string; sessions: number; duration: number }[];
}

interface BusinessMetrics {
  totalBusinesses: number;
  verifiedBusinesses: number;
  activeBusinesses: number;
  businessesByCategory: { category: string; count: number }[];
  businessesByStatus: { status: string; count: number }[];
  businessGrowth: { date: string; total: number; verified: number }[];
  revenueByBusiness: { business: string; revenue: number }[];
  topPerformingBusinesses: { name: string; score: number; metrics: any }[];
}

interface ContentMetrics {
  totalPosts: number;
  postsByType: { type: string; count: number }[];
  postsGrowth: { date: string; posts: number; engagement: number }[];
  topCategories: { category: string; posts: number; engagement: number }[];
  contentModeration: { status: string; count: number }[];
  viralContent: { id: string; title: string; engagement: number }[];
}

interface RevenueMetrics {
  totalRevenue: number;
  monthlyRevenue: { month: string; revenue: number; growth: number }[];
  revenueBySource: { source: string; amount: number; percentage: number }[];
  subscriptionMetrics: { plan: string; subscribers: number; revenue: number }[];
  churnRate: number;
  averageRevenuePerUser: number;
  lifetimeValue: number;
}

interface EngagementMetrics {
  totalEngagements: number;
  engagementRate: number;
  engagementByType: { type: string; count: number }[];
  engagementTrends: { date: string; likes: number; comments: number; shares: number }[];
  peakHours: { hour: number; engagement: number }[];
  userSessions: { date: string; sessions: number; duration: number }[];
}

interface PerformanceMetrics {
  pageLoadTime: number;
  apiResponseTime: number;
  errorRate: number;
  uptime: number;
  serverMetrics: { metric: string; value: number; status: 'good' | 'warning' | 'critical' }[];
  systemHealth: { component: string; status: string; lastCheck: string }[];
}

interface TrendData {
  date: string;
  users: number;
  businesses: number;
  posts: number;
  revenue: number;
  engagement: number;
}

interface RealTimeMetrics {
  activeUsers: number;
  onlineBusinesses: number;
  currentSessions: number;
  recentActivities: { type: string; count: number; timestamp: string }[];
  systemLoad: number;
  memoryUsage: number;
  cpuUsage: number;
}

const AdvancedAnalytics: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState('30d');
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const tabs = [
    { id: 'overview', label: 'Vue d\'ensemble', icon: BarChart3 },
    { id: 'users', label: 'Utilisateurs', icon: Users },
    { id: 'businesses', label: 'Entreprises', icon: Building2 },
    { id: 'content', label: 'Contenu', icon: FileText },
    { id: 'revenue', label: 'Revenus', icon: DollarSign },
    { id: 'engagement', label: 'Engagement', icon: Activity },
    { id: 'performance', label: 'Performance', icon: Zap },
    { id: 'realtime', label: 'Temps réel', icon: Globe }
  ];

  const dateRanges = [
    { value: '7d', label: '7 jours' },
    { value: '30d', label: '30 jours' },
    { value: '90d', label: '90 jours' },
    { value: '1y', label: '1 an' },
    { value: 'custom', label: 'Personnalisé' }
  ];

  useEffect(() => {
    loadAnalyticsData();
    
    // Actualisation automatique toutes les 5 minutes
    const interval = setInterval(() => {
      if (activeTab === 'realtime') {
        loadAnalyticsData();
      }
    }, 5 * 60 * 1000);
    
    setRefreshInterval(interval);
    
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [dateRange, activeTab]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔍 Chargement des analytics avancées...');

      // Charger les données en parallèle
      const [
        overviewData,
        userData,
        businessData,
        contentData,
        revenueData,
        engagementData,
        performanceData,
        trendsData,
        realTimeData
      ] = await Promise.all([
        loadOverviewMetrics(),
        loadUserMetrics(),
        loadBusinessMetrics(),
        loadContentMetrics(),
        loadRevenueMetrics(),
        loadEngagementMetrics(),
        loadPerformanceMetrics(),
        loadTrendsData(),
        loadRealTimeMetrics()
      ]);

      const analytics: AnalyticsData = {
        overview: overviewData,
        userMetrics: userData,
        businessMetrics: businessData,
        contentMetrics: contentData,
        revenueMetrics: revenueData,
        engagementMetrics: engagementData,
        performanceMetrics: performanceData,
        trends: trendsData,
        realTimeData: realTimeData
      };

      setAnalyticsData(analytics);
      setLastUpdated(new Date());
      console.log('✅ Analytics chargées:', analytics);

    } catch (error) {
      console.error('Erreur lors du chargement des analytics:', error);
      setError(`Erreur de chargement: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    } finally {
      setLoading(false);
    }
  };

  const loadOverviewMetrics = async (): Promise<OverviewMetrics> => {
    // Charger les métriques de vue d'ensemble
    const { data: usersCount } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });

    const { data: businessesCount } = await supabase
      .from('business_profiles')
      .select('*', { count: 'exact', head: true });

    const { data: postsCount } = await supabase
      .from('posts')
      .select('*', { count: 'exact', head: true });

    // Calculer les utilisateurs actifs (connectés dans les 30 derniers jours)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { data: activeUsersCount } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .gte('updated_at', thirtyDaysAgo.toISOString());

    // Nouveaux utilisateurs aujourd'hui
    const today = new Date().toISOString().split('T')[0];
    const { data: newUsersToday } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', today);

    return {
      totalUsers: usersCount?.length || 0,
      totalBusinesses: businessesCount?.length || 0,
      totalPosts: postsCount?.length || 0,
      totalRevenue: 0, // À calculer depuis les abonnements
      activeUsers: activeUsersCount?.length || 0,
      newUsersToday: newUsersToday?.length || 0,
      conversionRate: 0, // À calculer
      retentionRate: 0, // À calculer
      growthRate: 0 // À calculer
    };
  };

  const loadUserMetrics = async (): Promise<UserMetrics> => {
    // Charger les métriques utilisateurs détaillées
    const { data: users } = await supabase
      .from('profiles')
      .select('role, country, created_at, updated_at');

    const usersByRole = users?.reduce((acc: any[], user) => {
      const existing = acc.find(item => item.role === user.role);
      if (existing) {
        existing.count++;
      } else {
        acc.push({ role: user.role, count: 1, percentage: 0 });
      }
      return acc;
    }, []) || [];

    // Calculer les pourcentages
    const totalUsers = users?.length || 0;
    usersByRole.forEach(item => {
      item.percentage = totalUsers > 0 ? (item.count / totalUsers) * 100 : 0;
    });

    return {
      totalUsers: totalUsers,
      activeUsers: 0, // À calculer
      newUsers: 0, // À calculer
      usersByRole,
      usersByCountry: [], // À implémenter
      userGrowth: [], // À implémenter
      retentionCohorts: [], // À implémenter
      userEngagement: [] // À implémenter
    };
  };

  // Fonctions de chargement pour les autres métriques (à implémenter)
  const loadBusinessMetrics = async (): Promise<BusinessMetrics> => {
    // Implémentation similaire pour les entreprises
    return {
      totalBusinesses: 0,
      verifiedBusinesses: 0,
      activeBusinesses: 0,
      businessesByCategory: [],
      businessesByStatus: [],
      businessGrowth: [],
      revenueByBusiness: [],
      topPerformingBusinesses: []
    };
  };

  const loadContentMetrics = async (): Promise<ContentMetrics> => {
    return {
      totalPosts: 0,
      postsByType: [],
      postsGrowth: [],
      topCategories: [],
      contentModeration: [],
      viralContent: []
    };
  };

  const loadRevenueMetrics = async (): Promise<RevenueMetrics> => {
    return {
      totalRevenue: 0,
      monthlyRevenue: [],
      revenueBySource: [],
      subscriptionMetrics: [],
      churnRate: 0,
      averageRevenuePerUser: 0,
      lifetimeValue: 0
    };
  };

  const loadEngagementMetrics = async (): Promise<EngagementMetrics> => {
    return {
      totalEngagements: 0,
      engagementRate: 0,
      engagementByType: [],
      engagementTrends: [],
      peakHours: [],
      userSessions: []
    };
  };

  const loadPerformanceMetrics = async (): Promise<PerformanceMetrics> => {
    return {
      pageLoadTime: 0,
      apiResponseTime: 0,
      errorRate: 0,
      uptime: 99.9,
      serverMetrics: [],
      systemHealth: []
    };
  };

  const loadTrendsData = async (): Promise<TrendData[]> => {
    return [];
  };

  const loadRealTimeMetrics = async (): Promise<RealTimeMetrics> => {
    return {
      activeUsers: 0,
      onlineBusinesses: 0,
      currentSessions: 0,
      recentActivities: [],
      systemLoad: 0,
      memoryUsage: 0,
      cpuUsage: 0
    };
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Chargement des analytics avancées...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Avancées</h1>
          <p className="text-gray-600 mt-2">
            Analyse complète des performances de la plateforme
            <span className="ml-2 text-sm text-gray-500">
              Dernière mise à jour: {lastUpdated.toLocaleTimeString('fr-FR')}
            </span>
          </p>
        </div>
        <div className="flex space-x-2">
          <select
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
          >
            {dateRanges.map((range) => (
              <option key={range.value} value={range.value}>
                {range.label}
              </option>
            ))}
          </select>
          <Button
            variant="outline"
            leftIcon={<Download size={16} />}
            onClick={() => {/* Implémenter export */}}
          >
            Exporter
          </Button>
          <Button
            leftIcon={<RefreshCw size={16} />}
            onClick={() => loadAnalyticsData()}
          >
            Actualiser
          </Button>
        </div>
      </div>

      {/* Affichage des erreurs */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardBody className="p-4">
            <div className="flex items-center space-x-2 text-red-700">
              <AlertTriangle size={20} />
              <div>
                <h3 className="font-medium">Erreur de chargement</h3>
                <p className="text-sm">{error}</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2"
                  onClick={() => {
                    setError(null);
                    loadAnalyticsData();
                  }}
                >
                  Réessayer
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Onglets de navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon size={16} />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Contenu des onglets */}
      {analyticsData && (
        <div className="space-y-6">
          {activeTab === 'overview' && <OverviewTab data={analyticsData.overview} trends={analyticsData.trends} />}
          {activeTab === 'users' && <UsersTab data={analyticsData.userMetrics} />}
          {activeTab === 'businesses' && <BusinessesTab data={analyticsData.businessMetrics} />}
          {activeTab === 'content' && <ContentTab data={analyticsData.contentMetrics} />}
          {activeTab === 'revenue' && <RevenueTab data={analyticsData.revenueMetrics} />}
          {activeTab === 'engagement' && <EngagementTab data={analyticsData.engagementMetrics} />}
          {activeTab === 'performance' && <PerformanceTab data={analyticsData.performanceMetrics} />}
          {activeTab === 'realtime' && <RealTimeTab data={analyticsData.realTimeData} />}
        </div>
      )}
    </div>
  );
};

// Composant de métrique avec tendance
interface MetricCardProps {
  title: string;
  value: number | string;
  change?: number;
  changeLabel?: string;
  icon: React.ComponentType<any>;
  color: string;
  format?: 'number' | 'currency' | 'percentage';
  description?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  changeLabel,
  icon: Icon,
  color,
  format = 'number',
  description
}) => {
  const formatValue = (val: number | string) => {
    if (typeof val === 'string') return val;

    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('fr-FR', {
          style: 'currency',
          currency: 'XOF'
        }).format(val);
      case 'percentage':
        return `${val.toFixed(1)}%`;
      default:
        return new Intl.NumberFormat('fr-FR').format(val);
    }
  };

  const getTrendIcon = () => {
    if (!change) return <Minus size={16} className="text-gray-400" />;
    if (change > 0) return <ArrowUpRight size={16} className="text-green-500" />;
    if (change < 0) return <ArrowDownRight size={16} className="text-red-500" />;
    return <Minus size={16} className="text-gray-400" />;
  };

  const getTrendColor = () => {
    if (!change) return 'text-gray-500';
    return change > 0 ? 'text-green-600' : 'text-red-600';
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardBody className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <div className={`p-2 rounded-lg ${color}`}>
                <Icon size={20} className="text-white" />
              </div>
              <h3 className="text-sm font-medium text-gray-600">{title}</h3>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-gray-900">{formatValue(value)}</p>
              {change !== undefined && (
                <div className="flex items-center space-x-1">
                  {getTrendIcon()}
                  <span className={`text-sm font-medium ${getTrendColor()}`}>
                    {Math.abs(change).toFixed(1)}%
                  </span>
                  {changeLabel && (
                    <span className="text-sm text-gray-500">{changeLabel}</span>
                  )}
                </div>
              )}
              {description && (
                <p className="text-xs text-gray-500 mt-1">{description}</p>
              )}
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

// Onglet Vue d'ensemble
interface OverviewTabProps {
  data: OverviewMetrics;
  trends: TrendData[];
}

const OverviewTab: React.FC<OverviewTabProps> = ({ data, trends }) => {
  return (
    <div className="space-y-6">
      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Utilisateurs Total"
          value={data.totalUsers}
          change={5.2}
          changeLabel="ce mois"
          icon={Users}
          color="bg-blue-500"
          description="Tous les utilisateurs inscrits"
        />
        <MetricCard
          title="Entreprises"
          value={data.totalBusinesses}
          change={12.8}
          changeLabel="ce mois"
          icon={Building2}
          color="bg-green-500"
          description="Comptes entreprises actifs"
        />
        <MetricCard
          title="Publications"
          value={data.totalPosts}
          change={8.4}
          changeLabel="cette semaine"
          icon={FileText}
          color="bg-purple-500"
          description="Posts et avis publiés"
        />
        <MetricCard
          title="Revenus"
          value={data.totalRevenue}
          change={15.6}
          changeLabel="ce mois"
          icon={DollarSign}
          color="bg-orange-500"
          format="currency"
          description="Revenus des abonnements"
        />
      </div>

      {/* Métriques secondaires */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Utilisateurs Actifs"
          value={data.activeUsers}
          change={3.2}
          changeLabel="30 jours"
          icon={Activity}
          color="bg-cyan-500"
          description="Connectés récemment"
        />
        <MetricCard
          title="Nouveaux Aujourd'hui"
          value={data.newUsersToday}
          change={-2.1}
          changeLabel="vs hier"
          icon={TrendingUp}
          color="bg-indigo-500"
          description="Inscriptions du jour"
        />
        <MetricCard
          title="Taux de Conversion"
          value={data.conversionRate}
          change={1.8}
          changeLabel="ce mois"
          icon={Target}
          color="bg-pink-500"
          format="percentage"
          description="Visiteurs → Utilisateurs"
        />
        <MetricCard
          title="Taux de Rétention"
          value={data.retentionRate}
          change={4.5}
          changeLabel="30 jours"
          icon={CheckCircle}
          color="bg-emerald-500"
          format="percentage"
          description="Utilisateurs qui reviennent"
        />
      </div>

      {/* Graphique de tendances */}
      <Card>
        <CardBody className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Tendances Générales</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart data={trends.length > 0 ? trends : generateMockTrends()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => {
                    const date = new Date(value);
                    return `${date.getDate()}/${date.getMonth() + 1}`;
                  }}
                />
                <YAxis yAxisId="left" tick={{ fontSize: 12 }} />
                <YAxis yAxisId="right" orientation="right" tick={{ fontSize: 12 }} />
                <Tooltip
                  labelFormatter={(value) => {
                    const date = new Date(value);
                    return date.toLocaleDateString('fr-FR');
                  }}
                  formatter={(value: any, name: string) => [
                    typeof value === 'number' ? value.toLocaleString('fr-FR') : value,
                    name
                  ]}
                />
                <Legend />
                <Area
                  yAxisId="left"
                  type="monotone"
                  dataKey="users"
                  fill="#3b82f6"
                  fillOpacity={0.3}
                  stroke="#3b82f6"
                  strokeWidth={2}
                  name="Utilisateurs"
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="businesses"
                  stroke="#10b981"
                  strokeWidth={3}
                  dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                  name="Entreprises"
                />
                <Bar
                  yAxisId="left"
                  dataKey="posts"
                  fill="#8b5cf6"
                  fillOpacity={0.8}
                  name="Publications"
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

// Onglet Utilisateurs
interface UsersTabProps {
  data: UserMetrics;
}

const UsersTab: React.FC<UsersTabProps> = ({ data }) => {
  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

  return (
    <div className="space-y-6">
      {/* Métriques utilisateurs */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <MetricCard
          title="Total Utilisateurs"
          value={data.totalUsers}
          change={5.2}
          icon={Users}
          color="bg-blue-500"
        />
        <MetricCard
          title="Utilisateurs Actifs"
          value={data.activeUsers}
          change={3.8}
          icon={Activity}
          color="bg-green-500"
        />
        <MetricCard
          title="Nouveaux Utilisateurs"
          value={data.newUsers}
          change={12.4}
          icon={TrendingUp}
          color="bg-purple-500"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Répartition par rôle */}
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition par Rôle</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.usersByRole.length > 0 ? data.usersByRole : generateMockUserRoles()}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ role, percentage }) => `${role} (${percentage.toFixed(1)}%)`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {(data.usersByRole.length > 0 ? data.usersByRole : generateMockUserRoles()).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value: any, name: string) => [
                      `${value} utilisateurs`,
                      'Nombre'
                    ]}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardBody>
        </Card>

        {/* Croissance des utilisateurs */}
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Croissance des Utilisateurs</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={data.userGrowth.length > 0 ? data.userGrowth : generateMockUserGrowth()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return `${date.getDate()}/${date.getMonth() + 1}`;
                    }}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip
                    labelFormatter={(value) => {
                      const date = new Date(value);
                      return date.toLocaleDateString('fr-FR');
                    }}
                    formatter={(value: any, name: string) => [
                      typeof value === 'number' ? value.toLocaleString('fr-FR') : value,
                      name
                    ]}
                  />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="users"
                    stackId="1"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.6}
                    name="Total"
                  />
                  <Area
                    type="monotone"
                    dataKey="active"
                    stackId="2"
                    stroke="#10b981"
                    fill="#10b981"
                    fillOpacity={0.6}
                    name="Actifs"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

// Onglet Entreprises
interface BusinessesTabProps {
  data: BusinessMetrics;
}

const BusinessesTab: React.FC<BusinessesTabProps> = ({ data }) => {
  return (
    <div className="space-y-6">
      {/* Métriques entreprises */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <MetricCard
          title="Total Entreprises"
          value={data.totalBusinesses}
          change={8.5}
          icon={Building2}
          color="bg-blue-500"
        />
        <MetricCard
          title="Vérifiées"
          value={data.verifiedBusinesses}
          change={15.2}
          icon={CheckCircle}
          color="bg-green-500"
        />
        <MetricCard
          title="Actives"
          value={data.activeBusinesses}
          change={6.8}
          icon={Activity}
          color="bg-purple-500"
        />
        <MetricCard
          title="Revenus Moyen"
          value={data.revenueByBusiness.reduce((sum, b) => sum + b.revenue, 0) / Math.max(data.revenueByBusiness.length, 1)}
          change={12.3}
          icon={DollarSign}
          color="bg-orange-500"
          format="currency"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Répartition par catégorie */}
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition par Catégorie</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data.businessesByCategory.length > 0 ? data.businessesByCategory : generateMockBusinessCategories()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="category"
                    tick={{ fontSize: 12 }}
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip
                    formatter={(value: any, name: string) => [
                      `${value} entreprises`,
                      'Nombre'
                    ]}
                  />
                  <Bar
                    dataKey="count"
                    fill="#3b82f6"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardBody>
        </Card>

        {/* Croissance des entreprises */}
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Croissance des Entreprises</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data.businessGrowth.length > 0 ? data.businessGrowth : generateMockBusinessGrowth()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return `${date.getDate()}/${date.getMonth() + 1}`;
                    }}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip
                    labelFormatter={(value) => {
                      const date = new Date(value);
                      return date.toLocaleDateString('fr-FR');
                    }}
                    formatter={(value: any, name: string) => [
                      typeof value === 'number' ? value.toLocaleString('fr-FR') : value,
                      name
                    ]}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="total"
                    stroke="#3b82f6"
                    strokeWidth={3}
                    dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                    name="Total"
                  />
                  <Line
                    type="monotone"
                    dataKey="verified"
                    stroke="#10b981"
                    strokeWidth={3}
                    dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                    name="Vérifiées"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

// Onglet Contenu avec graphiques
const ContentTab: React.FC<{ data: ContentMetrics }> = ({ data }) => {
  const COLORS = ['#8b5cf6', '#ec4899', '#f59e0b', '#10b981', '#3b82f6'];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <MetricCard
          title="Total Publications"
          value={data.totalPosts}
          change={8.4}
          icon={FileText}
          color="bg-purple-500"
        />
        <MetricCard
          title="Contenu Viral"
          value={data.viralContent.length}
          change={25.6}
          icon={TrendingUp}
          color="bg-pink-500"
        />
        <MetricCard
          title="En Modération"
          value={data.contentModeration.reduce((sum, item) => sum + item.count, 0)}
          change={-12.3}
          icon={Eye}
          color="bg-orange-500"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Répartition par type de post */}
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition par Type</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.postsByType.length > 0 ? data.postsByType : generateMockPostTypes()}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ type, count }) => `${type} (${count})`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {(data.postsByType.length > 0 ? data.postsByType : generateMockPostTypes()).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardBody>
        </Card>

        {/* Évolution du contenu */}
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Évolution du Contenu</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={data.postsGrowth.length > 0 ? data.postsGrowth : generateMockContentGrowth()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return `${date.getDate()}/${date.getMonth() + 1}`;
                    }}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip
                    labelFormatter={(value) => {
                      const date = new Date(value);
                      return date.toLocaleDateString('fr-FR');
                    }}
                  />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="posts"
                    stackId="1"
                    stroke="#8b5cf6"
                    fill="#8b5cf6"
                    fillOpacity={0.6}
                    name="Publications"
                  />
                  <Area
                    type="monotone"
                    dataKey="engagement"
                    stackId="2"
                    stroke="#ec4899"
                    fill="#ec4899"
                    fillOpacity={0.6}
                    name="Engagement"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

const RevenueTab: React.FC<{ data: RevenueMetrics }> = ({ data }) => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      <MetricCard
        title="Revenus Total"
        value={data.totalRevenue}
        change={15.6}
        icon={DollarSign}
        color="bg-green-500"
        format="currency"
      />
      <MetricCard
        title="ARPU"
        value={data.averageRevenuePerUser}
        change={8.2}
        icon={Users}
        color="bg-blue-500"
        format="currency"
      />
      <MetricCard
        title="LTV"
        value={data.lifetimeValue}
        change={12.4}
        icon={Target}
        color="bg-purple-500"
        format="currency"
      />
      <MetricCard
        title="Taux de Churn"
        value={data.churnRate}
        change={-3.1}
        icon={TrendingDown}
        color="bg-red-500"
        format="percentage"
      />
    </div>

    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Évolution des revenus */}
      <Card>
        <CardBody className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Évolution des Revenus</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data.monthlyRevenue.length > 0 ? data.monthlyRevenue : generateMockRevenue()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" tick={{ fontSize: 12 }} />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip
                  formatter={(value: any) => [
                    new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XOF' }).format(value),
                    'Revenus'
                  ]}
                />
                <Line
                  type="monotone"
                  dataKey="revenue"
                  stroke="#10b981"
                  strokeWidth={3}
                  dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardBody>
      </Card>

      {/* Répartition par source */}
      <Card>
        <CardBody className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenus par Source</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={data.revenueBySource.length > 0 ? data.revenueBySource : generateMockRevenueSource()}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ source, percentage }) => `${source} (${percentage}%)`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="amount"
                >
                  {(data.revenueBySource.length > 0 ? data.revenueBySource : generateMockRevenueSource()).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={['#10b981', '#3b82f6', '#8b5cf6', '#f59e0b'][index % 4]} />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value: any) => [
                    new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XOF' }).format(value),
                    'Montant'
                  ]}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </CardBody>
      </Card>
    </div>
  </div>
);

const EngagementTab: React.FC<{ data: EngagementMetrics }> = ({ data }) => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <MetricCard
        title="Total Engagements"
        value={data.totalEngagements}
        change={18.7}
        icon={Activity}
        color="bg-blue-500"
      />
      <MetricCard
        title="Taux d'Engagement"
        value={data.engagementRate}
        change={5.3}
        icon={Target}
        color="bg-green-500"
        format="percentage"
      />
      <MetricCard
        title="Sessions Moyennes"
        value={data.userSessions.reduce((sum, s) => sum + s.sessions, 0) / Math.max(data.userSessions.length, 1)}
        change={7.8}
        icon={Clock}
        color="bg-purple-500"
      />
    </div>

    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Tendances d'engagement */}
      <Card>
        <CardBody className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Tendances d'Engagement</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={data.engagementTrends.length > 0 ? data.engagementTrends : generateMockEngagementTrends()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => {
                    const date = new Date(value);
                    return `${date.getDate()}/${date.getMonth() + 1}`;
                  }}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip
                  labelFormatter={(value) => {
                    const date = new Date(value);
                    return date.toLocaleDateString('fr-FR');
                  }}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="likes"
                  stackId="1"
                  stroke="#3b82f6"
                  fill="#3b82f6"
                  fillOpacity={0.6}
                  name="J'aime"
                />
                <Area
                  type="monotone"
                  dataKey="comments"
                  stackId="1"
                  stroke="#10b981"
                  fill="#10b981"
                  fillOpacity={0.6}
                  name="Commentaires"
                />
                <Area
                  type="monotone"
                  dataKey="shares"
                  stackId="1"
                  stroke="#8b5cf6"
                  fill="#8b5cf6"
                  fillOpacity={0.6}
                  name="Partages"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardBody>
      </Card>

      {/* Heures de pic */}
      <Card>
        <CardBody className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Heures de Pic d'Activité</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data.peakHours.length > 0 ? data.peakHours : generateMockPeakHours()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="hour"
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => `${value}h`}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip
                  formatter={(value: any) => [value, 'Engagements']}
                  labelFormatter={(value) => `${value}h00`}
                />
                <Bar
                  dataKey="engagement"
                  fill="#3b82f6"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardBody>
      </Card>
    </div>
  </div>
);

const PerformanceTab: React.FC<{ data: PerformanceMetrics }> = ({ data }) => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      <MetricCard
        title="Temps de Chargement"
        value={`${data.pageLoadTime.toFixed(2)}s`}
        change={-5.2}
        icon={Clock}
        color="bg-blue-500"
      />
      <MetricCard
        title="Temps API"
        value={`${data.apiResponseTime.toFixed(0)}ms`}
        change={-8.1}
        icon={Zap}
        color="bg-green-500"
      />
      <MetricCard
        title="Taux d'Erreur"
        value={data.errorRate}
        change={-15.3}
        icon={AlertTriangle}
        color="bg-red-500"
        format="percentage"
      />
      <MetricCard
        title="Disponibilité"
        value={data.uptime}
        change={0.1}
        icon={CheckCircle}
        color="bg-emerald-500"
        format="percentage"
      />
    </div>
  </div>
);

const RealTimeTab: React.FC<{ data: RealTimeMetrics }> = ({ data }) => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      <MetricCard
        title="Utilisateurs en Ligne"
        value={data.activeUsers}
        icon={Users}
        color="bg-green-500"
        description="Connectés maintenant"
      />
      <MetricCard
        title="Sessions Actives"
        value={data.currentSessions}
        icon={Activity}
        color="bg-blue-500"
        description="Sessions en cours"
      />
      <MetricCard
        title="Charge Système"
        value={data.systemLoad}
        icon={Zap}
        color="bg-orange-500"
        format="percentage"
        description="Utilisation serveur"
      />
      <MetricCard
        title="Mémoire"
        value={data.memoryUsage}
        icon={Activity}
        color="bg-purple-500"
        format="percentage"
        description="RAM utilisée"
      />
    </div>
  </div>
);

// Fonctions de génération de données mock pour les graphiques
const generateMockTrends = (): TrendData[] => {
  const data: TrendData[] = [];
  const today = new Date();

  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    data.push({
      date: date.toISOString().split('T')[0],
      users: Math.floor(1000 + Math.random() * 500 + i * 10),
      businesses: Math.floor(50 + Math.random() * 30 + i * 2),
      posts: Math.floor(200 + Math.random() * 100 + i * 5),
      revenue: Math.floor(5000 + Math.random() * 2000 + i * 100),
      engagement: Math.floor(800 + Math.random() * 400 + i * 8)
    });
  }

  return data;
};

const generateMockUserRoles = () => [
  { role: 'standard', count: 1250, percentage: 62.5 },
  { role: 'business', count: 450, percentage: 22.5 },
  { role: 'premium', count: 200, percentage: 10.0 },
  { role: 'admin', count: 80, percentage: 4.0 },
  { role: 'moderator', count: 20, percentage: 1.0 }
];

const generateMockUserGrowth = () => {
  const data = [];
  const today = new Date();

  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    data.push({
      date: date.toISOString().split('T')[0],
      users: Math.floor(1000 + i * 15 + Math.random() * 50),
      active: Math.floor(600 + i * 8 + Math.random() * 30)
    });
  }

  return data;
};

const generateMockBusinessCategories = () => [
  { category: 'Restaurant', count: 145 },
  { category: 'Commerce', count: 98 },
  { category: 'Services', count: 87 },
  { category: 'Technologie', count: 76 },
  { category: 'Santé', count: 65 },
  { category: 'Éducation', count: 54 },
  { category: 'Immobilier', count: 43 },
  { category: 'Transport', count: 32 }
];

const generateMockBusinessGrowth = () => {
  const data = [];
  const today = new Date();

  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    data.push({
      date: date.toISOString().split('T')[0],
      total: Math.floor(400 + i * 5 + Math.random() * 20),
      verified: Math.floor(200 + i * 3 + Math.random() * 15)
    });
  }

  return data;
};

const generateMockPostTypes = () => [
  { type: 'Coup de cœur', count: 245 },
  { type: 'Coup de gueule', count: 189 },
  { type: 'Demande d\'avis', count: 156 },
  { type: 'Avis produit', count: 134 },
  { type: 'Recommandation', count: 98 }
];

const generateMockContentGrowth = () => {
  const data = [];
  const today = new Date();

  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    data.push({
      date: date.toISOString().split('T')[0],
      posts: Math.floor(50 + i * 2 + Math.random() * 20),
      engagement: Math.floor(150 + i * 5 + Math.random() * 50)
    });
  }

  return data;
};

const generateMockRevenue = () => {
  const months = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'];
  return months.map((month, index) => ({
    month,
    revenue: Math.floor(50000 + index * 5000 + Math.random() * 20000),
    growth: Math.floor(-10 + Math.random() * 30)
  }));
};

const generateMockRevenueSource = () => [
  { source: 'Abonnements Premium', amount: 125000, percentage: 45.5 },
  { source: 'Publicités', amount: 89000, percentage: 32.4 },
  { source: 'Commissions', amount: 45000, percentage: 16.4 },
  { source: 'Services', amount: 15000, percentage: 5.7 }
];

const generateMockEngagementTrends = () => {
  const data = [];
  const today = new Date();

  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    data.push({
      date: date.toISOString().split('T')[0],
      likes: Math.floor(200 + i * 3 + Math.random() * 50),
      comments: Math.floor(80 + i * 2 + Math.random() * 30),
      shares: Math.floor(30 + i * 1 + Math.random() * 15)
    });
  }

  return data;
};

const generateMockPeakHours = () => {
  const hours = [];
  for (let i = 0; i < 24; i++) {
    let engagement = Math.floor(Math.random() * 100);

    // Simuler des pics d'activité aux heures typiques
    if (i >= 8 && i <= 10) engagement += 50; // Matin
    if (i >= 12 && i <= 14) engagement += 70; // Midi
    if (i >= 18 && i <= 22) engagement += 80; // Soirée

    hours.push({
      hour: i,
      engagement
    });
  }

  return hours;
};

export default AdvancedAnalytics;
