import React from 'react';
import { Tag, Layout, Monitor, Smartphone, Tablet, Eye } from 'lucide-react';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import Button from '../components/ui/Button';
import { useNavigate } from 'react-router-dom';

const OffersLayoutTestPage: React.FC = () => {
  const navigate = useNavigate();

  const layoutChanges = [
    {
      title: "Grille 2 colonnes",
      description: "Passage de 3 colonnes à 2 colonnes pour une meilleure occupation de l'espace",
      icon: <Layout className="text-blue-500" size={20} />,
      before: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
      after: "grid-cols-1 xl:grid-cols-2",
      improvement: "Cartes plus larges et mieux lisibles"
    },
    {
      title: "Images agrandies",
      description: "Hauteur des images augmentée de 192px à 256px",
      icon: <Monitor className="text-green-500" size={20} />,
      before: "h-48 (192px)",
      after: "h-64 (256px)",
      improvement: "Images plus visibles et attractives"
    },
    {
      title: "Contenu élargi",
      description: "Padding augmenté et tailles de texte optimisées",
      icon: <Tablet className="text-purple-500" size={20} />,
      before: "p-6, text-sm",
      after: "p-8, text-base",
      improvement: "Meilleure lisibilité et espacement"
    },
    {
      title: "Badges agrandis",
      description: "Badges de réduction et type d'offre plus visibles",
      icon: <Smartphone className="text-orange-500" size={20} />,
      before: "px-3 py-1, text-sm",
      after: "px-4 py-2, text-base",
      improvement: "Badges plus proéminents"
    }
  ];

  const responsiveBreakpoints = [
    {
      device: "Mobile",
      size: "< 1280px",
      columns: "1 colonne",
      description: "Affichage en pile verticale pour une lecture optimale",
      icon: <Smartphone className="text-blue-500" size={16} />
    },
    {
      device: "Desktop",
      size: "≥ 1280px",
      columns: "2 colonnes",
      description: "Affichage côte à côte avec cartes élargies",
      icon: <Monitor className="text-green-500" size={16} />
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* En-tête */}
      <div className="text-center">
        <div className="flex items-center justify-center space-x-3 mb-4">
          <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full">
            <Layout className="text-white" size={24} />
          </div>
          <h1 className="text-4xl font-bold text-gray-900">Test Layout 2 Colonnes</h1>
        </div>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Vérification de l'optimisation de l'affichage des offres en 2 colonnes au lieu de 3
        </p>
      </div>

      {/* Comparaison avant/après */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card className="border-red-200">
          <CardHeader className="bg-red-50">
            <h3 className="text-xl font-semibold text-red-900">❌ Avant (3 colonnes)</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="bg-gray-100 p-4 rounded-lg">
                <code className="text-sm">grid-cols-1 md:grid-cols-2 lg:grid-cols-3</code>
              </div>
              <ul className="space-y-2 text-sm text-red-700">
                <li>• Cartes trop étroites sur grand écran</li>
                <li>• Images petites (h-48 = 192px)</li>
                <li>• Texte difficile à lire</li>
                <li>• Badges peu visibles</li>
                <li>• Espacement insuffisant</li>
              </ul>
            </div>
          </CardBody>
        </Card>

        <Card className="border-green-200">
          <CardHeader className="bg-green-50">
            <h3 className="text-xl font-semibold text-green-900">✅ Après (2 colonnes)</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="bg-gray-100 p-4 rounded-lg">
                <code className="text-sm">grid-cols-1 xl:grid-cols-2</code>
              </div>
              <ul className="space-y-2 text-sm text-green-700">
                <li>• Cartes plus larges et aérées</li>
                <li>• Images agrandies (h-64 = 256px)</li>
                <li>• Texte plus lisible (text-base)</li>
                <li>• Badges plus proéminents</li>
                <li>• Meilleur espacement (p-8)</li>
              </ul>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Détails des modifications */}
      <Card>
        <CardHeader>
          <h2 className="text-2xl font-bold text-gray-900">🔧 Modifications Apportées</h2>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {layoutChanges.map((change, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start space-x-3 mb-3">
                  {change.icon}
                  <div>
                    <h3 className="font-semibold text-gray-900">{change.title}</h3>
                    <p className="text-sm text-gray-600">{change.description}</p>
                  </div>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-red-600">Avant:</span>
                    <code className="bg-red-50 px-2 py-1 rounded text-red-700">{change.before}</code>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-green-600">Après:</span>
                    <code className="bg-green-50 px-2 py-1 rounded text-green-700">{change.after}</code>
                  </div>
                  <div className="mt-2 p-2 bg-blue-50 rounded text-blue-700 text-xs">
                    💡 {change.improvement}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Responsive design */}
      <Card>
        <CardHeader>
          <h2 className="text-2xl font-bold text-gray-900">📱 Design Responsive</h2>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {responsiveBreakpoints.map((breakpoint, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-6 text-center">
                <div className="flex items-center justify-center mb-4">
                  {breakpoint.icon}
                  <h3 className="font-semibold text-gray-900 ml-2">{breakpoint.device}</h3>
                </div>
                <div className="space-y-2">
                  <div className="text-lg font-bold text-blue-600">{breakpoint.columns}</div>
                  <div className="text-sm text-gray-500">{breakpoint.size}</div>
                  <p className="text-sm text-gray-600">{breakpoint.description}</p>
                </div>
              </div>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Avantages */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50">
        <CardHeader>
          <h2 className="text-2xl font-bold text-gray-900">🎯 Avantages du Layout 2 Colonnes</h2>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                <Eye className="text-green-600" size={24} />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Meilleure Visibilité</h3>
              <p className="text-sm text-gray-600">
                Images plus grandes et contenu plus lisible pour une meilleure expérience utilisateur
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                <Layout className="text-blue-600" size={24} />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Utilisation Optimale</h3>
              <p className="text-sm text-gray-600">
                Meilleure occupation de l'espace disponible sans surcharger l'interface
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-purple-100 rounded-full flex items-center justify-center">
                <Monitor className="text-purple-600" size={24} />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Responsive</h3>
              <p className="text-sm text-gray-600">
                Adaptation automatique selon la taille d'écran pour tous les appareils
              </p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Actions de test */}
      <Card className="border-l-4 border-blue-500">
        <CardBody>
          <div className="text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">🧪 Tester le Nouveau Layout</h3>
            <p className="text-gray-600 mb-6">
              Cliquez sur le bouton ci-dessous pour voir les offres avec le nouveau layout 2 colonnes
            </p>
            
            <div className="flex flex-wrap justify-center gap-4">
              <Button
                onClick={() => navigate('/offers')}
                size="lg"
                leftIcon={<Tag size={20} />}
                className="font-semibold"
              >
                Voir Offres (2 colonnes)
              </Button>
              
              <Button
                onClick={() => navigate('/offers-test')}
                variant="outline"
                size="lg"
                leftIcon={<Eye size={20} />}
              >
                Page de Test Complète
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Instructions */}
      <Card className="bg-yellow-50 border border-yellow-200">
        <CardBody>
          <h3 className="font-semibold text-yellow-900 mb-3">📋 Instructions de Test</h3>
          <ol className="list-decimal list-inside space-y-2 text-yellow-800 text-sm">
            <li>Accédez à la page "Offres et Promotions" via le bouton ci-dessus</li>
            <li>Observez l'affichage en 2 colonnes sur grand écran (≥ 1280px)</li>
            <li>Vérifiez l'affichage en 1 colonne sur écran plus petit</li>
            <li>Notez la taille agrandie des images et du contenu</li>
            <li>Testez les interactions (hover, boutons, filtres)</li>
            <li>Comparez avec l'ancien layout 3 colonnes</li>
          </ol>
        </CardBody>
      </Card>

      {/* Statut */}
      <div className="text-center py-8">
        <div className="inline-flex items-center space-x-2 bg-green-100 text-green-800 px-6 py-3 rounded-full">
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          <span className="font-semibold">Layout 2 colonnes implémenté et optimisé</span>
        </div>
      </div>
    </div>
  );
};

export default OffersLayoutTestPage;
