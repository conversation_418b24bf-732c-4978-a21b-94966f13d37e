-- Migration pour créer la table des paramètres utilisateur

-- <PERSON><PERSON><PERSON> la table user_settings
CREATE TABLE IF NOT EXISTS user_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  
  -- Paramètres de notification (JSONB)
  notifications JSONB DEFAULT '{
    "emailNotifications": true,
    "pushNotifications": true,
    "marketingEmails": false,
    "statusUpdates": true,
    "newFollowers": true,
    "postLikes": true,
    "comments": true
  }'::jsonb,
  
  -- Paramètres de confidentialité (JSONB)
  privacy JSONB DEFAULT '{
    "profileVisibility": "public",
    "showEmail": false,
    "showLocation": true,
    "showActivity": true,
    "allowMessages": true,
    "allowFollows": true
  }'::jsonb,
  
  -- Paramètres d'affichage (JSONB)
  display JSONB DEFAULT '{
    "theme": "light",
    "language": "fr",
    "timezone": "Europe/Paris",
    "compactMode": false
  }'::jsonb,
  
  -- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Créer un index unique sur user_id (un seul paramétrage par utilisateur)
CREATE UNIQUE INDEX IF NOT EXISTS user_settings_user_id_idx ON user_settings(user_id);

-- Créer un index sur updated_at pour les requêtes de synchronisation
CREATE INDEX IF NOT EXISTS user_settings_updated_at_idx ON user_settings(updated_at);

-- Activer RLS (Row Level Security)
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- Politique RLS : Les utilisateurs peuvent voir et modifier leurs propres paramètres
CREATE POLICY "Users can view their own settings"
ON user_settings FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own settings"
ON user_settings FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own settings"
ON user_settings FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own settings"
ON user_settings FOR DELETE
USING (auth.uid() = user_id);

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_user_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour automatiquement updated_at
CREATE TRIGGER trigger_update_user_settings_updated_at
  BEFORE UPDATE ON user_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_user_settings_updated_at();

-- Fonction pour créer des paramètres par défaut pour un nouvel utilisateur
CREATE OR REPLACE FUNCTION create_default_user_settings()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO user_settings (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour créer automatiquement des paramètres par défaut lors de la création d'un profil
CREATE TRIGGER trigger_create_default_user_settings
  AFTER INSERT ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION create_default_user_settings();

-- Créer des paramètres par défaut pour les utilisateurs existants
INSERT INTO user_settings (user_id)
SELECT id FROM profiles
WHERE id NOT IN (SELECT user_id FROM user_settings)
ON CONFLICT (user_id) DO NOTHING;

-- Fonction utilitaire pour récupérer les paramètres d'un utilisateur
CREATE OR REPLACE FUNCTION get_user_settings(target_user_id UUID)
RETURNS TABLE (
  notifications JSONB,
  privacy JSONB,
  display JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    us.notifications,
    us.privacy,
    us.display
  FROM user_settings us
  WHERE us.user_id = target_user_id;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour mettre à jour des paramètres spécifiques
CREATE OR REPLACE FUNCTION update_user_setting(
  target_user_id UUID,
  setting_type TEXT,
  setting_value JSONB
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Vérifier que l'utilisateur peut modifier ces paramètres
  IF auth.uid() != target_user_id THEN
    RETURN FALSE;
  END IF;
  
  -- Mettre à jour le paramètre spécifique
  CASE setting_type
    WHEN 'notifications' THEN
      UPDATE user_settings 
      SET notifications = setting_value 
      WHERE user_id = target_user_id;
    WHEN 'privacy' THEN
      UPDATE user_settings 
      SET privacy = setting_value 
      WHERE user_id = target_user_id;
    WHEN 'display' THEN
      UPDATE user_settings 
      SET display = setting_value 
      WHERE user_id = target_user_id;
    ELSE
      RETURN FALSE;
  END CASE;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Vue pour joindre les profils avec leurs paramètres
CREATE OR REPLACE VIEW profiles_with_settings AS
SELECT 
  p.*,
  us.notifications,
  us.privacy,
  us.display,
  us.updated_at as settings_updated_at
FROM profiles p
LEFT JOIN user_settings us ON p.id = us.user_id;

-- Accorder les permissions sur la vue
GRANT SELECT ON profiles_with_settings TO authenticated;

-- Commentaires pour la documentation
COMMENT ON TABLE user_settings IS 'Paramètres personnalisés des utilisateurs (notifications, confidentialité, affichage)';
COMMENT ON COLUMN user_settings.notifications IS 'Paramètres de notification au format JSON';
COMMENT ON COLUMN user_settings.privacy IS 'Paramètres de confidentialité au format JSON';
COMMENT ON COLUMN user_settings.display IS 'Paramètres d''affichage au format JSON';

-- Afficher un résumé
DO $$
DECLARE
  settings_count INTEGER;
  profiles_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO settings_count FROM user_settings;
  SELECT COUNT(*) INTO profiles_count FROM profiles;
  
  RAISE NOTICE '=== PARAMÈTRES UTILISATEUR CRÉÉS ===';
  RAISE NOTICE 'Profils existants: %', profiles_count;
  RAISE NOTICE 'Paramètres créés: %', settings_count;
  RAISE NOTICE 'Couverture: %.1f%%', (settings_count::float / profiles_count * 100);
  RAISE NOTICE '=====================================';
END $$;
