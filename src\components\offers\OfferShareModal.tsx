import React, { useState } from 'react';
import { 
  X, Copy, Facebook, Twitter, MessageCircle, Mail, 
  Link2, Download, Heart, Bookmark, ExternalLink 
} from 'lucide-react';
import { Offer } from '../../data/realOffersData';
import Button from '../ui/Button';
import Modal from '../ui/Modal';
import { useToast } from '../ui/ToastContainer';

interface OfferShareModalProps {
  offer: Offer;
  isOpen: boolean;
  onClose: () => void;
  onShareComplete: (platform: string) => void;
}

const OfferShareModal: React.FC<OfferShareModalProps> = ({ 
  offer, 
  isOpen, 
  onClose, 
  onShareComplete 
}) => {
  const [copied, setCopied] = useState(false);
  const [customMessage, setCustomMessage] = useState('');
  const { showSuccess, showError } = useToast();

  const offerUrl = `${window.location.origin}/offers/${offer.id}`;
  const shareTitle = `🎯 ${offer.title} - ${offer.businessName}`;
  const shareText = `Découvrez cette offre exceptionnelle chez ${offer.businessName} : ${offer.title}. ${offer.discount}% de réduction !`;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(offerUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      showSuccess('Lien copié !', 'Le lien de l\'offre a été copié dans le presse-papiers');
      onShareComplete('copy_link');
    } catch (error) {
      console.error('Erreur lors de la copie:', error);
      showError('Erreur', 'Impossible de copier le lien');
    }
  };

  const handleSocialShare = (platform: string, url: string) => {
    // Ajouter le message personnalisé si présent
    let finalUrl = url;
    if (customMessage) {
      const encodedMessage = encodeURIComponent(customMessage + '\n\n');
      if (platform === 'twitter') {
        finalUrl = url.replace(encodeURIComponent(shareText), encodedMessage + encodeURIComponent(shareText));
      } else if (platform === 'whatsapp') {
        finalUrl = url.replace(encodeURIComponent(shareText), encodedMessage + encodeURIComponent(shareText));
      }
    }

    window.open(finalUrl, '_blank', 'width=600,height=400');
    showSuccess('Partage effectué !', `Offre partagée sur ${platform}`);
    onShareComplete(platform);
    onClose();
  };

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: shareTitle,
          text: shareText,
          url: offerUrl,
        });
        showSuccess('Partage effectué !', 'Offre partagée avec succès');
        onShareComplete('native');
        onClose();
      } catch (error) {
        console.error('Erreur lors du partage natif:', error);
      }
    }
  };

  const handleDownloadImage = () => {
    // Créer un lien de téléchargement pour l'image de l'offre
    const link = document.createElement('a');
    link.href = offer.imageUrl;
    link.download = `${offer.businessName}-${offer.title}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    showSuccess('Téléchargement lancé !', 'L\'image de l\'offre est en cours de téléchargement');
    onShareComplete('download');
  };

  const shareOptions = [
    {
      name: 'Copier le lien',
      icon: Copy,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100 hover:bg-gray-200',
      action: handleCopyLink,
      description: 'Copier le lien dans le presse-papiers'
    },
    {
      name: 'Facebook',
      icon: Facebook,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 hover:bg-blue-200',
      action: () => handleSocialShare('facebook', 
        `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(offerUrl)}&quote=${encodeURIComponent(shareText)}`
      ),
      description: 'Partager sur Facebook'
    },
    {
      name: 'Twitter',
      icon: Twitter,
      color: 'text-sky-500',
      bgColor: 'bg-sky-100 hover:bg-sky-200',
      action: () => handleSocialShare('twitter',
        `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(offerUrl)}&hashtags=Customeroom,Offre,${offer.category.replace(/\s+/g, '')}`
      ),
      description: 'Partager sur Twitter'
    },
    {
      name: 'WhatsApp',
      icon: MessageCircle,
      color: 'text-green-500',
      bgColor: 'bg-green-100 hover:bg-green-200',
      action: () => handleSocialShare('whatsapp',
        `https://wa.me/?text=${encodeURIComponent(shareText + '\n' + offerUrl)}`
      ),
      description: 'Partager sur WhatsApp'
    },
    {
      name: 'Email',
      icon: Mail,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 hover:bg-orange-200',
      action: () => handleSocialShare('email',
        `mailto:?subject=${encodeURIComponent(shareTitle)}&body=${encodeURIComponent(shareText + '\n\nVoir l\'offre : ' + offerUrl)}`
      ),
      description: 'Partager par email'
    },
    {
      name: 'Télécharger',
      icon: Download,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 hover:bg-purple-200',
      action: handleDownloadImage,
      description: 'Télécharger l\'image de l\'offre'
    }
  ];

  // Ajouter l'option de partage natif si disponible
  if (navigator.share) {
    shareOptions.unshift({
      name: 'Partager',
      icon: ExternalLink,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100 hover:bg-indigo-200',
      action: handleNativeShare,
      description: 'Utiliser le menu de partage du système'
    });
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <div className="p-6">
        {/* En-tête */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">Partager cette offre</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Aperçu de l'offre */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
          <div className="flex items-start space-x-4">
            <img
              src={offer.imageUrl}
              alt={offer.title}
              className="w-16 h-16 object-cover rounded-lg"
            />
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 text-sm">{offer.title}</h3>
              <p className="text-gray-600 text-sm">{offer.businessName}</p>
              <div className="flex items-center space-x-2 mt-1">
                <span className="bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded">
                  -{offer.discount}%
                </span>
                <span className="text-gray-500 text-xs">{offer.category}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Message personnalisé */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Message personnalisé (optionnel)
          </label>
          <textarea
            value={customMessage}
            onChange={(e) => setCustomMessage(e.target.value)}
            placeholder="Ajoutez votre propre message..."
            className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={3}
            maxLength={200}
          />
          <div className="text-right text-xs text-gray-500 mt-1">
            {customMessage.length}/200
          </div>
        </div>

        {/* Options de partage */}
        <div className="grid grid-cols-2 gap-3 mb-6">
          {shareOptions.map((option) => (
            <button
              key={option.name}
              onClick={option.action}
              className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${option.bgColor} group`}
            >
              <div className={`${option.color} group-hover:scale-110 transition-transform`}>
                <option.icon size={20} />
              </div>
              <div className="text-left">
                <div className="font-medium text-gray-900 text-sm">{option.name}</div>
                <div className="text-gray-600 text-xs">{option.description}</div>
              </div>
            </button>
          ))}
        </div>

        {/* Lien à copier */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Lien de l'offre
          </label>
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={offerUrl}
              readOnly
              className="flex-1 p-3 bg-gray-50 border border-gray-300 rounded-lg text-sm text-gray-600"
            />
            <Button
              onClick={handleCopyLink}
              variant={copied ? 'success' : 'outline'}
              size="sm"
              className="flex items-center"
            >
              {copied ? (
                <>
                  <span className="mr-2">✓</span>
                  Copié
                </>
              ) : (
                <>
                  <Copy size={16} className="mr-2" />
                  Copier
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            Fermer
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default OfferShareModal;
