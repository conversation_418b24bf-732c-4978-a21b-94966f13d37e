import React, { useState, useEffect } from 'react';
import { Crown, Gift, Star, Shield, Clock, AlertTriangle, CheckCircle } from 'lucide-react';
import { SubscriptionService, SubscriptionStatus } from '../../services/subscriptionService';

interface SubscriptionBadgeProps {
  businessId: string;
  size?: 'sm' | 'md' | 'lg';
  showDetails?: boolean;
}

const SubscriptionBadge: React.FC<SubscriptionBadgeProps> = ({ 
  businessId, 
  size = 'md', 
  showDetails = false 
}) => {
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSubscriptionStatus();
  }, [businessId]);

  const loadSubscriptionStatus = async () => {
    setLoading(true);
    try {
      const status = await SubscriptionService.getBusinessSubscriptionStatus(businessId);
      setSubscriptionStatus(status);
    } catch (error) {
      console.error('Erreur lors du chargement du statut d\'abonnement:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPlanConfig = (plan: string) => {
    switch (plan) {
      case 'free-trial':
        return {
          icon: <Gift size={size === 'sm' ? 14 : size === 'md' ? 16 : 20} />,
          color: 'bg-green-100 text-green-800 border-green-200',
          label: 'Essai Gratuit'
        };
      case 'starter':
        return {
          icon: <Star size={size === 'sm' ? 14 : size === 'md' ? 16 : 20} />,
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          label: 'Starter'
        };
      case 'professional':
        return {
          icon: <Crown size={size === 'sm' ? 14 : size === 'md' ? 16 : 20} />,
          color: 'bg-purple-100 text-purple-800 border-purple-200',
          label: 'Professional'
        };
      case 'enterprise':
        return {
          icon: <Shield size={size === 'sm' ? 14 : size === 'md' ? 16 : 20} />,
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          label: 'Enterprise'
        };
      default:
        return {
          icon: <Star size={size === 'sm' ? 14 : size === 'md' ? 16 : 20} />,
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          label: 'Plan Inconnu'
        };
    }
  };

  const getStatusIcon = (status: SubscriptionStatus) => {
    if (!status.isActive) {
      return <AlertTriangle size={size === 'sm' ? 12 : size === 'md' ? 14 : 16} className="text-red-500" />;
    }
    if (status.daysRemaining <= 3) {
      return <Clock size={size === 'sm' ? 12 : size === 'md' ? 14 : 16} className="text-yellow-500" />;
    }
    return <CheckCircle size={size === 'sm' ? 12 : size === 'md' ? 14 : 16} className="text-green-500" />;
  };

  if (loading) {
    return (
      <div className={`inline-flex items-center px-2 py-1 rounded-full border bg-gray-100 text-gray-600 ${
        size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'
      }`}>
        <div className="animate-pulse">Chargement...</div>
      </div>
    );
  }

  if (!subscriptionStatus) {
    return null;
  }

  const planConfig = getPlanConfig(subscriptionStatus.plan);
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  };

  return (
    <div className="inline-flex items-center space-x-2">
      {/* Badge principal */}
      <span className={`inline-flex items-center space-x-1 rounded-full border font-medium ${planConfig.color} ${sizeClasses[size]}`}>
        {planConfig.icon}
        <span>{planConfig.label}</span>
      </span>

      {/* Indicateur de statut */}
      {showDetails && (
        <div className="flex items-center space-x-1">
          {getStatusIcon(subscriptionStatus)}
          <span className={`font-medium ${
            !subscriptionStatus.isActive ? 'text-red-600' :
            subscriptionStatus.daysRemaining <= 3 ? 'text-yellow-600' :
            'text-green-600'
          } ${size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'}`}>
            {subscriptionStatus.daysRemaining} jour{subscriptionStatus.daysRemaining > 1 ? 's' : ''}
          </span>
        </div>
      )}

      {/* Alerte d'expiration */}
      {subscriptionStatus.isTrialPeriod && subscriptionStatus.daysRemaining <= 3 && (
        <span className={`inline-flex items-center space-x-1 px-2 py-0.5 rounded-full bg-yellow-100 text-yellow-800 border border-yellow-200 ${
          size === 'sm' ? 'text-xs' : 'text-sm'
        }`}>
          <AlertTriangle size={12} />
          <span>Expire bientôt</span>
        </span>
      )}
    </div>
  );
};

export default SubscriptionBadge;
