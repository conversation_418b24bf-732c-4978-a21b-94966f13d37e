-- SCRIPT DE CORRECTION ÉTAPE PAR ÉTAPE
-- Exécutez ce script dans l'éditeur SQL Supabase

-- =====================================================
-- ÉTAPE 1: NETTOYAGE PRÉALABLE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🧹 ÉTAPE 1: Nettoyage des fonctions existantes...';
END $$;

-- Supprimer les triggers existants
DROP TRIGGER IF EXISTS clean_interests_trigger ON profiles;
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;

-- Supprimer les fonctions existantes
DROP FUNCTION IF EXISTS clean_interests(text);
DROP FUNCTION IF EXISTS trigger_clean_interests();
DROP FUNCTION IF EXISTS update_profiles_updated_at();

DO $$
BEGIN
    RAISE NOTICE '✅ Nettoyage terminé';
END $$;

-- =====================================================
-- ÉTAPE 2: CRÉER LA FONCTION CLEAN_INTERESTS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 ÉTAPE 2: Création de la fonction clean_interests...';
END $$;

CREATE FUNCTION clean_interests(input_interests TEXT)
RETURNS TEXT AS $$
BEGIN
  -- Si l'input est NULL ou vide, retourner NULL
  IF input_interests IS NULL OR trim(input_interests) = '' THEN
    RETURN NULL;
  END IF;
  
  -- Nettoyer les espaces en trop et normaliser les virgules
  RETURN trim(regexp_replace(input_interests, '\s*,\s*', ', ', 'g'));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

DO $$
BEGIN
    RAISE NOTICE '✅ Fonction clean_interests créée';
END $$;

-- =====================================================
-- ÉTAPE 3: CRÉER LA FONCTION TRIGGER
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 ÉTAPE 3: Création de la fonction trigger...';
END $$;

CREATE FUNCTION trigger_clean_interests()
RETURNS TRIGGER AS $$
BEGIN
  NEW.interests = clean_interests(NEW.interests);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DO $$
BEGIN
    RAISE NOTICE '✅ Fonction trigger créée';
END $$;

-- =====================================================
-- ÉTAPE 4: CRÉER LE TRIGGER SUR PROFILES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 ÉTAPE 4: Création du trigger sur profiles...';
END $$;

CREATE TRIGGER clean_interests_trigger
  BEFORE INSERT OR UPDATE OF interests ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION trigger_clean_interests();

DO $$
BEGIN
    RAISE NOTICE '✅ Trigger clean_interests_trigger créé';
END $$;

-- =====================================================
-- ÉTAPE 5: CORRIGER LA FONCTION HANDLE_NEW_USER
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 ÉTAPE 5: Correction de la fonction handle_new_user...';
END $$;

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  -- Insérer le profil utilisateur de base
  INSERT INTO public.profiles (
    id, 
    username, 
    email, 
    role,
    created_at,
    updated_at
  )
  VALUES (
    new.id,
    new.raw_user_meta_data->>'username',
    new.email,
    COALESCE(new.raw_user_meta_data->>'role', 'standard'),
    now(),
    now()
  );

  -- Si l'utilisateur est une entreprise, créer le profil business
  IF (new.raw_user_meta_data->>'role' = 'business') THEN
    INSERT INTO public.business_profiles (
      id,
      business_name,
      business_description,
      created_at,
      updated_at
    )
    VALUES (
      new.id,
      new.raw_user_meta_data->>'businessName',
      COALESCE(new.raw_user_meta_data->>'businessDescription', ''),
      now(),
      now()
    );
  END IF;

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DO $$
BEGIN
    RAISE NOTICE '✅ Fonction handle_new_user corrigée';
END $$;

-- =====================================================
-- ÉTAPE 6: CRÉER LE TRIGGER D'INSCRIPTION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 ÉTAPE 6: Création du trigger d''inscription...';
END $$;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW 
  EXECUTE FUNCTION public.handle_new_user();

DO $$
BEGIN
    RAISE NOTICE '✅ Trigger on_auth_user_created créé';
END $$;

-- =====================================================
-- ÉTAPE 7: FONCTION UPDATED_AT
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 ÉTAPE 7: Création de la fonction updated_at...';
END $$;

CREATE FUNCTION update_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_profiles_updated_at();

DO $$
BEGIN
    RAISE NOTICE '✅ Fonction et trigger updated_at créés';
END $$;

-- =====================================================
-- ÉTAPE 8: PERMISSIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 ÉTAPE 8: Configuration des permissions...';
END $$;

GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO anon;
GRANT EXECUTE ON FUNCTION clean_interests(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION clean_interests(TEXT) TO anon;

DO $$
BEGIN
    RAISE NOTICE '✅ Permissions configurées';
END $$;

-- =====================================================
-- ÉTAPE 9: TESTS DE VÉRIFICATION
-- =====================================================

DO $$
DECLARE
    test_result TEXT;
BEGIN
    RAISE NOTICE '🧪 ÉTAPE 9: Tests de vérification...';
    
    -- Test de la fonction clean_interests
    SELECT clean_interests('  sport  ,   musique,voyage   ,  ') INTO test_result;
    
    IF test_result = 'sport, musique, voyage' THEN
        RAISE NOTICE '✅ Test clean_interests: SUCCÈS';
    ELSE
        RAISE NOTICE '❌ Test clean_interests: ÉCHEC - Résultat: %', test_result;
    END IF;
    
    -- Test NULL
    SELECT clean_interests(NULL) INTO test_result;
    
    IF test_result IS NULL THEN
        RAISE NOTICE '✅ Test NULL: SUCCÈS';
    ELSE
        RAISE NOTICE '❌ Test NULL: ÉCHEC';
    END IF;
END $$;

-- =====================================================
-- ÉTAPE 10: RÉSUMÉ FINAL
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '🎉 CORRECTION TERMINÉE AVEC SUCCÈS !';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Fonction clean_interests créée et testée';
    RAISE NOTICE '✅ Triggers configurés correctement';
    RAISE NOTICE '✅ Fonction handle_new_user corrigée';
    RAISE NOTICE '✅ Permissions accordées';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 PROCHAINES ÉTAPES:';
    RAISE NOTICE '1. Testez l''inscription sur votre application';
    RAISE NOTICE '2. Créez un compte test pour vérifier';
    RAISE NOTICE '3. Vérifiez qu''aucune erreur n''apparaît';
    RAISE NOTICE '';
    RAISE NOTICE '📍 URL de test: http://localhost:5173';
    RAISE NOTICE '';
END $$;

-- Vérification finale des objets créés
SELECT 
    'VÉRIFICATION FINALE' as section,
    'Fonctions créées' as type,
    COUNT(*) as count
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('clean_interests', 'trigger_clean_interests', 'handle_new_user', 'update_profiles_updated_at')

UNION ALL

SELECT 
    'VÉRIFICATION FINALE',
    'Triggers créés',
    COUNT(*)
FROM information_schema.triggers 
WHERE trigger_schema = 'public' 
AND trigger_name IN ('clean_interests_trigger', 'on_auth_user_created', 'update_profiles_updated_at');
