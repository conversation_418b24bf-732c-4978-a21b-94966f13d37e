import { useEffect } from 'react';
import { IPost } from '../types/index';

/**
 * Hook pour valider et surveiller les noms d'entreprises dans les posts
 */
export const useBusinessNameValidation = (posts: IPost[]) => {
  useEffect(() => {
    if (posts.length > 0) {
      const postsWithoutBusinessName = posts.filter(post => 
        !post.businessName || 
        post.businessName.trim() === '' ||
        post.businessName === 'Entreprise non spécifiée'
      );
      
      if (postsWithoutBusinessName.length > 0 && process.env.NODE_ENV === 'development') {
        console.warn('=== POSTS SANS NOM D\'ENTREPRISE ===');
        postsWithoutBusinessName.forEach(post => {
          console.warn(`Post ID: ${post.id}`, {
            businessName: post.businessName,
            productName: post.productName,
            userId: post.userId,
            username: post.username
          });
        });
        console.warn('=====================================');
      }
    }
  }, [posts]);

  // Statistiques sur les noms d'entreprises
  const businessNameStats = posts.reduce((acc, post) => {
    const hasValidBusinessName = post.businessName && 
                                 post.businessName.trim() !== '' && 
                                 post.businessName !== 'Entreprise non spécifiée';
    
    if (hasValidBusinessName) {
      acc.valid++;
      acc.businesses.add(post.businessName);
    } else {
      acc.invalid++;
    }
    
    return acc;
  }, {
    valid: 0,
    invalid: 0,
    businesses: new Set<string>()
  });

  return {
    validBusinessNames: businessNameStats.valid,
    invalidBusinessNames: businessNameStats.invalid,
    uniqueBusinesses: businessNameStats.businesses.size,
    businessList: Array.from(businessNameStats.businesses)
  };
};
