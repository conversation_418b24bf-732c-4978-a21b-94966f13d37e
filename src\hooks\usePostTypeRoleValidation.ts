import { useEffect } from 'react';
import { IPost, PostType, UserRole } from '../types/index';
import { canUserCreatePostType } from '../utils/postTypeValidation';

/**
 * Hook pour valider la cohérence entre les types de posts et les rôles des auteurs
 */
export const usePostTypeRoleValidation = (posts: IPost[]) => {
  useEffect(() => {
    if (posts.length > 0 && process.env.NODE_ENV === 'development') {
      const invalidPosts = posts.filter(post => {
        if (!post.authorRole || !post.type) return true;
        return !canUserCreatePostType(post.authorRole, post.type);
      });

      if (invalidPosts.length > 0) {
        console.warn('=== POSTS AVEC TYPES/RÔLES INCOHÉRENTS ===');
        invalidPosts.forEach(post => {
          console.warn(`Post ID: ${post.id}`, {
            authorRole: post.authorRole,
            postType: post.type,
            username: post.username,
            businessName: post.businessName,
            shouldBeAllowed: canUserCreatePostType(post.authorRole || UserRole.STANDARD, post.type)
          });
        });
        console.warn('==========================================');
      }
    }
  }, [posts]);

  // Statistiques sur la cohérence des types/rôles
  const validationStats = posts.reduce((acc, post) => {
    acc.total++;
    
    if (!post.authorRole || !post.type) {
      acc.missingData++;
      return acc;
    }

    const isValid = canUserCreatePostType(post.authorRole, post.type);
    
    if (isValid) {
      acc.valid++;
    } else {
      acc.invalid++;
    }

    // Compter par type de post
    acc.byType[post.type] = (acc.byType[post.type] || 0) + 1;
    
    // Compter par rôle
    acc.byRole[post.authorRole] = (acc.byRole[post.authorRole] || 0) + 1;

    // Compter les combinaisons spécifiques
    if (post.authorRole === UserRole.BUSINESS && post.type === PostType.REVIEW) {
      acc.businessReviews++;
    }
    if (post.authorRole === UserRole.STANDARD && (post.type === PostType.FAVORITE || post.type === PostType.COMPLAINT)) {
      acc.userOpinions++;
    }
    
    return acc;
  }, {
    total: 0,
    valid: 0,
    invalid: 0,
    missingData: 0,
    byType: {} as Record<PostType, number>,
    byRole: {} as Record<UserRole, number>,
    businessReviews: 0,
    userOpinions: 0
  });

  return {
    totalPosts: validationStats.total,
    validPosts: validationStats.valid,
    invalidPosts: validationStats.invalid,
    missingDataPosts: validationStats.missingData,
    businessReviews: validationStats.businessReviews,
    userOpinions: validationStats.userOpinions,
    typeDistribution: validationStats.byType,
    roleDistribution: validationStats.byRole,
    validationRate: validationStats.total > 0 ? Math.round((validationStats.valid / validationStats.total) * 100) : 100
  };
};
