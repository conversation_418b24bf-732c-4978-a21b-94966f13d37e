import React from 'react';
import { Star } from 'lucide-react';

interface StarRatingProps {
  rating: number;
  maxRating?: number;
  size?: number;
  interactive?: boolean;
  showValue?: boolean;
  showLabel?: boolean;
  label?: string;
  className?: string;
  onRatingChange?: (rating: number) => void;
}

const StarRating: React.FC<StarRatingProps> = ({
  rating,
  maxRating = 5,
  size = 20,
  interactive = false,
  showValue = false,
  showLabel = false,
  label,
  className = '',
  onRatingChange
}) => {
  const handleStarClick = (starValue: number) => {
    if (interactive && onRatingChange) {
      onRatingChange(starValue);
    }
  };

  return (
    <div className={`star-rating ${className}`}>
      {showLabel && label && (
        <label className="text-sm font-medium text-gray-700 mb-2 block">
          {label}
        </label>
      )}
      
      <div className="flex items-center gap-1">
        {Array.from({ length: maxRating }, (_, index) => {
          const starValue = index + 1;
          const isFilled = starValue <= rating;
          
          return (
            <Star
              key={starValue}
              size={size}
              fill={isFilled ? '#ffc107' : 'none'}
              stroke={isFilled ? '#ffc107' : '#d1d5db'}
              className={`
                ${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'}
                transition-all duration-200 ease-in-out
                ${interactive ? 'hover:fill-yellow-400' : ''}
              `}
              onClick={() => handleStarClick(starValue)}
            />
          );
        })}
        
        {showValue && (
          <span className="ml-2 text-sm font-medium text-gray-700">
            {rating}/{maxRating}
          </span>
        )}
      </div>
    </div>
  );
};

export default StarRating;
