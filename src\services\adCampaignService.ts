import { supabase } from '../lib/supabase';

// Types pour les campagnes publicitaires
export interface AdCampaign {
  id: string;
  business_id: string;
  title: string;
  description: string;
  image_url?: string;
  target_url: string;
  bid_amount: number;
  daily_budget: number;
  total_budget: number;
  spent: number;
  placements: string[];
  target_demographics: any;
  target_locations: string[];
  target_devices: string[];
  status: 'draft' | 'active' | 'paused' | 'completed' | 'rejected';
  start_date?: string;
  end_date?: string;
  impressions: number;
  clicks: number;
  ctr: number;
  created_at: string;
  updated_at: string;
}

export interface AdCreative {
  id: string;
  campaign_id: string;
  title: string;
  description: string;
  image_url: string;
  call_to_action: string;
  variant_name?: string;
  impressions: number;
  clicks: number;
  ctr: number;
  status: 'active' | 'paused' | 'rejected';
  created_at: string;
  updated_at: string;
}

export interface AdPlacement {
  id: string;
  name: string;
  description?: string;
  max_ads_per_hour: number;
  rotation_interval_minutes: number;
  priority_weight: number;
  base_cpc: number;
  competition_multiplier: number;
  is_active: boolean;
}

export interface FeedAd {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  targetUrl: string;
  businessName: string;
  businessLogo: string;
  discount?: number;
}

class AdCampaignService {
  // =====================================================
  // GESTION DES CAMPAGNES
  // =====================================================

  /**
   * Récupère toutes les campagnes d'une entreprise
   */
  async getCampaignsByBusiness(businessId: string): Promise<AdCampaign[]> {
    try {
      const { data, error } = await supabase
        .from('ad_campaigns')
        .select('*')
        .eq('business_id', businessId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des campagnes:', error);
      return [];
    }
  }

  /**
   * Crée une nouvelle campagne publicitaire
   */
  async createCampaign(campaignData: Partial<AdCampaign>): Promise<AdCampaign | null> {
    try {
      const { data, error } = await supabase
        .from('ad_campaigns')
        .insert([campaignData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Erreur lors de la création de la campagne:', error);
      return null;
    }
  }

  /**
   * Met à jour une campagne existante
   */
  async updateCampaign(campaignId: string, updates: Partial<AdCampaign>): Promise<AdCampaign | null> {
    try {
      const { data, error } = await supabase
        .from('ad_campaigns')
        .update(updates)
        .eq('id', campaignId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la campagne:', error);
      return null;
    }
  }

  /**
   * Supprime une campagne
   */
  async deleteCampaign(campaignId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('ad_campaigns')
        .delete()
        .eq('id', campaignId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression de la campagne:', error);
      return false;
    }
  }

  // =====================================================
  // GESTION DES EMPLACEMENTS
  // =====================================================

  /**
   * Récupère tous les emplacements publicitaires disponibles
   */
  async getAdPlacements(): Promise<AdPlacement[]> {
    try {
      const { data, error } = await supabase
        .from('ad_placements')
        .select('*')
        .eq('is_active', true)
        .order('priority_weight', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des emplacements:', error);
      return [];
    }
  }

  // =====================================================
  // AFFICHAGE DES PUBLICITÉS
  // =====================================================

  /**
   * Récupère les publicités actives pour un emplacement donné
   */
  async getActiveAdsForPlacement(placementId: string, limit: number = 10): Promise<FeedAd[]> {
    try {
      const { data, error } = await supabase
        .from('ad_campaigns')
        .select(`
          *,
          profiles!inner(username, profile_picture)
        `)
        .contains('placements', [placementId])
        .eq('status', 'active')
        .gte('total_budget', supabase.raw('spent'))
        .limit(limit);

      if (error) throw error;

      // Transformer les données pour le format FeedAd
      return (data || []).map(campaign => ({
        id: campaign.id,
        title: campaign.title,
        description: campaign.description,
        imageUrl: campaign.image_url || '/images/default-promo.jpg',
        targetUrl: campaign.target_url,
        businessName: campaign.profiles?.username || 'Entreprise',
        businessLogo: campaign.profiles?.profile_picture || '/images/default-business.jpg'
      }));
    } catch (error) {
      console.error('Erreur lors de la récupération des publicités:', error);
      return [];
    }
  }

  /**
   * Enregistre une impression publicitaire
   */
  async recordImpression(campaignId: string, creativeId: string, placementId: string, userId?: string): Promise<void> {
    try {
      const impressionData = {
        campaign_id: campaignId,
        creative_id: creativeId,
        placement_id: placementId,
        user_id: userId,
        viewed_at: new Date().toISOString(),
        device_type: this.getDeviceType(),
        page_url: window.location.href,
        referrer: document.referrer
      };

      const { error } = await supabase
        .from('ad_impressions')
        .insert([impressionData]);

      if (error) throw error;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de l\'impression:', error);
    }
  }

  /**
   * Enregistre un clic publicitaire
   */
  async recordClick(impressionId: string, campaignId: string, cost: number): Promise<void> {
    try {
      const clickData = {
        impression_id: impressionId,
        campaign_id: campaignId,
        cost: cost,
        clicked_at: new Date().toISOString(),
        target_url: window.location.href
      };

      const { error } = await supabase
        .from('ad_clicks')
        .insert([clickData]);

      if (error) throw error;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du clic:', error);
    }
  }

  // =====================================================
  // MÉTRIQUES ET ANALYTICS
  // =====================================================

  /**
   * Récupère les métriques d'une campagne
   */
  async getCampaignMetrics(campaignId: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('ad_campaigns')
        .select('impressions, clicks, ctr, spent, total_budget')
        .eq('id', campaignId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Erreur lors de la récupération des métriques:', error);
      return null;
    }
  }

  // =====================================================
  // UTILITAIRES
  // =====================================================

  /**
   * Détecte le type d'appareil
   */
  private getDeviceType(): string {
    const userAgent = navigator.userAgent;
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      return 'tablet';
    }
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
      return 'mobile';
    }
    return 'desktop';
  }

  /**
   * Calcule le coût par clic basé sur l'enchère et la concurrence
   */
  calculateCPC(bidAmount: number, competitionMultiplier: number = 1.0): number {
    return Math.round(bidAmount * competitionMultiplier);
  }
}

export const adCampaignService = new AdCampaignService();
