-- CORRECTION AVEC LES BONS STATUTS
-- Basé sur votre système de statuts utilisateur

-- =====================================================
-- 1. IDENTIFIER LES STATUTS VALIDES
-- =====================================================

-- Voir la contrainte
SELECT 
    'Contrainte valid_status:' as info,
    check_clause as definition
FROM information_schema.check_constraints 
WHERE constraint_name = 'valid_status';

-- Voir les types enum pour status
SELECT 
    'Types enum status:' as info,
    t.typname as type_name,
    string_agg(e.enumlabel, ', ' ORDER BY e.enumsortorder) as valeurs_autorisees
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid 
WHERE t.typname LIKE '%status%'
GROUP BY t.typname;

-- =====================================================
-- 2. FONCTION CORRIGÉE AVEC LES BONS STATUTS
-- =====================================================

-- Fonction avec les statuts corrects de votre système
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  -- Insérer le profil utilisateur avec le bon statut
  INSERT INTO public.profiles (
    id, 
    username, 
    email, 
    role,
    status,  -- Utiliser les statuts de votre enum UserStatus
    created_at,
    updated_at
  )
  VALUES (
    new.id,
    new.raw_user_meta_data->>'username',
    new.email,
    COALESCE(new.raw_user_meta_data->>'role', 'standard'),
    'newbie',  -- Statut par défaut selon votre système
    now(),
    now()
  );

  -- Si l'utilisateur est une entreprise, créer le profil business
  IF (new.raw_user_meta_data->>'role' = 'business') THEN
    INSERT INTO public.business_profiles (
      id,
      business_name,
      business_description,
      business_status,  -- Statut business
      created_at,
      updated_at
    )
    VALUES (
      new.id,
      new.raw_user_meta_data->>'businessName',
      COALESCE(new.raw_user_meta_data->>'businessDescription', ''),
      'new',  -- Statut par défaut pour les entreprises
      now(),
      now()
    );
  END IF;

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 3. FONCTION ALTERNATIVE AVEC TOUS LES STATUTS POSSIBLES
-- =====================================================

-- Si 'newbie' ne marche pas, essayons tous les statuts possibles
CREATE OR REPLACE FUNCTION public.handle_new_user_flexible()
RETURNS trigger AS $$
DECLARE
    user_status text;
    business_status text;
BEGIN
  -- Déterminer le statut utilisateur selon les valeurs autorisées
  -- Basé sur votre enum UserStatus
  user_status := CASE 
    WHEN new.raw_user_meta_data->>'role' = 'business' THEN 'member'
    ELSE 'newbie'  -- ou 'member' si newbie n'existe pas
  END;
  
  -- Déterminer le statut business
  business_status := 'new';  -- ou 'pending' selon votre enum

  -- Insérer le profil utilisateur
  INSERT INTO public.profiles (
    id, 
    username, 
    email, 
    role,
    status,
    created_at,
    updated_at
  )
  VALUES (
    new.id,
    new.raw_user_meta_data->>'username',
    new.email,
    COALESCE(new.raw_user_meta_data->>'role', 'standard'),
    user_status,
    now(),
    now()
  );

  -- Si l'utilisateur est une entreprise, créer le profil business
  IF (new.raw_user_meta_data->>'role' = 'business') THEN
    INSERT INTO public.business_profiles (
      id,
      business_name,
      business_description,
      business_status,
      created_at,
      updated_at
    )
    VALUES (
      new.id,
      new.raw_user_meta_data->>'businessName',
      COALESCE(new.raw_user_meta_data->>'businessDescription', ''),
      business_status,
      now(),
      now()
    );
  END IF;

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 4. TESTS AVEC DIFFÉRENTS STATUTS
-- =====================================================

-- Test avec 'newbie'
DO $$
DECLARE
    test_id uuid := gen_random_uuid();
BEGIN
    BEGIN
        INSERT INTO profiles (id, username, email, role, status, created_at, updated_at)
        VALUES (test_id, 'test_newbie', '<EMAIL>', 'standard', 'newbie', now(), now());
        
        RAISE NOTICE '✅ Statut NEWBIE: AUTORISÉ';
        DELETE FROM profiles WHERE id = test_id;
        
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '❌ Statut NEWBIE: REFUSÉ - %', SQLERRM;
    END;
END $$;

-- Test avec 'member'
DO $$
DECLARE
    test_id uuid := gen_random_uuid();
BEGIN
    BEGIN
        INSERT INTO profiles (id, username, email, role, status, created_at, updated_at)
        VALUES (test_id, 'test_member', '<EMAIL>', 'standard', 'member', now(), now());
        
        RAISE NOTICE '✅ Statut MEMBER: AUTORISÉ';
        DELETE FROM profiles WHERE id = test_id;
        
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '❌ Statut MEMBER: REFUSÉ - %', SQLERRM;
    END;
END $$;

-- Test avec 'contributor'
DO $$
DECLARE
    test_id uuid := gen_random_uuid();
BEGIN
    BEGIN
        INSERT INTO profiles (id, username, email, role, status, created_at, updated_at)
        VALUES (test_id, 'test_contrib', '<EMAIL>', 'standard', 'contributor', now(), now());
        
        RAISE NOTICE '✅ Statut CONTRIBUTOR: AUTORISÉ';
        DELETE FROM profiles WHERE id = test_id;
        
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '❌ Statut CONTRIBUTOR: REFUSÉ - %', SQLERRM;
    END;
END $$;

-- Test avec 'discoverer'
DO $$
DECLARE
    test_id uuid := gen_random_uuid();
BEGIN
    BEGIN
        INSERT INTO profiles (id, username, email, role, status, created_at, updated_at)
        VALUES (test_id, 'test_disc', '<EMAIL>', 'standard', 'discoverer', now(), now());
        
        RAISE NOTICE '✅ Statut DISCOVERER: AUTORISÉ';
        DELETE FROM profiles WHERE id = test_id;
        
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '❌ Statut DISCOVERER: REFUSÉ - %', SQLERRM;
    END;
END $$;

-- Test avec 'influencer'
DO $$
DECLARE
    test_id uuid := gen_random_uuid();
BEGIN
    BEGIN
        INSERT INTO profiles (id, username, email, role, status, created_at, updated_at)
        VALUES (test_id, 'test_inf', '<EMAIL>', 'standard', 'influencer', now(), now());
        
        RAISE NOTICE '✅ Statut INFLUENCER: AUTORISÉ';
        DELETE FROM profiles WHERE id = test_id;
        
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '❌ Statut INFLUENCER: REFUSÉ - %', SQLERRM;
    END;
END $$;

-- Test avec 'leader'
DO $$
DECLARE
    test_id uuid := gen_random_uuid();
BEGIN
    BEGIN
        INSERT INTO profiles (id, username, email, role, status, created_at, updated_at)
        VALUES (test_id, 'test_leader', '<EMAIL>', 'standard', 'leader', now(), now());
        
        RAISE NOTICE '✅ Statut LEADER: AUTORISÉ';
        DELETE FROM profiles WHERE id = test_id;
        
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '❌ Statut LEADER: REFUSÉ - %', SQLERRM;
    END;
END $$;

-- =====================================================
-- 5. MISE À JOUR DU TRIGGER AVEC LE BON STATUT
-- =====================================================

-- Une fois qu'on sait quel statut marche, on met à jour le trigger
-- (Remplacez 'STATUT_QUI_MARCHE' par le statut qui a réussi le test)

DO $$
DECLARE
    working_status text;
BEGIN
    -- Déterminer quel statut utiliser (à adapter selon les résultats des tests)
    working_status := 'member';  -- Changez selon les tests ci-dessus
    
    -- Créer la fonction finale avec le bon statut
    EXECUTE format('
        CREATE OR REPLACE FUNCTION public.handle_new_user()
        RETURNS trigger AS $func$
        BEGIN
          INSERT INTO public.profiles (
            id, username, email, role, status, created_at, updated_at
          ) VALUES (
            new.id,
            new.raw_user_meta_data->>''username'',
            new.email,
            COALESCE(new.raw_user_meta_data->>''role'', ''standard''),
            ''%s'',
            now(),
            now()
          );

          IF (new.raw_user_meta_data->>''role'' = ''business'') THEN
            INSERT INTO public.business_profiles (
              id, business_name, business_description, created_at, updated_at
            ) VALUES (
              new.id,
              new.raw_user_meta_data->>''businessName'',
              COALESCE(new.raw_user_meta_data->>''businessDescription'', ''''),
              now(),
              now()
            );
          END IF;

          RETURN new;
        END;
        $func$ LANGUAGE plpgsql SECURITY DEFINER;
    ', working_status);
    
    RAISE NOTICE 'Fonction mise à jour avec le statut: %', working_status;
END $$;

-- Recréer le trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW 
  EXECUTE FUNCTION public.handle_new_user();

-- =====================================================
-- 6. MESSAGE FINAL
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎯 CORRECTION TERMINÉE';
    RAISE NOTICE '';
    RAISE NOTICE 'Regardez les résultats des tests ci-dessus pour voir';
    RAISE NOTICE 'quels statuts sont autorisés dans votre système.';
    RAISE NOTICE '';
    RAISE NOTICE 'Testez maintenant l''inscription sur votre application !';
    RAISE NOTICE '';
END $$;
