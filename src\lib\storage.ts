import { supabase } from './supabase';

// Constantes pour les noms des buckets
export const STORAGE_BUCKETS = {
  AVATARS: 'profile-images',
  POSTS: 'post-images',
  PRODUCTS: 'product-images'
};

// Fonction pour télécharger une image de profil
export const uploadProfileImage = async (userId: string, file: File): Promise<string> => {
  if (!file) return '';
  
  const fileExt = file.name.split('.').pop();
  // Ajouter 'avatars/' au début du chemin
  const fileName = `avatars/${userId}/${Date.now()}.${fileExt}`;
  
  const { error } = await supabase.storage
    .from(STORAGE_BUCKETS.AVATARS)
    .upload(fileName, file, {
      cacheControl: '3600',
      upsert: true
    });
    
  if (error) {
    console.error('Erreur lors du téléchargement de l\'image de profil:', error);
    throw error;
  }
  
  const { data } = supabase.storage
    .from(STORAGE_BUCKETS.AVATARS)
    .getPublicUrl(fileName);
    
  return data.publicUrl;
};

// Fonction pour télécharger des images de produit
export const uploadProductImage = async (businessId: string, file: File): Promise<string> => {
  if (!file) return '';
  
  const fileExt = file.name.split('.').pop();
  const fileName = `${businessId}/${Date.now()}.${fileExt}`;
  
  const { error } = await supabase.storage
    .from(STORAGE_BUCKETS.PRODUCTS)
    .upload(fileName, file, {
      cacheControl: '3600',
      upsert: true
    });
    
  if (error) {
    console.error('Erreur lors du téléchargement de l\'image de produit:', error);
    throw error;
  }
  
  const { data } = supabase.storage
    .from(STORAGE_BUCKETS.PRODUCTS)
    .getPublicUrl(fileName);
    
  return data.publicUrl;
};

// Fonction pour télécharger des images de post
export const uploadPostImages = async (userId: string, files: File[]): Promise<string[]> => {
  const uploadedImageUrls: string[] = [];
  
  for (const file of files) {
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}/${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
    
    const { error } = await supabase.storage
      .from(STORAGE_BUCKETS.POSTS)
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: true
      });
      
    if (error) {
      console.error('Erreur lors du téléchargement de l\'image de post:', error);
      continue;
    }
    
    const { data } = supabase.storage
      .from(STORAGE_BUCKETS.POSTS)
      .getPublicUrl(fileName);
      
    if (data.publicUrl) {
      uploadedImageUrls.push(data.publicUrl);
    }
  }
  
  return uploadedImageUrls;
};