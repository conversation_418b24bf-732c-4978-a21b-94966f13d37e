import { supabase } from '../lib/supabase';
import {
  getMockBusinessData,
  generateMockRecommendations,
  generateMockProductProposals,
  generateMockCustomerInsights
} from './mockBusinessData';

// Types pour les recommandations business
export interface BusinessRecommendation {
  id: string;
  type: 'product_improvement' | 'new_product' | 'marketing_strategy' | 'customer_retention' | 'pricing_strategy';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  description: string;
  expectedImpact: string;
  implementationDifficulty: 'easy' | 'medium' | 'hard';
  estimatedCost: 'low' | 'medium' | 'high';
  timeframe: string;
  kpis: string[];
  actionSteps: string[];
  dataSupport: {
    customerFeedback: number;
    marketDemand: number;
    competitiveAdvantage: number;
  };
  confidence: number; // 0-100
  category: string;
}

export interface NewProductProposal {
  id: string;
  proposedName: string;
  category: string;
  targetMarket: string[];
  keyFeatures: string[];
  uniqueSellingPoints: string[];
  estimatedDemand: number;
  suggestedPriceRange: { min: number; max: number };
  developmentComplexity: 'low' | 'medium' | 'high';
  marketGap: string;
  customerNeedsSatisfied: string[];
  competitiveAdvantages: string[];
  riskFactors: string[];
  successProbability: number; // 0-100
  investmentRequired: string;
  timeToMarket: string;
  targetRevenue: string;
}

export interface CustomerInsight {
  totalCustomers: number;
  averageSatisfaction: number;
  loyaltyDistribution: Record<string, number>;
  topComplaints: Array<{ issue: string; frequency: number }>;
  emergingTrends: string[];
  churnRisk: number;
  growthOpportunities: string[];
}

export class BusinessAIRecommendationService {
  /**
   * Génère des recommandations complètes pour une entreprise
   */
  static async generateBusinessRecommendations(businessId: string): Promise<{
    recommendations: BusinessRecommendation[];
    newProductProposals: NewProductProposal[];
    customerInsights: CustomerInsight;
  }> {
    try {
      // En mode développement, utiliser les données simulées
      const isDevelopment = process.env.NODE_ENV === 'development' || true; // Force le mode dev pour la démo

      if (isDevelopment) {
        console.log('🤖 Mode développement : Utilisation des données simulées pour l\'IA Conseiller');

        // Utiliser les données simulées
        const recommendations = generateMockRecommendations(businessId);
        const newProductProposals = generateMockProductProposals(businessId);
        const customerInsights = generateMockCustomerInsights(businessId);

        return {
          recommendations,
          newProductProposals,
          customerInsights
        };
      }

      // Mode production : utiliser les vraies données
      const [postsData, productsData, ratingsData] = await Promise.all([
        this.getBusinessPosts(businessId),
        this.getBusinessProducts(businessId),
        this.getBusinessRatings(businessId)
      ]);

      // Analyser les données
      const customerInsights = this.analyzeCustomerInsights(postsData, ratingsData);
      const productAnalysis = this.analyzeProductPerformance(productsData, postsData);
      const marketTrends = this.analyzeMarketTrends(postsData);

      // Générer les recommandations
      const recommendations = this.generateRecommendations(
        customerInsights,
        productAnalysis,
        marketTrends,
        businessId
      );

      // Générer les propositions de nouveaux produits
      const newProductProposals = this.generateNewProductProposals(
        marketTrends,
        customerInsights,
        productAnalysis
      );

      return {
        recommendations,
        newProductProposals,
        customerInsights
      };
    } catch (error) {
      console.error('Erreur dans generateBusinessRecommendations:', error);
      return {
        recommendations: [],
        newProductProposals: [],
        customerInsights: {
          totalCustomers: 0,
          averageSatisfaction: 0,
          loyaltyDistribution: {},
          topComplaints: [],
          emergingTrends: [],
          churnRisk: 0,
          growthOpportunities: []
        }
      };
    }
  }

  /**
   * Récupère les posts liés à l'entreprise
   */
  private static async getBusinessPosts(businessId: string) {
    const { data, error } = await supabase
      .from('posts_with_author_details')
      .select('*')
      .eq('business_id', businessId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  /**
   * Récupère les produits de l'entreprise
   */
  private static async getBusinessProducts(businessId: string) {
    const { data, error } = await supabase
      .from('marketplace_products')
      .select(`
        *,
        marketcomments(*),
        marketrating(*)
      `)
      .eq('business_id', businessId);

    if (error) throw error;
    return data || [];
  }

  /**
   * Récupère les notes de l'entreprise
   */
  private static async getBusinessRatings(businessId: string) {
    const { data, error } = await supabase
      .from('posts_with_author_details')
      .select('rating, created_at, user_id, category')
      .eq('business_id', businessId)
      .not('rating', 'is', null);

    if (error) throw error;
    return data || [];
  }

  /**
   * Analyse les insights clients
   */
  private static analyzeCustomerInsights(posts: any[], ratings: any[]): CustomerInsight {
    const uniqueCustomers = new Set(posts.map(p => p.user_id)).size;
    const avgSatisfaction = ratings.length > 0
      ? ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length
      : 0;

    // Analyser la distribution de fidélité
    const customerInteractions = new Map<string, number>();
    posts.forEach(post => {
      const userId = post.user_id;
      customerInteractions.set(userId, (customerInteractions.get(userId) || 0) + 1);
    });

    const loyaltyDistribution = {
      'Nouveaux (1 interaction)': 0,
      'Occasionnels (2-3 interactions)': 0,
      'Réguliers (4-7 interactions)': 0,
      'Fidèles (8+ interactions)': 0
    };

    customerInteractions.forEach(count => {
      if (count === 1) loyaltyDistribution['Nouveaux (1 interaction)']++;
      else if (count <= 3) loyaltyDistribution['Occasionnels (2-3 interactions)']++;
      else if (count <= 7) loyaltyDistribution['Réguliers (4-7 interactions)']++;
      else loyaltyDistribution['Fidèles (8+ interactions)']++;
    });

    // Identifier les principales plaintes
    const complaints = this.extractComplaints(posts);
    const emergingTrends = this.identifyEmergingTrends(posts);
    const churnRisk = this.calculateChurnRisk(ratings);
    const growthOpportunities = this.identifyGrowthOpportunities(posts, ratings);

    return {
      totalCustomers: uniqueCustomers,
      averageSatisfaction: avgSatisfaction,
      loyaltyDistribution,
      topComplaints: complaints,
      emergingTrends,
      churnRisk,
      growthOpportunities
    };
  }

  /**
   * Analyse la performance des produits
   */
  private static analyzeProductPerformance(products: any[], posts: any[]) {
    return products.map(product => {
      const productPosts = posts.filter(p => p.product_name === product.name);
      const ratings = product.marketrating || [];
      const comments = product.marketcomments || [];

      const avgRating = ratings.length > 0
        ? ratings.reduce((sum: number, r: any) => sum + r.rating, 0) / ratings.length
        : 0;

      const sentimentTrend = this.calculateSentimentTrend(productPosts);
      const issues = this.identifyProductIssues(comments, productPosts);

      return {
        id: product.id,
        name: product.name,
        category: product.category,
        averageRating: avgRating,
        totalReviews: comments.length + productPosts.length,
        sentimentTrend,
        issues,
        strengths: this.identifyProductStrengths(comments, productPosts)
      };
    });
  }

  /**
   * Analyse les tendances du marché
   */
  private static analyzeMarketTrends(posts: any[]) {
    const categoryTrends = new Map<string, any>();

    posts.forEach(post => {
      const category = post.category || 'Autre';
      if (!categoryTrends.has(category)) {
        categoryTrends.set(category, {
          posts: [],
          ratings: [],
          demands: []
        });
      }

      const trend = categoryTrends.get(category);
      trend.posts.push(post);
      if (post.rating) trend.ratings.push(post.rating);

      // Identifier les demandes dans les descriptions
      if (post.description && this.containsDemand(post.description)) {
        trend.demands.push(post.description);
      }
    });

    return Array.from(categoryTrends.entries()).map(([category, data]) => ({
      category,
      growth: this.calculateCategoryGrowth(data.posts),
      satisfaction: data.ratings.length > 0
        ? data.ratings.reduce((a: number, b: number) => a + b, 0) / data.ratings.length
        : 0,
      unmetNeeds: this.extractUnmetNeeds(data.demands),
      opportunities: this.identifyOpportunities(data.posts, data.demands)
    }));
  }

  /**
   * Génère les recommandations stratégiques
   */
  private static generateRecommendations(
    customerInsights: CustomerInsight,
    productAnalysis: any[],
    marketTrends: any[],
    businessId: string
  ): BusinessRecommendation[] {
    const recommendations: BusinessRecommendation[] = [];

    // 1. Recommandations d'amélioration de produits
    productAnalysis.forEach(product => {
      if (product.averageRating < 3.5 && product.issues.length > 0) {
        recommendations.push({
          id: `improve-${product.id}`,
          type: 'product_improvement',
          priority: product.averageRating < 2.5 ? 'urgent' : 'high',
          title: `Améliorer ${product.name}`,
          description: `Le produit ${product.name} présente des problèmes de satisfaction client avec une note moyenne de ${product.averageRating.toFixed(1)}/5.`,
          expectedImpact: `Augmentation potentielle de la satisfaction de 1-2 points et réduction des retours de 30-50%`,
          implementationDifficulty: product.issues.length > 3 ? 'hard' : 'medium',
          estimatedCost: product.issues.length > 3 ? 'high' : 'medium',
          timeframe: '2-4 mois',
          kpis: ['Note moyenne du produit', 'Taux de retour', 'NPS'],
          actionSteps: [
            `Analyser en détail les ${product.issues.length} problèmes identifiés`,
            'Développer un plan d\'amélioration prioritaire',
            'Tester les améliorations avec un groupe pilote',
            'Déployer les améliorations et mesurer l\'impact'
          ],
          dataSupport: {
            customerFeedback: Math.min(100, product.totalReviews * 10),
            marketDemand: 70,
            competitiveAdvantage: 60
          },
          confidence: 85,
          category: product.category
        });
      }
    });

    // 2. Recommandations de fidélisation client
    if (customerInsights.churnRisk > 30) {
      recommendations.push({
        id: 'customer-retention',
        type: 'customer_retention',
        priority: 'high',
        title: 'Programme de fidélisation client',
        description: `Risque de perte de clients élevé (${customerInsights.churnRisk}%). Mise en place d'un programme de fidélisation nécessaire.`,
        expectedImpact: 'Réduction du churn de 20-30% et augmentation de la valeur vie client de 15-25%',
        implementationDifficulty: 'medium',
        estimatedCost: 'medium',
        timeframe: '1-3 mois',
        kpis: ['Taux de rétention', 'Fréquence d\'achat', 'Valeur panier moyen'],
        actionSteps: [
          'Segmenter la base clients selon le niveau de fidélité',
          'Créer des offres personnalisées par segment',
          'Mettre en place un système de points/récompenses',
          'Lancer des campagnes de réactivation ciblées'
        ],
        dataSupport: {
          customerFeedback: 80,
          marketDemand: 75,
          competitiveAdvantage: 70
        },
        confidence: 78,
        category: 'Fidélisation'
      });
    }

    // 3. Recommandations de stratégie marketing
    marketTrends.forEach(trend => {
      if (trend.growth > 20 && trend.opportunities.length > 0) {
        recommendations.push({
          id: `marketing-${trend.category}`,
          type: 'marketing_strategy',
          priority: 'medium',
          title: `Stratégie marketing pour ${trend.category}`,
          description: `La catégorie ${trend.category} montre une croissance de ${trend.growth}% avec des opportunités identifiées.`,
          expectedImpact: 'Augmentation des ventes de 15-30% dans cette catégorie',
          implementationDifficulty: 'easy',
          estimatedCost: 'low',
          timeframe: '1-2 mois',
          kpis: ['Trafic catégorie', 'Taux de conversion', 'CA catégorie'],
          actionSteps: [
            'Analyser la concurrence dans cette catégorie',
            'Développer des contenus ciblés',
            'Lancer des campagnes publicitaires spécifiques',
            'Optimiser le référencement pour cette catégorie'
          ],
          dataSupport: {
            customerFeedback: 65,
            marketDemand: 85,
            competitiveAdvantage: 55
          },
          confidence: 72,
          category: trend.category
        });
      }
    });

    // 4. Recommandations de prix
    if (customerInsights.averageSatisfaction > 4.0) {
      recommendations.push({
        id: 'pricing-optimization',
        type: 'pricing_strategy',
        priority: 'medium',
        title: 'Optimisation de la stratégie tarifaire',
        description: `Satisfaction client élevée (${customerInsights.averageSatisfaction.toFixed(1)}/5) permettant une optimisation des prix.`,
        expectedImpact: 'Augmentation de la marge de 5-15% sans impact significatif sur les ventes',
        implementationDifficulty: 'easy',
        estimatedCost: 'low',
        timeframe: '2-4 semaines',
        kpis: ['Marge brute', 'Volume des ventes', 'Prix moyen'],
        actionSteps: [
          'Analyser l\'élasticité prix de chaque produit',
          'Tester des augmentations graduelles sur produits phares',
          'Surveiller l\'impact sur les ventes',
          'Ajuster la stratégie selon les résultats'
        ],
        dataSupport: {
          customerFeedback: 90,
          marketDemand: 70,
          competitiveAdvantage: 75
        },
        confidence: 68,
        category: 'Pricing'
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Génère des propositions de nouveaux produits
   */
  private static generateNewProductProposals(
    marketTrends: any[],
    customerInsights: CustomerInsight,
    productAnalysis: any[]
  ): NewProductProposal[] {
    const proposals: NewProductProposal[] = [];

    marketTrends.forEach(trend => {
      if (trend.unmetNeeds.length > 0 && trend.growth > 15) {
        const proposal: NewProductProposal = {
          id: `new-product-${trend.category}-${Date.now()}`,
          proposedName: `${trend.category} Premium`,
          category: trend.category,
          targetMarket: this.identifyTargetMarket(trend, customerInsights),
          keyFeatures: this.generateKeyFeatures(trend.unmetNeeds),
          uniqueSellingPoints: this.generateUSPs(trend, productAnalysis),
          estimatedDemand: Math.min(100, trend.growth * 2),
          suggestedPriceRange: this.calculatePriceRange(trend, productAnalysis),
          developmentComplexity: this.assessDevelopmentComplexity(trend.unmetNeeds),
          marketGap: trend.unmetNeeds[0] || 'Besoin non satisfait identifié',
          customerNeedsSatisfied: trend.unmetNeeds.slice(0, 3),
          competitiveAdvantages: this.identifyCompetitiveAdvantages(trend),
          riskFactors: this.assessRiskFactors(trend),
          successProbability: this.calculateSuccessProbability(trend, customerInsights),
          investmentRequired: this.estimateInvestment(trend.unmetNeeds.length),
          timeToMarket: this.estimateTimeToMarket(trend.unmetNeeds.length),
          targetRevenue: this.estimateRevenue(trend.growth, customerInsights.totalCustomers)
        };

        proposals.push(proposal);
      }
    });

    return proposals.sort((a, b) => b.successProbability - a.successProbability);
  }

  // Méthodes utilitaires
  private static extractComplaints(posts: any[]): Array<{ issue: string; frequency: number }> {
    const complaints = new Map<string, number>();
    const complaintKeywords = ['problème', 'défaut', 'mauvais', 'décevant', 'insatisfait'];

    posts.filter(p => p.rating && p.rating <= 2).forEach(post => {
      if (post.description) {
        const text = post.description.toLowerCase();
        complaintKeywords.forEach(keyword => {
          if (text.includes(keyword)) {
            complaints.set(keyword, (complaints.get(keyword) || 0) + 1);
          }
        });
      }
    });

    return Array.from(complaints.entries())
      .map(([issue, frequency]) => ({ issue, frequency }))
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 5);
  }

  private static identifyEmergingTrends(posts: any[]): string[] {
    const trends: string[] = [];
    const trendKeywords = ['nouveau', 'innovation', 'tendance', 'moderne', 'futur'];

    posts.forEach(post => {
      if (post.description) {
        const text = post.description.toLowerCase();
        trendKeywords.forEach(keyword => {
          if (text.includes(keyword)) {
            trends.push(post.description.substring(0, 100) + '...');
          }
        });
      }
    });

    return [...new Set(trends)].slice(0, 3);
  }

  private static calculateChurnRisk(ratings: any[]): number {
    if (ratings.length === 0) return 0;

    const recentRatings = ratings.filter(r => {
      const ratingDate = new Date(r.created_at);
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      return ratingDate > thirtyDaysAgo;
    });

    const lowRatings = recentRatings.filter(r => r.rating <= 2).length;
    return Math.min(100, (lowRatings / Math.max(1, recentRatings.length)) * 100);
  }

  private static identifyGrowthOpportunities(posts: any[], ratings: any[]): string[] {
    const opportunities: string[] = [];

    // Analyser les catégories avec forte satisfaction mais faible volume
    const categoryData = new Map<string, { count: number; avgRating: number }>();

    posts.forEach(post => {
      const category = post.category || 'Autre';
      if (!categoryData.has(category)) {
        categoryData.set(category, { count: 0, avgRating: 0 });
      }
      const data = categoryData.get(category)!;
      data.count++;
      if (post.rating) {
        data.avgRating = (data.avgRating + post.rating) / 2;
      }
    });

    categoryData.forEach((data, category) => {
      if (data.avgRating > 4.0 && data.count < 10) {
        opportunities.push(`Développer l'offre en ${category} (forte satisfaction, faible volume)`);
      }
    });

    return opportunities.slice(0, 3);
  }

  private static calculateSentimentTrend(posts: any[]): 'improving' | 'stable' | 'declining' {
    if (posts.length < 2) return 'stable';

    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const recentPosts = posts.filter(p => new Date(p.created_at) > thirtyDaysAgo);
    const olderPosts = posts.filter(p => new Date(p.created_at) <= thirtyDaysAgo);

    if (recentPosts.length === 0 || olderPosts.length === 0) return 'stable';

    const recentAvg = recentPosts.reduce((sum, p) => sum + (p.rating || 3), 0) / recentPosts.length;
    const olderAvg = olderPosts.reduce((sum, p) => sum + (p.rating || 3), 0) / olderPosts.length;

    const diff = recentAvg - olderAvg;
    if (diff > 0.3) return 'improving';
    if (diff < -0.3) return 'declining';
    return 'stable';
  }

  private static identifyProductIssues(comments: any[], posts: any[]): string[] {
    const issues: string[] = [];
    const issueKeywords = ['problème', 'défaut', 'cassé', 'mauvais', 'décevant'];

    [...comments, ...posts].forEach(item => {
      if (item.rating && item.rating <= 2 && (item.comment || item.description)) {
        const text = (item.comment || item.description).toLowerCase();
        issueKeywords.forEach(keyword => {
          if (text.includes(keyword)) {
            issues.push(keyword);
          }
        });
      }
    });

    return [...new Set(issues)];
  }

  private static identifyProductStrengths(comments: any[], posts: any[]): string[] {
    const strengths: string[] = [];
    const strengthKeywords = ['excellent', 'parfait', 'recommande', 'qualité', 'satisfait'];

    [...comments, ...posts].forEach(item => {
      if (item.rating && item.rating >= 4 && (item.comment || item.description)) {
        const text = (item.comment || item.description).toLowerCase();
        strengthKeywords.forEach(keyword => {
          if (text.includes(keyword)) {
            strengths.push(keyword);
          }
        });
      }
    });

    return [...new Set(strengths)];
  }

  private static containsDemand(text: string): boolean {
    const demandKeywords = ['besoin', 'manque', 'voudrait', 'souhaite', 'demande'];
    const lowerText = text.toLowerCase();
    return demandKeywords.some(keyword => lowerText.includes(keyword));
  }

  private static calculateCategoryGrowth(posts: any[]): number {
    if (posts.length < 2) return 0;

    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

    const recentPosts = posts.filter(p => new Date(p.created_at) > thirtyDaysAgo);
    const olderPosts = posts.filter(p =>
      new Date(p.created_at) > sixtyDaysAgo && new Date(p.created_at) <= thirtyDaysAgo
    );

    if (olderPosts.length === 0) return 0;
    return ((recentPosts.length - olderPosts.length) / olderPosts.length) * 100;
  }

  private static extractUnmetNeeds(demands: string[]): string[] {
    return demands.slice(0, 3).map(demand =>
      demand.length > 100 ? demand.substring(0, 100) + '...' : demand
    );
  }

  private static identifyOpportunities(posts: any[], demands: string[]): string[] {
    const opportunities: string[] = [];

    if (demands.length > 0) {
      opportunities.push('Besoins clients non satisfaits identifiés');
    }

    if (posts.length > 0) {
      const avgRating = posts.reduce((sum, p) => sum + (p.rating || 3), 0) / posts.length;
      if (avgRating > 4.0) {
        opportunities.push('Forte satisfaction client dans cette catégorie');
      }
    }

    return opportunities;
  }

  private static identifyTargetMarket(trend: any, customerInsights: CustomerInsight): string[] {
    return ['Clients existants satisfaits', 'Nouveaux segments identifiés', 'Marché en croissance'];
  }

  private static generateKeyFeatures(unmetNeeds: string[]): string[] {
    return unmetNeeds.map(need => `Fonctionnalité répondant à: ${need.substring(0, 50)}...`);
  }

  private static generateUSPs(trend: any, productAnalysis: any[]): string[] {
    return [
      'Premier sur le marché dans cette niche',
      'Répond à un besoin non satisfait',
      'Qualité supérieure basée sur les retours clients'
    ];
  }

  private static calculatePriceRange(trend: any, productAnalysis: any[]): { min: number; max: number } {
    const avgPrice = productAnalysis.length > 0 ? 50000 : 30000;
    return {
      min: avgPrice * 0.8,
      max: avgPrice * 1.5
    };
  }

  private static assessDevelopmentComplexity(unmetNeeds: string[]): 'low' | 'medium' | 'high' {
    if (unmetNeeds.length <= 1) return 'low';
    if (unmetNeeds.length <= 3) return 'medium';
    return 'high';
  }

  private static identifyCompetitiveAdvantages(trend: any): string[] {
    return [
      'Première entreprise à répondre à ce besoin',
      'Connaissance approfondie des clients',
      'Capacité d\'innovation prouvée'
    ];
  }

  private static assessRiskFactors(trend: any): string[] {
    return [
      'Incertitude sur la taille du marché',
      'Possible concurrence future',
      'Coûts de développement'
    ];
  }

  private static calculateSuccessProbability(trend: any, customerInsights: CustomerInsight): number {
    let probability = 50; // Base

    if (trend.growth > 20) probability += 20;
    if (customerInsights.averageSatisfaction > 4.0) probability += 15;
    if (trend.unmetNeeds.length > 2) probability += 10;

    return Math.min(95, probability);
  }

  private static estimateInvestment(complexity: number): string {
    if (complexity <= 1) return '10 000 - 50 000 F CFA';
    if (complexity <= 3) return '50 000 - 200 000 F CFA';
    return '200 000 - 500 000 F CFA';
  }

  private static estimateTimeToMarket(complexity: number): string {
    if (complexity <= 1) return '1-2 mois';
    if (complexity <= 3) return '3-6 mois';
    return '6-12 mois';
  }

  private static estimateRevenue(growth: number, customers: number): string {
    const baseRevenue = customers * 25000; // Estimation base par client
    const projectedRevenue = baseRevenue * (1 + growth / 100);
    return `${Math.round(projectedRevenue).toLocaleString()} F CFA/an`;
  }
}
