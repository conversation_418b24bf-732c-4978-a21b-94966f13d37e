# 💰 Système de Facturation Publicitaire

## Vue d'ensemble

Le système de facturation publicitaire est une solution complète qui permet aux entreprises de gérer leurs budgets publicitaires, effectuer des recharges, suivre leurs dépenses et recevoir des alertes de facturation.

## 🏗️ Architecture

### Composants principaux

1. **AdBillingContext** - Contexte React pour la gestion d'état
2. **AdWalletManager** - Interface de gestion du portefeuille
3. **AdBillingDemo** - Composant de démonstration
4. **Base de données** - Tables Supabase pour la persistance

### Tables de base de données

```sql
-- Portefeuilles publicitaires
ad_wallets (
  id, business_id, balance, total_spent, 
  total_recharged, currency, status
)

-- Transactions publicitaires
ad_transactions (
  id, wallet_id, campaign_id, type, amount, 
  description, payment_method, status, metadata
)

-- Méthodes de paiement
payment_methods (
  id, business_id, type, provider, 
  account_number, account_name, is_default, is_active
)

-- Alertes de facturation
billing_alerts (
  id, business_id, type, message, 
  threshold, is_read
)

-- Factures publicitaires
ad_invoices (
  id, business_id, campaign_id, invoice_number,
  total_amount, currency, status, due_date
)
```

## 🚀 Fonctionnalités

### Gestion du portefeuille
- ✅ Création automatique du portefeuille
- ✅ Affichage du solde en temps réel
- ✅ Historique des transactions
- ✅ Statistiques de dépenses

### Recharge du compte
- ✅ Méthodes de paiement multiples (Mobile Money, Cartes bancaires, etc.)
- ✅ Montants suggérés
- ✅ Traitement sécurisé des paiements
- ✅ Confirmation instantanée

### Facturation des campagnes
- ✅ Vérification automatique du solde
- ✅ Facturation en temps réel
- ✅ Gestion des échecs de paiement
- ✅ Remboursements automatiques

### Alertes et notifications
- ✅ Alertes de solde faible
- ✅ Notifications d'échec de paiement
- ✅ Alertes de dépassement de budget
- ✅ Campagnes mises en pause automatiquement

## 💳 Méthodes de paiement supportées

### Mobile Money (Côte d'Ivoire)
- **Orange Money** - Principal opérateur
- **MTN Money** - Deuxième opérateur
- **Moov Money** - Troisième opérateur

### Cartes bancaires
- **Visa** - Cartes de crédit/débit
- **Mastercard** - Cartes de crédit/débit
- **American Express** - Cartes premium

### Virements bancaires
- **Ecobank** - Banque panafricaine
- **SGBCI** - Société Générale
- **BICICI** - BNP Paribas
- **UBA** - United Bank for Africa

### Cryptomonnaies (Futur)
- **Bitcoin** - Monnaie numérique
- **Ethereum** - Plateforme blockchain
- **USDT** - Stablecoin

## 🔧 Utilisation

### Intégration dans une campagne

```typescript
import { useAdBilling } from '../context/AdBillingContext';

const CampaignForm = () => {
  const { 
    wallet, 
    canAffordCampaign, 
    chargeCampaign 
  } = useAdBilling();

  const handleSubmit = async (campaignData) => {
    // Vérifier le solde
    if (!canAffordCampaign(campaignData.budget)) {
      alert('Solde insuffisant');
      return;
    }

    // Facturer la campagne
    const success = await chargeCampaign(
      campaignData.id,
      campaignData.budget,
      `Campagne: ${campaignData.title}`
    );

    if (success) {
      // Campagne créée avec succès
    }
  };
};
```

### Recharge du portefeuille

```typescript
const { rechargeWallet, paymentMethods } = useAdBilling();

const handleRecharge = async () => {
  const amount = 50000; // 50 000 F CFA
  const paymentMethodId = paymentMethods[0].id;
  
  try {
    await rechargeWallet(amount, paymentMethodId);
    alert('Recharge effectuée avec succès !');
  } catch (error) {
    alert('Erreur lors de la recharge');
  }
};
```

## 🎯 Seuils et limites

### Montants minimums
- **Recharge minimum** : 1 000 F CFA
- **Budget campagne minimum** : 5 000 F CFA
- **Budget quotidien minimum** : 500 F CFA

### Alertes automatiques
- **Solde faible** : 10 000 F CFA
- **Solde critique** : 5 000 F CFA
- **Pause automatique** : 1 000 F CFA

### Limites de transaction
- **Recharge maximum** : 1 000 000 F CFA/jour
- **Dépense maximum** : Solde disponible
- **Transactions simultanées** : 5 maximum

## 🔒 Sécurité

### Protection des données
- ✅ Chiffrement des informations de paiement
- ✅ Authentification à deux facteurs
- ✅ Audit trail complet
- ✅ Conformité PCI DSS

### Contrôles d'accès
- ✅ RLS (Row Level Security) activé
- ✅ Politiques par utilisateur
- ✅ Validation côté serveur
- ✅ Logs de sécurité

## 📊 Rapports et analytics

### Tableaux de bord
- **Vue d'ensemble** - Solde, dépenses, recharges
- **Transactions** - Historique détaillé
- **Performance** - ROI par campagne
- **Prévisions** - Estimation des dépenses

### Exports
- **PDF** - Factures et rapports
- **Excel** - Données détaillées
- **JSON** - Intégration API
- **CSV** - Analyse externe

## 🚨 Gestion des erreurs

### Types d'erreurs
1. **Solde insuffisant** - Recharge requise
2. **Échec de paiement** - Méthode invalide
3. **Limite dépassée** - Montant trop élevé
4. **Compte suspendu** - Contact support

### Actions automatiques
- **Pause des campagnes** - Si solde insuffisant
- **Notifications** - Alertes par email/SMS
- **Remboursements** - En cas d'erreur
- **Logs détaillés** - Pour le debugging

## 🔄 Workflow typique

1. **Création du compte** → Portefeuille automatique
2. **Ajout méthode de paiement** → Configuration
3. **Première recharge** → Activation du compte
4. **Création de campagne** → Vérification du solde
5. **Facturation automatique** → Déduction en temps réel
6. **Suivi des dépenses** → Tableaux de bord
7. **Alertes de solde** → Notifications proactives
8. **Recharge supplémentaire** → Continuité du service

## 🛠️ Configuration

### Variables d'environnement
```env
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_key
PAYMENT_PROVIDER_API_KEY=your_payment_key
BILLING_WEBHOOK_SECRET=your_webhook_secret
```

### Paramètres par défaut
```typescript
const BILLING_CONFIG = {
  currency: 'XOF', // Franc CFA
  minRecharge: 1000,
  minCampaignBudget: 5000,
  lowBalanceThreshold: 10000,
  criticalBalanceThreshold: 5000
};
```

## 📱 Interface utilisateur

### Composants disponibles
- **AdWalletManager** - Gestion complète du portefeuille
- **AdBillingDemo** - Démonstration et tests
- **BillingAlerts** - Affichage des alertes
- **PaymentMethods** - Gestion des méthodes de paiement

### Responsive design
- ✅ Mobile-first approach
- ✅ Tablettes optimisées
- ✅ Desktop full-featured
- ✅ PWA compatible

## 🔮 Roadmap

### Version 2.0
- [ ] Facturation récurrente
- [ ] Budgets partagés
- [ ] API publique
- [ ] Intégrations tierces

### Version 3.0
- [ ] IA pour optimisation budgets
- [ ] Prédictions de dépenses
- [ ] Recommandations automatiques
- [ ] Marketplace de services

---

**Développé avec ❤️ pour les entreprises ivoiriennes**
