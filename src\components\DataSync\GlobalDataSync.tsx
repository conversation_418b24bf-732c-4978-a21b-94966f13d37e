import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useRealBusinessData } from '../../hooks/useRealData';
import { Database, Wifi, WifiOff, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import './GlobalDataSync.css';

interface GlobalDataSyncContextType {
  isRealDataMode: boolean;
  setRealDataMode: (enabled: boolean) => void;
  syncStatus: 'idle' | 'syncing' | 'success' | 'error';
  lastSyncTime: Date | null;
  hasRealData: boolean;
  forceSync: () => void;
  dataStats: {
    sales: number;
    products: number;
    rankings: number;
  };
}

const GlobalDataSyncContext = createContext<GlobalDataSyncContextType | undefined>(undefined);

export const useGlobalDataSync = () => {
  const context = useContext(GlobalDataSyncContext);
  if (!context) {
    throw new Error('useGlobalDataSync must be used within a GlobalDataSyncProvider');
  }
  return context;
};

interface GlobalDataSyncProviderProps {
  children: React.ReactNode;
}

export const GlobalDataSyncProvider: React.FC<GlobalDataSyncProviderProps> = ({ children }) => {
  const { currentUser } = useAuth();
  const businessData = useRealBusinessData();
  
  const [isRealDataMode, setIsRealDataMode] = useState(false);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle');
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  const [showSyncIndicator, setShowSyncIndicator] = useState(false);

  // Calculer les statistiques des données
  const dataStats = {
    sales: businessData.sales?.length || 0,
    products: businessData.products?.length || 0,
    rankings: businessData.rankings?.length || 0
  };

  const hasRealData = dataStats.sales > 0 || dataStats.products > 0 || dataStats.rankings > 0;

  // Fonction pour basculer le mode de données
  const setRealDataMode = useCallback((enabled: boolean) => {
    if (enabled && !hasRealData) {
      console.warn('Tentative d\'activation du mode réel sans données disponibles');
      return;
    }
    
    setIsRealDataMode(enabled);
    setSyncStatus('syncing');
    setShowSyncIndicator(true);
    
    // Simuler une synchronisation
    setTimeout(() => {
      setSyncStatus('success');
      setLastSyncTime(new Date());
      
      // Masquer l'indicateur après 2 secondes
      setTimeout(() => {
        setShowSyncIndicator(false);
        setSyncStatus('idle');
      }, 2000);
    }, 1000);
  }, [hasRealData]);

  // Fonction pour forcer une synchronisation
  const forceSync = useCallback(() => {
    setSyncStatus('syncing');
    setShowSyncIndicator(true);
    
    // Rafraîchir toutes les données
    businessData.refreshAllData();
    
    setTimeout(() => {
      setSyncStatus('success');
      setLastSyncTime(new Date());
      
      setTimeout(() => {
        setShowSyncIndicator(false);
        setSyncStatus('idle');
      }, 2000);
    }, 1500);
  }, [businessData]);

  // Synchronisation automatique toutes les 5 minutes en mode réel
  useEffect(() => {
    if (!isRealDataMode) return;

    const interval = setInterval(() => {
      forceSync();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [isRealDataMode, forceSync]);

  // Détecter les changements de connectivité
  useEffect(() => {
    const handleOnline = () => {
      if (isRealDataMode) {
        forceSync();
      }
    };

    const handleOffline = () => {
      setSyncStatus('error');
      setShowSyncIndicator(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isRealDataMode, forceSync]);

  const contextValue: GlobalDataSyncContextType = {
    isRealDataMode,
    setRealDataMode,
    syncStatus,
    lastSyncTime,
    hasRealData,
    forceSync,
    dataStats
  };

  return (
    <GlobalDataSyncContext.Provider value={contextValue}>
      {children}
      
      {/* Indicateur de synchronisation global */}
      {showSyncIndicator && (
        <div className="global-sync-indicator">
          <div className="sync-indicator-content">
            <div className="sync-icon">
              {syncStatus === 'syncing' && <RefreshCw size={16} className="spinning" />}
              {syncStatus === 'success' && <CheckCircle size={16} className="success" />}
              {syncStatus === 'error' && <AlertCircle size={16} className="error" />}
            </div>
            <div className="sync-text">
              {syncStatus === 'syncing' && 'Synchronisation en cours...'}
              {syncStatus === 'success' && 'Données synchronisées'}
              {syncStatus === 'error' && 'Erreur de synchronisation'}
            </div>
          </div>
        </div>
      )}
    </GlobalDataSyncContext.Provider>
  );
};

// Composant d'état de synchronisation pour la barre de statut
export const SyncStatusBar: React.FC = () => {
  const { isRealDataMode, syncStatus, lastSyncTime, hasRealData, forceSync, dataStats } = useGlobalDataSync();
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const formatLastSync = (date: Date | null) => {
    if (!date) return 'Jamais';
    
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'À l\'instant';
    if (minutes < 60) return `Il y a ${minutes}m`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `Il y a ${hours}h`;
    
    return date.toLocaleDateString();
  };

  return (
    <div className="sync-status-bar">
      <div className="sync-status-content">
        <div className="sync-mode">
          <div className="mode-indicator">
            {isRealDataMode ? (
              <Database size={14} className="real-mode" />
            ) : (
              <Database size={14} className="demo-mode" />
            )}
            <span className="mode-text">
              {isRealDataMode ? 'Mode Réel' : 'Mode Démo'}
            </span>
          </div>
          
          <div className="connection-status">
            {isOnline ? (
              <Wifi size={12} className="online" />
            ) : (
              <WifiOff size={12} className="offline" />
            )}
            <span className="connection-text">
              {isOnline ? 'En ligne' : 'Hors ligne'}
            </span>
          </div>
        </div>

        {isRealDataMode && (
          <div className="sync-info">
            <div className="data-stats">
              <span className="stat-item">
                📊 {dataStats.sales} ventes
              </span>
              <span className="stat-item">
                📦 {dataStats.products} produits
              </span>
              <span className="stat-item">
                🏆 {dataStats.rankings} classements
              </span>
            </div>
            
            <div className="sync-controls">
              <span className="last-sync">
                Sync: {formatLastSync(lastSyncTime)}
              </span>
              <button 
                className="sync-button"
                onClick={forceSync}
                disabled={syncStatus === 'syncing'}
                title="Forcer la synchronisation"
              >
                <RefreshCw 
                  size={12} 
                  className={syncStatus === 'syncing' ? 'spinning' : ''} 
                />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default {
  GlobalDataSyncProvider,
  useGlobalDataSync,
  SyncStatusBar
};
