import { createClient } from '@supabase/supabase-js'; 
import { Database } from './database.types'; 

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL; 
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY; 

if (!supabaseUrl || !supabaseAnonKey) { 
  throw new Error('Missing Supabase environment variables'); 
} 

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

// Commentez ou supprimez ce bloc qui cause l'erreur
// supabase.from('products').on('postgres_changes', { event: '*', schema: 'public' }, (payload) => {
//   console.log('Changement dans la table products:', payload);
// }).subscribe();

// Si vous souhaitez activer les abonnements en temps réel, utilisez plutôt cette approche :
// Décommentez ce code une fois que vous avez configuré les abonnements en temps réel dans Supabase
/*
const channel = supabase.channel('table-db-changes');
channel
  .on('postgres_changes', { event: '*', schema: 'public', table: 'products' }, (payload) => {
    console.log('Changement dans la table products:', payload);
  })
  .subscribe();
*/