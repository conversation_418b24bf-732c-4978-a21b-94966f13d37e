import { supabase } from '../lib/supabase';

export interface Notification {
  id: string;
  user_id: string;
  type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  read: boolean;
  created_at: string;
}

export class NotificationService {
  /**
   * Récupère les notifications d'un utilisateur
   */
  static async getUserNotifications(userId: string, limit: number = 20): Promise<Notification[]> {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des notifications:', error);
      return [];
    }
  }

  /**
   * Marque une notification comme lue
   */
  static async markAsRead(notificationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);

      return !error;
    } catch (error) {
      console.error('Erreur lors du marquage de la notification:', error);
      return false;
    }
  }

  /**
   * Marque toutes les notifications comme lues
   */
  static async markAllAsRead(userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', userId)
        .eq('read', false);

      return !error;
    } catch (error) {
      console.error('Erreur lors du marquage de toutes les notifications:', error);
      return false;
    }
  }

  /**
   * Compte les notifications non lues
   */
  static async getUnreadCount(userId: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('read', false);

      if (error) throw error;
      return count || 0;
    } catch (error) {
      console.error('Erreur lors du comptage des notifications:', error);
      return 0;
    }
  }

  /**
   * Supprime une notification
   */
  static async deleteNotification(notificationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      return !error;
    } catch (error) {
      console.error('Erreur lors de la suppression de la notification:', error);
      return false;
    }
  }

  /**
   * Crée une notification
   */
  static async createNotification(
    userId: string,
    type: string,
    title: string,
    message: string,
    data?: Record<string, any>
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('notifications')
        .insert([{
          user_id: userId,
          type,
          title,
          message,
          data
        }]);

      return !error;
    } catch (error) {
      console.error('Erreur lors de la création de la notification:', error);
      return false;
    }
  }

  /**
   * S'abonne aux notifications en temps réel
   */
  static subscribeToNotifications(
    userId: string,
    onNotification: (notification: Notification) => void
  ) {
    const subscription = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          onNotification(payload.new as Notification);
        }
      )
      .subscribe();

    return subscription;
  }

  /**
   * Se désabonne des notifications en temps réel
   */
  static unsubscribeFromNotifications(subscription: any) {
    if (subscription) {
      supabase.removeChannel(subscription);
    }
  }

  /**
   * Envoie une notification push (simulation)
   */
  static async sendPushNotification(
    userId: string,
    title: string,
    message: string,
    data?: Record<string, any>
  ): Promise<void> {
    // Dans un environnement de production, ceci intégrerait avec un service de push notifications
    // comme Firebase Cloud Messaging, OneSignal, etc.
    
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        body: message,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        data
      });
    }
  }

  /**
   * Demande la permission pour les notifications push
   */
  static async requestNotificationPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.log('Ce navigateur ne supporte pas les notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }

    return false;
  }

  /**
   * Formate une notification pour l'affichage
   */
  static formatNotification(notification: Notification): {
    icon: string;
    color: string;
    timeAgo: string;
  } {
    const now = new Date();
    const notificationDate = new Date(notification.created_at);
    const diffInMinutes = Math.floor((now.getTime() - notificationDate.getTime()) / (1000 * 60));

    let timeAgo: string;
    if (diffInMinutes < 1) {
      timeAgo = 'À l\'instant';
    } else if (diffInMinutes < 60) {
      timeAgo = `Il y a ${diffInMinutes} min`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      timeAgo = `Il y a ${hours}h`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      timeAgo = `Il y a ${days}j`;
    }

    let icon = '📢';
    let color = 'blue';

    switch (notification.type) {
      case 'order_tracking':
        icon = '📦';
        color = 'green';
        break;
      case 'payment':
        icon = '💳';
        color = 'blue';
        break;
      case 'delivery':
        icon = '🚚';
        color = 'orange';
        break;
      case 'message':
        icon = '💬';
        color = 'purple';
        break;
      case 'promotion':
        icon = '🎉';
        color = 'pink';
        break;
      default:
        icon = '📢';
        color = 'gray';
    }

    return { icon, color, timeAgo };
  }
}
