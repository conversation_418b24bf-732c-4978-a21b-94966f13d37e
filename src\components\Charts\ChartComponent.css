/* Chart Component - Styles Ultra-Avancés */
.chart-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.chart-container:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.chart-wrapper {
  position: relative;
  width: 100%;
  animation: chartFadeIn 0.8s ease-out;
}

@keyframes chartFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Effet de brillance au hover */
.chart-container::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  border-radius: 14px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.chart-container:hover::before {
  opacity: 1;
}

/* Styles pour différents types de graphiques */
.chart-container.sales-chart {
  border-left: 4px solid #10b981;
}

.chart-container.products-chart {
  border-left: 4px solid #3b82f6;
}

.chart-container.rankings-chart {
  border-left: 4px solid #f59e0b;
}

.chart-container.analytics-chart {
  border-left: 4px solid #8b5cf6;
}

/* Loading state */
.chart-container.loading {
  position: relative;
  min-height: 300px;
}

.chart-container.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  margin: -20px 0 0 -20px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: chartSpin 1s linear infinite;
}

@keyframes chartSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .chart-container {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .chart-wrapper {
    height: 250px !important;
  }
}

@media (max-width: 480px) {
  .chart-container {
    padding: 12px;
  }
  
  .chart-wrapper {
    height: 200px !important;
  }
}

/* Styles pour les légendes personnalisées */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #374151;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-text {
  font-weight: 500;
}

.legend-value {
  color: #6b7280;
  margin-left: 4px;
}

/* Styles pour les tooltips personnalisés */
.chart-tooltip {
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12px;
  border-radius: 8px;
  font-size: 0.875rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tooltip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.tooltip-color {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.tooltip-label {
  flex: 1;
}

.tooltip-value {
  font-weight: 600;
}

/* Animations pour les graphiques */
.chart-container.animate-in {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Styles pour les métriques au-dessus des graphiques */
.chart-metrics {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.metric-item {
  flex: 1;
  text-align: center;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.metric-change {
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 2px;
}

.metric-change.positive {
  color: #10b981;
}

.metric-change.negative {
  color: #ef4444;
}

.metric-change.neutral {
  color: #6b7280;
}

/* Styles pour les contrôles de graphique */
.chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.chart-period-selector {
  display: flex;
  gap: 8px;
}

.period-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.period-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.period-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-action-btn {
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

/* Styles pour les graphiques en grille */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .chart-metrics {
    flex-direction: column;
    gap: 12px;
  }
  
  .chart-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .chart-period-selector {
    justify-content: center;
  }
}

/* Thème sombre (optionnel) */
.chart-container.dark {
  background: #1f2937;
  border-color: #374151;
  color: #f9fafb;
}

.chart-container.dark .chart-metrics {
  border-color: #374151;
}

.chart-container.dark .metric-value {
  color: #f9fafb;
}

.chart-container.dark .chart-controls {
  background: #374151;
}

.chart-container.dark .period-btn {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;
}

.chart-container.dark .period-btn:hover {
  background: #6b7280;
}

.chart-container.dark .chart-action-btn {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;
}

/* Accessibilité */
.chart-container:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Réduction de mouvement */
@media (prefers-reduced-motion: reduce) {
  .chart-container,
  .chart-wrapper,
  .chart-container::before {
    animation: none;
    transition: none;
  }
  
  .chart-container:hover {
    transform: none;
  }
}
