import React from 'react';
import { useAuth } from '../../context/AuthContext';
import { UserRole } from '../../types';
import Card, { CardBody, CardHeader } from '../ui/Card';

const AuthDebug: React.FC = () => {
  const { currentUser } = useAuth();

  return (
    <Card className="mb-4 border-yellow-200 bg-yellow-50">
      <CardHeader>
        <h3 className="text-lg font-semibold text-yellow-800">🔍 Debug - État de l'Authentification</h3>
      </CardHeader>
      <CardBody>
        <div className="space-y-2 text-sm">
          <div>
            <strong>Utilisateur connecté :</strong> {currentUser ? 'Oui' : 'Non'}
          </div>
          {currentUser && (
            <>
              <div>
                <strong>ID :</strong> {currentUser.id}
              </div>
              <div>
                <strong>Nom :</strong> {currentUser.username}
              </div>
              <div>
                <strong>Rôle :</strong> {currentUser.role}
              </div>
              <div>
                <strong>Est une entreprise :</strong> {currentUser.role === UserRole.BUSINESS ? 'Oui' : 'Non'}
              </div>
              {currentUser.role === UserRole.BUSINESS && (
                <>
                  <div>
                    <strong>Nom de l'entreprise :</strong> {(currentUser as any).businessName}
                  </div>
                  <div>
                    <strong>Statut business :</strong> {(currentUser as any).businessStatus}
                  </div>
                </>
              )}
            </>
          )}
        </div>
      </CardBody>
    </Card>
  );
};

export default AuthDebug;
