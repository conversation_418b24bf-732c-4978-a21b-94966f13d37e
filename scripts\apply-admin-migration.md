# Application de la Migration d'Administration

## Étapes pour appliquer la migration du système d'administration

### 1. Accéder à Supabase Dashboard

1. Allez sur [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Sélectionnez votre projet Customeroom
3. C<PERSON>z sur "SQL Editor" dans le menu de gauche

### 2. Exécuter la Migration

1. Copiez tout le contenu du fichier `supabase/migrations/20250602000002_create_admin_system.sql`
2. Collez-le dans l'éditeur SQL de Supabase
3. Cliquez sur "Run" pour exécuter la migration

### 3. Vérifier l'Installation

Après l'exécution, vous devriez voir :
- ✅ Tables créées : `admin_profiles`, `admin_permissions`, `admin_sessions`, etc.
- ✅ Permissions insérées : 30+ permissions système
- ✅ Fonctions créées : `check_admin_permission`, `log_admin_action`, etc.
- ✅ Triggers créés : Audit automatique

### 4. <PERSON><PERSON>er le Premier Super Admin

1. Créez d'abord un compte utilisateur normal via l'interface Customeroom
2. Notez l'email et le nom d'utilisateur
3. Copiez le contenu du fichier `scripts/create-super-admin.sql`
4. Modifiez les variables :
   ```sql
   target_email text := '<EMAIL>';
   target_username text := 'votre-username';
   ```
5. Exécutez le script dans l'éditeur SQL Supabase

### 5. Tester l'Accès

1. Connectez-vous avec le compte promu super admin
2. Allez sur `/admin` dans votre application
3. Vous devriez voir le tableau de bord d'administration

### 6. Vérification des Permissions

Exécutez cette requête pour vérifier que tout fonctionne :

```sql
-- Vérifier les tables créées
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE 'admin_%';

-- Vérifier les permissions
SELECT count(*) as permissions_count 
FROM admin_permissions;

-- Vérifier les super admins
SELECT p.username, p.email, ap.admin_code, ap.admin_level
FROM profiles p
JOIN admin_profiles ap ON p.id = ap.user_id
WHERE ap.admin_level = 'super_admin';
```

## Résolution des Problèmes

### Erreur : "relation does not exist"
- Vérifiez que la migration a été exécutée complètement
- Relancez la migration si nécessaire

### Erreur : "permission denied"
- Vérifiez que vous êtes connecté en tant qu'admin dans Supabase
- Vérifiez les politiques RLS

### Accès refusé à /admin
- Vérifiez que l'utilisateur a le rôle 'admin' dans la table profiles
- Vérifiez qu'un profil admin existe dans admin_profiles

### Interface d'admin ne s'affiche pas
- Vérifiez la console du navigateur pour les erreurs
- Vérifiez que les types TypeScript sont à jour
- Redémarrez le serveur de développement

## Support

Si vous rencontrez des problèmes :
1. Vérifiez les logs dans la console Supabase
2. Consultez la documentation dans `docs/ADMIN_SYSTEM.md`
3. Vérifiez les tables et données dans Supabase Dashboard
