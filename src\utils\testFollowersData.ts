import { supabase } from '../lib/supabase';

// Fonction pour créer des données de test pour les followers
export const createTestFollowersData = async (currentUserId: string) => {
  try {
    // Créer quelques profils de test s'ils n'existent pas
    const testProfiles = [
      {
        id: 'test-user-1',
        username: 'marie_du<PERSON>',
        email: '<EMAIL>',
        role: 'standard',
        status: 'verified',
        city: 'Paris',
        country: 'France',
        followers_count: 150,
        following_count: 89,
        post_count: 25,
        comment_count: 45,
        recommendations_count: 12
      },
      {
        id: 'test-user-2',
        username: 'jean_martin',
        email: '<EMAIL>',
        role: 'standard',
        status: 'active',
        city: 'Lyon',
        country: 'France',
        followers_count: 89,
        following_count: 156,
        post_count: 18,
        comment_count: 32,
        recommendations_count: 8
      },
      {
        id: 'test-business-1',
        username: 'tech_solutions',
        email: '<EMAIL>',
        role: 'business',
        status: 'verified',
        business_name: 'Tech Solutions SARL',
        city: 'Abidjan',
        country: 'Côte d\'Ivoire',
        followers_count: 1250,
        following_count: 45,
        post_count: 67,
        comment_count: 123,
        recommendations_count: 34
      }
    ];

    // Insérer les profils de test (ignorer si ils existent déjà)
    for (const profile of testProfiles) {
      const { error } = await supabase
        .from('profiles')
        .upsert(profile, { onConflict: 'id' });
      
      if (error) {
        console.log(`Profil ${profile.username} existe déjà ou erreur:`, error.message);
      }
    }

    // Créer des relations de followers
    const followRelations = [
      // Ces utilisateurs suivent l'utilisateur actuel
      { follower_id: 'test-user-1', following_id: currentUserId },
      { follower_id: 'test-user-2', following_id: currentUserId },
      { follower_id: 'test-business-1', following_id: currentUserId },
      
      // L'utilisateur actuel suit ces utilisateurs
      { follower_id: currentUserId, following_id: 'test-user-1' },
      { follower_id: currentUserId, following_id: 'test-business-1' }
    ];

    for (const relation of followRelations) {
      const { error } = await supabase
        .from('followers')
        .upsert(relation, { onConflict: 'follower_id,following_id' });
      
      if (error) {
        console.log('Relation de suivi existe déjà ou erreur:', error.message);
      }
    }

    console.log('Données de test créées avec succès !');
    return true;
  } catch (error) {
    console.error('Erreur lors de la création des données de test:', error);
    return false;
  }
};

// Fonction pour nettoyer les données de test
export const cleanupTestData = async () => {
  try {
    // Supprimer les relations de followers de test
    await supabase
      .from('followers')
      .delete()
      .in('follower_id', ['test-user-1', 'test-user-2', 'test-business-1']);

    await supabase
      .from('followers')
      .delete()
      .in('following_id', ['test-user-1', 'test-user-2', 'test-business-1']);

    // Supprimer les profils de test
    await supabase
      .from('profiles')
      .delete()
      .in('id', ['test-user-1', 'test-user-2', 'test-business-1']);

    console.log('Données de test nettoyées avec succès !');
    return true;
  } catch (error) {
    console.error('Erreur lors du nettoyage des données de test:', error);
    return false;
  }
};
