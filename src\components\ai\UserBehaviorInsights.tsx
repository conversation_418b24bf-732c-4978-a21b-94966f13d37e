import React, { useState, useEffect } from 'react';
import { 
  Brain, 
  TrendingUp, 
  Heart, 
  MessageCircle, 
  ShoppingCart, 
  Target,
  BarChart3,
  Clock,
  Smile,
  Frown,
  <PERSON>h,
  Zap
} from 'lucide-react';
import { UserBehaviorAnalysisService, IUserBehaviorProfile } from '../../services/userBehaviorAnalysisService';
import Card, { CardBody, CardHeader } from '../ui/Card';
import { useAuth } from '../../context/AuthContext';

const UserBehaviorInsights: React.FC = () => {
  const { currentUser } = useAuth();
  const [behaviorProfile, setBehaviorProfile] = useState<IUserBehaviorProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (currentUser?.id) {
      loadBehaviorProfile();
    }
  }, [currentUser]);

  const loadBehaviorProfile = async () => {
    if (!currentUser?.id) return;
    
    setLoading(true);
    try {
      const profile = await UserBehaviorAnalysisService.generateUserBehaviorProfile(currentUser.id);
      setBehaviorProfile(profile);
    } catch (error) {
      console.error('Erreur lors du chargement du profil comportemental:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSentimentIcon = (sentiment: 'positive' | 'neutral' | 'negative') => {
    switch (sentiment) {
      case 'positive': return <Smile className="text-green-500" size={20} />;
      case 'negative': return <Frown className="text-red-500" size={20} />;
      default: return <Meh className="text-gray-500" size={20} />;
    }
  };

  const getSentimentColor = (sentiment: 'positive' | 'neutral' | 'negative') => {
    switch (sentiment) {
      case 'positive': return 'text-green-600 bg-green-50';
      case 'negative': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getEngagementColor = (level: 'low' | 'medium' | 'high') => {
    switch (level) {
      case 'high': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardBody className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Analyse de votre comportement en cours...</p>
        </CardBody>
      </Card>
    );
  }

  if (!behaviorProfile) {
    return (
      <Card>
        <CardBody className="text-center py-8">
          <Brain className="mx-auto text-gray-400 mb-4" size={48} />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Profil comportemental non disponible
          </h3>
          <p className="text-gray-600">
            Interagissez plus avec la plateforme pour que l'IA puisse analyser vos préférences.
          </p>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center">
            <Brain className="text-blue-600 mr-3" size={24} />
            <div>
              <h2 className="text-xl font-bold text-gray-900">Votre Profil Comportemental</h2>
              <p className="text-sm text-gray-600">Analyse IA de vos préférences et habitudes</p>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Sentiment Analysis */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            {getSentimentIcon(behaviorProfile.sentimentAnalysis.overallSentiment)}
            <span className="ml-2">Analyse de Sentiment</span>
          </h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className={`p-4 rounded-lg ${getSentimentColor(behaviorProfile.sentimentAnalysis.overallSentiment)}`}>
              <h4 className="font-semibold mb-2">Sentiment Général</h4>
              <p className="text-sm capitalize">{behaviorProfile.sentimentAnalysis.overallSentiment}</p>
            </div>
            <div className="p-4 rounded-lg bg-green-50">
              <h4 className="font-semibold mb-2 text-green-700">Mots-clés Positifs</h4>
              <div className="flex flex-wrap gap-1">
                {behaviorProfile.sentimentAnalysis.positiveKeywords.slice(0, 5).map((keyword, index) => (
                  <span key={index} className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded">
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
            <div className="p-4 rounded-lg bg-red-50">
              <h4 className="font-semibold mb-2 text-red-700">Mots-clés Négatifs</h4>
              <div className="flex flex-wrap gap-1">
                {behaviorProfile.sentimentAnalysis.negativeKeywords.slice(0, 5).map((keyword, index) => (
                  <span key={index} className="text-xs bg-red-200 text-red-800 px-2 py-1 rounded">
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Preferences */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Heart className="text-red-500 mr-2" size={20} />
            Préférences Détectées
          </h3>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Catégories Préférées</h4>
              <div className="flex flex-wrap gap-2">
                {Object.entries(behaviorProfile.preferences.categories)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 5)
                  .map(([category, score]) => (
                    <div key={category} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                      {category} ({Math.round(score * 100)}%)
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Interaction Patterns */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <BarChart3 className="text-purple-500 mr-2" size={20} />
            Patterns d'Interaction
          </h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Clock className="mx-auto text-gray-600 mb-2" size={24} />
              <h4 className="font-medium text-gray-900">Heure Active</h4>
              <p className="text-sm text-gray-600">{behaviorProfile.interactionPatterns.mostActiveTimeOfDay}</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Zap className="mx-auto text-yellow-600 mb-2" size={24} />
              <h4 className="font-medium text-gray-900">Engagement</h4>
              <span className={`text-sm px-2 py-1 rounded ${getEngagementColor(behaviorProfile.interactionPatterns.engagementLevel)}`}>
                {behaviorProfile.interactionPatterns.engagementLevel}
              </span>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Clock className="mx-auto text-blue-600 mb-2" size={24} />
              <h4 className="font-medium text-gray-900">Session Moyenne</h4>
              <p className="text-sm text-gray-600">{behaviorProfile.interactionPatterns.averageSessionDuration} min</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <MessageCircle className="mx-auto text-green-600 mb-2" size={24} />
              <h4 className="font-medium text-gray-900">Contenu Préféré</h4>
              <p className="text-xs text-gray-600">
                {behaviorProfile.interactionPatterns.preferredContentTypes.join(', ')}
              </p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Purchase Intent */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <ShoppingCart className="text-green-500 mr-2" size={20} />
            Intention d'Achat
          </h3>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-700">Score d'intention</span>
              <div className="flex items-center space-x-2">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${behaviorProfile.purchaseIntent.score}%` }}
                  ></div>
                </div>
                <span className="text-sm font-semibold text-green-600">
                  {behaviorProfile.purchaseIntent.score}%
                </span>
              </div>
            </div>
            
            {behaviorProfile.purchaseIntent.indicators.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Indicateurs Détectés</h4>
                <ul className="space-y-1">
                  {behaviorProfile.purchaseIntent.indicators.map((indicator, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-center">
                      <Target className="text-green-500 mr-2" size={14} />
                      {indicator}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </CardBody>
      </Card>

      {/* Social Behavior */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <TrendingUp className="text-orange-500 mr-2" size={20} />
            Comportement Social
          </h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className={`w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center ${
                behaviorProfile.socialBehavior.followsBusinesses ? 'bg-green-100' : 'bg-gray-100'
              }`}>
                <Heart className={behaviorProfile.socialBehavior.followsBusinesses ? 'text-green-600' : 'text-gray-400'} size={20} />
              </div>
              <p className="text-xs text-gray-600">Suit des entreprises</p>
            </div>
            <div className="text-center">
              <div className={`w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center ${
                behaviorProfile.socialBehavior.commentsFrequently ? 'bg-blue-100' : 'bg-gray-100'
              }`}>
                <MessageCircle className={behaviorProfile.socialBehavior.commentsFrequently ? 'text-blue-600' : 'text-gray-400'} size={20} />
              </div>
              <p className="text-xs text-gray-600">Commente souvent</p>
            </div>
            <div className="text-center">
              <div className={`w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center ${
                behaviorProfile.socialBehavior.reactsToContent ? 'bg-yellow-100' : 'bg-gray-100'
              }`}>
                <Zap className={behaviorProfile.socialBehavior.reactsToContent ? 'text-yellow-600' : 'text-gray-400'} size={20} />
              </div>
              <p className="text-xs text-gray-600">Réagit au contenu</p>
            </div>
            <div className="text-center">
              <div className={`w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center ${
                behaviorProfile.socialBehavior.sharesContent ? 'bg-purple-100' : 'bg-gray-100'
              }`}>
                <TrendingUp className={behaviorProfile.socialBehavior.sharesContent ? 'text-purple-600' : 'text-gray-400'} size={20} />
              </div>
              <p className="text-xs text-gray-600">Partage du contenu</p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default UserBehaviorInsights;
