/* Gestionnaire de recommandations IA */
.ai-recommendations-manager {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem;
  background: #f8fafc;
  min-height: 100vh;
}

/* État de chargement */
.ai-recommendations-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ai-recommendations-loading h3 {
  margin: 1rem 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
}

.ai-recommendations-loading p {
  margin: 0;
  color: #6b7280;
}

/* En-tête */
.ai-header {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-left .page-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-title h1 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.page-title p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.refresh-btn, .export-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
}

/* Métriques rapides */
.quick-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  color: white;
}

.metric-card.sentiment .metric-icon { background: #10b981; }
.metric-card.improvements .metric-icon { background: #f59e0b; }
.metric-card.new-products .metric-icon { background: #3b82f6; }
.metric-card.trends .metric-icon { background: #8b5cf6; }

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Onglets */
.ai-tabs {
  display: flex;
  background: #f9fafb;
  border-radius: 8px;
  padding: 0.25rem;
  gap: 0.25rem;
  overflow-x: auto;
}

.ai-tabs .tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.ai-tabs .tab:hover {
  color: #374151;
  background: #f3f4f6;
}

.ai-tabs .tab.active {
  background: white;
  color: #8b5cf6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Contenu */
.ai-content {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 500px;
}

.tab-header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.tab-header h3 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
}

.tab-header p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

/* Vue d'ensemble */
.overview-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.priority-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.priority-action {
  padding: 1rem;
  background: #fef3c7;
  border-left: 4px solid #f59e0b;
  border-radius: 8px;
}

.action-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.action-title {
  font-weight: 500;
  color: #92400e;
}

.action-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.product-name {
  font-weight: 500;
  color: #1f2937;
}

.impact {
  color: #6b7280;
}

/* Opportunités de marché */
.market-opportunities {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.opportunity-card {
  padding: 1rem;
  background: #eff6ff;
  border-left: 4px solid #3b82f6;
  border-radius: 8px;
}

.opportunity-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.demand-indicator {
  font-size: 1.5rem;
}

.opportunity-header h4 {
  margin: 0 0 0.5rem 0;
  color: #1e40af;
}

.opportunity-header p {
  margin: 0;
  color: #374151;
  font-size: 0.875rem;
}

.opportunity-metrics {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.opportunity-metrics .metric {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #1e40af;
}

/* Analyse de sentiment */
.sentiment-analysis {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sentiment-chart {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.sentiment-bar {
  display: flex;
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
  background: #f3f4f6;
}

.sentiment-positive { background: #10b981; }
.sentiment-neutral { background: #f59e0b; }
.sentiment-negative { background: #ef4444; }

.sentiment-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
}

.sentiment-labels .positive { color: #10b981; }
.sentiment-labels .neutral { color: #f59e0b; }
.sentiment-labels .negative { color: #ef4444; }

.sentiment-insights {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.compliments, .complaints {
  padding: 1rem;
  border-radius: 8px;
}

.compliments {
  background: #d1fae5;
  border-left: 4px solid #10b981;
}

.complaints {
  background: #fed7aa;
  border-left: 4px solid #f59e0b;
}

.compliments h4, .complaints h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
}

.compliments ul, .complaints ul {
  margin: 0;
  padding-left: 1.25rem;
}

.compliments li, .complaints li {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Améliorations */
.improvements-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.improvement-card {
  border-left: 4px solid #8b5cf6;
}

.improvement-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.improvement-info {
  flex: 1;
}

.improvement-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.improvement-title h4 {
  margin: 0;
  color: #1f2937;
  font-size: 1rem;
}

.priority-badge {
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.priority-badge.urgent {
  background: #fee2e2;
  color: #991b1b;
}

.priority-badge.high {
  background: #fed7aa;
  color: #9a3412;
}

.priority-badge.medium {
  background: #fef3c7;
  color: #92400e;
}

.priority-badge.low {
  background: #d1fae5;
  color: #065f46;
}

.improvement-type {
  font-size: 0.875rem;
  color: #6b7280;
}

.improvement-actions {
  display: flex;
  gap: 0.5rem;
}

.improvement-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.suggestion {
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.5;
}

.improvement-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.improvement-metrics .metric {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.improvement-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.keyword-tag {
  padding: 0.25rem 0.5rem;
  background: #e0e7ff;
  color: #3730a3;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Nouveaux produits */
.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.suggestion-card {
  border-left: 4px solid #3b82f6;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.suggestion-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.suggestion-title h4 {
  margin: 0;
  color: #1f2937;
}

.suggestion-badges {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-end;
}

.demand-badge, .competition-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.demand-badge.very_high {
  background: #fee2e2;
  color: #991b1b;
}

.demand-badge.high {
  background: #fed7aa;
  color: #9a3412;
}

.demand-badge.medium {
  background: #fef3c7;
  color: #92400e;
}

.demand-badge.low {
  background: #d1fae5;
  color: #065f46;
}

.suggestion-description {
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.suggestion-metrics {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.metric-row {
  display: flex;
  gap: 1rem;
}

.metric-row .metric {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
  flex: 1;
}

.target-audience, .key-features, .based-on-comments {
  margin-bottom: 1rem;
}

.target-audience h5, .key-features h5, .based-on-comments h5 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.target-audience p {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.feature-tag {
  padding: 0.25rem 0.5rem;
  background: #dbeafe;
  color: #1e40af;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.comment-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

.quote-mark {
  color: #3b82f6;
  font-weight: bold;
}

.suggestion-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* Responsive */
@media (max-width: 768px) {
  .ai-recommendations-manager {
    padding: 0.5rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .quick-metrics {
    grid-template-columns: 1fr;
  }

  .ai-tabs {
    overflow-x: auto;
  }

  .sentiment-insights {
    grid-template-columns: 1fr;
  }

  .suggestions-grid {
    grid-template-columns: 1fr;
  }

  .improvement-header {
    flex-direction: column;
    gap: 1rem;
  }

  .improvement-actions {
    align-self: flex-start;
  }

  .metric-row {
    flex-direction: column;
    gap: 0.5rem;
  }
}
