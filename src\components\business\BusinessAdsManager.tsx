import React, { useState, useEffect } from 'react';
import { MessageCircle, Eye, TrendingUp, Calendar, Reply, Send, AlertCircle, RefreshCw } from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import Avatar from '../ui/Avatar';
import { supabase } from '../../lib/supabase';
import { formatTimeAgo } from '../../utils/dateUtils';

interface AdCampaign {
  id: string;
  title: string;
  description: string;
  image_url: string;
  status: string;
  impressions: number;
  clicks: number;
  created_at: string;
}

interface AdComment {
  id: string;
  campaign_id: string;
  user_id: string;
  content: string;
  parent_comment_id: string | null;
  created_at: string;
  is_approved: boolean;
  // Données utilisateur jointes
  username: string;
  profile_picture: string | null;
  // Réponses
  replies?: AdComment[];
}

interface AdResponse {
  id: string;
  comment_id: string;
  business_id: string;
  content: string;
  created_at: string;
}

interface BusinessAdsManagerProps {
  businessId: string;
}

const BusinessAdsManager: React.FC<BusinessAdsManagerProps> = ({ businessId }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'comments'>('overview');
  const [campaigns, setCampaigns] = useState<AdCampaign[]>([]);
  const [comments, setComments] = useState<AdComment[]>([]);
  const [responses, setResponses] = useState<AdResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');
  const [submitting, setSubmitting] = useState(false);

  // Charger les campagnes publicitaires de l'entreprise
  const loadCampaigns = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('ad_campaigns')
        .select('*')
        .eq('business_id', businessId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setCampaigns(data || []);
    } catch (error) {
      console.error('Erreur lors du chargement des campagnes:', error);
    } finally {
      setLoading(false);
    }
  };

  // Charger les commentaires sur les publicités de l'entreprise
  const loadComments = async () => {
    setLoading(true);
    try {
      // Récupérer les IDs des campagnes de l'entreprise
      const { data: campaignIds } = await supabase
        .from('ad_campaigns')
        .select('id')
        .eq('business_id', businessId);

      if (!campaignIds || campaignIds.length === 0) {
        setComments([]);
        return;
      }

      const ids = campaignIds.map(c => c.id);

      // Récupérer les commentaires avec les informations utilisateur
      const { data: commentsData, error } = await supabase
        .from('ad_comments')
        .select(`
          *,
          profiles:user_id (
            username,
            profile_picture
          ),
          ad_campaigns:campaign_id (
            title
          )
        `)
        .in('campaign_id', ids)
        .eq('is_approved', true)
        .is('parent_comment_id', null) // Seulement les commentaires principaux
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Formater les données
      const formattedComments = commentsData?.map(comment => ({
        id: comment.id,
        campaign_id: comment.campaign_id,
        user_id: comment.user_id,
        content: comment.content,
        parent_comment_id: comment.parent_comment_id,
        created_at: comment.created_at,
        is_approved: comment.is_approved,
        username: comment.profiles?.username || 'Utilisateur anonyme',
        profile_picture: comment.profiles?.profile_picture,
        campaign_title: comment.ad_campaigns?.title || 'Campagne inconnue'
      })) || [];

      setComments(formattedComments);

      // Charger les réponses existantes
      await loadResponses(ids);

    } catch (error) {
      console.error('Erreur lors du chargement des commentaires:', error);
    } finally {
      setLoading(false);
    }
  };

  // Charger les réponses de l'entreprise
  const loadResponses = async (campaignIds: string[]) => {
    try {
      const { data, error } = await supabase
        .from('ad_business_responses')
        .select('*')
        .eq('business_id', businessId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setResponses(data || []);
    } catch (error) {
      console.error('Erreur lors du chargement des réponses:', error);
    }
  };

  // Répondre à un commentaire
  const handleReply = async (commentId: string) => {
    if (!replyText.trim() || submitting) return;

    setSubmitting(true);
    try {
      // Insérer la réponse dans la table des réponses business
      const { error } = await supabase
        .from('ad_business_responses')
        .insert([{
          comment_id: commentId,
          business_id: businessId,
          content: replyText.trim()
        }]);

      if (error) throw error;

      // Recharger les données
      await loadComments();
      
      // Réinitialiser le formulaire
      setReplyText('');
      setReplyingTo(null);

      console.log('✅ Réponse ajoutée avec succès');
    } catch (error) {
      console.error('Erreur lors de l\'ajout de la réponse:', error);
      alert('Erreur lors de l\'ajout de la réponse');
    } finally {
      setSubmitting(false);
    }
  };

  // Obtenir la réponse pour un commentaire
  const getResponseForComment = (commentId: string) => {
    return responses.find(r => r.comment_id === commentId);
  };

  useEffect(() => {
    loadCampaigns();
    loadComments();
  }, [businessId]);

  const tabs = [
    { id: 'overview', label: 'Vue d\'ensemble', icon: <Eye size={16} /> },
    { id: 'comments', label: 'Ads Comments', icon: <MessageCircle size={16} /> }
  ];

  return (
    <div className="space-y-6">
      {/* En-tête avec onglets */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">Gestion des Publicités</h2>
            <Button
              variant="outline"
              leftIcon={<RefreshCw size={16} />}
              onClick={() => {
                loadCampaigns();
                loadComments();
              }}
              disabled={loading}
            >
              Actualiser
            </Button>
          </div>
          
          {/* Navigation des onglets */}
          <div className="border-b border-gray-200 mt-4">
            <nav className="flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </CardHeader>
      </Card>

      {/* Contenu des onglets */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Statistiques globales */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardBody>
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <TrendingUp className="text-blue-600" size={24} />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">{campaigns.length}</h3>
                    <p className="text-sm text-gray-500">Campagnes actives</p>
                  </div>
                </div>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Eye className="text-green-600" size={24} />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      {campaigns.reduce((sum, c) => sum + c.impressions, 0).toLocaleString()}
                    </h3>
                    <p className="text-sm text-gray-500">Impressions totales</p>
                  </div>
                </div>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <MessageCircle className="text-purple-600" size={24} />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">{comments.length}</h3>
                    <p className="text-sm text-gray-500">Commentaires reçus</p>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Liste des campagnes */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Mes Campagnes Publicitaires</h3>
            </CardHeader>
            <CardBody>
              {loading ? (
                <div className="text-center py-8">
                  <RefreshCw className="animate-spin mx-auto mb-2" size={24} />
                  <p>Chargement...</p>
                </div>
              ) : campaigns.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <TrendingUp size={48} className="mx-auto mb-4 text-gray-300" />
                  <p>Aucune campagne publicitaire</p>
                  <p className="text-sm">Créez votre première campagne pour commencer</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {campaigns.map((campaign) => (
                    <div key={campaign.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{campaign.title}</h4>
                          <p className="text-sm text-gray-600 mt-1">{campaign.description}</p>
                          <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                            <span>{campaign.impressions} impressions</span>
                            <span>{campaign.clicks} clics</span>
                            <span>Créée {formatTimeAgo(new Date(campaign.created_at))}</span>
                          </div>
                        </div>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          campaign.status === 'active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {campaign.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardBody>
          </Card>
        </div>
      )}

      {activeTab === 'comments' && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Commentaires sur vos Publicités</h3>
            <p className="text-sm text-gray-600">
              Répondez aux commentaires de vos clients pour améliorer l'engagement
            </p>
          </CardHeader>
          <CardBody>
            {loading ? (
              <div className="text-center py-8">
                <RefreshCw className="animate-spin mx-auto mb-2" size={24} />
                <p>Chargement des commentaires...</p>
              </div>
            ) : comments.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <MessageCircle size={48} className="mx-auto mb-4 text-gray-300" />
                <p>Aucun commentaire pour le moment</p>
                <p className="text-sm">Les commentaires sur vos publicités apparaîtront ici</p>
              </div>
            ) : (
              <div className="space-y-6">
                {comments.map((comment) => {
                  const response = getResponseForComment(comment.id);
                  return (
                    <div key={comment.id} className="border rounded-lg p-4">
                      {/* Commentaire original */}
                      <div className="flex items-start space-x-3">
                        <Avatar
                          src={comment.profile_picture}
                          alt={comment.username}
                          size="sm"
                        />
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-gray-900">{comment.username}</span>
                            <span className="text-xs text-gray-500">
                              {formatTimeAgo(new Date(comment.created_at))}
                            </span>
                          </div>
                          <p className="text-gray-700 mb-2">{comment.content}</p>
                          <p className="text-xs text-gray-500">
                            Sur la publicité : {(comment as any).campaign_title}
                          </p>
                        </div>
                      </div>

                      {/* Réponse existante */}
                      {response && (
                        <div className="mt-4 ml-12 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-sm font-medium text-blue-900">Votre réponse</span>
                            <span className="text-xs text-blue-600">
                              {formatTimeAgo(new Date(response.created_at))}
                            </span>
                          </div>
                          <p className="text-sm text-blue-800">{response.content}</p>
                        </div>
                      )}

                      {/* Formulaire de réponse */}
                      {!response && (
                        <div className="mt-4 ml-12">
                          {replyingTo === comment.id ? (
                            <div className="space-y-3">
                              <textarea
                                value={replyText}
                                onChange={(e) => setReplyText(e.target.value)}
                                placeholder="Écrivez votre réponse..."
                                className="w-full p-3 border border-gray-300 rounded-lg resize-none"
                                rows={3}
                                maxLength={500}
                              />
                              <div className="flex items-center justify-between">
                                <span className="text-xs text-gray-500">
                                  {replyText.length}/500 caractères
                                </span>
                                <div className="flex space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setReplyingTo(null);
                                      setReplyText('');
                                    }}
                                  >
                                    Annuler
                                  </Button>
                                  <Button
                                    size="sm"
                                    leftIcon={<Send size={14} />}
                                    onClick={() => handleReply(comment.id)}
                                    disabled={!replyText.trim() || submitting}
                                  >
                                    {submitting ? 'Envoi...' : 'Répondre'}
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              leftIcon={<Reply size={14} />}
                              onClick={() => setReplyingTo(comment.id)}
                            >
                              Répondre
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default BusinessAdsManager;
