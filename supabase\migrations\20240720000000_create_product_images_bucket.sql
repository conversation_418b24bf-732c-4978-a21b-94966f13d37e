-- Migration pour créer le bucket de stockage product-images

-- Création du bucket de stockage pour les images de produits
-- Cette commande doit être exécutée avec les privilèges appropriés
INSERT INTO storage.buckets (id, name, public)
VALUES ('product-images', 'product-images', true)
ON CONFLICT (id) DO NOTHING;

-- Suppression des politiques existantes pour le stockage (au cas où)
DROP POLICY IF EXISTS "Les images de produits sont accessibles publiquement" ON storage.objects;
DROP POLICY IF EXISTS "Les utilisateurs peuvent télécharger leurs propres images de produits" ON storage.objects;
DROP POLICY IF EXISTS "Les utilisateurs peuvent supprimer leurs propres images de produits" ON storage.objects;

-- Politique pour permettre l'accès public en lecture aux images de produits
CREATE POLICY "Les images de produits sont accessibles publiquement"
ON storage.objects FOR SELECT
USING (bucket_id = 'product-images');

-- Création d'une politique pour permettre aux utilisateurs authentifiés de télécharger leurs propres images
CREATE POLICY "Les utilisateurs peuvent télécharger leurs propres images de produits"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'product-images' AND 
  (
    -- Permet les chemins avec l'ID de l'entreprise comme premier segment
    (storage.foldername(name))[1] = auth.uid()::text
  )
);

-- Politique pour permettre aux utilisateurs de supprimer leurs propres images
CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres images de produits"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'product-images' AND 
  (
    -- Permet les chemins avec l'ID de l'entreprise comme premier segment
    (storage.foldername(name))[1] = auth.uid()::text
  )
);