import { offerInteractionService } from '../services/offerInteractionService';
import { realCameroonianOffers } from '../data/realOffersData';

/**
 * Utilitaire de test pour le service d'interactions avec les offres
 */
export class OfferInteractionTester {
  
  /**
   * Tester les likes d'offres
   */
  static async testOfferLikes(userId: string): Promise<void> {
    console.log('🧪 Test: Likes d\'offres');
    console.log('👤 User ID:', userId);
    
    if (!userId) {
      console.error('❌ User ID requis pour les tests');
      return;
    }

    try {
      // Prendre la première offre pour les tests
      const testOffer = realCameroonianOffers[0];
      if (!testOffer) {
        console.error('❌ Aucune offre disponible pour les tests');
        return;
      }

      console.log('🎯 Test avec l\'offre:', testOffer.title);
      console.log('🆔 Offer ID:', testOffer.id);

      // Test 1: Vérifier l'état initial
      const initialLiked = await offerInteractionService.hasUserLikedOffer(testOffer.id, userId);
      const initialCount = await offerInteractionService.getOfferLikesCount(testOffer.id);
      
      console.log('📊 État initial:');
      console.log(`   - Déjà liké: ${initialLiked}`);
      console.log(`   - Nombre de likes: ${initialCount}`);

      // Test 2: Ajouter un like
      console.log('➕ Test d\'ajout de like...');
      const likeSuccess = await offerInteractionService.likeOffer(testOffer.id, userId);
      
      if (likeSuccess) {
        console.log('✅ Like ajouté avec succès');
        
        // Vérifier le nouvel état
        const afterLikeCount = await offerInteractionService.getOfferLikesCount(testOffer.id);
        const isNowLiked = await offerInteractionService.hasUserLikedOffer(testOffer.id, userId);
        
        console.log('📊 Après like:');
        console.log(`   - Maintenant liké: ${isNowLiked}`);
        console.log(`   - Nouveau nombre: ${afterLikeCount}`);
        
        // Test 3: Retirer le like
        console.log('➖ Test de suppression de like...');
        const unlikeSuccess = await offerInteractionService.unlikeOffer(testOffer.id, userId);
        
        if (unlikeSuccess) {
          console.log('✅ Like retiré avec succès');
          
          // Vérifier l'état final
          const finalCount = await offerInteractionService.getOfferLikesCount(testOffer.id);
          const finalLiked = await offerInteractionService.hasUserLikedOffer(testOffer.id, userId);
          
          console.log('📊 État final:');
          console.log(`   - Encore liké: ${finalLiked}`);
          console.log(`   - Nombre final: ${finalCount}`);
        } else {
          console.log('❌ Échec de la suppression du like');
        }
      } else {
        console.log('❌ Échec de l\'ajout du like');
      }
      
    } catch (error) {
      console.error('❌ Erreur lors du test des likes:', error);
    }
  }

  /**
   * Tester les partages d'offres
   */
  static async testOfferShares(userId: string): Promise<void> {
    console.log('🧪 Test: Partages d\'offres');
    console.log('👤 User ID:', userId);
    
    if (!userId) {
      console.error('❌ User ID requis pour les tests');
      return;
    }

    try {
      // Prendre la deuxième offre pour les tests
      const testOffer = realCameroonianOffers[1] || realCameroonianOffers[0];
      if (!testOffer) {
        console.error('❌ Aucune offre disponible pour les tests');
        return;
      }

      console.log('🎯 Test avec l\'offre:', testOffer.title);
      console.log('🆔 Offer ID:', testOffer.id);

      // Test des différents types de partage
      const shareTypes = ['facebook', 'twitter', 'whatsapp', 'copy_link'] as const;
      
      for (const shareType of shareTypes) {
        console.log(`📤 Test partage ${shareType}...`);
        
        const initialCount = await offerInteractionService.getOfferSharesCount(testOffer.id);
        console.log(`   - Partages initiaux: ${initialCount}`);
        
        const shareSuccess = await offerInteractionService.shareOffer(testOffer.id, userId, shareType);
        
        if (shareSuccess) {
          console.log(`✅ Partage ${shareType} réussi`);
          
          const newCount = await offerInteractionService.getOfferSharesCount(testOffer.id);
          console.log(`   - Nouveaux partages: ${newCount}`);
        } else {
          console.log(`❌ Échec du partage ${shareType}`);
        }
      }
      
    } catch (error) {
      console.error('❌ Erreur lors du test des partages:', error);
    }
  }

  /**
   * Tester avec des IDs invalides
   */
  static async testWithInvalidIds(): Promise<void> {
    console.log('🧪 Test: Gestion des IDs invalides');
    
    const invalidIds = [
      'invalid-offer-id',
      '123',
      '',
      'non-existent-offer'
    ];
    
    const testUserId = '00000000-0000-0000-0000-000000000001'; // ID de test
    
    for (const invalidId of invalidIds) {
      console.log(`🔍 Test avec ID invalide: "${invalidId}"`);
      
      try {
        // Test like avec ID invalide
        const likeResult = await offerInteractionService.likeOffer(invalidId, testUserId);
        console.log(`   - Like: ${likeResult ? 'Réussi' : 'Échoué'} (attendu: Échoué)`);
        
        // Test count avec ID invalide
        const count = await offerInteractionService.getOfferLikesCount(invalidId);
        console.log(`   - Count: ${count} (attendu: 0)`);
        
      } catch (error) {
        console.log(`   - Erreur capturée (normal):`, error.message);
      }
    }
  }

  /**
   * Test de performance
   */
  static async testPerformance(userId: string, iterations: number = 3): Promise<void> {
    console.log('🧪 Test: Performance des interactions');
    console.log(`🔄 Nombre d'itérations: ${iterations}`);
    
    if (!userId) {
      console.error('❌ User ID requis pour les tests');
      return;
    }

    try {
      const testOffer = realCameroonianOffers[0];
      if (!testOffer) {
        console.error('❌ Aucune offre disponible pour les tests');
        return;
      }

      const startTime = Date.now();
      
      // Exécuter plusieurs opérations en parallèle
      const promises = Array.from({ length: iterations }, async (_, i) => {
        const operationStart = Date.now();
        
        // Séquence d'opérations
        await offerInteractionService.hasUserLikedOffer(testOffer.id, userId);
        await offerInteractionService.getOfferLikesCount(testOffer.id);
        await offerInteractionService.getOfferSharesCount(testOffer.id);
        
        const operationEnd = Date.now();
        return operationEnd - operationStart;
      });
      
      const results = await Promise.all(promises);
      const endTime = Date.now();
      
      console.log('✅ Test de performance terminé');
      console.log(`⏱️ Temps total: ${endTime - startTime}ms`);
      console.log(`📊 Temps par itération: ${results.map(t => `${t}ms`).join(', ')}`);
      console.log(`📈 Moyenne: ${Math.round(results.reduce((a, b) => a + b, 0) / results.length)}ms`);
      
    } catch (error) {
      console.error('❌ Erreur lors du test de performance:', error);
    }
  }

  /**
   * Afficher les statistiques des offres
   */
  static async showOfferStats(): Promise<void> {
    console.log('📊 Statistiques des offres');
    console.log('=' .repeat(50));
    
    try {
      for (const offer of realCameroonianOffers.slice(0, 3)) { // Limiter à 3 offres
        console.log(`🎯 ${offer.title}`);
        console.log(`   ID: ${offer.id}`);
        console.log(`   Entreprise: ${offer.businessName}`);
        
        const likesCount = await offerInteractionService.getOfferLikesCount(offer.id);
        const sharesCount = await offerInteractionService.getOfferSharesCount(offer.id);
        
        console.log(`   👍 Likes: ${likesCount}`);
        console.log(`   📤 Partages: ${sharesCount}`);
        console.log('');
      }
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des statistiques:', error);
    }
  }

  /**
   * Exécuter tous les tests
   */
  static async runAllTests(userId: string): Promise<void> {
    console.log('🚀 Démarrage des tests d\'interactions avec les offres');
    console.log('=' .repeat(60));
    
    await this.showOfferStats();
    console.log('');
    
    await this.testOfferLikes(userId);
    console.log('');
    
    await this.testOfferShares(userId);
    console.log('');
    
    await this.testWithInvalidIds();
    console.log('');
    
    await this.testPerformance(userId, 3);
    console.log('');
    
    console.log('🎉 Tests terminés');
    console.log('=' .repeat(60));
  }
}

// Fonction utilitaire pour exécuter les tests depuis la console
export const testOfferInteractions = (userId: string) => {
  OfferInteractionTester.runAllTests(userId);
};

// Export pour utilisation dans la console du navigateur
if (typeof window !== 'undefined') {
  (window as any).testOfferInteractions = testOfferInteractions;
  (window as any).OfferInteractionTester = OfferInteractionTester;
  
  console.log('🎯 Utilitaires de test des offres chargés ! Tapez testOfferInteractions("user-id") pour commencer.');
}
