-- Migration pour créer le système de demandes de suivi
-- Système d'abonnement avec demandes d'acceptation

-- =====================================================
-- 1. TABLE DES DEMANDES DE SUIVI
-- =====================================================
CREATE TABLE IF NOT EXISTS follow_requests (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  requester_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  target_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,

  -- Statut de la demande
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'cancelled')),
  
  -- Type de demande
  request_type text NOT NULL DEFAULT 'follow' CHECK (request_type IN ('follow', 'subscribe')),
  
  -- Message optionnel avec la demande
  message text,
  
  -- Métadonnées
  metadata jsonb DEFAULT '{}'::jsonb,

  -- Timestamps
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  responded_at timestamptz,

  -- Contraintes
  UNIQUE(requester_id, target_id, status) -- Éviter les doublons de demandes en attente
);

-- =====================================================
-- 2. MODIFIER LA TABLE FOLLOWERS POUR INCLURE LE STATUT
-- =====================================================
-- Ajouter des colonnes à la table followers existante
ALTER TABLE followers 
ADD COLUMN IF NOT EXISTS status text DEFAULT 'active' CHECK (status IN ('active', 'blocked', 'muted')),
ADD COLUMN IF NOT EXISTS follow_type text DEFAULT 'follow' CHECK (follow_type IN ('follow', 'subscribe')),
ADD COLUMN IF NOT EXISTS created_at timestamptz DEFAULT now(),
ADD COLUMN IF NOT EXISTS updated_at timestamptz DEFAULT now();

-- =====================================================
-- 3. TABLE DES PARAMÈTRES DE CONFIDENTIALITÉ
-- =====================================================
CREATE TABLE IF NOT EXISTS privacy_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,

  -- Paramètres de suivi
  require_follow_approval boolean DEFAULT false,
  allow_follow_requests boolean DEFAULT true,
  auto_accept_verified_users boolean DEFAULT false,
  auto_accept_business_users boolean DEFAULT false,

  -- Paramètres de notifications
  notify_follow_requests boolean DEFAULT true,
  notify_new_followers boolean DEFAULT true,
  notify_follow_accepted boolean DEFAULT true,
  notify_follow_rejected boolean DEFAULT false,

  -- Paramètres de visibilité
  profile_visibility text DEFAULT 'public' CHECK (profile_visibility IN ('public', 'followers_only', 'private')),
  posts_visibility text DEFAULT 'public' CHECK (posts_visibility IN ('public', 'followers_only', 'private')),

  -- Timestamps
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),

  -- Contrainte unique
  UNIQUE(user_id)
);

-- =====================================================
-- 4. TABLE DES BLOCAGES
-- =====================================================
CREATE TABLE IF NOT EXISTS user_blocks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  blocker_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  blocked_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,

  -- Raison du blocage
  reason text,
  
  -- Type de blocage
  block_type text DEFAULT 'full' CHECK (block_type IN ('full', 'posts_only', 'messages_only')),

  -- Timestamps
  created_at timestamptz DEFAULT now(),

  -- Contraintes
  UNIQUE(blocker_id, blocked_id),
  CHECK(blocker_id != blocked_id) -- On ne peut pas se bloquer soi-même
);

-- =====================================================
-- 5. FONCTIONS POUR GÉRER LES DEMANDES DE SUIVI
-- =====================================================

-- Fonction pour créer une demande de suivi
CREATE OR REPLACE FUNCTION create_follow_request(
  p_requester_id uuid,
  p_target_id uuid,
  p_message text DEFAULT NULL
) RETURNS uuid AS $$
DECLARE
  request_id uuid;
  target_settings record;
BEGIN
  -- Vérifier que l'utilisateur ne se suit pas lui-même
  IF p_requester_id = p_target_id THEN
    RAISE EXCEPTION 'Cannot follow yourself';
  END IF;

  -- Vérifier s'il y a un blocage
  IF EXISTS (
    SELECT 1 FROM user_blocks 
    WHERE (blocker_id = p_target_id AND blocked_id = p_requester_id)
       OR (blocker_id = p_requester_id AND blocked_id = p_target_id)
  ) THEN
    RAISE EXCEPTION 'User is blocked';
  END IF;

  -- Récupérer les paramètres de confidentialité de la cible
  SELECT * INTO target_settings 
  FROM privacy_settings 
  WHERE user_id = p_target_id;

  -- Si pas de paramètres, créer des paramètres par défaut
  IF target_settings IS NULL THEN
    INSERT INTO privacy_settings (user_id) VALUES (p_target_id);
    SELECT * INTO target_settings FROM privacy_settings WHERE user_id = p_target_id;
  END IF;

  -- Si l'approbation n'est pas requise, suivre directement
  IF NOT target_settings.require_follow_approval THEN
    INSERT INTO followers (follower_id, following_id, follow_type, status)
    VALUES (p_requester_id, p_target_id, 'follow', 'active')
    ON CONFLICT (follower_id, following_id) DO NOTHING;
    
    RETURN NULL; -- Pas de demande créée, suivi direct
  END IF;

  -- Créer la demande de suivi
  INSERT INTO follow_requests (requester_id, target_id, message, status)
  VALUES (p_requester_id, p_target_id, p_message, 'pending')
  ON CONFLICT (requester_id, target_id, status) DO NOTHING
  RETURNING id INTO request_id;

  RETURN request_id;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour répondre à une demande de suivi
CREATE OR REPLACE FUNCTION respond_to_follow_request(
  p_request_id uuid,
  p_response text, -- 'accepted' ou 'rejected'
  p_target_id uuid -- Pour vérifier que c'est bien la bonne personne qui répond
) RETURNS boolean AS $$
DECLARE
  request_record record;
BEGIN
  -- Récupérer la demande
  SELECT * INTO request_record 
  FROM follow_requests 
  WHERE id = p_request_id AND target_id = p_target_id AND status = 'pending';

  IF request_record IS NULL THEN
    RAISE EXCEPTION 'Follow request not found or not pending';
  END IF;

  -- Mettre à jour le statut de la demande
  UPDATE follow_requests 
  SET status = p_response, 
      responded_at = now(), 
      updated_at = now()
  WHERE id = p_request_id;

  -- Si accepté, créer la relation de suivi
  IF p_response = 'accepted' THEN
    INSERT INTO followers (follower_id, following_id, follow_type, status)
    VALUES (request_record.requester_id, request_record.target_id, 'follow', 'active')
    ON CONFLICT (follower_id, following_id) DO NOTHING;
  END IF;

  RETURN true;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CRÉATION DES INDEX POUR OPTIMISER LES PERFORMANCES
-- =====================================================

-- Index pour follow_requests
CREATE INDEX IF NOT EXISTS idx_follow_requests_requester_id ON follow_requests(requester_id);
CREATE INDEX IF NOT EXISTS idx_follow_requests_target_id ON follow_requests(target_id);
CREATE INDEX IF NOT EXISTS idx_follow_requests_status ON follow_requests(status);
CREATE INDEX IF NOT EXISTS idx_follow_requests_created_at ON follow_requests(created_at);

-- Index pour privacy_settings
CREATE INDEX IF NOT EXISTS idx_privacy_settings_user_id ON privacy_settings(user_id);

-- Index pour user_blocks
CREATE INDEX IF NOT EXISTS idx_user_blocks_blocker_id ON user_blocks(blocker_id);
CREATE INDEX IF NOT EXISTS idx_user_blocks_blocked_id ON user_blocks(blocked_id);

-- Index pour followers (améliorer les existants)
CREATE INDEX IF NOT EXISTS idx_followers_status ON followers(status);
CREATE INDEX IF NOT EXISTS idx_followers_follow_type ON followers(follow_type);
CREATE INDEX IF NOT EXISTS idx_followers_created_at ON followers(created_at);

-- =====================================================
-- TRIGGERS POUR MAINTENIR LES COMPTEURS
-- =====================================================

-- Fonction pour mettre à jour les compteurs de followers
CREATE OR REPLACE FUNCTION update_follower_counts() RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Augmenter le compteur de followers pour la cible
    UPDATE profiles 
    SET followers_count = COALESCE(followers_count, 0) + 1,
        updated_at = now()
    WHERE id = NEW.following_id;
    
    -- Augmenter le compteur de following pour le suiveur
    UPDATE profiles 
    SET following_count = COALESCE(following_count, 0) + 1,
        updated_at = now()
    WHERE id = NEW.follower_id;
    
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Diminuer le compteur de followers pour la cible
    UPDATE profiles 
    SET followers_count = GREATEST(COALESCE(followers_count, 0) - 1, 0),
        updated_at = now()
    WHERE id = OLD.following_id;
    
    -- Diminuer le compteur de following pour le suiveur
    UPDATE profiles 
    SET following_count = GREATEST(COALESCE(following_count, 0) - 1, 0),
        updated_at = now()
    WHERE id = OLD.follower_id;
    
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Créer le trigger
DROP TRIGGER IF EXISTS trigger_update_follower_counts ON followers;
CREATE TRIGGER trigger_update_follower_counts
  AFTER INSERT OR DELETE ON followers
  FOR EACH ROW
  EXECUTE FUNCTION update_follower_counts();

-- =====================================================
-- DONNÉES DE TEST POUR LES PARAMÈTRES DE CONFIDENTIALITÉ
-- =====================================================

-- Paramètres par défaut pour les utilisateurs existants
INSERT INTO privacy_settings (user_id, require_follow_approval, allow_follow_requests)
SELECT id, false, true
FROM profiles
WHERE NOT EXISTS (
  SELECT 1 FROM privacy_settings WHERE user_id = profiles.id
);

-- Paramètres spéciaux pour certains utilisateurs (exemple)
-- Dexima nécessite une approbation pour les suivis
UPDATE privacy_settings 
SET require_follow_approval = true,
    auto_accept_verified_users = true,
    notify_follow_requests = true
WHERE user_id IN (
  SELECT id FROM profiles WHERE username = 'Dexima'
);
