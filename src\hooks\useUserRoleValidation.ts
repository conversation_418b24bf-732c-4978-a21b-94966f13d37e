import { useEffect } from 'react';
import { IPost, UserRole } from '../types/index';

/**
 * Hook pour valider et surveiller les rôles utilisateur dans les posts
 */
export const useUserRoleValidation = (posts: IPost[]) => {
  useEffect(() => {
    if (posts.length > 0) {
      const postsWithoutRole = posts.filter(post => 
        !post.authorRole || 
        !Object.values(UserRole).includes(post.authorRole)
      );
      
      if (postsWithoutRole.length > 0 && process.env.NODE_ENV === 'development') {
        console.warn('=== POSTS SANS RÔLE VALIDE ===');
        postsWithoutRole.forEach(post => {
          console.warn(`Post ID: ${post.id}`, {
            authorRole: post.authorRole,
            username: post.username,
            businessName: post.businessName,
            userId: post.userId
          });
        });
        console.warn('===============================');
      }
    }
  }, [posts]);

  // Statistiques sur les rôles
  const roleStats = posts.reduce((acc, post) => {
    const role = post.authorRole || UserRole.STANDARD;
    
    acc.total++;
    acc.byRole[role] = (acc.byRole[role] || 0) + 1;
    
    if (!post.authorRole) {
      acc.missingRole++;
    }
    
    return acc;
  }, {
    total: 0,
    missingRole: 0,
    byRole: {} as Record<UserRole, number>
  });

  return {
    totalPosts: roleStats.total,
    missingRoles: roleStats.missingRole,
    businessPosts: roleStats.byRole[UserRole.BUSINESS] || 0,
    memberPosts: roleStats.byRole[UserRole.STANDARD] || 0,
    adminPosts: roleStats.byRole[UserRole.ADMIN] || 0,
    roleDistribution: roleStats.byRole
  };
};
