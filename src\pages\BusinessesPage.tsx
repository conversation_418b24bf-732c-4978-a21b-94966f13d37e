import React, { useMemo } from 'react';
import { usePosts } from '../context/PostsContext';
import { IPost } from '../types';
import BusinessProductSummaryCard from '../components/businesses/BusinessProductSummaryCard';
import { Building2 } from 'lucide-react'; // Icon for businesses

interface BusinessWithPosts extends IPost {
  // IPost already contains businessName, we'll group by it
}

const BusinessesPage: React.FC = () => {
  const { posts, loading } = usePosts();

  const businessesData = useMemo(() => {
    if (loading || posts.length === 0) {
      return {};
    }

    const businesses: { [businessName: string]: IPost[] } = {};
    posts.forEach(post => {
      if (!post.businessName) return; // Skip posts without a business name
      if (!businesses[post.businessName]) {
        businesses[post.businessName] = [];
      }
      businesses[post.businessName].push(post);
    });
    return businesses;
  }, [posts, loading]);

  if (loading) {
    return <div className="text-center py-10">Chargement des entreprises...</div>;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8 flex items-center">
        <Building2 size={32} className="mr-3 text-indigo-600" />
        <h1 className="text-3xl font-bold text-gray-900">Annuaire des Entreprises</h1>
      </div>
      <p className="mt-2 mb-8 text-gray-600">Découvrez les entreprises présentes sur la plateforme et les produits/services qu'elles proposent.</p>

      {Object.keys(businessesData).length === 0 && !loading && (
         <div className="text-center py-10 bg-white p-6 rounded-lg shadow">
          <Building2 size={48} className="mx-auto text-gray-400 mb-4" />
          <p className="text-xl text-gray-700 font-semibold">Aucune entreprise à afficher pour le moment.</p>
        </div>
      )}

      {Object.entries(businessesData).map(([businessName, businessPosts]) => (
        <section key={businessName} className="mb-12 bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-2xl font-semibold text-indigo-700 mb-6 border-b border-gray-200 pb-3">
            {businessPosts[0]?.businessName || businessName}
          </h2>
          {businessPosts.length === 0 ? (
            <p className="text-gray-500">Aucun produit/service listé pour cette entreprise pour le moment.</p>
          ) : (
            <div className="space-y-4">
              {businessPosts.map(post => (
                <BusinessProductSummaryCard key={post.id} post={post} allPosts={posts} />
              ))}
            </div>
          )}
        </section>
      ))}
    </div>
  );
};

export default BusinessesPage;
