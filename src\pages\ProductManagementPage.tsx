import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { UserRole } from '../types';
import { Navigate } from 'react-router-dom';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import Button from '../components/ui/Button';
import { BusinessProduct, getBusinessProducts, getBusinessStats, PRODUCT_CATEGORIES } from '../data/realProducts';
import {
  Plus,
  Package,
  Edit3,
  Trash2,
  Eye,
  ShoppingCart,
  ToggleLeft,
  ToggleRight,
  Search,
  Filter,
  Grid3X3,
  List,
  Star,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign
} from 'lucide-react';



const ProductManagementPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [products, setProducts] = useState<BusinessProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'draft' | 'pending' | 'rejected'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showAddModal, setShowAddModal] = useState(false);

  // Vérifier que l'utilisateur est une entreprise
  if (!currentUser || currentUser.role !== UserRole.BUSINESS) {
    return <Navigate to="/profile" replace />;
  }

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);

      // Utiliser les données réelles des produits pour cette entreprise
      const businessProducts = getBusinessProducts(currentUser.id);
      setProducts(businessProducts);
    } catch (error) {
      console.error('Erreur lors du chargement des produits:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleMarketplace = async (productId: string) => {
    setProducts(prev => prev.map(product => {
      if (product.id === productId) {
        const newStatus = !product.isOnMarketplace;
        return {
          ...product,
          isOnMarketplace: newStatus,
          marketplaceStatus: newStatus ? 'pending' : 'draft'
        };
      }
      return product;
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-700 bg-green-100';
      case 'pending':
        return 'text-yellow-700 bg-yellow-100';
      case 'rejected':
        return 'text-red-700 bg-red-100';
      case 'draft':
        return 'text-gray-700 bg-gray-100';
      default:
        return 'text-gray-700 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle size={14} />;
      case 'pending':
        return <Clock size={14} />;
      case 'rejected':
        return <AlertCircle size={14} />;
      case 'draft':
        return <Edit3 size={14} />;
      default:
        return <Package size={14} />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'En vente';
      case 'pending':
        return 'En attente';
      case 'rejected':
        return 'Refusé';
      case 'draft':
        return 'Brouillon';
      default:
        return status;
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.category.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterStatus === 'all' || product.marketplaceStatus === filterStatus;
    return matchesSearch && matchesFilter;
  });

  // Utiliser les statistiques réelles
  const stats = getBusinessStats(currentUser.id);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Package className="text-blue-600 mr-3" size={32} />
              Gestion des Produits
            </h1>
            <p className="text-lg text-gray-600 mt-2">
              Gérez vos produits et leur mise en vente sur le marketplace
            </p>
          </div>
          <Button
            variant="primary"
            leftIcon={<Plus size={20} />}
            onClick={() => setShowAddModal(true)}
            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
          >
            Ajouter un produit
          </Button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
              <div className="text-sm text-gray-600">Total produits</div>
            </CardBody>
          </Card>
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.active}</div>
              <div className="text-sm text-gray-600">En vente</div>
            </CardBody>
          </Card>
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.totalSales}</div>
              <div className="text-sm text-gray-600">Ventes totales</div>
            </CardBody>
          </Card>
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-orange-600">{formatPrice(stats.totalRevenue)} F</div>
              <div className="text-sm text-gray-600">Chiffre d'affaires</div>
            </CardBody>
          </Card>
        </div>
      </div>

      {/* Filters and Search */}
      <Card className="mb-6">
        <CardBody>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Rechercher un produit..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="flex items-center space-x-4">
              {/* Status Filter */}
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Tous les statuts</option>
                <option value="active">En vente</option>
                <option value="pending">En attente</option>
                <option value="draft">Brouillon</option>
                <option value="rejected">Refusé</option>
              </select>

              {/* View Mode */}
              <div className="flex items-center border border-gray-300 rounded-lg">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'text-gray-600 hover:bg-gray-100'} rounded-l-lg transition-colors`}
                >
                  <Grid3X3 size={18} />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'text-gray-600 hover:bg-gray-100'} rounded-r-lg transition-colors`}
                >
                  <List size={18} />
                </button>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Products Grid/List */}
      {filteredProducts.length === 0 ? (
        <Card>
          <CardBody className="text-center py-12">
            <Package className="mx-auto text-gray-400 mb-4" size={48} />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery ? 'Aucun produit trouvé' : 'Aucun produit'}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchQuery
                ? 'Essayez avec d\'autres mots-clés'
                : 'Commencez par ajouter votre premier produit'
              }
            </p>
            {!searchQuery && (
              <Button
                variant="primary"
                leftIcon={<Plus size={20} />}
                onClick={() => setShowAddModal(true)}
              >
                Ajouter un produit
              </Button>
            )}
          </CardBody>
        </Card>
      ) : (
        <div className={viewMode === 'grid'
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
          : 'space-y-4'
        }>
          {filteredProducts.map(product => (
            <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              {viewMode === 'grid' ? (
                <>
                  {/* Grid View */}
                  <div className="relative">
                    <img
                      src={product.images[0]}
                      alt={product.name}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-2 left-2">
                      <span className={`flex items-center text-xs font-semibold px-2 py-1 rounded-full ${getStatusColor(product.marketplaceStatus)}`}>
                        {getStatusIcon(product.marketplaceStatus)}
                        <span className="ml-1">{getStatusText(product.marketplaceStatus)}</span>
                      </span>
                    </div>
                    <div className="absolute top-2 right-2">
                      <button
                        onClick={() => toggleMarketplace(product.id)}
                        className={`p-2 rounded-full transition-colors ${
                          product.isOnMarketplace
                            ? 'bg-green-500 text-white'
                            : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                        }`}
                        title={product.isOnMarketplace ? 'Retirer du marketplace' : 'Mettre sur le marketplace'}
                      >
                        <ShoppingCart size={16} />
                      </button>
                    </div>
                  </div>
                  <CardBody className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-1 truncate">{product.name}</h3>
                    <p className="text-sm text-gray-600 mb-2">{product.category}</p>
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-lg font-bold text-blue-600">
                        {formatPrice(product.price)} F CFA
                      </span>
                      <span className="text-sm text-gray-500">Stock: {product.stock}</span>
                    </div>

                    {product.isOnMarketplace && (
                      <div className="grid grid-cols-3 gap-2 text-xs text-gray-600 mb-3">
                        <div className="text-center">
                          <div className="font-semibold">{product.views}</div>
                          <div>Vues</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold">{product.sales}</div>
                          <div>Ventes</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold">{formatPrice(product.revenue)}</div>
                          <div>Revenus</div>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <button className="p-1 text-gray-600 hover:text-blue-600 transition-colors">
                          <Eye size={16} />
                        </button>
                        <button className="p-1 text-gray-600 hover:text-blue-600 transition-colors">
                          <Edit3 size={16} />
                        </button>
                        <button className="p-1 text-gray-600 hover:text-red-600 transition-colors">
                          <Trash2 size={16} />
                        </button>
                      </div>
                      <span className="text-xs text-gray-500">
                        {formatDate(product.lastUpdated)}
                      </span>
                    </div>
                  </CardBody>
                </>
              ) : (
                /* List View */
                <CardBody className="p-4">
                  <div className="flex items-center space-x-4">
                    <img
                      src={product.images[0]}
                      alt={product.name}
                      className="w-16 h-16 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="font-semibold text-gray-900">{product.name}</h3>
                        <div className="flex items-center space-x-2">
                          <span className={`flex items-center text-xs font-semibold px-2 py-1 rounded-full ${getStatusColor(product.marketplaceStatus)}`}>
                            {getStatusIcon(product.marketplaceStatus)}
                            <span className="ml-1">{getStatusText(product.marketplaceStatus)}</span>
                          </span>
                          <button
                            onClick={() => toggleMarketplace(product.id)}
                            className={`p-1 rounded transition-colors ${
                              product.isOnMarketplace
                                ? 'bg-green-500 text-white'
                                : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                            }`}
                          >
                            <ShoppingCart size={14} />
                          </button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>{product.category}</span>
                        <span className="font-semibold text-blue-600">{formatPrice(product.price)} F CFA</span>
                      </div>
                      {product.isOnMarketplace && (
                        <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                          <span>{product.views} vues</span>
                          <span>{product.sales} ventes</span>
                          <span>{formatPrice(product.revenue)} F revenus</span>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-gray-600 hover:text-blue-600 transition-colors">
                        <Eye size={16} />
                      </button>
                      <button className="p-2 text-gray-600 hover:text-blue-600 transition-colors">
                        <Edit3 size={16} />
                      </button>
                      <button className="p-2 text-gray-600 hover:text-red-600 transition-colors">
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                </CardBody>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Add Product Modal */}
      {showAddModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setShowAddModal(false)} />
            <div className="relative bg-white rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">Ajouter un nouveau produit</h2>
                  <button
                    onClick={() => setShowAddModal(false)}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    ✕
                  </button>
                </div>

                <form className="space-y-6">
                  {/* Product Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nom du produit *
                    </label>
                    <input
                      type="text"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Ex: Huile de beauté naturelle..."
                    />
                  </div>

                  {/* Category */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Catégorie *
                    </label>
                    <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                      <option value="">Sélectionner une catégorie</option>
                      {PRODUCT_CATEGORIES.map(category => (
                        <option key={category} value={category.toLowerCase()}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description *
                    </label>
                    <textarea
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Décrivez votre produit en détail..."
                    />
                  </div>

                  {/* Price and Stock */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Prix (F CFA) *
                      </label>
                      <input
                        type="number"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="15000"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Stock initial
                      </label>
                      <input
                        type="number"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="50"
                      />
                    </div>
                  </div>

                  {/* Images */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Images du produit
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <div className="text-gray-500">
                        <Package size={48} className="mx-auto mb-2" />
                        <p>Glissez-déposez vos images ici ou</p>
                        <button type="button" className="text-blue-600 hover:text-blue-700 font-medium">
                          parcourir vos fichiers
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Options */}
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="negotiable"
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="negotiable" className="ml-2 text-sm text-gray-700">
                        Prix négociable
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="marketplace"
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="marketplace" className="ml-2 text-sm text-gray-700">
                        Mettre directement en vente sur le marketplace
                      </label>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <Button
                      variant="outline"
                      onClick={() => setShowAddModal(false)}
                    >
                      Annuler
                    </Button>
                    <Button
                      variant="primary"
                      leftIcon={<Plus size={18} />}
                    >
                      Ajouter le produit
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductManagementPage;
