-- DIAGNOSTIC COMPLET POUR L'INSCRIPTION
-- Exécutez ce script si l'inscription ne fonctionne toujours pas

-- =====================================================
-- 1. VÉRIFIER LES FONCTIONS NÉCESSAIRES
-- =====================================================

SELECT 
    '=== DIAGNOSTIC DES FONCTIONS ===' as section,
    '' as details
UNION ALL
SELECT 
    'clean_interests',
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'clean_interests')
        THEN '✅ Existe'
        ELSE '❌ Manquante'
    END
UNION ALL
SELECT 
    'handle_new_user',
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'handle_new_user')
        THEN '✅ Existe'
        ELSE '❌ Manquante'
    END
UNION ALL
SELECT 
    'trigger_clean_interests',
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'trigger_clean_interests')
        THEN '✅ Existe'
        ELSE '❌ Manquante'
    END;

-- =====================================================
-- 2. VÉRIFIER LES TRIGGERS
-- =====================================================

SELECT 
    '=== DIAGNOSTIC DES TRIGGERS ===' as section,
    '' as details
UNION ALL
SELECT 
    'clean_interests_trigger',
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'clean_interests_trigger')
        THEN '✅ Existe'
        ELSE '❌ Manquant'
    END
UNION ALL
SELECT 
    'on_auth_user_created',
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'on_auth_user_created')
        THEN '✅ Existe'
        ELSE '❌ Manquant'
    END;

-- =====================================================
-- 3. VÉRIFIER LA STRUCTURE DE LA TABLE PROFILES
-- =====================================================

SELECT 
    '=== STRUCTURE TABLE PROFILES ===' as section,
    '' as details
UNION ALL
SELECT 
    'Table profiles existe',
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles')
        THEN '✅ Oui'
        ELSE '❌ Non'
    END
UNION ALL
SELECT 
    'Colonnes essentielles',
    CASE 
        WHEN (
            SELECT COUNT(*) FROM information_schema.columns 
            WHERE table_name = 'profiles' 
            AND column_name IN ('id', 'username', 'email', 'role', 'created_at', 'updated_at')
        ) >= 6 THEN '✅ Toutes présentes'
        ELSE '❌ Manquantes'
    END;

-- =====================================================
-- 4. LISTER LES COLONNES DE PROFILES
-- =====================================================

SELECT 
    '=== COLONNES DE PROFILES ===' as section,
    column_name as details
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- =====================================================
-- 5. VÉRIFIER LA TABLE BUSINESS_PROFILES
-- =====================================================

SELECT 
    '=== TABLE BUSINESS_PROFILES ===' as section,
    '' as details
UNION ALL
SELECT 
    'Table business_profiles existe',
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'business_profiles')
        THEN '✅ Oui'
        ELSE '❌ Non'
    END;

-- =====================================================
-- 6. TESTER LA FONCTION CLEAN_INTERESTS
-- =====================================================

DO $$
DECLARE
    test_result TEXT;
BEGIN
    BEGIN
        SELECT clean_interests('  test  ,   data   ') INTO test_result;
        RAISE NOTICE '✅ Test clean_interests réussi: %', test_result;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '❌ Erreur test clean_interests: %', SQLERRM;
    END;
END $$;

-- =====================================================
-- 7. VÉRIFIER LES PERMISSIONS
-- =====================================================

SELECT 
    '=== PERMISSIONS ===' as section,
    '' as details
UNION ALL
SELECT 
    'Permissions profiles INSERT',
    CASE 
        WHEN has_table_privilege('authenticated', 'profiles', 'INSERT')
        THEN '✅ Autorisé'
        ELSE '❌ Refusé'
    END
UNION ALL
SELECT 
    'Permissions profiles SELECT',
    CASE 
        WHEN has_table_privilege('authenticated', 'profiles', 'SELECT')
        THEN '✅ Autorisé'
        ELSE '❌ Refusé'
    END;

-- =====================================================
-- 8. VÉRIFIER LES POLITIQUES RLS
-- =====================================================

SELECT 
    '=== POLITIQUES RLS ===' as section,
    tablename as details
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'business_profiles')
AND rowsecurity = true;

-- =====================================================
-- 9. SIMULER LA CRÉATION D'UN PROFIL
-- =====================================================

DO $$
DECLARE
    test_user_id uuid := gen_random_uuid();
    test_username text := 'test_user_' || extract(epoch from now())::text;
    test_email text := 'test' || extract(epoch from now())::text || '@example.com';
BEGIN
    BEGIN
        -- Tenter d'insérer un profil test
        INSERT INTO profiles (id, username, email, role, created_at, updated_at)
        VALUES (test_user_id, test_username, test_email, 'standard', now(), now());
        
        RAISE NOTICE '✅ Test insertion profil: SUCCÈS';
        
        -- Nettoyer le test
        DELETE FROM profiles WHERE id = test_user_id;
        
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '❌ Test insertion profil: ÉCHEC - %', SQLERRM;
    END;
END $$;

-- =====================================================
-- 10. RECOMMANDATIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== RECOMMANDATIONS ===';
    RAISE NOTICE '';
    
    -- Vérifier les fonctions
    IF NOT EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'clean_interests') THEN
        RAISE NOTICE '❌ PROBLÈME: Fonction clean_interests manquante';
        RAISE NOTICE '   SOLUTION: Relancez le script de correction';
    END IF;
    
    -- Vérifier les triggers
    IF NOT EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'on_auth_user_created') THEN
        RAISE NOTICE '❌ PROBLÈME: Trigger on_auth_user_created manquant';
        RAISE NOTICE '   SOLUTION: Relancez le script de correction';
    END IF;
    
    -- Vérifier la table profiles
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles') THEN
        RAISE NOTICE '❌ PROBLÈME: Table profiles manquante';
        RAISE NOTICE '   SOLUTION: Appliquez les migrations de base';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Si tous les tests sont ✅, le problème peut venir de:';
    RAISE NOTICE '1. Contraintes de validation sur les colonnes';
    RAISE NOTICE '2. Politiques RLS trop restrictives';
    RAISE NOTICE '3. Données invalides envoyées depuis le frontend';
    RAISE NOTICE '';
END $$;
