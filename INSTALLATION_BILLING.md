# 🚀 Installation du Système de Facturation Publicitaire

## ⚠️ Erreur actuelle

Vous voyez cette erreur car les tables de facturation ne sont pas encore créées dans votre base de données Supabase :

```
Failed to load resource: the server responded with a status of 404
```

## 🔧 Solution rapide

### Étape 1 : Accéder à Supabase
1. Allez sur [supabase.com](https://supabase.com)
2. Connectez-vous à votre projet
3. C<PERSON>z sur **"SQL Editor"** dans le menu de gauche

### Étape 2 : Exécuter le script SQL
1. Copiez le contenu du fichier `scripts/setup-billing-tables.sql`
2. Collez-le dans l'éditeur SQL de Supabase
3. C<PERSON>z sur **"Run"** pour exécuter le script

### Étape 3 : Vérifier l'installation
1. Allez dans **"Table Editor"**
2. Vérifiez que ces tables ont été créées :
   - `ad_wallets`
   - `ad_transactions`
   - `payment_methods`
   - `billing_alerts`
   - `ad_invoices`

## 📋 Script SQL complet

```sql
-- Table des portefeuilles publicitaires
CREATE TABLE IF NOT EXISTS ad_wallets (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  balance numeric DEFAULT 0 CHECK (balance >= 0),
  total_spent numeric DEFAULT 0 CHECK (total_spent >= 0),
  total_recharged numeric DEFAULT 0 CHECK (total_recharged >= 0),
  currency text NOT NULL DEFAULT 'XOF',
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'blocked')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(business_id)
);

-- Table des transactions publicitaires
CREATE TABLE IF NOT EXISTS ad_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  wallet_id uuid NOT NULL REFERENCES ad_wallets(id) ON DELETE CASCADE,
  campaign_id text,
  type text NOT NULL CHECK (type IN ('recharge', 'spend', 'refund', 'bonus')),
  amount numeric NOT NULL CHECK (amount > 0),
  description text NOT NULL,
  payment_method text,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Table des méthodes de paiement
CREATE TABLE IF NOT EXISTS payment_methods (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('mobile_money', 'bank_card', 'bank_transfer', 'crypto')),
  provider text NOT NULL,
  account_number text NOT NULL,
  account_name text NOT NULL,
  is_default boolean DEFAULT false,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Table des alertes de facturation
CREATE TABLE IF NOT EXISTS billing_alerts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('low_balance', 'campaign_paused', 'payment_failed', 'budget_exceeded')),
  message text NOT NULL,
  threshold numeric,
  is_read boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Table des factures publicitaires
CREATE TABLE IF NOT EXISTS ad_invoices (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  campaign_id text,
  invoice_number text UNIQUE NOT NULL,
  total_amount numeric NOT NULL CHECK (total_amount >= 0),
  currency text NOT NULL DEFAULT 'XOF',
  status text NOT NULL DEFAULT 'generated' CHECK (status IN ('generated', 'sent', 'paid', 'cancelled')),
  due_date timestamptz,
  paid_at timestamptz,
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Activation RLS
ALTER TABLE ad_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_invoices ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité (exemple pour ad_wallets)
CREATE POLICY "Les entreprises peuvent voir leur propre wallet"
  ON ad_wallets FOR SELECT USING (business_id = auth.uid());

CREATE POLICY "Les entreprises peuvent mettre à jour leur propre wallet"
  ON ad_wallets FOR UPDATE USING (business_id = auth.uid());

CREATE POLICY "Les entreprises peuvent créer leur propre wallet"
  ON ad_wallets FOR INSERT WITH CHECK (business_id = auth.uid());

-- Index pour les performances
CREATE INDEX idx_ad_wallets_business_id ON ad_wallets(business_id);
CREATE INDEX idx_ad_transactions_wallet_id ON ad_transactions(wallet_id);
CREATE INDEX idx_payment_methods_business_id ON payment_methods(business_id);
CREATE INDEX idx_billing_alerts_business_id ON billing_alerts(business_id);
```

## ✅ Après l'installation

Une fois les tables créées :

1. **Rechargez la page** - Le mode démonstration se désactivera automatiquement
2. **Testez le système** - Cliquez sur le bouton "Portefeuille" dans la barre d'outils
3. **Créez une campagne** - Le système vérifiera automatiquement le solde

## 🎯 Mode démonstration

En attendant l'installation des tables, le système fonctionne en **mode démonstration** avec :
- ✅ Portefeuille fictif avec 50 000 F CFA
- ✅ Méthode de paiement Orange Money
- ✅ Toutes les fonctionnalités disponibles
- ✅ Transactions simulées

## 🆘 Besoin d'aide ?

Si vous rencontrez des problèmes :

1. **Vérifiez les permissions** - Assurez-vous d'être admin du projet Supabase
2. **Consultez les logs** - Regardez la console du navigateur pour plus de détails
3. **Recommencez** - Supprimez les tables partielles et relancez le script complet

## 🔄 Migration automatique (Optionnel)

Pour automatiser l'installation, vous pouvez aussi :

1. Créer une migration Supabase :
```bash
supabase migration new billing_system
```

2. Copier le contenu du script dans le fichier de migration

3. Appliquer la migration :
```bash
supabase db push
```

---

**Une fois installé, vous aurez accès à un système de facturation publicitaire complet et professionnel ! 🚀**
