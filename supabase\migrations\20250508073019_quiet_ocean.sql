/*
  # Authentication Schema

  1. New Tables
    - `profiles`
      - `id` (uuid, primary key, references auth.users)
      - `username` (text, unique)
      - `email` (text, unique)
      - `profile_picture` (text)
      - `role` (text)
      - `status` (text)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

    - `business_profiles`
      - `id` (uuid, primary key, references profiles)
      - `business_name` (text, unique)
      - `business_status` (text)
      - `business_description` (text)
      - `wallet` (numeric)
      - `sales_count` (integer)

    - `follows`
      - `follower_id` (uuid, references profiles)
      - `following_id` (uuid, references profiles)
      - `created_at` (timestamptz)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
*/

-- Profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id),
  username text UNIQUE NOT NULL,
  email text UNIQUE NOT NULL,
  profile_picture text,
  role text NOT NULL DEFAULT 'standard',
  status text NOT NULL DEFAULT 'regular',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  CONSTRAINT valid_role CHECK (role IN ('standard', 'business', 'admin')),
  CONSTRAINT valid_status CHECK (status IN ('regular', 'influencer', 'discoverer', 'leader'))
);

ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Public profiles are viewable by everyone"
  ON profiles
  FOR SELECT
  USING (true);

CREATE POLICY "Users can update their own profile"
  ON profiles
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Business profiles table
CREATE TABLE IF NOT EXISTS business_profiles (
  id uuid PRIMARY KEY REFERENCES profiles(id),
  business_name text UNIQUE NOT NULL,
  business_status text NOT NULL DEFAULT 'pending',
  business_description text,
  wallet numeric DEFAULT 0 CHECK (wallet >= 0),
  sales_count integer DEFAULT 0 CHECK (sales_count >= 0),
  CONSTRAINT valid_business_status CHECK (business_status IN ('pending', 'active', 'inactive'))
);

ALTER TABLE business_profiles ENABLE ROW LEVEL SECURITY;

-- Business profiles policies
CREATE POLICY "Public business profiles are viewable by everyone"
  ON business_profiles
  FOR SELECT
  USING (true);

CREATE POLICY "Businesses can update their own profile"
  ON business_profiles
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Follows table
CREATE TABLE IF NOT EXISTS follows (
  follower_id uuid REFERENCES profiles(id) NOT NULL,
  following_id uuid REFERENCES profiles(id) NOT NULL,
  created_at timestamptz DEFAULT now(),
  PRIMARY KEY (follower_id, following_id),
  CONSTRAINT no_self_follow CHECK (follower_id != following_id)
);

ALTER TABLE follows ENABLE ROW LEVEL SECURITY;

-- Follows policies
CREATE POLICY "Anyone can view follows"
  ON follows
  FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can follow others"
  ON follows
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = follower_id);

CREATE POLICY "Users can unfollow"
  ON follows
  FOR DELETE
  TO authenticated
  USING (auth.uid() = follower_id);

-- Trigger for updating profiles.updated_at
CREATE OR REPLACE FUNCTION update_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_profiles_updated_at();

-- Function to create a profile after signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, username, email, role)
  VALUES (
    new.id,
    new.raw_user_meta_data->>'username',
    new.email,
    COALESCE(new.raw_user_meta_data->>'role', 'standard')
  );

  -- If user is a business, create business profile
  IF (new.raw_user_meta_data->>'role' = 'business') THEN
    INSERT INTO public.business_profiles (
      id,
      business_name,
      business_description
    )
    VALUES (
      new.id,
      new.raw_user_meta_data->>'businessName',
      new.raw_user_meta_data->>'businessDescription'
    );
  END IF;

  RETURN new;
END;
$$ language plpgsql security definer;

-- Trigger to create profile after signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();