import React from 'react';
import {
  Download, Printer, Mail, CheckCircle, CreditCard,
  Calendar, Building, Hash, Clock
} from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import { PaymentReceipt as PaymentReceiptType } from '../../services/paymentService';

interface PaymentReceiptProps {
  receipt: PaymentReceiptType;
  onClose?: () => void;
}

const PaymentReceipt: React.FC<PaymentReceiptProps> = ({ receipt, onClose }) => {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price) + ' F CFA';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // Créer un blob avec le contenu HTML du reçu
    const receiptContent = document.getElementById('receipt-content');
    if (receiptContent) {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Reçu de paiement - ${receipt.invoiceNumber}</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .company-logo { font-size: 24px; font-weight: bold; color: #2563eb; }
                .receipt-title { font-size: 20px; margin: 10px 0; }
                .section { margin: 20px 0; }
                .section-title { font-weight: bold; margin-bottom: 10px; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
                .info-row { display: flex; justify-content: space-between; margin: 5px 0; }
                .total-row { font-weight: bold; font-size: 18px; border-top: 2px solid #000; padding-top: 10px; margin-top: 10px; }
                .status-badge { background: #10b981; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
                @media print { .no-print { display: none; } }
              </style>
            </head>
            <body>
              ${receiptContent.innerHTML}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  const handleEmailReceipt = () => {
    const subject = `Reçu de paiement - ${receipt.invoiceNumber}`;
    const body = `Bonjour,

Veuillez trouver ci-joint votre reçu de paiement pour votre abonnement Customeroom.

Détails du paiement :
- Numéro de facture : ${receipt.invoiceNumber}
- Montant : ${formatPrice(receipt.amount)}
- Date : ${formatDate(receipt.paidAt)}
- Plan : ${receipt.planName}

Merci pour votre confiance !

L'équipe Customeroom`;

    window.location.href = `mailto:${receipt.businessInfo.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Actions en haut (non imprimées) */}
      <div className="no-print mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Reçu de paiement</h1>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={handleEmailReceipt}>
            <Mail size={16} className="mr-2" />
            Envoyer par email
          </Button>
          <Button variant="outline" onClick={handlePrint}>
            <Printer size={16} className="mr-2" />
            Imprimer
          </Button>
          <Button variant="outline" onClick={handleDownload}>
            <Download size={16} className="mr-2" />
            Télécharger PDF
          </Button>
          {onClose && (
            <Button onClick={onClose}>
              Fermer
            </Button>
          )}
        </div>
      </div>

      {/* Contenu du reçu */}
      <Card id="receipt-content">
        <CardBody className="p-8">
          {/* En-tête de l'entreprise */}
          <div className="text-center mb-8">
            <div className="text-3xl font-bold text-blue-600 mb-2">Customeroom</div>
            <div className="text-gray-600">
              Plateforme de gestion d'avis clients<br />
              Abidjan, Côte d'Ivoire<br />
              <EMAIL>
            </div>
          </div>

          {/* Titre du reçu */}
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">REÇU DE PAIEMENT</h2>
            <div className="flex justify-center items-center space-x-2">
              <CheckCircle className="text-green-600" size={20} />
              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                Payé
              </span>
            </div>
          </div>

          {/* Informations principales */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            {/* Informations de facturation */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Building className="mr-2" size={20} />
                Facturé à
              </h3>
              <div className="space-y-2 text-gray-700">
                <div className="font-medium">{receipt.businessInfo.name}</div>
                <div>{receipt.businessInfo.email}</div>
                {receipt.businessInfo.address && <div>{receipt.businessInfo.address}</div>}
                {receipt.businessInfo.taxId && (
                  <div className="text-sm">N° fiscal : {receipt.businessInfo.taxId}</div>
                )}
              </div>
            </div>

            {/* Détails du reçu */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Hash className="mr-2" size={20} />
                Détails du reçu
              </h3>
              <div className="space-y-2 text-gray-700">
                <div className="flex justify-between">
                  <span>Numéro de facture :</span>
                  <span className="font-medium">{receipt.invoiceNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span>Date de paiement :</span>
                  <span className="font-medium">{formatDate(receipt.paidAt)}</span>
                </div>
                <div className="flex justify-between">
                  <span>ID de transaction :</span>
                  <span className="font-mono text-sm">{receipt.transactionId}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Détails du service */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Détails du service</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium text-gray-900">{receipt.planName}</span>
                <span className="font-medium text-gray-900">{formatPrice(receipt.amount)}</span>
              </div>
              <div className="text-sm text-gray-600">
                Abonnement mensuel - Accès complet aux fonctionnalités
              </div>
            </div>
          </div>

          {/* Méthode de paiement */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <CreditCard className="mr-2" size={20} />
              Méthode de paiement
            </h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                {receipt.paymentMethod.type === 'card' && <CreditCard size={20} className="text-blue-600" />}
                {receipt.paymentMethod.type === 'mobile_money' && <CreditCard size={20} className="text-green-600" />}
                <div>
                  <div className="font-medium">{receipt.paymentMethod.provider}</div>
                  <div className="text-sm text-gray-600">
                    {receipt.paymentMethod.type === 'card' && `•••• •••• •••• ${receipt.paymentMethod.last4}`}
                    {receipt.paymentMethod.type === 'mobile_money' && receipt.paymentMethod.phoneNumber}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Récapitulatif des montants */}
          <div className="border-t border-gray-200 pt-6">
            <div className="space-y-3">
              <div className="flex justify-between text-gray-700">
                <span>Sous-total :</span>
                <span>{formatPrice(receipt.amount)}</span>
              </div>
              <div className="flex justify-between text-gray-700">
                <span>TVA (0%) :</span>
                <span>0 F CFA</span>
              </div>
              <div className="flex justify-between text-xl font-bold text-gray-900 border-t border-gray-200 pt-3">
                <span>Total payé :</span>
                <span>{formatPrice(receipt.amount)}</span>
              </div>
            </div>
          </div>

          {/* Informations supplémentaires */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Conditions de service</h4>
                <p>
                  Ce paiement confirme votre abonnement aux services Customeroom pour une durée d'un mois.
                  L'abonnement se renouvelle automatiquement sauf annulation.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Support client</h4>
                <p>
                  Pour toute question concernant cette facture, contactez-nous :<br />
                  Email : <EMAIL><br />
                  Téléphone : +225 XX XX XX XX XX
                </p>
              </div>
            </div>
          </div>

          {/* Pied de page */}
          <div className="mt-8 pt-6 border-t border-gray-200 text-center text-sm text-gray-500">
            <p>Merci pour votre confiance !</p>
            <p className="mt-2">
              Ce reçu a été généré automatiquement le {formatDate(receipt.paidAt)}
            </p>
          </div>
        </CardBody>
      </Card>

      {/* Actions en bas (non imprimées) */}
      <div className="no-print mt-6 text-center">
        <p className="text-gray-600 mb-4">
          Conservez ce reçu pour vos dossiers. Il vous sera utile pour le support client.
        </p>
        {onClose && (
          <Button onClick={onClose} size="lg">
            Retour au tableau de bord
          </Button>
        )}
      </div>
    </div>
  );
};

export default PaymentReceipt;
