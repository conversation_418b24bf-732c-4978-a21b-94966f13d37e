/**
 * Utilitaires de formatage pour l'affichage des données
 * Solution centralisée pour le formatage des montants, nombres, etc.
 */

/**
 * Formate un montant en F CFA avec séparateurs de milliers
 * @param amount - Montant à formater
 * @param showDecimals - Afficher les décimales (défaut: false)
 * @returns Montant formaté en F CFA
 */
export function formatAmount(amount: number | string, showDecimals: boolean = false): string {
  // Convertir en nombre si c'est une string
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  // Vérifier si c'est un nombre valide
  if (isNaN(numAmount)) {
    return '0 F CFA';
  }

  // Formater avec séparateurs de milliers
  const formatter = new Intl.NumberFormat('fr-FR', {
    minimumFractionDigits: showDecimals ? 2 : 0,
    maximumFractionDigits: showDecimals ? 2 : 0
  });

  return `${formatter.format(numAmount)} F CFA`;
}

/**
 * Formate un montant court (avec K, M pour les grands nombres)
 * @param amount - Montant à formater
 * @returns Montant formaté court en F CFA
 */
export function formatAmountShort(amount: number | string): string {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numAmount)) {
    return '0 F CFA';
  }

  if (numAmount >= 1000000) {
    return `${(numAmount / 1000000).toFixed(1)}M F CFA`;
  } else if (numAmount >= 1000) {
    return `${(numAmount / 1000).toFixed(1)}K F CFA`;
  } else {
    return `${numAmount.toLocaleString('fr-FR')} F CFA`;
  }
}

/**
 * Formate un pourcentage
 * @param value - Valeur à formater
 * @param decimals - Nombre de décimales (défaut: 1)
 * @returns Pourcentage formaté
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  if (isNaN(value)) {
    return '0%';
  }
  return `${value.toFixed(decimals)}%`;
}

/**
 * Formate un nombre avec séparateurs de milliers
 * @param value - Nombre à formater
 * @returns Nombre formaté
 */
export function formatNumber(value: number | string): string {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue)) {
    return '0';
  }

  return new Intl.NumberFormat('fr-FR').format(numValue);
}

/**
 * Formate une date en français
 * @param date - Date à formater (string ou Date)
 * @param format - Format de sortie ('short', 'medium', 'long')
 * @returns Date formatée
 */
export function formatDate(date: string | Date, format: 'short' | 'medium' | 'long' = 'medium'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'Date invalide';
  }

  const options: Intl.DateTimeFormatOptions = {
    short: { day: '2-digit', month: '2-digit', year: 'numeric' },
    medium: { day: '2-digit', month: 'long', year: 'numeric' },
    long: { weekday: 'long', day: '2-digit', month: 'long', year: 'numeric' }
  };

  return dateObj.toLocaleDateString('fr-FR', options[format]);
}

/**
 * Formate une durée en français
 * @param minutes - Durée en minutes
 * @returns Durée formatée
 */
export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes} min`;
  } else if (minutes < 1440) {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`;
  } else {
    const days = Math.floor(minutes / 1440);
    const remainingHours = Math.floor((minutes % 1440) / 60);
    return remainingHours > 0 ? `${days}j ${remainingHours}h` : `${days}j`;
  }
}

/**
 * Formate un statut de stock avec couleur
 * @param stock - Quantité en stock
 * @param minStock - Stock minimum
 * @returns Objet avec le texte et la classe CSS
 */
export function formatStockStatus(stock: number, minStock: number = 5): {
  text: string;
  className: string;
  color: string;
} {
  if (stock === 0) {
    return {
      text: 'Rupture de stock',
      className: 'stock-out',
      color: '#dc2626'
    };
  } else if (stock <= minStock) {
    return {
      text: `Stock faible (${stock})`,
      className: 'stock-low',
      color: '#d97706'
    };
  } else {
    return {
      text: `En stock (${stock})`,
      className: 'stock-good',
      color: '#16a34a'
    };
  }
}

/**
 * Formate un statut de produit
 * @param status - Statut du produit
 * @returns Objet avec le texte et la classe CSS
 */
export function formatProductStatus(status: string): {
  text: string;
  className: string;
  color: string;
} {
  const statusMap = {
    active: {
      text: 'Actif',
      className: 'status-active',
      color: '#16a34a'
    },
    inactive: {
      text: 'Inactif',
      className: 'status-inactive',
      color: '#6b7280'
    },
    out_of_stock: {
      text: 'Rupture',
      className: 'status-out-of-stock',
      color: '#dc2626'
    },
    draft: {
      text: 'Brouillon',
      className: 'status-draft',
      color: '#d97706'
    }
  };

  return statusMap[status as keyof typeof statusMap] || statusMap.inactive;
}

/**
 * Calcule et formate une remise
 * @param originalPrice - Prix original
 * @param currentPrice - Prix actuel
 * @returns Objet avec le montant et le pourcentage de remise
 */
export function formatDiscount(originalPrice: number, currentPrice: number): {
  amount: string;
  percentage: string;
  hasDiscount: boolean;
} {
  if (originalPrice <= currentPrice || originalPrice <= 0) {
    return {
      amount: '0 F CFA',
      percentage: '0%',
      hasDiscount: false
    };
  }

  const discountAmount = originalPrice - currentPrice;
  const discountPercentage = (discountAmount / originalPrice) * 100;

  return {
    amount: formatAmount(discountAmount),
    percentage: formatPercentage(discountPercentage, 0),
    hasDiscount: true
  };
}

/**
 * Formate une note/rating avec étoiles
 * @param rating - Note (0-5)
 * @param maxRating - Note maximum (défaut: 5)
 * @returns Objet avec les étoiles et le texte
 */
export function formatRating(rating: number, maxRating: number = 5): {
  stars: string;
  text: string;
  fullStars: number;
  emptyStars: number;
} {
  const clampedRating = Math.max(0, Math.min(rating, maxRating));
  const fullStars = Math.floor(clampedRating);
  const emptyStars = maxRating - fullStars;
  
  return {
    stars: '★'.repeat(fullStars) + '☆'.repeat(emptyStars),
    text: `${clampedRating.toFixed(1)}/${maxRating}`,
    fullStars,
    emptyStars
  };
}

/**
 * Formate une taille de fichier
 * @param bytes - Taille en bytes
 * @returns Taille formatée
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
}

/**
 * Formate un texte en titre (première lettre en majuscule)
 * @param text - Texte à formater
 * @returns Texte formaté
 */
export function formatTitle(text: string): string {
  if (!text) return '';
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
}

/**
 * Tronque un texte avec ellipses
 * @param text - Texte à tronquer
 * @param maxLength - Longueur maximum
 * @returns Texte tronqué
 */
export function truncateText(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Formate un numéro de téléphone ivoirien
 * @param phone - Numéro de téléphone
 * @returns Numéro formaté
 */
export function formatPhone(phone: string): string {
  if (!phone) return '';

  // Supprimer tous les caractères non numériques
  const cleaned = phone.replace(/\D/g, '');

  // Format pour numéros ivoiriens
  if (cleaned.length === 8) {
    // Format local : XX XX XX XX
    return cleaned.replace(/(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4');
  } else if (cleaned.length === 10 && cleaned.startsWith('0')) {
    // Format national : 0X XX XX XX XX
    return cleaned.replace(/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5');
  } else if (cleaned.length === 13 && cleaned.startsWith('225')) {
    // Format international : +225 XX XX XX XX XX
    return `+${cleaned.replace(/(\d{3})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5 $6')}`;
  } else if (cleaned.length === 11 && cleaned.startsWith('225')) {
    // Format international sans + : 225 XX XX XX XX XX
    return `+${cleaned.replace(/(\d{3})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5 $6')}`;
  }

  return phone; // Retourner tel quel si format non reconnu
}
