import React, { useState, useEffect } from 'react';
import {
  X,
  Building2,
  Calendar,
  DollarSign,
  Eye,
  TrendingUp,
  <PERSON>,
  MousePointer,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Download,
  BarChart3,
  Target,
  MapPin,
  Globe
} from 'lucide-react';
import Modal from '../ui/Modal';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';
import { formatAmount } from '../../utils/formatUtils';
import { AdminAdPayment } from '../../services/adminPaymentService';

interface AdPaymentDetailsModalProps {
  adPayment: AdminAdPayment | null;
  isOpen: boolean;
  onClose: () => void;
  onStop: (adPaymentId: string, reason: string) => void;
  onPause: (adPaymentId: string) => void;
  onResume: (adPaymentId: string) => void;
}

interface AdCampaignDetails {
  id: string;
  title: string;
  description: string;
  targetAudience: string;
  ageRange: string;
  location: string;
  interests: string[];
  budget: number;
  bidType: 'cpm' | 'cpc' | 'cpa';
  createdDate: string;
  lastModified: string;
}

interface AdPerformanceMetrics {
  impressions: number;
  clicks: number;
  ctr: number;
  cpm: number;
  cpc: number;
  conversions: number;
  conversionRate: number;
  reach: number;
  frequency: number;
  engagement: number;
  costPerConversion: number;
}

interface AdSchedule {
  startDate: string;
  endDate: string;
  dailyBudget: number;
  totalBudget: number;
  timeSlots: string[];
  daysOfWeek: string[];
  timezone: string;
}

const AdPaymentDetailsModal: React.FC<AdPaymentDetailsModalProps> = ({
  adPayment,
  isOpen,
  onClose,
  onStop,
  onPause,
  onResume
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'campaign' | 'performance' | 'schedule'>('overview');
  const [campaignDetails, setCampaignDetails] = useState<AdCampaignDetails | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<AdPerformanceMetrics | null>(null);
  const [adSchedule, setAdSchedule] = useState<AdSchedule | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (adPayment && isOpen) {
      loadAdPaymentData();
    }
  }, [adPayment, isOpen]);

  const loadAdPaymentData = async () => {
    if (!adPayment) return;
    
    setLoading(true);
    try {
      // Simuler le chargement des données détaillées
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Données mockées pour la démonstration
      setCampaignDetails({
        id: adPayment.adId,
        title: adPayment.adTitle,
        description: `Campagne publicitaire pour ${adPayment.businessName} - ${adPayment.adTitle}`,
        targetAudience: 'Femmes 25-45 ans intéressées par la beauté',
        ageRange: '25-45 ans',
        location: 'Douala, Yaoundé, Cameroun',
        interests: ['Beauté', 'Cosmétiques', 'Soins de la peau', 'Mode'],
        budget: adPayment.amount,
        bidType: 'cpm',
        createdDate: adPayment.paymentDate,
        lastModified: new Date().toISOString()
      });

      // Métriques de performance calculées
      const ctr = (adPayment.clicks / adPayment.impressions) * 100;
      const cpm = (adPayment.amount / adPayment.impressions) * 1000;
      const cpc = adPayment.amount / adPayment.clicks;
      
      setPerformanceMetrics({
        impressions: adPayment.impressions,
        clicks: adPayment.clicks,
        ctr: ctr,
        cpm: cpm,
        cpc: cpc,
        conversions: Math.floor(adPayment.clicks * 0.15), // 15% de conversion simulée
        conversionRate: 15,
        reach: Math.floor(adPayment.impressions * 0.7), // 70% de reach simulé
        frequency: 2.2,
        engagement: Math.floor(adPayment.clicks * 1.5), // Engagement simulé
        costPerConversion: cpc / 0.15
      });

      setAdSchedule({
        startDate: adPayment.startDate,
        endDate: adPayment.endDate,
        dailyBudget: Math.floor(adPayment.amount / adPayment.duration),
        totalBudget: adPayment.amount,
        timeSlots: ['09:00-12:00', '14:00-18:00', '19:00-22:00'],
        daysOfWeek: ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'],
        timezone: 'Africa/Douala'
      });

    } catch (error) {
      console.error('Erreur lors du chargement des données publicitaires:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'completed':
        return 'text-blue-600 bg-blue-100';
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle size={16} />;
      case 'completed':
        return <CheckCircle size={16} />;
      case 'cancelled':
        return <XCircle size={16} />;
      case 'pending':
        return <Clock size={16} />;
      default:
        return <AlertCircle size={16} />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'completed':
        return 'Terminée';
      case 'cancelled':
        return 'Annulée';
      case 'pending':
        return 'En attente';
      default:
        return status;
    }
  };

  const calculateDaysRemaining = () => {
    if (!adPayment) return 0;
    const endDate = new Date(adPayment.endDate);
    const today = new Date();
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const calculateProgress = () => {
    if (!adPayment) return 0;
    const startDate = new Date(adPayment.startDate);
    const endDate = new Date(adPayment.endDate);
    const today = new Date();
    
    const totalDuration = endDate.getTime() - startDate.getTime();
    const elapsed = today.getTime() - startDate.getTime();
    
    return Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
  };

  if (!adPayment) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="p-6">
        {/* En-tête */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <TrendingUp className="text-blue-600" size={24} />
            <div>
              <h2 className="text-xl font-bold text-gray-900">Détails de la publicité</h2>
              <p className="text-gray-600">{adPayment.adTitle}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Onglets */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', label: 'Vue d\'ensemble', icon: <Eye size={16} /> },
              { id: 'campaign', label: 'Campagne', icon: <Target size={16} /> },
              { id: 'performance', label: 'Performance', icon: <BarChart3 size={16} /> },
              { id: 'schedule', label: 'Planification', icon: <Calendar size={16} /> }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon}
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Onglet Vue d'ensemble */}
            {activeTab === 'overview' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Informations principales */}
                <Card>
                  <CardBody className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations de la publicité</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">ID Publicité:</span>
                        <span className="font-medium text-gray-900">{adPayment.adId}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Entreprise:</span>
                        <span className="font-medium text-gray-900">{adPayment.businessName}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Statut:</span>
                        <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(adPayment.status)}`}>
                          {getStatusIcon(adPayment.status)}
                          <span className="ml-1">{getStatusLabel(adPayment.status)}</span>
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Budget total:</span>
                        <span className="font-semibold text-gray-900">{formatAmount(adPayment.amount)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Durée:</span>
                        <span className="font-medium text-gray-900">{adPayment.duration} jours</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Jours restants:</span>
                        <span className="font-medium text-gray-900">{calculateDaysRemaining()} jours</span>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Métriques rapides */}
                <Card>
                  <CardBody className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Métriques rapides</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">{adPayment.impressions.toLocaleString()}</div>
                        <div className="text-sm text-gray-600">Impressions</div>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">{adPayment.clicks}</div>
                        <div className="text-sm text-gray-600">Clics</div>
                      </div>
                      <div className="text-center p-3 bg-purple-50 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">{((adPayment.clicks / adPayment.impressions) * 100).toFixed(2)}%</div>
                        <div className="text-sm text-gray-600">CTR</div>
                      </div>
                      <div className="text-center p-3 bg-orange-50 rounded-lg">
                        <div className="text-2xl font-bold text-orange-600">{formatAmount(adPayment.amount / adPayment.clicks)}</div>
                        <div className="text-sm text-gray-600">CPC</div>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Progression */}
                <Card className="lg:col-span-2">
                  <CardBody className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Progression de la campagne</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between text-sm">
                        <span>Progression</span>
                        <span>{calculateProgress().toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${calculateProgress()}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-sm text-gray-600">
                        <span>Début: {new Date(adPayment.startDate).toLocaleDateString('fr-FR')}</span>
                        <span>Fin: {new Date(adPayment.endDate).toLocaleDateString('fr-FR')}</span>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}

            {/* Onglet Campagne */}
            {activeTab === 'campaign' && campaignDetails && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Détails de la campagne */}
                <Card>
                  <CardBody className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Détails de la campagne</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="text-sm text-gray-600">Titre</div>
                        <div className="font-medium text-gray-900">{campaignDetails.title}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Description</div>
                        <div className="font-medium text-gray-900">{campaignDetails.description}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Type d'enchère</div>
                        <div className="font-medium text-gray-900">
                          {campaignDetails.bidType === 'cpm' ? 'Coût par mille (CPM)' :
                           campaignDetails.bidType === 'cpc' ? 'Coût par clic (CPC)' : 'Coût par acquisition (CPA)'}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Date de création</div>
                        <div className="font-medium text-gray-900">
                          {new Date(campaignDetails.createdDate).toLocaleDateString('fr-FR')}
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Ciblage */}
                <Card>
                  <CardBody className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Ciblage</h3>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <Users className="text-gray-400" size={16} />
                        <div>
                          <div className="text-sm text-gray-600">Audience cible</div>
                          <div className="font-medium text-gray-900">{campaignDetails.targetAudience}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Calendar className="text-gray-400" size={16} />
                        <div>
                          <div className="text-sm text-gray-600">Tranche d'âge</div>
                          <div className="font-medium text-gray-900">{campaignDetails.ageRange}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <MapPin className="text-gray-400" size={16} />
                        <div>
                          <div className="text-sm text-gray-600">Localisation</div>
                          <div className="font-medium text-gray-900">{campaignDetails.location}</div>
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600 mb-2">Centres d'intérêt</div>
                        <div className="flex flex-wrap gap-2">
                          {campaignDetails.interests.map((interest, index) => (
                            <span key={index} className="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                              {interest}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}

            {/* Onglet Performance */}
            {activeTab === 'performance' && performanceMetrics && (
              <div className="space-y-6">
                {/* Métriques principales */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Card>
                    <CardBody className="p-4 text-center">
                      <div className="text-2xl font-bold text-blue-600">{performanceMetrics.impressions.toLocaleString()}</div>
                      <div className="text-sm text-gray-600">Impressions</div>
                    </CardBody>
                  </Card>
                  <Card>
                    <CardBody className="p-4 text-center">
                      <div className="text-2xl font-bold text-green-600">{performanceMetrics.clicks}</div>
                      <div className="text-sm text-gray-600">Clics</div>
                    </CardBody>
                  </Card>
                  <Card>
                    <CardBody className="p-4 text-center">
                      <div className="text-2xl font-bold text-purple-600">{performanceMetrics.ctr.toFixed(2)}%</div>
                      <div className="text-sm text-gray-600">CTR</div>
                    </CardBody>
                  </Card>
                  <Card>
                    <CardBody className="p-4 text-center">
                      <div className="text-2xl font-bold text-orange-600">{formatAmount(performanceMetrics.cpc)}</div>
                      <div className="text-sm text-gray-600">CPC</div>
                    </CardBody>
                  </Card>
                </div>

                {/* Métriques avancées */}
                <Card>
                  <CardBody className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Métriques avancées</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Portée (Reach):</span>
                          <span className="font-medium">{performanceMetrics.reach.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Fréquence:</span>
                          <span className="font-medium">{performanceMetrics.frequency.toFixed(1)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">CPM:</span>
                          <span className="font-medium">{formatAmount(performanceMetrics.cpm)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Engagement:</span>
                          <span className="font-medium">{performanceMetrics.engagement}</span>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Conversions:</span>
                          <span className="font-medium">{performanceMetrics.conversions}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Taux de conversion:</span>
                          <span className="font-medium">{performanceMetrics.conversionRate.toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Coût par conversion:</span>
                          <span className="font-medium">{formatAmount(performanceMetrics.costPerConversion)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">ROI estimé:</span>
                          <span className="font-medium text-green-600">+{(performanceMetrics.conversionRate * 2).toFixed(1)}%</span>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}

            {/* Onglet Planification */}
            {activeTab === 'schedule' && adSchedule && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Calendrier */}
                <Card>
                  <CardBody className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Calendrier</h3>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <Calendar className="text-green-500" size={16} />
                        <div>
                          <div className="text-sm text-gray-600">Date de début</div>
                          <div className="font-medium text-gray-900">
                            {new Date(adSchedule.startDate).toLocaleDateString('fr-FR', {
                              day: '2-digit',
                              month: 'long',
                              year: 'numeric'
                            })}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Calendar className="text-red-500" size={16} />
                        <div>
                          <div className="text-sm text-gray-600">Date de fin</div>
                          <div className="font-medium text-gray-900">
                            {new Date(adSchedule.endDate).toLocaleDateString('fr-FR', {
                              day: '2-digit',
                              month: 'long',
                              year: 'numeric'
                            })}
                          </div>
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600 mb-2">Jours de diffusion</div>
                        <div className="flex flex-wrap gap-2">
                          {adSchedule.daysOfWeek.map((day, index) => (
                            <span key={index} className="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                              {day}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600 mb-2">Créneaux horaires</div>
                        <div className="space-y-1">
                          {adSchedule.timeSlots.map((slot, index) => (
                            <div key={index} className="text-sm font-medium text-gray-900">{slot}</div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Budget */}
                <Card>
                  <CardBody className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Budget</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Budget total:</span>
                        <span className="font-semibold text-gray-900">{formatAmount(adSchedule.totalBudget)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Budget quotidien:</span>
                        <span className="font-medium text-gray-900">{formatAmount(adSchedule.dailyBudget)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Dépensé à ce jour:</span>
                        <span className="font-medium text-gray-900">
                          {formatAmount(Math.floor(adSchedule.totalBudget * (calculateProgress() / 100)))}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Budget restant:</span>
                        <span className="font-medium text-green-600">
                          {formatAmount(adSchedule.totalBudget - Math.floor(adSchedule.totalBudget * (calculateProgress() / 100)))}
                        </span>
                      </div>
                      <div className="pt-4 border-t border-gray-200">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Fuseau horaire:</span>
                          <span className="font-medium text-gray-900">{adSchedule.timezone}</span>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-between items-center mt-6 pt-6 border-t border-gray-200">
          <div className="flex space-x-3">
            <Button
              variant="outline"
              leftIcon={<Download size={16} />}
            >
              Rapport
            </Button>
            <Button
              variant="outline"
              leftIcon={<RefreshCw size={16} />}
              onClick={loadAdPaymentData}
            >
              Actualiser
            </Button>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={onClose}>
              Fermer
            </Button>
            {adPayment.status === 'active' && (
              <>
                <Button
                  variant="outline"
                  leftIcon={<Clock size={16} />}
                  onClick={() => onPause(adPayment.id)}
                  className="text-orange-600 border-orange-300 hover:bg-orange-50"
                >
                  Suspendre
                </Button>
                <Button
                  variant="danger"
                  leftIcon={<XCircle size={16} />}
                  onClick={() => {
                    // Cette fonction sera développée dans le modal d'arrêt
                    console.log('Arrêter publicité:', adPayment.id);
                  }}
                >
                  Arrêter
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default AdPaymentDetailsModal;
