import React, { useState, useEffect } from 'react';
import { Search, Filter, Users as UsersIcon } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../context/AuthContext';
import { useFollow } from '../context/FollowContext';
import UserCard from '../components/ui/UserCard';
import UserStatusBadge from '../components/ui/UserStatusBadge';
import { IUser, UserRole, UserStatus } from '../types';

const UsersPage: React.FC = () => {
  const { currentUser } = useAuth();
  const { isFollowing, followUser, unfollowUser } = useFollow();
  
  const [users, setUsers] = useState<IUser[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<IUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<UserStatus | 'all'>('all');
  const [roleFilter, setRoleFilter] = useState<UserRole | 'all'>('all');
  const [followingStates, setFollowingStates] = useState<Record<string, boolean>>({});
  const [followingLoading, setFollowingLoading] = useState<Record<string, boolean>>({});

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    filterUsers();
  }, [users, searchTerm, statusFilter, roleFilter]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      const usersData: IUser[] = data.map(profile => ({
        id: profile.id,
        username: profile.username,
        email: profile.email,
        profilePicture: profile.profile_picture,
        coverPhotoUrl: profile.cover_photo_url,
        role: profile.role as UserRole,
        status: profile.status as UserStatus,
        createdAt: profile.created_at,
        city: profile.city,
        country: profile.country,
        post_count: profile.post_count || 0,
        followers_count: profile.followers_count || 0,
        following_count: profile.following_count || 0,
        ...(profile.role === UserRole.BUSINESS && {
          businessName: profile.business_name
        })
      }));

      setUsers(usersData);

      // Récupérer les états de suivi pour tous les utilisateurs
      if (currentUser) {
        const followingStatesMap: Record<string, boolean> = {};
        for (const user of usersData) {
          if (user.id !== currentUser.id) {
            const following = await isFollowing(user.id);
            followingStatesMap[user.id] = following;
          }
        }
        setFollowingStates(followingStatesMap);
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterUsers = () => {
    let filtered = users.filter(user => user.id !== currentUser?.id); // Exclure l'utilisateur actuel

    // Filtrer par terme de recherche
    if (searchTerm) {
      filtered = filtered.filter(user => {
        const displayName = user.role === UserRole.BUSINESS && 'businessName' in user 
          ? (user as any).businessName 
          : user.username;
        return displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
               user.username.toLowerCase().includes(searchTerm.toLowerCase());
      });
    }

    // Filtrer par statut
    if (statusFilter !== 'all') {
      filtered = filtered.filter(user => user.status === statusFilter);
    }

    // Filtrer par rôle
    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter);
    }

    setFilteredUsers(filtered);
  };

  const handleFollow = async (userId: string) => {
    if (!currentUser) return;

    setFollowingLoading(prev => ({ ...prev, [userId]: true }));
    try {
      const isCurrentlyFollowing = followingStates[userId];
      
      if (isCurrentlyFollowing) {
        await unfollowUser(userId);
        setFollowingStates(prev => ({ ...prev, [userId]: false }));
      } else {
        await followUser(userId);
        setFollowingStates(prev => ({ ...prev, [userId]: true }));
      }
    } catch (error) {
      console.error('Erreur lors du suivi/désuivi:', error);
    } finally {
      setFollowingLoading(prev => ({ ...prev, [userId]: false }));
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <UsersIcon className="text-blue-600 mr-3" size={32} />
          <h1 className="text-3xl font-bold text-gray-900">Communauté</h1>
        </div>
        <p className="text-lg text-gray-600">
          Découvrez les membres de notre communauté et leurs niveaux de statut
        </p>
      </div>

      {/* Filtres */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Recherche */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Rechercher un utilisateur..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Filtre par statut */}
          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as UserStatus | 'all')}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tous les statuts</option>
              <option value={UserStatus.NEWBIE}>Nouveau</option>
              <option value={UserStatus.MEMBER}>Membre</option>
              <option value={UserStatus.CONTRIBUTOR}>Contributeur</option>
              <option value={UserStatus.DISCOVERER}>Découvreur</option>
              <option value={UserStatus.INFLUENCER}>Influenceur</option>
              <option value={UserStatus.LEADER}>Leader</option>
            </select>
          </div>

          {/* Filtre par rôle */}
          <div>
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value as UserRole | 'all')}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tous les rôles</option>
              <option value={UserRole.USER}>Utilisateurs</option>
              <option value={UserRole.BUSINESS}>Entreprises</option>
            </select>
          </div>
        </div>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{filteredUsers.length}</div>
          <div className="text-sm text-gray-600">Utilisateurs trouvés</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
          <div className="text-2xl font-bold text-green-600">
            {filteredUsers.filter(u => u.role === UserRole.BUSINESS).length}
          </div>
          <div className="text-sm text-gray-600">Entreprises</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">
            {filteredUsers.filter(u => [UserStatus.INFLUENCER, UserStatus.LEADER].includes(u.status)).length}
          </div>
          <div className="text-sm text-gray-600">Influenceurs+</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
          <div className="text-2xl font-bold text-red-600">
            {filteredUsers.filter(u => u.status === UserStatus.LEADER).length}
          </div>
          <div className="text-sm text-gray-600">Leaders</div>
        </div>
      </div>

      {/* Liste des utilisateurs */}
      {filteredUsers.length === 0 ? (
        <div className="text-center py-12">
          <UsersIcon className="mx-auto text-gray-400 mb-4" size={48} />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun utilisateur trouvé</h3>
          <p className="text-gray-600">Essayez de modifier vos critères de recherche.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredUsers.map(user => (
            <UserCard
              key={user.id}
              user={user}
              showFollowButton={!!currentUser}
              onFollow={() => handleFollow(user.id)}
              isFollowing={followingStates[user.id] || false}
              isLoading={followingLoading[user.id] || false}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default UsersPage;
