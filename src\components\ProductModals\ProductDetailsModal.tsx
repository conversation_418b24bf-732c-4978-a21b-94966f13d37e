import React from 'react';
import {
  X,
  Eye,
  Star,
  Package,
  DollarSign,
  BarChart3,
  TrendingUp,
  Calendar,
  Tag,
  Award,
  ShoppingCart,
  Users,
  Heart,
  ExternalLink
} from 'lucide-react';
import { getFirstProductImage, createImageProps } from '../../utils/imageUtils';
import { formatAmount } from '../../utils/formatUtils';
import './ProductModals.css';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  category: string;
  subcategory: string;
  brand: string;
  sku: string;
  stock: number;
  minStock: number;
  images: string[];
  tags: string[];
  status: 'active' | 'inactive' | 'out_of_stock';
  rating: number;
  reviewsCount: number;
  salesCount: number;
  revenue: number;
  createdAt: string;
  updatedAt: string;
  featured: boolean;
  trending: boolean;
  onSale: boolean;
  weight?: number;
  dimensions?: { length: number; width: number; height: number };
}

interface ProductDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product | null;
}

const ProductDetailsModal: React.FC<ProductDetailsModalProps> = ({
  isOpen,
  onClose,
  product
}) => {
  if (!isOpen || !product) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getStatusBadge = (status: string) => {
    const badges = {
      active: { label: 'Actif', class: 'status-active' },
      inactive: { label: 'Inactif', class: 'status-inactive' },
      out_of_stock: { label: 'Rupture', class: 'status-out-of-stock' }
    };
    return badges[status as keyof typeof badges] || badges.active;
  };

  const getStockStatus = () => {
    if (product.stock === 0) return { label: 'Rupture de stock', class: 'stock-empty' };
    if (product.stock <= product.minStock) return { label: 'Stock faible', class: 'stock-low' };
    return { label: 'Stock suffisant', class: 'stock-good' };
  };

  return (
    <div className="modal-overlay">
      <div className="modal-container large">
        <div className="modal-header">
          <div className="modal-title">
            <Eye size={24} />
            <h2>Détails du produit</h2>
          </div>
          <button className="modal-close" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="modal-content">
          {/* En-tête du produit */}
          <div className="product-header">
            <div className="product-main-info">
              <div className="product-image-gallery">
                <img
                  {...createImageProps(
                    getFirstProductImage(product.images, product.category, product.name),
                    product.name,
                    product.category
                  )}
                  className="main-product-image"
                />

                {product.images.length > 1 && (
                  <div className="image-thumbnails">
                    {product.images.slice(1, 4).map((image, index) => (
                      <img
                        key={index}
                        src={image}
                        alt={`${product.name} ${index + 2}`}
                        className="thumbnail"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = `https://via.placeholder.com/80x80?text=${index + 2}`;
                        }}
                      />
                    ))}
                    {product.images.length > 4 && (
                      <div className="more-images">
                        +{product.images.length - 4}
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="product-info">
                <div className="product-title-section">
                  <h3>{product.name}</h3>
                  <div className="product-badges">
                    <span className={`status-badge ${getStatusBadge(product.status).class}`}>
                      {getStatusBadge(product.status).label}
                    </span>
                    {product.featured && (
                      <span className="feature-badge">
                        <Heart size={12} />
                        Favori
                      </span>
                    )}
                    {product.trending && (
                      <span className="trending-badge">
                        <TrendingUp size={12} />
                        Tendance
                      </span>
                    )}
                    {product.onSale && (
                      <span className="sale-badge">
                        Promotion
                      </span>
                    )}
                  </div>
                </div>

                <div className="product-pricing">
                  <div className="current-price">{formatAmount(product.price)}</div>
                  {product.originalPrice && (
                    <div className="original-price">{formatAmount(product.originalPrice)}</div>
                  )}
                  {product.originalPrice && (
                    <div className="discount-percentage">
                      -{Math.round((1 - product.price / product.originalPrice) * 100)}%
                    </div>
                  )}
                </div>

                <div className="product-rating">
                  <div className="stars">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        size={16}
                        fill={i < Math.floor(product.rating) ? '#fbbf24' : 'none'}
                        color="#fbbf24"
                      />
                    ))}
                  </div>
                  <span className="rating-text">
                    {product.rating.toFixed(1)} ({product.reviewsCount} avis)
                  </span>
                </div>

                <p className="product-description">{product.description}</p>
              </div>
            </div>
          </div>

          {/* Informations détaillées */}
          <div className="product-details-grid">
            {/* Informations générales */}
            <div className="detail-section">
              <h4 className="section-title">
                <Package size={18} />
                Informations générales
              </h4>
              <div className="detail-items">
                <div className="detail-item">
                  <span className="label">SKU:</span>
                  <span className="value">{product.sku}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Marque:</span>
                  <span className="value">{product.brand}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Catégorie:</span>
                  <span className="value">{product.category}</span>
                </div>
                {product.subcategory && (
                  <div className="detail-item">
                    <span className="label">Sous-catégorie:</span>
                    <span className="value">{product.subcategory}</span>
                  </div>
                )}
                <div className="detail-item">
                  <span className="label">Créé le:</span>
                  <span className="value">{formatDate(product.createdAt)}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Modifié le:</span>
                  <span className="value">{formatDate(product.updatedAt)}</span>
                </div>
              </div>
            </div>

            {/* Stock et inventaire */}
            <div className="detail-section">
              <h4 className="section-title">
                <BarChart3 size={18} />
                Stock et inventaire
              </h4>
              <div className="detail-items">
                <div className="detail-item">
                  <span className="label">Stock actuel:</span>
                  <span className={`value stock-value ${getStockStatus().class}`}>
                    {product.stock} unités
                  </span>
                </div>
                <div className="detail-item">
                  <span className="label">Stock minimum:</span>
                  <span className="value">{product.minStock} unités</span>
                </div>
                <div className="detail-item">
                  <span className="label">État du stock:</span>
                  <span className={`value ${getStockStatus().class}`}>
                    {getStockStatus().label}
                  </span>
                </div>
                {product.weight && (
                  <div className="detail-item">
                    <span className="label">Poids:</span>
                    <span className="value">{product.weight}g</span>
                  </div>
                )}
                {product.dimensions && (
                  <div className="detail-item">
                    <span className="label">Dimensions:</span>
                    <span className="value">
                      {product.dimensions.length} × {product.dimensions.width} × {product.dimensions.height} cm
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Performance commerciale */}
            <div className="detail-section">
              <h4 className="section-title">
                <TrendingUp size={18} />
                Performance commerciale
              </h4>
              <div className="detail-items">
                <div className="detail-item">
                  <span className="label">Ventes totales:</span>
                  <span className="value">{product.salesCount} unités</span>
                </div>
                <div className="detail-item">
                  <span className="label">Chiffre d'affaires:</span>
                  <span className="value">{formatAmount(product.revenue)}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Prix moyen de vente:</span>
                  <span className="value">
                    {product.salesCount > 0
                      ? formatAmount(product.revenue / product.salesCount)
                      : formatAmount(product.price)
                    }
                  </span>
                </div>
                <div className="detail-item">
                  <span className="label">Note moyenne:</span>
                  <span className="value">{product.rating.toFixed(1)}/5</span>
                </div>
              </div>
            </div>

            {/* Tags et métadonnées */}
            <div className="detail-section">
              <h4 className="section-title">
                <Tag size={18} />
                Tags et métadonnées
              </h4>
              <div className="detail-items">
                {product.tags.length > 0 ? (
                  <div className="detail-item">
                    <span className="label">Tags:</span>
                    <div className="tags-container">
                      {product.tags.map((tag, index) => (
                        <span key={index} className="tag">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="detail-item">
                    <span className="value text-muted">Aucun tag défini</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="modal-actions">
            <button className="btn-secondary" onClick={onClose}>
              Fermer
            </button>
            <button className="btn-primary">
              <ExternalLink size={16} />
              Voir sur le site
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailsModal;
