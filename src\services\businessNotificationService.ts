import { supabase } from '../lib/supabase';

/**
 * Types de notifications business
 */
export type BusinessNotificationType =
  | 'new_recommendation'
  | 'urgent_action'
  | 'data_quality_improved'
  | 'satisfaction_alert'
  | 'new_product_opportunity'
  | 'performance_milestone'
  | 'competitor_alert'
  | 'trend_detected';

export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface BusinessNotification {
  id: string;
  businessId: string;
  type: BusinessNotificationType;
  priority: NotificationPriority;
  title: string;
  message: string;
  actionUrl?: string;
  actionLabel?: string;
  data?: any;
  isRead: boolean;
  isArchived: boolean;
  createdAt: Date;
  readAt?: Date;
  expiresAt?: Date;
}

/**
 * Service de notifications business avec stockage local (simulation)
 */
export class BusinessNotificationService {
  private static readonly STORAGE_KEY = 'business_notifications';
  private static readonly MAX_NOTIFICATIONS = 100;

  /**
   * Crée une nouvelle notification
   */
  static async createNotification(notification: Omit<BusinessNotification, 'id' | 'isRead' | 'isArchived' | 'createdAt'>): Promise<BusinessNotification> {
    try {
      console.log('📢 Création d\'une nouvelle notification:', notification.title);

      const newNotification: BusinessNotification = {
        id: this.generateId(),
        ...notification,
        isRead: false,
        isArchived: false,
        createdAt: new Date()
      };

      // Stocker localement (simulation)
      const notifications = this.getStoredNotifications();
      notifications.unshift(newNotification);

      // Limiter le nombre de notifications
      if (notifications.length > this.MAX_NOTIFICATIONS) {
        notifications.splice(this.MAX_NOTIFICATIONS);
      }

      this.saveNotifications(notifications);

      // Déclencher une notification push si autorisée
      this.triggerPushNotification(newNotification);

      console.log('✅ Notification créée avec succès:', newNotification.id);
      return newNotification;
    } catch (error) {
      console.error('❌ Erreur dans createNotification:', error);
      throw error;
    }
  }

  /**
   * Récupère toutes les notifications d'une entreprise
   */
  static async getBusinessNotifications(businessId: string, options?: {
    includeRead?: boolean;
    includeArchived?: boolean;
    limit?: number;
    priority?: NotificationPriority;
  }): Promise<BusinessNotification[]> {
    try {
      console.log('🔍 Récupération des notifications pour:', businessId);

      let notifications = this.getStoredNotifications()
        .filter(n => n.businessId === businessId);

      // Filtres optionnels
      if (!options?.includeRead) {
        notifications = notifications.filter(n => !n.isRead);
      }
      if (!options?.includeArchived) {
        notifications = notifications.filter(n => !n.isArchived);
      }
      if (options?.priority) {
        notifications = notifications.filter(n => n.priority === options.priority);
      }

      // Limite
      if (options?.limit) {
        notifications = notifications.slice(0, options.limit);
      }

      console.log('✅ Notifications récupérées:', notifications.length);
      return notifications;
    } catch (error) {
      console.error('❌ Erreur dans getBusinessNotifications:', error);
      return [];
    }
  }

  /**
   * Marque une notification comme lue
   */
  static async markAsRead(notificationId: string): Promise<boolean> {
    try {
      const notifications = this.getStoredNotifications();
      const notification = notifications.find(n => n.id === notificationId);

      if (notification) {
        notification.isRead = true;
        notification.readAt = new Date();
        this.saveNotifications(notifications);
        console.log('✅ Notification marquée comme lue:', notificationId);
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Erreur dans markAsRead:', error);
      return false;
    }
  }

  /**
   * Archive une notification
   */
  static async archiveNotification(notificationId: string): Promise<boolean> {
    try {
      const notifications = this.getStoredNotifications();
      const notification = notifications.find(n => n.id === notificationId);

      if (notification) {
        notification.isArchived = true;
        this.saveNotifications(notifications);
        console.log('✅ Notification archivée:', notificationId);
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Erreur dans archiveNotification:', error);
      return false;
    }
  }

  /**
   * Compte les notifications non lues
   */
  static async getUnreadCount(businessId: string): Promise<number> {
    try {
      const notifications = this.getStoredNotifications();
      const unreadCount = notifications.filter(n =>
        n.businessId === businessId && !n.isRead && !n.isArchived
      ).length;

      return unreadCount;
    } catch (error) {
      console.error('❌ Erreur dans getUnreadCount:', error);
      return 0;
    }
  }

  /**
   * Supprime les notifications expirées
   */
  static async cleanupExpiredNotifications(): Promise<number> {
    try {
      const notifications = this.getStoredNotifications();
      const now = new Date();
      const validNotifications = notifications.filter(n =>
        !n.expiresAt || n.expiresAt > now
      );

      const deletedCount = notifications.length - validNotifications.length;
      this.saveNotifications(validNotifications);

      console.log('🧹 Notifications expirées supprimées:', deletedCount);
      return deletedCount;
    } catch (error) {
      console.error('❌ Erreur dans cleanupExpiredNotifications:', error);
      return 0;
    }
  }

  /**
   * Marque toutes les notifications comme lues
   */
  static async markAllAsRead(businessId: string): Promise<boolean> {
    try {
      const notifications = this.getStoredNotifications();
      let updated = false;

      notifications.forEach(n => {
        if (n.businessId === businessId && !n.isRead) {
          n.isRead = true;
          n.readAt = new Date();
          updated = true;
        }
      });

      if (updated) {
        this.saveNotifications(notifications);
        console.log('✅ Toutes les notifications marquées comme lues pour:', businessId);
      }

      return updated;
    } catch (error) {
      console.error('❌ Erreur dans markAllAsRead:', error);
      return false;
    }
  }

  /**
   * Récupère les notifications depuis le localStorage
   */
  private static getStoredNotifications(): BusinessNotification[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return [];

      const parsed = JSON.parse(stored);
      return parsed.map((n: any) => ({
        ...n,
        createdAt: new Date(n.createdAt),
        readAt: n.readAt ? new Date(n.readAt) : undefined,
        expiresAt: n.expiresAt ? new Date(n.expiresAt) : undefined
      }));
    } catch (error) {
      console.error('❌ Erreur lors de la lecture des notifications:', error);
      return [];
    }
  }

  /**
   * Sauvegarde les notifications dans le localStorage
   */
  private static saveNotifications(notifications: BusinessNotification[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(notifications));
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde des notifications:', error);
    }
  }

  /**
   * Génère un ID unique
   */
  private static generateId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Déclenche une notification push
   */
  private static triggerPushNotification(notification: BusinessNotification): void {
    try {
      if ('Notification' in window && Notification.permission === 'granted') {
        const pushNotification = new Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico',
          badge: '/favicon.ico',
          tag: notification.id,
          data: notification.data
        });

        // Auto-fermer après 5 secondes
        setTimeout(() => {
          pushNotification.close();
        }, 5000);

        // Gérer le clic sur la notification
        pushNotification.onclick = () => {
          window.focus();
          if (notification.actionUrl) {
            window.location.href = notification.actionUrl;
          }
          pushNotification.close();
        };
      }
    } catch (error) {
      console.error('❌ Erreur lors de l\'envoi de la notification push:', error);
    }
  }

  /**
   * Demande la permission pour les notifications push
   */
  static async requestNotificationPermission(): Promise<boolean> {
    try {
      if (!('Notification' in window)) {
        console.log('Ce navigateur ne supporte pas les notifications');
        return false;
      }

      if (Notification.permission === 'granted') {
        return true;
      }

      if (Notification.permission !== 'denied') {
        const permission = await Notification.requestPermission();
        return permission === 'granted';
      }

      return false;
    } catch (error) {
      console.error('❌ Erreur lors de la demande de permission:', error);
      return false;
    }
  }

  /**
   * Obtient l'icône et la couleur selon le type de notification
   */
  static getNotificationStyle(type: BusinessNotificationType, priority: NotificationPriority): {
    icon: string;
    color: string;
    bgColor: string;
  } {
    let icon = '📢';
    let color = 'blue';
    let bgColor = 'bg-blue-50';

    switch (type) {
      case 'new_recommendation':
        icon = '💡';
        color = 'blue';
        bgColor = 'bg-blue-50';
        break;
      case 'urgent_action':
        icon = '⚠️';
        color = 'red';
        bgColor = 'bg-red-50';
        break;
      case 'satisfaction_alert':
        icon = '😟';
        color = 'orange';
        bgColor = 'bg-orange-50';
        break;
      case 'new_product_opportunity':
        icon = '🚀';
        color = 'green';
        bgColor = 'bg-green-50';
        break;
      case 'performance_milestone':
        icon = '🎉';
        color = 'purple';
        bgColor = 'bg-purple-50';
        break;
      case 'data_quality_improved':
        icon = '📊';
        color = 'indigo';
        bgColor = 'bg-indigo-50';
        break;
      case 'trend_detected':
        icon = '📈';
        color = 'teal';
        bgColor = 'bg-teal-50';
        break;
      case 'competitor_alert':
        icon = '🎯';
        color = 'yellow';
        bgColor = 'bg-yellow-50';
        break;
    }

    // Ajuster selon la priorité
    if (priority === 'urgent') {
      color = 'red';
      bgColor = 'bg-red-50';
    } else if (priority === 'high') {
      color = 'orange';
      bgColor = 'bg-orange-50';
    }

    return { icon, color, bgColor };
  }

  /**
   * Formate le temps relatif
   */
  static formatTimeAgo(date: Date): string {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'À l\'instant';
    } else if (diffInMinutes < 60) {
      return `Il y a ${diffInMinutes} min`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `Il y a ${hours}h`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `Il y a ${days}j`;
    }
  }
}

/**
 * Service de génération automatique de notifications
 */
export class AutoNotificationService {

  /**
   * Analyse les données business et génère des notifications automatiques
   */
  static async generateAutomaticNotifications(businessId: string): Promise<BusinessNotification[]> {
    try {
      console.log('🤖 Génération automatique de notifications pour:', businessId);

      const notifications: BusinessNotification[] = [];

      // Importer les services d'analyse
      const { RealBusinessDataService } = await import('./realBusinessDataService');
      const { RealAIAnalysisService } = await import('./realAIAnalysisService');

      // Récupérer les données business
      const businessData = await RealBusinessDataService.getCompleteBusinessData(businessId);
      const dataQuality = await RealBusinessDataService.hasEnoughDataForAnalysis(businessId);

      // 1. Notification pour amélioration de la qualité des données
      if (dataQuality.dataQuality === 'poor') {
        const notification = await BusinessNotificationService.createNotification({
          businessId,
          type: 'data_quality_improved',
          priority: 'medium',
          title: 'Améliorez vos données pour de meilleures recommandations',
          message: `Vous avez ${dataQuality.dataPoints.total} points de données. Collectez plus d'avis clients pour des analyses plus précises.`,
          actionUrl: '/business-profile?tab=ai-advisor',
          actionLabel: 'Voir les recommandations',
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 jours
        });
        notifications.push(notification);
      }

      // 2. Alerte de satisfaction client
      if (businessData.ratings.length > 0) {
        const averageRating = businessData.ratings.reduce((sum: number, r: any) => sum + r.rating, 0) / businessData.ratings.length;
        if (averageRating < 3.5) {
          const notification = await BusinessNotificationService.createNotification({
            businessId,
            type: 'satisfaction_alert',
            priority: 'urgent',
            title: '⚠️ Alerte Satisfaction Client',
            message: `Votre note moyenne (${averageRating.toFixed(1)}/5) est en baisse. Action immédiate recommandée.`,
            actionUrl: '/business-profile?tab=ai-advisor',
            actionLabel: 'Voir le plan d\'action',
            data: { averageRating, totalRatings: businessData.ratings.length }
          });
          notifications.push(notification);
        }
      }

      // 3. Opportunité de nouveau produit
      if (dataQuality.hasEnoughData) {
        const productSuggestions = await RealAIAnalysisService.generateProductSuggestions(businessId);
        if (productSuggestions.suggestions.length > 0) {
          const topSuggestion = productSuggestions.suggestions[0];
          const notification = await BusinessNotificationService.createNotification({
            businessId,
            type: 'new_product_opportunity',
            priority: 'medium',
            title: '💡 Nouvelle Opportunité Produit Détectée',
            message: `L'IA a identifié une opportunité: "${topSuggestion.title}" avec ${topSuggestion.confidence}% de confiance.`,
            actionUrl: '/business-profile?tab=ai-advisor&subtab=products',
            actionLabel: 'Explorer l\'opportunité',
            data: { suggestion: topSuggestion }
          });
          notifications.push(notification);
        }
      }

      // 4. Milestone de performance
      if (businessData.stats.postsCount >= 10 && businessData.stats.productsCount >= 5) {
        const notification = await BusinessNotificationService.createNotification({
          businessId,
          type: 'performance_milestone',
          priority: 'low',
          title: '🎉 Félicitations ! Milestone Atteint',
          message: `Vous avez atteint ${businessData.stats.postsCount} avis et ${businessData.stats.productsCount} produits. Votre présence se renforce !`,
          actionUrl: '/business-profile',
          actionLabel: 'Voir mon profil',
          data: {
            postsCount: businessData.stats.postsCount,
            productsCount: businessData.stats.productsCount
          }
        });
        notifications.push(notification);
      }

      // 5. Nouvelle recommandation disponible
      const recommendations = await RealAIAnalysisService.generateStrategicRecommendations(businessId);
      if (recommendations.recommendations.length > 0) {
        const urgentRecs = recommendations.recommendations.filter((r: any) => r.priority === 'urgent');
        if (urgentRecs.length > 0) {
          const notification = await BusinessNotificationService.createNotification({
            businessId,
            type: 'new_recommendation',
            priority: 'high',
            title: '🎯 Nouvelles Recommandations Urgentes',
            message: `${urgentRecs.length} recommandation(s) urgente(s) détectée(s) par l'IA. Action immédiate recommandée.`,
            actionUrl: '/business-profile?tab=ai-advisor',
            actionLabel: 'Voir les recommandations',
            data: { urgentCount: urgentRecs.length }
          });
          notifications.push(notification);
        }
      }

      console.log('✅ Notifications automatiques générées:', notifications.length);
      return notifications;
    } catch (error) {
      console.error('❌ Erreur dans generateAutomaticNotifications:', error);
      return [];
    }
  }

  /**
   * Vérifie et génère des notifications basées sur les seuils
   */
  static async checkThresholds(businessId: string): Promise<void> {
    try {
      console.log('🔍 Vérification des seuils pour:', businessId);

      // Cette fonction peut être appelée périodiquement
      await this.generateAutomaticNotifications(businessId);

      // Nettoyer les notifications expirées
      await BusinessNotificationService.cleanupExpiredNotifications();

    } catch (error) {
      console.error('❌ Erreur dans checkThresholds:', error);
    }
  }

  /**
   * Démarre la surveillance automatique (simulation)
   */
  static startAutoMonitoring(businessId: string, intervalMinutes: number = 30): () => void {
    console.log('🔄 Démarrage de la surveillance automatique pour:', businessId);

    const interval = setInterval(() => {
      this.checkThresholds(businessId);
    }, intervalMinutes * 60 * 1000);

    // Première vérification immédiate
    this.checkThresholds(businessId);

    // Retourner une fonction pour arrêter la surveillance
    return () => {
      console.log('⏹️ Arrêt de la surveillance automatique');
      clearInterval(interval);
    };
  }
}
