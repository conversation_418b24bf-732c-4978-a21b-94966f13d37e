import React from 'react';
import { UserStatus } from '../../types';
import { UserStatusService } from '../../services/userStatusService';

interface UserStatusBadgeProps {
  status: UserStatus;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'solid' | 'gradient';
  showIcon?: boolean;
  showText?: boolean;
  animated?: boolean;
  className?: string;
}

const UserStatusBadge: React.FC<UserStatusBadgeProps> = ({
  status,
  size = 'sm',
  variant = 'default',
  showIcon = true,
  showText = true,
  animated = false,
  className = ''
}) => {
  const statusLevel = UserStatusService.getStatusLevel(status);

  if (!statusLevel) {
    return null;
  }

  const IconComponent = statusLevel.icon;

  // Tailles des badges
  const sizeClasses = {
    xs: {
      container: 'px-1.5 py-0.5 text-xs',
      icon: 10,
      gap: 'gap-1'
    },
    sm: {
      container: 'px-2 py-1 text-xs',
      icon: 12,
      gap: 'gap-1'
    },
    md: {
      container: 'px-3 py-1.5 text-sm',
      icon: 14,
      gap: 'gap-1.5'
    },
    lg: {
      container: 'px-4 py-2 text-base',
      icon: 16,
      gap: 'gap-2'
    }
  };

  const currentSize = sizeClasses[size];

  // Classes de style basées sur le statut et la variante
  const getStatusClasses = () => {
    const baseClasses = 'inline-flex items-center font-medium rounded-full transition-all duration-200';

    switch (variant) {
      case 'outline':
        switch (status) {
          case UserStatus.NEWBIE:
            return `${baseClasses} border-2 border-gray-300 text-gray-700 bg-white hover:bg-gray-50`;
          case UserStatus.MEMBER:
            return `${baseClasses} border-2 border-blue-300 text-blue-700 bg-white hover:bg-blue-50`;
          case UserStatus.CONTRIBUTOR:
            return `${baseClasses} border-2 border-green-300 text-green-700 bg-white hover:bg-green-50`;
          case UserStatus.DISCOVERER:
            return `${baseClasses} border-2 border-purple-300 text-purple-700 bg-white hover:bg-purple-50`;
          case UserStatus.INFLUENCER:
            return `${baseClasses} border-2 border-yellow-300 text-yellow-700 bg-white hover:bg-yellow-50`;
          case UserStatus.LEADER:
            return `${baseClasses} border-2 border-red-300 text-red-700 bg-white hover:bg-red-50`;
          default:
            return `${baseClasses} border-2 border-gray-300 text-gray-700 bg-white`;
        }

      case 'solid':
        switch (status) {
          case UserStatus.NEWBIE:
            return `${baseClasses} bg-gray-600 text-white hover:bg-gray-700`;
          case UserStatus.MEMBER:
            return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700`;
          case UserStatus.CONTRIBUTOR:
            return `${baseClasses} bg-green-600 text-white hover:bg-green-700`;
          case UserStatus.DISCOVERER:
            return `${baseClasses} bg-purple-600 text-white hover:bg-purple-700`;
          case UserStatus.INFLUENCER:
            return `${baseClasses} bg-yellow-600 text-white hover:bg-yellow-700`;
          case UserStatus.LEADER:
            return `${baseClasses} bg-red-600 text-white hover:bg-red-700`;
          default:
            return `${baseClasses} bg-gray-600 text-white`;
        }

      case 'gradient':
        switch (status) {
          case UserStatus.NEWBIE:
            return `${baseClasses} bg-gradient-to-r from-gray-400 to-gray-600 text-white`;
          case UserStatus.MEMBER:
            return `${baseClasses} bg-gradient-to-r from-blue-400 to-blue-600 text-white`;
          case UserStatus.CONTRIBUTOR:
            return `${baseClasses} bg-gradient-to-r from-green-400 to-green-600 text-white`;
          case UserStatus.DISCOVERER:
            return `${baseClasses} bg-gradient-to-r from-purple-400 to-purple-600 text-white`;
          case UserStatus.INFLUENCER:
            return `${baseClasses} bg-gradient-to-r from-yellow-400 to-orange-500 text-white`;
          case UserStatus.LEADER:
            return `${baseClasses} bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg`;
          default:
            return `${baseClasses} bg-gradient-to-r from-gray-400 to-gray-600 text-white`;
        }

      default: // 'default'
        switch (status) {
          case UserStatus.NEWBIE:
            return `${baseClasses} bg-gray-100 text-gray-700 hover:bg-gray-200`;
          case UserStatus.MEMBER:
            return `${baseClasses} bg-blue-100 text-blue-700 hover:bg-blue-200`;
          case UserStatus.CONTRIBUTOR:
            return `${baseClasses} bg-green-100 text-green-700 hover:bg-green-200`;
          case UserStatus.DISCOVERER:
            return `${baseClasses} bg-purple-100 text-purple-700 hover:bg-purple-200`;
          case UserStatus.INFLUENCER:
            return `${baseClasses} bg-yellow-100 text-yellow-700 hover:bg-yellow-200`;
          case UserStatus.LEADER:
            return `${baseClasses} bg-red-100 text-red-700 hover:bg-red-200`;
          default:
            return `${baseClasses} bg-gray-100 text-gray-700`;
        }
    }
  };

  const animationClasses = animated ? 'animate-pulse hover:animate-none' : '';

  return (
    <span
      className={`
        ${getStatusClasses()} ${currentSize.container} ${currentSize.gap}
        ${animationClasses} ${className}
      `}
      title={`${statusLevel.name} - ${statusLevel.description}`}
    >
      {showIcon && (
        <IconComponent size={currentSize.icon} className="flex-shrink-0" />
      )}
      {showText && (
        <span className="truncate">{statusLevel.name}</span>
      )}
    </span>
  );
};

export default UserStatusBadge;
