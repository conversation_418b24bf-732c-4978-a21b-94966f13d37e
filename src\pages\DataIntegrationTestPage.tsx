import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { DataIntegrationTest } from '../components/DataIntegrationTest';
import { SyncStatusBar } from '../components/DataSync';
import { useRealBusinessData } from '../hooks/useRealData';
import { 
  Database, 
  CheckCircle, 
  AlertTriangle, 
  Info,
  ArrowLeft,
  RefreshCw,
  Zap,
  BarChart3,
  Package,
  Trophy
} from 'lucide-react';
import { Link } from 'react-router-dom';

const DataIntegrationTestPage: React.FC = () => {
  const { currentUser } = useAuth();
  const businessData = useRealBusinessData();
  const [showAdvancedInfo, setShowAdvancedInfo] = useState(false);

  const getIntegrationStatus = () => {
    if (!currentUser) return 'error';
    if (businessData.loading) return 'loading';
    
    const hasData = (businessData.sales && businessData.sales.length > 0) ||
                   (businessData.products && businessData.products.length > 0) ||
                   (businessData.rankings && businessData.rankings.length > 0);
    
    return hasData ? 'success' : 'warning';
  };

  const getStatusMessage = () => {
    const status = getIntegrationStatus();
    switch (status) {
      case 'success':
        return 'Intégration fonctionnelle - Données disponibles';
      case 'warning':
        return 'Intégration prête - En attente de données';
      case 'loading':
        return 'Chargement des données en cours...';
      case 'error':
        return 'Erreur - Utilisateur non connecté';
      default:
        return 'État inconnu';
    }
  };

  const getStatusIcon = () => {
    const status = getIntegrationStatus();
    switch (status) {
      case 'success':
        return <CheckCircle size={24} className="text-green-500" />;
      case 'warning':
        return <AlertTriangle size={24} className="text-yellow-500" />;
      case 'loading':
        return <RefreshCw size={24} className="text-blue-500 spinning" />;
      case 'error':
        return <AlertTriangle size={24} className="text-red-500" />;
      default:
        return <Info size={24} className="text-gray-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link 
                to="/business/sales" 
                className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                <ArrowLeft size={20} />
                Retour
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center gap-3">
                <Database size={28} className="text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Test d'intégration des données
                  </h1>
                  <p className="text-gray-600">
                    Validation complète du système de vraies données
                  </p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              {getStatusIcon()}
              <div className="text-right">
                <div className="font-semibold text-gray-900">
                  {getStatusMessage()}
                </div>
                <div className="text-sm text-gray-500">
                  {currentUser ? `Connecté: ${currentUser.email}` : 'Non connecté'}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sync Status Bar */}
        <SyncStatusBar />

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <BarChart3 size={24} className="text-blue-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {businessData.sales?.length || 0}
                </div>
                <div className="text-gray-600">Ventes</div>
              </div>
            </div>
            <div className="mt-2 text-sm text-gray-500">
              CA: {businessData.salesStats?.totalRevenue?.toLocaleString() || 0} F CFA
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Package size={24} className="text-green-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {businessData.products?.length || 0}
                </div>
                <div className="text-gray-600">Produits</div>
              </div>
            </div>
            <div className="mt-2 text-sm text-gray-500">
              Prix moyen: {businessData.productStats?.averagePrice?.toLocaleString() || 0} F CFA
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Trophy size={24} className="text-yellow-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {businessData.rankings?.length || 0}
                </div>
                <div className="text-gray-600">Classements</div>
              </div>
            </div>
            <div className="mt-2 text-sm text-gray-500">
              Top 3: {businessData.rankings?.filter(r => r.currentRank <= 3).length || 0}
            </div>
          </div>
        </div>

        {/* Integration Test Component */}
        <DataIntegrationTest />

        {/* Advanced Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">
              Informations techniques
            </h2>
            <button
              onClick={() => setShowAdvancedInfo(!showAdvancedInfo)}
              className="flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors"
            >
              <Info size={16} />
              {showAdvancedInfo ? 'Masquer' : 'Afficher'}
            </button>
          </div>

          {showAdvancedInfo && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Services actifs</h3>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li>✅ SalesDataService - Récupération ventes</li>
                    <li>✅ ProductsDataService - Gestion produits</li>
                    <li>✅ RankingsDataService - Calcul classements</li>
                    <li>✅ useRealBusinessData - Hook combiné</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Pages intégrées</h3>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li>✅ BusinessSalesPage - Toggle actif</li>
                    <li>✅ BusinessProductsPage - Toggle actif</li>
                    <li>✅ BusinessRankingsPage - Toggle actif</li>
                    <li>✅ DataToggle - Composant universel</li>
                  </ul>
                </div>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-medium text-gray-900 mb-2">État des données</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium">Ventes</div>
                    <div className="text-gray-600">
                      Chargement: {businessData.loading ? 'Oui' : 'Non'}
                    </div>
                    <div className="text-gray-600">
                      Erreurs: {businessData.errors.sales ? 'Oui' : 'Non'}
                    </div>
                  </div>
                  
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium">Produits</div>
                    <div className="text-gray-600">
                      Chargement: {businessData.loading ? 'Oui' : 'Non'}
                    </div>
                    <div className="text-gray-600">
                      Erreurs: {businessData.errors.products ? 'Oui' : 'Non'}
                    </div>
                  </div>
                  
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium">Classements</div>
                    <div className="text-gray-600">
                      Chargement: {businessData.loading ? 'Oui' : 'Non'}
                    </div>
                    <div className="text-gray-600">
                      Erreurs: {businessData.errors.rankings ? 'Oui' : 'Non'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-medium text-gray-900 mb-2">Actions disponibles</h3>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={businessData.refreshAllData}
                    className="flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                  >
                    <RefreshCw size={14} />
                    Rafraîchir toutes les données
                  </button>
                  
                  <button
                    onClick={businessData.refreshSalesData}
                    className="flex items-center gap-2 px-3 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
                  >
                    <BarChart3 size={14} />
                    Rafraîchir ventes
                  </button>
                  
                  <button
                    onClick={businessData.refreshProductsData}
                    className="flex items-center gap-2 px-3 py-2 bg-yellow-100 text-yellow-700 rounded-lg hover:bg-yellow-200 transition-colors"
                  >
                    <Package size={14} />
                    Rafraîchir produits
                  </button>
                  
                  <button
                    onClick={businessData.refreshRankingsData}
                    className="flex items-center gap-2 px-3 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors"
                  >
                    <Trophy size={14} />
                    Rafraîchir classements
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Actions rapides
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              to="/business/sales"
              className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
            >
              <BarChart3 size={20} className="text-blue-600" />
              <div>
                <div className="font-medium text-gray-900">Mes ventes</div>
                <div className="text-sm text-gray-600">Tester le toggle ventes</div>
              </div>
            </Link>
            
            <Link
              to="/business/products"
              className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors"
            >
              <Package size={20} className="text-green-600" />
              <div>
                <div className="font-medium text-gray-900">Mes produits</div>
                <div className="text-sm text-gray-600">Tester le toggle produits</div>
              </div>
            </Link>
            
            <Link
              to="/business/rankings"
              className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-yellow-300 hover:bg-yellow-50 transition-colors"
            >
              <Trophy size={20} className="text-yellow-600" />
              <div>
                <div className="font-medium text-gray-900">Mes classements</div>
                <div className="text-sm text-gray-600">Tester le toggle classements</div>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataIntegrationTestPage;
