-- Migration pour créer le système de support complet
-- Chat en direct, tickets de support, et forum communautaire

-- =====================================================
-- 1. TABLE DES CONVERSATIONS DE CHAT
-- =====================================================
CREATE TABLE IF NOT EXISTS chat_conversations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  agent_id uuid REFERENCES profiles(id) ON DELETE SET NULL,

  -- Statut de la conversation
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'waiting', 'closed', 'transferred')),
  priority text NOT NULL DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),

  -- Métadonnées
  subject text,
  department text DEFAULT 'general',
  language text DEFAULT 'fr',

  -- Timestamps
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  closed_at timestamptz
);

-- =====================================================
-- 2. TABLE DES MESSAGES DE CHAT
-- =====================================================
CREATE TABLE IF NOT EXISTS chat_messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id uuid NOT NULL REFERENCES chat_conversations(id) ON DELETE CASCADE,
  sender_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,

  -- Contenu du message
  content text NOT NULL CHECK (length(content) > 0 AND length(content) <= 2000),
  message_type text NOT NULL DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'system')),

  -- Métadonnées
  attachments jsonb DEFAULT '[]'::jsonb,
  metadata jsonb DEFAULT '{}'::jsonb,

  -- Statut du message
  is_read boolean DEFAULT false,
  is_edited boolean DEFAULT false,
  edited_at timestamptz,

  -- Timestamps
  created_at timestamptz DEFAULT now()
);

-- Créer la séquence pour les numéros de tickets
CREATE SEQUENCE IF NOT EXISTS ticket_sequence START 1;

-- =====================================================
-- 3. TABLE DES TICKETS DE SUPPORT
-- =====================================================
CREATE TABLE IF NOT EXISTS support_tickets (
  id text PRIMARY KEY DEFAULT 'T' || to_char(now(), 'YYYYMMDD') || '-' || lpad(nextval('ticket_sequence')::text, 4, '0'),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  assigned_agent_id uuid REFERENCES profiles(id) ON DELETE SET NULL,

  -- Informations du ticket
  subject text NOT NULL CHECK (length(subject) > 0 AND length(subject) <= 200),
  description text NOT NULL CHECK (length(description) > 0),
  category text NOT NULL DEFAULT 'general' CHECK (category IN ('general', 'technical', 'billing', 'account', 'feature', 'bug')),

  -- Statut et priorité
  status text NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'waiting_user', 'resolved', 'closed')),
  priority text NOT NULL DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),

  -- Métadonnées
  tags jsonb DEFAULT '[]'::jsonb,
  attachments jsonb DEFAULT '[]'::jsonb,
  metadata jsonb DEFAULT '{}'::jsonb,

  -- Satisfaction
  satisfaction_rating integer CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
  satisfaction_comment text,

  -- Timestamps
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  resolved_at timestamptz,
  closed_at timestamptz
);

-- =====================================================
-- 4. TABLE DES MESSAGES DE TICKETS
-- =====================================================
CREATE TABLE IF NOT EXISTS ticket_messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  ticket_id text NOT NULL REFERENCES support_tickets(id) ON DELETE CASCADE,
  sender_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,

  -- Contenu du message
  content text NOT NULL CHECK (length(content) > 0),
  message_type text NOT NULL DEFAULT 'reply' CHECK (message_type IN ('reply', 'note', 'system')),

  -- Métadonnées
  attachments jsonb DEFAULT '[]'::jsonb,
  is_internal boolean DEFAULT false, -- Pour les notes internes des agents

  -- Timestamps
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- =====================================================
-- 5. TABLE DES POSTS COMMUNAUTAIRES
-- =====================================================
CREATE TABLE IF NOT EXISTS community_posts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  author_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,

  -- Contenu du post
  title text NOT NULL CHECK (length(title) > 0 AND length(title) <= 200),
  content text NOT NULL CHECK (length(content) > 0),
  category text NOT NULL DEFAULT 'general' CHECK (category IN ('general', 'conseils', 'bugs', 'features', 'questions')),

  -- Métadonnées
  tags jsonb DEFAULT '[]'::jsonb,
  attachments jsonb DEFAULT '[]'::jsonb,

  -- Engagement
  votes_up integer DEFAULT 0,
  votes_down integer DEFAULT 0,
  views_count integer DEFAULT 0,
  replies_count integer DEFAULT 0,

  -- Statut
  is_solved boolean DEFAULT false,
  is_pinned boolean DEFAULT false,
  is_locked boolean DEFAULT false,

  -- Modération
  is_approved boolean DEFAULT true,
  is_flagged boolean DEFAULT false,
  flagged_reason text,

  -- Timestamps
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- =====================================================
-- 6. TABLE DES RÉPONSES COMMUNAUTAIRES
-- =====================================================
CREATE TABLE IF NOT EXISTS community_replies (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id uuid NOT NULL REFERENCES community_posts(id) ON DELETE CASCADE,
  author_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  parent_reply_id uuid REFERENCES community_replies(id) ON DELETE CASCADE,

  -- Contenu de la réponse
  content text NOT NULL CHECK (length(content) > 0),

  -- Engagement
  votes_up integer DEFAULT 0,
  votes_down integer DEFAULT 0,

  -- Statut
  is_solution boolean DEFAULT false,
  is_approved boolean DEFAULT true,
  is_flagged boolean DEFAULT false,

  -- Timestamps
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- =====================================================
-- 7. TABLE DES VOTES COMMUNAUTAIRES
-- =====================================================
CREATE TABLE IF NOT EXISTS community_votes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  post_id uuid REFERENCES community_posts(id) ON DELETE CASCADE,
  reply_id uuid REFERENCES community_replies(id) ON DELETE CASCADE,

  -- Type de vote
  vote_type text NOT NULL CHECK (vote_type IN ('up', 'down')),

  -- Timestamps
  created_at timestamptz DEFAULT now(),

  -- Contraintes
  CHECK ((post_id IS NOT NULL AND reply_id IS NULL) OR (post_id IS NULL AND reply_id IS NOT NULL)),
  UNIQUE(user_id, post_id),
  UNIQUE(user_id, reply_id)
);

-- =====================================================
-- 8. TABLE DES ÉVALUATIONS FAQ
-- =====================================================
CREATE TABLE IF NOT EXISTS faq_ratings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  faq_question text NOT NULL,

  -- Évaluation
  is_helpful boolean NOT NULL,
  feedback text,

  -- Timestamps
  created_at timestamptz DEFAULT now(),

  -- Contraintes
  UNIQUE(user_id, faq_question)
);

-- =============================
-- TABLE DES ABONNEMENTS (FOLLOWERS)
-- =============================
CREATE TABLE IF NOT EXISTS followers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  follower_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  following_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE (follower_id, following_id)
);

CREATE INDEX IF NOT EXISTS idx_followers_follower_id ON followers(follower_id);
CREATE INDEX IF NOT EXISTS idx_followers_following_id ON followers(following_id);

-- Activer la sécurité RLS
ALTER TABLE followers ENABLE ROW LEVEL SECURITY;

-- Politique : chaque utilisateur peut voir ses abonnés/abonnements
CREATE POLICY "select_own_followers"
  ON followers
  FOR SELECT
  USING (
    auth.uid() = follower_id OR auth.uid() = following_id
  );

-- Politique : chaque utilisateur peut suivre quelqu'un
CREATE POLICY "insert_own_follow"
  ON followers
  FOR INSERT
  WITH CHECK (
    auth.uid() = follower_id
  );

-- Politique : chaque utilisateur peut se désabonner
CREATE POLICY "delete_own_follow"
  ON followers
  FOR DELETE
  USING (
    auth.uid() = follower_id
  );

-- =====================================================
-- CRÉATION DES INDEX POUR OPTIMISER LES PERFORMANCES
-- =====================================================

-- Index pour chat_conversations
CREATE INDEX IF NOT EXISTS idx_chat_conversations_user_id ON chat_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_conversations_agent_id ON chat_conversations(agent_id);
CREATE INDEX IF NOT EXISTS idx_chat_conversations_status ON chat_conversations(status);
CREATE INDEX IF NOT EXISTS idx_chat_conversations_created_at ON chat_conversations(created_at);

-- Index pour chat_messages
CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation_id ON chat_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_sender_id ON chat_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);

-- Index pour support_tickets
CREATE INDEX IF NOT EXISTS idx_support_tickets_user_id ON support_tickets(user_id);
CREATE INDEX IF NOT EXISTS idx_support_tickets_assigned_agent_id ON support_tickets(assigned_agent_id);
CREATE INDEX IF NOT EXISTS idx_support_tickets_status ON support_tickets(status);
CREATE INDEX IF NOT EXISTS idx_support_tickets_priority ON support_tickets(priority);
CREATE INDEX IF NOT EXISTS idx_support_tickets_category ON support_tickets(category);
CREATE INDEX IF NOT EXISTS idx_support_tickets_created_at ON support_tickets(created_at);

-- Index pour ticket_messages
CREATE INDEX IF NOT EXISTS idx_ticket_messages_ticket_id ON ticket_messages(ticket_id);
CREATE INDEX IF NOT EXISTS idx_ticket_messages_sender_id ON ticket_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_ticket_messages_created_at ON ticket_messages(created_at);

-- Index pour community_posts
CREATE INDEX IF NOT EXISTS idx_community_posts_author_id ON community_posts(author_id);
CREATE INDEX IF NOT EXISTS idx_community_posts_category ON community_posts(category);
CREATE INDEX IF NOT EXISTS idx_community_posts_created_at ON community_posts(created_at);
CREATE INDEX IF NOT EXISTS idx_community_posts_votes ON community_posts(votes_up, votes_down);
CREATE INDEX IF NOT EXISTS idx_community_posts_is_solved ON community_posts(is_solved);

-- Index pour community_replies
CREATE INDEX IF NOT EXISTS idx_community_replies_post_id ON community_replies(post_id);
CREATE INDEX IF NOT EXISTS idx_community_replies_author_id ON community_replies(author_id);
CREATE INDEX IF NOT EXISTS idx_community_replies_parent_reply_id ON community_replies(parent_reply_id);
CREATE INDEX IF NOT EXISTS idx_community_replies_created_at ON community_replies(created_at);

-- Index pour community_votes
CREATE INDEX IF NOT EXISTS idx_community_votes_user_id ON community_votes(user_id);
CREATE INDEX IF NOT EXISTS idx_community_votes_post_id ON community_votes(post_id);
CREATE INDEX IF NOT EXISTS idx_community_votes_reply_id ON community_votes(reply_id);

-- Index pour faq_ratings
CREATE INDEX IF NOT EXISTS idx_faq_ratings_faq_question ON faq_ratings(faq_question);
CREATE INDEX IF NOT EXISTS idx_faq_ratings_is_helpful ON faq_ratings(is_helpful);

-- Politique RLS : autoriser l'insertion de nouveaux profils (pour l'inscription Supabase Auth)
CREATE POLICY "Allow insert for authenticated"
  ON profiles
  FOR INSERT
  WITH CHECK (true);

-- Politique RLS : autoriser la lecture de son propre profil
CREATE POLICY "Allow select own profile"
  ON profiles
  FOR SELECT
  USING (auth.uid() = id);

-- Politique RLS : autoriser la mise à jour de son propre profil
CREATE POLICY "Allow update own profile"
  ON profiles
  FOR UPDATE
  USING (auth.uid() = id);

-- Politique RLS : autoriser la suppression de son propre profil (optionnel)
CREATE POLICY "Allow delete own profile"
  ON profiles
  FOR DELETE
  USING (auth.uid() = id);
