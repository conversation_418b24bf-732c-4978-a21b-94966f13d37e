import { UserRole } from '../types';

/**
 * Utilitaires pour gérer les permissions d'interaction avec les publicités
 */

/**
 * Vérifie si un utilisateur peut commenter une publicité
 * @param userRole - Le rôle de l'utilisateur
 * @returns true si l'utilisateur peut commenter, false sinon
 */
export const canUserCommentOnAd = (userRole: UserRole | undefined): boolean => {
  if (!userRole) return false;
  
  // Seuls les utilisateurs réguliers et les admins peuvent commenter les publicités
  return userRole === UserRole.STANDARD || userRole === UserRole.ADMIN;
};

/**
 * Vérifie si un utilisateur peut liker une publicité
 * @param userRole - Le rôle de l'utilisateur
 * @returns true si l'utilisateur peut liker, false sinon
 */
export const canUserLikeAd = (userRole: UserRole | undefined): boolean => {
  if (!userRole) return false;
  
  // Tous les utilisateurs authentifiés peuvent liker les publicités
  return true;
};

/**
 * Vérifie si un utilisateur peut partager une publicité
 * @param userRole - Le rôle de l'utilisateur
 * @returns true si l'utilisateur peut partager, false sinon
 */
export const canUserShareAd = (userRole: UserRole | undefined): boolean => {
  if (!userRole) return false;
  
  // Tous les utilisateurs authentifiés peuvent partager les publicités
  return true;
};

/**
 * Retourne le message d'explication pour une restriction
 * @param action - L'action tentée ('comment', 'like', 'share')
 * @param userRole - Le rôle de l'utilisateur
 * @returns Le message d'explication ou null si aucune restriction
 */
export const getAdRestrictionMessage = (
  action: 'comment' | 'like' | 'share',
  userRole: UserRole | undefined
): string | null => {
  if (!userRole) return 'Vous devez être connecté pour effectuer cette action.';
  
  switch (action) {
    case 'comment':
      if (userRole === UserRole.BUSINESS) {
        return 'Les comptes entreprise ne peuvent pas commenter les publicités. Seuls les utilisateurs réguliers peuvent interagir avec les publicités pour maintenir l\'authenticité des retours clients.';
      }
      break;
    case 'like':
    case 'share':
      // Aucune restriction pour les likes et partages
      return null;
    default:
      return null;
  }
  
  return null;
};

/**
 * Vérifie si un utilisateur est une entreprise
 * @param userRole - Le rôle de l'utilisateur
 * @returns true si l'utilisateur est une entreprise
 */
export const isBusinessUser = (userRole: UserRole | undefined): boolean => {
  return userRole === UserRole.BUSINESS;
};

/**
 * Retourne les actions autorisées pour un utilisateur sur les publicités
 * @param userRole - Le rôle de l'utilisateur
 * @returns Un objet avec les permissions
 */
export const getAdPermissions = (userRole: UserRole | undefined) => {
  return {
    canComment: canUserCommentOnAd(userRole),
    canLike: canUserLikeAd(userRole),
    canShare: canUserShareAd(userRole),
    isBusiness: isBusinessUser(userRole),
    restrictions: {
      comment: getAdRestrictionMessage('comment', userRole),
      like: getAdRestrictionMessage('like', userRole),
      share: getAdRestrictionMessage('share', userRole)
    }
  };
};
