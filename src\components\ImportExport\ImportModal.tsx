import React, { useState, useRef } from 'react';
import {
  X,
  Upload,
  FileText,
  Download,
  AlertCircle,
  CheckCircle,
  Eye,
  RefreshCw,
  FileSpreadsheet,
  Database,
  Zap,
  Settings,
  Info
} from 'lucide-react';
import { formatAmount } from '../../utils/formatUtils';
import './ImportExport.css';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  category: string;
  subcategory: string;
  brand: string;
  sku: string;
  stock: number;
  minStock: number;
  images: string[];
  tags: string[];
  status: 'active' | 'inactive' | 'out_of_stock';
}

interface ImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (products: Omit<Product, 'id'>[]) => void;
}

interface ImportError {
  row: number;
  field: string;
  message: string;
  value: any;
}

interface ImportPreview {
  valid: Omit<Product, 'id'>[];
  errors: ImportError[];
  warnings: ImportError[];
  duplicates: string[];
}

const ImportModal: React.FC<ImportModalProps> = ({ isOpen, onClose, onImport }) => {
  const [step, setStep] = useState<'upload' | 'mapping' | 'preview' | 'processing'>('upload');
  const [file, setFile] = useState<File | null>(null);
  const [csvData, setCsvData] = useState<string[][]>([]);
  const [fieldMapping, setFieldMapping] = useState<Record<string, string>>({});
  const [preview, setPreview] = useState<ImportPreview>({ valid: [], errors: [], warnings: [], duplicates: [] });
  const [isProcessing, setIsProcessing] = useState(false);
  const [importOptions, setImportOptions] = useState({
    skipDuplicates: true,
    updateExisting: false,
    validateStock: true,
    autoGenerateSku: true,
    defaultStatus: 'active' as Product['status']
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  if (!isOpen) return null;

  const requiredFields = ['name', 'price', 'category', 'brand'];
  const optionalFields = ['description', 'originalPrice', 'subcategory', 'sku', 'stock', 'minStock', 'tags', 'status'];
  const allFields = [...requiredFields, ...optionalFields];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFile = event.target.files?.[0];
    if (!uploadedFile) return;

    if (!uploadedFile.name.endsWith('.csv')) {
      alert('Veuillez sélectionner un fichier CSV');
      return;
    }

    setFile(uploadedFile);

    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      const rows = text.split('\n').map(row =>
        row.split(',').map(cell => cell.trim().replace(/^"|"$/g, ''))
      );
      setCsvData(rows.filter(row => row.some(cell => cell.length > 0)));
      setStep('mapping');
    };
    reader.readAsText(uploadedFile);
  };

  const handleFieldMapping = (csvColumn: string, productField: string) => {
    setFieldMapping(prev => ({ ...prev, [csvColumn]: productField }));
  };

  const validateAndPreview = () => {
    if (csvData.length < 2) {
      alert('Le fichier doit contenir au moins une ligne de données');
      return;
    }

    const headers = csvData[0];
    const dataRows = csvData.slice(1);

    // Vérifier que tous les champs requis sont mappés
    const mappedFields = Object.values(fieldMapping);
    const missingRequired = requiredFields.filter(field => !mappedFields.includes(field));

    if (missingRequired.length > 0) {
      alert(`Champs requis manquants: ${missingRequired.join(', ')}`);
      return;
    }

    const valid: Omit<Product, 'id'>[] = [];
    const errors: ImportError[] = [];
    const warnings: ImportError[] = [];
    const duplicates: string[] = [];
    const seenNames = new Set<string>();

    dataRows.forEach((row, index) => {
      const rowNumber = index + 2; // +2 car index commence à 0 et on skip les headers
      const product: any = {
        images: [],
        tags: []
      };

      // Mapper les données
      headers.forEach((header, colIndex) => {
        const field = fieldMapping[header];
        if (field && row[colIndex]) {
          let value = row[colIndex];

          // Conversion des types
          if (field === 'price' || field === 'originalPrice') {
            const numValue = parseFloat(value.replace(/[^\d.-]/g, ''));
            if (isNaN(numValue) || numValue <= 0) {
              errors.push({
                row: rowNumber,
                field,
                message: 'Prix invalide',
                value
              });
              return;
            }
            product[field] = numValue;
          } else if (field === 'stock' || field === 'minStock') {
            const numValue = parseInt(value);
            if (isNaN(numValue) || numValue < 0) {
              errors.push({
                row: rowNumber,
                field,
                message: 'Quantité invalide',
                value
              });
              return;
            }
            product[field] = numValue;
          } else if (field === 'tags') {
            product[field] = value.split(';').map(tag => tag.trim()).filter(tag => tag);
          } else {
            product[field] = value;
          }
        }
      });

      // Validation des champs requis
      requiredFields.forEach(field => {
        if (!product[field]) {
          errors.push({
            row: rowNumber,
            field,
            message: 'Champ requis manquant',
            value: ''
          });
        }
      });

      // Vérification des doublons
      if (product.name && seenNames.has(product.name.toLowerCase())) {
        duplicates.push(product.name);
        warnings.push({
          row: rowNumber,
          field: 'name',
          message: 'Nom de produit en double',
          value: product.name
        });
      } else if (product.name) {
        seenNames.add(product.name.toLowerCase());
      }

      // Valeurs par défaut
      if (!product.sku && importOptions.autoGenerateSku) {
        product.sku = `PRD-${Date.now()}-${index}`;
      }

      if (!product.status) {
        product.status = importOptions.defaultStatus;
      }

      if (!product.stock) {
        product.stock = 0;
      }

      if (!product.minStock) {
        product.minStock = 5;
      }

      // Validation du stock
      if (importOptions.validateStock && product.stock < 0) {
        warnings.push({
          row: rowNumber,
          field: 'stock',
          message: 'Stock négatif détecté',
          value: product.stock
        });
      }

      // Si pas d'erreurs critiques, ajouter aux produits valides
      const rowErrors = errors.filter(e => e.row === rowNumber);
      if (rowErrors.length === 0 && product.name && product.price && product.category && product.brand) {
        valid.push(product);
      }
    });

    setPreview({ valid, errors, warnings, duplicates });
    setStep('preview');
  };

  const handleImport = async () => {
    setIsProcessing(true);

    try {
      // Simulation du traitement
      await new Promise(resolve => setTimeout(resolve, 2000));

      let productsToImport = preview.valid;

      if (importOptions.skipDuplicates) {
        productsToImport = productsToImport.filter(p => !preview.duplicates.includes(p.name));
      }

      onImport(productsToImport);
      onClose();
      resetModal();
    } catch (error) {
      alert('Erreur lors de l\'importation');
    } finally {
      setIsProcessing(false);
    }
  };

  const resetModal = () => {
    setStep('upload');
    setFile(null);
    setCsvData([]);
    setFieldMapping({});
    setPreview({ valid: [], errors: [], warnings: [], duplicates: [] });
    setIsProcessing(false);
  };

  const downloadTemplate = () => {
    const headers = ['name', 'description', 'price', 'originalPrice', 'category', 'subcategory', 'brand', 'sku', 'stock', 'minStock', 'tags', 'status'];
    const sampleData = [
      'Huile de beauté Olgane', 'Huile naturelle aux extraits de plantes', '15000', '18000', 'Beauté', 'Soins du visage', 'Olgane', 'OLG-HB-001', '45', '10', 'naturel;bio;hydratant', 'active'
    ];

    const csvContent = [headers.join(','), sampleData.join(',')].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'template-import-produits.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="modal-overlay">
      <div className="modal-container large">
        <div className="modal-header">
          <div className="modal-title">
            <Upload size={24} />
            <h2>Importer des produits</h2>
            <div className="import-steps">
              <span className={step === 'upload' ? 'active' : step === 'mapping' || step === 'preview' || step === 'processing' ? 'completed' : ''}>1</span>
              <span className={step === 'mapping' ? 'active' : step === 'preview' || step === 'processing' ? 'completed' : ''}>2</span>
              <span className={step === 'preview' ? 'active' : step === 'processing' ? 'completed' : ''}>3</span>
              <span className={step === 'processing' ? 'active' : ''}>4</span>
            </div>
          </div>
          <button className="modal-close" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="modal-content">
          {step === 'upload' && (
            <div className="upload-step">
              <div className="upload-info">
                <h3>Étape 1: Sélectionner le fichier</h3>
                <p>Importez vos produits depuis un fichier CSV. Téléchargez notre modèle pour commencer.</p>
              </div>

              <div className="template-download">
                <button className="btn-template" onClick={downloadTemplate}>
                  <Download size={16} />
                  Télécharger le modèle CSV
                </button>
                <small>Utilisez ce modèle pour formater correctement vos données</small>
              </div>

              <div className="file-upload-area">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv"
                  onChange={handleFileUpload}
                  style={{ display: 'none' }}
                />

                <div className="upload-zone" onClick={() => fileInputRef.current?.click()}>
                  <FileSpreadsheet size={48} />
                  <h4>Glissez votre fichier CSV ici</h4>
                  <p>ou cliquez pour sélectionner</p>
                  <small>Formats supportés: CSV (max 10MB)</small>
                </div>

                {file && (
                  <div className="file-info">
                    <FileText size={16} />
                    <span>{file.name}</span>
                    <span className="file-size">({(file.size / 1024).toFixed(1)} KB)</span>
                  </div>
                )}
              </div>

              <div className="import-options">
                <h4>Options d'importation</h4>
                <div className="options-grid">
                  <label className="option-item">
                    <input
                      type="checkbox"
                      checked={importOptions.skipDuplicates}
                      onChange={(e) => setImportOptions(prev => ({ ...prev, skipDuplicates: e.target.checked }))}
                    />
                    <span>Ignorer les doublons</span>
                  </label>

                  <label className="option-item">
                    <input
                      type="checkbox"
                      checked={importOptions.updateExisting}
                      onChange={(e) => setImportOptions(prev => ({ ...prev, updateExisting: e.target.checked }))}
                    />
                    <span>Mettre à jour les existants</span>
                  </label>

                  <label className="option-item">
                    <input
                      type="checkbox"
                      checked={importOptions.validateStock}
                      onChange={(e) => setImportOptions(prev => ({ ...prev, validateStock: e.target.checked }))}
                    />
                    <span>Valider les stocks</span>
                  </label>

                  <label className="option-item">
                    <input
                      type="checkbox"
                      checked={importOptions.autoGenerateSku}
                      onChange={(e) => setImportOptions(prev => ({ ...prev, autoGenerateSku: e.target.checked }))}
                    />
                    <span>Générer les SKU automatiquement</span>
                  </label>
                </div>
              </div>
            </div>
          )}

          {step === 'mapping' && (
            <div className="mapping-step">
              <div className="mapping-info">
                <h3>Étape 2: Mapper les colonnes</h3>
                <p>Associez les colonnes de votre fichier aux champs des produits</p>
              </div>

              <div className="mapping-container">
                <div className="csv-preview">
                  <h4>Aperçu du fichier ({csvData.length - 1} lignes)</h4>
                  <div className="csv-table">
                    <table>
                      <thead>
                        <tr>
                          {csvData[0]?.map((header, index) => (
                            <th key={index}>{header}</th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {csvData.slice(1, 4).map((row, index) => (
                          <tr key={index}>
                            {row.map((cell, cellIndex) => (
                              <td key={cellIndex}>{cell}</td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                <div className="field-mapping">
                  <h4>Correspondance des champs</h4>
                  <div className="mapping-list">
                    {csvData[0]?.map((header, index) => (
                      <div key={index} className="mapping-item">
                        <div className="csv-column">
                          <strong>{header}</strong>
                          <small>{csvData[1]?.[index] || 'Exemple non disponible'}</small>
                        </div>
                        <div className="arrow">→</div>
                        <div className="product-field">
                          <select
                            value={fieldMapping[header] || ''}
                            onChange={(e) => handleFieldMapping(header, e.target.value)}
                          >
                            <option value="">Ignorer cette colonne</option>
                            <optgroup label="Champs requis">
                              {requiredFields.map(field => (
                                <option key={field} value={field}>
                                  {field} *
                                </option>
                              ))}
                            </optgroup>
                            <optgroup label="Champs optionnels">
                              {optionalFields.map(field => (
                                <option key={field} value={field}>
                                  {field}
                                </option>
                              ))}
                            </optgroup>
                          </select>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="mapping-actions">
                <button className="btn-secondary" onClick={() => setStep('upload')}>
                  Retour
                </button>
                <button className="btn-primary" onClick={validateAndPreview}>
                  <Eye size={16} />
                  Prévisualiser
                </button>
              </div>
            </div>
          )}

          {step === 'preview' && (
            <div className="preview-step">
              <div className="preview-info">
                <h3>Étape 3: Vérification et prévisualisation</h3>
                <p>Vérifiez les données avant l'importation finale</p>
              </div>

              <div className="preview-stats">
                <div className="stat-card success">
                  <CheckCircle size={20} />
                  <div>
                    <strong>{preview.valid.length}</strong>
                    <span>Produits valides</span>
                  </div>
                </div>

                <div className="stat-card error">
                  <AlertCircle size={20} />
                  <div>
                    <strong>{preview.errors.length}</strong>
                    <span>Erreurs</span>
                  </div>
                </div>

                <div className="stat-card warning">
                  <Info size={20} />
                  <div>
                    <strong>{preview.warnings.length}</strong>
                    <span>Avertissements</span>
                  </div>
                </div>
              </div>

              {preview.errors.length > 0 && (
                <div className="errors-section">
                  <h4>Erreurs à corriger</h4>
                  <div className="errors-list">
                    {preview.errors.slice(0, 10).map((error, index) => (
                      <div key={index} className="error-item">
                        <AlertCircle size={16} />
                        <span>Ligne {error.row}: {error.message} ({error.field})</span>
                      </div>
                    ))}
                    {preview.errors.length > 10 && (
                      <div className="more-errors">
                        +{preview.errors.length - 10} autres erreurs...
                      </div>
                    )}
                  </div>
                </div>
              )}

              {preview.warnings.length > 0 && (
                <div className="warnings-section">
                  <h4>Avertissements</h4>
                  <div className="warnings-list">
                    {preview.warnings.slice(0, 5).map((warning, index) => (
                      <div key={index} className="warning-item">
                        <Info size={16} />
                        <span>Ligne {warning.row}: {warning.message} ({warning.field})</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="preview-actions">
                <button className="btn-secondary" onClick={() => setStep('mapping')}>
                  Retour
                </button>
                <button
                  className="btn-primary"
                  onClick={handleImport}
                  disabled={preview.valid.length === 0}
                >
                  <Database size={16} />
                  Importer {preview.valid.length} produit(s)
                </button>
              </div>
            </div>
          )}

          {step === 'processing' && (
            <div className="processing-step">
              <div className="processing-animation">
                <RefreshCw size={48} className="spinning" />
                <h3>Importation en cours...</h3>
                <p>Traitement de {preview.valid.length} produits</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImportModal;
