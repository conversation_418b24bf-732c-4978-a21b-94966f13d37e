import { supabase } from '../lib/supabase';
import { IProduct, IUser, IBusinessUser } from '../types';

// Types pour les vraies données
export interface RealSale {
  id: string;
  order_id: string;
  buyer_id: string;
  seller_id: string;
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  commission: number;
  net_amount: number;
  status: 'completed' | 'pending' | 'cancelled' | 'refunded';
  payment_method: string;
  created_at: string;
  updated_at: string;
  // Relations
  product?: IProduct;
  buyer?: IUser;
  seller?: IBusinessUser;
}

export interface RealSalesStats {
  totalRevenue: number;
  totalCommissions: number;
  totalSales: number;
  averageOrderValue: number;
  monthlyGrowth: number;
  topProduct: string;
  topClient: string;
  conversionRate: number;
  repeatCustomers: number;
  newCustomers: number;
}

export interface RealProductStats {
  totalProducts: number;
  activeProducts: number;
  totalRevenue: number;
  averagePrice: number;
  topCategory: string;
  lowStockCount: number;
  trendingCount: number;
  conversionRate: number;
}

// Service pour les ventes
export class SalesDataService {
  // Récupérer les ventes d'un business
  static async getSalesByBusinessId(businessId: string): Promise<RealSale[]> {
    try {
      const { data: orders, error } = await supabase
        .from('orders')
        .select(`
          *,
          product:products(*),
          buyer:profiles!orders_buyer_id_fkey(*),
          seller:profiles!orders_seller_id_fkey(*)
        `)
        .eq('seller_id', businessId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erreur lors de la récupération des ventes:', error);
        return [];
      }

      return orders?.map(order => ({
        id: order.id,
        order_id: order.id,
        buyer_id: order.buyer_id,
        seller_id: order.seller_id,
        product_id: order.product_id,
        quantity: order.quantity,
        unit_price: order.product?.price || 0,
        total_price: order.total_price,
        commission: Math.round(order.total_price * 0.05), // 5% de commission
        net_amount: Math.round(order.total_price * 0.95),
        status: order.status as RealSale['status'],
        payment_method: 'Mobile Money', // Par défaut
        created_at: order.created_at,
        updated_at: order.updated_at,
        product: order.product,
        buyer: order.buyer,
        seller: order.seller
      })) || [];
    } catch (error) {
      console.error('Erreur dans getSalesByBusinessId:', error);
      return [];
    }
  }

  // Calculer les statistiques de vente
  static async calculateSalesStats(businessId: string): Promise<RealSalesStats> {
    try {
      const sales = await this.getSalesByBusinessId(businessId);
      
      if (sales.length === 0) {
        return {
          totalRevenue: 0,
          totalCommissions: 0,
          totalSales: 0,
          averageOrderValue: 0,
          monthlyGrowth: 0,
          topProduct: '',
          topClient: '',
          conversionRate: 0,
          repeatCustomers: 0,
          newCustomers: 0
        };
      }

      // Calculs des statistiques
      const totalRevenue = sales.reduce((sum, sale) => sum + sale.total_price, 0);
      const totalCommissions = sales.reduce((sum, sale) => sum + sale.commission, 0);
      const totalSales = sales.length;
      const averageOrderValue = totalRevenue / totalSales;

      // Top produit
      const productSales = new Map();
      sales.forEach(sale => {
        const productName = sale.product?.name || 'Produit inconnu';
        productSales.set(productName, (productSales.get(productName) || 0) + sale.quantity);
      });
      const topProduct = Array.from(productSales.entries())
        .sort((a, b) => b[1] - a[1])[0]?.[0] || '';

      // Top client
      const clientSales = new Map();
      sales.forEach(sale => {
        const clientName = sale.buyer?.username || 'Client inconnu';
        clientSales.set(clientName, (clientSales.get(clientName) || 0) + sale.total_price);
      });
      const topClient = Array.from(clientSales.entries())
        .sort((a, b) => b[1] - a[1])[0]?.[0] || '';

      // Croissance mensuelle (simulation basée sur les données)
      const currentMonth = new Date().getMonth();
      const currentMonthSales = sales.filter(sale => 
        new Date(sale.created_at).getMonth() === currentMonth
      );
      const previousMonthSales = sales.filter(sale => 
        new Date(sale.created_at).getMonth() === currentMonth - 1
      );
      
      const currentRevenue = currentMonthSales.reduce((sum, sale) => sum + sale.total_price, 0);
      const previousRevenue = previousMonthSales.reduce((sum, sale) => sum + sale.total_price, 0);
      const monthlyGrowth = previousRevenue > 0 
        ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 
        : 0;

      // Clients récurrents vs nouveaux
      const uniqueClients = new Set(sales.map(sale => sale.buyer_id));
      const clientPurchaseCounts = new Map();
      sales.forEach(sale => {
        const count = clientPurchaseCounts.get(sale.buyer_id) || 0;
        clientPurchaseCounts.set(sale.buyer_id, count + 1);
      });
      
      const repeatCustomers = Array.from(clientPurchaseCounts.values())
        .filter(count => count > 1).length;
      const newCustomers = uniqueClients.size - repeatCustomers;

      return {
        totalRevenue,
        totalCommissions,
        totalSales,
        averageOrderValue,
        monthlyGrowth,
        topProduct,
        topClient,
        conversionRate: 75.5, // Simulation
        repeatCustomers,
        newCustomers
      };
    } catch (error) {
      console.error('Erreur dans calculateSalesStats:', error);
      return {
        totalRevenue: 0,
        totalCommissions: 0,
        totalSales: 0,
        averageOrderValue: 0,
        monthlyGrowth: 0,
        topProduct: '',
        topClient: '',
        conversionRate: 0,
        repeatCustomers: 0,
        newCustomers: 0
      };
    }
  }

  // Récupérer les ventes par période
  static async getSalesByPeriod(businessId: string, days: number = 30): Promise<RealSale[]> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data: orders, error } = await supabase
        .from('orders')
        .select(`
          *,
          product:products(*),
          buyer:profiles!orders_buyer_id_fkey(*),
          seller:profiles!orders_seller_id_fkey(*)
        `)
        .eq('seller_id', businessId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erreur lors de la récupération des ventes par période:', error);
        return [];
      }

      return orders?.map(order => ({
        id: order.id,
        order_id: order.id,
        buyer_id: order.buyer_id,
        seller_id: order.seller_id,
        product_id: order.product_id,
        quantity: order.quantity,
        unit_price: order.product?.price || 0,
        total_price: order.total_price,
        commission: Math.round(order.total_price * 0.05),
        net_amount: Math.round(order.total_price * 0.95),
        status: order.status as RealSale['status'],
        payment_method: 'Mobile Money',
        created_at: order.created_at,
        updated_at: order.updated_at,
        product: order.product,
        buyer: order.buyer,
        seller: order.seller
      })) || [];
    } catch (error) {
      console.error('Erreur dans getSalesByPeriod:', error);
      return [];
    }
  }
}

// Service pour les produits
export class ProductsDataService {
  // Récupérer les produits d'un business avec statistiques
  static async getProductsByBusinessId(businessId: string): Promise<IProduct[]> {
    try {
      const { data: products, error } = await supabase
        .from('products')
        .select('*')
        .eq('business_id', businessId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erreur lors de la récupération des produits:', error);
        return [];
      }

      return products || [];
    } catch (error) {
      console.error('Erreur dans getProductsByBusinessId:', error);
      return [];
    }
  }

  // Calculer les statistiques des produits
  static async calculateProductStats(businessId: string): Promise<RealProductStats> {
    try {
      const products = await this.getProductsByBusinessId(businessId);
      const sales = await SalesDataService.getSalesByBusinessId(businessId);

      if (products.length === 0) {
        return {
          totalProducts: 0,
          activeProducts: 0,
          totalRevenue: 0,
          averagePrice: 0,
          topCategory: '',
          lowStockCount: 0,
          trendingCount: 0,
          conversionRate: 0
        };
      }

      const totalProducts = products.length;
      const activeProducts = products.filter(p => (p.stock || 0) > 0).length;
      const totalRevenue = sales.reduce((sum, sale) => sum + sale.total_price, 0);
      const averagePrice = products.reduce((sum, p) => sum + p.price, 0) / totalProducts;

      // Top catégorie
      const categoryCount = new Map();
      products.forEach(product => {
        categoryCount.set(product.category, (categoryCount.get(product.category) || 0) + 1);
      });
      const topCategory = Array.from(categoryCount.entries())
        .sort((a, b) => b[1] - a[1])[0]?.[0] || '';

      const lowStockCount = products.filter(p => (p.stock || 0) <= 5).length;
      const trendingCount = products.filter(p => p.averageRating >= 4.5).length;

      return {
        totalProducts,
        activeProducts,
        totalRevenue,
        averagePrice,
        topCategory,
        lowStockCount,
        trendingCount,
        conversionRate: 68.3 // Simulation
      };
    } catch (error) {
      console.error('Erreur dans calculateProductStats:', error);
      return {
        totalProducts: 0,
        activeProducts: 0,
        totalRevenue: 0,
        averagePrice: 0,
        topCategory: '',
        lowStockCount: 0,
        trendingCount: 0,
        conversionRate: 0
      };
    }
  }

  // Mettre à jour un produit
  static async updateProduct(productId: string, updates: Partial<IProduct>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('products')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', productId);

      if (error) {
        console.error('Erreur lors de la mise à jour du produit:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erreur dans updateProduct:', error);
      return false;
    }
  }

  // Supprimer un produit
  static async deleteProduct(productId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', productId);

      if (error) {
        console.error('Erreur lors de la suppression du produit:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erreur dans deleteProduct:', error);
      return false;
    }
  }
}

// Service pour les classements
export class RankingsDataService {
  // Calculer les classements de produits
  static async calculateProductRankings(businessId: string) {
    try {
      // Récupérer tous les produits de la même catégorie
      const products = await ProductsDataService.getProductsByBusinessId(businessId);
      const sales = await SalesDataService.getSalesByBusinessId(businessId);

      // Calculer les classements par catégorie
      const rankings = products.map(product => {
        const productSales = sales.filter(sale => sale.product_id === product.id);
        const totalSales = productSales.reduce((sum, sale) => sum + sale.quantity, 0);
        const revenue = productSales.reduce((sum, sale) => sum + sale.total_price, 0);

        return {
          id: `RANK-${product.id}`,
          productId: product.id,
          productName: product.name,
          category: product.category,
          averageRating: product.averageRating,
          totalReviews: 0, // À implémenter avec les avis
          totalSales,
          revenue,
          score: (product.averageRating * 0.4) + (totalSales * 0.6) // Score composite
        };
      });

      // Trier par score et assigner les rangs
      rankings.sort((a, b) => b.score - a.score);
      
      return rankings.map((ranking, index) => ({
        ...ranking,
        currentRank: index + 1,
        previousRank: index + 1 + Math.floor(Math.random() * 3 - 1), // Simulation
        totalInCategory: rankings.length,
        isTopSeller: index < 3,
        isTopRated: ranking.averageRating >= 4.5,
        isTrending: Math.random() > 0.7 // Simulation
      }));
    } catch (error) {
      console.error('Erreur dans calculateProductRankings:', error);
      return [];
    }
  }
}

export default {
  SalesDataService,
  ProductsDataService,
  RankingsDataService
};
