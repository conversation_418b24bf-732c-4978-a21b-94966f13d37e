import React, { useMemo } from 'react';
import { Award, ThumbsUp, MessageSquare, ShoppingBag, Users } from 'lucide-react';
import { IBusinessUser } from '../../types';
import Card, { CardBody } from '../ui/Card';
import BusinessStatusBadge from '../ui/BusinessStatusBadge';
import { usePosts } from '../../context/PostsContext';

interface BusinessStatsProps {
  business: IBusinessUser;
}

const BusinessStats: React.FC<BusinessStatsProps> = ({ business }) => {
  const { posts: allPosts } = usePosts();

  const businessReviewsCount = useMemo(() => {
    if (!business || !allPosts) return 0;
    let count = 0;
    allPosts.forEach(post => {
      if (post.businessName === business.businessName) {
        count += post.comments?.length || 0;
      }
    });
    return count;
  }, [allPosts, business]);

  const averageRating = useMemo(() => {
    if (!business || !business.catalog || business.catalog.length === 0) return 0;
    const sum = business.catalog.reduce((acc, product) => acc + product.averageRating, 0);
    return (sum / business.catalog.length).toFixed(1);
  }, [business]);

  return (
    <div className="space-y-4">
      {/* Business Status */}
      <Card>
        <CardBody className="p-4">
          <div className="flex items-center mb-1">
            <Award className="w-5 h-5 text-blue-500 mr-2" />
            <h3 className="font-semibold text-gray-800 text-md">Statut</h3>
          </div>
          <div className="business-status-badge">
            {business.businessStatus && (
              <BusinessStatusBadge
                status={business.businessStatus}
                size="md"
                variant="default"
              />
            )}
          </div>
        </CardBody>
      </Card>

      {/* Statistics */}
      <Card>
        <CardBody className="p-4">
          <h3 className="font-semibold text-gray-800 text-md mb-3">Statistiques</h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center text-gray-600">
                <ShoppingBag className="w-4 h-4 mr-2 text-blue-500" />
                <span>Produits</span>
              </div>
              <span className="font-semibold text-gray-800">{business.catalog?.length || 0}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center text-gray-600">
                <ThumbsUp className="w-4 h-4 mr-2 text-green-500" />
                <span>Note moyenne</span>
              </div>
              <span className="font-semibold text-gray-800">{averageRating} / 5</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center text-gray-600">
                <MessageSquare className="w-4 h-4 mr-2 text-purple-500" />
                <span>Avis reçus</span>
              </div>
              <span className="font-semibold text-gray-800">{businessReviewsCount}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center text-gray-600">
                <ShoppingBag className="w-4 h-4 mr-2 text-orange-500" />
                <span>Ventes</span>
              </div>
              <span className="font-semibold text-gray-800">{business.salesCount || 0}</span>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Network */}
      <Card>
        <CardBody className="p-4">
          <h3 className="font-semibold text-gray-800 text-md mb-3">Réseau</h3>
          <div className="flex justify-around text-center">
            <div>
              <div className="text-xl font-bold text-gray-900">{business.followers_count || 0}</div>
              <div className="text-xs text-gray-500">Abonnés</div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default BusinessStats;
