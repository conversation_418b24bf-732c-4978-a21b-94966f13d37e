import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import {
  Package,
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Edit,
  Trash2,
  Eye,
  TrendingUp,
  TrendingDown,
  DollarSign,
  BarChart3,
  PieChart,
  Target,
  Star,
  Heart,
  ShoppingCart,
  Users,
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Upload,
  Download,
  ExternalLink,
  MoreHorizontal,
  Zap,
  Brain,
  Lightbulb,
  Activity,
  Award,
  Bookmark,
  Tag,
  Image,
  Camera,
  Palette,
  Settings,
  RefreshCw,
  Bell,
  Mail,
  FileText,
  ChevronDown,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import { AIChatButton } from '../components/AIChat';
import { DataToggle } from '../components/DataToggle';
import { useRealProductsData } from '../hooks/useRealData';
import { getFirstProductImage, createImageProps } from '../utils/imageUtils';
import { formatAmount, formatAmountShort } from '../utils/formatUtils';
import {
  AddProductModal,
  EditProductModal,
  ProductDetailsModal,
  DeleteProductModal
} from '../components/ProductModals';
import {
  BulkActionsModal,
  BulkActionsBar,
  BulkAction
} from '../components/BulkActions';
import {
  ImportModal,
  ExportModal
} from '../components/ImportExport';
import '../styles/BusinessProducts.css';

// Types pour les produits
interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  category: string;
  subcategory: string;
  brand: string;
  sku: string;
  stock: number;
  minStock: number;
  images: string[];
  tags: string[];
  status: 'active' | 'inactive' | 'draft' | 'out_of_stock';
  rating: number;
  reviewsCount: number;
  salesCount: number;
  revenue: number;
  createdAt: string;
  updatedAt: string;
  featured: boolean;
  trending: boolean;
  onSale: boolean;
  seoTitle?: string;
  seoDescription?: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  variants?: ProductVariant[];
}

interface ProductVariant {
  id: string;
  name: string;
  value: string;
  price: number;
  stock: number;
  sku: string;
}

interface ProductStats {
  totalProducts: number;
  activeProducts: number;
  totalRevenue: number;
  averagePrice: number;
  topCategory: string;
  lowStockCount: number;
  trendingCount: number;
  conversionRate: number;
}

interface ProductInsight {
  id: string;
  type: 'success' | 'warning' | 'info' | 'error';
  title: string;
  description: string;
  action?: string;
  priority: 'high' | 'medium' | 'low';
}

const BusinessProductsPage: React.FC = () => {
  const { currentUser } = useAuth();

  // États principaux
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [productStats, setProductStats] = useState<ProductStats>({
    totalProducts: 0,
    activeProducts: 0,
    totalRevenue: 0,
    averagePrice: 0,
    topCategory: '',
    lowStockCount: 0,
    trendingCount: 0,
    conversionRate: 0
  });

  // États pour les filtres et recherche
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priceRange, setPriceRange] = useState({ min: 0, max: 100000 });
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // États pour les modals et fonctionnalités
  const [showFilters, setShowFilters] = useState(false);
  const [showAddProduct, setShowAddProduct] = useState(false);
  const [showEditProduct, setShowEditProduct] = useState<string | null>(null);
  const [showProductDetails, setShowProductDetails] = useState<string | null>(null);
  const [showDeleteProduct, setShowDeleteProduct] = useState<string | null>(null);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [showBulkActionsModal, setShowBulkActionsModal] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showInsights, setShowInsights] = useState(true);

  // États pour les vraies données
  const [useRealData, setUseRealData] = useState(false);
  const realProductsData = useRealProductsData();
  const [dataLoading, setDataLoading] = useState(false);

  // États pour les insights et analytics
  const [productInsights, setProductInsights] = useState<ProductInsight[]>([]);
  const [showAnalytics, setShowAnalytics] = useState(false);

  // Données fictives réalistes pour les produits
  useEffect(() => {
    const mockProducts: Product[] = [
      {
        id: 'PRD-001',
        name: 'Olgane - Huile de beauté naturelle',
        description: 'Huile de beauté 100% naturelle aux extraits de plantes africaines. Nourrit et hydrate la peau en profondeur.',
        price: 15000,
        originalPrice: 18000,
        category: 'Beauté',
        subcategory: 'Soins du visage',
        brand: 'Olgane',
        sku: 'OLG-HB-001',
        stock: 45,
        minStock: 10,
        images: ['https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400&h=400&fit=crop&crop=center', 'https://images.unsplash.com/photo-1571781926291-c477ebfd024b?w=400&h=400&fit=crop&crop=center'],
        tags: ['naturel', 'bio', 'hydratant', 'anti-âge'],
        status: 'active',
        rating: 4.8,
        reviewsCount: 127,
        salesCount: 89,
        revenue: 1335000,
        createdAt: '2024-11-15',
        updatedAt: '2025-01-20',
        featured: true,
        trending: true,
        onSale: true,
        seoTitle: 'Huile de beauté naturelle Olgane - Soins visage bio',
        seoDescription: 'Découvrez l\'huile de beauté Olgane, 100% naturelle...',
        weight: 50,
        dimensions: { length: 5, width: 5, height: 12 }
      },
      {
        id: 'PRD-002',
        name: 'Crème hydratante Shea Butter',
        description: 'Crème hydratante enrichie au beurre de karité pur. Idéale pour les peaux sèches et sensibles.',
        price: 12000,
        category: 'Cosmétiques',
        subcategory: 'Hydratation',
        brand: 'AfriCare',
        sku: 'AFC-CH-002',
        stock: 32,
        minStock: 15,
        images: ['https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=400&h=400&fit=crop&crop=center'],
        tags: ['karité', 'hydratant', 'peaux sèches', 'naturel'],
        status: 'active',
        rating: 4.6,
        reviewsCount: 89,
        salesCount: 67,
        revenue: 804000,
        createdAt: '2024-12-01',
        updatedAt: '2025-01-18',
        featured: false,
        trending: false,
        onSale: false,
        weight: 100,
        dimensions: { length: 8, width: 8, height: 6 }
      },
      {
        id: 'PRD-003',
        name: 'Savon noir traditionnel',
        description: 'Savon noir artisanal fabriqué selon les méthodes traditionnelles. Nettoie et purifie en douceur.',
        price: 3500,
        category: 'Hygiène',
        subcategory: 'Nettoyage',
        brand: 'Tradition',
        sku: 'TRD-SN-003',
        stock: 78,
        minStock: 20,
        images: ['https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center', 'https://images.unsplash.com/photo-1586495777744-4413f21062fa?w=400&h=400&fit=crop&crop=center'],
        tags: ['traditionnel', 'artisanal', 'purifiant', 'économique'],
        status: 'active',
        rating: 4.4,
        reviewsCount: 156,
        salesCount: 234,
        revenue: 819000,
        createdAt: '2024-10-20',
        updatedAt: '2025-01-15',
        featured: false,
        trending: true,
        onSale: false,
        weight: 200,
        dimensions: { length: 10, width: 6, height: 3 }
      },
      {
        id: 'PRD-004',
        name: 'Sérum anti-âge Vitamine C',
        description: 'Sérum concentré en vitamine C pour lutter contre les signes de l\'âge. Éclat et fermeté garantis.',
        price: 25000,
        originalPrice: 30000,
        category: 'Beauté',
        subcategory: 'Anti-âge',
        brand: 'GlowUp',
        sku: 'GLW-SA-004',
        stock: 18,
        minStock: 5,
        images: ['https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop&crop=center'],
        tags: ['anti-âge', 'vitamine C', 'éclat', 'premium'],
        status: 'active',
        rating: 4.9,
        reviewsCount: 73,
        salesCount: 45,
        revenue: 1125000,
        createdAt: '2024-12-10',
        updatedAt: '2025-01-22',
        featured: true,
        trending: true,
        onSale: true,
        weight: 30,
        dimensions: { length: 3, width: 3, height: 10 }
      },
      {
        id: 'PRD-005',
        name: 'Masque purifiant argile verte',
        description: 'Masque à l\'argile verte pour purifier et matifier la peau. Idéal pour les peaux mixtes à grasses.',
        price: 8000,
        category: 'Soins',
        subcategory: 'Masques',
        brand: 'PureSkin',
        sku: 'PRS-MP-005',
        stock: 5,
        minStock: 10,
        images: ['https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400&h=400&fit=crop&crop=center'],
        tags: ['argile verte', 'purifiant', 'matifiant', 'peaux grasses'],
        status: 'active',
        rating: 4.3,
        reviewsCount: 92,
        salesCount: 78,
        revenue: 624000,
        createdAt: '2024-11-25',
        updatedAt: '2025-01-10',
        featured: false,
        trending: false,
        onSale: false,
        weight: 150,
        dimensions: { length: 7, width: 7, height: 5 }
      },
      {
        id: 'PRD-006',
        name: 'Parfum floral Jasmin',
        description: 'Parfum délicat aux notes de jasmin et de fleurs blanches. Élégance et fraîcheur au quotidien.',
        price: 35000,
        category: 'Parfums',
        subcategory: 'Femme',
        brand: 'Essence',
        sku: 'ESS-PF-006',
        stock: 0,
        minStock: 8,
        images: ['https://images.unsplash.com/photo-1541643600914-78b084683601?w=400&h=400&fit=crop&crop=center'],
        tags: ['jasmin', 'floral', 'élégant', 'longue tenue'],
        status: 'out_of_stock',
        rating: 4.7,
        reviewsCount: 64,
        salesCount: 32,
        revenue: 1120000,
        createdAt: '2024-09-15',
        updatedAt: '2025-01-05',
        featured: true,
        trending: false,
        onSale: false,
        weight: 100,
        dimensions: { length: 4, width: 4, height: 15 }
      }
    ];

    setProducts(mockProducts);
    setFilteredProducts(mockProducts);

    // Calcul des statistiques
    const stats: ProductStats = {
      totalProducts: mockProducts.length,
      activeProducts: mockProducts.filter(p => p.status === 'active').length,
      totalRevenue: mockProducts.reduce((sum, p) => sum + p.revenue, 0),
      averagePrice: mockProducts.reduce((sum, p) => sum + p.price, 0) / mockProducts.length,
      topCategory: 'Beauté',
      lowStockCount: mockProducts.filter(p => p.stock <= p.minStock).length,
      trendingCount: mockProducts.filter(p => p.trending).length,
      conversionRate: 12.5
    };
    setProductStats(stats);

    // Insights fictifs
    const insights: ProductInsight[] = [
      {
        id: '1',
        type: 'success',
        title: 'Produit star identifié',
        description: 'L\'huile Olgane génère 35% de votre CA avec un taux de conversion de 18%',
        action: 'Augmenter le stock et créer des variantes',
        priority: 'high'
      },
      {
        id: '2',
        type: 'warning',
        title: 'Stock faible détecté',
        description: '2 produits sont en rupture de stock et 1 produit atteint le seuil minimum',
        action: 'Réapprovisionner rapidement',
        priority: 'high'
      },
      {
        id: '3',
        type: 'info',
        title: 'Optimisation des prix',
        description: 'Vos produits beauté peuvent supporter une hausse de 15% selon l\'analyse concurrentielle',
        action: 'Tester une augmentation progressive',
        priority: 'medium'
      }
    ];
    setProductInsights(insights);
  }, []);

  // Fonction de filtrage et recherche
  useEffect(() => {
    let filtered = [...products];

    // Recherche
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filtre par catégorie
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(product => product.category === categoryFilter);
    }

    // Filtre par statut
    if (statusFilter !== 'all') {
      filtered = filtered.filter(product => product.status === statusFilter);
    }

    // Filtre par prix
    filtered = filtered.filter(product =>
      product.price >= priceRange.min && product.price <= priceRange.max
    );

    // Tri
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'price':
          aValue = a.price;
          bValue = b.price;
          break;
        case 'stock':
          aValue = a.stock;
          bValue = b.stock;
          break;
        case 'rating':
          aValue = a.rating;
          bValue = b.rating;
          break;
        case 'salesCount':
          aValue = a.salesCount;
          bValue = b.salesCount;
          break;
        case 'revenue':
          aValue = a.revenue;
          bValue = b.revenue;
          break;
        case 'createdAt':
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredProducts(filtered);
  }, [products, searchTerm, categoryFilter, statusFilter, priceRange, sortBy, sortOrder]);



  // Fonction pour formater les dates
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Fonction pour obtenir la couleur du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'status-active';
      case 'inactive': return 'status-inactive';
      case 'draft': return 'status-draft';
      case 'out_of_stock': return 'status-out-of-stock';
      default: return 'status-active';
    }
  };

  // Fonction pour obtenir le texte du statut
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Actif';
      case 'inactive': return 'Inactif';
      case 'draft': return 'Brouillon';
      case 'out_of_stock': return 'Rupture';
      default: return status;
    }
  };

  // Fonction pour obtenir la classe CSS du stock
  const getStockClass = (product: Product) => {
    if (product.stock === 0) return 'stock-out';
    if (product.stock <= product.minStock) return 'stock-low';
    return 'stock-good';
  };

  // Fonction pour obtenir le texte du stock
  const getStockText = (product: Product) => {
    if (product.stock === 0) return 'Rupture';
    if (product.stock <= product.minStock) return `Stock faible (${product.stock})`;
    return `En stock (${product.stock})`;
  };

  // Fonctions d'export avancées
  const handleExportCatalog = () => {
    // Simulation d'export PDF catalogue
    const catalogData = {
      title: 'Catalogue Produits - Janvier 2025',
      business: currentUser?.username || 'Mon Business',
      products: filteredProducts.map(product => ({
        name: product.name,
        description: product.description,
        price: product.price,
        category: product.category,
        brand: product.brand,
        rating: product.rating,
        stock: product.stock,
        images: product.images
      })),
      stats: productStats,
      generatedAt: new Date().toISOString()
    };

    console.log('📄 Catalogue PDF généré:', catalogData);

    // Simulation du téléchargement
    const blob = new Blob([JSON.stringify(catalogData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `catalogue-produits-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    alert('📄 Catalogue PDF généré avec succès ! (Simulation avec fichier JSON)');
  };

  const handleExportInventory = () => {
    // Simulation d'export Excel inventaire
    const inventoryData = filteredProducts.map(product => ({
      'SKU': product.sku,
      'Nom': product.name,
      'Catégorie': product.category,
      'Marque': product.brand,
      'Prix': product.price,
      'Prix Original': product.originalPrice || '',
      'Stock': product.stock,
      'Stock Min': product.minStock,
      'Statut': product.status,
      'Note': product.rating,
      'Avis': product.reviewsCount,
      'Ventes': product.salesCount,
      'CA': product.revenue,
      'Featured': product.featured ? 'Oui' : 'Non',
      'Trending': product.trending ? 'Oui' : 'Non',
      'Promo': product.onSale ? 'Oui' : 'Non',
      'Créé le': product.createdAt,
      'Modifié le': product.updatedAt
    }));

    console.log('📊 Inventaire Excel généré:', inventoryData);

    // Simulation du téléchargement CSV
    const csvContent = [
      Object.keys(inventoryData[0]).join(','),
      ...inventoryData.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `inventaire-produits-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    alert('📊 Inventaire Excel exporté avec succès ! (Simulation avec fichier CSV)');
  };

  // Fonction pour gérer la sélection multiple
  const handleProductSelect = (productId: string) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  // Fonction pour sélectionner tous les produits
  const handleSelectAll = () => {
    if (selectedProducts.length === filteredProducts.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(filteredProducts.map(p => p.id));
    }
  };

  // Fonctions pour les actions bulk avancées
  const handleBulkEdit = () => {
    if (selectedProducts.length === 0) {
      alert('Veuillez sélectionner au moins un produit');
      return;
    }
    setShowBulkActionsModal(true);
  };

  const handleBulkDelete = () => {
    if (selectedProducts.length === 0) {
      alert('Veuillez sélectionner au moins un produit');
      return;
    }

    const selectedNames = products
      .filter(p => selectedProducts.includes(p.id))
      .map(p => p.name)
      .join(', ');

    if (confirm(`Êtes-vous sûr de vouloir supprimer ${selectedProducts.length} produit(s)?\n\n${selectedNames}`)) {
      // Suppression avec mise à jour des stats
      const deletedProducts = products.filter(p => selectedProducts.includes(p.id));
      const updatedProducts = products.filter(p => !selectedProducts.includes(p.id));

      setProducts(updatedProducts);
      setSelectedProducts([]);

      // Recalculer les stats
      const activeDeleted = deletedProducts.filter(p => p.status === 'active').length;
      const newStats = {
        ...productStats,
        totalProducts: productStats.totalProducts - deletedProducts.length,
        activeProducts: productStats.activeProducts - activeDeleted
      };
      setProductStats(newStats);

      alert(`${deletedProducts.length} produit(s) supprimé(s) avec succès !`);
    }
  };

  const handleApplyBulkActions = async (action: BulkAction) => {
    const selectedProductsData = products.filter(p => selectedProducts.includes(p.id));

    try {
      let updatedProducts = [...products];

      switch (action.type) {
        case 'price':
          updatedProducts = products.map(product => {
            if (!selectedProducts.includes(product.id)) return product;

            let newPrice = product.price;
            if (action.data.priceType === 'fixed') {
              newPrice = action.data.newPrice;
            } else if (action.data.priceType === 'percentage') {
              newPrice = product.price * (1 + action.data.percentage / 100);
            }

            return {
              ...product,
              price: Math.round(newPrice),
              updatedAt: new Date().toISOString().split('T')[0]
            };
          });
          break;

        case 'category':
          updatedProducts = products.map(product =>
            selectedProducts.includes(product.id)
              ? { ...product, category: action.data.newCategory, updatedAt: new Date().toISOString().split('T')[0] }
              : product
          );
          break;

        case 'status':
          updatedProducts = products.map(product =>
            selectedProducts.includes(product.id)
              ? { ...product, status: action.data.newStatus, updatedAt: new Date().toISOString().split('T')[0] }
              : product
          );
          break;

        case 'stock':
          updatedProducts = products.map(product => {
            if (!selectedProducts.includes(product.id)) return product;

            let newStock = product.stock;
            switch (action.data.stockAction) {
              case 'set':
                newStock = action.data.stockValue;
                break;
              case 'add':
                newStock = product.stock + action.data.stockValue;
                break;
              case 'subtract':
                newStock = Math.max(0, product.stock - action.data.stockValue);
                break;
              case 'reset':
                newStock = 0;
                break;
            }

            return {
              ...product,
              stock: newStock,
              status: newStock === 0 ? 'out_of_stock' : product.status,
              updatedAt: new Date().toISOString().split('T')[0]
            };
          });
          break;

        case 'discount':
          updatedProducts = products.map(product => {
            if (!selectedProducts.includes(product.id)) return product;

            const originalPrice = product.originalPrice || product.price;
            let newPrice = product.price;

            if (action.data.discountType === 'percentage') {
              newPrice = originalPrice * (1 - action.data.discountValue / 100);
            } else {
              newPrice = originalPrice - action.data.discountValue;
            }

            return {
              ...product,
              price: Math.round(Math.max(100, newPrice)), // Prix minimum 100 F
              originalPrice: originalPrice,
              onSale: true,
              updatedAt: new Date().toISOString().split('T')[0]
            };
          });
          break;

        case 'delete':
          const deletedProducts = products.filter(p => selectedProducts.includes(p.id));
          updatedProducts = products.filter(p => !selectedProducts.includes(p.id));

          // Recalculer les stats
          const activeDeleted = deletedProducts.filter(p => p.status === 'active').length;
          const newStats = {
            ...productStats,
            totalProducts: productStats.totalProducts - deletedProducts.length,
            activeProducts: productStats.activeProducts - activeDeleted
          };
          setProductStats(newStats);
          break;
      }

      setProducts(updatedProducts);
      setSelectedProducts([]);
      setShowBulkActionsModal(false);

      // Messages de succès personnalisés
      const actionMessages = {
        price: `Prix mis à jour pour ${selectedProductsData.length} produit(s)`,
        category: `Catégorie modifiée pour ${selectedProductsData.length} produit(s)`,
        status: `Statut mis à jour pour ${selectedProductsData.length} produit(s)`,
        stock: `Stock mis à jour pour ${selectedProductsData.length} produit(s)`,
        discount: `Remise appliquée à ${selectedProductsData.length} produit(s)`,
        delete: `${selectedProductsData.length} produit(s) supprimé(s)`
      };

      alert(actionMessages[action.type] + ' avec succès !');

    } catch (error) {
      alert('Erreur lors de l\'application des actions en lot');
      console.error('Bulk action error:', error);
    }
  };

  // Fonctions pour les modals
  const handleAddProduct = (productData: Omit<Product, 'id'>) => {
    const newProduct: Product = {
      ...productData,
      id: `PRD-${Date.now()}`,
      rating: 0,
      reviewsCount: 0,
      salesCount: 0,
      revenue: 0,
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0],
      featured: false,
      trending: false,
      onSale: productData.originalPrice ? productData.price < productData.originalPrice : false
    };

    setProducts(prev => [newProduct, ...prev]);
    setShowAddProduct(false);

    // Recalculer les stats
    const newStats = {
      ...productStats,
      totalProducts: products.length + 1,
      activeProducts: newProduct.status === 'active' ? productStats.activeProducts + 1 : productStats.activeProducts
    };
    setProductStats(newStats);

    alert(`Produit "${newProduct.name}" ajouté avec succès !`);
  };

  const handleEditProduct = (productId: string, updates: Partial<Product>) => {
    setProducts(prev => prev.map(product =>
      product.id === productId
        ? { ...product, ...updates, updatedAt: new Date().toISOString().split('T')[0] }
        : product
    ));
    setShowEditProduct(null);
    alert('Produit modifié avec succès !');
  };

  const handleViewProduct = (productId: string) => {
    setShowProductDetails(productId);
  };

  const handleDeleteProduct = (productId: string) => {
    setShowDeleteProduct(productId);
  };

  const handleConfirmDelete = (productId: string) => {
    const product = products.find(p => p.id === productId);
    if (product) {
      setProducts(prev => prev.filter(p => p.id !== productId));
      setSelectedProducts(prev => prev.filter(id => id !== productId));

      // Recalculer les stats
      const newStats = {
        ...productStats,
        totalProducts: products.length - 1,
        activeProducts: product.status === 'active' ? productStats.activeProducts - 1 : productStats.activeProducts
      };
      setProductStats(newStats);

      alert(`Produit "${product.name}" supprimé avec succès !`);
    }
    setShowDeleteProduct(null);
  };

  // Fonctions pour l'import/export
  const handleImportProducts = (importedProducts: Omit<Product, 'id'>[]) => {
    const newProducts: Product[] = importedProducts.map((productData, index) => ({
      ...productData,
      id: `IMP-${Date.now()}-${index}`,
      rating: 0,
      reviewsCount: 0,
      salesCount: 0,
      revenue: 0,
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0],
      featured: false,
      trending: false,
      onSale: productData.originalPrice ? productData.price < productData.originalPrice : false
    }));

    setProducts(prev => [...newProducts, ...prev]);

    // Recalculer les stats
    const activeImported = newProducts.filter(p => p.status === 'active').length;
    const newStats = {
      ...productStats,
      totalProducts: productStats.totalProducts + newProducts.length,
      activeProducts: productStats.activeProducts + activeImported
    };
    setProductStats(newStats);

    alert(`${newProducts.length} produit(s) importé(s) avec succès !`);
    setShowImportModal(false);
  };

  const handleExportProducts = () => {
    setShowExportModal(true);
    setShowExportMenu(false);
  };

  // Fonction pour basculer les favoris
  const handleToggleFavorite = (productId: string) => {
    const updatedProducts = products.map(product =>
      product.id === productId
        ? { ...product, featured: !product.featured }
        : product
    );
    setProducts(updatedProducts);

    const product = products.find(p => p.id === productId);
    const action = product?.featured ? 'retiré des' : 'ajouté aux';
    alert(`Produit ${action} favoris !`);
  };

  // Fonction pour basculer entre données fictives et vraies données
  const handleDataToggle = (useReal: boolean) => {
    setUseRealData(useReal);
    setDataLoading(true);

    if (useReal && realProductsData.products && realProductsData.productStats) {
      // Convertir les vraies données au format attendu
      const convertedProducts: Product[] = realProductsData.products.map(realProduct => ({
        id: realProduct.id,
        name: realProduct.name,
        description: realProduct.description || 'Description non disponible',
        price: realProduct.price,
        originalPrice: undefined,
        category: realProduct.category,
        subcategory: realProduct.subcategory || '',
        brand: realProduct.brand || 'Marque inconnue',
        sku: realProduct.id, // Utiliser l'ID comme SKU temporaire
        stock: realProduct.stock || 0,
        minStock: 5, // Valeur par défaut
        images: realProduct.images || [],
        tags: realProduct.tags || [],
        status: (realProduct.stock || 0) > 0 ? 'active' : 'out_of_stock',
        rating: realProduct.averageRating || 0,
        reviewsCount: realProduct.reviewsCount || 0,
        salesCount: 0, // À calculer depuis les ventes
        revenue: 0, // À calculer depuis les ventes
        createdAt: realProduct.createdAt || new Date().toISOString().split('T')[0],
        updatedAt: realProduct.updatedAt || new Date().toISOString().split('T')[0],
        featured: false,
        trending: (realProduct.averageRating || 0) >= 4.5,
        onSale: false,
        seoTitle: realProduct.name,
        seoDescription: realProduct.description || '',
        weight: 0,
        dimensions: { length: 0, width: 0, height: 0 }
      }));

      const convertedStats: ProductStats = {
        totalProducts: realProductsData.productStats.totalProducts,
        activeProducts: realProductsData.productStats.activeProducts,
        totalRevenue: realProductsData.productStats.totalRevenue,
        averagePrice: realProductsData.productStats.averagePrice,
        topCategory: realProductsData.productStats.topCategory,
        lowStockCount: realProductsData.productStats.lowStockCount,
        trendingCount: realProductsData.productStats.trendingCount,
        conversionRate: realProductsData.productStats.conversionRate
      };

      setProducts(convertedProducts);
      setProductStats(convertedStats);

      // Insights basés sur les vraies données
      const realInsights: ProductInsight[] = [
        {
          id: '1',
          type: 'success',
          title: 'Données réelles chargées',
          description: `${convertedProducts.length} produits synchronisés depuis la base de données`,
          action: 'Données à jour',
          priority: 'high'
        },
        {
          id: '2',
          type: convertedStats.lowStockCount > 0 ? 'warning' : 'success',
          title: 'État des stocks',
          description: convertedStats.lowStockCount > 0
            ? `${convertedStats.lowStockCount} produit(s) en stock faible`
            : 'Tous les produits ont un stock suffisant',
          action: convertedStats.lowStockCount > 0 ? 'Réapprovisionner' : 'Continuer la surveillance',
          priority: convertedStats.lowStockCount > 0 ? 'high' : 'low'
        },
        {
          id: '3',
          type: 'info',
          title: 'Performance globale',
          description: `CA total: ${(convertedStats.totalRevenue / 1000000).toFixed(1)}M F - Prix moyen: ${Math.round(convertedStats.averagePrice).toLocaleString()} F`,
          action: 'Analyser les tendances',
          priority: 'medium'
        }
      ];
      setProductInsights(realInsights);
    }

    setTimeout(() => setDataLoading(false), 500);
  };

  return (
    <div className="business-products-page">
      {/* Header avec actions principales */}
      <div className="products-header">
        <div className="header-content">
          <h1 className="page-title">
            <Package size={28} />
            Gestion des Produits
          </h1>
          <p className="page-subtitle">
            Gérez votre catalogue, optimisez vos prix et analysez les performances
          </p>
        </div>

        <div className="header-actions">
          <button
            className="action-btn secondary"
            onClick={() => setShowAnalytics(!showAnalytics)}
          >
            <BarChart3 size={16} />
            Analytics
          </button>
          <div className="export-dropdown">
            <button
              className="action-btn secondary"
              onClick={() => setShowExportMenu(!showExportMenu)}
            >
              <Download size={16} />
              Exporter
              <ChevronDown size={14} />
            </button>
            {showExportMenu && (
              <div className="export-menu">
                <div className="export-section">
                  <h4>Import/Export Avancé</h4>
                  <button
                    className="export-option import"
                    onClick={() => {
                      setShowImportModal(true);
                      setShowExportMenu(false);
                    }}
                  >
                    <Upload size={16} />
                    <div>
                      <span>Importer des produits</span>
                      <small>CSV avec mapping intelligent</small>
                    </div>
                  </button>
                  <button
                    className="export-option export"
                    onClick={handleExportProducts}
                  >
                    <Download size={16} />
                    <div>
                      <span>Exporter les produits</span>
                      <small>Excel, CSV, JSON avec filtres</small>
                    </div>
                  </button>
                </div>

                <div className="export-section">
                  <h4>Exports rapides</h4>
                  <button
                    className="export-option catalog"
                    onClick={() => {
                      handleExportCatalog();
                      setShowExportMenu(false);
                    }}
                  >
                    <FileText size={16} />
                    <div>
                      <span>Catalogue PDF</span>
                      <small>Catalogue produits avec images</small>
                    </div>
                  </button>
                  <button
                    className="export-option inventory"
                    onClick={() => {
                      handleExportInventory();
                      setShowExportMenu(false);
                    }}
                  >
                    <Package size={16} />
                    <div>
                      <span>Inventaire Excel</span>
                      <small>Données complètes des produits</small>
                    </div>
                  </button>
                </div>
              </div>
            )}
          </div>
          <button
            className="action-btn primary"
            onClick={() => setShowAddProduct(true)}
          >
            <Plus size={16} />
            Nouveau produit
          </button>
        </div>
      </div>

      {/* Toggle Données Réelles / Fictives */}
      <DataToggle
        onToggle={handleDataToggle}
        initialValue={useRealData}
        hasRealData={realProductsData.products && realProductsData.products.length > 0}
        loading={dataLoading || realProductsData.loading}
      />

      {/* KPIs Dashboard */}
      <div className="products-kpis">
        <div className="kpi-card total">
          <div className="kpi-icon">
            <Package size={24} />
          </div>
          <div className="kpi-content">
            <h3>Total Produits</h3>
            <p className="kpi-value">{productStats.totalProducts}</p>
            <span className="kpi-trend positive">
              <ArrowUpRight size={16} />
              +2 ce mois
            </span>
          </div>
        </div>

        <div className="kpi-card revenue">
          <div className="kpi-icon">
            <DollarSign size={24} />
          </div>
          <div className="kpi-content">
            <h3>CA Produits</h3>
            <p className="kpi-value">{formatAmountShort(productStats.totalRevenue)}</p>
            <span className="kpi-trend positive">
              <ArrowUpRight size={16} />
              +18% ce mois
            </span>
          </div>
        </div>

        <div className="kpi-card price">
          <div className="kpi-icon">
            <Target size={24} />
          </div>
          <div className="kpi-content">
            <h3>Prix Moyen</h3>
            <p className="kpi-value">{Math.round(productStats.averagePrice).toLocaleString()} F</p>
            <span className="kpi-trend neutral">
              <ArrowUpRight size={16} />
              Stable
            </span>
          </div>
        </div>

        <div className="kpi-card stock">
          <div className="kpi-icon">
            <AlertCircle size={24} />
          </div>
          <div className="kpi-content">
            <h3>Alertes Stock</h3>
            <p className="kpi-value">{productStats.lowStockCount}</p>
            <span className="kpi-trend warning">
              <ArrowDownRight size={16} />
              Attention requise
            </span>
          </div>
        </div>
      </div>

      {/* Insights Section */}
      {showInsights && productInsights.length > 0 && (
        <div className="products-insights">
          <div className="insights-header">
            <h2 className="insights-title">
              <Brain size={20} />
              Insights & Recommandations
            </h2>
            <button
              className="action-btn secondary"
              onClick={() => setShowInsights(false)}
            >
              Masquer
            </button>
          </div>

          <div className="insights-list">
            {productInsights.map((insight) => (
              <div key={insight.id} className={`insight-item ${insight.type}`}>
                <div className="insight-icon">
                  {insight.type === 'success' && <TrendingUp size={20} />}
                  {insight.type === 'warning' && <AlertCircle size={20} />}
                  {insight.type === 'info' && <Lightbulb size={20} />}
                  {insight.type === 'error' && <XCircle size={20} />}
                </div>
                <div className="insight-content">
                  <h4>{insight.title}</h4>
                  <p>{insight.description}</p>
                  {insight.action && <p className="insight-action">{insight.action}</p>}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Toolbar avec recherche et filtres */}
      <div className="products-toolbar">
        <div className="toolbar-top">
          <div className="toolbar-left">
            <div className="search-container">
              <Search size={16} />
              <input
                type="text"
                placeholder="Rechercher un produit..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>

            <button
              className={`filter-btn ${showFilters ? 'active' : ''}`}
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter size={16} />
              Filtres
              {showFilters && <span className="filter-count">3</span>}
            </button>
          </div>

          <div className="toolbar-right">
            <div className="view-toggle">
              <button
                className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
                onClick={() => setViewMode('grid')}
              >
                <Grid size={16} />
              </button>
              <button
                className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
                onClick={() => setViewMode('list')}
              >
                <List size={16} />
              </button>
            </div>



            <div className="results-summary">
              {filteredProducts.length} produit(s) sur {products.length}
              {selectedProducts.length > 0 && (
                <button
                  className="select-all-btn"
                  onClick={handleSelectAll}
                >
                  {selectedProducts.length === filteredProducts.length ? 'Tout désélectionner' : 'Tout sélectionner'}
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Panel de filtres */}
        {showFilters && (
          <div className="filters-panel">
            <div className="filter-group">
              <label>Catégorie</label>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="filter-select"
              >
                <option value="all">Toutes les catégories</option>
                <option value="Beauté">Beauté</option>
                <option value="Cosmétiques">Cosmétiques</option>
                <option value="Hygiène">Hygiène</option>
                <option value="Soins">Soins</option>
                <option value="Parfums">Parfums</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Statut</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="filter-select"
              >
                <option value="all">Tous les statuts</option>
                <option value="active">Actif</option>
                <option value="inactive">Inactif</option>
                <option value="draft">Brouillon</option>
                <option value="out_of_stock">Rupture de stock</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Prix</label>
              <div className="price-range">
                <input
                  type="number"
                  placeholder="Min"
                  value={priceRange.min}
                  onChange={(e) => setPriceRange({...priceRange, min: Number(e.target.value)})}
                  className="price-input"
                />
                <span>-</span>
                <input
                  type="number"
                  placeholder="Max"
                  value={priceRange.max}
                  onChange={(e) => setPriceRange({...priceRange, max: Number(e.target.value)})}
                  className="price-input"
                />
              </div>
            </div>

            <div className="filter-group">
              <label>Tri</label>
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  setSortBy(field);
                  setSortOrder(order as 'asc' | 'desc');
                }}
                className="filter-select"
              >
                <option value="name-asc">Nom (A-Z)</option>
                <option value="name-desc">Nom (Z-A)</option>
                <option value="price-asc">Prix (croissant)</option>
                <option value="price-desc">Prix (décroissant)</option>
                <option value="stock-asc">Stock (faible)</option>
                <option value="stock-desc">Stock (élevé)</option>
                <option value="rating-desc">Note (élevée)</option>
                <option value="salesCount-desc">Ventes (élevées)</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Container des produits */}
      <div className="products-container">
        {filteredProducts.length === 0 ? (
          <div className="empty-state">
            <Package size={48} />
            <h3>Aucun produit trouvé</h3>
            <p>Aucun produit ne correspond à vos critères de recherche.</p>
            <button onClick={() => setShowAddProduct(true)}>
              <Plus size={16} />
              Ajouter votre premier produit
            </button>
          </div>
        ) : (
          <div className={viewMode === 'grid' ? 'products-grid' : 'products-list'}>
            {filteredProducts.map((product) => (
              <div key={product.id} className={`product-card ${viewMode === 'list' ? 'list-view' : ''} ${selectedProducts.includes(product.id) ? 'selected' : ''}`}>
                {/* Checkbox de sélection */}
                <div className="product-checkbox">
                  <input
                    type="checkbox"
                    checked={selectedProducts.includes(product.id)}
                    onChange={() => handleProductSelect(product.id)}
                    className="product-select"
                  />
                </div>

                <div className="product-image">
                  <img
                    {...createImageProps(
                      getFirstProductImage(product.images, product.category, product.name),
                      product.name,
                      product.category
                    )}
                  />

                  {/* Badges */}
                  <div className="product-badges">
                    {product.featured && <span className="product-badge badge-featured">Featured</span>}
                    {product.trending && <span className="product-badge badge-trending">Trending</span>}
                    {product.onSale && <span className="product-badge badge-sale">Promo</span>}
                    {product.status === 'out_of_stock' && <span className="product-badge badge-out-of-stock">Rupture</span>}
                  </div>

                  {/* Actions */}
                  <div className="product-actions">
                    <button
                      className="action-icon"
                      onClick={() => handleViewProduct(product.id)}
                      title="Voir détails"
                    >
                      <Eye size={16} />
                    </button>
                    <button
                      className="action-icon"
                      onClick={() => setShowEditProduct(product.id)}
                      title="Modifier"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      className={`action-icon ${product.featured ? 'favorited' : ''}`}
                      onClick={() => handleToggleFavorite(product.id)}
                      title={product.featured ? 'Retirer des favoris' : 'Ajouter aux favoris'}
                    >
                      <Heart size={16} fill={product.featured ? '#ef4444' : 'none'} />
                    </button>
                    <button
                      className="action-icon delete"
                      onClick={() => handleDeleteProduct(product.id)}
                      title="Supprimer"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>

                <div className="product-content">
                  <div className="product-header">
                    <h3 className="product-name">{product.name}</h3>
                    <span className={`product-status status-${product.status.replace('_', '-')}`}>
                      {product.status === 'active' && 'Actif'}
                      {product.status === 'inactive' && 'Inactif'}
                      {product.status === 'draft' && 'Brouillon'}
                      {product.status === 'out_of_stock' && 'Rupture'}
                    </span>
                  </div>

                  <div className="product-category">{product.category} • {product.brand}</div>
                  <p className="product-description">{product.description}</p>

                  <div className="product-price">
                    <span className="current-price">{formatAmount(product.price)}</span>
                    {product.originalPrice && (
                      <>
                        <span className="original-price">{formatAmount(product.originalPrice)}</span>
                        <span className="discount-badge">
                          -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                        </span>
                      </>
                    )}
                  </div>

                  <div className="product-meta">
                    <div className="product-rating">
                      <div className="rating-stars">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            size={14}
                            className="star"
                            fill={i < Math.floor(product.rating) ? '#fbbf24' : 'none'}
                          />
                        ))}
                      </div>
                      <span className="rating-text">
                        {product.rating} ({product.reviewsCount})
                      </span>
                    </div>

                    <div className={`product-stock ${getStockClass(product)}`}>
                      {getStockText(product)}
                    </div>
                  </div>

                  <div className="product-footer">
                    <button
                      className="product-btn btn-primary"
                      onClick={() => setShowEditProduct(product.id)}
                    >
                      <Edit size={14} />
                      Modifier
                    </button>
                    <button
                      className="product-btn btn-secondary"
                      onClick={() => handleViewProduct(product.id)}
                    >
                      <Eye size={14} />
                      Détails
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {filteredProducts.length > 0 && (
          <div className="pagination">
            <button className="page-btn" disabled>
              Précédent
            </button>
            <button className="page-btn active">1</button>
            <button className="page-btn">2</button>
            <button className="page-btn">3</button>
            <span className="page-info">Page 1 sur 3</span>
            <button className="page-btn">
              Suivant
            </button>
          </div>
        )}
      </div>

      {/* Chat IA Assistant */}
      <AIChatButton
        context="products"
        currentData={{
          stats: productStats,
          products: filteredProducts,
          insights: productInsights
        }}
      />

      {/* Modals */}
      <AddProductModal
        isOpen={showAddProduct}
        onClose={() => setShowAddProduct(false)}
        onSave={handleAddProduct}
      />

      <EditProductModal
        isOpen={showEditProduct !== null}
        onClose={() => setShowEditProduct(null)}
        onSave={handleEditProduct}
        product={showEditProduct ? products.find(p => p.id === showEditProduct) || null : null}
      />

      <ProductDetailsModal
        isOpen={showProductDetails !== null}
        onClose={() => setShowProductDetails(null)}
        product={showProductDetails ? products.find(p => p.id === showProductDetails) || null : null}
      />

      <DeleteProductModal
        isOpen={showDeleteProduct !== null}
        onClose={() => setShowDeleteProduct(null)}
        onConfirm={handleConfirmDelete}
        product={showDeleteProduct ? products.find(p => p.id === showDeleteProduct) || null : null}
      />

      {/* Barre d'actions bulk avancée */}
      <BulkActionsBar
        selectedCount={selectedProducts.length}
        totalCount={filteredProducts.length}
        onSelectAll={handleSelectAll}
        onDeselectAll={() => setSelectedProducts([])}
        onBulkEdit={handleBulkEdit}
        onBulkDelete={handleBulkDelete}
        isAllSelected={selectedProducts.length === filteredProducts.length && filteredProducts.length > 0}
      />

      {/* Modal d'actions bulk avancée */}
      <BulkActionsModal
        isOpen={showBulkActionsModal}
        onClose={() => setShowBulkActionsModal(false)}
        selectedProducts={products.filter(p => selectedProducts.includes(p.id))}
        onApplyActions={handleApplyBulkActions}
      />

      {/* Modals d'import/export avancées */}
      <ImportModal
        isOpen={showImportModal}
        onClose={() => setShowImportModal(false)}
        onImport={handleImportProducts}
      />

      <ExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        products={products}
        selectedProducts={selectedProducts}
      />
    </div>
  );
};

export default BusinessProductsPage;
