// ... existing code ...
// Replace external placeholder URLs with local assets
import placeholderLarge from '../assets/placeholders/placeholder-600x300.jpg';
import placeholderSmall from '../assets/placeholders/placeholder-50x50.jpg';

// Use these imports in your component
const ImageComponent = ({ src, alt, size }) => {
  // Use local fallback if src is not available
  const fallbackSrc = size === 'small' ? placeholderSmall : placeholderLarge;
  
  return (
    <img 
      src={src || fallbackSrc} 
      alt={alt || 'Image'} 
      onError={(e) => {
        e.currentTarget.src = fallbackSrc;
      }}
    />
  );
};
// ... existing code ...