import React, { useState, useEffect } from 'react';
import {
  Star,
  MessageCircle,
  ThumbsUp,
  Reply,
  MoreHorizontal,
  Shield,
  Send,
  Edit3,
  Trash2
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import {
  IMarketComment,
  IMarketRating,
  IProductStats,
  ICreateComment,
  ICreateRating
} from '../../types';
import {
  getProductComments,
  getProductRatings,
  getProductStats,
  addComment,
  addOrUpdateRating,
  getUserRating,
  deleteComment
} from '../../services/marketplaceReviewService';
import Button from '../ui/Button';
import Avatar from '../ui/Avatar';

interface ProductReviewsProps {
  productId: string;
  productName: string;
}

// Fonction utilitaire pour formater les dates
const formatTimeAgo = (date: Date): string => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'À l\'instant';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `il y a ${minutes} minute${minutes > 1 ? 's' : ''}`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `il y a ${hours} heure${hours > 1 ? 's' : ''}`;
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400);
    return `il y a ${days} jour${days > 1 ? 's' : ''}`;
  } else {
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  }
};

const ProductReviews: React.FC<ProductReviewsProps> = ({ productId, productName }) => {
  const { currentUser } = useAuth();
  const [comments, setComments] = useState<IMarketComment[]>([]);
  const [stats, setStats] = useState<IProductStats | null>(null);
  const [userRating, setUserRating] = useState<IMarketRating | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'reviews' | 'ratings'>('reviews');

  // États pour les formulaires
  const [newComment, setNewComment] = useState('');
  const [newRating, setNewRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadReviews();
  }, [productId]);

  const loadReviews = async () => {
    setLoading(true);
    try {
      const [commentsData, statsData] = await Promise.all([
        getProductComments(productId),
        getProductStats(productId)
      ]);

      setComments(commentsData);
      setStats(statsData);

      // Charger la note de l'utilisateur actuel s'il est connecté
      if (currentUser) {
        const userRatingData = await getUserRating(productId, currentUser.id);
        setUserRating(userRatingData);
        if (userRatingData) {
          setNewRating(userRatingData.rating);
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement des avis:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitComment = async () => {
    if (!currentUser || !newComment.trim()) return;

    setSubmitting(true);
    try {
      const commentData: ICreateComment = {
        productId,
        comment: newComment.trim()
      };

      const result = await addComment(commentData);
      if (result) {
        setNewComment('');
        await loadReviews(); // Recharger les commentaires
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout du commentaire:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleSubmitReply = async (parentCommentId: string) => {
    if (!currentUser || !replyText.trim()) return;

    setSubmitting(true);
    try {
      const replyData: ICreateComment = {
        productId,
        comment: replyText.trim(),
        parentCommentId
      };

      const result = await addComment(replyData);
      if (result) {
        setReplyText('');
        setReplyingTo(null);
        await loadReviews(); // Recharger les commentaires
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout de la réponse:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleSubmitRating = async (rating: number) => {
    if (!currentUser) return;

    setSubmitting(true);
    try {
      const ratingData: ICreateRating = {
        productId,
        rating
      };

      const result = await addOrUpdateRating(ratingData);
      if (result) {
        setUserRating(result);
        setNewRating(rating);
        await loadReviews(); // Recharger les stats
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout de la note:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce commentaire ?')) return;

    const success = await deleteComment(commentId);
    if (success) {
      await loadReviews();
    }
  };

  const renderStars = (rating: number, interactive = false, onRate?: (rating: number) => void) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => interactive && onRate?.(star)}
            onMouseEnter={() => interactive && setHoverRating(star)}
            onMouseLeave={() => interactive && setHoverRating(0)}
            disabled={!interactive}
            className={`${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'} transition-transform`}
          >
            <Star
              size={interactive ? 24 : 16}
              className={`${
                star <= (interactive ? (hoverRating || rating) : rating)
                  ? 'text-yellow-400 fill-current'
                  : 'text-gray-300'
              } transition-colors`}
            />
          </button>
        ))}
      </div>
    );
  };

  const renderComment = (comment: IMarketComment, isReply = false) => (
    <div key={comment.id} className={`${isReply ? 'ml-12 mt-4' : 'mb-6'} bg-white rounded-lg border border-gray-100 p-4`}>
      <div className="flex items-start space-x-3">
        <Avatar
          src={comment.profilePicture}
          alt={comment.username || 'Utilisateur'}
          size="sm"
        />
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className="font-semibold text-gray-900">
              {comment.username || 'Utilisateur anonyme'}
            </span>
            {comment.isVerifiedPurchase && (
              <div className="flex items-center text-green-600 text-xs bg-green-50 px-2 py-1 rounded-full">
                <Shield size={12} className="mr-1" />
                Achat vérifié
              </div>
            )}
            {comment.userRating && (
              <div className="flex items-center">
                {renderStars(comment.userRating)}
              </div>
            )}
          </div>

          <p className="text-gray-700 mb-3 leading-relaxed">{comment.comment}</p>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>
                {formatTimeAgo(comment.createdAt)}
              </span>
              {!isReply && currentUser && (
                <button
                  onClick={() => setReplyingTo(comment.id)}
                  className="flex items-center space-x-1 hover:text-blue-600 transition-colors"
                >
                  <Reply size={14} />
                  <span>Répondre</span>
                </button>
              )}
            </div>

            {currentUser?.id === comment.userId && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleDeleteComment(comment.id)}
                  className="text-red-500 hover:text-red-700 transition-colors"
                >
                  <Trash2 size={14} />
                </button>
              </div>
            )}
          </div>

          {/* Formulaire de réponse */}
          {replyingTo === comment.id && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <textarea
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                placeholder="Écrivez votre réponse..."
                className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
              />
              <div className="flex justify-end space-x-2 mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setReplyingTo(null);
                    setReplyText('');
                  }}
                >
                  Annuler
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={() => handleSubmitReply(comment.id)}
                  disabled={!replyText.trim() || submitting}
                  leftIcon={<Send size={14} />}
                >
                  Répondre
                </Button>
              </div>
            </div>
          )}

          {/* Réponses */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-4 space-y-4">
              {comment.replies.map(reply => renderComment(reply, true))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      {/* Header avec statistiques */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-gray-900">Avis et commentaires</h3>
          {stats && (
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <Star size={16} className="text-yellow-400 fill-current" />
                <span className="font-semibold">{stats.averageRating.toFixed(1)}</span>
                <span>({stats.totalRatings} notes)</span>
              </div>
              <div className="flex items-center space-x-1">
                <MessageCircle size={16} />
                <span>{stats.totalComments} commentaires</span>
              </div>
            </div>
          )}
        </div>

        {/* Onglets */}
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('reviews')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'reviews'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Commentaires ({stats?.totalComments || 0})
          </button>
          <button
            onClick={() => setActiveTab('ratings')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'ratings'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Notes ({stats?.totalRatings || 0})
          </button>
        </div>
      </div>

      <div className="p-6">
        {activeTab === 'reviews' ? (
          <div>
            {/* Formulaire d'ajout de commentaire */}
            {currentUser && (
              <div className="mb-8 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-3">Laisser un commentaire</h4>
                <textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder={`Partagez votre expérience avec ${productName}...`}
                  className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={4}
                />
                <div className="flex justify-between items-center mt-3">
                  <span className="text-sm text-gray-500">
                    {newComment.length}/1000 caractères
                  </span>
                  <Button
                    variant="primary"
                    onClick={handleSubmitComment}
                    disabled={!newComment.trim() || newComment.length < 10 || submitting}
                    leftIcon={<Send size={16} />}
                  >
                    Publier le commentaire
                  </Button>
                </div>
              </div>
            )}

            {/* Liste des commentaires */}
            {comments.length > 0 ? (
              <div className="space-y-6">
                {comments.map(comment => renderComment(comment))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <MessageCircle size={48} className="mx-auto mb-4 text-gray-300" />
                <p>Aucun commentaire pour le moment</p>
                <p className="text-sm">Soyez le premier à partager votre avis !</p>
              </div>
            )}
          </div>
        ) : (
          <div>
            {/* Formulaire de notation */}
            {currentUser && (
              <div className="mb-8 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-3">
                  {userRating ? 'Modifier votre note' : 'Noter ce produit'}
                </h4>
                <div className="flex items-center space-x-4">
                  {renderStars(newRating, true, handleSubmitRating)}
                  <span className="text-sm text-gray-600">
                    {newRating > 0 ? `${newRating}/5 étoiles` : 'Cliquez pour noter'}
                  </span>
                </div>
              </div>
            )}

            {/* Répartition des notes */}
            {stats && stats.totalRatings > 0 && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-4">Répartition des notes</h4>
                <div className="space-y-2">
                  {[5, 4, 3, 2, 1].map(rating => {
                    // Simulation de la répartition - dans une vraie app, ces données viendraient de la DB
                    const count = Math.floor(Math.random() * (stats.totalRatings / 2));
                    const percentage = stats.totalRatings > 0 ? (count / stats.totalRatings) * 100 : 0;

                    return (
                      <div key={rating} className="flex items-center space-x-3">
                        <div className="flex items-center space-x-1 w-16">
                          <span className="text-sm">{rating}</span>
                          <Star size={14} className="text-yellow-400 fill-current" />
                        </div>
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                        <span className="text-sm text-gray-600 w-8">{count}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductReviews;
