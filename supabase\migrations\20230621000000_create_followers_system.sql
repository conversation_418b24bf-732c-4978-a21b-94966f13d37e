-- Migration pour implémenter un système d'abonnement et d'abonné

-- Création de la table followers pour gérer les relations d'abonnement
CREATE TABLE IF NOT EXISTS public.followers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  follower_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  following_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Contrainte d'unicité pour éviter les doublons
  CONSTRAINT unique_follower_following UNIQUE (follower_id, following_id),
  
  -- Empêcher un utilisateur de se suivre lui-même
  CONSTRAINT prevent_self_follow CHECK (follower_id != following_id)
);

-- Création d'index pour améliorer les performances des requêtes
CREATE INDEX IF NOT EXISTS followers_follower_id_idx ON public.followers(follower_id);
CREATE INDEX IF NOT EXISTS followers_following_id_idx ON public.followers(following_id);

-- Ajout de colonnes pour le comptage des abonnés et abonnements dans la table profiles
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS followers_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS following_count INTEGER DEFAULT 0;

-- Commentaires explicatifs sur les colonnes
COMMENT ON COLUMN public.profiles.followers_count IS 'Nombre d''utilisateurs qui suivent ce profil';
COMMENT ON COLUMN public.profiles.following_count IS 'Nombre d''utilisateurs que ce profil suit';

-- Activation de RLS (Row Level Security) sur la table followers
ALTER TABLE public.followers ENABLE ROW LEVEL SECURITY;

-- Politiques RLS pour la table followers
-- Tout le monde peut voir les relations d'abonnement
CREATE POLICY "Les utilisateurs peuvent voir toutes les relations d'abonnement"
ON public.followers FOR SELECT
USING (true);

-- Seuls les utilisateurs authentifiés peuvent créer des abonnements
CREATE POLICY "Les utilisateurs authentifiés peuvent créer des abonnements"
ON public.followers FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = follower_id);

-- Seuls les utilisateurs peuvent supprimer leurs propres abonnements
CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres abonnements"
ON public.followers FOR DELETE
USING (auth.uid() = follower_id);

-- Fonction pour mettre à jour les compteurs d'abonnés et d'abonnements
CREATE OR REPLACE FUNCTION update_follower_counts()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Incrémenter les compteurs
    UPDATE public.profiles SET followers_count = followers_count + 1 WHERE id = NEW.following_id;
    UPDATE public.profiles SET following_count = following_count + 1 WHERE id = NEW.follower_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Décrémenter les compteurs
    UPDATE public.profiles SET followers_count = followers_count - 1 WHERE id = OLD.following_id;
    UPDATE public.profiles SET following_count = following_count - 1 WHERE id = OLD.follower_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Création des triggers pour appeler la fonction lors des modifications
CREATE TRIGGER follower_insert_trigger
AFTER INSERT ON public.followers
FOR EACH ROW
EXECUTE FUNCTION update_follower_counts();

CREATE TRIGGER follower_delete_trigger
AFTER DELETE ON public.followers
FOR EACH ROW
EXECUTE FUNCTION update_follower_counts();

-- Création d'une fonction pour vérifier si un utilisateur en suit un autre
CREATE OR REPLACE FUNCTION is_following(follower_id UUID, following_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.followers
    WHERE follower_id = $1 AND following_id = $2
  );
END;
$$ LANGUAGE plpgsql;