import { UserStatus } from '../types';
import { ShieldCheck, UserCircle, MessageSquareText, TrendingUp, Award, Crown } from 'lucide-react';

export interface StatusLevel {
  status: UserStatus;
  name: string;
  description: string;
  icon: any;
  color: string;
  bgColor: string;
  textColor: string;
  criteria: StatusCriteria;
  benefits: string[];
  nextLevel?: UserStatus;
}

export interface StatusCriteria {
  profileComplete?: boolean;
  minPosts?: number;
  minComments?: number;
  minLikes?: number;
  minFollowers?: number;
  minRecommendations?: number;
  accountAge?: number; // en jours
  qualityScore?: number; // score de qualité des posts (1-10)
  engagementRate?: number; // taux d'engagement en pourcentage
  specialRequirements?: string[];
}

export class UserStatusService {
  /**
   * Configuration des niveaux de statut utilisateur
   */
  static readonly STATUS_LEVELS: Record<UserStatus, StatusLevel> = {
    [UserStatus.NEWBIE]: {
      status: UserStatus.NEWBIE,
      name: 'Nouveau',
      description: 'Bienvenue ! Complétez votre profil pour commencer votre parcours.',
      icon: UserCircle,
      color: '#6B7280',
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-600',
      criteria: {
        profileComplete: false
      },
      benefits: [
        'Accès aux fonctionnalités de base',
        'Possibilité de consulter les avis',
        'Création de compte gratuite'
      ],
      nextLevel: UserStatus.MEMBER
    },

    [UserStatus.MEMBER]: {
      status: UserStatus.MEMBER,
      name: 'Membre',
      description: 'Profil complet ! Vous pouvez maintenant participer pleinement à la communauté.',
      icon: ShieldCheck,
      color: '#3B82F6',
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-700',
      criteria: {
        profileComplete: true
      },
      benefits: [
        'Profil complet avec photo et couverture',
        'Accès à toutes les fonctionnalités de base',
        'Possibilité de publier des avis',
        'Système de recommandations',
        'Messagerie privée'
      ],
      nextLevel: UserStatus.CONTRIBUTOR
    },

    [UserStatus.CONTRIBUTOR]: {
      status: UserStatus.CONTRIBUTOR,
      name: 'Contributeur',
      description: 'Vous participez activement ! Continuez à partager vos expériences.',
      icon: MessageSquareText,
      color: '#10B981',
      bgColor: 'bg-green-100',
      textColor: 'text-green-700',
      criteria: {
        profileComplete: true,
        minPosts: 1,
        minComments: 1
      },
      benefits: [
        'Badge de contributeur visible',
        'Priorité dans les résultats de recherche',
        'Accès aux discussions de groupe',
        'Notifications prioritaires',
        'Possibilité de créer des listes'
      ],
      nextLevel: UserStatus.DISCOVERER
    },

    [UserStatus.DISCOVERER]: {
      status: UserStatus.DISCOVERER,
      name: 'Découvreur',
      description: 'Expert en découvertes ! Vos avis aident la communauté à faire de meilleurs choix.',
      icon: TrendingUp,
      color: '#8B5CF6',
      bgColor: 'bg-purple-100',
      textColor: 'text-purple-700',
      criteria: {
        profileComplete: true,
        minPosts: 10,
        minComments: 25,
        minLikes: 50,
        minFollowers: 20,
        accountAge: 30
      },
      benefits: [
        'Badge découvreur prestigieux',
        'Avis mis en avant dans les résultats',
        'Accès aux fonctionnalités bêta',
        'Programme de récompenses exclusif',
        'Invitation aux événements communautaires',
        'Support client prioritaire'
      ],
      nextLevel: UserStatus.INFLUENCER
    },

    [UserStatus.INFLUENCER]: {
      status: UserStatus.INFLUENCER,
      name: 'Influenceur',
      description: 'Votre voix compte ! Vous influencez positivement les décisions de la communauté.',
      icon: Award,
      color: '#F59E0B',
      bgColor: 'bg-yellow-100',
      textColor: 'text-yellow-700',
      criteria: {
        profileComplete: true,
        minPosts: 50,
        minComments: 100,
        minLikes: 500,
        minFollowers: 100,
        minRecommendations: 200,
        accountAge: 90,
        qualityScore: 8,
        engagementRate: 15
      },
      benefits: [
        'Badge influenceur doré',
        'Avis épinglés en tête des résultats',
        'Programme de partenariat',
        'Accès aux statistiques avancées',
        'Possibilité de créer des événements',
        'Revenus de parrainage',
        'Accès VIP aux nouveautés'
      ],
      nextLevel: UserStatus.LEADER
    },

    [UserStatus.LEADER]: {
      status: UserStatus.LEADER,
      name: 'Leader',
      description: 'Élite de la communauté ! Vous êtes un pilier de notre plateforme.',
      icon: Crown,
      color: '#EF4444',
      bgColor: 'bg-red-100',
      textColor: 'text-red-700',
      criteria: {
        profileComplete: true,
        minPosts: 200,
        minComments: 500,
        minLikes: 2000,
        minFollowers: 500,
        minRecommendations: 1000,
        accountAge: 180,
        qualityScore: 9,
        engagementRate: 25,
        specialRequirements: [
          'Modération communautaire',
          'Contribution exceptionnelle',
          'Respect des valeurs de la plateforme'
        ]
      },
      benefits: [
        'Badge leader exclusif avec couronne',
        'Statut de modérateur communautaire',
        'Accès aux outils de modération',
        'Programme de revenus avancé',
        'Consultation sur les nouvelles fonctionnalités',
        'Accès direct à l\'équipe de développement',
        'Événements exclusifs leaders',
        'Reconnaissance publique sur la plateforme'
      ]
    }
  };

  /**
   * Obtient les informations d'un niveau de statut
   */
  static getStatusLevel(status: UserStatus): StatusLevel {
    return this.STATUS_LEVELS[status];
  }

  /**
   * Obtient tous les niveaux de statut dans l'ordre de progression
   */
  static getAllStatusLevels(): StatusLevel[] {
    return [
      this.STATUS_LEVELS[UserStatus.NEWBIE],
      this.STATUS_LEVELS[UserStatus.MEMBER],
      this.STATUS_LEVELS[UserStatus.CONTRIBUTOR],
      this.STATUS_LEVELS[UserStatus.DISCOVERER],
      this.STATUS_LEVELS[UserStatus.INFLUENCER],
      this.STATUS_LEVELS[UserStatus.LEADER]
    ];
  }

  /**
   * Calcule le progrès vers le niveau suivant
   */
  static calculateProgressToNextLevel(
    currentStatus: UserStatus,
    userStats: {
      profileComplete: boolean;
      postCount: number;
      commentCount: number;
      likesReceived: number;
      followersCount: number;
      recommendationsCount: number;
      accountAgeDays: number;
      qualityScore?: number;
      engagementRate?: number;
    }
  ): {
    nextLevel: StatusLevel | null;
    progress: number;
    missingCriteria: string[];
  } {
    const currentLevel = this.getStatusLevel(currentStatus);

    // Vérification de sécurité
    if (!currentLevel) {
      return {
        nextLevel: null,
        progress: 0,
        missingCriteria: ['Statut utilisateur invalide']
      };
    }

    if (!currentLevel.nextLevel) {
      return {
        nextLevel: null,
        progress: 100,
        missingCriteria: []
      };
    }

    const nextLevel = this.getStatusLevel(currentLevel.nextLevel);

    // Vérification de sécurité pour le niveau suivant
    if (!nextLevel) {
      return {
        nextLevel: null,
        progress: 100,
        missingCriteria: ['Niveau suivant introuvable']
      };
    }

    const criteria = nextLevel.criteria;
    const missingCriteria: string[] = [];
    let totalCriteria = 0;
    let metCriteria = 0;

    // Vérifier chaque critère
    if (criteria.profileComplete !== undefined) {
      totalCriteria++;
      if (userStats.profileComplete) {
        metCriteria++;
      } else {
        missingCriteria.push('Profil complet requis');
      }
    }

    if (criteria.minPosts !== undefined) {
      totalCriteria++;
      if (userStats.postCount >= criteria.minPosts) {
        metCriteria++;
      } else {
        missingCriteria.push(`${criteria.minPosts - userStats.postCount} posts supplémentaires`);
      }
    }

    if (criteria.minComments !== undefined) {
      totalCriteria++;
      if (userStats.commentCount >= criteria.minComments) {
        metCriteria++;
      } else {
        missingCriteria.push(`${criteria.minComments - userStats.commentCount} commentaires supplémentaires`);
      }
    }

    if (criteria.minLikes !== undefined) {
      totalCriteria++;
      if (userStats.likesReceived >= criteria.minLikes) {
        metCriteria++;
      } else {
        missingCriteria.push(`${criteria.minLikes - userStats.likesReceived} likes supplémentaires`);
      }
    }

    if (criteria.minFollowers !== undefined) {
      totalCriteria++;
      if (userStats.followersCount >= criteria.minFollowers) {
        metCriteria++;
      } else {
        missingCriteria.push(`${criteria.minFollowers - userStats.followersCount} followers supplémentaires`);
      }
    }

    if (criteria.minRecommendations !== undefined) {
      totalCriteria++;
      if (userStats.recommendationsCount >= criteria.minRecommendations) {
        metCriteria++;
      } else {
        missingCriteria.push(`${criteria.minRecommendations - userStats.recommendationsCount} recommandations supplémentaires`);
      }
    }

    if (criteria.accountAge !== undefined) {
      totalCriteria++;
      if (userStats.accountAgeDays >= criteria.accountAge) {
        metCriteria++;
      } else {
        missingCriteria.push(`Compte trop récent (${criteria.accountAge - userStats.accountAgeDays} jours restants)`);
      }
    }

    const progress = totalCriteria > 0 ? Math.round((metCriteria / totalCriteria) * 100) : 0;

    return {
      nextLevel,
      progress,
      missingCriteria
    };
  }

  /**
   * Formate les critères pour l'affichage
   */
  static formatCriteria(criteria: StatusCriteria): string[] {
    const formatted: string[] = [];

    if (criteria.profileComplete) {
      formatted.push('Profil complet (photo, couverture, localisation)');
    }
    if (criteria.minPosts) {
      formatted.push(`Au moins ${criteria.minPosts} avis publiés`);
    }
    if (criteria.minComments) {
      formatted.push(`Au moins ${criteria.minComments} commentaires`);
    }
    if (criteria.minLikes) {
      formatted.push(`Au moins ${criteria.minLikes} likes reçus`);
    }
    if (criteria.minFollowers) {
      formatted.push(`Au moins ${criteria.minFollowers} followers`);
    }
    if (criteria.minRecommendations) {
      formatted.push(`Au moins ${criteria.minRecommendations} recommandations`);
    }
    if (criteria.accountAge) {
      formatted.push(`Compte actif depuis ${criteria.accountAge} jours`);
    }
    if (criteria.qualityScore) {
      formatted.push(`Score de qualité minimum: ${criteria.qualityScore}/10`);
    }
    if (criteria.engagementRate) {
      formatted.push(`Taux d'engagement minimum: ${criteria.engagementRate}%`);
    }
    if (criteria.specialRequirements) {
      formatted.push(...criteria.specialRequirements);
    }

    return formatted;
  }
}
