import React from 'react';
import { Link } from 'react-router-dom';
import { MapPin, Calendar, Users } from 'lucide-react';
import Avatar from './Avatar';
import UserStatusBadge from './UserStatusBadge';
import BusinessStatusBadge from './BusinessStatusBadge';
import VerificationBadge from './VerificationBadge';
import { IUser, UserRole, IBusinessUser, BusinessStatus } from '../../types';
import { DEFAULT_IMAGES } from '../../constants/defaultImages';

interface UserCardProps {
  user: IUser;
  showFollowButton?: boolean;
  onFollow?: () => void;
  isFollowing?: boolean;
  isLoading?: boolean;
  className?: string;
}

const UserCard: React.FC<UserCardProps> = ({
  user,
  showFollowButton = false,
  onFollow,
  isFollowing = false,
  isLoading = false,
  className = ''
}) => {
  const getAvatarSrc = () => {
    if (user.profilePicture && user.profilePicture.trim() !== '') {
      return user.profilePicture;
    }
    return user.role === UserRole.BUSINESS ? DEFAULT_IMAGES.BUSINESS : DEFAULT_IMAGES.AVATAR;
  };

  const displayName = user.role === UserRole.BUSINESS && 'businessName' in user 
    ? (user as IBusinessUser).businessName 
    : user.username;

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow ${className}`}>
      <div className="flex items-start space-x-4">
        {/* Avatar */}
        <Link to={`/profile/${user.id}`}>
          <Avatar
            src={getAvatarSrc()}
            alt={displayName}
            size="lg"
            className="hover:ring-2 hover:ring-blue-500 transition-all"
          />
        </Link>

        {/* User Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <Link
                to={`/profile/${user.id}`}
                className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors truncate"
              >
                {displayName}
              </Link>
              <VerificationBadge
                isVerified={(user as any).is_verified || false}
                userRole={user.role}
                businessVerified={user.role === UserRole.BUSINESS ? (user as any).business_verified || false : false}
                size="sm"
                variant="minimal"
                showTooltip={true}
              />
            </div>
            
            {/* Follow Button */}
            {showFollowButton && onFollow && (
              <button
                onClick={onFollow}
                disabled={isLoading}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isFollowing
                    ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {isLoading ? 'Chargement...' : (isFollowing ? 'Abonné' : 'Suivre')}
              </button>
            )}
          </div>

          {/* Username */}
          <p className="text-sm text-gray-500 mb-3">@{user.username}</p>

          {/* Badges */}
          <div className="flex items-center space-x-2 mb-3">
            {/* Badge de statut - Entreprise (priorité) */}
            {user.role === UserRole.BUSINESS ? (
              'businessStatus' in user && (user as IBusinessUser).businessStatus ? (
                <BusinessStatusBadge
                  status={(user as IBusinessUser).businessStatus}
                  size="sm"
                  variant="default"
                />
              ) : (
                /* Badge par défaut pour les entreprises sans businessStatus */
                <BusinessStatusBadge
                  status={BusinessStatus.NEW}
                  size="sm"
                  variant="default"
                />
              )
            ) : user.status ? (
              /* Badge de statut - Utilisateur normal */
              <UserStatusBadge
                status={user.status}
                size="sm"
                variant="default"
              />
            ) : null}

            {/* Badge entreprise */}
            {user.role === UserRole.BUSINESS && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Entreprise
              </span>
            )}
          </div>

          {/* User Details */}
          <div className="space-y-2 text-sm text-gray-600">
            {/* Localisation */}
            {user.city && user.country && (
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-2 flex-shrink-0" />
                <span className="truncate">{user.city}, {user.country}</span>
              </div>
            )}

            {/* Date d'inscription */}
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2 flex-shrink-0" />
              <span>
                Membre depuis {new Date(user.createdAt).toLocaleDateString('fr-FR', {
                  month: 'long',
                  year: 'numeric'
                })}
              </span>
            </div>

            {/* Statistiques */}
            <div className="flex items-center">
              <Users className="w-4 h-4 mr-2 flex-shrink-0" />
              <span>
                {(user as any).followers_count || 0} abonné{((user as any).followers_count || 0) > 1 ? 's' : ''} • {' '}
                {(user as any).post_count || 0} publication{((user as any).post_count || 0) > 1 ? 's' : ''}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserCard;
