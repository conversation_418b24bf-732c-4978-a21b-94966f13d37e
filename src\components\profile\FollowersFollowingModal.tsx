import React, { useState, useEffect } from 'react';
import { X, Users, UserPlus, Search, User } from 'lucide-react';
import { supabase } from '../../lib/supabase';
import { IUser, UserRole } from '../../types';
import Avatar from '../ui/Avatar';
import Button from '../ui/Button';
import { useAuth } from '../../context/AuthContext';
import { useFollow } from '../../context/FollowContext';

interface UserProfile {
  id: string;
  username: string;
  profilePicture?: string;
  role: UserRole;
  businessName?: string;
  city?: string;
  country?: string;
  followers_count: number;
  following_count: number;
}

interface FollowersFollowingModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  initialTab: 'followers' | 'following';
  userCounts: {
    followers: number;
    following: number;
  };
}

const FollowersFollowingModal: React.FC<FollowersFollowingModalProps> = ({
  isOpen,
  onClose,
  userId,
  initialTab,
  userCounts
}) => {
  const { currentUser } = useAuth();
  const { followUser, unfollowUser } = useFollow();
  const [activeTab, setActiveTab] = useState<'followers' | 'following'>(initialTab);
  const [followers, setFollowers] = useState<UserProfile[]>([]);
  const [following, setFollowing] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [followStatus, setFollowStatus] = useState<Record<string, boolean>>({});

  // Charger les données selon l'onglet actif
  useEffect(() => {
    if (isOpen) {
      if (activeTab === 'followers') {
        loadFollowers();
      } else {
        loadFollowing();
      }
    }
  }, [isOpen, activeTab, userId]);

  const loadFollowers = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('followers')
        .select(`
          follower_id,
          profiles!followers_follower_id_fkey (
            id,
            username,
            profile_picture,
            role,
            business_name,
            city,
            country,
            followers_count,
            following_count
          )
        `)
        .eq('following_id', userId);

      if (error) throw error;

      const followersData = data?.map(item => ({
        id: item.profiles.id,
        username: item.profiles.username,
        profilePicture: item.profiles.profile_picture,
        role: item.profiles.role,
        businessName: item.profiles.business_name,
        city: item.profiles.city,
        country: item.profiles.country,
        followers_count: item.profiles.followers_count || 0,
        following_count: item.profiles.following_count || 0
      })) || [];

      setFollowers(followersData);
      
      // Vérifier le statut de suivi pour chaque utilisateur
      if (currentUser) {
        const statusPromises = followersData.map(async (user) => {
          const { data } = await supabase
            .from('followers')
            .select('id')
            .eq('follower_id', currentUser.id)
            .eq('following_id', user.id)
            .single();
          return { userId: user.id, isFollowing: !!data };
        });
        
        const statuses = await Promise.all(statusPromises);
        const statusMap = statuses.reduce((acc, { userId, isFollowing }) => {
          acc[userId] = isFollowing;
          return acc;
        }, {} as Record<string, boolean>);
        
        setFollowStatus(statusMap);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des abonnés:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadFollowing = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('followers')
        .select(`
          following_id,
          profiles!followers_following_id_fkey (
            id,
            username,
            profile_picture,
            role,
            business_name,
            city,
            country,
            followers_count,
            following_count
          )
        `)
        .eq('follower_id', userId);

      if (error) throw error;

      const followingData = data?.map(item => ({
        id: item.profiles.id,
        username: item.profiles.username,
        profilePicture: item.profiles.profile_picture,
        role: item.profiles.role,
        businessName: item.profiles.business_name,
        city: item.profiles.city,
        country: item.profiles.country,
        followers_count: item.profiles.followers_count || 0,
        following_count: item.profiles.following_count || 0
      })) || [];

      setFollowing(followingData);
      
      // Pour les abonnements, on sait déjà qu'on les suit tous
      const statusMap = followingData.reduce((acc, user) => {
        acc[user.id] = true;
        return acc;
      }, {} as Record<string, boolean>);
      
      setFollowStatus(statusMap);
    } catch (error) {
      console.error('Erreur lors du chargement des abonnements:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFollowToggle = async (targetUserId: string) => {
    if (!currentUser || targetUserId === currentUser.id) return;
    
    try {
      if (followStatus[targetUserId]) {
        await unfollowUser(targetUserId);
        setFollowStatus(prev => ({ ...prev, [targetUserId]: false }));
      } else {
        await followUser(targetUserId);
        setFollowStatus(prev => ({ ...prev, [targetUserId]: true }));
      }
    } catch (error) {
      console.error("Erreur lors de la modification de l'abonnement:", error);
    }
  };

  const filteredUsers = (activeTab === 'followers' ? followers : following).filter(user =>
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.businessName && user.businessName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl w-full max-w-2xl max-h-[80vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold text-white">
                {activeTab === 'followers' ? 'Abonnés' : 'Abonnements'}
              </h2>
              <p className="text-blue-100 mt-1">
                {activeTab === 'followers' 
                  ? `${userCounts.followers} abonné${userCounts.followers > 1 ? 's' : ''}`
                  : `${userCounts.following} abonnement${userCounts.following > 1 ? 's' : ''}`
                }
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:bg-white/20 p-2 rounded-full transition-colors"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b bg-gray-50">
          <button
            className={`flex-1 px-6 py-4 font-medium transition-colors flex items-center justify-center space-x-2 ${
              activeTab === 'followers'
                ? 'border-b-2 border-blue-500 text-blue-600 bg-white'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
            }`}
            onClick={() => setActiveTab('followers')}
          >
            <Users size={20} />
            <span>Abonnés ({userCounts.followers})</span>
          </button>
          <button
            className={`flex-1 px-6 py-4 font-medium transition-colors flex items-center justify-center space-x-2 ${
              activeTab === 'following'
                ? 'border-b-2 border-blue-500 text-blue-600 bg-white'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
            }`}
            onClick={() => setActiveTab('following')}
          >
            <UserPlus size={20} />
            <span>Abonnements ({userCounts.following})</span>
          </button>
        </div>

        {/* Search */}
        <div className="p-4 border-b">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Rechercher un utilisateur..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto max-h-96">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : filteredUsers.length > 0 ? (
            <div className="p-4 space-y-3">
              {filteredUsers.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                  <div className="flex items-center space-x-3">
                    <Avatar
                      src={user.profilePicture}
                      alt={user.businessName || user.username}
                      size="md"
                    />
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {user.role === UserRole.BUSINESS && user.businessName 
                          ? user.businessName 
                          : user.username}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {user.city && user.country ? `${user.city}, ${user.country}` : '@' + user.username}
                      </p>
                      <div className="flex items-center space-x-4 text-xs text-gray-400 mt-1">
                        <span>{user.followers_count} abonnés</span>
                        <span>{user.following_count} abonnements</span>
                      </div>
                    </div>
                  </div>
                  
                  {currentUser && user.id !== currentUser.id && (
                    <Button
                      onClick={() => handleFollowToggle(user.id)}
                      variant={followStatus[user.id] ? "outline" : "primary"}
                      size="sm"
                    >
                      {followStatus[user.id] ? 'Abonné' : 'Suivre'}
                    </Button>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-gray-500">
              <User size={48} className="mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">
                {searchTerm ? 'Aucun résultat' : `Aucun ${activeTab === 'followers' ? 'abonné' : 'abonnement'}`}
              </h3>
              <p className="text-center max-w-md">
                {searchTerm 
                  ? 'Aucun utilisateur ne correspond à votre recherche.'
                  : activeTab === 'followers'
                    ? 'Cet utilisateur n\'a pas encore d\'abonnés.'
                    : 'Cet utilisateur ne suit personne pour le moment.'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FollowersFollowingModal;
