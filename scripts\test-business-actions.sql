-- SCRIPT DE TEST POUR LES ACTIONS BUSINESS DÉVELOPPÉES
-- Exécutez ce script pour tester toutes les fonctionnalités

-- =====================================================
-- 1. CRÉER UNE ENTREPRISE DE TEST COMPLÈTE
-- =====================================================

DO $$
DECLARE
    test_business_id uuid := gen_random_uuid();
    test_user_id uuid := gen_random_uuid();
BEGIN
    RAISE NOTICE '🧪 CRÉATION D''ENTREPRISE DE TEST POUR ACTIONS';
    RAISE NOTICE '============================================';
    
    -- <PERSON><PERSON><PERSON> le profil utilisateur
    INSERT INTO profiles (
        id, username, email, role, status, is_verified, 
        profile_picture, country, city, phone, website, bio, created_at, updated_at
    ) VALUES (
        test_user_id,
        'test_actions_business',
        '<EMAIL>',
        'business',
        'member',
        false,
        'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=150',
        'France',
        'Test City',
        '+33 1 23 45 67 89',
        'https://test-business.demo',
        'Entreprise de test pour validation des actions administrateur',
        NOW(),
        NOW()
    );
    
    -- Créer le profil business
    INSERT INTO business_profiles (
        id, business_name, business_description, business_category,
        business_status, business_verified, created_at, updated_at
    ) VALUES (
        test_user_id,
        'Test Actions Business',
        'Entreprise créée spécialement pour tester toutes les actions administrateur',
        'Technology',
        'new',
        false,
        NOW(),
        NOW()
    );
    
    RAISE NOTICE '✅ Entreprise de test créée:';
    RAISE NOTICE '   ID: %', test_user_id;
    RAISE NOTICE '   Nom: Test Actions Business';
    RAISE NOTICE '   Email: <EMAIL>';
    RAISE NOTICE '   Statut: new';
    RAISE NOTICE '   Vérifiée: false';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 TESTEZ MAINTENANT DANS L''INTERFACE:';
    RAISE NOTICE '1. Allez dans "Gestion Entreprises"';
    RAISE NOTICE '2. Cherchez "Test Actions Business"';
    RAISE NOTICE '3. Cliquez sur ⋮ pour ouvrir les actions';
    RAISE NOTICE '4. Testez chaque bouton développé';
    
END $$;

-- =====================================================
-- 2. VÉRIFIER LES STATUTS DISPONIBLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📊 STATUTS BUSINESS DISPONIBLES:';
    RAISE NOTICE '================================';
    RAISE NOTICE '🆕 new - Entreprise récemment inscrite';
    RAISE NOTICE '✅ verified - Entreprise validée et vérifiée';
    RAISE NOTICE '💎 premium - Abonnement premium actif';
    RAISE NOTICE '⭐ featured - Entreprise promue et mise en avant';
    RAISE NOTICE '🤝 partner - Partenaire officiel de la plateforme';
    RAISE NOTICE '⏸️ suspended - Entreprise suspendue';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 ACTIONS TESTABLES:';
    RAISE NOTICE '• 👁️ Voir les détails complets';
    RAISE NOTICE '• 🛡️ Vérifier l''entreprise';
    RAISE NOTICE '• 📈 Changer le statut';
    RAISE NOTICE '• ⏸️ Suspendre l''entreprise';
    RAISE NOTICE '• ▶️ Réactiver l''entreprise (si suspendue)';
    RAISE NOTICE '• 🌐 Voir profil public';
END $$;

-- =====================================================
-- 3. SIMULER DIFFÉRENTS SCÉNARIOS DE TEST
-- =====================================================

-- Créer des entreprises avec différents statuts pour tester
DO $$
DECLARE
    business_statuses text[] := ARRAY['new', 'verified', 'premium', 'featured', 'partner', 'suspended'];
    status_name text;
    test_id uuid;
    i integer := 1;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎭 CRÉATION DE SCÉNARIOS DE TEST:';
    RAISE NOTICE '================================';
    
    FOREACH status_name IN ARRAY business_statuses
    LOOP
        test_id := gen_random_uuid();
        
        -- Créer le profil utilisateur
        INSERT INTO profiles (
            id, username, email, role, status, is_verified, 
            profile_picture, country, city, bio, created_at, updated_at
        ) VALUES (
            test_id,
            'test_' || status_name || '_business',
            'test.' || status_name || '@scenario.demo',
            'business',
            'member',
            CASE WHEN status_name IN ('verified', 'premium', 'featured', 'partner') THEN true ELSE false END,
            'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=150',
            'France',
            'Test City ' || i,
            'Entreprise de test avec statut ' || status_name,
            NOW() - (i || ' days')::interval,
            NOW()
        ) ON CONFLICT (email) DO NOTHING;
        
        -- Créer le profil business
        INSERT INTO business_profiles (
            id, business_name, business_description, business_category,
            business_status, business_verified, 
            suspension_reason,
            created_at, updated_at
        ) VALUES (
            test_id,
            'Test ' || initcap(status_name) || ' Business',
            'Entreprise de test avec statut ' || status_name || ' pour validation des actions',
            'Technology',
            status_name,
            CASE WHEN status_name IN ('verified', 'premium', 'featured', 'partner') THEN true ELSE false END,
            CASE WHEN status_name = 'suspended' THEN 'Suspension de test pour validation des actions' ELSE NULL END,
            NOW() - (i || ' days')::interval,
            NOW()
        ) ON CONFLICT (id) DO NOTHING;
        
        RAISE NOTICE '✅ Créé: Test % Business (statut: %)', initcap(status_name), status_name;
        i := i + 1;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎯 SCÉNARIOS CRÉÉS POUR TESTER:';
    RAISE NOTICE '• Test New Business - Testez la vérification';
    RAISE NOTICE '• Test Verified Business - Testez le changement de statut';
    RAISE NOTICE '• Test Premium Business - Testez la suspension';
    RAISE NOTICE '• Test Featured Business - Testez les détails';
    RAISE NOTICE '• Test Partner Business - Testez le profil public';
    RAISE NOTICE '• Test Suspended Business - Testez la réactivation';
END $$;

-- =====================================================
-- 4. VÉRIFICATION DES DONNÉES CRÉÉES
-- =====================================================

SELECT 
    '🏢 ENTREPRISES DE TEST CRÉÉES' as titre,
    bp.business_name,
    p.username,
    bp.business_status,
    bp.business_verified,
    CASE 
        WHEN bp.business_status = 'suspended' THEN '⏸️ Suspendue'
        WHEN bp.business_verified THEN '✅ Vérifiée'
        ELSE '🆕 Non vérifiée'
    END as etat,
    bp.created_at
FROM business_profiles bp
JOIN profiles p ON bp.id = p.id
WHERE p.email LIKE '%@%.demo'
ORDER BY bp.created_at DESC;

-- =====================================================
-- 5. INSTRUCTIONS DE TEST DÉTAILLÉES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📋 GUIDE DE TEST COMPLET:';
    RAISE NOTICE '========================';
    RAISE NOTICE '';
    RAISE NOTICE '1️⃣ TEST DE VÉRIFICATION:';
    RAISE NOTICE '   • Cherchez "Test New Business"';
    RAISE NOTICE '   • Cliquez ⋮ → 🛡️ Vérifier l''entreprise';
    RAISE NOTICE '   • Vérifiez que le statut change et le badge apparaît';
    RAISE NOTICE '';
    RAISE NOTICE '2️⃣ TEST DE CHANGEMENT DE STATUT:';
    RAISE NOTICE '   • Cherchez "Test Verified Business"';
    RAISE NOTICE '   • Cliquez ⋮ → 📈 Changer le statut';
    RAISE NOTICE '   • Testez le passage vers "premium"';
    RAISE NOTICE '';
    RAISE NOTICE '3️⃣ TEST DE SUSPENSION:';
    RAISE NOTICE '   • Cherchez "Test Premium Business"';
    RAISE NOTICE '   • Cliquez ⋮ → ⏸️ Suspendre l''entreprise';
    RAISE NOTICE '   • Choisissez une raison et confirmez';
    RAISE NOTICE '';
    RAISE NOTICE '4️⃣ TEST DE RÉACTIVATION:';
    RAISE NOTICE '   • Cherchez "Test Suspended Business"';
    RAISE NOTICE '   • Cliquez ⋮ → ▶️ Réactiver l''entreprise';
    RAISE NOTICE '   • Vérifiez que le statut redevient "verified"';
    RAISE NOTICE '';
    RAISE NOTICE '5️⃣ TEST DES DÉTAILS:';
    RAISE NOTICE '   • Cherchez n''importe quelle entreprise';
    RAISE NOTICE '   • Cliquez ⋮ → 👁️ Voir les détails complets';
    RAISE NOTICE '   • Vérifiez que la modal s''ouvre avec toutes les infos';
    RAISE NOTICE '';
    RAISE NOTICE '6️⃣ TEST DU PROFIL PUBLIC:';
    RAISE NOTICE '   • Cherchez "Test Partner Business"';
    RAISE NOTICE '   • Cliquez ⋮ → 🌐 Voir profil public';
    RAISE NOTICE '   • Vérifiez que l''URL est correcte';
    RAISE NOTICE '';
    RAISE NOTICE '✅ TOUS LES BOUTONS SONT MAINTENANT FONCTIONNELS !';
    RAISE NOTICE '';
    RAISE NOTICE '🧹 NETTOYAGE (optionnel):';
    RAISE NOTICE 'Pour supprimer les données de test:';
    RAISE NOTICE 'DELETE FROM business_profiles WHERE business_name LIKE ''Test % Business'';';
    RAISE NOTICE 'DELETE FROM profiles WHERE email LIKE ''test.%@%.demo'';';
END $$;
