import { supabase } from '../lib/supabase';
import { UserStatus } from '../types';

/**
 * Initialise les statuts des utilisateurs dans la base de données
 */
export const initializeUserStatuses = async (): Promise<{
  success: boolean;
  message: string;
  stats?: {
    newbie: number;
    member: number;
    contributor: number;
    discoverer: number;
    influencer: number;
    leader: number;
  };
}> => {
  try {
    console.log('🚀 Début de l\'initialisation des statuts utilisateur...');

    // 1. Mettre tous les utilisateurs sans statut en NEWBIE
    const { error: newbieError } = await supabase
      .from('profiles')
      .update({ status: UserStatus.NEWBIE })
      .or('status.is.null,status.not.in.(newbie,member,contributor,discoverer,influencer,leader)');

    if (newbieError) {
      console.error('Erreur lors de l\'initialisation NEWBIE:', newbieError);
      throw newbieError;
    }

    console.log('✅ Étape 1: Utilisateurs sans statut mis en NEWBIE');

    // 2. Passer en MEMBER ceux qui ont un profil complet
    const { error: memberError } = await supabase
      .from('profiles')
      .update({ status: UserStatus.MEMBER })
      .eq('status', UserStatus.NEWBIE)
      .not('profile_picture', 'is', null)
      .not('cover_photo_url', 'is', null)
      .not('city', 'is', null)
      .not('country', 'is', null);

    if (memberError) {
      console.error('Erreur lors de la mise à jour MEMBER:', memberError);
      throw memberError;
    }

    console.log('✅ Étape 2: Utilisateurs avec profil complet mis en MEMBER');

    // 3. Passer en CONTRIBUTOR ceux qui ont au moins 1 post ou commentaire
    const { error: contributorError } = await supabase
      .from('profiles')
      .update({ status: UserStatus.CONTRIBUTOR })
      .in('status', [UserStatus.NEWBIE, UserStatus.MEMBER])
      .or('post_count.gt.0,comment_count.gt.0');

    if (contributorError) {
      console.error('Erreur lors de la mise à jour CONTRIBUTOR:', contributorError);
      throw contributorError;
    }

    console.log('✅ Étape 3: Utilisateurs actifs mis en CONTRIBUTOR');

    // 4. Récupérer les statistiques finales
    const { data: stats, error: statsError } = await supabase
      .from('profiles')
      .select('status')
      .not('status', 'is', null);

    if (statsError) {
      console.error('Erreur lors de la récupération des statistiques:', statsError);
      throw statsError;
    }

    // Compter les statuts
    const statusCounts = {
      newbie: 0,
      member: 0,
      contributor: 0,
      discoverer: 0,
      influencer: 0,
      leader: 0
    };

    stats?.forEach(user => {
      if (user.status in statusCounts) {
        statusCounts[user.status as keyof typeof statusCounts]++;
      }
    });

    console.log('📊 Statistiques finales:', statusCounts);

    return {
      success: true,
      message: 'Statuts utilisateur initialisés avec succès !',
      stats: statusCounts
    };

  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation des statuts:', error);
    return {
      success: false,
      message: `Erreur lors de l'initialisation: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
    };
  }
};

/**
 * Met à jour le statut d'un utilisateur spécifique selon ses métriques
 */
export const updateSingleUserStatus = async (userId: string): Promise<{
  success: boolean;
  message: string;
  oldStatus?: UserStatus;
  newStatus?: UserStatus;
}> => {
  try {
    // Récupérer les données de l'utilisateur
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      throw new Error('Utilisateur introuvable');
    }

    const oldStatus = user.status as UserStatus;

    // Vérifier si le profil est complet
    const profileComplete = !!(
      user.profile_picture &&
      user.cover_photo_url &&
      user.city &&
      user.country
    );

    // Calculer l'âge du compte en jours
    const accountAgeDays = Math.floor(
      (Date.now() - new Date(user.created_at).getTime()) / (1000 * 60 * 60 * 24)
    );

    // Récupérer le nombre de likes reçus (approximation)
    const { data: posts } = await supabase
      .from('posts')
      .select('likes')
      .eq('user_id', userId);

    let likesReceived = 0;
    if (posts) {
      likesReceived = posts.reduce((total, post) => {
        const likes = Array.isArray(post.likes) ? post.likes.length : 0;
        return total + likes;
      }, 0);
    }

    // Déterminer le nouveau statut
    let newStatus = UserStatus.NEWBIE;

    if (
      profileComplete &&
      (user.post_count || 0) >= 200 &&
      (user.comment_count || 0) >= 500 &&
      likesReceived >= 2000 &&
      (user.followers_count || 0) >= 500 &&
      (user.recommendations_count || 0) >= 1000 &&
      accountAgeDays >= 180
    ) {
      newStatus = UserStatus.LEADER;
    } else if (
      profileComplete &&
      (user.post_count || 0) >= 50 &&
      (user.comment_count || 0) >= 100 &&
      likesReceived >= 500 &&
      (user.followers_count || 0) >= 100 &&
      (user.recommendations_count || 0) >= 200 &&
      accountAgeDays >= 90
    ) {
      newStatus = UserStatus.INFLUENCER;
    } else if (
      profileComplete &&
      (user.post_count || 0) >= 10 &&
      (user.comment_count || 0) >= 25 &&
      likesReceived >= 50 &&
      (user.followers_count || 0) >= 20 &&
      accountAgeDays >= 30
    ) {
      newStatus = UserStatus.DISCOVERER;
    } else if (
      profileComplete &&
      ((user.post_count || 0) >= 1 || (user.comment_count || 0) >= 1)
    ) {
      newStatus = UserStatus.CONTRIBUTOR;
    } else if (profileComplete) {
      newStatus = UserStatus.MEMBER;
    }

    // Mettre à jour uniquement si c'est une progression
    const statusOrder = [
      UserStatus.NEWBIE,
      UserStatus.MEMBER,
      UserStatus.CONTRIBUTOR,
      UserStatus.DISCOVERER,
      UserStatus.INFLUENCER,
      UserStatus.LEADER
    ];

    const oldIndex = statusOrder.indexOf(oldStatus);
    const newIndex = statusOrder.indexOf(newStatus);

    if (newIndex > oldIndex) {
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ status: newStatus })
        .eq('id', userId);

      if (updateError) {
        throw updateError;
      }

      return {
        success: true,
        message: `Statut mis à jour de ${oldStatus} vers ${newStatus}`,
        oldStatus,
        newStatus
      };
    } else {
      return {
        success: true,
        message: 'Aucune progression de statut détectée',
        oldStatus,
        newStatus: oldStatus
      };
    }

  } catch (error) {
    console.error('Erreur lors de la mise à jour du statut:', error);
    return {
      success: false,
      message: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
    };
  }
};

/**
 * Force la mise à jour de tous les statuts utilisateur
 */
export const forceUpdateAllStatuses = async (): Promise<{
  success: boolean;
  message: string;
  processed: number;
  updated: number;
}> => {
  try {
    const { data: users, error } = await supabase
      .from('profiles')
      .select('id');

    if (error || !users) {
      throw error;
    }

    let processed = 0;
    let updated = 0;

    for (const user of users) {
      try {
        const result = await updateSingleUserStatus(user.id);
        processed++;
        if (result.success && result.oldStatus !== result.newStatus) {
          updated++;
        }
      } catch (error) {
        console.error(`Erreur pour l'utilisateur ${user.id}:`, error);
      }
    }

    return {
      success: true,
      message: `Traitement terminé: ${updated} utilisateurs mis à jour sur ${processed} traités`,
      processed,
      updated
    };

  } catch (error) {
    console.error('Erreur lors de la mise à jour globale:', error);
    return {
      success: false,
      message: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
      processed: 0,
      updated: 0
    };
  }
};
