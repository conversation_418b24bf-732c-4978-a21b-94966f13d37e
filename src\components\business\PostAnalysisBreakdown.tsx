import React from 'react';
import { 
  MessageCircle, Heart, Share2, ThumbsUp, ThumbsDown, 
  Building2, User, TrendingUp, TrendingDown, Award 
} from 'lucide-react';
import Card, { Card<PERSON>ody, CardHeader } from '../ui/Card';

interface PostAnalysisBreakdownProps {
  posts: any[];
  isVisible: boolean;
}

const PostAnalysisBreakdown: React.FC<PostAnalysisBreakdownProps> = ({ posts, isVisible }) => {
  if (!isVisible || posts.length === 0) return null;

  // Analyser les types de posts selon la structure définie
  const coupDeCoeur = posts.filter(p => p.type === 'coup_de_coeur');
  const coupDeGueule = posts.filter(p => p.type === 'coup_de_gueule');
  const demandeAvis = posts.filter(p => p.type === 'demande_avis' || p.source === 'business_post');

  // Analyser les interactions
  const totalLikes = posts.reduce((sum, p) => sum + (p.likesCount || 0), 0);
  const totalShares = posts.reduce((sum, p) => sum + (p.sharesCount || 0), 0);
  const totalRecommendations = posts.reduce((sum, p) => sum + (p.recommendationsCount || 0), 0);
  const totalComments = posts.reduce((sum, p) => sum + (p.commentsCount || 0), 0);

  // Estimer les commentaires favorables vs défavorables
  const favorableComments = Math.round(
    coupDeCoeur.reduce((sum, p) => sum + (p.commentsCount || 0), 0) * 0.8 +
    demandeAvis.reduce((sum, p) => sum + (p.commentsCount || 0), 0) * 0.6
  );
  
  const unfavorableComments = Math.round(
    coupDeGueule.reduce((sum, p) => sum + (p.commentsCount || 0), 0) * 0.8 +
    demandeAvis.reduce((sum, p) => sum + (p.commentsCount || 0), 0) * 0.4
  );

  // Calculer les taux
  const satisfactionRate = posts.length > 0 ? (coupDeCoeur.length / (coupDeCoeur.length + coupDeGueule.length)) * 100 : 0;
  const engagementRate = posts.length > 0 ? (totalLikes + totalShares + totalRecommendations + totalComments) / posts.length : 0;

  return (
    <Card className="border-l-4 border-blue-500 bg-blue-50">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <MessageCircle className="text-blue-600" size={24} />
          <div>
            <h3 className="text-lg font-semibold text-blue-900">
              Analyse Détaillée des Posts
            </h3>
            <p className="text-blue-700 text-sm">
              Répartition selon les types de posts et interactions
            </p>
          </div>
        </div>
      </CardHeader>
      
      <CardBody>
        <div className="space-y-6">
          {/* Types de Posts */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
              <Building2 className="mr-2 text-purple-600" size={18} />
              Types de Posts
            </h4>
            <div className="grid grid-cols-3 gap-4">
              {/* Demandes d'avis */}
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Building2 className="text-purple-600" size={20} />
                  <span className="font-medium text-gray-900">Demandes d'Avis</span>
                </div>
                <div className="text-2xl font-bold text-purple-600">{demandeAvis.length}</div>
                <div className="text-sm text-gray-600">Posts d'entreprise pour recueillir des avis</div>
                <div className="mt-2 text-xs text-gray-500">
                  {demandeAvis.length > 0 && (
                    <>Moyenne: {(demandeAvis.reduce((sum, p) => sum + (p.commentsCount || 0), 0) / demandeAvis.length).toFixed(1)} avis/post</>
                  )}
                </div>
              </div>

              {/* Coups de cœur */}
              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Heart className="text-green-600" size={20} />
                  <span className="font-medium text-gray-900">Coups de Cœur</span>
                </div>
                <div className="text-2xl font-bold text-green-600">{coupDeCoeur.length}</div>
                <div className="text-sm text-gray-600">Consommateurs satisfaits partageant leur expérience</div>
                <div className="mt-2 text-xs text-gray-500">
                  Engagement: {coupDeCoeur.reduce((sum, p) => sum + (p.likesCount || 0) + (p.sharesCount || 0), 0)} interactions
                </div>
              </div>

              {/* Coups de gueule */}
              <div className="bg-white rounded-lg p-4 border border-red-200">
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingDown className="text-red-600" size={20} />
                  <span className="font-medium text-gray-900">Coups de Gueule</span>
                </div>
                <div className="text-2xl font-bold text-red-600">{coupDeGueule.length}</div>
                <div className="text-sm text-gray-600">Consommateurs insatisfaits partageant leur expérience</div>
                <div className="mt-2 text-xs text-gray-500">
                  Nécessitent une attention immédiate
                </div>
              </div>
            </div>
          </div>

          {/* Interactions */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
              <TrendingUp className="mr-2 text-blue-600" size={18} />
              Interactions des Consommateurs
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white rounded-lg p-3 border border-blue-200">
                <div className="flex items-center space-x-2 mb-1">
                  <Heart className="text-red-500" size={16} />
                  <span className="text-sm font-medium text-gray-900">J'aime</span>
                </div>
                <div className="text-xl font-bold text-gray-900">{totalLikes}</div>
                <div className="text-xs text-gray-600">Appréciations</div>
              </div>

              <div className="bg-white rounded-lg p-3 border border-blue-200">
                <div className="flex items-center space-x-2 mb-1">
                  <Share2 className="text-blue-500" size={16} />
                  <span className="text-sm font-medium text-gray-900">Partages</span>
                </div>
                <div className="text-xl font-bold text-gray-900">{totalShares}</div>
                <div className="text-xs text-gray-600">Diffusion</div>
              </div>

              <div className="bg-white rounded-lg p-3 border border-blue-200">
                <div className="flex items-center space-x-2 mb-1">
                  <Award className="text-green-500" size={16} />
                  <span className="text-sm font-medium text-gray-900">Recommandations</span>
                </div>
                <div className="text-xl font-bold text-gray-900">{totalRecommendations}</div>
                <div className="text-xs text-gray-600">Produits recommandés</div>
              </div>

              <div className="bg-white rounded-lg p-3 border border-blue-200">
                <div className="flex items-center space-x-2 mb-1">
                  <MessageCircle className="text-purple-500" size={16} />
                  <span className="text-sm font-medium text-gray-900">Commentaires</span>
                </div>
                <div className="text-xl font-bold text-gray-900">{totalComments}</div>
                <div className="text-xs text-gray-600">Avis détaillés</div>
              </div>
            </div>
          </div>

          {/* Analyse des Commentaires */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
              <MessageCircle className="mr-2 text-indigo-600" size={18} />
              Analyse des Commentaires
            </h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="flex items-center space-x-2 mb-2">
                  <ThumbsUp className="text-green-600" size={20} />
                  <span className="font-medium text-gray-900">Commentaires Favorables</span>
                </div>
                <div className="text-2xl font-bold text-green-600">{favorableComments}</div>
                <div className="text-sm text-gray-600">
                  Principalement sur les "coups de cœur"
                </div>
              </div>

              <div className="bg-white rounded-lg p-4 border border-red-200">
                <div className="flex items-center space-x-2 mb-2">
                  <ThumbsDown className="text-red-600" size={20} />
                  <span className="font-medium text-gray-900">Commentaires Défavorables</span>
                </div>
                <div className="text-2xl font-bold text-red-600">{unfavorableComments}</div>
                <div className="text-sm text-gray-600">
                  Principalement sur les "coups de gueule"
                </div>
              </div>
            </div>
          </div>

          {/* Métriques Globales */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
              <TrendingUp className="mr-2 text-green-600" size={18} />
              Métriques Globales
            </h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900">Taux de Satisfaction</span>
                  <span className={`text-sm font-medium ${satisfactionRate >= 70 ? 'text-green-600' : satisfactionRate >= 50 ? 'text-yellow-600' : 'text-red-600'}`}>
                    {satisfactionRate.toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${satisfactionRate >= 70 ? 'bg-green-500' : satisfactionRate >= 50 ? 'bg-yellow-500' : 'bg-red-500'}`}
                    style={{ width: `${satisfactionRate}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-600 mt-1">
                  Basé sur le ratio coups de cœur / coups de gueule
                </div>
              </div>

              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900">Engagement Moyen</span>
                  <span className="text-sm font-medium text-blue-600">
                    {engagementRate.toFixed(1)}/post
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  {totalLikes + totalShares + totalRecommendations + totalComments} interactions totales
                </div>
                <div className="text-xs text-gray-600 mt-1">
                  J'aime + Partages + Recommandations + Commentaires
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default PostAnalysisBreakdown;
