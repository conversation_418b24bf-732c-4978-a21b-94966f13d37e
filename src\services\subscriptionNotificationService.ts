import { supabase } from '../lib/supabase';

export interface SubscriptionNotification {
  id: string;
  business_id: string;
  type: 'activation' | 'expiration_warning' | 'expired' | 'renewal';
  title: string;
  message: string;
  plan_name: string;
  days_remaining?: number;
  is_read: boolean;
  created_at: string;
}

export class SubscriptionNotificationService {
  
  /**
   * Envoie une notification d'activation d'abonnement
   */
  static async sendActivationNotification(
    businessId: string, 
    planName: string, 
    daysRemaining: number
  ): Promise<boolean> {
    try {
      console.log('📧 Envoi notification d\'activation pour:', businessId);

      const notification: Omit<SubscriptionNotification, 'id' | 'created_at'> = {
        business_id: businessId,
        type: 'activation',
        title: '🎉 Abonnement Activé !',
        message: `Votre abonnement ${planName} a été activé avec succès. Vous avez ${daysRemaining} jour${daysRemaining > 1 ? 's' : ''} d'accès complet à toutes les fonctionnalités.`,
        plan_name: planName,
        days_remaining: daysRemaining,
        is_read: false
      };

      // Insérer la notification dans la base de données
      const { data, error } = await supabase
        .from('subscription_notifications')
        .insert(notification)
        .select()
        .single();

      if (error) {
        console.error('❌ Erreur lors de l\'insertion de la notification:', error);
        return false;
      }

      console.log('✅ Notification d\'activation envoyée:', data.id);

      // Envoyer également une notification système générale
      await this.sendSystemNotification(businessId, {
        title: '🎉 Abonnement Activé',
        message: `Votre abonnement ${planName} est maintenant actif !`,
        type: 'subscription_activated',
        data: {
          plan_name: planName,
          days_remaining: daysRemaining
        }
      });

      return true;

    } catch (error) {
      console.error('❌ Erreur dans sendActivationNotification:', error);
      return false;
    }
  }

  /**
   * Envoie une notification d'avertissement d'expiration
   */
  static async sendExpirationWarning(
    businessId: string, 
    planName: string, 
    daysRemaining: number
  ): Promise<boolean> {
    try {
      console.log('⚠️ Envoi notification d\'expiration pour:', businessId);

      const notification: Omit<SubscriptionNotification, 'id' | 'created_at'> = {
        business_id: businessId,
        type: 'expiration_warning',
        title: '⚠️ Abonnement expire bientôt',
        message: `Votre abonnement ${planName} expire dans ${daysRemaining} jour${daysRemaining > 1 ? 's' : ''}. Pensez à le renouveler pour continuer à profiter de toutes les fonctionnalités.`,
        plan_name: planName,
        days_remaining: daysRemaining,
        is_read: false
      };

      const { data, error } = await supabase
        .from('subscription_notifications')
        .insert(notification)
        .select()
        .single();

      if (error) {
        console.error('❌ Erreur lors de l\'insertion de la notification:', error);
        return false;
      }

      console.log('✅ Notification d\'expiration envoyée:', data.id);
      return true;

    } catch (error) {
      console.error('❌ Erreur dans sendExpirationWarning:', error);
      return false;
    }
  }

  /**
   * Récupère les notifications d'abonnement pour une entreprise
   */
  static async getSubscriptionNotifications(businessId: string): Promise<SubscriptionNotification[]> {
    try {
      const { data, error } = await supabase
        .from('subscription_notifications')
        .select('*')
        .eq('business_id', businessId)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('❌ Erreur lors de la récupération des notifications:', error);
        return [];
      }

      return data || [];

    } catch (error) {
      console.error('❌ Erreur dans getSubscriptionNotifications:', error);
      return [];
    }
  }

  /**
   * Marque une notification comme lue
   */
  static async markAsRead(notificationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('subscription_notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) {
        console.error('❌ Erreur lors du marquage comme lu:', error);
        return false;
      }

      return true;

    } catch (error) {
      console.error('❌ Erreur dans markAsRead:', error);
      return false;
    }
  }

  /**
   * Envoie une notification système générale
   */
  private static async sendSystemNotification(
    businessId: string, 
    notification: {
      title: string;
      message: string;
      type: string;
      data?: any;
    }
  ): Promise<void> {
    try {
      // Utiliser le service de notification existant si disponible
      const { NotificationService } = await import('./notificationService');
      
      await NotificationService.createNotification({
        userId: businessId,
        type: notification.type as any,
        title: notification.title,
        message: notification.message,
        data: notification.data
      });

      console.log('✅ Notification système envoyée');

    } catch (error) {
      console.warn('⚠️ Impossible d\'envoyer la notification système:', error);
      // Ne pas faire échouer le processus principal
    }
  }

  /**
   * Vérifie et envoie les notifications d'expiration automatiques
   */
  static async checkAndSendExpirationWarnings(): Promise<void> {
    try {
      console.log('🔍 Vérification des abonnements qui expirent bientôt...');

      // Récupérer les abonnements qui expirent dans 7, 3 ou 1 jour(s)
      const warningDays = [7, 3, 1];
      
      for (const days of warningDays) {
        const targetDate = new Date();
        targetDate.setDate(targetDate.getDate() + days);
        const targetDateStr = targetDate.toISOString().split('T')[0];

        const { data: subscriptions, error } = await supabase
          .from('business_subscriptions')
          .select(`
            business_id,
            end_date,
            subscription_plans (
              name
            )
          `)
          .eq('is_active', true)
          .gte('end_date', targetDateStr)
          .lt('end_date', targetDateStr + ' 23:59:59');

        if (error) {
          console.error('❌ Erreur lors de la récupération des abonnements:', error);
          continue;
        }

        // Envoyer les notifications
        for (const subscription of subscriptions || []) {
          await this.sendExpirationWarning(
            subscription.business_id,
            subscription.subscription_plans?.name || 'Plan inconnu',
            days
          );
        }
      }

      console.log('✅ Vérification des notifications d\'expiration terminée');

    } catch (error) {
      console.error('❌ Erreur dans checkAndSendExpirationWarnings:', error);
    }
  }

  /**
   * Nettoie les anciennes notifications (plus de 30 jours)
   */
  static async cleanupOldNotifications(): Promise<void> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { error } = await supabase
        .from('subscription_notifications')
        .delete()
        .lt('created_at', thirtyDaysAgo.toISOString());

      if (error) {
        console.error('❌ Erreur lors du nettoyage:', error);
        return;
      }

      console.log('✅ Nettoyage des anciennes notifications terminé');

    } catch (error) {
      console.error('❌ Erreur dans cleanupOldNotifications:', error);
    }
  }
}
