// User Types
export enum UserRole {
  STANDARD = 'standard',
  BUSINESS = 'business',
  ADMIN = 'admin'
}

export enum UserStatus {
  NEWBIE = 'newbie',           // Just signed up, profile incomplete
  MEMBER = 'member',           // Profile complete (avatar, cover, country, city)
  CONTRIBUTOR = 'contributor', // Made first post or comment
  DISCOVERER = 'discoverer',     // Active, making posts/comments
  INFLUENCER = 'influencer',     // High quality/popular posts
  LEADER = 'leader'            // Top tier
}

export enum BusinessStatus {
  NEW = 'new',                 // Nouveau compte entreprise
  PENDING = 'pending',         // En attente de vérification
  ACTIVE = 'active',          // Compte actif
  INACTIVE = 'inactive',       // Compte inactif
  VERIFIED = 'verified',       // Compte vérifié
  PREMIUM = 'premium',         // Abonnement premium
  FEATURED = 'featured',       // Mis en avant sur la plateforme
  PARTNER = 'partner'          // Partenaire officiel
}

export enum PostType {
  FAVORITE = 'favorite',  // Changed to lowercase to match DB constraint
  COMPLAINT = 'complaint', // Changed to lowercase to match DB constraint
  REVIEW = 'review'        // Changed to lowercase to match DB constraint
}

export enum QualityBadge {
  GOLD = 'gold',
  SILVER = 'silver',
  BRONZE = 'bronze',
  NONE = 'none'
}

export enum NegotiationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  COUNTER_OFFER = 'counter_offer'
}

// User Interfaces
export interface IUser {
  id: string;
  username: string;
  email: string;
  profilePicture?: string;
  role: UserRole;
  status: UserStatus;
  following_count: number;
  followers_count: number;
  createdAt: Date;
  coverPhotoUrl?: string;
  country?: string;
  city?: string;
  age?: number;
  gender?: 'Homme' | 'Femme' | 'Autre' | 'Préfère ne pas dire';
  post_count: number;
  comment_count: number;
  recommendations_count: number;
  profile_completed_at?: string | Date;
  // Nouveaux champs du formulaire développé
  bio?: string;
  website?: string;
  phone?: string;
  profession?: string;
  interests?: string;
}

// Type pour les données du formulaire d'édition de profil
export interface IProfileFormData {
  username: string;
  city: string;
  country: string;
  age: string;
  gender: string;
  bio: string;
  website: string;
  phone: string;
  profession: string;
  interests: string;
  profilePictureFile: File | null;
  coverPhotoFile: File | null;
}

// Type pour les statistiques de complétion du profil
export interface IProfileCompletionStats {
  completion_percentage: number;
  missing_fields: string[];
  total_fields: number;
  completed_fields: number;
}

// Type pour la réponse de mise à jour du profil
export interface IProfileUpdateResponse {
  success?: boolean;
  error?: string;
  message?: string;
}

export interface IBusinessUser extends IUser {
  businessName: string;
  businessStatus?: BusinessStatus;
  businessDescription?: string;
  businessCategory?: string;
  businessAddress?: string;
  businessPhone?: string;
  businessEmail?: string;
  businessWebsite?: string;
  businessLicense?: string;
  foundedYear?: number;
  employeeCount?: number;
  // city et country sont déjà définis dans IUser
  catalog?: IProduct[];
  wallet?: number;
  salesCount?: number;
  averageRating?: number;
  totalReviews?: number;
  followers?: number;
  following?: number;
}

// Post Interface
export interface IPost {
  id: string;
  title?: string; // Made title optional
  type: PostType;
  businessName: string;
  productName: string;
  category: string;
  description: string;
  images: string[];
  rating: number;
  tags: string[];
  userId: string;
  username: string;
  userProfilePicture?: string;
  authorStatus?: UserStatus; // Added: status of the post author
  authorRole?: UserRole; // Ajouté: rôle explicite de l'auteur (business, standard, admin)
  businessStatus?: BusinessStatus; // Ajouté: statut business pour les entreprises mentionnées
  likes: string[];
  recommendations?: string[];
  shares?: string[]; // Ajouté pour suivre les partages
  comments: IComment[];
  createdAt: Date;
}

// Comment Interface
export interface IComment {
  id: string;
  userId: string;
  username: string;
  profilePicture: string;
  content: string;
  hasUsedProduct: boolean;
  rating?: number;
  createdAt: Date;
  // Optional: for business-authored comments
  businessName?: string;
  authorRole?: UserRole;
  authorStatus?: UserStatus; // Added: status of the comment author
}

// Product Interface
export interface IProduct {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  businessId: string;
  averageRating: number;
  qualityBadge: QualityBadge;
  images: string[];
  createdAt: Date;
  negotiable?: boolean;
  stock?: number;
}

// Interface pour les commentaires du marketplace
export interface IMarketComment {
  id: string;
  productId: string;
  userId: string;
  comment: string;
  isVerifiedPurchase: boolean;
  isApproved: boolean;
  parentCommentId?: string;
  createdAt: Date;
  updatedAt: Date;
  // Informations utilisateur (jointure)
  username?: string;
  profilePicture?: string;
  userRating?: number;
  // Réponses (commentaires enfants)
  replies?: IMarketComment[];
}

// Interface pour les notes du marketplace
export interface IMarketRating {
  id: string;
  productId: string;
  userId: string;
  rating: number; // 1-5
  isVerifiedPurchase: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Interface pour les statistiques des produits
export interface IProductStats {
  productId: string;
  totalRatings: number;
  averageRating: number;
  totalComments: number;
  verifiedRatings: number;
  verifiedComments: number;
}

// Interface pour créer un nouveau commentaire
export interface ICreateComment {
  productId: string;
  comment: string;
  parentCommentId?: string;
}

// Interface pour créer une nouvelle note
export interface ICreateRating {
  productId: string;
  rating: number;
}

// Negotiation Interface
export interface INegotiation {
  id: string;
  productId: string;
  buyerId: string;
  sellerId: string;
  initialPrice: number;
  currentOffer: number;
  status: NegotiationStatus;
  messages: INegotiationMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface INegotiationMessage {
  id: string;
  negotiationId: string;
  senderId: string;
  content: string;
  offer?: number;
  createdAt: Date;
}

// Order Interface
export interface IOrder {
  id: string;
  buyerId: string;
  sellerId: string;
  productId: string;
  quantity: number;
  totalPrice: number;
  status: OrderStatus;
  negotiationId?: string;
  createdAt: Date;
}

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

// Notification Interface
export interface INotification {
  id: string;
  userId: string;
  type: 'like' | 'comment' | 'follow' | 'rating' | 'badge' | 'negotiation' | 'order';
  content: string;
  read: boolean;
  relatedId: string;
  createdAt: Date;
}

// =====================================================
// FOLLOW REQUEST SYSTEM INTERFACES
// =====================================================

// Follow Request Interfaces
export interface IFollowRequest {
  id: string;
  requesterId: string;
  targetId: string;
  status: 'pending' | 'accepted' | 'rejected' | 'cancelled';
  requestType: 'follow' | 'subscribe';
  message?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  respondedAt?: Date;
  // Informations enrichies
  requesterProfile?: IUser;
  targetProfile?: IUser;
}

export interface IPrivacySettings {
  id: string;
  userId: string;
  // Paramètres de suivi
  requireFollowApproval: boolean;
  allowFollowRequests: boolean;
  autoAcceptVerifiedUsers: boolean;
  autoAcceptBusinessUsers: boolean;
  // Paramètres de notifications
  notifyFollowRequests: boolean;
  notifyNewFollowers: boolean;
  notifyFollowAccepted: boolean;
  notifyFollowRejected: boolean;
  // Paramètres de visibilité
  profileVisibility: 'public' | 'followers_only' | 'private';
  postsVisibility: 'public' | 'followers_only' | 'private';
  createdAt: Date;
  updatedAt: Date;
}

export interface IUserBlock {
  id: string;
  blockerId: string;
  blockedId: string;
  reason?: string;
  blockType: 'full' | 'posts_only' | 'messages_only';
  createdAt: Date;
  // Informations enrichies
  blockedProfile?: IUser;
}

export interface IFollowerRelation {
  id: string;
  followerId: string;
  followingId: string;
  status: 'active' | 'blocked' | 'muted';
  followType: 'follow' | 'subscribe';
  createdAt: Date;
  updatedAt: Date;
  // Informations enrichies
  followerProfile?: IUser;
  followingProfile?: IUser;
}

// Subscription Interfaces
export interface ISubscription {
  id: string;
  subscriberId: string;
  targetId: string;
  targetType: 'user' | 'business';
  level: 'basic' | 'premium' | 'vip';
  createdAt: Date;
  updatedAt: Date;
  status: 'active' | 'paused' | 'cancelled' | 'expired';
  notificationsEnabled: boolean;
  autoRenewal: boolean;
  expiresAt?: Date;
}
