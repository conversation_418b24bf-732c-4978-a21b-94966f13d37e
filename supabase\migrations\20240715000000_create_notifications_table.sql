-- Migration pour la table notifications
CREATE TABLE public.notifications ( 
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), 
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE, 
  message TEXT NOT NULL, 
  type TEXT NOT NULL, 
  read BOOLEAN DEFAULT false, 
  created_at TIMESTAMPTZ DEFAULT NOW(), 
  updated_at TIMESTAMPTZ DEFAULT NOW() 
); 

-- Add RLS policies 
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY; 

-- Policy to allow users to see only their own notifications 
CREATE POLICY "Users can view their own notifications" 
ON public.notifications FOR SELECT 
USING (auth.uid() = user_id); 

-- Politique pour permettre aux utilisateurs d'insérer leurs propres notifications 
CREATE POLICY "Users can insert their own notifications" 
ON public.notifications FOR INSERT 
WITH CHECK (auth.uid() = user_id); 

-- Politique pour permettre aux utilisateurs de mettre à jour leurs propres notifications 
CREATE POLICY "Users can update their own notifications" 
ON public.notifications FOR UPDATE 
USING (auth.uid() = user_id);

-- Grant access to authenticated users 
GRANT SELECT, INSERT, UPDATE ON public.notifications TO authenticated;
