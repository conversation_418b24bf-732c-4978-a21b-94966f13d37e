-- Migration pour créer le système complet de publicités
-- Cette migration ajoute toutes les tables nécessaires pour gérer les publicités

-- =====================================================
-- 1. TABLE DES CAMPAGNES PUBLICITAIRES
-- =====================================================
CREATE TABLE IF NOT EXISTS ad_campaigns (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  
  -- Informations de base
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  image_url TEXT,
  target_url TEXT NOT NULL,
  
  -- Budget et enchères
  bid_amount DECIMAL(10,2) NOT NULL CHECK (bid_amount > 0), -- Enchère par clic en F CFA
  daily_budget DECIMAL(10,2) NOT NULL CHECK (daily_budget > 0),
  total_budget DECIMAL(10,2) NOT NULL CHECK (total_budget > 0),
  spent DECIMAL(10,2) DEFAULT 0 CHECK (spent >= 0),
  
  -- Ciblage
  placements JSONB DEFAULT '[]'::jsonb, -- ['newsfeed', 'sidebar', 'offers']
  target_demographics JSONB DEFAULT '{}'::jsonb, -- Age, genre, etc.
  target_locations JSONB DEFAULT '[]'::jsonb, -- Villes ciblées
  target_devices JSONB DEFAULT '[]'::jsonb, -- Mobile, desktop
  
  -- Statut et dates
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'completed', 'rejected')),
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ,
  
  -- Métriques
  impressions INTEGER DEFAULT 0,
  clicks INTEGER DEFAULT 0,
  ctr DECIMAL(5,4) DEFAULT 0, -- Click-through rate
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  
  -- Contraintes
  CONSTRAINT valid_budget CHECK (total_budget >= daily_budget),
  CONSTRAINT valid_dates CHECK (end_date IS NULL OR end_date > start_date)
);

-- =====================================================
-- 2. TABLE DES CRÉATIFS PUBLICITAIRES
-- =====================================================
CREATE TABLE IF NOT EXISTS ad_creatives (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id uuid NOT NULL REFERENCES ad_campaigns(id) ON DELETE CASCADE,
  
  -- Contenu créatif
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  image_url TEXT NOT NULL,
  call_to_action VARCHAR(50) DEFAULT 'En savoir plus',
  
  -- Variantes A/B testing
  variant_name VARCHAR(50),
  
  -- Métriques par créatif
  impressions INTEGER DEFAULT 0,
  clicks INTEGER DEFAULT 0,
  ctr DECIMAL(5,4) DEFAULT 0,
  
  -- Statut
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'paused', 'rejected')),
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- 3. TABLE DES EMPLACEMENTS PUBLICITAIRES
-- =====================================================
CREATE TABLE IF NOT EXISTS ad_placements (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  
  -- Configuration de l'emplacement
  max_ads_per_hour INTEGER NOT NULL DEFAULT 12,
  rotation_interval_minutes INTEGER NOT NULL DEFAULT 5,
  priority_weight DECIMAL(3,2) NOT NULL DEFAULT 1.0,
  
  -- Tarification
  base_cpc DECIMAL(10,2) NOT NULL DEFAULT 100, -- Coût par clic de base
  competition_multiplier DECIMAL(3,2) DEFAULT 1.0,
  
  -- Statut
  is_active BOOLEAN DEFAULT true,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- 4. TABLE DES IMPRESSIONS PUBLICITAIRES
-- =====================================================
CREATE TABLE IF NOT EXISTS ad_impressions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id uuid NOT NULL REFERENCES ad_campaigns(id) ON DELETE CASCADE,
  creative_id uuid NOT NULL REFERENCES ad_creatives(id) ON DELETE CASCADE,
  placement_id VARCHAR(50) NOT NULL REFERENCES ad_placements(id),
  
  -- Informations utilisateur (anonymisées)
  user_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
  user_agent TEXT,
  ip_address INET,
  
  -- Contexte d'affichage
  page_url TEXT,
  referrer TEXT,
  device_type VARCHAR(20), -- mobile, desktop, tablet
  
  -- Métriques
  viewed_at TIMESTAMPTZ DEFAULT now(),
  view_duration_seconds INTEGER, -- Temps de visibilité
  
  -- Géolocalisation (optionnel)
  country VARCHAR(2),
  city VARCHAR(100)
);

-- =====================================================
-- 5. TABLE DES CLICS PUBLICITAIRES
-- =====================================================
CREATE TABLE IF NOT EXISTS ad_clicks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  impression_id uuid NOT NULL REFERENCES ad_impressions(id) ON DELETE CASCADE,
  campaign_id uuid NOT NULL REFERENCES ad_campaigns(id) ON DELETE CASCADE,
  
  -- Coût du clic
  cost DECIMAL(10,2) NOT NULL,
  
  -- Informations du clic
  clicked_at TIMESTAMPTZ DEFAULT now(),
  target_url TEXT NOT NULL,
  
  -- Validation du clic
  is_valid BOOLEAN DEFAULT true, -- Pour détecter les clics frauduleux
  fraud_score DECIMAL(3,2) DEFAULT 0
);

-- =====================================================
-- 6. INSERTION DES EMPLACEMENTS PAR DÉFAUT
-- =====================================================
INSERT INTO ad_placements (id, name, description, max_ads_per_hour, rotation_interval_minutes, priority_weight, base_cpc) VALUES
('newsfeed', 'Fil d''actualité', 'Apparaît entre les posts des utilisateurs', 12, 5, 1.0, 250),
('sidebar', 'Barre latérale droite', 'Carrousel rotatif dans la sidebar', 6, 10, 0.7, 150),
('offers', 'Offres et promotions', 'Page des offres spéciales', 24, 2, 0.8, 200),
('marketplace', 'Marketplace', 'Page du marketplace', 20, 3, 1.2, 300),
('search', 'Résultats de recherche', 'Dans les résultats de recherche', 30, 2, 1.5, 400)
ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- 7. INDEX POUR OPTIMISER LES PERFORMANCES
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_business_id ON ad_campaigns(business_id);
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_status ON ad_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_placements ON ad_campaigns USING GIN(placements);
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_dates ON ad_campaigns(start_date, end_date);

CREATE INDEX IF NOT EXISTS idx_ad_creatives_campaign_id ON ad_creatives(campaign_id);
CREATE INDEX IF NOT EXISTS idx_ad_creatives_status ON ad_creatives(status);

CREATE INDEX IF NOT EXISTS idx_ad_impressions_campaign_id ON ad_impressions(campaign_id);
CREATE INDEX IF NOT EXISTS idx_ad_impressions_placement_id ON ad_impressions(placement_id);
CREATE INDEX IF NOT EXISTS idx_ad_impressions_viewed_at ON ad_impressions(viewed_at);
CREATE INDEX IF NOT EXISTS idx_ad_impressions_user_id ON ad_impressions(user_id);

CREATE INDEX IF NOT EXISTS idx_ad_clicks_campaign_id ON ad_clicks(campaign_id);
CREATE INDEX IF NOT EXISTS idx_ad_clicks_clicked_at ON ad_clicks(clicked_at);
CREATE INDEX IF NOT EXISTS idx_ad_clicks_impression_id ON ad_clicks(impression_id);

-- =====================================================
-- 8. POLITIQUES RLS (ROW LEVEL SECURITY)
-- =====================================================

-- Activer RLS sur toutes les tables
ALTER TABLE ad_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_creatives ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_placements ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_impressions ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_clicks ENABLE ROW LEVEL SECURITY;

-- Politiques pour ad_campaigns
CREATE POLICY "Businesses can view their own campaigns" ON ad_campaigns
  FOR SELECT USING (business_id = auth.uid());

CREATE POLICY "Businesses can insert their own campaigns" ON ad_campaigns
  FOR INSERT WITH CHECK (business_id = auth.uid());

CREATE POLICY "Businesses can update their own campaigns" ON ad_campaigns
  FOR UPDATE USING (business_id = auth.uid());

CREATE POLICY "Businesses can delete their own campaigns" ON ad_campaigns
  FOR DELETE USING (business_id = auth.uid());

-- Politiques pour ad_creatives
CREATE POLICY "Businesses can manage creatives for their campaigns" ON ad_creatives
  FOR ALL USING (
    campaign_id IN (
      SELECT id FROM ad_campaigns WHERE business_id = auth.uid()
    )
  );

-- Politiques pour ad_placements (lecture seule pour tous)
CREATE POLICY "Everyone can view ad placements" ON ad_placements
  FOR SELECT USING (true);

-- Politiques pour ad_impressions (système uniquement)
CREATE POLICY "System can manage impressions" ON ad_impressions
  FOR ALL USING (true);

-- Politiques pour ad_clicks (système uniquement)
CREATE POLICY "System can manage clicks" ON ad_clicks
  FOR ALL USING (true);

-- =====================================================
-- 9. FONCTIONS UTILITAIRES
-- =====================================================

-- Fonction pour calculer le CTR
CREATE OR REPLACE FUNCTION calculate_ctr(campaign_id_param uuid)
RETURNS DECIMAL(5,4) AS $$
DECLARE
  total_impressions INTEGER;
  total_clicks INTEGER;
  ctr_result DECIMAL(5,4);
BEGIN
  SELECT impressions, clicks INTO total_impressions, total_clicks
  FROM ad_campaigns WHERE id = campaign_id_param;

  IF total_impressions > 0 THEN
    ctr_result := (total_clicks::DECIMAL / total_impressions::DECIMAL) * 100;
  ELSE
    ctr_result := 0;
  END IF;

  RETURN ctr_result;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour mettre à jour les métriques de campagne
CREATE OR REPLACE FUNCTION update_campaign_metrics()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_TABLE_NAME = 'ad_impressions' THEN
    UPDATE ad_campaigns
    SET impressions = impressions + 1,
        ctr = calculate_ctr(NEW.campaign_id),
        updated_at = now()
    WHERE id = NEW.campaign_id;

    UPDATE ad_creatives
    SET impressions = impressions + 1,
        ctr = calculate_ctr(NEW.campaign_id),
        updated_at = now()
    WHERE id = NEW.creative_id;
  END IF;

  IF TG_TABLE_NAME = 'ad_clicks' THEN
    UPDATE ad_campaigns
    SET clicks = clicks + 1,
        spent = spent + NEW.cost,
        ctr = calculate_ctr(NEW.campaign_id),
        updated_at = now()
    WHERE id = NEW.campaign_id;

    -- Déduire du portefeuille publicitaire
    UPDATE ad_wallets
    SET balance = balance - NEW.cost,
        total_spent = total_spent + NEW.cost,
        updated_at = now()
    WHERE business_id = (
      SELECT business_id FROM ad_campaigns WHERE id = NEW.campaign_id
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers pour mettre à jour automatiquement les métriques
CREATE TRIGGER update_metrics_on_impression
  AFTER INSERT ON ad_impressions
  FOR EACH ROW EXECUTE FUNCTION update_campaign_metrics();

CREATE TRIGGER update_metrics_on_click
  AFTER INSERT ON ad_clicks
  FOR EACH ROW EXECUTE FUNCTION update_campaign_metrics();

-- =====================================================
-- 10. COMMENTAIRES POUR DOCUMENTATION
-- =====================================================
COMMENT ON TABLE ad_campaigns IS 'Campagnes publicitaires créées par les entreprises';
COMMENT ON TABLE ad_creatives IS 'Créatifs publicitaires (images, textes) pour les campagnes';
COMMENT ON TABLE ad_placements IS 'Emplacements disponibles pour afficher les publicités';
COMMENT ON TABLE ad_impressions IS 'Enregistrement de chaque affichage de publicité';
COMMENT ON TABLE ad_clicks IS 'Enregistrement de chaque clic sur une publicité';

COMMENT ON COLUMN ad_campaigns.bid_amount IS 'Montant de l''enchère par clic en F CFA';
COMMENT ON COLUMN ad_campaigns.placements IS 'Liste des emplacements ciblés (JSON array)';
COMMENT ON COLUMN ad_campaigns.target_demographics IS 'Ciblage démographique (JSON object)';
COMMENT ON COLUMN ad_campaigns.ctr IS 'Taux de clic en pourcentage (0-100)';

COMMENT ON COLUMN ad_impressions.view_duration_seconds IS 'Durée de visibilité de la publicité';
COMMENT ON COLUMN ad_clicks.fraud_score IS 'Score de détection de fraude (0-1)';

-- =====================================================
-- 11. DONNÉES DE TEST (OPTIONNEL)
-- =====================================================
-- Décommentez pour insérer des données de test

/*
-- Exemple de campagne de test
INSERT INTO ad_campaigns (
  business_id, title, description, target_url, bid_amount, daily_budget, total_budget,
  placements, status, start_date
) VALUES (
  (SELECT id FROM profiles WHERE role = 'business' LIMIT 1),
  'Promotion Été - Huile de Beauté',
  'Profitez de notre offre spéciale été avec 20% de réduction sur notre huile de beauté Olgane.',
  'https://example.com/promo-ete',
  250.00,
  5000.00,
  50000.00,
  '["newsfeed", "sidebar"]'::jsonb,
  'active',
  now()
);
*/
