import React, { useState, useEffect } from 'react';
import {
  X,
  Building2,
  Calendar,
  CreditCard,
  DollarSign,
  User,
  Mail,
  Phone,
  MapPin,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Download,
  Eye,
  Edit
} from 'lucide-react';
import Modal from '../ui/Modal';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';
import { formatAmount } from '../../utils/formatUtils';
import { AdminSubscription } from '../../services/adminPaymentService';

interface SubscriptionDetailsModalProps {
  subscription: AdminSubscription | null;
  isOpen: boolean;
  onClose: () => void;
  onSuspend: (subscriptionId: string, reason: string) => void;
  onReactivate: (subscriptionId: string) => void;
}

interface BusinessDetails {
  id: string;
  businessName: string;
  email: string;
  phone: string;
  address: string;
  registrationNumber: string;
  taxId: string;
  contactPerson: string;
  website: string;
  industry: string;
  employeeCount: string;
  registrationDate: string;
  verificationStatus: 'verified' | 'pending' | 'rejected';
}

interface PaymentHistory {
  id: string;
  date: string;
  amount: number;
  status: 'completed' | 'pending' | 'failed';
  paymentMethod: string;
  reference: string;
  description: string;
}

const SubscriptionDetailsModal: React.FC<SubscriptionDetailsModalProps> = ({
  subscription,
  isOpen,
  onClose,
  onSuspend,
  onReactivate
}) => {
  const [activeTab, setActiveTab] = useState<'details' | 'business' | 'payments' | 'history'>('details');
  const [businessDetails, setBusinessDetails] = useState<BusinessDetails | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistory[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (subscription && isOpen) {
      loadSubscriptionData();
    }
  }, [subscription, isOpen]);

  const loadSubscriptionData = async () => {
    if (!subscription) return;
    
    setLoading(true);
    try {
      // Simuler le chargement des données détaillées
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Données mockées pour la démonstration
      setBusinessDetails({
        id: subscription.businessId,
        businessName: subscription.businessName,
        email: `contact@${subscription.businessName.toLowerCase().replace(/\s+/g, '')}.cm`,
        phone: '+237 6XX XX XX XX',
        address: 'Douala, Cameroun',
        registrationNumber: `RC/DLA/2023/${Math.floor(Math.random() * 10000)}`,
        taxId: `CM${Math.floor(Math.random() * 1000000000)}`,
        contactPerson: 'Directeur Général',
        website: `www.${subscription.businessName.toLowerCase().replace(/\s+/g, '')}.cm`,
        industry: subscription.businessName.includes('Cosmétiques') ? 'Beauté & Cosmétiques' :
                  subscription.businessName.includes('Pharmacie') ? 'Santé & Pharmacie' :
                  subscription.businessName.includes('Restaurant') ? 'Restauration' :
                  subscription.businessName.includes('Boutique') ? 'Mode & Vêtements' :
                  'Automobile',
        employeeCount: '10-50',
        registrationDate: '2023-06-15',
        verificationStatus: 'verified'
      });

      // Historique des paiements mockés
      setPaymentHistory([
        {
          id: 'pay_001',
          date: subscription.lastPayment || subscription.startDate,
          amount: subscription.amount,
          status: 'completed',
          paymentMethod: subscription.paymentMethod,
          reference: `REF_${Date.now()}`,
          description: `Paiement ${subscription.planType === 'yearly' ? 'annuel' : 'mensuel'}`
        },
        ...(subscription.planType === 'monthly' ? [
          {
            id: 'pay_002',
            date: '2024-10-01',
            amount: subscription.amount,
            status: 'completed' as const,
            paymentMethod: subscription.paymentMethod,
            reference: `REF_${Date.now() - 86400000}`,
            description: 'Paiement mensuel précédent'
          }
        ] : [])
      ]);

    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'expired':
        return 'text-red-600 bg-red-100';
      case 'cancelled':
        return 'text-gray-600 bg-gray-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle size={16} />;
      case 'expired':
      case 'cancelled':
        return <XCircle size={16} />;
      case 'pending':
        return <Clock size={16} />;
      default:
        return <AlertCircle size={16} />;
    }
  };

  const getPlanTypeLabel = (planType: string) => {
    switch (planType) {
      case 'yearly':
        return 'Annuel';
      case 'monthly':
        return 'Mensuel';
      case 'trial':
        return 'Essai gratuit';
      default:
        return planType;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Actif';
      case 'expired':
        return 'Expiré';
      case 'cancelled':
        return 'Annulé';
      case 'pending':
        return 'En attente';
      default:
        return status;
    }
  };

  if (!subscription) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="p-6">
        {/* En-tête */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Building2 className="text-blue-600" size={24} />
            <div>
              <h2 className="text-xl font-bold text-gray-900">Détails de l'abonnement</h2>
              <p className="text-gray-600">{subscription.businessName}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Onglets */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'details', label: 'Abonnement', icon: <CreditCard size={16} /> },
              { id: 'business', label: 'Entreprise', icon: <Building2 size={16} /> },
              { id: 'payments', label: 'Paiements', icon: <DollarSign size={16} /> },
              { id: 'history', label: 'Historique', icon: <Clock size={16} /> }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon}
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Onglet Détails de l'abonnement */}
            {activeTab === 'details' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Informations principales */}
                <Card>
                  <CardBody className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations de l'abonnement</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">ID Abonnement:</span>
                        <span className="font-medium text-gray-900">{subscription.id}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Type de plan:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          subscription.planType === 'yearly' ? 'bg-purple-100 text-purple-800' :
                          subscription.planType === 'monthly' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {getPlanTypeLabel(subscription.planType)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Statut:</span>
                        <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(subscription.status)}`}>
                          {getStatusIcon(subscription.status)}
                          <span className="ml-1">{getStatusLabel(subscription.status)}</span>
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Montant:</span>
                        <span className="font-semibold text-gray-900">{formatAmount(subscription.amount)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Renouvellement auto:</span>
                        <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${
                          subscription.autoRenewal ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {subscription.autoRenewal ? (
                            <>
                              <CheckCircle size={12} className="mr-1" />
                              Activé
                            </>
                          ) : (
                            <>
                              <XCircle size={12} className="mr-1" />
                              Désactivé
                            </>
                          )}
                        </span>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Dates importantes */}
                <Card>
                  <CardBody className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Dates importantes</h3>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <Calendar className="text-blue-500" size={16} />
                        <div>
                          <div className="text-sm text-gray-600">Date de début</div>
                          <div className="font-medium text-gray-900">
                            {new Date(subscription.startDate).toLocaleDateString('fr-FR', {
                              day: '2-digit',
                              month: 'long',
                              year: 'numeric'
                            })}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Calendar className="text-red-500" size={16} />
                        <div>
                          <div className="text-sm text-gray-600">Date de fin</div>
                          <div className="font-medium text-gray-900">
                            {new Date(subscription.endDate).toLocaleDateString('fr-FR', {
                              day: '2-digit',
                              month: 'long',
                              year: 'numeric'
                            })}
                          </div>
                        </div>
                      </div>
                      {subscription.lastPayment && (
                        <div className="flex items-center space-x-3">
                          <CreditCard className="text-green-500" size={16} />
                          <div>
                            <div className="text-sm text-gray-600">Dernier paiement</div>
                            <div className="font-medium text-gray-900">
                              {new Date(subscription.lastPayment).toLocaleDateString('fr-FR', {
                                day: '2-digit',
                                month: 'long',
                                year: 'numeric'
                              })}
                            </div>
                          </div>
                        </div>
                      )}
                      {subscription.nextPayment && (
                        <div className="flex items-center space-x-3">
                          <Clock className="text-orange-500" size={16} />
                          <div>
                            <div className="text-sm text-gray-600">Prochain paiement</div>
                            <div className="font-medium text-gray-900">
                              {new Date(subscription.nextPayment).toLocaleDateString('fr-FR', {
                                day: '2-digit',
                                month: 'long',
                                year: 'numeric'
                              })}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}

            {/* Onglet Entreprise */}
            {activeTab === 'business' && businessDetails && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Informations générales */}
                <Card>
                  <CardBody className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations générales</h3>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <Building2 className="text-gray-400" size={16} />
                        <div>
                          <div className="text-sm text-gray-600">Nom de l'entreprise</div>
                          <div className="font-medium text-gray-900">{businessDetails.businessName}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Mail className="text-gray-400" size={16} />
                        <div>
                          <div className="text-sm text-gray-600">Email</div>
                          <div className="font-medium text-gray-900">{businessDetails.email}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Phone className="text-gray-400" size={16} />
                        <div>
                          <div className="text-sm text-gray-600">Téléphone</div>
                          <div className="font-medium text-gray-900">{businessDetails.phone}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <MapPin className="text-gray-400" size={16} />
                        <div>
                          <div className="text-sm text-gray-600">Adresse</div>
                          <div className="font-medium text-gray-900">{businessDetails.address}</div>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Informations légales */}
                <Card>
                  <CardBody className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations légales</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="text-sm text-gray-600">Numéro d'enregistrement</div>
                        <div className="font-medium text-gray-900">{businessDetails.registrationNumber}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Numéro fiscal</div>
                        <div className="font-medium text-gray-900">{businessDetails.taxId}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Secteur d'activité</div>
                        <div className="font-medium text-gray-900">{businessDetails.industry}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Nombre d'employés</div>
                        <div className="font-medium text-gray-900">{businessDetails.employeeCount}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Statut de vérification</div>
                        <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${
                          businessDetails.verificationStatus === 'verified' ? 'bg-green-100 text-green-800' :
                          businessDetails.verificationStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {businessDetails.verificationStatus === 'verified' && <CheckCircle size={12} className="mr-1" />}
                          {businessDetails.verificationStatus === 'pending' && <Clock size={12} className="mr-1" />}
                          {businessDetails.verificationStatus === 'rejected' && <XCircle size={12} className="mr-1" />}
                          {businessDetails.verificationStatus === 'verified' ? 'Vérifiée' :
                           businessDetails.verificationStatus === 'pending' ? 'En attente' : 'Rejetée'}
                        </span>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}

            {/* Onglet Paiements */}
            {activeTab === 'payments' && (
              <Card>
                <CardBody className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Méthode de paiement</h3>
                  <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <CreditCard className="text-blue-500" size={24} />
                    <div>
                      <div className="font-medium text-gray-900">{subscription.paymentMethod}</div>
                      <div className="text-sm text-gray-600">Méthode de paiement principale</div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            )}

            {/* Onglet Historique */}
            {activeTab === 'history' && (
              <Card>
                <CardBody className="p-0">
                  <div className="p-6 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">Historique des paiements</h3>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Montant
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Statut
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Méthode
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Référence
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {paymentHistory.map((payment) => (
                          <tr key={payment.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {new Date(payment.date).toLocaleDateString('fr-FR')}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {formatAmount(payment.amount)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${
                                payment.status === 'completed' ? 'bg-green-100 text-green-800' :
                                payment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                                {payment.status === 'completed' ? 'Complété' :
                                 payment.status === 'pending' ? 'En attente' : 'Échoué'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {payment.paymentMethod}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {payment.reference}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardBody>
              </Card>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-between items-center mt-6 pt-6 border-t border-gray-200">
          <div className="flex space-x-3">
            <Button
              variant="outline"
              leftIcon={<Download size={16} />}
            >
              Exporter
            </Button>
            <Button
              variant="outline"
              leftIcon={<RefreshCw size={16} />}
              onClick={loadSubscriptionData}
            >
              Actualiser
            </Button>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={onClose}>
              Fermer
            </Button>
            {subscription.status === 'active' && (
              <Button
                variant="danger"
                leftIcon={<XCircle size={16} />}
                onClick={() => {
                  // Cette fonction sera développée dans le modal de suspension
                  console.log('Suspendre abonnement:', subscription.id);
                }}
              >
                Suspendre
              </Button>
            )}
            {(subscription.status === 'expired' || subscription.status === 'cancelled') && (
              <Button
                variant="primary"
                leftIcon={<CheckCircle size={16} />}
                onClick={() => onReactivate(subscription.id)}
              >
                Réactiver
              </Button>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default SubscriptionDetailsModal;
