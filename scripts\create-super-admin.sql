-- Script pour créer un super administrateur
-- À exécuter après avoir créé un compte utilisateur normal

-- Exemple d'utilisation :
-- 1. C<PERSON>er d'abord un compte utilisateur normal via l'interface
-- 2. Remplacer '<EMAIL>' par l'email du compte créé
-- 3. Exécuter ce script dans Supabase SQL Editor

-- =====================================================
-- CRÉATION DU SUPER ADMINISTRATEUR
-- =====================================================

-- Remplacer par l'email de l'utilisateur à promouvoir
DO $$
DECLARE
    target_email text := '<EMAIL>'; -- MODIFIER CET EMAIL
    target_username text := 'admin'; -- MODIFIER CE USERNAME
    user_id uuid;
    admin_id uuid;
    admin_code text;
BEGIN
    -- Vérifier si l'utilisateur existe
    SELECT id INTO user_id
    FROM profiles
    WHERE email = target_email OR username = target_username;
    
    IF user_id IS NULL THEN
        RAISE EXCEPTION 'Utilisateur non trouvé avec email % ou username %', target_email, target_username;
    END IF;
    
    -- Vérifier si l'utilisateur n'est pas déjà admin
    IF EXISTS (SELECT 1 FROM admin_profiles WHERE user_id = user_id) THEN
        RAISE NOTICE 'L''utilisateur est déjà administrateur';
        RETURN;
    END IF;
    
    -- Générer un code admin unique
    admin_code := 'ADM' || lpad(floor(random() * 1000000)::text, 6, '0');
    
    -- Mettre à jour le rôle dans profiles
    UPDATE profiles
    SET role = 'admin'
    WHERE id = user_id;
    
    -- Créer le profil admin
    INSERT INTO admin_profiles (
        user_id,
        admin_level,
        admin_code,
        department,
        is_active,
        notes
    ) VALUES (
        user_id,
        'super_admin',
        admin_code,
        'Administration Système',
        true,
        'Super administrateur créé via script'
    ) RETURNING id INTO admin_id;
    
    -- Log de création
    INSERT INTO admin_audit_log (
        admin_id,
        action_type,
        action_description,
        target_type,
        target_id,
        new_values,
        metadata
    ) VALUES (
        admin_id,
        'CREATE_SUPER_ADMIN',
        'Création du super administrateur via script',
        'admin',
        admin_id::text,
        jsonb_build_object(
            'admin_code', admin_code,
            'user_id', user_id,
            'admin_level', 'super_admin'
        ),
        jsonb_build_object(
            'script_created', true,
            'created_at', now()
        )
    );
    
    RAISE NOTICE 'Super administrateur créé avec succès !';
    RAISE NOTICE 'Code admin: %', admin_code;
    RAISE NOTICE 'User ID: %', user_id;
    RAISE NOTICE 'Admin ID: %', admin_id;
    
END $$;

-- =====================================================
-- VÉRIFICATION DU SUPER ADMINISTRATEUR
-- =====================================================

-- Afficher les informations du super admin créé
SELECT 
    p.username,
    p.email,
    p.role,
    ap.admin_code,
    ap.admin_level,
    ap.department,
    ap.is_active,
    ap.created_at
FROM profiles p
JOIN admin_profiles ap ON p.id = ap.user_id
WHERE ap.admin_level = 'super_admin'
ORDER BY ap.created_at DESC;

-- =====================================================
-- CRÉATION D'AUTRES ADMINISTRATEURS (OPTIONNEL)
-- =====================================================

-- Exemple pour créer d'autres types d'administrateurs
-- Décommenter et modifier selon les besoins

/*
-- Créer un administrateur plateforme
INSERT INTO admin_profiles (
    user_id,
    admin_level,
    admin_code,
    department,
    is_active,
    notes
) VALUES (
    (SELECT id FROM profiles WHERE email = '<EMAIL>'),
    'platform_admin',
    'ADM' || lpad(floor(random() * 1000000)::text, 6, '0'),
    'Administration Plateforme',
    true,
    'Administrateur plateforme'
);

-- Créer un modérateur de contenu
INSERT INTO admin_profiles (
    user_id,
    admin_level,
    admin_code,
    department,
    is_active,
    notes
) VALUES (
    (SELECT id FROM profiles WHERE email = '<EMAIL>'),
    'content_moderator',
    'ADM' || lpad(floor(random() * 1000000)::text, 6, '0'),
    'Modération',
    true,
    'Modérateur de contenu'
);

-- Créer un administrateur support
INSERT INTO admin_profiles (
    user_id,
    admin_level,
    admin_code,
    department,
    is_active,
    notes
) VALUES (
    (SELECT id FROM profiles WHERE email = '<EMAIL>'),
    'support_admin',
    'ADM' || lpad(floor(random() * 1000000)::text, 6, '0'),
    'Support Client',
    true,
    'Administrateur support'
);
*/

-- =====================================================
-- CRÉATION D'ALERTES DE TEST (OPTIONNEL)
-- =====================================================

-- Créer quelques alertes de test pour le tableau de bord
INSERT INTO admin_alerts (
    alert_type,
    title,
    message,
    priority,
    status,
    details
) VALUES 
(
    'system',
    'Système initialisé',
    'Le système d''administration a été initialisé avec succès.',
    'low',
    'resolved',
    '{"component": "admin_system", "version": "1.0.0"}'::jsonb
),
(
    'security',
    'Nouveau super administrateur',
    'Un nouveau super administrateur a été créé.',
    'medium',
    'open',
    '{"action": "super_admin_created", "requires_review": true}'::jsonb
),
(
    'maintenance',
    'Maintenance programmée',
    'Une maintenance système est programmée pour ce week-end.',
    'low',
    'open',
    '{"scheduled_date": "2024-01-15", "duration": "2h", "impact": "minimal"}'::jsonb
);

-- =====================================================
-- AFFICHAGE FINAL
-- =====================================================

-- Afficher un résumé de l'installation
SELECT 
    'RÉSUMÉ DE L''INSTALLATION' as section,
    '' as details
UNION ALL
SELECT 
    'Super Admins créés:',
    count(*)::text
FROM admin_profiles 
WHERE admin_level = 'super_admin'
UNION ALL
SELECT 
    'Total Admins:',
    count(*)::text
FROM admin_profiles
UNION ALL
SELECT 
    'Alertes créées:',
    count(*)::text
FROM admin_alerts
UNION ALL
SELECT 
    'Permissions disponibles:',
    count(*)::text
FROM admin_permissions;

RAISE NOTICE '=== INSTALLATION TERMINÉE ===';
RAISE NOTICE 'Le système d''administration est maintenant opérationnel.';
RAISE NOTICE 'Accédez à /admin pour utiliser le tableau de bord.';
RAISE NOTICE 'Consultez les logs d''audit pour suivre les activités.';
