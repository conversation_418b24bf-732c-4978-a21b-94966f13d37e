import { supabase } from '../lib/supabase';

/**
 * Teste la configuration du stockage Supabase
 */
export class StorageTest {
  /**
   * Vérifie si les buckets existent et sont accessibles
   */
  static async checkBuckets(): Promise<{ profilePictures: boolean; coverPhotos: boolean }> {
    try {
      // Vérifier le bucket profile-pictures
      const { data: profileBuckets, error: profileError } = await supabase.storage.listBuckets();
      
      if (profileError) {
        console.error('Erreur lors de la vérification des buckets:', profileError);
        return { profilePictures: false, coverPhotos: false };
      }

      const profilePicturesBucket = profileBuckets?.find(bucket => bucket.name === 'profile-pictures');
      const coverPhotosBucket = profileBuckets?.find(bucket => bucket.name === 'cover-photos');

      console.log('Buckets disponibles:', profileBuckets?.map(b => b.name));
      console.log('Bucket profile-pictures trouvé:', !!profilePicturesBucket);
      console.log('Bucket cover-photos trouvé:', !!coverPhotosBucket);

      return {
        profilePictures: !!profilePicturesBucket,
        coverPhotos: !!coverPhotosBucket
      };
    } catch (error) {
      console.error('Erreur dans checkBuckets:', error);
      return { profilePictures: false, coverPhotos: false };
    }
  }

  /**
   * Teste l'upload d'un fichier de test
   */
  static async testUpload(userId: string): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      // Créer un fichier de test (image 1x1 pixel)
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = '#FF0000';
        ctx.fillRect(0, 0, 1, 1);
      }

      // Convertir en blob
      return new Promise((resolve) => {
        canvas.toBlob(async (blob) => {
          if (!blob) {
            resolve({ success: false, error: 'Impossible de créer le fichier de test' });
            return;
          }

          const testFile = new File([blob], 'test.png', { type: 'image/png' });
          const fileName = `${userId}/test-${Date.now()}.png`;

          // Tenter l'upload
          const { data, error } = await supabase.storage
            .from('profile-pictures')
            .upload(fileName, testFile, {
              upsert: true,
              contentType: 'image/png'
            });

          if (error) {
            resolve({ success: false, error: error.message });
            return;
          }

          // Récupérer l'URL publique
          const { data: urlData } = supabase.storage
            .from('profile-pictures')
            .getPublicUrl(fileName);

          // Nettoyer le fichier de test
          await supabase.storage
            .from('profile-pictures')
            .remove([fileName]);

          resolve({ success: true, url: urlData.publicUrl });
        }, 'image/png');
      });
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * Affiche un rapport complet de l'état du stockage
   */
  static async generateReport(userId: string): Promise<void> {
    console.log('=== RAPPORT DE TEST DU STOCKAGE SUPABASE ===');
    
    // Test des buckets
    const buckets = await this.checkBuckets();
    console.log('Buckets disponibles:', buckets);

    // Test d'upload
    if (buckets.profilePictures) {
      console.log('Test d\'upload en cours...');
      const uploadTest = await this.testUpload(userId);
      console.log('Résultat du test d\'upload:', uploadTest);
    } else {
      console.log('❌ Impossible de tester l\'upload - bucket profile-pictures non trouvé');
    }

    console.log('=== FIN DU RAPPORT ===');
  }
}
