import { useEffect } from 'react';
import { IPost } from '../types/index';

/**
 * Hook de debug pour surveiller les changements de types de posts
 */
export const usePostTypeDebug = (posts: IPost[]) => {
  useEffect(() => {
    if (posts.length > 0) {
      console.log('=== POST TYPE DEBUG ===');
      posts.forEach((post, index) => {
        console.log(`Post ${index + 1}:`, {
          id: post.id,
          type: post.type,
          businessName: post.businessName,
          productName: post.productName
        });
      });
      console.log('======================');
    }
  }, [posts]);

  // Retourner des statistiques sur les types de posts
  const typeStats = posts.reduce((acc, post) => {
    acc[post.type] = (acc[post.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return { typeStats };
};
