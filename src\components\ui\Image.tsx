import React, { useState, useEffect } from 'react';
import { getImageWithFallback, fixAvatarImagePath } from '../../utils/imageUtils';

interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  fallbackType?: 'avatar' | 'product' | 'post';
}

const Image: React.FC<ImageProps> = ({ src, alt, fallbackType = 'avatar', ...props }) => {
  // Prétraiter l'URL source si c'est un avatar
  const processedSrc = fallbackType === 'avatar' && src ? fixAvatarImagePath(src) : src;
  const [imgSrc, setImgSrc] = useState(getImageWithFallback(processedSrc, fallbackType));
  
  // Mettre à jour l'image source si la prop src change
  useEffect(() => {
    const newSrc = fallbackType === 'avatar' && src ? fixAvatarImagePath(src) : src;
    setImgSrc(getImageWithFallback(newSrc, fallbackType));
  }, [src, fallbackType]);
  
  const handleError = () => {
    console.log(`Image error loading: ${imgSrc}`);
    setImgSrc(getImageWithFallback(null, fallbackType));
  };
  
  return <img src={imgSrc} alt={alt} onError={handleError} {...props} />;
};

export default Image;