/* CSS Charts - Graphiques CSS Purs Ultra-Avancés */
.css-chart-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  height: 350px;
  display: flex;
  flex-direction: column;
}

.css-chart-container:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.chart-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.8rem;
  color: #6b7280;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-color.revenue {
  background: #3b82f6;
}

.legend-color.count {
  background: #10b981;
}

.legend-color.current {
  background: #3b82f6;
}

.legend-color.previous {
  background: #9ca3af;
}

/* Line Chart Styles */
.line-chart {
  flex: 1;
  position: relative;
  margin-top: 10px;
}

.chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 30px;
  pointer-events: none;
}

.grid-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background: #f3f4f6;
  border-top: 1px dashed #d1d5db;
}

.chart-data {
  position: relative;
  height: calc(100% - 30px);
  display: flex;
  align-items: end;
}

.data-point {
  position: absolute;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: translateX(-50%);
}

.revenue-bar {
  width: 8px;
  background: linear-gradient(to top, #3b82f6, #60a5fa);
  border-radius: 4px 4px 0 0;
  margin-bottom: 4px;
  transition: all 0.3s ease;
  animation: barGrow 1s ease-out;
}

.revenue-bar:hover {
  background: linear-gradient(to top, #2563eb, #3b82f6);
  transform: scaleY(1.05);
}

.count-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #10b981;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  animation: pointAppear 1s ease-out 0.5s both;
}

.count-point:hover {
  background: #059669;
  transform: scale(1.3);
}

.date-label {
  position: absolute;
  bottom: -25px;
  font-size: 0.7rem;
  color: #6b7280;
  white-space: nowrap;
}

@keyframes barGrow {
  from {
    height: 0;
  }
  to {
    height: var(--final-height);
  }
}

@keyframes pointAppear {
  from {
    opacity: 0;
    transform: scale(0);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pie Chart Styles */
.pie-chart-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20px;
}

.pie-chart {
  flex-shrink: 0;
  width: 200px;
  height: 200px;
}

.pie-svg {
  width: 100%;
  height: 100%;
  animation: pieRotate 1s ease-out;
}

.pie-segment {
  transition: all 0.3s ease;
  cursor: pointer;
}

.pie-segment:hover {
  filter: brightness(1.1);
  transform-origin: center;
  animation: segmentPulse 0.3s ease;
}

@keyframes pieRotate {
  from {
    transform: rotate(-90deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes segmentPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.pie-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.pie-legend .legend-item {
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.pie-legend .legend-item:hover {
  background: #e2e8f0;
  transform: translateX(4px);
}

.legend-text {
  font-weight: 500;
  color: #374151;
}

/* Bar Chart Styles */
.bar-chart {
  flex: 1;
  position: relative;
  margin-top: 10px;
}

.bars-container {
  height: calc(100% - 40px);
  display: flex;
  align-items: end;
  gap: 20px;
  padding: 0 10px;
}

.bar-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.bars {
  display: flex;
  gap: 4px;
  align-items: end;
  height: 100%;
  margin-bottom: 8px;
}

.bar {
  width: 20px;
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
  cursor: pointer;
  animation: barSlideUp 0.8s ease-out;
}

.bar.current {
  background: linear-gradient(to top, #3b82f6, #60a5fa);
}

.bar.previous {
  background: linear-gradient(to top, #9ca3af, #d1d5db);
}

.bar:hover {
  transform: scaleY(1.05) scaleX(1.1);
  filter: brightness(1.1);
}

.bar-label {
  text-align: center;
  font-size: 0.75rem;
}

.month-name {
  display: block;
  color: #374151;
  font-weight: 500;
  margin-bottom: 2px;
}

.change-indicator {
  font-size: 0.7rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.change-indicator.positive {
  color: #059669;
  background: #d1fae5;
}

.change-indicator.negative {
  color: #dc2626;
  background: #fee2e2;
}

@keyframes barSlideUp {
  from {
    height: 0;
    opacity: 0;
  }
  to {
    height: var(--final-height);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .css-chart-container {
    height: 300px;
    padding: 16px;
  }
  
  .chart-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .chart-legend {
    gap: 12px;
  }
  
  .pie-chart-container {
    flex-direction: column;
    gap: 16px;
  }
  
  .pie-chart {
    width: 150px;
    height: 150px;
  }
  
  .bars-container {
    gap: 12px;
  }
  
  .bar {
    width: 16px;
  }
}

@media (max-width: 480px) {
  .css-chart-container {
    height: 250px;
    padding: 12px;
  }
  
  .pie-chart {
    width: 120px;
    height: 120px;
  }
  
  .bars-container {
    gap: 8px;
  }
  
  .bar {
    width: 12px;
  }
  
  .chart-header h3 {
    font-size: 0.9rem;
  }
}

/* Loading Animation */
.css-chart-container.loading {
  position: relative;
}

.css-chart-container.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  margin: -20px 0 0 -20px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Accessibility */
.css-chart-container:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .css-chart-container,
  .revenue-bar,
  .count-point,
  .pie-svg,
  .pie-segment,
  .bar {
    animation: none;
    transition: none;
  }
  
  .css-chart-container:hover {
    transform: none;
  }
  
  .bar:hover,
  .revenue-bar:hover {
    transform: none;
  }
}
