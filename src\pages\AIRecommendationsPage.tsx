import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { UserRole } from '../types';
import { useNavigate } from 'react-router-dom';
import {
  Brain,
  Sparkles,
  TrendingUp,
  Heart,
  Star,
  MessageCircle,
  Send,
  Zap,
  Target,
  Award,
  ShoppingCart,
  Bot,
  Lightbulb,
  RefreshCw
} from 'lucide-react';
import {
  IAIRecommendation,
  IAIAssistant,
  AIRecommendationService
} from '../services/aiRecommendationService';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import Button from '../components/ui/Button';
import ProductCard from '../components/marketplace/ProductCard';
import BusinessAIRestrictionMessage from '../components/ai/BusinessAIRestrictionMessage';
import UserBehaviorInsights from '../components/ai/UserBehaviorInsights';
import AICapabilitiesHelp from '../components/ai/AICapabilitiesHelp';

const AIRecommendationsPage: React.FC = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  // Afficher un message informatif pour les entreprises
  if (currentUser && currentUser.role === UserRole.BUSINESS) {
    return (
      <BusinessAIRestrictionMessage
        onNavigateToProfile={() => navigate('/profile')}
        onNavigateToAnalytics={() => navigate('/business-sales')}
      />
    );
  }

  const [recommendations, setRecommendations] = useState<IAIRecommendation[]>([]);
  const [trendingRecommendations, setTrendingRecommendations] = useState<IAIRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'personalized' | 'trending' | 'assistant' | 'profile'>('personalized');

  // États pour l'assistant IA
  const [question, setQuestion] = useState('');
  const [conversation, setConversation] = useState<IAIAssistant[]>([]);
  const [askingAI, setAskingAI] = useState(false);

  // États pour les filtres
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [minScore, setMinScore] = useState(60);

  useEffect(() => {
    loadRecommendations();
  }, [currentUser]);

  const loadRecommendations = async () => {
    setLoading(true);
    try {
      const userId = currentUser?.id || 'guest-user';

      // Charger les recommandations personnalisées
      const personalizedRecs = await AIRecommendationService.getPersonalizedRecommendations(userId, 12);
      setRecommendations(personalizedRecs);

      // Charger les tendances
      const trendingRecs = AIRecommendationService.getTrendingRecommendations();
      setTrendingRecommendations(trendingRecs);

      // Ajouter un message de bienvenue de l'assistant
      if (conversation.length === 0) {
        const welcomeMessage: IAIAssistant = {
          question: '',
          answer: `Bonjour ! Je suis votre assistant IA personnel. J'ai analysé vos préférences et je peux vous aider à trouver les meilleurs produits. Que puis-je faire pour vous aujourd'hui ?`,
          relatedProducts: personalizedRecs.slice(0, 3).map(r => r.product),
          confidence: 95,
          timestamp: new Date()
        };
        setConversation([welcomeMessage]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des recommandations:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAskAI = async () => {
    if (!question.trim() || askingAI) return;

    setAskingAI(true);
    try {
      const userId = currentUser?.id || 'guest-user';
      const response = await AIRecommendationService.askAIAssistant(question, userId);

      setConversation(prev => [...prev, response]);
      setQuestion('');
    } catch (error) {
      console.error('Erreur lors de la question à l\'IA:', error);
    } finally {
      setAskingAI(false);
    }
  };

  const handleProductAction = (productId: string, action: 'buy' | 'negotiate') => {
    // Logique pour acheter ou négocier
    console.log(`Action ${action} sur le produit ${productId}`);
  };

  const getCategoryIcon = (category: IAIRecommendation['category']) => {
    switch (category) {
      case 'trending': return <TrendingUp size={16} className="text-orange-500" />;
      case 'quality': return <Award size={16} className="text-purple-500" />;
      case 'budget': return <Target size={16} className="text-green-500" />;
      case 'similar': return <Heart size={16} className="text-red-500" />;
      default: return <Sparkles size={16} className="text-blue-500" />;
    }
  };

  const getCategoryLabel = (category: IAIRecommendation['category']) => {
    switch (category) {
      case 'trending': return 'Tendance';
      case 'quality': return 'Qualité';
      case 'budget': return 'Budget';
      case 'similar': return 'Similaire';
      default: return 'Personnalisé';
    }
  };

  const filteredRecommendations = recommendations.filter(rec => {
    const categoryMatch = selectedCategory === 'all' || rec.category === selectedCategory;
    const scoreMatch = rec.score >= minScore;
    return categoryMatch && scoreMatch;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">L'IA analyse vos préférences...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Brain className="text-blue-600 mr-3" size={32} />
              Recommandations IA
              <Sparkles className="text-yellow-500 ml-2" size={24} />
            </h1>
            <p className="text-lg text-gray-600 mt-2">
              Découvrez des produits personnalisés grâce à notre intelligence artificielle
            </p>
          </div>
          <Button
            variant="outline"
            leftIcon={<RefreshCw size={18} />}
            onClick={loadRecommendations}
            className="hover:bg-blue-50"
          >
            Actualiser
          </Button>
        </div>

        {/* Onglets */}
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('personalized')}
            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors flex items-center justify-center ${
              activeTab === 'personalized'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Target size={18} className="mr-2" />
            Pour Vous ({recommendations.length})
          </button>
          <button
            onClick={() => setActiveTab('trending')}
            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors flex items-center justify-center ${
              activeTab === 'trending'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <TrendingUp size={18} className="mr-2" />
            Tendances ({trendingRecommendations.length})
          </button>
          <button
            onClick={() => setActiveTab('assistant')}
            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors flex items-center justify-center ${
              activeTab === 'assistant'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Bot size={18} className="mr-2" />
            Assistant IA
          </button>
          <button
            onClick={() => setActiveTab('profile')}
            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors flex items-center justify-center ${
              activeTab === 'profile'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Brain size={18} className="mr-2" />
            Mon Profil IA
          </button>
        </div>
      </div>

      {/* Contenu des onglets */}
      {activeTab === 'personalized' && (
        <div>
          {/* Filtres */}
          <Card className="mb-6">
            <CardBody>
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div className="flex items-center space-x-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Catégorie
                    </label>
                    <select
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">Toutes</option>
                      <option value="personalized">Personnalisé</option>
                      <option value="quality">Qualité</option>
                      <option value="budget">Budget</option>
                      <option value="similar">Similaire</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Score minimum: {minScore}%
                    </label>
                    <input
                      type="range"
                      min="40"
                      max="100"
                      value={minScore}
                      onChange={(e) => setMinScore(Number(e.target.value))}
                      className="w-32"
                    />
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  {filteredRecommendations.length} recommandations trouvées
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Grille des recommandations */}
          {filteredRecommendations.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredRecommendations.map(recommendation => (
                <Card key={recommendation.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative">
                    <img
                      src={recommendation.product.images[0]}
                      alt={recommendation.product.name}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-2 left-2 flex items-center space-x-2">
                      <div className="flex items-center bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-semibold">
                        {getCategoryIcon(recommendation.category)}
                        <span className="ml-1">{getCategoryLabel(recommendation.category)}</span>
                      </div>
                      <div className="bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                        {Math.round(recommendation.score)}%
                      </div>
                    </div>
                  </div>

                  <CardBody className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-2">{recommendation.product.name}</h3>

                    {/* IA Insight */}
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-3 rounded-lg mb-3">
                      <div className="flex items-start space-x-2">
                        <Lightbulb size={16} className="text-blue-500 mt-0.5 flex-shrink-0" />
                        <p className="text-sm text-gray-700 italic">{recommendation.aiInsight}</p>
                      </div>
                    </div>

                    {/* Raisons */}
                    <div className="mb-3">
                      <h4 className="text-xs font-semibold text-gray-600 mb-2">POURQUOI CETTE RECOMMANDATION :</h4>
                      <ul className="space-y-1">
                        {recommendation.reasons.slice(0, 3).map((reason, index) => (
                          <li key={index} className="text-xs text-gray-600 flex items-start">
                            <span className="text-green-500 mr-1">•</span>
                            {reason}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Prix et note */}
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-lg font-bold text-blue-600">
                        {new Intl.NumberFormat('fr-FR').format(recommendation.product.price)} F CFA
                      </span>
                      <div className="flex items-center">
                        <Star size={14} className="text-yellow-400 fill-current mr-1" />
                        <span className="text-sm font-semibold">{recommendation.product.averageRating}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      <Button
                        variant="primary"
                        size="sm"
                        fullWidth
                        leftIcon={<ShoppingCart size={14} />}
                        onClick={() => handleProductAction(recommendation.product.id, 'buy')}
                        className="bg-gradient-to-r from-blue-600 to-blue-700"
                      >
                        Acheter
                      </Button>
                      {recommendation.product.negotiable && (
                        <Button
                          variant="outline"
                          size="sm"
                          leftIcon={<MessageCircle size={14} />}
                          onClick={() => handleProductAction(recommendation.product.id, 'negotiate')}
                        >
                          Négocier
                        </Button>
                      )}
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardBody className="text-center py-12">
                <Brain className="mx-auto text-gray-400 mb-4" size={48} />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucune recommandation trouvée
                </h3>
                <p className="text-gray-600 mb-4">
                  Ajustez vos filtres ou explorez d'autres catégories
                </p>
                <Button
                  variant="primary"
                  onClick={() => {
                    setSelectedCategory('all');
                    setMinScore(40);
                  }}
                >
                  Réinitialiser les filtres
                </Button>
              </CardBody>
            </Card>
          )}
        </div>
      )}

      {activeTab === 'trending' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {trendingRecommendations.map(recommendation => (
            <ProductCard
              key={recommendation.id}
              product={recommendation.product}
              onBuyClick={() => handleProductAction(recommendation.product.id, 'buy')}
              onNegotiateClick={() => handleProductAction(recommendation.product.id, 'negotiate')}
            />
          ))}
        </div>
      )}

      {activeTab === 'assistant' && (
        <div className="max-w-4xl mx-auto">
          <AICapabilitiesHelp />
          <Card>
            <CardHeader>
              <div className="flex items-center">
                <Bot className="text-blue-600 mr-3" size={24} />
                <h2 className="text-xl font-bold">Assistant IA Personnel</h2>
              </div>
            </CardHeader>
            <CardBody>
              {/* Conversation */}
              <div className="space-y-4 mb-6 max-h-96 overflow-y-auto">
                {conversation.map((message, index) => (
                  <div key={index} className="space-y-3">
                    {message.question && (
                      <div className="flex justify-end">
                        <div className="bg-blue-500 text-white p-3 rounded-lg max-w-xs">
                          {message.question}
                        </div>
                      </div>
                    )}
                    <div className="flex items-start space-x-3">
                      <div className="bg-blue-100 p-2 rounded-full">
                        <Bot size={20} className="text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="bg-gray-100 p-3 rounded-lg">
                          <p className="text-gray-800">{message.answer}</p>
                          <div className="flex items-center mt-2 text-xs text-gray-500">
                            <Zap size={12} className="mr-1" />
                            Confiance: {message.confidence}%
                          </div>
                        </div>
                        {message.relatedProducts && message.relatedProducts.length > 0 && (
                          <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-3">
                            {message.relatedProducts.map(product => (
                              <div key={product.id} className="border rounded-lg p-3 hover:shadow-md transition-shadow">
                                <img
                                  src={product.images[0]}
                                  alt={product.name}
                                  className="w-full h-24 object-cover rounded mb-2"
                                />
                                <h4 className="font-medium text-sm">{product.name}</h4>
                                <p className="text-blue-600 font-bold text-sm">
                                  {new Intl.NumberFormat('fr-FR').format(product.price)} F CFA
                                </p>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Input pour poser une question */}
              <div className="flex space-x-3">
                <input
                  type="text"
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleAskAI()}
                  placeholder="Posez votre question à l'IA..."
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={askingAI}
                />
                <Button
                  variant="primary"
                  onClick={handleAskAI}
                  disabled={!question.trim() || askingAI}
                  leftIcon={askingAI ? <RefreshCw size={18} className="animate-spin" /> : <Send size={18} />}
                >
                  {askingAI ? 'Réflexion...' : 'Envoyer'}
                </Button>
              </div>

              {/* Suggestions de questions */}
              <div className="mt-4">
                <p className="text-sm text-gray-600 mb-2">Suggestions :</p>
                <div className="flex flex-wrap gap-2">
                  {[
                    'Que penses-tu du produit Olgane ?',
                    'Recommande-moi une boisson',
                    'Quel est le meilleur smartphone ?',
                    'Quels produits de beauté recommandes-tu ?',
                    'Aide-moi à choisir dans mon budget',
                    'Quels sont les produits tendance ?'
                  ].map(suggestion => (
                    <button
                      key={suggestion}
                      onClick={() => setQuestion(suggestion)}
                      className="text-xs bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full transition-colors"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {activeTab === 'profile' && (
        <div>
          <UserBehaviorInsights />
        </div>
      )}
    </div>
  );
};

export default AIRecommendationsPage;