import React, { useState } from 'react';
import { Download, FileText, Table, Calendar, Settings, X, Check } from 'lucide-react';
import { formatAmount, formatDate } from '../../utils/formatUtils';

interface ExportData {
  campaigns: any[];
  analytics: any;
  chartData: any;
  timeRange: string;
  generatedAt: string;
}

interface AdvancedExportProps {
  isOpen: boolean;
  onClose: () => void;
  data: ExportData;
  onExport: (format: 'pdf' | 'excel', options: ExportOptions) => void;
}

interface ExportOptions {
  format: 'pdf' | 'excel';
  includeCharts: boolean;
  includeAnalytics: boolean;
  includeCampaigns: boolean;
  includeRecommendations: boolean;
  dateRange: string;
  customTitle: string;
  logoUrl?: string;
}

const AdvancedExport: React.FC<AdvancedExportProps> = ({
  isOpen,
  onClose,
  data,
  onExport
}) => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    includeCharts: true,
    includeAnalytics: true,
    includeCampaigns: true,
    includeRecommendations: true,
    dateRange: data.timeRange,
    customTitle: `Rapport Publicitaire - ${formatDate(new Date(), 'medium')}`,
    logoUrl: ''
  });

  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  const handleExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    // Simulation du processus d'export avec progression
    const steps = [
      { name: 'Préparation des données', duration: 500 },
      { name: 'Génération des graphiques', duration: 800 },
      { name: 'Compilation du rapport', duration: 600 },
      { name: 'Finalisation', duration: 300 }
    ];

    let currentProgress = 0;
    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, step.duration));
      currentProgress += 25;
      setExportProgress(currentProgress);
    }

    // Générer le contenu du rapport
    const reportContent = generateReportContent();
    
    if (exportOptions.format === 'pdf') {
      generatePDFReport(reportContent);
    } else {
      generateExcelReport(reportContent);
    }

    onExport(exportOptions.format, exportOptions);
    setIsExporting(false);
    setExportProgress(0);
    onClose();
  };

  const generateReportContent = () => {
    const content = {
      title: exportOptions.customTitle,
      generatedAt: new Date().toISOString(),
      timeRange: exportOptions.dateRange,
      summary: {
        totalCampaigns: data.campaigns.length,
        activeCampaigns: data.campaigns.filter(c => c.status === 'active').length,
        totalBudget: data.campaigns.reduce((sum, c) => sum + c.budget, 0),
        totalSpent: data.campaigns.reduce((sum, c) => sum + c.spent, 0),
        roas: data.analytics.roas,
        ctr: data.campaigns.reduce((sum, c) => sum + c.ctr, 0) / data.campaigns.length
      },
      campaigns: exportOptions.includeCampaigns ? data.campaigns : [],
      analytics: exportOptions.includeAnalytics ? data.analytics : null,
      charts: exportOptions.includeCharts ? data.chartData : null,
      recommendations: exportOptions.includeRecommendations ? [
        'Augmenter le budget pour l\'audience 25-34 ans (+40% CTR)',
        'Optimiser la planification weekend (+30% conversions)',
        'Tester des créations avec modèles locaux (+20% engagement)'
      ] : []
    };

    return content;
  };

  const generatePDFReport = (content: any) => {
    // Simulation de génération PDF
    const pdfContent = `
# ${content.title}

**Généré le :** ${formatDate(new Date(), 'long')}
**Période :** ${content.timeRange}

## Résumé Exécutif

- **Campagnes totales :** ${content.summary.totalCampaigns}
- **Campagnes actives :** ${content.summary.activeCampaigns}
- **Budget total :** ${formatAmount(content.summary.totalBudget)}
- **Dépenses :** ${formatAmount(content.summary.totalSpent)}
- **ROAS :** ${content.summary.roas}x
- **CTR moyen :** ${content.summary.ctr.toFixed(2)}%

## Performances des Campagnes

${content.campaigns.map((campaign: any) => `
### ${campaign.title}
- **Statut :** ${campaign.status}
- **Budget :** ${formatAmount(campaign.budget)}
- **Dépensé :** ${formatAmount(campaign.spent)}
- **Impressions :** ${campaign.impressions.toLocaleString()}
- **Clics :** ${campaign.clicks.toLocaleString()}
- **CTR :** ${campaign.ctr.toFixed(2)}%
`).join('\n')}

## Recommandations IA

${content.recommendations.map((rec: string, index: number) => `${index + 1}. ${rec}`).join('\n')}

---
Rapport généré automatiquement par l'Assistant IA Publicitaire
    `;

    // Créer et télécharger le fichier
    const blob = new Blob([pdfContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rapport-publicites-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const generateExcelReport = (content: any) => {
    // Simulation de génération Excel (CSV)
    const csvContent = [
      ['Rapport Publicitaire', content.title],
      ['Généré le', formatDate(new Date(), 'long')],
      ['Période', content.timeRange],
      [''],
      ['Résumé'],
      ['Métrique', 'Valeur'],
      ['Campagnes totales', content.summary.totalCampaigns],
      ['Campagnes actives', content.summary.activeCampaigns],
      ['Budget total', content.summary.totalBudget],
      ['Dépenses', content.summary.totalSpent],
      ['ROAS', content.summary.roas],
      ['CTR moyen', content.summary.ctr.toFixed(2) + '%'],
      [''],
      ['Détail des Campagnes'],
      ['Nom', 'Statut', 'Budget', 'Dépensé', 'Impressions', 'Clics', 'CTR'],
      ...content.campaigns.map((campaign: any) => [
        campaign.title,
        campaign.status,
        campaign.budget,
        campaign.spent,
        campaign.impressions,
        campaign.clicks,
        campaign.ctr.toFixed(2) + '%'
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rapport-publicites-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (!isOpen) return null;

  return (
    <div className="advanced-export-modal">
      <div className="export-modal-content">
        <div className="export-modal-header">
          <div className="export-title">
            <Download size={24} />
            <div>
              <h2>Export Avancé</h2>
              <p>Personnalisez votre rapport publicitaire</p>
            </div>
          </div>
          <button className="close-btn" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="export-modal-body">
          {isExporting ? (
            <div className="export-progress">
              <div className="progress-header">
                <h3>Génération du rapport en cours...</h3>
                <span>{exportProgress}%</span>
              </div>
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${exportProgress}%` }}
                ></div>
              </div>
              <div className="progress-steps">
                <div className={`step ${exportProgress >= 25 ? 'completed' : 'active'}`}>
                  <Check size={16} />
                  Préparation des données
                </div>
                <div className={`step ${exportProgress >= 50 ? 'completed' : exportProgress >= 25 ? 'active' : ''}`}>
                  <Check size={16} />
                  Génération des graphiques
                </div>
                <div className={`step ${exportProgress >= 75 ? 'completed' : exportProgress >= 50 ? 'active' : ''}`}>
                  <Check size={16} />
                  Compilation du rapport
                </div>
                <div className={`step ${exportProgress >= 100 ? 'completed' : exportProgress >= 75 ? 'active' : ''}`}>
                  <Check size={16} />
                  Finalisation
                </div>
              </div>
            </div>
          ) : (
            <div className="export-options">
              <div className="option-section">
                <h3>Format d'export</h3>
                <div className="format-selector">
                  <button 
                    className={`format-btn ${exportOptions.format === 'pdf' ? 'active' : ''}`}
                    onClick={() => setExportOptions({...exportOptions, format: 'pdf'})}
                  >
                    <FileText size={20} />
                    <div>
                      <span>PDF</span>
                      <small>Rapport formaté avec graphiques</small>
                    </div>
                  </button>
                  <button 
                    className={`format-btn ${exportOptions.format === 'excel' ? 'active' : ''}`}
                    onClick={() => setExportOptions({...exportOptions, format: 'excel'})}
                  >
                    <Table size={20} />
                    <div>
                      <span>Excel/CSV</span>
                      <small>Données tabulaires pour analyse</small>
                    </div>
                  </button>
                </div>
              </div>

              <div className="option-section">
                <h3>Contenu du rapport</h3>
                <div className="content-options">
                  <label className="option-checkbox">
                    <input 
                      type="checkbox" 
                      checked={exportOptions.includeAnalytics}
                      onChange={(e) => setExportOptions({...exportOptions, includeAnalytics: e.target.checked})}
                    />
                    <span>Analytics et métriques</span>
                  </label>
                  <label className="option-checkbox">
                    <input 
                      type="checkbox" 
                      checked={exportOptions.includeCampaigns}
                      onChange={(e) => setExportOptions({...exportOptions, includeCampaigns: e.target.checked})}
                    />
                    <span>Détails des campagnes</span>
                  </label>
                  <label className="option-checkbox">
                    <input 
                      type="checkbox" 
                      checked={exportOptions.includeCharts}
                      onChange={(e) => setExportOptions({...exportOptions, includeCharts: e.target.checked})}
                    />
                    <span>Graphiques et visualisations</span>
                  </label>
                  <label className="option-checkbox">
                    <input 
                      type="checkbox" 
                      checked={exportOptions.includeRecommendations}
                      onChange={(e) => setExportOptions({...exportOptions, includeRecommendations: e.target.checked})}
                    />
                    <span>Recommandations IA</span>
                  </label>
                </div>
              </div>

              <div className="option-section">
                <h3>Personnalisation</h3>
                <div className="custom-options">
                  <div className="input-group">
                    <label>Titre du rapport</label>
                    <input 
                      type="text" 
                      value={exportOptions.customTitle}
                      onChange={(e) => setExportOptions({...exportOptions, customTitle: e.target.value})}
                      placeholder="Titre personnalisé..."
                    />
                  </div>
                  <div className="input-group">
                    <label>Période</label>
                    <select 
                      value={exportOptions.dateRange}
                      onChange={(e) => setExportOptions({...exportOptions, dateRange: e.target.value})}
                    >
                      <option value="7d">7 derniers jours</option>
                      <option value="30d">30 derniers jours</option>
                      <option value="90d">3 derniers mois</option>
                      <option value="1y">Dernière année</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {!isExporting && (
          <div className="export-modal-footer">
            <button className="cancel-btn" onClick={onClose}>
              Annuler
            </button>
            <button className="export-btn" onClick={handleExport}>
              <Download size={16} />
              Générer le rapport
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedExport;
