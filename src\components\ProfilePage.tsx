import React, { useState, useEffect, useMemo } from 'react';
import '../styles/ProfilePage.css';
import { useAuth } from '../context/AuthContext';
import { usePosts } from '../context/PostsContext';
import { supabase } from '../lib/supabase';
import { IUser, IBusinessUser, PostType } from '../types';

import ProfileStats from './profile/ProfileStats';
import ProfileBanner from './profile/ProfileBanner';
import TopRatedProducts from './profile/TopRatedProducts';
import Announcements from './profile/Announcements';
import FollowStats from './profile/FollowStats';
import UserInfoCard from './profile/UserInfoCard';
import PostCard from './posts/PostCard';
import Card, { CardBody } from './ui/Card';
import Button from './ui/Button';
import { MessageCircle, ThumbsUp, Calendar, Home, User, Filter, TrendingUp, Clock, Star } from 'lucide-react';



interface ProfilePageProps {
  userId?: string;
}

const ProfilePage: React.FC<ProfilePageProps> = ({ userId: propUserId }) => {
  const { currentUser } = useAuth();
  const { posts } = usePosts();
  const [activeTab, setActiveTab] = useState('publications');
  const [activeFilter, setActiveFilter] = useState('recent');

  // Déterminer l'ID de l'utilisateur à afficher
  const effectiveUserId = propUserId || currentUser?.id;

  const [profileData, setProfileData] = useState<IUser | IBusinessUser | null>(null);
  const [userPosts, setUserPosts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Effet pour charger les données du profil
  useEffect(() => {
    const fetchProfileData = async () => {
      if (!effectiveUserId) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', effectiveUserId)
          .single();

        if (error) throw error;

        if (data) {
          // Si c'est une entreprise, récupérer aussi les données business
          if (data.role === 'business') {
            const { data: businessData, error: businessError } = await supabase
              .from('business_profiles')
              .select('*')
              .eq('id', effectiveUserId)
              .single();

            if (businessError && businessError.code !== 'PGRST116') throw businessError;

            // Créer un objet IBusinessUser complet
            const businessUserData = {
              ...data,
              profilePicture: data.profile_picture,
              coverPhotoUrl: data.cover_photo_url,
              createdAt: data.created_at ? new Date(data.created_at) : new Date(),
              profile_completed_at: data.profile_completed_at ? new Date(data.profile_completed_at) : null,
              // Données spécifiques à l'entreprise
              businessName: businessData?.business_name || '',
              businessStatus: businessData?.business_status,
              businessDescription: businessData?.business_description || '',
              businessCategory: businessData?.business_category,
              businessAddress: businessData?.business_address,
              businessPhone: businessData?.business_phone,
              businessEmail: businessData?.business_email,
              businessWebsite: businessData?.business_website,
              businessLicense: businessData?.business_license,
              foundedYear: businessData?.founded_year,
              employeeCount: businessData?.employee_count,
              wallet: businessData?.wallet,
              salesCount: businessData?.sales_count,
              averageRating: businessData?.average_rating,
              totalReviews: businessData?.total_reviews,
              post_count: data.post_count || 0,
              comment_count: data.comment_count || 0,
              followers_count: data.followers_count || 0,
              following_count: data.following_count || 0,
              recommendations_count: 0,
            };

            setProfileData(businessUserData as IBusinessUser);
          } else {
            // Convertir les dates et mapper les champs correctement pour utilisateur normal
            const profileWithDates = {
              ...data,
              profilePicture: data.profile_picture,
              coverPhotoUrl: data.cover_photo_url,
              createdAt: data.created_at ? new Date(data.created_at) : new Date(),
              profile_completed_at: data.profile_completed_at ? new Date(data.profile_completed_at) : null
            };

            setProfileData(profileWithDates as IUser);
          }

          // Récupérer les posts de l'utilisateur
          const userPostsData = posts.filter(post => post.userId === effectiveUserId);
          setUserPosts(userPostsData);
        }
      } catch (error) {
        console.error("Erreur lors de la récupération du profil:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfileData();
  }, [effectiveUserId, posts]);

  // Vérifier si c'est le profil de l'utilisateur connecté
  const isOwnProfile = currentUser?.id === effectiveUserId;

  // Filtres pour le fil d'actualité
  const filters = [
    { id: 'recent', label: 'Les plus récents', icon: <Clock size={16} /> },
    { id: 'trending', label: 'Tendances', icon: <TrendingUp size={16} /> },
    { id: 'top', label: 'Mieux notés', icon: <Star size={16} /> },
  ];

  // Trier les posts selon le filtre actif
  const sortedPosts = useMemo(() => {
    if (activeFilter === 'recent') {
      return [...posts].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    } else if (activeFilter === 'trending') {
      return [...posts].sort((a, b) => (b.likes.length + b.comments.length * 2) - (a.likes.length + a.comments.length * 2));
    } else if (activeFilter === 'top') {
      return [...posts].sort((a, b) => b.rating - a.rating);
    }
    return posts;
  }, [posts, activeFilter]);

  // Fonction pour gérer la mise à jour du profil
  const handleProfileUpdate = async () => {
    try {
      // Recharger les données du profil après mise à jour
      const { data: updatedData, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', effectiveUserId)
        .single();

      if (error) throw error;

      if (updatedData) {
        // Si c'est une entreprise, récupérer aussi les données business
        if (updatedData.role === 'business') {
          const { data: businessData, error: businessError } = await supabase
            .from('business_profiles')
            .select('*')
            .eq('id', effectiveUserId)
            .single();

          if (businessError && businessError.code !== 'PGRST116') throw businessError;

          // Créer un objet IBusinessUser complet
          const businessUserData = {
            ...updatedData,
            profilePicture: updatedData.profile_picture,
            coverPhotoUrl: updatedData.cover_photo_url,
            createdAt: updatedData.created_at ? new Date(updatedData.created_at) : new Date(),
            profile_completed_at: updatedData.profile_completed_at ? new Date(updatedData.profile_completed_at) : null,
            // Données spécifiques à l'entreprise
            businessName: businessData?.business_name || '',
            businessStatus: businessData?.business_status,
            businessDescription: businessData?.business_description || '',
            businessCategory: businessData?.business_category,
            post_count: updatedData.post_count || 0,
            comment_count: updatedData.comment_count || 0,
            followers_count: updatedData.followers_count || 0,
            following_count: updatedData.following_count || 0,
            recommendations_count: 0,
          };

          setProfileData(businessUserData as IBusinessUser);
        } else {
          const profileWithDates = {
            ...updatedData,
            profilePicture: updatedData.profile_picture,
            coverPhotoUrl: updatedData.cover_photo_url,
            createdAt: updatedData.created_at ? new Date(updatedData.created_at) : new Date(),
            profile_completed_at: updatedData.profile_completed_at ? new Date(updatedData.profile_completed_at) : null
          };
          setProfileData(profileWithDates as IUser);
        }
      }
    } catch (error) {
      console.error("Erreur lors de la mise à jour du profil:", error);
    }
  };



  if (isLoading) {
    return (
      <div className="profile-page-container">
        <div className="loading-spinner">Chargement du profil...</div>
      </div>
    );
  }

  if (!profileData) {
    return (
      <div className="profile-page-container">
        <div className="error-message">Utilisateur non trouvé.</div>
      </div>
    );
  }



  return (
    <div className="profile-page-container">
      {/* En-tête du profil avec photo de couverture et avatar + bouton éditer profil */}
      <ProfileBanner
        user={profileData}
        isFollowing={false} // Pas de suivi sur son propre profil
        onFollow={() => {}} // Pas d'action sur son propre profil
        isLoading={false}
        isOwnProfile={isOwnProfile}
        onProfileUpdate={handleProfileUpdate}
      />
      {/* Section principale avec contenu et sidebars */}
      <div className="profile-content-container">
        {/* Sidebar gauche avec statistiques */}
        <div className="profile-sidebar-left">
          {profileData && <UserInfoCard user={profileData} />}
          <div className="mt-4">
            {profileData && <ProfileStats user={profileData} />}
          </div>
          <FollowStats
            userId={effectiveUserId || ''}
            followersCount={(profileData as any)?.followers_count || 0}
            followingCount={(profileData as any)?.following_count || 0}
            className="mt-4"
          />



          <TopRatedProducts className="mt-4" />
          <Announcements className="mt-4" />
        </div>

        {/* Contenu principal */}
        <div className="profile-main-content">
          {/* Onglets de navigation */}
          <div className="profile-tabs-container">
            <div className={`flex border-b bg-white ${!isOwnProfile ? 'single-tab' : ''}`}>
              <button
                className={`${isOwnProfile ? 'flex-1' : 'w-full'} px-6 py-4 font-medium transition-colors duration-200 flex items-center justify-center space-x-3 ${
                  activeTab === 'publications'
                    ? 'border-b-2 border-blue-500 text-blue-600 bg-blue-50'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                }`}
                onClick={() => setActiveTab('publications')}
              >
                <User size={20} />
                <span className="text-base font-semibold">Publications</span>
              </button>
              {isOwnProfile && (
                <button
                  className={`flex-1 px-6 py-4 font-medium transition-colors duration-200 flex items-center justify-center space-x-3 ${
                    activeTab === 'newsfeed'
                      ? 'border-b-2 border-blue-500 text-blue-600 bg-blue-50'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                  }`}
                  onClick={() => setActiveTab('newsfeed')}
                >
                  <Home size={20} />
                  <span className="text-base font-semibold">Fil d'actualité</span>
                </button>
              )}
            </div>
          </div>

          {/* Contenu des onglets */}
          <div className="profile-tab-content">
            {activeTab === 'publications' && (
              <div className="posts-container">
                {userPosts.length > 0 ? (
                  userPosts.map(post => (
                    <div key={post.id} className="post-card">
                      <div className="post-header">
                        <div className="post-type-badge">
                          {post.type === PostType.FAVORITE ? 'Coup de cœur' : 'Coup de gueule'}
                        </div>
                        <div className="post-date">
                          <Calendar size={14} />
                          <span>{new Date(post.createdAt).toLocaleDateString('fr-FR')}</span>
                        </div>
                      </div>
                      <h3 className="post-title">{post.title || post.productName}</h3>
                      <div className="post-business">
                        <span className="business-label">Entreprise:</span>
                        <span className="business-name">{post.businessName}</span>
                      </div>
                      <p className="post-description">{post.description}</p>
                      {post.images && post.images.length > 0 && (
                        <div className="post-images">
                          <img src={post.images[0]} alt={post.title || post.productName} />
                        </div>
                      )}
                      <div className="post-footer">
                        <div className="post-stats">
                          <div className="post-stat">
                            <MessageCircle size={16} />
                            <span>{post.comments?.length || 0}</span>
                          </div>
                          <div className="post-stat">
                            <ThumbsUp size={16} />
                            <span>{post.likes?.length || 0}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="no-posts-message">
                    <p>Aucune publication pour le moment</p>
                    {isOwnProfile && (
                      <button className="create-post-button">Créer une publication</button>
                    )}
                  </div>
                )}
              </div>
            )}



            {activeTab === 'newsfeed' && isOwnProfile && (
              <div className="w-full max-w-2xl mx-auto">
                {/* Filters */}
                <Card className="mb-4">
                  <CardBody className="p-3">
                    <div className="flex items-center">
                      <Filter size={16} className="text-gray-500 mr-2" />
                      <span className="text-sm font-medium text-gray-700 mr-4">Filtrer:</span>
                      <div className="flex space-x-2 overflow-x-auto scrollbar-hide">
                        {filters.map((filter) => (
                          <Button
                            key={filter.id}
                            variant={activeFilter === filter.id ? 'primary' : 'outline'}
                            size="sm"
                            leftIcon={filter.icon}
                            onClick={() => setActiveFilter(filter.id)}
                          >
                            {filter.label}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Posts */}
                {sortedPosts.length > 0 ? (
                  sortedPosts.map((post) => (
                    <PostCard key={post.id} post={post} />
                  ))
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">Aucune publication disponible.</p>
                    <p className="mt-2 text-gray-600">Suivez des entreprises pour voir leurs publications ici !</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>


      </div>
    </div>
  );
};

export default ProfilePage;