import React, { useState } from 'react';
import {
  TrendingUp, Target, BarChart3, <PERSON>Chart, Calendar, Download,
  Users, DollarSign, Eye, MousePointer, Zap, Brain, Al<PERSON><PERSON><PERSON>gle,
  CheckCircle, ArrowUp, ArrowDown, Minus
} from 'lucide-react';
import { formatAmount, formatPercentage } from '../../utils/formatUtils';
import InteractiveCharts from './InteractiveCharts';

interface AnalyticsProps {
  analytics: {
    roas: number;
    cpa: number;
    conversionRate: number;
    avgOrderValue: number;
    lifetimeValue: number;
    reachRate: number;
    frequencyRate: number;
    brandAwareness: number;
    engagementRate: number;
    videoCompletionRate: number;
    socialShares: number;
    websiteTraffic: number;
    leadGeneration: number;
    customerRetention: number;
    marketShare: number;
  };
  chartData: any;
  timeRange: string;
  onTimeRangeChange: (range: string) => void;
  onExport: (format: 'pdf' | 'excel') => void;
}

const AdvancedAnalyticsDashboard: React.FC<AnalyticsProps> = ({
  analytics,
  chartData,
  timeRange,
  onTimeRangeChange,
  onExport
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  const timeRanges = [
    { value: '7d', label: '7 jours' },
    { value: '30d', label: '30 jours' },
    { value: '90d', label: '3 mois' },
    { value: '1y', label: '1 an' }
  ];

  const getTrendIcon = (value: number, threshold: number = 0) => {
    if (value > threshold) return <ArrowUp className="trend-up" size={16} />;
    if (value < threshold) return <ArrowDown className="trend-down" size={16} />;
    return <Minus className="trend-neutral" size={16} />;
  };

  const MetricCard = ({
    icon: Icon,
    title,
    value,
    format = 'number',
    trend,
    color = '#4f46e5',
    description
  }: {
    icon: any;
    title: string;
    value: number;
    format?: 'number' | 'currency' | 'percentage';
    trend?: number;
    color?: string;
    description?: string;
  }) => {
    const formatValue = () => {
      switch (format) {
        case 'currency':
          return formatAmount(value);
        case 'percentage':
          return formatPercentage(value);
        default:
          return value.toLocaleString('fr-FR');
      }
    };

    return (
      <div className="analytics-metric-card">
        <div className="metric-header">
          <div className="metric-icon" style={{ backgroundColor: `${color}20`, color }}>
            <Icon size={20} />
          </div>
          {trend !== undefined && (
            <div className="metric-trend">
              {getTrendIcon(trend)}
              <span className={trend > 0 ? 'trend-positive' : trend < 0 ? 'trend-negative' : 'trend-neutral'}>
                {Math.abs(trend)}%
              </span>
            </div>
          )}
        </div>
        <div className="metric-content">
          <h3 className="metric-title">{title}</h3>
          <div className="metric-value">{formatValue()}</div>
          {description && <p className="metric-description">{description}</p>}
        </div>
      </div>
    );
  };

  const PerformanceChart = () => (
    <div className="analytics-chart-container">
      <div className="chart-header">
        <h3>Performance des campagnes</h3>
        <div className="chart-legend">
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#4f46e5' }}></div>
            <span>Impressions</span>
          </div>
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#10b981' }}></div>
            <span>Clics</span>
          </div>
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#f59e0b' }}></div>
            <span>Conversions</span>
          </div>
        </div>
      </div>
      <div className="chart-placeholder">
        <div className="chart-bars">
          {chartData.performance.labels.map((label: string, index: number) => (
            <div key={label} className="chart-bar-group">
              <div className="chart-label">{label}</div>
              <div className="chart-bars-container">
                <div
                  className="chart-bar impressions"
                  style={{
                    height: `${(chartData.performance.impressions[index] / Math.max(...chartData.performance.impressions)) * 100}%`,
                    backgroundColor: '#4f46e5'
                  }}
                ></div>
                <div
                  className="chart-bar clicks"
                  style={{
                    height: `${(chartData.performance.clicks[index] / Math.max(...chartData.performance.clicks)) * 100}%`,
                    backgroundColor: '#10b981'
                  }}
                ></div>
                <div
                  className="chart-bar conversions"
                  style={{
                    height: `${(chartData.performance.conversions[index] / Math.max(...chartData.performance.conversions)) * 100}%`,
                    backgroundColor: '#f59e0b'
                  }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const AudienceInsights = () => (
    <div className="audience-insights">
      <h3>Insights Audience</h3>
      <div className="insights-grid">
        <div className="insight-section">
          <h4>Répartition par âge</h4>
          <div className="age-distribution">
            {chartData.audience.demographics.age.map((item: any) => (
              <div key={item.range} className="age-bar">
                <span className="age-label">{item.range}</span>
                <div className="age-progress">
                  <div
                    className="age-fill"
                    style={{ width: `${item.percentage}%` }}
                  ></div>
                </div>
                <span className="age-percentage">{item.percentage}%</span>
              </div>
            ))}
          </div>
        </div>

        <div className="insight-section">
          <h4>Centres d'intérêt</h4>
          <div className="interests-list">
            {chartData.audience.demographics.interests.map((item: any) => (
              <div key={item.category} className="interest-item">
                <span className="interest-name">{item.category}</span>
                <span className="interest-percentage">{item.percentage}%</span>
              </div>
            ))}
          </div>
        </div>

        <div className="insight-section">
          <h4>Répartition géographique</h4>
          <div className="geography-list">
            {chartData.audience.geography.map((item: any) => (
              <div key={item.city} className="geography-item">
                <span className="city-name">{item.city}</span>
                <div className="city-progress">
                  <div
                    className="city-fill"
                    style={{ width: `${item.percentage}%` }}
                  ></div>
                </div>
                <span className="city-percentage">{item.percentage}%</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="advanced-analytics-dashboard">
      <div className="analytics-header">
        <div className="analytics-title">
          <h2>Analytics Avancées</h2>
          <p>Analyse détaillée des performances publicitaires</p>
        </div>

        <div className="analytics-controls">
          <div className="time-range-selector">
            {timeRanges.map(range => (
              <button
                key={range.value}
                className={`time-range-btn ${timeRange === range.value ? 'active' : ''}`}
                onClick={() => onTimeRangeChange(range.value)}
              >
                {range.label}
              </button>
            ))}
          </div>

          <div className="export-buttons">
            <button
              className="export-btn pdf"
              onClick={() => onExport('pdf')}
            >
              <Download size={16} />
              PDF
            </button>
            <button
              className="export-btn excel"
              onClick={() => onExport('excel')}
            >
              <Download size={16} />
              Excel
            </button>
          </div>
        </div>
      </div>

      <div className="analytics-tabs">
        <button
          className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          <BarChart3 size={16} />
          Vue d'ensemble
        </button>
        <button
          className={`tab-btn ${activeTab === 'performance' ? 'active' : ''}`}
          onClick={() => setActiveTab('performance')}
        >
          <TrendingUp size={16} />
          Performance
        </button>
        <button
          className={`tab-btn ${activeTab === 'audience' ? 'active' : ''}`}
          onClick={() => setActiveTab('audience')}
        >
          <Users size={16} />
          Audience
        </button>
      </div>

      <div className="analytics-content">
        {activeTab === 'overview' && (
          <div className="overview-tab">
            <div className="metrics-grid">
              <MetricCard
                icon={DollarSign}
                title="ROAS"
                value={analytics.roas}
                format="number"
                trend={12.5}
                color="#10b981"
                description="Retour sur investissement publicitaire"
              />
              <MetricCard
                icon={Target}
                title="CPA"
                value={analytics.cpa}
                format="currency"
                trend={-8.2}
                color="#f59e0b"
                description="Coût par acquisition"
              />
              <MetricCard
                icon={TrendingUp}
                title="Taux de conversion"
                value={analytics.conversionRate}
                format="percentage"
                trend={15.3}
                color="#8b5cf6"
                description="Pourcentage de conversions"
              />
              <MetricCard
                icon={Users}
                title="Portée"
                value={analytics.reachRate}
                format="percentage"
                trend={5.7}
                color="#06b6d4"
                description="Pourcentage d'audience atteinte"
              />
              <MetricCard
                icon={Eye}
                title="Notoriété"
                value={analytics.brandAwareness}
                format="percentage"
                trend={22.1}
                color="#ec4899"
                description="Niveau de notoriété de marque"
              />
              <MetricCard
                icon={Zap}
                title="Engagement"
                value={analytics.engagementRate}
                format="percentage"
                trend={18.9}
                color="#f97316"
                description="Taux d'engagement moyen"
              />
            </div>
          </div>
        )}

        {activeTab === 'performance' && (
          <div className="performance-tab">
            <InteractiveCharts
              data={chartData}
              timeRange={timeRange}
              onDataUpdate={(newData) => {
                // Callback pour mettre à jour les données si nécessaire
                console.log('Données mises à jour:', newData);
              }}
            />
          </div>
        )}

        {activeTab === 'audience' && (
          <div className="audience-tab">
            <AudienceInsights />
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedAnalyticsDashboard;
