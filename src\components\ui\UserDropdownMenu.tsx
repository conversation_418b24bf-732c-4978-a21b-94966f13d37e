import React, { useState, useRef, useEffect } from 'react';
import {
  User,
  Settings,
  LogOut,
  Bell,
  MessageSquare,
  ShoppingBag,
  Heart,
  Star,
  Crown,
  Shield,
  ChevronDown,
  Store,
  Package
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { UserRole, IBusinessUser, BusinessStatus } from '../../types';
import Avatar from './Avatar';
import UserStatusBadge from './UserStatusBadge';
import BusinessStatusBadge from './BusinessStatusBadge';
import VerificationBadge from './VerificationBadge';

interface UserDropdownMenuProps {
  onProfileClick: () => void;
  onSettingsClick: () => void;
  onOrdersClick?: () => void;
  onMyRecommendationsClick?: () => void;
  onBusinessProductsClick?: () => void;
  onAdminClick?: () => void;
  className?: string;
}

const UserDropdownMenu: React.FC<UserDropdownMenuProps> = ({
  onProfileClick,
  onSettingsClick,
  onOrdersClick,
  onMyRecommendationsClick,
  onBusinessProductsClick,
  onAdminClick,
  className = ''
}) => {
  const { currentUser, logout } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Fermer le menu quand on clique à l'extérieur
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Fermer le menu quand on appuie sur Escape
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);

  if (!currentUser) return null;

  const handleMenuClick = (action: () => void) => {
    action();
    setIsOpen(false);
  };

  const handleLogout = async () => {
    try {
      await logout();
      setIsOpen(false);
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    }
  };

  const isBusinessUser = currentUser.role === UserRole.BUSINESS;
  const isAdminUser = currentUser.role === UserRole.ADMIN;
  const displayName = isBusinessUser && 'businessName' in currentUser
    ? (currentUser as any).businessName
    : currentUser.username;

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Avatar Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 p-1 rounded-full hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <div className="flex items-center space-x-2">
          {/* Badge de statut - Entreprise (priorité) ou Utilisateur normal */}
          {currentUser.role === UserRole.BUSINESS ? (
            'businessStatus' in currentUser && (currentUser as IBusinessUser).businessStatus ? (
              <BusinessStatusBadge
                status={(currentUser as IBusinessUser).businessStatus}
                size="xs"
                variant="gradient"
              />
            ) : (
              /* Badge par défaut pour les entreprises sans businessStatus */
              <BusinessStatusBadge
                status={BusinessStatus.NEW}
                size="xs"
                variant="gradient"
              />
            )
          ) : currentUser.status ? (
            /* Badge de statut - Utilisateur normal */
            <UserStatusBadge
              status={currentUser.status}
              size="xs"
              variant="gradient"
            />
          ) : null}
          <Avatar
            src={currentUser.profilePicture}
            alt={displayName}
            size="sm"
            status="online"
          />
          <ChevronDown
            size={16}
            className={`text-gray-500 transition-transform duration-200 ${
              isOpen ? 'rotate-180' : ''
            }`}
          />
        </div>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          {/* User Info Header */}
          <div className="px-4 py-3 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <Avatar
                src={currentUser.profilePicture}
                alt={displayName}
                size="md"
                status="online"
              />
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {displayName}
                  </p>
                  <VerificationBadge
                    isVerified={(currentUser as any).is_verified || false}
                    userRole={currentUser.role}
                    businessVerified={isBusinessUser ? (currentUser as any).business_verified || false : false}
                    size="xs"
                    variant="minimal"
                    showTooltip={true}
                  />
                </div>
                <p className="text-xs text-gray-500 truncate">
                  {currentUser.email}
                </p>
                <div className="mt-1">
                  {/* Badge de statut - Entreprise (priorité) ou Utilisateur normal */}
                  {currentUser.role === UserRole.BUSINESS ? (
                    'businessStatus' in currentUser && (currentUser as IBusinessUser).businessStatus ? (
                      <BusinessStatusBadge
                        status={(currentUser as IBusinessUser).businessStatus}
                        size="xs"
                        variant="default"
                      />
                    ) : (
                      /* Badge par défaut pour les entreprises sans businessStatus */
                      <BusinessStatusBadge
                        status={BusinessStatus.NEW}
                        size="xs"
                        variant="default"
                      />
                    )
                  ) : currentUser.status ? (
                    /* Badge de statut - Utilisateur normal */
                    <UserStatusBadge
                      status={currentUser.status}
                      size="xs"
                      variant="default"
                    />
                  ) : null}
                </div>
              </div>
              {isBusinessUser && (
                <div className="flex-shrink-0">
                  <div className="flex items-center px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">
                    <Store size={12} className="mr-1" />
                    Entreprise
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-1">
            {/* Profile */}
            <button
              onClick={() => handleMenuClick(onProfileClick)}
              className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <User size={16} className="mr-3 text-gray-500" />
              <span>Mon profil</span>
            </button>

            {/* Business-specific items */}
            {isBusinessUser && onBusinessProductsClick && (
              <button
                onClick={() => handleMenuClick(onBusinessProductsClick)}
                className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
              >
                <Package size={16} className="mr-3 text-gray-500" />
                <span>Mes produits</span>
              </button>
            )}

            {/* Orders */}
            {onOrdersClick && (
              <button
                onClick={() => handleMenuClick(onOrdersClick)}
                className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
              >
                <ShoppingBag size={16} className="mr-3 text-gray-500" />
                <span>{isBusinessUser ? 'Mes commandes' : 'Mes commandes'}</span>
              </button>
            )}

            {/* Recommendations */}
            {onMyRecommendationsClick && (
              <button
                onClick={() => handleMenuClick(onMyRecommendationsClick)}
                className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
              >
                <Heart size={16} className="mr-3 text-gray-500" />
                <span>Mes recommandations</span>
              </button>
            )}

            {/* Divider */}
            <div className="border-t border-gray-100 my-1"></div>

            {/* Notifications */}
            <button
              className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <Bell size={16} className="mr-3 text-gray-500" />
              <span>Notifications</span>
              <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
                3
              </span>
            </button>

            {/* Messages */}
            <button
              className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <MessageSquare size={16} className="mr-3 text-gray-500" />
              <span>Messages</span>
            </button>

            {/* Divider */}
            <div className="border-t border-gray-100 my-1"></div>

            {/* Admin Panel - Only for admins */}
            {isAdminUser && onAdminClick && (
              <button
                onClick={() => handleMenuClick(onAdminClick)}
                className="w-full flex items-center px-4 py-2 text-sm text-purple-700 hover:bg-purple-50 transition-colors"
              >
                <Shield size={16} className="mr-3 text-purple-500" />
                <span>Administration</span>
              </button>
            )}

            {/* Settings */}
            <button
              onClick={() => handleMenuClick(onSettingsClick)}
              className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <Settings size={16} className="mr-3 text-gray-500" />
              <span>Paramètres</span>
            </button>

            {/* Divider */}
            <div className="border-t border-gray-100 my-1"></div>

            {/* Logout */}
            <button
              onClick={handleLogout}
              className="w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
            >
              <LogOut size={16} className="mr-3 text-red-500" />
              <span>Se déconnecter</span>
            </button>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-100 px-4 py-2">
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Customeroom</span>
              <div className="flex items-center space-x-1">
                {currentUser.status === 'leader' && <Crown size={12} className="text-yellow-500" />}
                {currentUser.role === UserRole.ADMIN && <Shield size={12} className="text-purple-500" />}
                <span className="capitalize">{currentUser.status}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserDropdownMenu;
