import React, { useState } from 'react';
import { Brain, MessageSquare, Zap, Sparkles } from 'lucide-react';
import AIChat from './AIChat';
import './AIChatButton.css';

interface AIChatButtonProps {
  context: 'sales' | 'products' | 'rankings' | 'general';
  currentData?: any;
}

const AIChatButton: React.FC<AIChatButtonProps> = ({ context, currentData }) => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const getContextInfo = () => {
    switch (context) {
      case 'sales':
        return {
          icon: <MessageSquare size={24} />,
          label: 'Assistant Ventes IA',
          color: '#10b981',
          description: 'Optimisez vos ventes'
        };
      case 'products':
        return {
          icon: <Brain size={24} />,
          label: 'Assistant Produits IA',
          color: '#3b82f6',
          description: 'Gérez vos produits'
        };
      case 'rankings':
        return {
          icon: <Zap size={24} />,
          label: 'Assistant Rankings IA',
          color: '#f59e0b',
          description: 'Améliorez votre classement'
        };
      default:
        return {
          icon: <Sparkles size={24} />,
          label: 'Assistant IA',
          color: '#8b5cf6',
          description: 'Votre assistant personnel'
        };
    }
  };

  const contextInfo = getContextInfo();

  return (
    <>
      {/* Bouton flottant */}
      <div 
        className={`ai-chat-fab ${isChatOpen ? 'chat-open' : ''}`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => setIsChatOpen(true)}
        style={{ '--context-color': contextInfo.color } as React.CSSProperties}
      >
        {/* Icône principale */}
        <div className="fab-icon">
          {contextInfo.icon}
        </div>

        {/* Badge de notification (optionnel) */}
        <div className="fab-badge">
          <span>!</span>
        </div>

        {/* Tooltip au hover */}
        {isHovered && (
          <div className="fab-tooltip">
            <div className="tooltip-content">
              <h4>{contextInfo.label}</h4>
              <p>{contextInfo.description}</p>
            </div>
            <div className="tooltip-arrow"></div>
          </div>
        )}

        {/* Effet de pulsation */}
        <div className="fab-pulse"></div>
        <div className="fab-pulse-2"></div>
      </div>

      {/* Composant Chat IA */}
      <AIChat
        isOpen={isChatOpen}
        onClose={() => setIsChatOpen(false)}
        context={context}
        currentData={currentData}
      />
    </>
  );
};

export default AIChatButton;
