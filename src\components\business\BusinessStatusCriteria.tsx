import React, { useState, useEffect } from 'react';
import { BusinessStatusService, BusinessStatusLevel } from '../../services/businessStatusService';
import { BusinessStatus, IBusinessUser } from '../../types';
import { CheckCircle, Clock, AlertTriangle, Info, ChevronRight, Star } from 'lucide-react';
import Card, { CardBody } from '../ui/Card';

interface BusinessStatusCriteriaProps {
  businessUser: IBusinessUser;
}

const BusinessStatusCriteria: React.FC<BusinessStatusCriteriaProps> = ({ businessUser }) => {
  const [businessMetrics, setBusinessMetrics] = useState({
    profileComplete: true,
    productsCount: businessUser.catalog?.length || 0,
    salesCount: businessUser.salesCount || 0,
    reviewsCount: 0,
    averageRating: 0,
    accountAgeDays: 0,
    isVerified: businessUser.businessStatus === BusinessStatus.VERIFIED || 
                businessUser.businessStatus === BusinessStatus.PREMIUM ||
                businessUser.businessStatus === BusinessStatus.FEATURED ||
                businessUser.businessStatus === BusinessStatus.PARTNER,
    hasBusinessDocument: false,
    totalRevenue: businessUser.wallet || 0
  });

  const [expandedLevel, setExpandedLevel] = useState<BusinessStatus | null>(businessUser.businessStatus);

  useEffect(() => {
    // Calculer l'âge du compte
    const accountCreated = new Date(businessUser.createdAt);
    const now = new Date();
    const ageDays = Math.floor((now.getTime() - accountCreated.getTime()) / (1000 * 60 * 60 * 24));
    
    setBusinessMetrics(prev => ({
      ...prev,
      accountAgeDays: ageDays
    }));
  }, [businessUser.createdAt]);

  const currentLevel = BusinessStatusService.getStatusLevel(businessUser.businessStatus);
  const allLevels = BusinessStatusService.getAllStatusLevels();

  const renderStatusIcon = (level: BusinessStatusLevel, isCurrent: boolean) => {
    const IconComponent = level.icon;
    const size = isCurrent ? 24 : 20;
    
    return (
      <div className={`p-2 rounded-full ${level.bgColor} ${level.textColor}`}>
        <IconComponent size={size} />
      </div>
    );
  };

  const renderCriteriaCheck = (level: BusinessStatusLevel) => {
    const check = BusinessStatusService.checkCriteria(level.status, businessMetrics);
    
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="font-medium text-gray-900">Critères requis</span>
          <span className={`text-sm font-medium ${check.eligible ? 'text-green-600' : 'text-orange-600'}`}>
            {check.eligible ? 'Tous remplis ✓' : `${check.missingCriteria.length} manquants`}
          </span>
        </div>
        
        {/* Critères détaillés */}
        <div className="space-y-2">
          {level.criteria.profileComplete !== undefined && (
            <div className="flex items-center text-sm">
              {businessMetrics.profileComplete ? (
                <CheckCircle size={16} className="text-green-500 mr-2" />
              ) : (
                <Clock size={16} className="text-orange-500 mr-2" />
              )}
              <span>Profil d'entreprise complet</span>
            </div>
          )}
          
          {level.criteria.minProducts && (
            <div className="flex items-center text-sm">
              {businessMetrics.productsCount >= level.criteria.minProducts ? (
                <CheckCircle size={16} className="text-green-500 mr-2" />
              ) : (
                <Clock size={16} className="text-orange-500 mr-2" />
              )}
              <span>
                Au moins {level.criteria.minProducts} produits 
                ({businessMetrics.productsCount} actuellement)
              </span>
            </div>
          )}
          
          {level.criteria.minSales && (
            <div className="flex items-center text-sm">
              {businessMetrics.salesCount >= level.criteria.minSales ? (
                <CheckCircle size={16} className="text-green-500 mr-2" />
              ) : (
                <Clock size={16} className="text-orange-500 mr-2" />
              )}
              <span>
                Au moins {level.criteria.minSales} ventes 
                ({businessMetrics.salesCount} actuellement)
              </span>
            </div>
          )}
          
          {level.criteria.accountAge && (
            <div className="flex items-center text-sm">
              {businessMetrics.accountAgeDays >= level.criteria.accountAge ? (
                <CheckCircle size={16} className="text-green-500 mr-2" />
              ) : (
                <Clock size={16} className="text-orange-500 mr-2" />
              )}
              <span>
                Compte actif depuis {level.criteria.accountAge} jours 
                ({businessMetrics.accountAgeDays} jours actuellement)
              </span>
            </div>
          )}
          
          {level.criteria.verificationRequired && (
            <div className="flex items-center text-sm">
              {businessMetrics.isVerified ? (
                <CheckCircle size={16} className="text-green-500 mr-2" />
              ) : (
                <Clock size={16} className="text-orange-500 mr-2" />
              )}
              <span>Vérification d'entreprise</span>
            </div>
          )}
          
          {level.criteria.businessDocumentRequired && (
            <div className="flex items-center text-sm">
              {businessMetrics.hasBusinessDocument ? (
                <CheckCircle size={16} className="text-green-500 mr-2" />
              ) : (
                <Clock size={16} className="text-orange-500 mr-2" />
              )}
              <span>Document officiel d'entreprise</span>
            </div>
          )}
          
          {level.criteria.minRevenue && (
            <div className="flex items-center text-sm">
              {businessMetrics.totalRevenue >= level.criteria.minRevenue ? (
                <CheckCircle size={16} className="text-green-500 mr-2" />
              ) : (
                <Clock size={16} className="text-orange-500 mr-2" />
              )}
              <span>
                Chiffre d'affaires: {level.criteria.minRevenue.toLocaleString()} F CFA minimum
              </span>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderBenefits = (benefits: string[]) => (
    <div className="space-y-2">
      <span className="font-medium text-gray-900">Avantages</span>
      <ul className="space-y-1">
        {benefits.map((benefit, index) => (
          <li key={index} className="flex items-start text-sm text-gray-600">
            <Star size={14} className="text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
            <span>{benefit}</span>
          </li>
        ))}
      </ul>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* En-tête avec statut actuel */}
      <Card className="border-2 border-blue-200">
        <CardBody>
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Votre Statut d'Entreprise Actuel
            </h2>
            
            {currentLevel && (
              <div className="flex items-center justify-center mb-4">
                {renderStatusIcon(currentLevel, true)}
                <div className="ml-4 text-left">
                  <h3 className="text-lg font-medium text-gray-900">
                    {currentLevel.name}
                  </h3>
                  <p className="text-gray-600">
                    {currentLevel.description}
                  </p>
                </div>
              </div>
            )}
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-start">
                <Info size={20} className="text-blue-500 mr-3 mt-0.5" />
                <div className="text-left">
                  <h4 className="font-medium text-blue-900 mb-2">
                    Comment fonctionne le système de statut ?
                  </h4>
                  <p className="text-sm text-blue-700">
                    Votre statut d'entreprise évolue automatiquement en fonction de vos performances, 
                    de votre activité et de la qualité de vos services. Plus votre statut est élevé, 
                    plus vous bénéficiez d'avantages et de visibilité sur la plateforme.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Liste des niveaux de statut */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Tous les Niveaux de Statut
        </h3>
        
        {allLevels.map((level, index) => {
          const isCurrent = level.status === businessUser.businessStatus;
          const isExpanded = expandedLevel === level.status;
          const check = BusinessStatusService.checkCriteria(level.status, businessMetrics);
          
          return (
            <Card 
              key={level.status} 
              className={`transition-all duration-200 ${
                isCurrent ? 'border-2 border-blue-500 shadow-md' : 'border border-gray-200'
              }`}
            >
              <CardBody>
                <div 
                  className="cursor-pointer"
                  onClick={() => setExpandedLevel(isExpanded ? null : level.status)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {renderStatusIcon(level, isCurrent)}
                      <div className="ml-4">
                        <div className="flex items-center">
                          <h4 className="text-lg font-medium text-gray-900">
                            {level.name}
                          </h4>
                          {isCurrent && (
                            <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                              Actuel
                            </span>
                          )}
                          {check.eligible && !isCurrent && (
                            <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                              Éligible
                            </span>
                          )}
                        </div>
                        <p className="text-gray-600 mt-1">
                          {level.description}
                        </p>
                      </div>
                    </div>
                    
                    <ChevronRight 
                      size={20} 
                      className={`text-gray-400 transition-transform ${
                        isExpanded ? 'rotate-90' : ''
                      }`}
                    />
                  </div>
                </div>
                
                {isExpanded && (
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        {renderCriteriaCheck(level)}
                      </div>
                      <div>
                        {renderBenefits(level.benefits)}
                      </div>
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          );
        })}
      </div>
      
      {/* Note importante */}
      <Card className="bg-yellow-50 border-yellow-200">
        <CardBody>
          <div className="flex items-start">
            <AlertTriangle size={20} className="text-yellow-600 mr-3 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-900 mb-2">
                Note Importante
              </h4>
              <p className="text-sm text-yellow-800">
                Les statuts sont mis à jour automatiquement chaque jour. Pour le statut "Vérifié" et supérieur, 
                une vérification manuelle peut être requise. Contactez notre équipe support pour accélérer 
                le processus de vérification.
              </p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default BusinessStatusCriteria;
