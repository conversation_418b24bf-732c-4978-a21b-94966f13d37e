export enum QualityBadge {
  NONE = 'none',
  BRONZE = 'bronze',
  SILVER = 'silver',
  GOLD = 'gold',
  PLATINUM = 'platinum'
}

export interface IProduct {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  businessId: string;
  averageRating: number;
  qualityBadge: QualityBadge;
  images: string[];
  createdAt: Date;
  negotiable: boolean;
  stock: number;
  isOnMarketplace?: boolean;
}