import React, { useState, useEffect } from 'react';
import {
  Bell, BellRing, X, Check, Archive, ExternalLink, 
  AlertTriangle, Info, CheckCircle, Clock, Settings,
  MoreVertical, Trash2, MarkAsUnread
} from 'lucide-react';
import Card, { Card<PERSON>ody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import {
  BusinessNotificationService,
  AutoNotificationService,
  BusinessNotification,
  NotificationPriority
} from '../../services/businessNotificationService';

interface NotificationCenterProps {
  businessId: string;
  isOpen: boolean;
  onClose: () => void;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  businessId,
  isOpen,
  onClose
}) => {
  const [notifications, setNotifications] = useState<BusinessNotification[]>([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread' | 'urgent'>('unread');
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    if (isOpen) {
      loadNotifications();
      loadUnreadCount();
    }
  }, [isOpen, businessId, filter]);

  const loadNotifications = async () => {
    setLoading(true);
    try {
      const options = {
        includeRead: filter === 'all',
        includeArchived: false,
        limit: 50,
        priority: filter === 'urgent' ? 'urgent' as NotificationPriority : undefined
      };

      const notifs = await BusinessNotificationService.getBusinessNotifications(businessId, options);
      setNotifications(notifs);
    } catch (error) {
      console.error('Erreur lors du chargement des notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUnreadCount = async () => {
    const count = await BusinessNotificationService.getUnreadCount(businessId);
    setUnreadCount(count);
  };

  const handleMarkAsRead = async (notificationId: string) => {
    const success = await BusinessNotificationService.markAsRead(notificationId);
    if (success) {
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, isRead: true, readAt: new Date() } : n)
      );
      loadUnreadCount();
    }
  };

  const handleArchive = async (notificationId: string) => {
    const success = await BusinessNotificationService.archiveNotification(notificationId);
    if (success) {
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      loadUnreadCount();
    }
  };

  const handleMarkAllAsRead = async () => {
    const success = await BusinessNotificationService.markAllAsRead(businessId);
    if (success) {
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true, readAt: new Date() })));
      setUnreadCount(0);
    }
  };

  const handleGenerateTestNotifications = async () => {
    setLoading(true);
    try {
      await AutoNotificationService.generateAutomaticNotifications(businessId);
      await loadNotifications();
      await loadUnreadCount();
    } catch (error) {
      console.error('Erreur lors de la génération des notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationClick = async (notification: BusinessNotification) => {
    if (!notification.isRead) {
      await handleMarkAsRead(notification.id);
    }
    
    if (notification.actionUrl) {
      // Fermer le centre de notifications
      onClose();
      // Naviguer vers l'URL d'action
      window.location.href = notification.actionUrl;
    }
  };

  const getPriorityBadge = (priority: NotificationPriority) => {
    const styles = {
      urgent: 'bg-red-100 text-red-800 border-red-200',
      high: 'bg-orange-100 text-orange-800 border-orange-200',
      medium: 'bg-blue-100 text-blue-800 border-blue-200',
      low: 'bg-gray-100 text-gray-800 border-gray-200'
    };

    const labels = {
      urgent: 'Urgent',
      high: 'Important',
      medium: 'Moyen',
      low: 'Info'
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full border ${styles[priority]}`}>
        {labels[priority]}
      </span>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start justify-end pt-16 pr-4">
      <div className="bg-white rounded-lg shadow-xl w-96 max-h-[80vh] flex flex-col">
        {/* Header */}
        <CardHeader className="border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <BellRing className="text-blue-600" size={24} />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
                <p className="text-sm text-gray-600">
                  {unreadCount} non lue{unreadCount !== 1 ? 's' : ''}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </CardHeader>

        {/* Filters */}
        <div className="p-4 border-b bg-gray-50">
          <div className="flex space-x-2 mb-3">
            {[
              { key: 'unread', label: 'Non lues', count: unreadCount },
              { key: 'all', label: 'Toutes', count: notifications.length },
              { key: 'urgent', label: 'Urgentes', count: notifications.filter(n => n.priority === 'urgent').length }
            ].map(filterOption => (
              <button
                key={filterOption.key}
                onClick={() => setFilter(filterOption.key as any)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  filter === filterOption.key
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-100'
                }`}
              >
                {filterOption.label} ({filterOption.count})
              </button>
            ))}
          </div>

          <div className="flex space-x-2">
            <Button
              onClick={handleMarkAllAsRead}
              variant="outline"
              size="sm"
              disabled={unreadCount === 0}
            >
              <Check size={14} className="mr-1" />
              Tout marquer lu
            </Button>
            <Button
              onClick={handleGenerateTestNotifications}
              variant="outline"
              size="sm"
              disabled={loading}
            >
              <Bell size={14} className="mr-1" />
              Générer test
            </Button>
          </div>
        </div>

        {/* Notifications List */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Chargement...</p>
            </div>
          ) : notifications.length === 0 ? (
            <div className="p-8 text-center">
              <Bell className="mx-auto text-gray-400 mb-4" size={48} />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune notification</h3>
              <p className="text-gray-600">
                {filter === 'unread' ? 'Toutes vos notifications sont lues' : 'Aucune notification trouvée'}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {notifications.map(notification => {
                const style = BusinessNotificationService.getNotificationStyle(notification.type, notification.priority);
                const timeAgo = BusinessNotificationService.formatTimeAgo(notification.createdAt);

                return (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${
                      !notification.isRead ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`flex-shrink-0 w-10 h-10 rounded-full ${style.bgColor} flex items-center justify-center`}>
                        <span className="text-lg">{style.icon}</span>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h4 className={`text-sm font-medium ${!notification.isRead ? 'text-gray-900' : 'text-gray-700'}`}>
                                {notification.title}
                              </h4>
                              {getPriorityBadge(notification.priority)}
                            </div>
                            <p className="text-sm text-gray-600 mb-2">
                              {notification.message}
                            </p>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-gray-500">{timeAgo}</span>
                              {notification.actionLabel && (
                                <span className="text-xs text-blue-600 font-medium flex items-center">
                                  {notification.actionLabel}
                                  <ExternalLink size={12} className="ml-1" />
                                </span>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-1 ml-2">
                            {!notification.isRead && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleMarkAsRead(notification.id);
                                }}
                                className="text-blue-600 hover:text-blue-800 transition-colors"
                                title="Marquer comme lu"
                              >
                                <Check size={16} />
                              </button>
                            )}
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleArchive(notification.id);
                              }}
                              className="text-gray-400 hover:text-gray-600 transition-colors"
                              title="Archiver"
                            >
                              <Archive size={16} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Notifications automatiques activées</span>
            <button className="text-blue-600 hover:text-blue-800 transition-colors">
              <Settings size={16} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationCenter;
