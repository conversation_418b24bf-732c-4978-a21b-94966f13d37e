import React from 'react';
import { 
  Brain, 
  Search, 
  MessageCircle, 
  Star, 
  TrendingUp, 
  ShoppingCart,
  Lightbulb,
  Target,
  HelpCircle
} from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';

const AICapabilitiesHelp: React.FC = () => {
  return (
    <Card className="mb-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
      <CardHeader>
        <div className="flex items-center">
          <div className="bg-blue-100 p-2 rounded-full mr-3">
            <HelpCircle className="text-blue-600" size={20} />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Comment utiliser l'Assistant IA</h3>
            <p className="text-sm text-gray-600">Découvrez tout ce que je peux faire pour vous aider</p>
          </div>
        </div>
      </CardHeader>
      <CardBody>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          
          {/* Analyse de produits spécifiques */}
          <div className="bg-white p-4 rounded-lg border border-blue-100">
            <div className="flex items-center mb-3">
              <Search className="text-blue-500 mr-2" size={18} />
              <h4 className="font-semibold text-gray-900">Analyse de Produits</h4>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Je peux analyser n'importe quel produit spécifique de notre catalogue.
            </p>
            <div className="space-y-2">
              <div className="bg-gray-50 p-2 rounded text-xs">
                <strong>Exemples :</strong>
                <br />• "Que penses-tu du produit Olgane ?"
                <br />• "Analyse le smartphone Samsung"
                <br />• "Donne-moi ton avis sur cette crème"
              </div>
            </div>
          </div>

          {/* Recommandations par catégorie */}
          <div className="bg-white p-4 rounded-lg border border-green-100">
            <div className="flex items-center mb-3">
              <TrendingUp className="text-green-500 mr-2" size={18} />
              <h4 className="font-semibold text-gray-900">Recommandations</h4>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Je recommande des produits selon vos préférences et besoins.
            </p>
            <div className="space-y-2">
              <div className="bg-gray-50 p-2 rounded text-xs">
                <strong>Exemples :</strong>
                <br />• "Recommande-moi une boisson"
                <br />• "Quel produit de beauté choisir ?"
                <br />• "Conseille-moi un smartphone"
              </div>
            </div>
          </div>

          {/* Aide budgétaire */}
          <div className="bg-white p-4 rounded-lg border border-yellow-100">
            <div className="flex items-center mb-3">
              <Target className="text-yellow-500 mr-2" size={18} />
              <h4 className="font-semibold text-gray-900">Aide Budgétaire</h4>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              J'analyse votre budget et propose des options adaptées.
            </p>
            <div className="space-y-2">
              <div className="bg-gray-50 p-2 rounded text-xs">
                <strong>Exemples :</strong>
                <br />• "Aide-moi à choisir dans mon budget"
                <br />• "Quels sont les produits abordables ?"
                <br />• "Meilleur rapport qualité-prix ?"
              </div>
            </div>
          </div>

          {/* Analyse de qualité */}
          <div className="bg-white p-4 rounded-lg border border-purple-100">
            <div className="flex items-center mb-3">
              <Star className="text-purple-500 mr-2" size={18} />
              <h4 className="font-semibold text-gray-900">Analyse Qualité</h4>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              J'évalue la qualité basée sur les avis et notes clients.
            </p>
            <div className="space-y-2">
              <div className="bg-gray-50 p-2 rounded text-xs">
                <strong>Exemples :</strong>
                <br />• "Quels sont les meilleurs produits ?"
                <br />• "Produits avec les meilleures notes"
                <br />• "Qualité de ce produit ?"
              </div>
            </div>
          </div>

          {/* Tendances */}
          <div className="bg-white p-4 rounded-lg border border-orange-100">
            <div className="flex items-center mb-3">
              <TrendingUp className="text-orange-500 mr-2" size={18} />
              <h4 className="font-semibold text-gray-900">Tendances</h4>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Je vous informe sur les produits populaires et tendances.
            </p>
            <div className="space-y-2">
              <div className="bg-gray-50 p-2 rounded text-xs">
                <strong>Exemples :</strong>
                <br />• "Quels produits sont tendance ?"
                <br />• "Nouveautés populaires"
                <br />• "Que choisissent les autres ?"
              </div>
            </div>
          </div>

          {/* Conseils personnalisés */}
          <div className="bg-white p-4 rounded-lg border border-red-100">
            <div className="flex items-center mb-3">
              <Lightbulb className="text-red-500 mr-2" size={18} />
              <h4 className="font-semibold text-gray-900">Conseils Personnalisés</h4>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Mes conseils s'adaptent à votre profil et historique.
            </p>
            <div className="space-y-2">
              <div className="bg-gray-50 p-2 rounded text-xs">
                <strong>Basé sur :</strong>
                <br />• Vos commentaires passés
                <br />• Vos réactions aux posts
                <br />• Vos préférences détectées
              </div>
            </div>
          </div>

        </div>

        {/* Conseils d'utilisation */}
        <div className="mt-6 p-4 bg-white rounded-lg border border-gray-200">
          <div className="flex items-center mb-3">
            <Brain className="text-blue-600 mr-2" size={20} />
            <h4 className="font-semibold text-gray-900">💡 Conseils pour de meilleures réponses</h4>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <h5 className="font-medium text-gray-900 mb-2">✅ Questions efficaces :</h5>
              <ul className="space-y-1">
                <li>• Mentionnez le nom exact du produit</li>
                <li>• Précisez votre besoin (budget, qualité, etc.)</li>
                <li>• Demandez des comparaisons</li>
                <li>• Posez des questions spécifiques</li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium text-gray-900 mb-2">🎯 Exemples concrets :</h5>
              <ul className="space-y-1">
                <li>• "Olgane vs autres huiles de beauté ?"</li>
                <li>• "Smartphone moins de 100 000 F CFA ?"</li>
                <li>• "Produit le mieux noté en cosmétique ?"</li>
                <li>• "Que disent les avis sur ce produit ?"</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Note sur l'apprentissage */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start">
            <Brain className="text-blue-600 mr-2 mt-0.5 flex-shrink-0" size={16} />
            <div className="text-sm">
              <p className="text-blue-800">
                <strong>🧠 Mon IA s'améliore :</strong> Plus vous interagissez avec la plateforme 
                (commentaires, réactions, achats), plus mes recommandations deviennent précises et personnalisées !
              </p>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default AICapabilitiesHelp;
