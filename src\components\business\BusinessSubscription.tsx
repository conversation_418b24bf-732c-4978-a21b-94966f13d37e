import React, { useState, useEffect } from 'react';
import {
  Gift,
  Calendar,
  Crown,
  Check,
  Clock,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Shield,
  Users,
  BarChart3,
  Headphones
} from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import { supabase } from '../../lib/supabase';
import { useBusinessSubscriptionStatus } from '../../hooks/useBusinessSubscriptionStatus';

// Types pour les abonnements
interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  duration: number; // en jours
  currency: string;
  features: string[];
  icon: React.ReactNode;
  color: string;
  popular?: boolean;
  savings?: string;
  type: 'trial' | 'monthly' | 'yearly';
}

interface BusinessSubscription {
  id: string;
  plan_id: string;
  start_date: string;
  end_date: string;
  is_active: boolean;
  is_trial: boolean;
  auto_renew: boolean;
  trial_used: boolean;
  plan?: SubscriptionPlan;
}

const BusinessSubscription: React.FC = () => {
  const { status, loading, error: statusError, refetch } = useBusinessSubscriptionStatus();
  const [success, setSuccess] = useState<string | null>(null);
  const [generatingCode, setGeneratingCode] = useState<string | null>(null);
  const [generatedCode, setGeneratedCode] = useState<any | null>(null);
  const [error, setError] = useState<string | null>(null); // Ajout du state pour les erreurs

  // Plans d'abonnement disponibles
  const subscriptionPlans: SubscriptionPlan[] = [
    {
      id: 'trial-7days',
      name: 'Essai Gratuit',
      description: 'Découvrez toutes nos fonctionnalités pendant 7 jours',
      price: 0,
      duration: 7,
      currency: 'XOF',
      type: 'trial',
      icon: <Gift size={24} className="text-green-600" />,
      color: 'green',
      features: [
        'Accès complet à toutes les fonctionnalités',
        'Gestion illimitée des avis clients',
        'Tableaux de bord interactifs',
        'Notifications en temps réel',
        'Support par email',
        'Aucun engagement'
      ]
    },
    {
      id: 'monthly-plan',
      name: 'Abonnement Mensuel',
      description: 'Plan mensuel flexible pour votre entreprise',
      price: 25000,
      duration: 30,
      currency: 'XOF',
      type: 'monthly',
      icon: <Calendar size={24} className="text-blue-600" />,
      color: 'blue',
      features: [
        'Toutes les fonctionnalités incluses',
        'Gestion illimitée des avis',
        'Analytics avancées',
        'Support prioritaire',
        'Intégrations API',
        'Sauvegarde automatique',
        'Rapports personnalisés'
      ]
    },
    {
      id: 'yearly-plan',
      name: 'Abonnement Annuel',
      description: 'Économisez 20% avec notre plan annuel',
      price: 240000,
      originalPrice: 300000,
      duration: 365,
      currency: 'XOF',
      type: 'yearly',
      popular: true,
      savings: 'Économisez 60 000 F CFA',
      icon: <Crown size={24} className="text-purple-600" />,
      color: 'purple',
      features: [
        'Toutes les fonctionnalités Premium',
        'Gestion illimitée des avis',
        'Analytics avancées + IA',
        'Support prioritaire 24/7',
        'Intégrations API complètes',
        'Sauvegarde automatique',
        'Rapports personnalisés',
        'Conseiller dédié',
        'Formation personnalisée'
      ]
    }
  ];

  const handleChoosePlan = async (planId: string) => {
    try {
      console.log('🎯 DÉBUT - Clic sur plan:', planId);
      setGeneratingCode(planId);
      setError(null);
      setSuccess(null);

      const plan = subscriptionPlans.find(p => p.id === planId);
      console.log('📦 Plan trouvé:', plan);
      if (!plan) {
        setError('Plan non trouvé');
        setGeneratingCode(null);
        return;
      }

      // Récupérer l'utilisateur connecté
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setError('Utilisateur non connecté');
        setGeneratingCode(null);
        return;
      }

      // Importer le service de génération de codes
      const { SubscriptionCodeService } = await import('../../services/subscriptionCodeService');
      console.log('✅ Service importé');

      // Générer le code de validation
      const codeData = await SubscriptionCodeService.createSubscriptionCode(
        user.id,
        user.user_metadata?.business_name || user.user_metadata?.company_name || user.user_metadata?.full_name || user.email || 'Mon Entreprise',
        plan.type,
        plan.price
      );

      console.log('📝 Code reçu du service:', codeData);
      if (codeData) {
        codeData.planName = plan.name;
        setGeneratedCode(codeData);
        setSuccess(`Code de validation généré avec succès ! Transmettez ce code à l'administrateur après votre paiement.`);
      } else {
        setError('Erreur lors de la génération du code');
      }
    } catch (error) {
      console.error('❌ ERREUR COMPLÈTE:', error);
      setError('Erreur lors de la génération du code de validation');
    } finally {
      console.log('🏁 Fin génération, reset loading');
      setGeneratingCode(null);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0
    }).format(price);
  };

  const getDaysRemaining = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Chargement de votre abonnement...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Messages */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardBody className="p-4">
            <div className="flex items-center space-x-2 text-red-700">
              <AlertTriangle size={20} />
              <span>{error}</span>
            </div>
          </CardBody>
        </Card>
      )}
      {success && (
        <Card className="border-green-200 bg-green-50">
          <CardBody className="p-4">
            <div className="flex items-center space-x-2 text-green-700">
              <CheckCircle size={20} />
              <span>{success}</span>
            </div>
          </CardBody>
        </Card>
      )}
      {/* Abonnement actuel réel */}
      {status && (
        <Card className={`${!status.isActive ? 'border-red-200 bg-red-50' : status.daysRemaining <= 7 ? 'border-yellow-200 bg-yellow-50' : 'border-green-200 bg-green-50'}`}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Abonnement Actuel</h3>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${!status.isActive ? 'bg-red-100 text-red-700' : status.daysRemaining <= 7 ? 'bg-yellow-100 text-yellow-700' : 'bg-green-100 text-green-700'}`}>
                {!status.isActive ? 'Expiré' : status.daysRemaining <= 7 ? 'Expire bientôt' : 'Actif'}
              </div>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900">{status.planName}</h4>
                <p className="text-sm text-gray-600">
                  {!status.isActive ? 'Expiré' : `${status.daysRemaining} jour${status.daysRemaining > 1 ? 's' : ''} restant${status.daysRemaining > 1 ? 's' : ''}`}
                </p>
                <p className="text-xs text-gray-500">
                  Expire le {new Date(status.endDate).toLocaleDateString('fr-FR')}
                </p>
              </div>
              <Button onClick={refetch} variant="outline" size="sm">Rafraîchir</Button>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Plans d'abonnement disponibles */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Nos Plans d'Abonnement
        </h2>
        <p className="text-gray-600 mb-8">
          Choisissez le plan qui correspond le mieux aux besoins de votre entreprise.
          Après avoir choisi, vous pourrez générer un code de validation pour activer votre abonnement.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {subscriptionPlans.map((plan) => {
            const isCurrentPlan = status?.plan_id === plan.id;

            return (
              <Card
                key={plan.id}
                className={`relative ${plan.popular ? 'ring-2 ring-purple-500 shadow-lg' : ''} hover:shadow-lg transition-shadow`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                      Plus populaire
                    </span>
                  </div>
                )}

                <CardBody className="p-6">
                  <div className="text-center mb-6">
                    <div className="flex justify-center mb-3">
                      {plan.icon}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                      {plan.name}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4">
                      {plan.description}
                    </p>

                    <div className="mb-4">
                      {plan.originalPrice && (
                        <div className="text-sm text-gray-500 line-through">
                          {formatPrice(plan.originalPrice)}
                        </div>
                      )}
                      <div className="text-3xl font-bold text-gray-900">
                        {plan.price === 0 ? 'Gratuit' : formatPrice(plan.price)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {plan.duration === 7 ? '7 jours' :
                         plan.duration === 30 ? 'par mois' :
                         'par an'}
                      </div>
                      {plan.savings && (
                        <div className="text-sm text-green-600 font-medium mt-1">
                          {plan.savings}
                        </div>
                      )}
                    </div>
                  </div>

                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <Check size={16} className="text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  {isCurrentPlan ? (
                    <div className="mb-4 text-center">
                      <span className="inline-flex items-center px-3 py-1 bg-green-100 text-green-700 text-sm rounded-full">
                        <CheckCircle size={16} className="mr-1" />
                        Plan actuel
                      </span>
                    </div>
                  ) : (
                    <Button
                      onClick={() => handleChoosePlan(plan.id)}
                      variant={plan.popular ? 'primary' : 'outline'}
                      className="w-full"
                      disabled={generatingCode === plan.id}
                    >
                      {generatingCode === plan.id ? (
                        <>
                          <RefreshCw className="animate-spin mr-2" size={16} />
                          Génération du code...
                        </>
                      ) : plan.price === 0 ? (
                        'Commencer l\'essai gratuit'
                      ) : (
                        'Choisir ce plan'
                      )}
                    </Button>
                  )}
                </CardBody>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Code généré */}
      {generatedCode && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <CheckCircle className="text-green-600" size={24} />
              <h3 className="text-xl font-semibold text-green-900">
                Code de Validation Généré
              </h3>
            </div>
          </CardHeader>
          <CardBody>
            <div className="text-center mb-6">
              <div className="text-sm text-green-700 mb-2">
                Votre code de validation pour le plan <strong>{generatedCode.planName || generatedCode.plan?.name}</strong>
              </div>
              <div className="text-4xl font-bold text-green-900 mb-2 font-mono tracking-wider">
                {generatedCode.code ?
                  generatedCode.code.replace(/(\d{4})(\d{4})/, '$1-$2') :
                  generatedCode.code
                }
              </div>
              <div className="text-sm text-green-600">
                Montant: <strong>{formatPrice(generatedCode.amount)}</strong>
              </div>
              <div className="text-sm text-green-600">
                Expire le: <strong>{new Date(generatedCode.expires_at).toLocaleDateString('fr-FR')} à {new Date(generatedCode.expires_at).toLocaleTimeString('fr-FR')}</strong>
              </div>
            </div>

            <div className="bg-green-100 p-4 rounded-lg">
              <h4 className="font-medium text-green-900 mb-2">📋 Instructions</h4>
              <ol className="text-green-800 text-sm space-y-1">
                <li><strong>1.</strong> Effectuez votre paiement de <strong>{formatPrice(generatedCode.amount)}</strong></li>
                <li><strong>2.</strong> Transmettez ce code <strong>{generatedCode.code}</strong> à l'administrateur</li>
                <li><strong>3.</strong> L'administrateur validera le code après vérification du paiement</li>
                <li><strong>4.</strong> Votre abonnement sera automatiquement activé</li>
              </ol>
            </div>

            <div className="mt-4 flex justify-center">
              <Button
                onClick={() => {
                  navigator.clipboard.writeText(generatedCode.code);
                  setSuccess('Code copié dans le presse-papiers !');
                }}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <span>📋</span>
                <span>Copier le code</span>
              </Button>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Information sur le processus de validation */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Shield className="text-blue-600" size={24} />
            <h3 className="text-xl font-semibold text-blue-900">
              Processus de Validation Sécurisé
            </h3>
          </div>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-sm">1</span>
              </div>
              <div>
                <h4 className="font-medium text-blue-900">Choisir et Générer</h4>
                <p className="text-sm text-blue-700">
                  Cliquez sur "Choisir ce plan" pour générer automatiquement votre code de validation
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-sm">2</span>
              </div>
              <div>
                <h4 className="font-medium text-blue-900">Effectuer le Paiement</h4>
                <p className="text-sm text-blue-700">
                  Effectuez votre paiement selon le montant du plan choisi
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-sm">3</span>
              </div>
              <div>
                <h4 className="font-medium text-blue-900">Validation Administrative</h4>
                <p className="text-sm text-blue-700">
                  Transmettez votre code à l'administrateur qui le validera après vérification du paiement
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-sm">4</span>
              </div>
              <div>
                <h4 className="font-medium text-blue-900">Activation de l'Abonnement</h4>
                <p className="text-sm text-blue-700">
                  Une fois le paiement vérifié et le code validé, votre abonnement est automatiquement activé
                </p>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-100 rounded-lg">
            <div className="flex items-center space-x-2">
              <Clock className="text-blue-600" size={16} />
              <span className="text-sm font-medium text-blue-900">
                ⏰ Les codes expirent automatiquement après 24h pour votre sécurité
              </span>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Avantages de l'abonnement */}
      <Card>
        <CardHeader>
          <h3 className="text-xl font-semibold text-gray-900">
            Pourquoi choisir Customeroom Business ?
          </h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <BarChart3 size={20} className="text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Analytics Avancées</h4>
                <p className="text-sm text-gray-600">
                  Tableaux de bord détaillés pour suivre vos performances
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users size={20} className="text-green-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Gestion des Avis</h4>
                <p className="text-sm text-gray-600">
                  Centralisez et répondez à tous vos avis clients
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Headphones size={20} className="text-purple-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Support Prioritaire</h4>
                <p className="text-sm text-gray-600">
                  Assistance rapide et personnalisée
                </p>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default BusinessSubscription;
