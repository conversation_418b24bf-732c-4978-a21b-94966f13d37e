import { supabase } from '../lib/supabase';

// Types pour le suivi de commande
export interface TrackingEvent {
  id: string;
  tracking_id: string;
  order_id: string;
  status: OrderTrackingStatus;
  title: string;
  description: string;
  location?: string;
  timestamp: string;
  is_automated: boolean;
  created_by?: string;
  metadata?: Record<string, any>;
  created_at: string;
}

export interface OrderTracking {
  id: string;
  order_id: string;
  tracking_number: string;
  current_status: OrderTrackingStatus;
  estimated_delivery?: string;
  actual_delivery?: string;
  carrier?: string;
  created_at: string;
  updated_at: string;
  events: TrackingEvent[];
}

export enum OrderTrackingStatus {
  ORDER_CONFIRMED = 'order_confirmed',
  PAYMENT_CONFIRMED = 'payment_confirmed',
  PREPARING = 'preparing',
  READY_FOR_PICKUP = 'ready_for_pickup',
  PICKED_UP = 'picked_up',
  IN_TRANSIT = 'in_transit',
  OUT_FOR_DELIVERY = 'out_for_delivery',
  DELIVERED = 'delivered',
  DELIVERY_FAILED = 'delivery_failed',
  RETURNED = 'returned',
  CANCELLED = 'cancelled'
}

export class OrderTrackingService {
  /**
   * Initialise le suivi pour une nouvelle commande
   */
  static async initializeTracking(orderId: string, buyerId: string, sellerId: string): Promise<OrderTracking | null> {
    try {
      // Générer un numéro de suivi unique
      const trackingNumber = this.generateTrackingNumber();

      // Calculer la date de livraison estimée (3-5 jours ouvrables)
      const estimatedDelivery = this.calculateEstimatedDelivery();

      // Créer l'enregistrement de suivi principal
      const { data: tracking, error: trackingError } = await supabase
        .from('order_tracking')
        .insert([{
          order_id: orderId,
          tracking_number: trackingNumber,
          current_status: OrderTrackingStatus.ORDER_CONFIRMED,
          estimated_delivery: estimatedDelivery,
          carrier: 'Livraison Standard'
        }])
        .select()
        .single();

      if (trackingError) throw trackingError;

      // Créer l'événement initial
      await this.addTrackingEvent(
        tracking.id,
        OrderTrackingStatus.ORDER_CONFIRMED,
        'Commande confirmée',
        'Votre commande a été confirmée et est en cours de traitement',
        'Abidjan, Côte d\'Ivoire'
      );

      // Envoyer les notifications
      await this.sendTrackingNotification(buyerId, sellerId, orderId, OrderTrackingStatus.ORDER_CONFIRMED);

      return await this.getOrderTracking(orderId);
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du suivi:', error);
      return null;
    }
  }

  /**
   * Ajoute un événement de suivi
   */
  static async addTrackingEvent(
    trackingId: string,
    status: OrderTrackingStatus,
    title: string,
    description: string,
    location?: string,
    metadata?: Record<string, any>
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('tracking_events')
        .insert([{
          tracking_id: trackingId,
          order_id: (await supabase.from('order_tracking').select('order_id').eq('id', trackingId).single()).data?.order_id,
          status,
          title,
          description,
          location,
          timestamp: new Date().toISOString(),
          is_automated: true,
          metadata
        }]);

      if (error) throw error;

      // Mettre à jour le statut actuel du suivi
      await supabase
        .from('order_tracking')
        .update({
          current_status: status,
          updated_at: new Date().toISOString()
        })
        .eq('id', trackingId);

      return true;
    } catch (error) {
      console.error('Erreur lors de l\'ajout de l\'événement de suivi:', error);
      return false;
    }
  }

  /**
   * Met à jour le statut d'une commande et ajoute un événement de suivi
   */
  static async updateOrderStatus(
    orderId: string,
    newStatus: OrderTrackingStatus,
    title: string,
    description: string,
    location?: string
  ): Promise<boolean> {
    try {
      // Récupérer le suivi existant
      const { data: tracking, error: trackingError } = await supabase
        .from('order_tracking')
        .select('id, order_id')
        .eq('order_id', orderId)
        .single();

      if (trackingError) throw trackingError;

      // Ajouter l'événement de suivi
      const success = await this.addTrackingEvent(
        tracking.id,
        newStatus,
        title,
        description,
        location
      );

      if (success) {
        // Récupérer les IDs de l'acheteur et du vendeur
        const { data: order, error: orderError } = await supabase
          .from('orders')
          .select('buyer_id, seller_id')
          .eq('id', orderId)
          .single();

        if (!orderError && order) {
          // Envoyer les notifications
          await this.sendTrackingNotification(order.buyer_id, order.seller_id, orderId, newStatus);
        }
      }

      return success;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error);
      return false;
    }
  }

  /**
   * Récupère le suivi complet d'une commande
   */
  static async getOrderTracking(orderId: string): Promise<OrderTracking | null> {
    try {
      const { data: tracking, error: trackingError } = await supabase
        .from('order_tracking')
        .select(`
          *,
          events:tracking_events(*)
        `)
        .eq('order_id', orderId)
        .single();

      if (trackingError) throw trackingError;

      // Trier les événements par date
      if (tracking.events) {
        tracking.events.sort((a: any, b: any) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      }

      return tracking;
    } catch (error) {
      console.error('Erreur lors de la récupération du suivi:', error);
      return null;
    }
  }

  /**
   * Génère un numéro de suivi unique
   */
  private static generateTrackingNumber(): string {
    const prefix = 'TRK';
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  }

  /**
   * Calcule la date de livraison estimée
   */
  private static calculateEstimatedDelivery(): string {
    const now = new Date();
    const deliveryDays = Math.floor(Math.random() * 3) + 3; // 3-5 jours
    now.setDate(now.getDate() + deliveryDays);
    return now.toISOString();
  }

  /**
   * Envoie les notifications de suivi
   */
  private static async sendTrackingNotification(
    buyerId: string,
    sellerId: string,
    orderId: string,
    status: OrderTrackingStatus
  ): Promise<void> {
    try {
      const notifications = [
        {
          user_id: buyerId,
          type: 'order_tracking',
          title: this.getNotificationTitle(status, 'buyer'),
          message: this.getNotificationMessage(status, 'buyer'),
          data: { order_id: orderId, status }
        },
        {
          user_id: sellerId,
          type: 'order_tracking',
          title: this.getNotificationTitle(status, 'seller'),
          message: this.getNotificationMessage(status, 'seller'),
          data: { order_id: orderId, status }
        }
      ];

      await supabase
        .from('notifications')
        .insert(notifications);
    } catch (error) {
      console.error('Erreur lors de l\'envoi des notifications:', error);
    }
  }

  /**
   * Génère le titre de notification selon le statut et le rôle
   */
  private static getNotificationTitle(status: OrderTrackingStatus, role: 'buyer' | 'seller'): string {
    const titles = {
      [OrderTrackingStatus.ORDER_CONFIRMED]: {
        buyer: 'Commande confirmée',
        seller: 'Nouvelle commande reçue'
      },
      [OrderTrackingStatus.PAYMENT_CONFIRMED]: {
        buyer: 'Paiement confirmé',
        seller: 'Paiement reçu'
      },
      [OrderTrackingStatus.PREPARING]: {
        buyer: 'Commande en préparation',
        seller: 'Préparez la commande'
      },
      [OrderTrackingStatus.READY_FOR_PICKUP]: {
        buyer: 'Commande prête',
        seller: 'Commande prête pour enlèvement'
      },
      [OrderTrackingStatus.IN_TRANSIT]: {
        buyer: 'Commande en transit',
        seller: 'Commande expédiée'
      },
      [OrderTrackingStatus.DELIVERED]: {
        buyer: 'Commande livrée',
        seller: 'Commande livrée avec succès'
      }
    };

    return titles[status]?.[role] || 'Mise à jour de commande';
  }

  /**
   * Génère le message de notification selon le statut et le rôle
   */
  private static getNotificationMessage(status: OrderTrackingStatus, role: 'buyer' | 'seller'): string {
    const messages = {
      [OrderTrackingStatus.ORDER_CONFIRMED]: {
        buyer: 'Votre commande a été confirmée et est en cours de traitement.',
        seller: 'Vous avez reçu une nouvelle commande. Préparez-la pour l\'expédition.'
      },
      [OrderTrackingStatus.PAYMENT_CONFIRMED]: {
        buyer: 'Votre paiement a été confirmé avec succès.',
        seller: 'Le paiement de la commande a été reçu.'
      },
      [OrderTrackingStatus.PREPARING]: {
        buyer: 'Votre commande est actuellement en cours de préparation.',
        seller: 'Veuillez préparer la commande pour l\'expédition.'
      },
      [OrderTrackingStatus.DELIVERED]: {
        buyer: 'Votre commande a été livrée avec succès.',
        seller: 'La commande a été livrée au client.'
      }
    };

    return messages[status]?.[role] || 'Votre commande a été mise à jour.';
  }

  /**
   * Simule la progression automatique d'une commande (pour les tests)
   */
  static async simulateOrderProgression(orderId: string): Promise<void> {
    const progressionSteps = [
      {
        status: OrderTrackingStatus.PAYMENT_CONFIRMED,
        title: 'Paiement confirmé',
        description: 'Le paiement a été traité avec succès',
        delay: 2000
      },
      {
        status: OrderTrackingStatus.PREPARING,
        title: 'Préparation en cours',
        description: 'Votre commande est en cours de préparation',
        delay: 5000
      },
      {
        status: OrderTrackingStatus.READY_FOR_PICKUP,
        title: 'Prête pour enlèvement',
        description: 'Votre commande est prête et attend l\'enlèvement',
        delay: 8000
      },
      {
        status: OrderTrackingStatus.IN_TRANSIT,
        title: 'En transit',
        description: 'Votre commande est en route vers sa destination',
        delay: 12000
      }
    ];

    for (const step of progressionSteps) {
      setTimeout(async () => {
        await this.updateOrderStatus(
          orderId,
          step.status,
          step.title,
          step.description,
          'Abidjan, Côte d\'Ivoire'
        );
      }, step.delay);
    }
  }
}
