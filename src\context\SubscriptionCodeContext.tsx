import React, { createContext, useContext, useState, useEffect } from 'react';
import { SubscriptionCode } from '../services/subscriptionCodeService';

interface SubscriptionCodeContextType {
  generatedCodes: SubscriptionCode[];
  addCode: (code: SubscriptionCode) => void;
  getAllCodes: () => SubscriptionCode[];
  clearCodes: () => void;
}

const SubscriptionCodeContext = createContext<SubscriptionCodeContextType | undefined>(undefined);

export const useSubscriptionCodes = () => {
  const context = useContext(SubscriptionCodeContext);
  if (!context) {
    throw new Error('useSubscriptionCodes must be used within a SubscriptionCodeProvider');
  }
  return context;
};

export const SubscriptionCodeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [generatedCodes, setGeneratedCodes] = useState<SubscriptionCode[]>([]);

  // Charger les codes depuis localStorage au démarrage
  useEffect(() => {
    try {
      const stored = localStorage.getItem('customeroom_generated_codes');
      if (stored) {
        const codes = JSON.parse(stored);
        setGeneratedCodes(codes);
        console.log('🔄 CONTEXT - Codes chargés depuis localStorage:', codes.length);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des codes:', error);
    }
  }, []);

  // Sauvegarder dans localStorage à chaque changement
  useEffect(() => {
    try {
      localStorage.setItem('customeroom_generated_codes', JSON.stringify(generatedCodes));
      console.log('💾 CONTEXT - Codes sauvés dans localStorage:', generatedCodes.length);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des codes:', error);
    }
  }, [generatedCodes]);

  const addCode = (code: SubscriptionCode) => {
    setGeneratedCodes(prev => {
      const updated = [...prev, code];
      console.log('➕ CONTEXT - Code ajouté, total:', updated.length);
      return updated;
    });
  };

  const getAllCodes = () => {
    console.log('📋 CONTEXT - getAllCodes appelé, codes disponibles:', generatedCodes.length);
    return generatedCodes;
  };

  const clearCodes = () => {
    setGeneratedCodes([]);
    localStorage.removeItem('customeroom_generated_codes');
    console.log('🗑️ CONTEXT - Codes vidés');
  };

  return (
    <SubscriptionCodeContext.Provider value={{
      generatedCodes,
      addCode,
      getAllCodes,
      clearCodes
    }}>
      {children}
    </SubscriptionCodeContext.Provider>
  );
};
