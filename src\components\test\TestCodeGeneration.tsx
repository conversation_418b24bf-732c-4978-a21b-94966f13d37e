import React, { useState } from 'react';
import { SubscriptionCodeService } from '../../services/subscriptionCodeService';
import Button from '../ui/Button';
import Card, { CardBody, CardHeader } from '../ui/Card';

/**
 * 🧪 Test - Génération de Codes d'Abonnement
 * 
 * Test direct du service de génération de codes pour vérifier
 * que la génération fonctionne correctement.
 */
const TestCodeGeneration: React.FC = () => {
  const [generatedCode, setGeneratedCode] = useState<any | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testCodeGeneration = async (planType: 'trial' | 'monthly' | 'yearly', amount: number) => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🧪 Test de génération de code:', { planType, amount });
      
      const code = await SubscriptionCodeService.createSubscriptionCode(
        'test_business_123',
        'Entreprise Test',
        planType,
        amount
      );
      
      if (code) {
        // Ajouter le nom du plan
        code.planName = planType === 'trial' ? 'Essai Gratuit' : 
                       planType === 'monthly' ? 'Plan Mensuel' : 'Plan Annuel';
        
        setGeneratedCode(code);
        console.log('✅ Code généré avec succès:', code);
      } else {
        setError('Erreur lors de la génération du code');
      }
      
    } catch (err) {
      console.error('❌ Erreur:', err);
      setError('Erreur lors de la génération du code');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0
    }).format(price);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* En-tête */}
        <div className="mb-6 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            🧪 Test - Génération de Codes d'Abonnement
          </h1>
          <p className="text-gray-600">
            Test direct du service SubscriptionCodeService pour vérifier la génération de codes.
          </p>
        </div>

        {/* Boutons de test */}
        <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            onClick={() => testCodeGeneration('trial', 0)}
            disabled={loading}
            className="h-20 flex flex-col items-center justify-center"
          >
            <div className="text-lg mb-1">🎁</div>
            <div>Essai Gratuit</div>
            <div className="text-sm opacity-75">0 F CFA</div>
          </Button>
          
          <Button
            onClick={() => testCodeGeneration('monthly', 25000)}
            disabled={loading}
            className="h-20 flex flex-col items-center justify-center"
          >
            <div className="text-lg mb-1">📅</div>
            <div>Plan Mensuel</div>
            <div className="text-sm opacity-75">25,000 F CFA</div>
          </Button>
          
          <Button
            onClick={() => testCodeGeneration('yearly', 240000)}
            disabled={loading}
            className="h-20 flex flex-col items-center justify-center"
          >
            <div className="text-lg mb-1">👑</div>
            <div>Plan Annuel</div>
            <div className="text-sm opacity-75">240,000 F CFA</div>
          </Button>
        </div>

        {/* État de chargement */}
        {loading && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-blue-700">Génération du code en cours...</span>
            </div>
          </div>
        )}

        {/* Erreur */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="text-red-700">❌ {error}</div>
          </div>
        )}

        {/* Code généré */}
        {generatedCode && (
          <Card className="border-green-200 bg-green-50 mb-6">
            <CardHeader>
              <h2 className="text-xl font-semibold text-green-900">
                ✅ Code Généré avec Succès
              </h2>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium text-green-900 mb-3">Informations du Code</h3>
                  <div className="space-y-2 text-sm">
                    <div><strong>Code:</strong> <span className="font-mono text-lg">{generatedCode.code}</span></div>
                    <div><strong>Code Formaté:</strong> <span className="font-mono text-lg">{generatedCode.code?.replace(/(\d{4})(\d{4})/, '$1-$2')}</span></div>
                    <div><strong>Plan:</strong> {generatedCode.planName}</div>
                    <div><strong>Type:</strong> {generatedCode.plan_type}</div>
                    <div><strong>Montant:</strong> {formatPrice(generatedCode.amount)}</div>
                    <div><strong>Statut:</strong> {generatedCode.status}</div>
                  </div>
                </div>
                <div>
                  <h3 className="font-medium text-green-900 mb-3">Dates</h3>
                  <div className="space-y-2 text-sm">
                    <div><strong>Généré le:</strong> {new Date(generatedCode.generated_at).toLocaleString('fr-FR')}</div>
                    <div><strong>Expire le:</strong> {new Date(generatedCode.expires_at).toLocaleString('fr-FR')}</div>
                    <div><strong>Créé le:</strong> {new Date(generatedCode.created_at).toLocaleString('fr-FR')}</div>
                  </div>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-green-100 rounded">
                <h4 className="font-medium text-green-900 mb-2">JSON Complet</h4>
                <pre className="text-xs text-green-800 overflow-x-auto">
                  {JSON.stringify(generatedCode, null, 2)}
                </pre>
              </div>
            </CardBody>
          </Card>
        )}

        {/* Instructions */}
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h2 className="text-lg font-semibold text-yellow-800 mb-3">
            📋 Instructions de Test
          </h2>
          <ol className="list-decimal list-inside text-yellow-700 space-y-1 text-sm">
            <li>Cliquez sur un des boutons ci-dessus pour tester la génération</li>
            <li>Vérifiez que le code est généré sans erreur</li>
            <li>Observez le format du code (8 chiffres)</li>
            <li>Vérifiez que le montant correspond au plan choisi</li>
            <li>Confirmez que l'expiration est fixée à 24h</li>
            <li>Ouvrez la console (F12) pour voir les logs détaillés</li>
          </ol>
        </div>

        {/* Codes attendus */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h2 className="text-lg font-semibold text-blue-800 mb-3">
            🎯 Codes Attendus
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="bg-white p-3 rounded border">
              <div className="font-medium text-blue-800">🎁 Essai Gratuit</div>
              <div className="font-mono">00000001, 00000002...</div>
              <div className="text-blue-600">0 F CFA</div>
            </div>
            <div className="bg-white p-3 rounded border">
              <div className="font-medium text-blue-800">📅 Plan Mensuel</div>
              <div className="font-mono">02500001, 02500002...</div>
              <div className="text-blue-600">25,000 F CFA</div>
            </div>
            <div className="bg-white p-3 rounded border">
              <div className="font-medium text-blue-800">👑 Plan Annuel</div>
              <div className="font-mono">24000001, 24000002...</div>
              <div className="text-blue-600">240,000 F CFA</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestCodeGeneration;
