import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../context/AuthContext';
import UserStatusBadge from '../components/ui/UserStatusBadge';
import { UserStatus, UserRole } from '../types';
import { initializeUserStatuses, forceUpdateAllStatuses } from '../utils/statusInitializer';
import FollowRequestDebugPanel from '../components/debug/FollowRequestDebugPanel';

interface UserData {
  id: string;
  username: string;
  status: UserStatus;
  role: UserRole;
  profile_picture?: string;
}

interface PostData {
  id: string;
  author_username: string;
  author_status: UserStatus;
  author_role: UserRole;
  business_name: string;
}

const TestStatusPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [users, setUsers] = useState<UserData[]>([]);
  const [posts, setPosts] = useState<PostData[]>([]);
  const [loading, setLoading] = useState(true);
  const [initLoading, setInitLoading] = useState(false);
  const [updateLoading, setUpdateLoading] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Récupérer les utilisateurs
      const { data: usersData, error: usersError } = await supabase
        .from('profiles')
        .select('id, username, status, role, profile_picture')
        .limit(10);

      if (usersError) {
        console.error('Erreur lors de la récupération des utilisateurs:', usersError);
      } else {
        console.log('Utilisateurs récupérés:', usersData);
        setUsers(usersData || []);
      }

      // Récupérer les posts avec les détails de l'auteur
      const { data: postsData, error: postsError } = await supabase
        .from('posts_with_author_details')
        .select('id, author_username, author_status, author_role, business_name')
        .limit(10);

      if (postsError) {
        console.error('Erreur lors de la récupération des posts:', postsError);
      } else {
        console.log('Posts récupérés:', postsData);
        setPosts(postsData || []);
      }

    } catch (error) {
      console.error('Erreur générale:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateUserStatus = async (userId: string, newStatus: UserStatus) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ status: newStatus })
        .eq('id', userId);

      if (error) {
        console.error('Erreur lors de la mise à jour du statut:', error);
        alert('Erreur lors de la mise à jour du statut');
      } else {
        alert('Statut mis à jour avec succès !');
        fetchData(); // Recharger les données
      }
    } catch (error) {
      console.error('Erreur:', error);
    }
  };

  const handleInitializeStatuses = async () => {
    setInitLoading(true);
    try {
      const result = await initializeUserStatuses();
      if (result.success) {
        alert(`${result.message}\n\nStatistiques:\n${JSON.stringify(result.stats, null, 2)}`);
        fetchData(); // Recharger les données
      } else {
        alert(`Erreur: ${result.message}`);
      }
    } catch (error) {
      console.error('Erreur lors de l\'initialisation:', error);
      alert('Erreur lors de l\'initialisation des statuts');
    } finally {
      setInitLoading(false);
    }
  };

  const handleForceUpdateAll = async () => {
    setUpdateLoading(true);
    try {
      const result = await forceUpdateAllStatuses();
      if (result.success) {
        alert(`${result.message}\n\nTraités: ${result.processed}\nMis à jour: ${result.updated}`);
        fetchData(); // Recharger les données
      } else {
        alert(`Erreur: ${result.message}`);
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      alert('Erreur lors de la mise à jour des statuts');
    } finally {
      setUpdateLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Test des Badges de Statut</h1>

      {/* Panel de Debug des Demandes de Suivi */}
      <div className="mb-8">
        <FollowRequestDebugPanel />
      </div>

      {/* Boutons d'administration */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Administration des Statuts</h2>
        <div className="flex space-x-4">
          <button
            onClick={handleInitializeStatuses}
            disabled={initLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {initLoading ? 'Initialisation...' : 'Initialiser les statuts'}
          </button>
          <button
            onClick={handleForceUpdateAll}
            disabled={updateLoading}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {updateLoading ? 'Mise à jour...' : 'Forcer la mise à jour'}
          </button>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          Utilisez ces boutons pour initialiser ou mettre à jour les statuts de tous les utilisateurs.
        </p>
      </div>

      {/* Utilisateur actuel */}
      {currentUser && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Utilisateur Actuel</h2>
          <div className="flex items-center space-x-4">
            <div>
              <p><strong>Nom:</strong> {currentUser.username}</p>
              <p><strong>Rôle:</strong> {currentUser.role}</p>
              <p><strong>Statut:</strong> {currentUser.status}</p>
            </div>
            <div>
              <UserStatusBadge status={currentUser.status} size="lg" variant="gradient" />
            </div>
          </div>
        </div>
      )}

      {/* Test des différents badges */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Test des Badges</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {Object.values(UserStatus).map(status => (
            <div key={status} className="text-center p-4 border rounded-lg">
              <p className="text-sm text-gray-600 mb-2">{status}</p>
              <UserStatusBadge status={status} size="md" variant="default" />
              <br />
              <UserStatusBadge status={status} size="sm" variant="gradient" className="mt-2" />
            </div>
          ))}
        </div>
      </div>

      {/* Utilisateurs de la base de données */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Utilisateurs (Base de données)</h2>
        {users.length === 0 ? (
          <p className="text-gray-600">Aucun utilisateur trouvé</p>
        ) : (
          <div className="space-y-4">
            {users.map(user => (
              <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div>
                    <p><strong>{user.username}</strong></p>
                    <p className="text-sm text-gray-600">Rôle: {user.role}</p>
                    <p className="text-sm text-gray-600">Statut: {user.status}</p>
                  </div>
                  <UserStatusBadge status={user.status} size="sm" variant="default" />
                </div>
                <div className="space-x-2">
                  {Object.values(UserStatus).map(status => (
                    <button
                      key={status}
                      onClick={() => updateUserStatus(user.id, status)}
                      className={`px-3 py-1 text-xs rounded ${
                        user.status === status
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      {status}
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Posts de la base de données */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Posts (Base de données)</h2>
        {posts.length === 0 ? (
          <p className="text-gray-600">Aucun post trouvé</p>
        ) : (
          <div className="space-y-4">
            {posts.map(post => (
              <div key={post.id} className="p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div>
                    <p><strong>{post.author_username}</strong></p>
                    <p className="text-sm text-gray-600">Entreprise: {post.business_name}</p>
                    <p className="text-sm text-gray-600">Rôle: {post.author_role}</p>
                    <p className="text-sm text-gray-600">Statut: {post.author_status}</p>
                  </div>
                  {post.author_status && (
                    <UserStatusBadge status={post.author_status} size="sm" variant="default" />
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TestStatusPage;
