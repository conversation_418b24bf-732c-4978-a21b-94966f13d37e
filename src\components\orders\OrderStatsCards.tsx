import React from 'react';
import { ShoppingBag, TrendingUp, Star, Clock, Check } from 'lucide-react';
import { formatAmount } from '../../utils/formatUtils';

interface OrderStats {
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  pendingOrders: number;
  deliveredOrders: number;
}

interface OrderStatsCardsProps {
  stats: OrderStats;
}

const OrderStatsCards: React.FC<OrderStatsCardsProps> = ({ stats }) => {
  const statsData = [
    {
      title: 'Total commandes',
      value: stats.totalOrders.toString(),
      icon: ShoppingBag,
      bgColor: 'bg-blue-100',
      iconColor: 'text-blue-600',
      description: 'Commandes passées'
    },
    {
      title: 'Total dépensé',
      value: formatAmount(stats.totalSpent),
      icon: TrendingUp,
      bgColor: 'bg-green-100',
      iconColor: 'text-green-600',
      description: 'Montant total'
    },
    {
      title: 'Panier moyen',
      value: formatAmount(stats.averageOrderValue),
      icon: Star,
      bgColor: 'bg-purple-100',
      iconColor: 'text-purple-600',
      description: 'Valeur moyenne'
    },
    {
      title: 'En attente',
      value: stats.pendingOrders.toString(),
      icon: Clock,
      bgColor: 'bg-orange-100',
      iconColor: 'text-orange-600',
      description: 'À traiter'
    },
    {
      title: 'Livrées',
      value: stats.deliveredOrders.toString(),
      icon: Check,
      bgColor: 'bg-emerald-100',
      iconColor: 'text-emerald-600',
      description: 'Terminées'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      {statsData.map((stat, index) => {
        const IconComponent = stat.icon;
        return (
          <div key={index} className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <div className={`p-2 ${stat.bgColor} rounded-lg`}>
                <IconComponent className={`w-6 h-6 ${stat.iconColor}`} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default OrderStatsCards;
