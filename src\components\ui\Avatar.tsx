import React from 'react';
import { fixAvatarImagePath } from '../../utils/imageUtils';
import defaultAvatar from '../../assets/default-avatar.svg';
import defaultBusiness from '../../assets/default-business.svg';

// Utiliser l'image locale comme fallback au lieu d'une URL externe
const DEFAULT_AVATAR_URL = defaultAvatar;

interface AvatarProps {
  src: string | null | undefined;
  alt?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'custom';
  customSize?: string;
  status?: 'online' | 'offline' | 'away' | 'busy' | 'none';
  className?: string;
}

const Avatar: React.FC<AvatarProps> = ({
  src,
  alt = '',
  size = 'md',
  customSize,
  status = 'none',
  className = '',
}) => {
  // Detect if this is a business avatar by alt (since Avatar doesn't know user type directly)
  const isBusiness = alt && (alt.toLowerCase().includes('entreprise') || alt.toLowerCase().includes('business'));
  const fallback = isBusiness ? defaultBusiness : DEFAULT_AVATAR_URL;
  const [imgSrc, setImgSrc] = React.useState<string>(src ? fixAvatarImagePath(src) : fallback);

  // Update image if src prop changes
  React.useEffect(() => {
    setImgSrc(src ? fixAvatarImagePath(src) : fallback);
  }, [src, fallback]);

  const sizeStyles = {
    xs: 'h-6 w-6',
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
    custom: customSize || 'h-40 w-40', // Taille par défaut pour custom
  };

  const statusColors = {
    online: 'bg-green-500',
    offline: 'bg-gray-400',
    away: 'bg-yellow-500',
    busy: 'bg-red-500',
    none: 'hidden',
  };

  const statusSizes = {
    xs: 'h-1.5 w-1.5',
    sm: 'h-2 w-2',
    md: 'h-2.5 w-2.5',
    lg: 'h-3 w-3',
    xl: 'h-4 w-4',
    custom: 'h-4 w-4',
  };

  // Gestionnaire d'erreur pour charger l'image par défaut si l'image source ne peut pas être chargée
  const handleImageError = () => {
    setImgSrc(fallback);
  };

  const sizeClass = sizeStyles[size];
  const finalClassName = `${sizeClass} rounded-full object-cover border-2 border-white ${className}`.trim();

  return (
    <div className="relative inline-block">
      <img
        src={imgSrc}
        alt={alt}
        className={finalClassName}
        onError={handleImageError}
      />
      {status !== 'none' && (
        <span
          className={`absolute bottom-0 right-0 block rounded-full ring-2 ring-white ${statusColors[status]} ${statusSizes[size]}`}
        ></span>
      )}
    </div>
  );
};

export default Avatar;
