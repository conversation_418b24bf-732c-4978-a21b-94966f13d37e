import React, { useState } from 'react';
import { Users, UserPlus } from 'lucide-react';
import FollowersFollowingModal from './FollowersFollowingModal';

interface FollowStatsProps {
  userId: string;
  followersCount: number;
  followingCount: number;
  className?: string;
}

const FollowStats: React.FC<FollowStatsProps> = ({
  userId,
  followersCount,
  followingCount,
  className = ''
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalTab, setModalTab] = useState<'followers' | 'following'>('followers');

  const handleOpenModal = (tab: 'followers' | 'following') => {
    setModalTab(tab);
    setIsModalOpen(true);
  };

  return (
    <>
      <div className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden ${className}`}>
        <div className="bg-gradient-to-r from-green-500 to-teal-600 px-4 py-3">
          <div className="flex items-center text-white">
            <div className="bg-white/20 rounded-lg p-1.5 mr-2">
              <Users size={16} />
            </div>
            <h3 className="font-semibold text-sm">Communauté</h3>
          </div>
        </div>

        <div className="p-4">
          <div className="grid grid-cols-2 gap-3">
            {/* Abonnés */}
            <button
              onClick={() => handleOpenModal('followers')}
              className="p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg hover:from-blue-100 hover:to-blue-200 transition-all duration-200 text-left group border border-blue-200"
            >
              <div className="flex items-center justify-between mb-2">
                <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Users size={14} className="text-blue-600" />
                </div>
                <span className="text-xs text-blue-600 opacity-0 group-hover:opacity-100 transition-opacity">
                  Voir
                </span>
              </div>
              <div className="text-xl font-bold text-blue-700 mb-1">
                {followersCount.toLocaleString()}
              </div>
              <div className="text-xs text-blue-600 font-medium">
                Abonné{followersCount > 1 ? 's' : ''}
              </div>
            </button>

            {/* Abonnements */}
            <button
              onClick={() => handleOpenModal('following')}
              className="p-3 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg hover:from-emerald-100 hover:to-emerald-200 transition-all duration-200 text-left group border border-emerald-200"
            >
              <div className="flex items-center justify-between mb-2">
                <div className="w-6 h-6 bg-emerald-100 rounded-lg flex items-center justify-center">
                  <UserPlus size={14} className="text-emerald-600" />
                </div>
                <span className="text-xs text-emerald-600 opacity-0 group-hover:opacity-100 transition-opacity">
                  Voir
                </span>
              </div>
              <div className="text-xl font-bold text-emerald-700 mb-1">
                {followingCount.toLocaleString()}
              </div>
              <div className="text-xs text-emerald-600 font-medium">
                Abonnement{followingCount > 1 ? 's' : ''}
              </div>
            </button>
          </div>

          {/* Statistiques supplémentaires */}
          <div className="mt-4 pt-3 border-t border-gray-100">
            <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
              <span className="text-xs text-gray-600 font-medium">Ratio abonnés/abonnements</span>
              <span className="text-sm font-bold text-gray-900">
                {followingCount > 0
                  ? (followersCount / followingCount).toFixed(1)
                  : followersCount > 0 ? '∞' : '0'
                }
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Modal pour afficher la liste des abonnés/abonnements */}
      <FollowersFollowingModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        userId={userId}
        initialTab={modalTab}
        userCounts={{
          followers: followersCount,
          following: followingCount
        }}
      />
    </>
  );
};

export default FollowStats;
