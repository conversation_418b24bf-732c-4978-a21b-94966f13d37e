import { PostType, UserRole } from '../types/index';

/**
 * Utilitaires pour valider les types de posts selon les rôles utilisateur
 */

/**
 * Vérifie si un utilisateur peut créer un type de post donné
 */
export const canUserCreatePostType = (userRole: UserRole, postType: PostType): boolean => {
  switch (userRole) {
    case UserRole.BUSINESS:
      // Les entreprises peuvent uniquement créer des demandes d'avis
      return postType === PostType.REVIEW;
    
    case UserRole.STANDARD:
      // Les utilisateurs standards peuvent créer des coups de coeur et coups de gueule
      return postType === PostType.FAVORITE || postType === PostType.COMPLAINT;
    
    case UserRole.ADMIN:
      // Les admins peuvent créer tous les types
      return true;
    
    default:
      return false;
  }
};

/**
 * Retourne les types de posts autorisés pour un rôle donné
 */
export const getAllowedPostTypes = (userRole: UserRole): PostType[] => {
  switch (userRole) {
    case UserRole.BUSINESS:
      return [PostType.REVIEW];
    
    case UserRole.STANDARD:
      return [PostType.FAVORITE, PostType.COMPLAINT];
    
    case UserRole.ADMIN:
      return [PostType.FAVORITE, PostType.COMPLAINT, PostType.REVIEW];
    
    default:
      return [];
  }
};

/**
 * Retourne le message d'erreur approprié si un utilisateur tente de créer un type de post non autorisé
 */
export const getPostTypeErrorMessage = (userRole: UserRole, postType: PostType): string | null => {
  if (canUserCreatePostType(userRole, postType)) {
    return null;
  }

  switch (userRole) {
    case UserRole.BUSINESS:
      return 'Les entreprises peuvent uniquement créer des demandes d\'avis pour recueillir les opinions des utilisateurs sur leurs produits ou services.';
    
    case UserRole.STANDARD:
      return 'Les utilisateurs standards peuvent uniquement partager des coups de coeur ou des coups de gueule sur les produits et services qu\'ils ont testés.';
    
    default:
      return 'Vous n\'êtes pas autorisé à créer ce type de post.';
  }
};

/**
 * Retourne la description du type de post selon le rôle
 */
export const getPostTypeDescription = (postType: PostType, userRole: UserRole): string => {
  switch (postType) {
    case PostType.FAVORITE:
      return userRole === UserRole.BUSINESS 
        ? 'Non disponible pour les entreprises'
        : 'Partagez votre expérience positive avec un produit ou service que vous recommandez.';
    
    case PostType.COMPLAINT:
      return userRole === UserRole.BUSINESS 
        ? 'Non disponible pour les entreprises'
        : 'Exprimez votre mécontentement concernant un produit ou service pour alerter la communauté.';
    
    case PostType.REVIEW:
      return userRole === UserRole.BUSINESS 
        ? 'Présentez votre produit ou service et demandez l\'avis de la communauté.'
        : 'Non disponible pour les utilisateurs standards';
    
    default:
      return 'Type de post inconnu.';
  }
};

/**
 * Retourne les règles de validation pour un type de post
 */
export const getPostTypeValidationRules = (postType: PostType) => {
  switch (postType) {
    case PostType.FAVORITE:
    case PostType.COMPLAINT:
      return {
        requiresRating: true,
        minRating: 1,
        maxRating: 5,
        allowedRoles: [UserRole.STANDARD, UserRole.ADMIN]
      };
    
    case PostType.REVIEW:
      return {
        requiresRating: false,
        minRating: null,
        maxRating: null,
        allowedRoles: [UserRole.BUSINESS, UserRole.ADMIN]
      };
    
    default:
      return {
        requiresRating: false,
        minRating: null,
        maxRating: null,
        allowedRoles: []
      };
  }
};
