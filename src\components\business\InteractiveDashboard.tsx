import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>3, <PERSON><PERSON>ding<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Line<PERSON>hart, Calendar,
  Filter, Download, Refresh<PERSON><PERSON>, Eye, Users, Heart, MessageCircle
} from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import { RealBusinessDataService } from '../../services/realBusinessDataService';
import { CustomBarChart, CustomPieChart, CustomLineChart, SatisfactionGauge } from '../charts/CustomCharts';

interface InteractiveDashboardProps {
  businessId: string;
  isVisible: boolean;
}

const InteractiveDashboard: React.FC<InteractiveDashboardProps> = ({ businessId, isVisible }) => {
  const [loading, setLoading] = useState(false);
  const [businessData, setBusinessData] = useState<any>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  const [selectedChart, setSelectedChart] = useState<'posts' | 'engagement' | 'satisfaction' | 'trends'>('posts');

  useEffect(() => {
    if (isVisible && businessId) {
      loadDashboardData();
    }
  }, [isVisible, businessId, selectedTimeRange]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const data = await RealBusinessDataService.getCompleteBusinessData(businessId);
      setBusinessData(data);
    } catch (error) {
      console.error('Erreur lors du chargement des données dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isVisible) return null;

  // Filtrer les données selon la période sélectionnée
  const filterDataByTimeRange = (data: any[]) => {
    if (!data || selectedTimeRange === 'all') return data;

    const now = new Date();
    const days = selectedTimeRange === '7d' ? 7 : selectedTimeRange === '30d' ? 30 : 90;
    const cutoffDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

    return data.filter(item => new Date(item.created_at) >= cutoffDate);
  };

  const filteredPosts = businessData ? filterDataByTimeRange(businessData.posts) : [];
  const filteredRatings = businessData ? filterDataByTimeRange(businessData.ratings) : [];

  // Préparer les données pour les graphiques
  const preparePostsData = () => {
    const coupDeCoeur = filteredPosts.filter(p => p.type === 'coup_de_coeur').length;
    const coupDeGueule = filteredPosts.filter(p => p.type === 'coup_de_gueule').length;
    const demandeAvis = filteredPosts.filter(p => p.type === 'demande_avis' || p.source === 'business_post').length;

    return [
      { name: 'Coups de Cœur', value: coupDeCoeur, color: '#10B981', icon: '💚' },
      { name: 'Coups de Gueule', value: coupDeGueule, color: '#EF4444', icon: '💔' },
      { name: 'Demandes d\'Avis', value: demandeAvis, color: '#8B5CF6', icon: '📝' }
    ];
  };

  const prepareEngagementData = () => {
    const totalLikes = filteredPosts.reduce((sum, p) => sum + (p.likesCount || 0), 0);
    const totalShares = filteredPosts.reduce((sum, p) => sum + (p.sharesCount || 0), 0);
    const totalRecommendations = filteredPosts.reduce((sum, p) => sum + (p.recommendationsCount || 0), 0);
    const totalComments = filteredPosts.reduce((sum, p) => sum + (p.commentsCount || 0), 0);

    return [
      { name: 'J\'aime', value: totalLikes, color: '#EC4899', icon: '❤️' },
      { name: 'Partages', value: totalShares, color: '#3B82F6', icon: '🔄' },
      { name: 'Recommandations', value: totalRecommendations, color: '#10B981', icon: '⭐' },
      { name: 'Commentaires', value: totalComments, color: '#8B5CF6', icon: '💬' }
    ];
  };

  const prepareTrendData = () => {
    // Grouper par semaine pour les tendances
    const weeklyData: { [key: string]: any } = {};

    filteredPosts.forEach(post => {
      const date = new Date(post.created_at);
      const weekStart = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay());
      const weekKey = weekStart.toISOString().split('T')[0];

      if (!weeklyData[weekKey]) {
        weeklyData[weekKey] = {
          week: weekKey,
          coupDeCoeur: 0,
          coupDeGueule: 0,
          demandeAvis: 0,
          engagement: 0
        };
      }

      if (post.type === 'coup_de_coeur') weeklyData[weekKey].coupDeCoeur++;
      else if (post.type === 'coup_de_gueule') weeklyData[weekKey].coupDeGueule++;
      else weeklyData[weekKey].demandeAvis++;

      weeklyData[weekKey].engagement += (post.likesCount || 0) + (post.sharesCount || 0) + (post.recommendationsCount || 0);
    });

    return Object.values(weeklyData).sort((a: any, b: any) => a.week.localeCompare(b.week));
  };

  const calculateSatisfactionRate = () => {
    const coupDeCoeur = filteredPosts.filter(p => p.type === 'coup_de_coeur').length;
    const coupDeGueule = filteredPosts.filter(p => p.type === 'coup_de_gueule').length;
    const total = coupDeCoeur + coupDeGueule;
    return total > 0 ? (coupDeCoeur / total) * 100 : 0;
  };

  const timeRangeOptions = [
    { value: '7d', label: '7 jours' },
    { value: '30d', label: '30 jours' },
    { value: '90d', label: '90 jours' },
    { value: 'all', label: 'Tout' }
  ];

  const chartOptions = [
    { value: 'posts', label: 'Types de Posts', icon: <BarChart3 size={16} /> },
    { value: 'engagement', label: 'Engagement', icon: <Heart size={16} /> },
    { value: 'satisfaction', label: 'Satisfaction', icon: <TrendingUp size={16} /> },
    { value: 'trends', label: 'Tendances', icon: <LineChart size={16} /> }
  ];

  return (
    <Card className="border-l-4 border-indigo-500 bg-gradient-to-r from-indigo-50 to-purple-50">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BarChart3 className="text-indigo-600" size={24} />
            <div>
              <h3 className="text-lg font-semibold text-indigo-900">
                Tableaux de Bord Interactifs
              </h3>
              <p className="text-indigo-700 text-sm">
                Visualisations en temps réel des données business
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button onClick={loadDashboardData} variant="outline" size="sm" disabled={loading}>
              {loading ? <RefreshCw className="animate-spin" size={16} /> : <RefreshCw size={16} />}
            </Button>
            <Button variant="outline" size="sm">
              <Download size={16} className="mr-1" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardBody>
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <BarChart3 className="mx-auto text-indigo-600 mb-4 animate-pulse" size={48} />
              <p className="text-gray-600">Chargement des données...</p>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Contrôles */}
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div className="flex items-center space-x-2">
                <Calendar className="text-gray-600" size={16} />
                <span className="text-sm font-medium text-gray-700">Période :</span>
                <div className="flex space-x-1">
                  {timeRangeOptions.map(option => (
                    <button
                      key={option.value}
                      onClick={() => setSelectedTimeRange(option.value as any)}
                      className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                        selectedTimeRange === option.value
                          ? 'bg-indigo-600 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Eye className="text-gray-600" size={16} />
                <span className="text-sm font-medium text-gray-700">Vue :</span>
                <div className="flex space-x-1">
                  {chartOptions.map(option => (
                    <button
                      key={option.value}
                      onClick={() => setSelectedChart(option.value as any)}
                      className={`flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                        selectedChart === option.value
                          ? 'bg-indigo-600 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {option.icon}
                      <span>{option.label}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Métriques rapides */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white rounded-lg p-4 border border-indigo-200">
                <div className="flex items-center space-x-2">
                  <MessageCircle className="text-indigo-600" size={20} />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{filteredPosts.length}</p>
                    <p className="text-sm text-gray-600">Posts</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="text-green-600" size={20} />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{calculateSatisfactionRate().toFixed(1)}%</p>
                    <p className="text-sm text-gray-600">Satisfaction</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-4 border border-purple-200">
                <div className="flex items-center space-x-2">
                  <Heart className="text-purple-600" size={20} />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">
                      {filteredPosts.reduce((sum, p) => sum + (p.likesCount || 0) + (p.sharesCount || 0) + (p.recommendationsCount || 0), 0)}
                    </p>
                    <p className="text-sm text-gray-600">Engagement</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="flex items-center space-x-2">
                  <Users className="text-blue-600" size={20} />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">
                      {new Set(filteredPosts.map(p => p.user_id)).size}
                    </p>
                    <p className="text-sm text-gray-600">Utilisateurs actifs</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Zone de graphique - Placeholder pour l'instant */}
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">
                {chartOptions.find(opt => opt.value === selectedChart)?.label} - {timeRangeOptions.find(opt => opt.value === selectedTimeRange)?.label}
              </h4>

              {selectedChart === 'posts' && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <CustomBarChart
                    data={preparePostsData()}
                    title="Répartition des Types de Posts"
                    height={250}
                  />
                  <CustomPieChart
                    data={preparePostsData()}
                    title="Distribution"
                    size={200}
                  />
                </div>
              )}

              {selectedChart === 'engagement' && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <CustomBarChart
                    data={prepareEngagementData()}
                    title="Interactions par Type"
                    height={250}
                  />
                  <CustomPieChart
                    data={prepareEngagementData()}
                    title="Répartition de l'Engagement"
                    size={200}
                  />
                </div>
              )}

              {selectedChart === 'satisfaction' && (
                <div className="flex justify-center py-8">
                  <div className="text-center">
                    <SatisfactionGauge value={calculateSatisfactionRate()} size={300} />
                    <div className="mt-6 space-y-2">
                      <p className="text-lg font-medium text-gray-900">Taux de Satisfaction Client</p>
                      <p className="text-sm text-gray-600">Basé sur le ratio coups de cœur / coups de gueule</p>
                      <div className="grid grid-cols-2 gap-4 mt-4">
                        <div className="bg-green-50 rounded-lg p-3">
                          <div className="text-2xl font-bold text-green-600">
                            {filteredPosts.filter(p => p.type === 'coup_de_coeur').length}
                          </div>
                          <div className="text-sm text-green-700">Coups de Cœur</div>
                        </div>
                        <div className="bg-red-50 rounded-lg p-3">
                          <div className="text-2xl font-bold text-red-600">
                            {filteredPosts.filter(p => p.type === 'coup_de_gueule').length}
                          </div>
                          <div className="text-sm text-red-700">Coups de Gueule</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {selectedChart === 'trends' && (
                <div>
                  <CustomLineChart
                    data={prepareTrendData()}
                    title="Évolution des Posts et Engagement dans le Temps"
                    height={300}
                  />
                  <div className="mt-4 text-center text-sm text-gray-600">
                    <p>Les données sont groupées par semaine pour une meilleure lisibilité</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default InteractiveDashboard;
