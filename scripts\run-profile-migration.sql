-- Script pour tester la migration des profils utilisateur
-- Ce script peut être exécuté dans l'interface Supabase SQL Editor

-- 1. Vérifier la structure actuelle de la table profiles
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
ORDER BY ordinal_position;

-- 2. Vérifier les contraintes existantes
SELECT constraint_name, constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'profiles';

-- 3. Tester l'insertion d'un profil avec les nouveaux champs
-- (Remplacez les valeurs par des données de test appropriées)
/*
INSERT INTO profiles (
  id, 
  username, 
  email, 
  bio, 
  website, 
  phone, 
  profession, 
  interests,
  city,
  country,
  age,
  gender
) VALUES (
  gen_random_uuid(),
  'test_user_profile',
  '<EMAIL>',
  'Ceci est une biographie de test pour vérifier la limite de 500 caractères.',
  'https://example.com',
  '+33123456789',
  'Développeur Web',
  'Programmation, Technologie, Voyage',
  'Paris',
  'France',
  25,
  'Homme'
);
*/

-- 4. Tester la fonction de nettoyage des centres d'intérêt
SELECT clean_interests('  sport  ,   musique,voyage   ,  ') as cleaned_interests;

-- 5. Tester la fonction de statistiques de complétion
-- (Remplacez l'UUID par un ID utilisateur existant)
/*
SELECT get_profile_completion_stats('your-user-id-here');
*/

-- 6. Tester la fonction de mise à jour du profil
-- (Remplacez l'UUID par un ID utilisateur existant)
/*
SELECT update_user_profile(
  'your-user-id-here',
  'nouveau_username',
  'Nouvelle Ville',
  'Nouveau Pays',
  30,
  'Femme',
  'Nouvelle biographie mise à jour',
  'https://nouveau-site.com',
  '+33987654321',
  'Designer UX/UI',
  'Design, Art, Créativité'
);
*/

-- 7. Vérifier la vue profiles_with_stats
SELECT 
  username,
  completion_stats,
  is_profile_complete,
  interests_array
FROM profiles_with_stats 
LIMIT 5;

-- 8. Tester les contraintes de validation
-- Ces requêtes devraient échouer avec des erreurs de contrainte

-- Test bio trop longue (plus de 500 caractères)
/*
UPDATE profiles 
SET bio = repeat('a', 501) 
WHERE username = 'test_user_profile';
*/

-- Test site web invalide
/*
UPDATE profiles 
SET website = 'site-invalide' 
WHERE username = 'test_user_profile';
*/

-- Test âge invalide
/*
UPDATE profiles 
SET age = 150 
WHERE username = 'test_user_profile';
*/

-- Test genre invalide
/*
UPDATE profiles 
SET gender = 'Genre Invalide' 
WHERE username = 'test_user_profile';
*/

-- 9. Vérifier les index créés
SELECT 
  indexname,
  indexdef
FROM pg_indexes 
WHERE tablename = 'profiles'
AND indexname LIKE '%profession%' OR indexname LIKE '%city%';

-- 10. Vérifier les triggers
SELECT 
  trigger_name,
  event_manipulation,
  action_timing
FROM information_schema.triggers 
WHERE event_object_table = 'profiles';

-- 11. Statistiques sur les profils existants
SELECT 
  COUNT(*) as total_profiles,
  COUNT(bio) as profiles_with_bio,
  COUNT(website) as profiles_with_website,
  COUNT(phone) as profiles_with_phone,
  COUNT(profession) as profiles_with_profession,
  COUNT(interests) as profiles_with_interests,
  COUNT(city) as profiles_with_city,
  COUNT(country) as profiles_with_country,
  COUNT(age) as profiles_with_age,
  COUNT(gender) as profiles_with_gender
FROM profiles;

-- 12. Exemples de requêtes utiles pour l'application

-- Rechercher des utilisateurs par profession
SELECT username, profession, city, country
FROM profiles 
WHERE profession ILIKE '%développeur%'
LIMIT 10;

-- Rechercher des utilisateurs par centres d'intérêt
SELECT username, interests
FROM profiles 
WHERE interests ILIKE '%technologie%'
LIMIT 10;

-- Profils les plus complets
SELECT 
  username,
  (completion_stats->>'completion_percentage')::int as completion_percentage
FROM profiles_with_stats
WHERE is_profile_complete = true
ORDER BY (completion_stats->>'completion_percentage')::int DESC
LIMIT 10;

-- Statistiques par pays
SELECT 
  country,
  COUNT(*) as user_count,
  AVG((completion_stats->>'completion_percentage')::int) as avg_completion
FROM profiles_with_stats
WHERE country IS NOT NULL
GROUP BY country
ORDER BY user_count DESC
LIMIT 10;

-- 13. Nettoyage (décommentez si vous voulez supprimer les données de test)
/*
DELETE FROM profiles WHERE username = 'test_user_profile';
*/
