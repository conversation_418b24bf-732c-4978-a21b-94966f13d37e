import React from 'react';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { Download, FileText, Calendar, TrendingUp } from 'lucide-react';
import Button from '../ui/Button';
import { formatAmount } from '../../utils/formatUtils';
import { DashboardMetrics } from '../../services/businessDashboardService';

interface DashboardExportProps {
  metrics: DashboardMetrics | null;
  businessName: string;
  period: string;
}

const DashboardExport: React.FC<DashboardExportProps> = ({ metrics, businessName, period }) => {
  const generatePDFReport = () => {
    if (!metrics) return;

    const doc = new jsPDF();
    const currentDate = new Date().toLocaleDateString('fr-FR');
    
    // En-tête du document
    doc.setFontSize(20);
    doc.setTextColor(59, 130, 246); // Bleu
    doc.text('Rapport d\'Activité Business', 20, 30);
    
    doc.setFontSize(12);
    doc.setTextColor(0, 0, 0);
    doc.text(`Entreprise: ${businessName}`, 20, 45);
    doc.text(`Période: ${getPeriodLabel(period)}`, 20, 55);
    doc.text(`Généré le: ${currentDate}`, 20, 65);
    
    // Ligne de séparation
    doc.setDrawColor(229, 231, 235);
    doc.line(20, 75, 190, 75);
    
    // Métriques principales
    doc.setFontSize(16);
    doc.setTextColor(31, 41, 55);
    doc.text('Métriques Principales', 20, 90);
    
    const metricsData = [
      ['Métrique', 'Valeur', 'Évolution'],
      ['Chiffre d\'affaires', formatAmount(metrics.totalRevenue), `${metrics.revenueGrowth > 0 ? '+' : ''}${metrics.revenueGrowth}%`],
      ['Commandes', metrics.totalOrders.toString(), `${metrics.ordersGrowth > 0 ? '+' : ''}${metrics.ordersGrowth}%`],
      ['Produits', metrics.totalProducts.toString(), `${metrics.productsGrowth > 0 ? '+' : ''}${metrics.productsGrowth}%`],
      ['Note moyenne', `${metrics.averageRating.toFixed(1)}/5`, `${metrics.ratingGrowth > 0 ? '+' : ''}${metrics.ratingGrowth}%`],
      ['Avis clients', metrics.totalReviews.toString(), `${metrics.reviewsGrowth > 0 ? '+' : ''}${metrics.reviewsGrowth}%`],
      ['Taux de conversion', `${metrics.conversionRate.toFixed(1)}%`, `${metrics.conversionGrowth > 0 ? '+' : ''}${metrics.conversionGrowth}%`]
    ];
    
    (doc as any).autoTable({
      startY: 100,
      head: [metricsData[0]],
      body: metricsData.slice(1),
      theme: 'grid',
      headStyles: {
        fillColor: [59, 130, 246],
        textColor: 255,
        fontSize: 10,
        fontStyle: 'bold'
      },
      bodyStyles: {
        fontSize: 9,
        textColor: [31, 41, 55]
      },
      alternateRowStyles: {
        fillColor: [248, 250, 252]
      },
      columnStyles: {
        0: { cellWidth: 60 },
        1: { cellWidth: 50, halign: 'right' },
        2: { cellWidth: 40, halign: 'center' }
      }
    });
    
    // Analyse des performances
    const finalY = (doc as any).lastAutoTable.finalY + 20;
    doc.setFontSize(16);
    doc.text('Analyse des Performances', 20, finalY);
    
    doc.setFontSize(10);
    doc.setTextColor(75, 85, 99);
    
    let analysisY = finalY + 15;
    
    // Analyse du CA
    if (metrics.revenueGrowth > 0) {
      doc.setTextColor(16, 185, 129);
      doc.text('✓ Croissance positive du chiffre d\'affaires', 25, analysisY);
    } else {
      doc.setTextColor(239, 68, 68);
      doc.text('⚠ Baisse du chiffre d\'affaires', 25, analysisY);
    }
    analysisY += 10;
    
    // Analyse des commandes
    if (metrics.ordersGrowth > 0) {
      doc.setTextColor(16, 185, 129);
      doc.text('✓ Augmentation du nombre de commandes', 25, analysisY);
    } else {
      doc.setTextColor(239, 68, 68);
      doc.text('⚠ Diminution du nombre de commandes', 25, analysisY);
    }
    analysisY += 10;
    
    // Analyse de la satisfaction
    if (metrics.averageRating >= 4.0) {
      doc.setTextColor(16, 185, 129);
      doc.text('✓ Excellente satisfaction client', 25, analysisY);
    } else if (metrics.averageRating >= 3.0) {
      doc.setTextColor(245, 158, 11);
      doc.text('⚠ Satisfaction client correcte', 25, analysisY);
    } else {
      doc.setTextColor(239, 68, 68);
      doc.text('⚠ Satisfaction client à améliorer', 25, analysisY);
    }
    analysisY += 10;
    
    // Recommandations
    doc.setFontSize(16);
    doc.setTextColor(31, 41, 55);
    doc.text('Recommandations', 20, analysisY + 10);
    
    doc.setFontSize(10);
    doc.setTextColor(75, 85, 99);
    analysisY += 25;
    
    if (metrics.revenueGrowth < 0) {
      doc.text('• Analyser les causes de la baisse du CA', 25, analysisY);
      analysisY += 8;
    }
    
    if (metrics.conversionRate < 2.0) {
      doc.text('• Optimiser le processus de commande', 25, analysisY);
      analysisY += 8;
    }
    
    if (metrics.averageRating < 4.0) {
      doc.text('• Améliorer la qualité des produits/services', 25, analysisY);
      analysisY += 8;
    }
    
    doc.text('• Continuer à développer la gamme de produits', 25, analysisY);
    analysisY += 8;
    doc.text('• Renforcer la stratégie marketing', 25, analysisY);
    
    // Pied de page
    doc.setFontSize(8);
    doc.setTextColor(156, 163, 175);
    doc.text('Rapport généré par Customeroom Business - customeroom.com', 20, 280);
    
    // Télécharger le PDF
    const fileName = `rapport-business-${businessName.toLowerCase().replace(/\s+/g, '-')}-${period}-${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(fileName);
  };
  
  const getPeriodLabel = (period: string) => {
    switch (period) {
      case '7d': return '7 derniers jours';
      case '30d': return '30 derniers jours';
      case '90d': return '90 derniers jours';
      case '1y': return '1 an';
      default: return period;
    }
  };
  
  const generateCSVReport = () => {
    if (!metrics) return;
    
    const csvData = [
      ['Métrique', 'Valeur', 'Évolution'],
      ['Chiffre d\'affaires', metrics.totalRevenue.toString(), `${metrics.revenueGrowth}%`],
      ['Commandes', metrics.totalOrders.toString(), `${metrics.ordersGrowth}%`],
      ['Produits', metrics.totalProducts.toString(), `${metrics.productsGrowth}%`],
      ['Note moyenne', metrics.averageRating.toFixed(1), `${metrics.ratingGrowth}%`],
      ['Avis clients', metrics.totalReviews.toString(), `${metrics.reviewsGrowth}%`],
      ['Taux de conversion', `${metrics.conversionRate.toFixed(1)}%`, `${metrics.conversionGrowth}%`]
    ];
    
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `donnees-business-${businessName.toLowerCase().replace(/\s+/g, '-')}-${period}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };
  
  return (
    <div className="flex items-center space-x-2">
      <Button
        onClick={generatePDFReport}
        disabled={!metrics}
        className="bg-red-600 hover:bg-red-700 text-white"
        size="sm"
      >
        <FileText size={16} />
        <span className="ml-2">PDF</span>
      </Button>
      
      <Button
        onClick={generateCSVReport}
        disabled={!metrics}
        className="bg-green-600 hover:bg-green-700 text-white"
        size="sm"
      >
        <Download size={16} />
        <span className="ml-2">CSV</span>
      </Button>
    </div>
  );
};

export default DashboardExport;
