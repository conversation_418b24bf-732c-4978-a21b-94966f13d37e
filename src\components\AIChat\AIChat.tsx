import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  Send,
  X,
  Minimize2,
  Maximize2,
  MessageSquare,
  Lightbulb,
  TrendingUp,
  Users,
  Package,
  ShoppingCart,
  BarChart3,
  Target,
  Zap,
  Star,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import './AIChat.css';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

interface AIChatProps {
  isOpen: boolean;
  onClose: () => void;
  context: 'sales' | 'products' | 'rankings' | 'general';
  currentData?: any;
}

const AIChat: React.FC<AIChatProps> = ({ isOpen, onClose, context, currentData }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Messages d'accueil contextuels
  const getWelcomeMessage = () => {
    switch (context) {
      case 'sales':
        return {
          content: "🚀 Bonjour ! Je suis votre assistant IA pour les ventes. Je peux vous aider à analyser vos performances, optimiser vos revenus et identifier des opportunités de croissance. Que souhaitez-vous savoir ?",
          suggestions: [
            "Analyse mes performances de vente",
            "Comment améliorer mon chiffre d'affaires ?",
            "Quels sont mes meilleurs produits ?",
            "Prédictions pour le mois prochain"
          ]
        };
      case 'products':
        return {
          content: "📦 Salut ! Je suis votre assistant IA pour la gestion des produits. Je peux vous aider à optimiser votre catalogue, analyser les performances produits et suggérer des améliorations. Comment puis-je vous assister ?",
          suggestions: [
            "Optimise mes prix de vente",
            "Quels produits sont en sous-performance ?",
            "Recommandations de stock",
            "Analyse de la concurrence"
          ]
        };
      case 'rankings':
        return {
          content: "🏆 Hello ! Je suis votre assistant IA pour les classements. Je peux analyser votre positionnement, suggérer des stratégies d'amélioration et vous aider à grimper dans les rankings. Que voulez-vous savoir ?",
          suggestions: [
            "Comment améliorer mon classement ?",
            "Analyse de ma position concurrentielle",
            "Stratégies de croissance",
            "Benchmarking avec la concurrence"
          ]
        };
      default:
        return {
          content: "🤖 Bonjour ! Je suis votre assistant IA personnel. Je peux vous aider avec vos ventes, produits, classements et bien plus encore. Posez-moi n'importe quelle question !",
          suggestions: [
            "Vue d'ensemble de mon business",
            "Recommandations générales",
            "Analyse de performance globale",
            "Opportunités d'amélioration"
          ]
        };
    }
  };

  // Initialiser la conversation
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcome = getWelcomeMessage();
      setMessages([{
        id: '1',
        type: 'ai',
        content: welcome.content,
        timestamp: new Date(),
        suggestions: welcome.suggestions
      }]);
    }
  }, [isOpen, context]);

  // Auto-scroll vers le bas
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Générer une réponse IA contextuelle
  const generateAIResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();
    
    // Réponses contextuelles selon la page
    if (context === 'sales') {
      if (input.includes('vente') || input.includes('chiffre') || input.includes('revenus')) {
        return `📊 **Analyse de vos ventes** :\n\n✅ **Performance actuelle** : Excellent ! Vous avez généré 118 000 F CFA ce mois (+18%)\n\n🎯 **Top insights** :\n• Votre meilleur produit : "Olgane - Huile de beauté" (35% du CA)\n• Taux de conversion : 12.5% (au-dessus de la moyenne)\n• Panier moyen : 29 500 F CFA\n\n💡 **Recommandations** :\n1. Augmentez le stock d'Olgane (forte demande)\n2. Créez des bundles avec vos produits stars\n3. Lancez une campagne pour les produits beauté\n\n🔮 **Prédiction** : Avec cette tendance, vous devriez atteindre 140 000 F CFA le mois prochain !`;
      }
      
      if (input.includes('améliorer') || input.includes('optimiser')) {
        return `🚀 **Stratégies d'optimisation** :\n\n🎯 **Actions prioritaires** :\n1. **Prix** : Testez +15% sur vos produits beauté\n2. **Stock** : Réapprovisionnez les produits en rupture\n3. **Marketing** : Ciblez les clients inactifs\n\n📈 **Opportunités détectées** :\n• 67% de clients récurrents (à améliorer)\n• Catégorie "Soins" sous-exploitée\n• Potentiel cross-selling important\n\n💰 **Impact estimé** : +25% de revenus en 3 mois`;
      }
    }
    
    if (context === 'products') {
      if (input.includes('prix') || input.includes('optimiser')) {
        return `💰 **Optimisation des prix** :\n\n📊 **Analyse concurrentielle** :\n• Vos produits beauté : 15-20% sous le marché\n• Opportunité d'augmentation : +15% sans impact négatif\n• Élasticité prix favorable sur le premium\n\n🎯 **Recommandations spécifiques** :\n• Olgane Huile : 15 000 → 17 500 F (+17%)\n• Sérum Vitamine C : 25 000 → 28 000 F (+12%)\n• Crème Karité : Prix optimal actuel\n\n📈 **Impact prévu** : +22% de marge, -3% de volume = +18% de profit`;
      }
      
      if (input.includes('stock') || input.includes('rupture')) {
        return `📦 **Gestion des stocks** :\n\n⚠️ **Alertes critiques** :\n• Parfum Jasmin : RUPTURE (0 unités)\n• Masque Argile : Stock critique (5/10)\n• Sérum Vitamine C : Stock faible (18/5)\n\n📊 **Recommandations de réapprovisionnement** :\n1. **Urgent** : Parfum Jasmin (20 unités)\n2. **Cette semaine** : Masque Argile (25 unités)\n3. **Surveillance** : Olgane Huile (tendance forte)\n\n💡 **Optimisation** : Implémentez un système de réapprovisionnement automatique basé sur les ventes moyennes.`;
      }
    }
    
    if (context === 'rankings') {
      if (input.includes('classement') || input.includes('position')) {
        return `🏆 **Analyse de votre classement** :\n\n📈 **Position actuelle** :\n• Classement général : #12 (+3 places)\n• Catégorie Beauté : #8 (+5 places)\n• Note moyenne : 4.6/5 ⭐\n\n🎯 **Facteurs de succès** :\n• Qualité produits : Excellente\n• Service client : Très bon\n• Rapidité livraison : À améliorer\n\n🚀 **Plan d'action pour le TOP 10** :\n1. Améliorer la logistique (délais)\n2. Augmenter le nombre d'avis clients\n3. Diversifier l'offre produits\n\n📊 **Objectif réalisable** : TOP 8 en 2 mois avec ces actions !`;
      }
    }
    
    // Réponses génériques intelligentes
    if (input.includes('recommandation') || input.includes('conseil')) {
      return `💡 **Mes recommandations personnalisées** :\n\n🎯 **Priorité 1 - Croissance** :\n• Lancez une campagne sur vos produits stars\n• Optimisez vos prix (potentiel +18% de marge)\n• Développez le cross-selling\n\n🎯 **Priorité 2 - Fidélisation** :\n• Créez un programme de fidélité\n• Améliorez le service après-vente\n• Personnalisez l'expérience client\n\n🎯 **Priorité 3 - Innovation** :\n• Testez de nouveaux produits\n• Explorez de nouveaux canaux\n• Automatisez vos processus\n\n📈 **ROI estimé** : 35% d'amélioration globale en 6 mois`;
    }
    
    if (input.includes('prédiction') || input.includes('futur')) {
      return `🔮 **Prédictions IA** :\n\n📊 **Tendances détectées** :\n• Croissance soutenue : +18% mensuel\n• Saisonnalité favorable (beauté)\n• Demande croissante sur le bio/naturel\n\n📈 **Projections 3 mois** :\n• Chiffre d'affaires : 420 000 F CFA\n• Nouveaux clients : +45%\n• Panier moyen : +12%\n\n🎯 **Opportunités émergentes** :\n• Marché des soins masculins\n• Produits anti-âge premium\n• Cosmétiques éco-responsables\n\n⚡ **Action recommandée** : Préparez votre stock pour la haute saison !`;
    }
    
    // Réponse par défaut contextuelle
    return `🤖 **Assistant IA ${context}** :\n\nJe suis là pour vous aider ! Voici ce que je peux faire :\n\n${context === 'sales' ? '📊 Analyser vos ventes et revenus\n💰 Optimiser votre chiffre d\'affaires\n📈 Prédire les tendances futures\n🎯 Identifier les opportunités' : ''}\n${context === 'products' ? '📦 Optimiser votre catalogue\n💰 Ajuster vos prix\n📊 Analyser les performances\n🎯 Gérer vos stocks' : ''}\n${context === 'rankings' ? '🏆 Améliorer votre classement\n📈 Analyser la concurrence\n🎯 Stratégies de croissance\n⭐ Optimiser votre réputation' : ''}\n\nPosez-moi une question spécifique pour obtenir des insights personnalisés !`;
  };

  // Envoyer un message
  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simuler le délai de réponse IA
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: generateAIResponse(inputValue),
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1500);
  };

  // Utiliser une suggestion
  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
  };

  // Gérer l'envoi avec Enter
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`ai-chat-container ${isMinimized ? 'minimized' : ''}`}>
      {/* Header */}
      <div className="ai-chat-header">
        <div className="chat-header-left">
          <div className="ai-avatar">
            <Brain size={20} />
          </div>
          <div className="chat-title">
            <h3>Assistant IA</h3>
            <span className="chat-context">
              {context === 'sales' && '📊 Ventes'}
              {context === 'products' && '📦 Produits'}
              {context === 'rankings' && '🏆 Classements'}
              {context === 'general' && '🤖 Général'}
            </span>
          </div>
        </div>
        
        <div className="chat-header-actions">
          <button 
            className="chat-action-btn"
            onClick={() => setIsMinimized(!isMinimized)}
            title={isMinimized ? 'Agrandir' : 'Réduire'}
          >
            {isMinimized ? <Maximize2 size={16} /> : <Minimize2 size={16} />}
          </button>
          <button 
            className="chat-action-btn close"
            onClick={onClose}
            title="Fermer"
          >
            <X size={16} />
          </button>
        </div>
      </div>

      {/* Messages */}
      {!isMinimized && (
        <>
          <div className="ai-chat-messages">
            {messages.map((message) => (
              <div key={message.id} className={`message ${message.type}`}>
                <div className="message-avatar">
                  {message.type === 'ai' ? (
                    <Brain size={16} />
                  ) : (
                    <MessageSquare size={16} />
                  )}
                </div>
                
                <div className="message-content">
                  <div className="message-text">
                    {message.content.split('\n').map((line, index) => (
                      <div key={index}>{line}</div>
                    ))}
                  </div>
                  
                  {message.suggestions && (
                    <div className="message-suggestions">
                      {message.suggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          className="suggestion-btn"
                          onClick={() => handleSuggestionClick(suggestion)}
                        >
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  )}
                  
                  <div className="message-time">
                    {message.timestamp.toLocaleTimeString('fr-FR', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </div>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="message ai typing">
                <div className="message-avatar">
                  <Brain size={16} />
                </div>
                <div className="message-content">
                  <div className="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="ai-chat-input">
            <div className="input-container">
              <textarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Posez votre question à l'IA..."
                className="chat-textarea"
                rows={1}
              />
              <button 
                className="send-btn"
                onClick={handleSendMessage}
                disabled={!inputValue.trim()}
              >
                <Send size={16} />
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default AIChat;
