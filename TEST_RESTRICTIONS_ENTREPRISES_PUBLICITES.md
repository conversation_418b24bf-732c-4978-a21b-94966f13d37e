# 🧪 Guide de Test - Restrictions Entreprises sur Publicités

## 📋 Vue d'Ensemble

Ce guide explique comment tester les restrictions qui empêchent les comptes entreprise de commenter les publicités dans l'application customeroom.

## 🎯 Objectif des Tests

Vérifier que :
- ✅ Les **utilisateurs réguliers** peuvent commenter les publicités
- ✅ Les **administrateurs** peuvent commenter les publicités  
- 🚫 Les **comptes entreprise** ne peuvent PAS commenter les publicités
- ✅ Les **entreprises** peuvent toujours liker et partager les publicités

## 🚀 Démarrage Rapide

### 1. Accéder à la Page de Test

1. Lancez l'application : `npm run dev`
2. Connectez-vous avec un compte
3. Allez sur la page d'accueil
4. Dans la section debug, cliquez sur **"Restrictions Pub"**
5. Ou accédez directement à : `http://localhost:5173/ad-restrictions-test`

### 2. Tests Automatisés Interface

La page de test permet de :
- 🔄 Simuler différents rôles utilisateur
- 📊 Voir les permissions en temps réel
- 🧪 Tester les interactions avec une publicité
- 📝 Suivre les instructions de test

## 🧪 Scénarios de Test

### Scénario 1 : Utilisateur Standard ✅

**Étapes :**
1. Sélectionnez "Utilisateur Standard" dans le simulateur
2. Vérifiez que toutes les permissions sont vertes
3. Cliquez sur "Commenter" dans la publicité de test
4. Écrivez un commentaire et publiez

**Résultat attendu :**
- ✅ Bouton commentaire actif
- ✅ Formulaire de commentaire visible
- ✅ Commentaire publié avec succès

### Scénario 2 : Compte Entreprise 🚫

**Étapes :**
1. Sélectionnez "Compte Entreprise" dans le simulateur
2. Vérifiez que les commentaires sont bloqués (rouge)
3. Cliquez sur "Commenter" dans la publicité de test
4. Observez le message d'information

**Résultat attendu :**
- 🚫 Bouton commentaire désactivé (opacité réduite)
- 📝 Message d'information affiché
- 🔒 Aucun formulaire de commentaire
- ✅ Likes et partages toujours disponibles

### Scénario 3 : Administrateur ✅

**Étapes :**
1. Sélectionnez "Administrateur" dans le simulateur
2. Vérifiez que toutes les permissions sont vertes
3. Testez toutes les interactions

**Résultat attendu :**
- ✅ Toutes les fonctionnalités disponibles
- ✅ Aucune restriction

## 🗄️ Tests Base de Données

### Exécuter les Tests SQL

1. Ouvrez Supabase Dashboard
2. Allez dans l'éditeur SQL
3. Exécutez le script : `scripts/test-ad-restrictions.sql`

### Tests Inclus

- ✅ Vérification des tables et structures
- ✅ Test des fonctions et triggers
- ✅ Validation des politiques RLS
- ✅ Tests d'insertion avec différents rôles
- ✅ Vérification de la vue filtrée

## 🔍 Tests Manuels Détaillés

### Test 1 : Interface Utilisateur

**Avec un compte entreprise :**
1. Connectez-vous avec un compte business
2. Allez sur la page d'accueil
3. Trouvez une publicité dans le fil
4. Tentez de cliquer sur "Commenter"

**Vérifications :**
- [ ] Bouton grisé/désactivé
- [ ] Tooltip explicatif au survol
- [ ] Message d'information si formulaire ouvert
- [ ] Likes et partages fonctionnels

### Test 2 : API Backend

**Test avec Postman/curl :**
```bash
# Tentative de commentaire avec un compte entreprise
curl -X POST 'your-supabase-url/rest/v1/ad_comments' \
  -H 'Authorization: Bearer YOUR_BUSINESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "campaign_id": "test-campaign-id",
    "user_id": "business-user-id", 
    "content": "Test comment"
  }'
```

**Résultat attendu :** Erreur 403 ou message de restriction

### Test 3 : Contournement de Sécurité

**Tentatives de contournement :**
1. Modification du rôle côté client (DevTools)
2. Appel direct à l'API
3. Injection SQL (si applicable)

**Résultat attendu :** Toutes les tentatives doivent échouer

## 📊 Métriques à Surveiller

### Logs d'Application
```javascript
// Rechercher ces messages dans la console
"🚫 Tentative de commentaire bloquée: l'utilisateur XXX est une entreprise"
"💬 Commentaire ajouté pour la campagne XXX par l'utilisateur XXX"
```

### Base de Données
```sql
-- Vérifier les violations enregistrées
SELECT * FROM ad_comment_violations 
WHERE created_at > NOW() - INTERVAL '1 hour';

-- Compter les commentaires par rôle
SELECT p.role, COUNT(ac.id) as comment_count
FROM profiles p
LEFT JOIN ad_comments ac ON p.id = ac.user_id
GROUP BY p.role;
```

## 🐛 Dépannage

### Problème : Entreprise peut toujours commenter

**Solutions :**
1. Vérifier que la migration SQL a été appliquée
2. Contrôler les politiques RLS
3. Vérifier le rôle utilisateur en base
4. Redémarrer l'application

### Problème : Utilisateur standard bloqué

**Solutions :**
1. Vérifier le rôle en base de données
2. Contrôler les permissions côté client
3. Vérifier les logs d'erreur

### Problème : Interface incohérente

**Solutions :**
1. Vider le cache navigateur
2. Vérifier les imports TypeScript
3. Redémarrer le serveur de développement

## ✅ Checklist de Validation

### Tests Interface ✅
- [ ] Bouton commentaire désactivé pour entreprises
- [ ] Message d'information affiché
- [ ] Tooltip explicatif présent
- [ ] Likes/partages fonctionnels pour tous
- [ ] Formulaire commentaire masqué pour entreprises

### Tests Backend ✅
- [ ] Service bloque les commentaires d'entreprises
- [ ] Messages d'erreur appropriés
- [ ] Logs de sécurité enregistrés
- [ ] API retourne erreurs correctes

### Tests Base de Données ✅
- [ ] Trigger de validation actif
- [ ] Politiques RLS configurées
- [ ] Vue filtrée opérationnelle
- [ ] Contraintes respectées

### Tests Sécurité ✅
- [ ] Contournement côté client impossible
- [ ] Appels API directs bloqués
- [ ] Validation multi-niveaux active
- [ ] Audit trail fonctionnel

## 📞 Support

### En cas de problème :
1. Vérifiez les logs de la console navigateur
2. Consultez les logs Supabase
3. Exécutez le script de test SQL
4. Contactez l'équipe de développement

### Fichiers de référence :
- `src/services/adInteractionService.ts` - Logique métier
- `src/components/ads/FeedAdCard.tsx` - Interface utilisateur
- `src/utils/adPermissions.ts` - Utilitaires permissions
- `supabase/migrations/20250131000000_restrict_business_ad_comments.sql` - Migration

---

## 🎯 Conclusion

Les restrictions sont maintenant en place et testées pour :
- 🚫 Empêcher les entreprises de commenter les publicités
- ✅ Maintenir l'engagement via likes et partages
- 🛡️ Assurer la sécurité multi-niveaux
- 📱 Fournir une expérience utilisateur claire

**Système validé et prêt pour la production !** 🚀
