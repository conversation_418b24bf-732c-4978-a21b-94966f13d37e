import { IProduct, QualityBadge } from '../types';

// Données réelles des produits du marketplace
export const REAL_PRODUCTS: IProduct[] = [
  {
    id: '1',
    name: 'Smartphone UltraPhone X',
    description: 'Le dernier smartphone avec des fonctionnalités innovantes. Écran OLED 6.7", processeur octa-core, 128GB de stockage.',
    category: 'Électronique',
    price: 799.99,
    businessId: 'business1',
    averageRating: 4.5,
    qualityBadge: QualityBadge.GOLD,
    images: ['https://images.pexels.com/photos/1092644/pexels-photo-1092644.jpeg'],
    createdAt: new Date('2025-05-20').toISOString() as unknown as Date,
    negotiable: true,
    stock: 10
  },
  {
    id: '2',
    name: '<PERSON><PERSON> - <PERSON><PERSON> de beaut<PERSON>',
    description: 'Huile naturelle pour le soin de la peau et des cheveux. Fabriquée à partir d\'ingrédients 100% naturels de Côte d\'Ivoire.',
    category: '<PERSON><PERSON>',
    price: 15000,
    businessId: 'business2',
    averageRating: 5.0,
    qualityBadge: QualityBadge.PREMIUM,
    images: ['https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400&h=400&fit=crop'],
    createdAt: new Date('2025-05-18').toISOString() as unknown as Date,
    negotiable: false,
    stock: 50
  },
  {
    id: '3',
    name: 'Crème hydratante visage',
    description: 'Crème hydratante pour tous types de peau. Enrichie en vitamines et antioxydants. Texture légère et absorption rapide.',
    category: 'Beauté',
    price: 12500,
    businessId: 'business2',
    averageRating: 3.0,
    qualityBadge: QualityBadge.SILVER,
    images: ['https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?w=400&h=400&fit=crop'],
    createdAt: new Date('2025-05-15').toISOString() as unknown as Date,
    negotiable: false,
    stock: 35
  },
  {
    id: '5',
    name: 'Savon Naturel au Karité',
    description: 'Savon artisanal fabriqué à base de beurre de karité pur. Idéal pour les peaux sensibles. Sans produits chimiques.',
    category: 'Beauté',
    price: 5000,
    businessId: 'business4',
    averageRating: 4.2,
    qualityBadge: QualityBadge.VERIFIED,
    images: ['https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400&h=400&fit=crop'],
    createdAt: new Date('2025-05-12').toISOString() as unknown as Date,
    negotiable: true,
    stock: 45
  },
  {
    id: '6',
    name: 'Ordinateur Portable ProBook',
    description: 'Ordinateur portable performant pour professionnels et étudiants. Processeur Intel i7, 16GB RAM, SSD 512GB.',
    category: 'Électronique',
    price: 450000,
    businessId: 'business1',
    averageRating: 4.7,
    qualityBadge: QualityBadge.EXCELLENT,
    images: ['https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=400&fit=crop'],
    createdAt: new Date('2025-05-10').toISOString() as unknown as Date,
    negotiable: false,
    stock: 8
  }
];

// Interface pour les produits d'entreprise avec données supplémentaires
export interface BusinessProduct extends IProduct {
  isOnMarketplace: boolean;
  marketplaceStatus: 'active' | 'pending' | 'rejected' | 'draft';
  views: number;
  sales: number;
  revenue: number;
  lastUpdated: string;
}

// Fonction pour obtenir les produits d'une entreprise spécifique
export const getBusinessProducts = (businessId: string): BusinessProduct[] => {
  // Filtrer les produits par businessId et ajouter les données de gestion
  const businessProducts = REAL_PRODUCTS
    .filter(product => product.businessId === businessId)
    .map(product => {
      // Générer des données réalistes basées sur l'ID du produit
      const baseViews = parseInt(product.id) * 247;
      const baseSales = Math.floor(baseViews * 0.05); // 5% de conversion
      const revenue = baseSales * product.price;
      
      return {
        ...product,
        isOnMarketplace: true,
        marketplaceStatus: 'active' as const,
        views: baseViews + Math.floor(Math.random() * 100),
        sales: baseSales + Math.floor(Math.random() * 10),
        revenue: revenue,
        lastUpdated: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
      };
    });

  // Ajouter quelques produits supplémentaires en brouillon/attente pour cette entreprise
  const additionalProducts: BusinessProduct[] = [
    {
      id: `${businessId}-draft-1`,
      name: 'Nouveau Produit en Développement',
      description: 'Produit innovant en cours de finalisation. Bientôt disponible sur le marketplace.',
      category: 'Beauté',
      price: 18000,
      businessId: businessId,
      averageRating: 0,
      qualityBadge: QualityBadge.NONE,
      images: ['https://images.unsplash.com/photo-1556228720-195a672e8a03?w=400&h=400&fit=crop'],
      createdAt: new Date('2025-05-25').toISOString() as unknown as Date,
      negotiable: true,
      stock: 0,
      isOnMarketplace: false,
      marketplaceStatus: 'draft',
      views: 0,
      sales: 0,
      revenue: 0,
      lastUpdated: new Date('2025-05-25T09:15:00Z').toISOString()
    },
    {
      id: `${businessId}-pending-1`,
      name: 'Sérum Anti-âge Premium',
      description: 'Sérum anti-âge avec des ingrédients actifs naturels. En attente d\'approbation pour le marketplace.',
      category: 'Beauté',
      price: 25000,
      businessId: businessId,
      averageRating: 0,
      qualityBadge: QualityBadge.NONE,
      images: ['https://images.unsplash.com/photo-1556228720-195a672e8a03?w=400&h=400&fit=crop'],
      createdAt: new Date('2025-05-26').toISOString() as unknown as Date,
      negotiable: true,
      stock: 15,
      isOnMarketplace: false,
      marketplaceStatus: 'pending',
      views: 0,
      sales: 0,
      revenue: 0,
      lastUpdated: new Date('2025-05-26T16:45:00Z').toISOString()
    }
  ];

  return [...businessProducts, ...additionalProducts];
};

// Catégories disponibles
export const PRODUCT_CATEGORIES = [
  'Électronique',
  'Beauté',
  'Mode',
  'Maison',
  'Sport',
  'Auto',
  'Services'
];

// Fonction pour obtenir les statistiques d'une entreprise
export const getBusinessStats = (businessId: string) => {
  const products = getBusinessProducts(businessId);
  
  return {
    total: products.length,
    active: products.filter(p => p.marketplaceStatus === 'active').length,
    pending: products.filter(p => p.marketplaceStatus === 'pending').length,
    draft: products.filter(p => p.marketplaceStatus === 'draft').length,
    rejected: products.filter(p => p.marketplaceStatus === 'rejected').length,
    totalRevenue: products.reduce((sum, p) => sum + p.revenue, 0),
    totalSales: products.reduce((sum, p) => sum + p.sales, 0),
    totalViews: products.reduce((sum, p) => sum + p.views, 0)
  };
};
