import React, { useState, useEffect } from 'react';
import { useFollow } from '../../context/FollowContext';
import { useAuth } from '../../context/AuthContext';
import Avatar from '../ui/Avatar';
import Button from '../ui/Button';
import { Users, UserPlus, UserMinus, Star } from 'lucide-react';
import { Link } from 'react-router-dom';
import SubscriptionPlansModal from './SubscriptionPlansModal';

interface UserProfile {
  id: string;
  username: string;
  profile_picture?: string;
  subscription_level?: 'basic' | 'premium' | 'vip';
  role?: string;
  businessName?: string;
}

interface FollowersFollowingSectionProps {
  userId: string;
  username: string;
  isOwnProfile: boolean;
}

const FollowersFollowingSection: React.FC<FollowersFollowingSectionProps> = ({
  userId,
  username,
  isOwnProfile
}) => {
  const { getFollowersDetails, getFollowingDetails, followUser, unfollowUser, isFollowing } = useFollow();
  const { currentUser } = useAuth();
  
  const [activeTab, setActiveTab] = useState<'followers' | 'following'>('followers');
  const [followers, setFollowers] = useState<UserProfile[]>([]);
  const [following, setFollowing] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [followStatus, setFollowStatus] = useState<Record<string, boolean>>({});
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        if (activeTab === 'followers') {
          const followersData = await getFollowersDetails(userId);
          setFollowers(followersData);
          
          // Check if current user is following each follower
          if (currentUser) {
            const statusMap: Record<string, boolean> = {};
            for (const follower of followersData) {
              statusMap[follower.id] = await isFollowing(follower.id);
            }
            setFollowStatus(statusMap);
          }
        } else {
          const followingData = await getFollowingDetails(userId);
          setFollowing(followingData);
          
          // Check if current user is following each following
          if (currentUser) {
            const statusMap: Record<string, boolean> = {};
            for (const followedUser of followingData) {
              statusMap[followedUser.id] = await isFollowing(followedUser.id);
            }
            setFollowStatus(statusMap);
          }
        }
      } catch (error) {
        console.error(`Erreur lors de la récupération des ${activeTab}:`, error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [userId, activeTab, getFollowersDetails, getFollowingDetails, isFollowing, currentUser]);

  const handleFollowToggle = async (targetUserId: string) => {
    if (!currentUser) return;
    
    try {
      if (followStatus[targetUserId]) {
        await unfollowUser(targetUserId);
        setFollowStatus(prev => ({ ...prev, [targetUserId]: false }));
      } else {
        await followUser(targetUserId);
        setFollowStatus(prev => ({ ...prev, [targetUserId]: true }));
      }
    } catch (error) {
      console.error("Erreur lors de la modification de l'abonnement:", error);
    }
  };

  const handleSubscribe = (user: UserProfile) => {
    setSelectedUser(user);
    setShowSubscriptionModal(true);
  };

  const displayedUsers = activeTab === 'followers' ? followers : following;

  const getSubscriptionBadge = (level?: 'basic' | 'premium' | 'vip') => {
    if (!level || level === 'basic') return null;
    
    const colors = {
      premium: 'bg-purple-100 text-purple-800',
      vip: 'bg-amber-100 text-amber-800'
    };
    
    return (
      <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${colors[level]}`}>
        {level === 'premium' ? 'Premium' : 'VIP'}
      </span>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Réseau</h2>
        <div className="text-sm text-gray-500">
          <span className="mr-3">{followers.length} abonnés</span>
          <span>{following.length} abonnements</span>
        </div>
      </div>

      <div className="flex border-b mb-4">
        <button
          className={`py-2 px-4 font-medium flex items-center ${activeTab === 'followers' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
          onClick={() => setActiveTab('followers')}
        >
          <Users size={18} className="mr-2" />
          Abonnés
        </button>
        <button
          className={`py-2 px-4 font-medium flex items-center ${activeTab === 'following' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
          onClick={() => setActiveTab('following')}
        >
          <UserPlus size={18} className="mr-2" />
          Abonnements
        </button>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      ) : displayedUsers.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          {activeTab === 'followers' 
            ? `${isOwnProfile ? 'Vous n\'avez' : `${username} n'a`} pas encore d'abonnés.` 
            : `${isOwnProfile ? 'Vous ne suivez' : `${username} ne suit`} personne pour le moment.`}
        </div>
      ) : (
        <div className="space-y-4">
          {displayedUsers.map(user => (
            <div key={user.id} className="flex items-center justify-between">
              <Link to={`/profile/${user.id}`} className="flex items-center flex-1">
                <Avatar 
                  src={user.profile_picture} 
                  alt={user.role === 'business' && user.businessName ? user.businessName : user.username} 
                  size="md"
                  className="mr-3"
                />
                <div>
                  <div className="flex items-center">
                    <h3 className="font-medium text-gray-900">{user.role === 'business' && user.businessName ? user.businessName : user.username}</h3>
                    {getSubscriptionBadge(user.subscription_level)}
                  </div>
                </div>
              </Link>
              
              {currentUser && currentUser.id !== user.id && (
                <div className="flex space-x-2">
                  <Button
                    variant={followStatus[user.id] ? "outline" : "primary"}
                    size="sm"
                    onClick={() => handleFollowToggle(user.id)}
                    className={followStatus[user.id] ? "text-gray-700" : ""}
                    leftIcon={followStatus[user.id] ? <UserMinus size={16} /> : <UserPlus size={16} />}
                  >
                    {followStatus[user.id] ? 'Abonné' : 'Suivre'}
                  </Button>
                  
                  {followStatus[user.id] && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSubscribe(user)}
                      leftIcon={<Star size={16} className="text-amber-500" />}
                    >
                      Soutenir
                    </Button>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
      
      {displayedUsers.length > 0 && (
        <div className="mt-4 text-center">
          <Button 
            variant="link" 
            size="sm"
            className="text-blue-600"
          >
            Voir tous les {activeTab === 'followers' ? 'abonnés' : 'abonnements'}
          </Button>
        </div>
      )}
      
      {showSubscriptionModal && selectedUser && (
        <SubscriptionPlansModal
          isOpen={showSubscriptionModal}
          onClose={() => setShowSubscriptionModal(false)}
          targetId={selectedUser.id}
          targetType="user"
          targetName={selectedUser.username}
        />
      )}
    </div>
  );
};

export default FollowersFollowingSection;
