import React, { useMemo } from 'react';
import { ExternalLink } from 'lucide-react';
import { Link } from 'react-router-dom';
import Button from '../ui/Button';
import { IPost } from '../../types';
import { usePosts } from '../../context/PostsContext';
import { getAverageRatingForPost } from '../../lib/utils';
import TopRatedProductItem from './TopRatedProductItem';
import SidebarAdsCarousel from '../ads/SidebarAdsCarousel';

interface RightSidebarProps {
  onNavigateToTopProducts: () => void;
}

interface RankedPostForSidebar extends IPost {
  calculatedAverageRating: number;
  rank: number; // Overall rank
}

const RightSidebar: React.FC<RightSidebarProps> = ({ onNavigateToTopProducts }) => {
  const { posts, loading: postsLoading } = usePosts();

  const topRatedOverall = useMemo(() => {
    if (postsLoading || posts.length === 0) {
      return [];
    }
    const postsWithAvgRating = posts.map(post => ({
      ...post,
      calculatedAverageRating: getAverageRatingForPost(post).average,
    }));

    postsWithAvgRating.sort((a, b) => b.calculatedAverageRating - a.calculatedAverageRating);
    
    return postsWithAvgRating.slice(0, 3).map((post, index) => ({
      ...post,
      rank: index + 1, // Overall rank 1, 2, 3
    }));
  }, [posts, postsLoading]);

  return (
    <aside className="w-full md:w-80 lg:w-96 px-4 hidden lg:block sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto space-y-6 py-6">
      {/* Top Products */}
      <div className="bg-white p-4 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Produits Mieux Notés</h3>
        {postsLoading ? (
          <p className="text-sm text-gray-500">Chargement...</p>
        ) : topRatedOverall.length > 0 ? (
          <ul className="space-y-1">
            {topRatedOverall.map((product) => (
              <TopRatedProductItem 
                key={product.id} 
                post={product} 
                rank={product.rank} 
              />
            ))}
          </ul>
        ) : (
          <p className="text-sm text-gray-500">Aucun produit à afficher pour le moment.</p>
        )}
        <Button 
          variant="ghost" 
          size="sm" 
          className="mt-3 w-full justify-center text-blue-600 hover:text-blue-800" 
          rightIcon={<ExternalLink size={14} />}
          onClick={onNavigateToTopProducts}
        >
          Voir tous les classements
        </Button>
      </div>

      {/* Annonces publicitaires avec rotation automatique */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-sm font-semibold text-gray-900">Annonces</h3>
          <span className="text-xs text-gray-500">Sponsorisé</span>
        </div>
        <SidebarAdsCarousel />
      </div>

      {/* Suggested Topics (Placeholder) */}
      <div className="bg-white p-4 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Catégories Populaires</h3>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm">#Électronique</Button>
          <Button variant="outline" size="sm">#Maison</Button>
          <Button variant="outline" size="sm">#Mode</Button>
        </div>
      </div>
      
      {/* Footer Links */}
      <div className="text-xs text-gray-500 pt-4 border-t border-gray-200">
        <div className="flex flex-wrap gap-x-3 gap-y-1 mb-1">
          <Link to="/about" className="hover:underline hover:text-blue-600">À propos</Link>
          <Link to="/privacy" className="hover:underline hover:text-blue-600">Confidentialité</Link>
          <Link to="/terms" className="hover:underline hover:text-blue-600">Conditions</Link>
        </div>
        <p>© {new Date().getFullYear()} Customeroom.</p>
      </div>
    </aside>
  );
};

export default RightSidebar;
