import React, { useMemo } from 'react';
import { usePosts } from '../context/PostsContext';
import PostCard from '../components/posts/PostCard';
import { Search } from 'lucide-react';

interface SearchResultsPageProps {
  searchQuery: string;
}

const SearchResultsPage: React.FC<SearchResultsPageProps> = ({ searchQuery }) => {
  const { posts, loading } = usePosts();

  const filteredPosts = useMemo(() => {
    if (!searchQuery || posts.length === 0) {
      return [];
    }
    const lowerCaseQuery = searchQuery.toLowerCase();
    return posts.filter(post => 
      post.productName.toLowerCase().includes(lowerCaseQuery) ||
      post.businessName.toLowerCase().includes(lowerCaseQuery) ||
      post.description.toLowerCase().includes(lowerCaseQuery) ||
      (post.category && post.category.toLowerCase().includes(lowerCaseQuery)) ||
      (post.tags && post.tags.some(tag => tag.toLowerCase().includes(lowerCaseQuery)))
    );
  }, [posts, searchQuery]);

  if (loading) {
    return <div className="text-center py-10">Recherche en cours...</div>;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 flex items-center">
          <Search size={30} className="mr-3 text-blue-600" />
          Résultats de recherche pour: <span className="text-blue-600 ml-2">"{searchQuery}"</span>
        </h1>
        <p className="text-sm text-gray-500 mt-1">
          La recherche inclut les noms de produits, noms d'entreprises, descriptions, catégories et tags.
        </p>
      </div>

      {filteredPosts.length === 0 && !loading && (
        <div className="text-center py-10 bg-white p-6 rounded-lg shadow">
          <Search size={48} className="mx-auto text-gray-400 mb-4" />
          <p className="text-xl text-gray-700 font-semibold">Aucun résultat trouvé.</p>
          <p className="text-gray-500 mt-2">Essayez d'affiner vos termes de recherche.</p>
        </div>
      )}

      {filteredPosts.length > 0 && (
        <div className="space-y-6">
          {filteredPosts.map(post => (
            <PostCard key={post.id} post={post} />
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchResultsPage;
