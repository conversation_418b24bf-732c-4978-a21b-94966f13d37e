import React, { useState, useEffect } from 'react';
import {
  Crown,
  Gift,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  X
} from 'lucide-react';
import { supabase } from '../../lib/supabase';

interface SubscriptionStatus {
  planName: string;
  planType: 'trial' | 'monthly' | 'yearly';
  daysRemaining: number;
  isActive: boolean;
  isExpired: boolean;
  isExpiringSoon: boolean;
  isTrial: boolean;
}

interface SubscriptionStatusBadgeProps {
  businessId?: string;
  className?: string;
}

const SubscriptionStatusBadge: React.FC<SubscriptionStatusBadgeProps> = ({ 
  businessId, 
  className = '' 
}) => {
  const [status, setStatus] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSubscriptionStatus();
  }, [businessId]);

  const loadSubscriptionStatus = async () => {
    try {
      setLoading(true);

      const { data: { user } } = await supabase.auth.getUser();
      const targetBusinessId = businessId || user?.id;

      if (!targetBusinessId) return;

      // Charger l'abonnement actuel
      const { data: subscription, error } = await supabase
        .from('business_subscriptions')
        .select(`
          *,
          subscription_plans (
            name,
            plan_type
          )
        `)
        .eq('business_id', targetBusinessId)
        .eq('is_active', true)
        .single();

      if (error || !subscription) {
        setStatus(null);
        return;
      }

      const endDate = new Date(subscription.end_date);
      const now = new Date();
      const diffTime = endDate.getTime() - now.getTime();
      const daysRemaining = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      setStatus({
        planName: subscription.subscription_plans?.name || 'Plan inconnu',
        planType: subscription.subscription_plans?.plan_type || 'monthly',
        daysRemaining: Math.max(0, daysRemaining),
        isActive: subscription.is_active,
        isExpired: daysRemaining <= 0,
        isExpiringSoon: daysRemaining > 0 && daysRemaining <= 7,
        isTrial: subscription.is_trial
      });

    } catch (error) {
      console.error('Erreur lors du chargement du statut d\'abonnement:', error);
      setStatus(null);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={`animate-pulse bg-gray-200 rounded-full px-3 py-1 ${className}`}>
        <div className="h-4 w-20 bg-gray-300 rounded"></div>
      </div>
    );
  }

  if (!status) {
    return (
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-600 ${className}`}>
        <X size={14} className="mr-1" />
        Aucun abonnement
      </div>
    );
  }

  const getStatusConfig = () => {
    if (status.isExpired) {
      return {
        icon: <AlertTriangle size={14} />,
        text: 'Expiré',
        bgColor: 'bg-red-100',
        textColor: 'text-red-700',
        borderColor: 'border-red-200'
      };
    }

    if (status.isExpiringSoon) {
      return {
        icon: <Clock size={14} />,
        text: `${status.daysRemaining}j restant${status.daysRemaining > 1 ? 's' : ''}`,
        bgColor: 'bg-yellow-100',
        textColor: 'text-yellow-700',
        borderColor: 'border-yellow-200'
      };
    }

    if (status.isTrial) {
      return {
        icon: <Gift size={14} />,
        text: `Essai (${status.daysRemaining}j)`,
        bgColor: 'bg-green-100',
        textColor: 'text-green-700',
        borderColor: 'border-green-200'
      };
    }

    // Plan actif
    const planConfig = {
      trial: {
        icon: <Gift size={14} />,
        bgColor: 'bg-green-100',
        textColor: 'text-green-700',
        borderColor: 'border-green-200'
      },
      monthly: {
        icon: <Calendar size={14} />,
        bgColor: 'bg-blue-100',
        textColor: 'text-blue-700',
        borderColor: 'border-blue-200'
      },
      yearly: {
        icon: <Crown size={14} />,
        bgColor: 'bg-purple-100',
        textColor: 'text-purple-700',
        borderColor: 'border-purple-200'
      }
    };

    const config = planConfig[status.planType] || planConfig.monthly;

    return {
      ...config,
      text: status.planType === 'yearly' ? 'Premium Annuel' : 
            status.planType === 'monthly' ? 'Premium' : 
            status.planName
    };
  };

  const config = getStatusConfig();

  return (
    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${config.bgColor} ${config.textColor} ${config.borderColor} ${className}`}>
      {config.icon}
      <span className="ml-1">{config.text}</span>
    </div>
  );
};

export default SubscriptionStatusBadge;
