import React, { useState, useEffect } from 'react';
import { useFollow } from '../../context/FollowContext';
import { Building2, MapPin, Calendar, Link, Users, UserPlus } from 'lucide-react';
import EditBusinessProfileForm from './EditBusinessProfileForm';
import { IBusinessUser } from '../../types';

interface BusinessHeaderProps {
  businessName: string;
  profilePicture: string;
  coverPhoto: string;
  location?: string;
  websiteUrl?: string;
  foundedDate?: string;
  businessCategory?: string;
  isOwnProfile: boolean;
  businessId: string;
  businessData: IBusinessUser;
  onProfileUpdate?: (updatedData: IBusinessUser) => void;
}

const BusinessHeader: React.FC<BusinessHeaderProps> = ({
  businessName,
  profilePicture,
  coverPhoto,
  location,
  websiteUrl,
  foundedDate,
  businessCategory,
  isOwnProfile,
  businessId,
  businessData,
  onProfileUpdate
}) => {
  const { isFollowing, followUser, unfollowUser } = useFollow();
  const [following, setFollowing] = useState<boolean>(false);
  const [showEditForm, setShowEditForm] = useState(false);
  
  useEffect(() => {
    if (businessId) {
      setFollowing(isFollowing(businessId));
    }
  }, [businessId, isFollowing]);

  const handleFollowToggle = () => {
    try {
      if (following) {
        unfollowUser(businessId);
        setFollowing(false);
      } else {
        followUser(businessId);
        setFollowing(true);
      }
    } catch (error) {
      console.error("Erreur lors de la modification de l'abonnement:", error);
    }
  };

  const handleEditProfile = () => {
    setShowEditForm(true);
  };

  const handleCloseEditForm = () => {
    setShowEditForm(false);
  };

  const handleProfileUpdate = (updatedData: IBusinessUser) => {
    if (onProfileUpdate) {
      onProfileUpdate(updatedData);
    }
  };

  return (
    <>
      <div className="business-header-container">
        {/* Cover Photo */}
        <div className="profile-cover-photo" style={{ backgroundImage: `url(${coverPhoto || '/default-business-cover.jpg'})` }}>
          <div className="profile-status">
            <span className="status-badge">Entreprise</span>
          </div>
        </div>
        
        {/* Profile Info Section */}
        <div className="profile-info-section">
          <div className="profile-avatar-container">
            <img 
              src={profilePicture || '/default-business-avatar.png'} 
              alt={`${businessName}`}
              className="profile-avatar business-avatar"
            />
          </div>
          
          <div className="profile-details">
            <div className="profile-name-section">
              <h1 className="profile-username">{businessName}</h1>
              {businessCategory && (
                <div className="business-category">
                  <Building2 size={16} className="business-icon" />
                  <span>{businessCategory}</span>
                </div>
              )}
            </div>
            
            <div className="profile-meta">
              {foundedDate && (
                <span className="business-founded">
                  <Calendar size={14} className="business-icon" />
                  Membre depuis {foundedDate}
                </span>
              )}
              {location && (
                <span className="location-info">
                  <MapPin size={14} className="business-icon" />
                  {location}
                </span>
              )}
              {websiteUrl && (
                <span className="business-website">
                  <Link size={14} className="business-icon" />
                  <a href={websiteUrl} target="_blank" rel="noopener noreferrer">
                    {websiteUrl.replace(/(^\w+:|^)\/\//, '')}
                  </a>
                </span>
              )}
            </div>
          </div>
          
          <div className="profile-actions">
            {isOwnProfile ? (
              <button className="edit-profile-button" onClick={handleEditProfile}>Éditer le profil</button>
            ) : (
              <button 
                onClick={handleFollowToggle}
                className={`follow-button ${following ? 'following' : ''}`}
              >
                {following ? 'Ne plus suivre' : 'Suivre'}
              </button>
            )}
          </div>
        </div>
      </div>
      
      {showEditForm && (
        <EditBusinessProfileForm 
          businessData={businessData}
          onClose={handleCloseEditForm}
          onUpdate={handleProfileUpdate}
        />
      )}
    </>
  );
};

export default BusinessHeader;
