-- Migration pour initialiser les statuts des utilisateurs existants
-- et s'assurer que tous les utilisateurs ont un statut valide

-- Mettre à jour la contrainte de statut pour inclure tous les nouveaux statuts
ALTER TABLE profiles 
DROP CONSTRAINT IF EXISTS valid_status;

ALTER TABLE profiles 
ADD CONSTRAINT valid_status 
CHECK (status IN ('newbie', 'member', 'contributor', 'discoverer', 'influencer', 'leader'));

-- Mettre à jour tous les utilisateurs existants qui ont un statut invalide ou NULL
UPDATE profiles 
SET status = 'newbie' 
WHERE status IS NULL 
   OR status NOT IN ('newbie', 'member', 'contributor', 'discoverer', 'influencer', 'leader');

-- Mettre à jour les utilisateurs selon leurs données existantes
-- Passer en MEMBER ceux qui ont un profil complet
UPDATE profiles 
SET status = 'member' 
WHERE status = 'newbie' 
  AND profile_picture IS NOT NULL 
  AND cover_photo_url IS NOT NULL 
  AND city IS NOT NULL 
  AND country IS NOT NULL;

-- Passer en CONTRIBUTOR ceux qui ont au moins 1 post ou commentaire
UPDATE profiles 
SET status = 'contributor' 
WHERE status IN ('newbie', 'member')
  AND (
    post_count > 0 
    OR comment_count > 0
    OR id IN (
      SELECT DISTINCT user_id 
      FROM posts 
      WHERE user_id = profiles.id
    )
    OR id IN (
      SELECT DISTINCT user_id 
      FROM comments 
      WHERE user_id = profiles.id
    )
  );

-- Fonction pour calculer les likes reçus par un utilisateur
CREATE OR REPLACE FUNCTION get_user_likes_received(user_id UUID)
RETURNS INTEGER AS $$
DECLARE
  total_likes INTEGER := 0;
BEGIN
  SELECT COALESCE(SUM(array_length(likes, 1)), 0)
  INTO total_likes
  FROM posts 
  WHERE user_id = $1 
    AND likes IS NOT NULL;
  
  RETURN total_likes;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour calculer l'âge du compte en jours
CREATE OR REPLACE FUNCTION get_account_age_days(user_id UUID)
RETURNS INTEGER AS $$
DECLARE
  age_days INTEGER := 0;
BEGIN
  SELECT EXTRACT(DAY FROM (NOW() - created_at))
  INTO age_days
  FROM profiles 
  WHERE id = $1;
  
  RETURN COALESCE(age_days, 0);
END;
$$ LANGUAGE plpgsql;

-- Passer en DISCOVERER ceux qui remplissent les critères avancés
UPDATE profiles 
SET status = 'discoverer' 
WHERE status IN ('newbie', 'member', 'contributor')
  AND post_count >= 10
  AND comment_count >= 25
  AND get_user_likes_received(id) >= 50
  AND followers_count >= 20
  AND get_account_age_days(id) >= 30;

-- Passer en INFLUENCER ceux qui remplissent les critères d'influence
UPDATE profiles 
SET status = 'influencer' 
WHERE status IN ('newbie', 'member', 'contributor', 'discoverer')
  AND post_count >= 50
  AND comment_count >= 100
  AND get_user_likes_received(id) >= 500
  AND followers_count >= 100
  AND recommendations_count >= 200
  AND get_account_age_days(id) >= 90;

-- Passer en LEADER ceux qui remplissent les critères de leadership
UPDATE profiles 
SET status = 'leader' 
WHERE status IN ('newbie', 'member', 'contributor', 'discoverer', 'influencer')
  AND post_count >= 200
  AND comment_count >= 500
  AND get_user_likes_received(id) >= 2000
  AND followers_count >= 500
  AND recommendations_count >= 1000
  AND get_account_age_days(id) >= 180;

-- Ajouter un index sur la colonne status pour améliorer les performances
CREATE INDEX IF NOT EXISTS profiles_status_idx ON profiles(status);

-- Ajouter une fonction trigger pour mettre à jour automatiquement le statut
CREATE OR REPLACE FUNCTION update_user_status_on_activity()
RETURNS TRIGGER AS $$
BEGIN
  -- Cette fonction sera appelée lors de mises à jour des métriques utilisateur
  -- Elle recalcule automatiquement le statut approprié
  
  DECLARE
    current_status TEXT;
    new_status TEXT := 'newbie';
    profile_complete BOOLEAN := FALSE;
    likes_received INTEGER := 0;
    account_age INTEGER := 0;
  BEGIN
    -- Récupérer le statut actuel
    SELECT status INTO current_status FROM profiles WHERE id = NEW.id;
    
    -- Vérifier si le profil est complet
    SELECT (profile_picture IS NOT NULL AND cover_photo_url IS NOT NULL AND city IS NOT NULL AND country IS NOT NULL)
    INTO profile_complete
    FROM profiles WHERE id = NEW.id;
    
    -- Calculer les métriques
    likes_received := get_user_likes_received(NEW.id);
    account_age := get_account_age_days(NEW.id);
    
    -- Déterminer le nouveau statut (progression uniquement)
    IF profile_complete AND 
       NEW.post_count >= 200 AND 
       NEW.comment_count >= 500 AND 
       likes_received >= 2000 AND 
       NEW.followers_count >= 500 AND 
       NEW.recommendations_count >= 1000 AND 
       account_age >= 180 THEN
      new_status := 'leader';
    ELSIF profile_complete AND 
          NEW.post_count >= 50 AND 
          NEW.comment_count >= 100 AND 
          likes_received >= 500 AND 
          NEW.followers_count >= 100 AND 
          NEW.recommendations_count >= 200 AND 
          account_age >= 90 THEN
      new_status := 'influencer';
    ELSIF profile_complete AND 
          NEW.post_count >= 10 AND 
          NEW.comment_count >= 25 AND 
          likes_received >= 50 AND 
          NEW.followers_count >= 20 AND 
          account_age >= 30 THEN
      new_status := 'discoverer';
    ELSIF profile_complete AND 
          (NEW.post_count >= 1 OR NEW.comment_count >= 1) THEN
      new_status := 'contributor';
    ELSIF profile_complete THEN
      new_status := 'member';
    ELSE
      new_status := 'newbie';
    END IF;
    
    -- Mettre à jour uniquement si c'est une progression
    IF new_status != current_status THEN
      CASE 
        WHEN current_status = 'newbie' THEN
          NEW.status := new_status;
        WHEN current_status = 'member' AND new_status IN ('contributor', 'discoverer', 'influencer', 'leader') THEN
          NEW.status := new_status;
        WHEN current_status = 'contributor' AND new_status IN ('discoverer', 'influencer', 'leader') THEN
          NEW.status := new_status;
        WHEN current_status = 'discoverer' AND new_status IN ('influencer', 'leader') THEN
          NEW.status := new_status;
        WHEN current_status = 'influencer' AND new_status = 'leader' THEN
          NEW.status := new_status;
        ELSE
          -- Pas de changement si ce n'est pas une progression
          NULL;
      END CASE;
    END IF;
    
    RETURN NEW;
  END;
END;
$$ LANGUAGE plpgsql;

-- Créer le trigger pour la mise à jour automatique du statut
DROP TRIGGER IF EXISTS trigger_update_user_status ON profiles;
CREATE TRIGGER trigger_update_user_status
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  WHEN (OLD.post_count IS DISTINCT FROM NEW.post_count OR 
        OLD.comment_count IS DISTINCT FROM NEW.comment_count OR 
        OLD.followers_count IS DISTINCT FROM NEW.followers_count OR 
        OLD.recommendations_count IS DISTINCT FROM NEW.recommendations_count OR
        OLD.profile_picture IS DISTINCT FROM NEW.profile_picture OR
        OLD.cover_photo_url IS DISTINCT FROM NEW.cover_photo_url OR
        OLD.city IS DISTINCT FROM NEW.city OR
        OLD.country IS DISTINCT FROM NEW.country)
  EXECUTE FUNCTION update_user_status_on_activity();

-- Afficher un résumé des statuts après la migration
DO $$
DECLARE
  newbie_count INTEGER;
  member_count INTEGER;
  contributor_count INTEGER;
  discoverer_count INTEGER;
  influencer_count INTEGER;
  leader_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO newbie_count FROM profiles WHERE status = 'newbie';
  SELECT COUNT(*) INTO member_count FROM profiles WHERE status = 'member';
  SELECT COUNT(*) INTO contributor_count FROM profiles WHERE status = 'contributor';
  SELECT COUNT(*) INTO discoverer_count FROM profiles WHERE status = 'discoverer';
  SELECT COUNT(*) INTO influencer_count FROM profiles WHERE status = 'influencer';
  SELECT COUNT(*) INTO leader_count FROM profiles WHERE status = 'leader';
  
  RAISE NOTICE 'Résumé des statuts utilisateur après migration:';
  RAISE NOTICE 'NEWBIE: %', newbie_count;
  RAISE NOTICE 'MEMBER: %', member_count;
  RAISE NOTICE 'CONTRIBUTOR: %', contributor_count;
  RAISE NOTICE 'DISCOVERER: %', discoverer_count;
  RAISE NOTICE 'INFLUENCER: %', influencer_count;
  RAISE NOTICE 'LEADER: %', leader_count;
END $$;
