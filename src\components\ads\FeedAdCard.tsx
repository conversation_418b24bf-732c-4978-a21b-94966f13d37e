import React, { useState, useEffect } from 'react';
import { ExternalLink, Heart, MessageCircle, Share2, Info, Send, Copy, AlertCircle, Facebook, Twitter, MessageSquare, Mail, Link, Download, Bookmark } from 'lucide-react';
import Button from '../ui/Button';
import { useToast } from '../ui/ToastContainer';
import '../../styles/FeedAdCard.css';
import { DEFAULT_IMAGES } from '../../constants/defaultImages';
import { adInteractionService } from '../../services/adInteractionService';
import { useAuth } from '../../context/AuthContext';

import { getAdPermissions } from '../../utils/adPermissions';

import AdCommentsSection from './AdCommentsSection';

// Type pour les publicités dans le fil d'actualité
export interface FeedAd {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  targetUrl: string;
  businessName: string;
  businessLogo: string;
  discount?: number;
}

interface FeedAdCardProps {
  ad: FeedAd;
  onClick?: (ad: FeedAd) => void;
}

const FeedAdCard: React.FC<FeedAdCardProps> = ({ ad, onClick }) => {
  const { currentUser } = useAuth();
  const { showSuccess, showError, showInfo } = useToast();
  const [isVisible, setIsVisible] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [logoError, setLogoError] = useState(false);
  const [imageError, setImageError] = useState(false);

  // États pour les interactions
  const [isLiked, setIsLiked] = useState(false);
  const [likesCount, setLikesCount] = useState(0);
  const [commentsCount, setCommentsCount] = useState(0);
  const [sharesCount, setSharesCount] = useState(0);
  const [showCommentForm, setShowCommentForm] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [showShareMenu, setShowShareMenu] = useState(false);
  const [loading, setLoading] = useState(false);

  // Charger les données d'engagement au montage
  useEffect(() => {
    loadEngagementData();
  }, [ad.id, currentUser]);

  // Animation d'entrée lors du montage du composant
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Charger les données d'engagement
  const loadEngagementData = async () => {
    if (!currentUser) {
      console.log('🔍 Pas d\'utilisateur connecté, skip du chargement des données d\'engagement');
      return;
    }

    console.log(`🔍 Chargement des données d'engagement pour la publicité ${ad.id} et l'utilisateur ${currentUser.id}`);

    try {
      // Vérifier si l'utilisateur a liké
      console.log('🔍 Vérification du like...');
      const hasLiked = await adInteractionService.hasUserLikedAd(ad.id, currentUser.id);
      console.log(`✅ Utilisateur a liké: ${hasLiked}`);
      setIsLiked(hasLiked);

      // Charger les métriques
      console.log('🔍 Chargement des métriques...');
      const metrics = await adInteractionService.getEngagementMetrics(ad.id);
      console.log('📊 Métriques reçues:', metrics);

      if (metrics) {
        setLikesCount(metrics.total_likes);
        setCommentsCount(metrics.total_comments);
        setSharesCount(metrics.total_shares);
        console.log(`✅ Métriques chargées: ${metrics.total_likes} likes, ${metrics.total_comments} commentaires, ${metrics.total_shares} partages`);
      } else {
        console.log('⚠️ Aucune métrique trouvée, utilisation des valeurs par défaut');
        // Initialiser les métriques si elles n'existent pas
        setLikesCount(0);
        setCommentsCount(0);
        setSharesCount(0);
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des données d\'engagement:', error);
      // En cas d'erreur, on garde les valeurs par défaut
      setLikesCount(0);
      setCommentsCount(0);
      setSharesCount(0);
    }
  };

  // Gestionnaire de like/unlike
  const handleLike = async (e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();

    console.log('🖱️ CLIC DÉTECTÉ sur le bouton Like');
    console.log('📊 État actuel:', { currentUser: !!currentUser, loading, isLiked, adId: ad.id });

    if (!currentUser || loading) {
      console.log('⚠️ Impossible de liker: utilisateur non connecté ou chargement en cours');
      alert('Vous devez être connecté pour aimer cette publicité');
      return;
    }

    console.log(`🔍 Tentative de ${isLiked ? 'unlike' : 'like'} pour la publicité ${ad.id}`);
    setLoading(true);

    try {
      if (isLiked) {
        console.log('👎 Suppression du like...');
        const success = await adInteractionService.unlikeAd(ad.id, currentUser.id);
        console.log(`✅ Résultat unlike: ${success}`);
        if (success) {
          setIsLiked(false);
          setLikesCount(prev => Math.max(0, prev - 1));
          console.log('✅ Interface mise à jour: like supprimé');
        }
      } else {
        console.log('👍 Ajout du like...');
        const success = await adInteractionService.likeAd(ad.id, currentUser.id);
        console.log(`✅ Résultat like: ${success}`);
        if (success) {
          setIsLiked(true);
          setLikesCount(prev => prev + 1);
          console.log('✅ Interface mise à jour: like ajouté');
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors du like/unlike:', error);
      alert(`Erreur: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // Obtenir les permissions de l'utilisateur pour les publicités
  const adPermissions = getAdPermissions(currentUser?.role);
  const { canComment, isBusiness, restrictions } = adPermissions;

  // Gestionnaire de clic sur le bouton commenter
  const handleCommentButtonClick = (e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();

    console.log('🖱️ CLIC DÉTECTÉ sur le bouton Commenter');
    console.log('📊 État actuel:', { currentUser: !!currentUser, canComment, isBusiness, restrictions });

    if (!currentUser) {
      console.log('⚠️ Impossible de commenter: utilisateur non connecté');
      alert('Vous devez être connecté pour commenter cette publicité');
      return;
    }

    if (!canComment) {
      console.log('⚠️ Impossible de commenter: permissions insuffisantes');
      alert(restrictions.comment || 'Vous n\'avez pas les permissions pour commenter');
      return;
    }

    console.log('✅ Ouverture du formulaire de commentaire');
    setShowCommentForm(!showCommentForm);
  };

  // Gestionnaire d'ajout de commentaire
  const handleAddComment = async () => {
    if (!currentUser || !commentText.trim() || loading || !canComment) return;

    setLoading(true);
    try {
      const comment = await adInteractionService.addComment(ad.id, currentUser.id, commentText.trim());
      if (comment) {
        setCommentText('');
        setShowCommentForm(false);
        setCommentsCount(prev => prev + 1);
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout du commentaire:', error);
      // Afficher le message d'erreur à l'utilisateur
      if (error instanceof Error) {
        alert(error.message);
      } else {
        alert('Erreur lors de l\'ajout du commentaire');
      }
    } finally {
      setLoading(false);
    }
  };

  // Gestionnaire de clic sur le bouton partager
  const handleShareButtonClick = (e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();

    console.log('🖱️ CLIC DÉTECTÉ sur le bouton Partager');
    console.log('📊 État actuel:', { currentUser: !!currentUser, showShareMenu });

    if (!currentUser) {
      console.log('⚠️ Impossible de partager: utilisateur non connecté');
      showError('Connexion requise', 'Vous devez être connecté pour partager cette publicité');
      return;
    }

    console.log('✅ Basculement du menu de partage');
    setShowShareMenu(!showShareMenu);
  };

  // Gestionnaire de partage amélioré
  const handleShare = async (platform: string, e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();

    console.log(`🖱️ CLIC DÉTECTÉ sur partage ${platform}`);
    console.log('📊 État actuel:', { currentUser: !!currentUser, platform, adId: ad.id, targetUrl: ad.targetUrl });

    if (!currentUser) {
      console.log('⚠️ Utilisateur non connecté pour le partage');
      showError('Connexion requise', 'Vous devez être connecté pour partager cette publicité');
      return;
    }

    try {
      let success = false;
      let message = '';

      switch (platform) {
        case 'copy_link':
          console.log('📋 Tentative de copie du lien...');
          success = await adInteractionService.copyLinkToClipboard(ad.id, currentUser.id, ad.targetUrl);
          message = success ? 'Lien copié dans le presse-papiers !' : 'Erreur lors de la copie du lien';
          break;

        case 'save':
          console.log('💾 Sauvegarde de la publicité...');
          success = await adInteractionService.shareAd(ad.id, currentUser.id, 'internal');
          message = success ? 'Publicité sauvegardée dans vos favoris !' : 'Erreur lors de la sauvegarde';
          break;

        case 'download':
          console.log('📥 Téléchargement de l\'image...');
          success = await downloadAdImage();
          message = success ? 'Image téléchargée avec succès !' : 'Erreur lors du téléchargement';
          break;

        case 'facebook':
        case 'twitter':
        case 'whatsapp':
        case 'email':
          console.log(`📤 Partage sur ${platform}...`);
          adInteractionService.shareToSocialMedia(ad.id, currentUser.id, platform, ad.title, ad.targetUrl);
          success = true;
          message = `Partage sur ${platform} effectué !`;
          break;

        default:
          console.error('Plateforme de partage non supportée:', platform);
          message = 'Plateforme de partage non supportée';
          break;
      }

      if (success) {
        setSharesCount(prev => prev + 1);
        console.log('✅ Partage effectué avec succès');

        // Afficher une notification de succès
        showShareNotification(message, 'success');
      } else {
        console.log('❌ Échec du partage');
        showShareNotification(message, 'error');
      }

      setShowShareMenu(false);
    } catch (error) {
      console.error('❌ Erreur lors du partage:', error);
      showShareNotification('Erreur lors du partage', 'error');
      setShowShareMenu(false);
    }
  };

  // Fonction pour télécharger l'image de la publicité
  const downloadAdImage = async (): Promise<boolean> => {
    try {
      if (!ad.imageUrl) return false;

      const response = await fetch(ad.imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `publicite-${ad.businessName}-${ad.id}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      window.URL.revokeObjectURL(url);

      // Enregistrer l'action de téléchargement
      await adInteractionService.shareAd(ad.id, currentUser!.id, 'internal');

      return true;
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error);
      return false;
    }
  };

  // Fonction pour afficher les notifications de partage
  const showShareNotification = (message: string, type: 'success' | 'error') => {
    if (type === 'success') {
      showSuccess('Partage réussi', message);
    } else {
      showError('Erreur de partage', message);
    }
  };

  // Gestion du survol sur l'icône d'information
  const handleInfoMouseEnter = () => {
    setShowTooltip(true);
  };

  const handleInfoMouseLeave = () => {
    setShowTooltip(false);
  };

  // Gestionnaires d'erreurs d'images
  const handleLogoError = () => {
    setLogoError(true);
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div className={`feed-ad-card ${isVisible ? 'visible' : ''}`}>
      <div className="feed-ad-header">
        <div className="feed-ad-business-info">
          <img
            src={logoError ? DEFAULT_IMAGES.BUSINESS : ad.businessLogo}
            alt={ad.businessName}
            className="feed-ad-business-logo pulse-animation"
            onError={handleLogoError}
          />
          <div>
            <h3 className="feed-ad-business-name">{ad.businessName}</h3>
            <div className="feed-ad-sponsored-container">
              <span className="feed-ad-sponsored-label">Sponsorisé</span>
              <div
                className="feed-ad-info-icon"
                onMouseEnter={handleInfoMouseEnter}
                onMouseLeave={handleInfoMouseLeave}
              >
                <Info size={14} />
                {showTooltip && (
                  <div className="feed-ad-tooltip">
                    Cette publication est une annonce publicitaire payante. Elle a été ciblée en fonction de vos centres d'intérêt.
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="feed-ad-content">
        <h2 className="feed-ad-title">{ad.title}</h2>
        <p className="feed-ad-description">{ad.description}</p>
        {ad.discount && (
          <div className="feed-ad-discount-badge shine-effect">
            -{ad.discount}%
          </div>
        )}
      </div>

      <div className="feed-ad-image-container">
        <img
          src={imageError ? DEFAULT_IMAGES.PROMO : ad.imageUrl}
          alt={ad.title}
          className="feed-ad-image zoom-effect"
          loading="lazy"
          onError={handleImageError}
        />
        {ad.discount && (
          <div className="feed-ad-promo-banner">
            <span className="feed-ad-promo-text">Offre limitée</span>
          </div>
        )}
      </div>

      <div className="feed-ad-footer">
        <Button
          variant="primary"
          size="sm"
          rightIcon={<ExternalLink size={16} />}
          onClick={() => onClick ? onClick(ad) : window.open(ad.targetUrl, '_blank')}
          className="feed-ad-cta-button"
        >
          En savoir plus
        </Button>

        <div className="feed-ad-engagement">
          <button
            className={`feed-ad-engagement-button ${isLiked ? 'liked' : ''}`}
            onClick={handleLike}
            disabled={!currentUser || loading}
            type="button"

          >
            <Heart size={18} fill={isLiked ? '#ef4444' : 'none'} color={isLiked ? '#ef4444' : 'currentColor'} />
            <span>{likesCount > 0 ? `${likesCount} J'aime` : "J'aime"}</span>
          </button>

          <button
            className={`feed-ad-engagement-button ${!canComment ? 'opacity-50 cursor-not-allowed' : ''}`}
            onClick={handleCommentButtonClick}
            disabled={!currentUser || !canComment}
            title={restrictions.comment || ''}
            type="button"

          >
            <MessageCircle size={18} />
            <span>{commentsCount > 0 ? `${commentsCount} Commentaires` : 'Commenter'}</span>
          </button>

          <div className="relative">
            <button
              className="feed-ad-engagement-button"
              onClick={handleShareButtonClick}
              disabled={!currentUser}
              type="button"

            >
              <Share2 size={18} />
              <span>{sharesCount > 0 ? `${sharesCount} Partages` : 'Partager'}</span>
            </button>

            {/* Menu de partage amélioré */}
            {showShareMenu && (
              <div className="absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-xl shadow-xl p-3 z-20 min-w-[280px]">
                {/* En-tête du menu */}
                <div className="flex items-center justify-between mb-3 pb-2 border-b border-gray-100">
                  <h4 className="font-semibold text-gray-800 text-sm">Partager cette publicité</h4>
                  <button
                    onClick={() => setShowShareMenu(false)}
                    className="text-gray-400 hover:text-gray-600 p-1"
                    type="button"
                  >
                    ✕
                  </button>
                </div>

                {/* Options de partage principales */}
                <div className="space-y-1 mb-3">
                  <button
                    onClick={(e) => handleShare('facebook', e)}
                    className="w-full text-left px-3 py-2.5 hover:bg-blue-50 rounded-lg flex items-center gap-3 transition-colors"
                    type="button"
                  >
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                      <Facebook size={16} className="text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">Facebook</div>
                      <div className="text-xs text-gray-500">Partager sur votre timeline</div>
                    </div>
                  </button>

                  <button
                    onClick={(e) => handleShare('twitter', e)}
                    className="w-full text-left px-3 py-2.5 hover:bg-blue-50 rounded-lg flex items-center gap-3 transition-colors"
                    type="button"
                  >
                    <div className="w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center">
                      <Twitter size={16} className="text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">Twitter</div>
                      <div className="text-xs text-gray-500">Tweeter à vos followers</div>
                    </div>
                  </button>

                  <button
                    onClick={(e) => handleShare('whatsapp', e)}
                    className="w-full text-left px-3 py-2.5 hover:bg-green-50 rounded-lg flex items-center gap-3 transition-colors"
                    type="button"
                  >
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <MessageSquare size={16} className="text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">WhatsApp</div>
                      <div className="text-xs text-gray-500">Envoyer à vos contacts</div>
                    </div>
                  </button>

                  <button
                    onClick={(e) => handleShare('email', e)}
                    className="w-full text-left px-3 py-2.5 hover:bg-gray-50 rounded-lg flex items-center gap-3 transition-colors"
                    type="button"
                  >
                    <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                      <Mail size={16} className="text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">Email</div>
                      <div className="text-xs text-gray-500">Envoyer par email</div>
                    </div>
                  </button>
                </div>

                {/* Séparateur */}
                <div className="border-t border-gray-100 my-3"></div>

                {/* Actions supplémentaires */}
                <div className="space-y-1">
                  <button
                    onClick={(e) => handleShare('copy_link', e)}
                    className="w-full text-left px-3 py-2.5 hover:bg-gray-50 rounded-lg flex items-center gap-3 transition-colors"
                    type="button"
                  >
                    <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center">
                      <Link size={16} className="text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">Copier le lien</div>
                      <div className="text-xs text-gray-500">Copier dans le presse-papiers</div>
                    </div>
                  </button>

                  <button
                    onClick={(e) => handleShare('save', e)}
                    className="w-full text-left px-3 py-2.5 hover:bg-yellow-50 rounded-lg flex items-center gap-3 transition-colors"
                    type="button"
                  >
                    <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                      <Bookmark size={16} className="text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">Sauvegarder</div>
                      <div className="text-xs text-gray-500">Ajouter à vos favoris</div>
                    </div>
                  </button>

                  <button
                    onClick={(e) => handleShare('download', e)}
                    className="w-full text-left px-3 py-2.5 hover:bg-purple-50 rounded-lg flex items-center gap-3 transition-colors"
                    type="button"
                  >
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                      <Download size={16} className="text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">Télécharger</div>
                      <div className="text-xs text-gray-500">Sauvegarder l'image</div>
                    </div>
                  </button>
                </div>

                {/* Statistiques de partage */}
                <div className="mt-3 pt-2 border-t border-gray-100">
                  <div className="text-xs text-gray-500 text-center">
                    {sharesCount > 0 ? `Partagé ${sharesCount} fois` : 'Soyez le premier à partager'}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Message d'information pour les entreprises */}
        {isBusiness && showCommentForm && (
          <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertCircle size={16} className="text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-amber-800">
                <p className="font-medium">Restriction pour les comptes entreprise</p>
                <p className="mt-1">{restrictions.comment}</p>
              </div>
            </div>
          </div>
        )}

        {/* Formulaire de commentaire */}
        {showCommentForm && currentUser && canComment && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <div className="flex gap-2">
              <textarea
                value={commentText}
                onChange={(e) => setCommentText(e.target.value)}
                placeholder="Écrivez votre commentaire..."
                className="flex-1 p-2 border border-gray-300 rounded-lg resize-none"
                rows={2}
                maxLength={1000}
              />
              <button
                onClick={handleAddComment}
                disabled={!commentText.trim() || loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
              >
                <Send size={16} />
                {loading ? '...' : 'Publier'}
              </button>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {commentText.length}/1000 caractères
            </div>
          </div>
        )}



        {/* Section des commentaires avec réponses des entreprises */}
        <AdCommentsSection
          campaignId={ad.id}
          commentsCount={commentsCount}
          onCommentsCountChange={setCommentsCount}
        />


      </div>
    </div>
  );
};

export default FeedAdCard;
