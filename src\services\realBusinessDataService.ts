import { supabase } from '../lib/supabase';

/**
 * Service pour récupérer les vraies données business depuis Supabase
 * Remplace les données simulées par des données réelles
 */
export class RealBusinessDataService {

  /**
   * Récupère tous les posts/avis liés à une entreprise
   * Inclut les posts de l'entreprise (demandes d'avis) et les posts des utilisateurs sur l'entreprise
   */
  static async getBusinessPosts(businessId: string) {
    try {
      console.log('🔍 Récupération des posts pour businessId:', businessId);

      // Pour Dexima, récupérer tous les posts liés à cette entreprise
      const businessName = 'Dexima'; // Nom de l'entreprise

      // 1. Posts des utilisateurs mentionnant l'entreprise (coup de cœur/coup de gueule)
      const { data: userPosts, error: userPostsError } = await supabase
        .from('posts_with_author_details')
        .select('*')
        .eq('business_name', businessName)
        .order('created_at', { ascending: false });

      if (userPostsError) {
        console.error('❌ Erreur lors de la récupération des posts utilisateurs:', userPostsError);
      }

      // 2. Posts de l'entreprise elle-même (demandes d'avis)
      // Récupérer l'ID utilisateur de l'entreprise Dexima
      const { data: businessUser, error: businessUserError } = await supabase
        .from('profiles')
        .select('id')
        .eq('username', 'dexima')
        .single();

      let businessPosts: any[] = [];
      if (businessUser && !businessUserError) {
        const { data: companyPosts, error: companyPostsError } = await supabase
          .from('posts_with_author_details')
          .select('*')
          .eq('user_id', businessUser.id)
          .order('created_at', { ascending: false });

        if (!companyPostsError && companyPosts) {
          businessPosts = companyPosts;
        }
      }

      // Combiner tous les posts et supprimer les doublons
      const allPosts = [...(userPosts || []), ...businessPosts];
      const uniquePosts = allPosts.filter((post, index, self) =>
        index === self.findIndex(p => p.id === post.id)
      );

      console.log('✅ Posts récupérés:', {
        userPosts: userPosts?.length || 0,
        businessPosts: businessPosts.length,
        total: uniquePosts.length,
        businessName
      });

      return uniquePosts;
    } catch (error) {
      console.error('❌ Erreur dans getBusinessPosts:', error);
      return [];
    }
  }

  /**
   * Récupère les produits d'une entreprise avec leurs commentaires et notes
   */
  static async getBusinessProducts(businessId: string) {
    try {
      console.log('🔍 Récupération des produits pour businessId:', businessId);

      // Récupérer les produits
      const { data: products, error: productsError } = await supabase
        .from('products')
        .select('*')
        .eq('business_id', businessId)
        .order('created_at', { ascending: false });

      if (productsError) {
        console.error('❌ Erreur lors de la récupération des produits:', productsError);
        throw productsError;
      }

      if (!products || products.length === 0) {
        console.log('ℹ️ Aucun produit trouvé pour cette entreprise');
        return [];
      }

      // Pour chaque produit, récupérer ses commentaires et notes
      const productsWithDetails = await Promise.all(
        products.map(async (product) => {
          // Récupérer les commentaires (sans jointure pour éviter l'erreur 400)
          const { data: comments, error: commentsError } = await supabase
            .from('marketcomen')
            .select('*')
            .eq('product_id', product.id)
            .eq('is_approved', true)
            .order('created_at', { ascending: false });

          // Récupérer les notes
          const { data: ratings, error: ratingsError } = await supabase
            .from('marketrating')
            .select('*')
            .eq('product_id', product.id)
            .order('created_at', { ascending: false });

          if (commentsError) {
            console.warn('⚠️ Erreur lors de la récupération des commentaires:', commentsError);
          }
          if (ratingsError) {
            console.warn('⚠️ Erreur lors de la récupération des notes:', ratingsError);
          }

          return {
            ...product,
            marketcomments: comments || [],
            marketrating: ratings || []
          };
        })
      );

      console.log('✅ Produits avec détails récupérés:', productsWithDetails.length);
      return productsWithDetails;
    } catch (error) {
      console.error('❌ Erreur dans getBusinessProducts:', error);
      return [];
    }
  }

  /**
   * Récupère les notes/ratings et interactions pour une entreprise
   */
  static async getBusinessRatings(businessId: string) {
    try {
      console.log('🔍 Récupération des ratings pour businessId:', businessId);

      const businessName = 'Dexima';

      // Récupérer les ratings des posts mentionnant l'entreprise
      const { data: postRatings, error: postError } = await supabase
        .from('posts_with_author_details')
        .select('rating, created_at, user_id, category, type, likes, recommendations, shares')
        .eq('business_name', businessName)
        .not('rating', 'is', null)
        .order('created_at', { ascending: false });

      if (postError) {
        console.error('❌ Erreur lors de la récupération des ratings de posts:', postError);
      }

      // Récupérer les posts de l'entreprise avec leurs interactions
      const { data: businessUser } = await supabase
        .from('profiles')
        .select('id')
        .eq('username', 'dexima')
        .single();

      let businessPostRatings: any[] = [];
      if (businessUser) {
        const { data: companyPostRatings, error: companyError } = await supabase
          .from('posts_with_author_details')
          .select('rating, created_at, user_id, category, type, likes, recommendations, shares, comments')
          .eq('user_id', businessUser.id)
          .order('created_at', { ascending: false });

        if (!companyError && companyPostRatings) {
          businessPostRatings = companyPostRatings;
        }
      }

      // Récupérer les ratings des produits marketplace
      const { data: products } = await supabase
        .from('products')
        .select('id')
        .eq('business_id', businessId);

      let marketRatings: any[] = [];
      if (products && products.length > 0) {
        const productIds = products.map(p => p.id);

        const { data: marketRatingsData, error: marketError } = await supabase
          .from('marketrating')
          .select('rating, created_at, user_id, product_id')
          .in('product_id', productIds)
          .order('created_at', { ascending: false });

        if (marketError) {
          console.warn('⚠️ Erreur lors de la récupération des ratings marketplace:', marketError);
        } else {
          marketRatings = marketRatingsData || [];
        }
      }

      // Combiner tous les ratings et interactions
      const allRatings = [
        ...(postRatings || []).map(r => ({
          ...r,
          source: 'user_post',
          likesCount: Array.isArray(r.likes) ? r.likes.length : 0,
          recommendationsCount: Array.isArray(r.recommendations) ? r.recommendations.length : 0,
          sharesCount: Array.isArray(r.shares) ? r.shares.length : 0
        })),
        ...businessPostRatings.map(r => ({
          ...r,
          source: 'business_post',
          likesCount: Array.isArray(r.likes) ? r.likes.length : 0,
          recommendationsCount: Array.isArray(r.recommendations) ? r.recommendations.length : 0,
          sharesCount: Array.isArray(r.shares) ? r.shares.length : 0,
          commentsCount: Array.isArray(r.comments) ? r.comments.length : 0
        })),
        ...marketRatings.map(r => ({
          rating: r.rating,
          created_at: r.created_at,
          user_id: r.user_id,
          category: 'Marketplace',
          source: 'marketplace',
          likesCount: 0,
          recommendationsCount: 0,
          sharesCount: 0
        }))
      ];

      console.log('✅ Ratings et interactions récupérés:', {
        userPosts: postRatings?.length || 0,
        businessPosts: businessPostRatings.length,
        marketRatings: marketRatings.length,
        total: allRatings.length
      });

      return allRatings;
    } catch (error) {
      console.error('❌ Erreur dans getBusinessRatings:', error);
      return [];
    }
  }

  /**
   * Récupère les statistiques générales d'une entreprise
   */
  static async getBusinessStats(businessId: string) {
    try {
      console.log('🔍 Récupération des stats pour businessId:', businessId);

      // Récupérer le profil business
      const { data: businessProfile, error: profileError } = await supabase
        .from('business_profiles')
        .select('*')
        .eq('id', businessId)
        .single();

      if (profileError) {
        console.warn('⚠️ Erreur lors de la récupération du profil business:', profileError);
      }

      // Compter les posts
      const { count: postsCount, error: postsCountError } = await supabase
        .from('posts')
        .select('*', { count: 'exact', head: true })
        .eq('business_name', businessProfile?.business_name || '');

      // Compter les produits
      const { count: productsCount, error: productsCountError } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('business_id', businessId);

      // Récupérer les followers (si table existe)
      const { count: followersCount } = await supabase
        .from('followers')
        .select('*', { count: 'exact', head: true })
        .eq('following_id', businessId);

      const stats = {
        businessProfile: businessProfile || null,
        postsCount: postsCount || 0,
        productsCount: productsCount || 0,
        followersCount: followersCount || 0,
        salesCount: businessProfile?.sales_count || 0,
        wallet: businessProfile?.wallet || 0
      };

      console.log('✅ Stats récupérées:', stats);
      return stats;
    } catch (error) {
      console.error('❌ Erreur dans getBusinessStats:', error);
      return {
        businessProfile: null,
        postsCount: 0,
        productsCount: 0,
        followersCount: 0,
        salesCount: 0,
        wallet: 0
      };
    }
  }

  /**
   * Récupère les données complètes pour l'analyse IA
   */
  static async getCompleteBusinessData(businessId: string) {
    try {
      console.log('🤖 Récupération des données complètes pour l\'IA - businessId:', businessId);

      const [posts, products, ratings, stats] = await Promise.all([
        this.getBusinessPosts(businessId),
        this.getBusinessProducts(businessId),
        this.getBusinessRatings(businessId),
        this.getBusinessStats(businessId)
      ]);

      const completeData = {
        posts,
        products,
        ratings,
        stats,
        metadata: {
          totalDataPoints: posts.length + products.length + ratings.length,
          lastUpdated: new Date().toISOString(),
          businessId
        }
      };

      console.log('✅ Données complètes récupérées:', {
        posts: posts.length,
        products: products.length,
        ratings: ratings.length,
        totalDataPoints: completeData.metadata.totalDataPoints
      });

      return completeData;
    } catch (error) {
      console.error('❌ Erreur dans getCompleteBusinessData:', error);
      return {
        posts: [],
        products: [],
        ratings: [],
        stats: {
          businessProfile: null,
          postsCount: 0,
          productsCount: 0,
          followersCount: 0,
          salesCount: 0,
          wallet: 0
        },
        metadata: {
          totalDataPoints: 0,
          lastUpdated: new Date().toISOString(),
          businessId,
          error: 'Failed to fetch data'
        }
      };
    }
  }

  /**
   * Vérifie si une entreprise a suffisamment de données pour l'analyse IA
   */
  static async hasEnoughDataForAnalysis(businessId: string): Promise<{
    hasEnoughData: boolean;
    dataQuality: 'excellent' | 'good' | 'fair' | 'poor';
    recommendations: string[];
    dataPoints: {
      posts: number;
      products: number;
      ratings: number;
      total: number;
    };
  }> {
    try {
      const data = await this.getCompleteBusinessData(businessId);

      const dataPoints = {
        posts: data.posts.length,
        products: data.products.length,
        ratings: data.ratings.length,
        total: data.metadata.totalDataPoints
      };

      let dataQuality: 'excellent' | 'good' | 'fair' | 'poor' = 'poor';
      let hasEnoughData = false;
      const recommendations: string[] = [];

      // Évaluer la qualité des données
      if (dataPoints.total >= 50) {
        dataQuality = 'excellent';
        hasEnoughData = true;
      } else if (dataPoints.total >= 20) {
        dataQuality = 'good';
        hasEnoughData = true;
      } else if (dataPoints.total >= 10) {
        dataQuality = 'fair';
        hasEnoughData = true;
        recommendations.push('Encouragez plus de clients à laisser des avis pour améliorer la précision des recommandations');
      } else {
        dataQuality = 'poor';
        hasEnoughData = false;
        recommendations.push('Vous avez besoin de plus de données pour des recommandations précises');
        recommendations.push('Ajoutez plus de produits à votre catalogue');
        recommendations.push('Encouragez vos clients à laisser des avis');
      }

      // Recommandations spécifiques
      if (dataPoints.posts < 5) {
        recommendations.push('Encouragez plus d\'avis clients sur vos produits');
      }
      if (dataPoints.products < 3) {
        recommendations.push('Ajoutez plus de produits à votre catalogue marketplace');
      }
      if (dataPoints.ratings < 10) {
        recommendations.push('Collectez plus de notes de satisfaction client');
      }

      return {
        hasEnoughData,
        dataQuality,
        recommendations,
        dataPoints
      };
    } catch (error) {
      console.error('❌ Erreur dans hasEnoughDataForAnalysis:', error);
      return {
        hasEnoughData: false,
        dataQuality: 'poor',
        recommendations: ['Erreur lors de l\'évaluation des données'],
        dataPoints: { posts: 0, products: 0, ratings: 0, total: 0 }
      };
    }
  }
}
