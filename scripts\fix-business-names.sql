-- SCRIPT POUR CORRIGER LES NOMS D'ENTREPRISE
-- Exécutez ce script dans Supabase SQL Editor pour vérifier et corriger les noms

-- =====================================================
-- 1. VÉRIFIER LES DONNÉES ACTUELLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 Vérification des noms d''entreprise...';
    
    -- Afficher les profils avec leurs noms
    RAISE NOTICE '';
    RAISE NOTICE '📋 PROFILS ACTUELS:';
    
    -- <PERSON><PERSON> requête va afficher les données pour diagnostic
    PERFORM 1; -- Placeholder pour la structure
    
END $$;

-- Requête pour voir les profils actuels
SELECT 
    p.id,
    p.username,
    p.full_name,
    p.business_name as profile_business_name,
    bp.business_name as bp_business_name,
    bp.company_name as bp_company_name,
    bp.name as bp_name
FROM profiles p
LEFT JOIN business_profiles bp ON p.id = bp.id
WHERE p.user_type = 'business'
ORDER BY p.created_at DESC;

-- =====================================================
-- 2. VÉRIFIER LES CODES D'ABONNEMENT EXISTANTS
-- =====================================================

-- Voir les codes avec les noms actuels
SELECT 
    sc.code,
    sc.business_name as code_business_name,
    p.username,
    p.business_name as profile_business_name,
    bp.business_name as bp_business_name,
    bp.company_name as bp_company_name
FROM subscription_codes sc
LEFT JOIN profiles p ON sc.business_id = p.id
LEFT JOIN business_profiles bp ON sc.business_id = bp.id
ORDER BY sc.generated_at DESC;

-- =====================================================
-- 3. FONCTION POUR OBTENIR LE BON NOM D'ENTREPRISE
-- =====================================================

CREATE OR REPLACE FUNCTION get_business_name(p_business_id UUID)
RETURNS VARCHAR(255) AS $$
DECLARE
    business_name VARCHAR(255);
BEGIN
    -- Essayer d'abord business_profiles
    SELECT COALESCE(bp.business_name, bp.company_name, bp.name)
    INTO business_name
    FROM business_profiles bp
    WHERE bp.id = p_business_id
    AND (bp.business_name IS NOT NULL OR bp.company_name IS NOT NULL OR bp.name IS NOT NULL);
    
    -- Si pas trouvé, essayer profiles
    IF business_name IS NULL THEN
        SELECT COALESCE(p.business_name, p.full_name, p.username)
        INTO business_name
        FROM profiles p
        WHERE p.id = p_business_id;
    END IF;
    
    -- Fallback
    IF business_name IS NULL THEN
        business_name := 'Entreprise Inconnue';
    END IF;
    
    RETURN business_name;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. CORRIGER LES CODES EXISTANTS (OPTIONNEL)
-- =====================================================

-- Mettre à jour les codes existants avec les bons noms
-- ATTENTION: Décommentez seulement si vous voulez corriger les codes existants

/*
UPDATE subscription_codes 
SET business_name = get_business_name(business_id)
WHERE business_name != get_business_name(business_id);
*/

-- =====================================================
-- 5. EXEMPLE DE MISE À JOUR MANUELLE
-- =====================================================

-- Si vous connaissez l'ID de l'entreprise DEXIMA, vous pouvez corriger manuellement:
-- Remplacez 'USER_ID_HERE' par l'ID réel de l'utilisateur

/*
-- Exemple pour corriger un profil spécifique
UPDATE profiles 
SET business_name = 'DEXIMA'
WHERE id = 'USER_ID_HERE';

-- Ou dans business_profiles
UPDATE business_profiles 
SET business_name = 'DEXIMA'
WHERE id = 'USER_ID_HERE';

-- Corriger les codes existants pour cet utilisateur
UPDATE subscription_codes 
SET business_name = 'DEXIMA'
WHERE business_id = 'USER_ID_HERE';
*/

-- =====================================================
-- 6. VÉRIFICATION FINALE
-- =====================================================

DO $$
DECLARE
    total_profiles integer;
    total_business_profiles integer;
    total_codes integer;
BEGIN
    SELECT COUNT(*) INTO total_profiles FROM profiles WHERE user_type = 'business';
    SELECT COUNT(*) INTO total_business_profiles FROM business_profiles;
    SELECT COUNT(*) INTO total_codes FROM subscription_codes;
    
    RAISE NOTICE '';
    RAISE NOTICE '📊 STATISTIQUES:';
    RAISE NOTICE '   👥 Profils business: %', total_profiles;
    RAISE NOTICE '   🏢 Business profiles: %', total_business_profiles;
    RAISE NOTICE '   🔐 Codes d''abonnement: %', total_codes;
    RAISE NOTICE '';
    RAISE NOTICE '✅ Fonction get_business_name() créée';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 POUR CORRIGER MANUELLEMENT:';
    RAISE NOTICE '1. Identifiez l''ID de l''utilisateur DEXIMA dans les résultats ci-dessus';
    RAISE NOTICE '2. Exécutez les requêtes UPDATE avec le bon ID';
    RAISE NOTICE '3. Rechargez l''interface pour voir les changements';
    RAISE NOTICE '';
END $$;
