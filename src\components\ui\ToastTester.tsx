import React from 'react';
import { useToast } from './ToastContainer';

/**
 * Composant de test pour vérifier le fonctionnement du ToastProvider
 */
const ToastTester: React.FC = () => {
  const { showSuccess, showError, showInfo, showWarning } = useToast();

  const testSuccess = () => {
    showSuccess('Test réussi !', 'Le ToastProvider fonctionne correctement');
  };

  const testError = () => {
    showError('Test d\'erreur', 'Simulation d\'une erreur pour tester l\'affichage');
  };

  const testInfo = () => {
    showInfo('Information', 'Ceci est un message d\'information');
  };

  const testWarning = () => {
    showWarning('Avertissement', 'Ceci est un message d\'avertissement');
  };

  const testMultiple = () => {
    showSuccess('Toast 1', 'Premier toast');
    setTimeout(() => showError('Toast 2', 'Deuxième toast'), 500);
    setTimeout(() => showInfo('Toast 3', 'Troisième toast'), 1000);
    setTimeout(() => showWarning('Toast 4', 'Quatrième toast'), 1500);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md max-w-md mx-auto">
      <h2 className="text-xl font-bold mb-4 text-gray-800">
        🧪 Test du ToastProvider
      </h2>
      
      <div className="space-y-3">
        <button
          onClick={testSuccess}
          className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
        >
          ✅ Test Succès
        </button>
        
        <button
          onClick={testError}
          className="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          ❌ Test Erreur
        </button>
        
        <button
          onClick={testInfo}
          className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          ℹ️ Test Info
        </button>
        
        <button
          onClick={testWarning}
          className="w-full px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors"
        >
          ⚠️ Test Warning
        </button>
        
        <button
          onClick={testMultiple}
          className="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
        >
          🎭 Test Multiple
        </button>
      </div>
      
      <div className="mt-4 p-3 bg-gray-100 rounded text-sm text-gray-600">
        <p><strong>Instructions :</strong></p>
        <ul className="list-disc list-inside mt-1 space-y-1">
          <li>Cliquez sur les boutons pour tester les toasts</li>
          <li>Les toasts apparaissent en haut à droite</li>
          <li>Ils se ferment automatiquement après 4 secondes</li>
          <li>Vous pouvez les fermer manuellement avec le X</li>
        </ul>
      </div>
    </div>
  );
};

export default ToastTester;
