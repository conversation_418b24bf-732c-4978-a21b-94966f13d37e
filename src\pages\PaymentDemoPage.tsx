import React, { useState } from 'react';
import {
  CreditCard, CheckCircle, Download, Printer, Mail,
  ArrowRight, Shield, Clock, Star, Crown
} from 'lucide-react';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import Button from '../components/ui/Button';

const PaymentDemoPage: React.FC = () => {
  const [currentDemo, setCurrentDemo] = useState<'plans' | 'payment' | 'receipt'>('plans');

  const renderPlansDemo = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Choisissez votre plan</h2>
        <p className="text-gray-600">Sélectionnez le plan qui correspond à vos besoins</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Plan Starter */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <Star className="mx-auto text-blue-600 mb-3" size={32} />
            <h3 className="text-xl font-bold text-gray-900">Starter</h3>
            <div className="text-3xl font-bold text-gray-900 mt-2">15,000 F CFA</div>
            <div className="text-sm text-gray-600">par mois</div>
          </CardHeader>
          <CardBody>
            <ul className="space-y-2 mb-6">
              <li className="flex items-center text-sm">
                <CheckCircle className="text-green-500 mr-2" size={16} />
                Jusqu'à 500 avis/mois
              </li>
              <li className="flex items-center text-sm">
                <CheckCircle className="text-green-500 mr-2" size={16} />
                Export PDF
              </li>
              <li className="flex items-center text-sm">
                <CheckCircle className="text-green-500 mr-2" size={16} />
                Support prioritaire
              </li>
            </ul>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setCurrentDemo('payment')}
            >
              Choisir ce plan
            </Button>
          </CardBody>
        </Card>

        {/* Plan Professional */}
        <Card className="hover:shadow-lg transition-shadow ring-2 ring-purple-500">
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">
              Plus populaire
            </span>
          </div>
          <CardHeader className="text-center">
            <Crown className="mx-auto text-purple-600 mb-3" size={32} />
            <h3 className="text-xl font-bold text-gray-900">Professional</h3>
            <div className="text-3xl font-bold text-gray-900 mt-2">35,000 F CFA</div>
            <div className="text-sm text-gray-600">par mois</div>
          </CardHeader>
          <CardBody>
            <ul className="space-y-2 mb-6">
              <li className="flex items-center text-sm">
                <CheckCircle className="text-green-500 mr-2" size={16} />
                Avis illimités
              </li>
              <li className="flex items-center text-sm">
                <CheckCircle className="text-green-500 mr-2" size={16} />
                Analyses avancées
              </li>
              <li className="flex items-center text-sm">
                <CheckCircle className="text-green-500 mr-2" size={16} />
                Support 24/7
              </li>
            </ul>
            <Button
              className="w-full"
              onClick={() => setCurrentDemo('payment')}
            >
              Choisir ce plan
            </Button>
          </CardBody>
        </Card>

        {/* Plan Enterprise */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <Shield className="mx-auto text-yellow-600 mb-3" size={32} />
            <h3 className="text-xl font-bold text-gray-900">Enterprise</h3>
            <div className="text-3xl font-bold text-gray-900 mt-2">75,000 F CFA</div>
            <div className="text-sm text-gray-600">par mois</div>
          </CardHeader>
          <CardBody>
            <ul className="space-y-2 mb-6">
              <li className="flex items-center text-sm">
                <CheckCircle className="text-green-500 mr-2" size={16} />
                Multi-comptes
              </li>
              <li className="flex items-center text-sm">
                <CheckCircle className="text-green-500 mr-2" size={16} />
                SLA 99.9%
              </li>
              <li className="flex items-center text-sm">
                <CheckCircle className="text-green-500 mr-2" size={16} />
                Support dédié
              </li>
            </ul>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setCurrentDemo('payment')}
            >
              Choisir ce plan
            </Button>
          </CardBody>
        </Card>
      </div>
    </div>
  );

  const renderPaymentDemo = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Processus de paiement</h2>
        <p className="text-gray-600">Simulation du processus de paiement sécurisé</p>
      </div>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardBody className="p-8">
            {/* Résumé de commande */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Résumé de la commande</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">Plan Professional</span>
                  <span className="font-medium">35,000 F CFA</span>
                </div>
                <div className="text-sm text-gray-600">Abonnement mensuel</div>
              </div>
            </div>

            {/* Méthodes de paiement */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Méthode de paiement</h3>
              <div className="space-y-3">
                <div className="border border-blue-300 bg-blue-50 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <CreditCard className="text-blue-600" size={24} />
                    <div>
                      <h4 className="font-medium text-gray-900">Carte Visa</h4>
                      <p className="text-sm text-gray-600">•••• •••• •••• 4242</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Sécurité */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <div className="flex items-start space-x-3">
                <Shield className="text-green-600 mt-0.5" size={20} />
                <div>
                  <h4 className="font-medium text-green-800">Paiement sécurisé</h4>
                  <p className="text-green-700 text-sm mt-1">
                    Chiffrement SSL 256 bits - Vos données sont protégées
                  </p>
                </div>
              </div>
            </div>

            <div className="flex space-x-4">
              <Button
                variant="outline"
                onClick={() => setCurrentDemo('plans')}
                className="flex-1"
              >
                Retour
              </Button>
              <Button
                onClick={() => setCurrentDemo('receipt')}
                className="flex-1"
              >
                <Clock className="mr-2" size={16} />
                Simuler le paiement
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );

  const renderReceiptDemo = () => (
    <div className="space-y-6">
      <div className="text-center">
        <CheckCircle className="mx-auto text-green-600 mb-4" size={64} />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Paiement réussi !</h2>
        <p className="text-gray-600">Votre reçu de paiement est prêt</p>
      </div>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">Reçu de paiement</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Numéro de facture :</span>
                  <div className="font-medium">INV-2024-001234</div>
                </div>
                <div>
                  <span className="text-gray-600">Date de paiement :</span>
                  <div className="font-medium">{new Date().toLocaleDateString('fr-FR')}</div>
                </div>
                <div>
                  <span className="text-gray-600">Plan souscrit :</span>
                  <div className="font-medium">Professional</div>
                </div>
                <div>
                  <span className="text-gray-600">Montant payé :</span>
                  <div className="font-medium">35,000 F CFA</div>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="text-sm text-gray-600 mb-3">ID de transaction :</div>
                <div className="font-mono text-sm bg-gray-100 p-2 rounded">
                  txn_1234567890_abcdef123456
                </div>
              </div>

              <div className="flex space-x-3">
                <Button variant="outline" size="sm" className="flex-1">
                  <Printer size={14} className="mr-2" />
                  Imprimer
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  <Download size={14} className="mr-2" />
                  Télécharger PDF
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  <Mail size={14} className="mr-2" />
                  Envoyer par email
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      <div className="text-center">
        <Button onClick={() => setCurrentDemo('plans')}>
          Nouvelle démonstration
        </Button>
      </div>
    </div>
  );

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Navigation des étapes */}
      <div className="flex justify-center mb-8">
        <div className="flex items-center space-x-4">
          <div className={`flex items-center space-x-2 px-4 py-2 rounded-full ${
            currentDemo === 'plans' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'
          }`}>
            <span className="w-6 h-6 rounded-full bg-current text-white text-xs flex items-center justify-center">1</span>
            <span className="font-medium">Plans</span>
          </div>
          <ArrowRight className="text-gray-400" size={20} />
          <div className={`flex items-center space-x-2 px-4 py-2 rounded-full ${
            currentDemo === 'payment' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'
          }`}>
            <span className="w-6 h-6 rounded-full bg-current text-white text-xs flex items-center justify-center">2</span>
            <span className="font-medium">Paiement</span>
          </div>
          <ArrowRight className="text-gray-400" size={20} />
          <div className={`flex items-center space-x-2 px-4 py-2 rounded-full ${
            currentDemo === 'receipt' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'
          }`}>
            <span className="w-6 h-6 rounded-full bg-current text-white text-xs flex items-center justify-center">3</span>
            <span className="font-medium">Reçu</span>
          </div>
        </div>
      </div>

      {/* Contenu de la démonstration */}
      {currentDemo === 'plans' && renderPlansDemo()}
      {currentDemo === 'payment' && renderPaymentDemo()}
      {currentDemo === 'receipt' && renderReceiptDemo()}

      {/* Note */}
      <div className="mt-8 text-center text-sm text-gray-500">
        <p>🎯 Démonstration du processus de paiement complet - Aucun vrai paiement n'est effectué</p>
      </div>
    </div>
  );
};

export default PaymentDemoPage;
