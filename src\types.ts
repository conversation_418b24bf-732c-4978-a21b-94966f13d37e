export interface IUser {
  id: string;
  username: string;
  email: string;
  profilePicture?: string;
  coverPhotoUrl?: string;
  city?: string;
  country?: string;
  createdAt: string;
  role: UserRole;
  status: UserStatus;
  post_count?: number;
}

export interface IProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  images: string[];
  stock: number;
  negotiable: boolean;
  averageRating: number;
  qualityBadge: QualityBadge;
  businessId: string;
  createdAt: string;
  updatedAt: string;
}

export interface IBusinessUser extends IUser {
  businessName: string;
  businessType?: string;
  businessStatus?: BusinessStatus;
  verified?: boolean;
  businessDescription?: string;
  businessCategory?: string;
  website?: string;
  phone?: string;
  address?: string;
  catalog?: IProduct[];
  wallet?: number;
  salesCount?: number;
  averageRating?: number;
  totalReviews?: number;
  followers?: number;
  following?: number;
}

export interface IPost {
  id: string;
  userId: string;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  recommendations?: string[];
  comments?: IComment[];
}

export interface IComment {
  id: string;
  userId: string;
  content: string;
  createdAt: string;
  hasUsedProduct: boolean;
}

export interface ISubscription {
  id: string;
  subscriberId: string;  // ID de l'utilisateur qui s'abonne
  targetId: string;      // ID de l'utilisateur ou entreprise à qui on s'abonne
  targetType: 'user' | 'business'; // Type de la cible
  level: 'basic' | 'premium' | 'vip'; // Niveau d'abonnement
  createdAt: Date;
  updatedAt: Date;
  status: 'active' | 'paused' | 'cancelled';
  notificationsEnabled: boolean;
  autoRenewal: boolean;
  expiresAt?: Date; // Pour les abonnements à durée limitée
}

export interface ISubscriptionPlan {
  id: string;
  name: string;
  level: 'basic' | 'premium' | 'vip';
  price: number;
  duration: number; // en jours
  features: string[];
  targetType: 'user' | 'business';
  isActive: boolean;
}

export enum PostType {
  FAVORITE = 'favorite',
  COMPLAINT = 'complaint'
}

export enum UserRole {
  STANDARD = 'standard',
  BUSINESS = 'business',
  ADMIN = 'admin'
}

export enum NegotiationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  COUNTER_OFFER = 'counter_offer',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled'
}

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

export enum UserStatus {
  NEWBIE = 'newbie',           // Just signed up, profile incomplete
  MEMBER = 'member',           // Profile complete (avatar, cover, country, city)
  CONTRIBUTOR = 'contributor', // Made first post or comment
  DISCOVERER = 'discoverer',   // Active, making posts/comments
  INFLUENCER = 'influencer',   // High quality/popular posts
  LEADER = 'leader'            // Top tier
}

export enum BusinessStatus {
  NEW = 'new',                 // Nouveau compte entreprise
  VERIFIED = 'verified',       // Compte vérifié
  PREMIUM = 'premium',         // Abonnement premium
  FEATURED = 'featured',       // Mis en avant sur la plateforme
  PARTNER = 'partner'          // Partenaire officiel
}

export enum QualityBadge {
  STANDARD = 'standard',
  VERIFIED = 'verified',
  PREMIUM = 'premium',
  EXCELLENT = 'excellent',
  TOP_QUALITY = 'top_quality',
  GOLD = 'gold',
  SILVER = 'silver',
  BRONZE = 'bronze',
  NONE = 'none'
}