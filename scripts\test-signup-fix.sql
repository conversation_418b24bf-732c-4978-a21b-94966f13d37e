-- Script de test pour vérifier que l'inscription fonctionne après la correction

-- =====================================================
-- 1. VÉRIFIER QUE LES FONCTIONS EXISTENT
-- =====================================================

-- Vérifier la fonction clean_interests
SELECT 
    'Fonction clean_interests' as test,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.routines 
            WHERE routine_schema = 'public' 
            AND routine_name = 'clean_interests'
        ) THEN '✅ Existe'
        ELSE '❌ Manquante'
    END as status;

-- Vérifier la fonction handle_new_user
SELECT 
    'Fonction handle_new_user' as test,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.routines 
            WHERE routine_schema = 'public' 
            AND routine_name = 'handle_new_user'
        ) THEN '✅ Existe'
        ELSE '❌ Manquante'
    END as status;

-- =====================================================
-- 2. TESTER LA FONCTION CLEAN_INTERESTS
-- =====================================================

-- Test 1: Nettoyage des espaces
SELECT 
    'Test clean_interests - espaces' as test,
    clean_interests('  sport  ,   musique,voyage   ,  ') as result,
    CASE 
        WHEN clean_interests('  sport  ,   musique,voyage   ,  ') = 'sport, musique, voyage'
        THEN '✅ OK'
        ELSE '❌ Échec'
    END as status;

-- Test 2: Gestion des valeurs NULL
SELECT 
    'Test clean_interests - NULL' as test,
    clean_interests(NULL) as result,
    CASE 
        WHEN clean_interests(NULL) IS NULL
        THEN '✅ OK'
        ELSE '❌ Échec'
    END as status;

-- Test 3: Chaîne vide
SELECT 
    'Test clean_interests - vide' as test,
    clean_interests('') as result,
    CASE 
        WHEN clean_interests('') IS NULL
        THEN '✅ OK'
        ELSE '❌ Échec'
    END as status;

-- =====================================================
-- 3. VÉRIFIER LES TRIGGERS
-- =====================================================

-- Vérifier le trigger clean_interests_trigger
SELECT 
    'Trigger clean_interests_trigger' as test,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.triggers 
            WHERE trigger_schema = 'public' 
            AND trigger_name = 'clean_interests_trigger'
            AND event_object_table = 'profiles'
        ) THEN '✅ Existe'
        ELSE '❌ Manquant'
    END as status;

-- Vérifier le trigger on_auth_user_created
SELECT 
    'Trigger on_auth_user_created' as test,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.triggers 
            WHERE trigger_schema = 'public' 
            AND trigger_name = 'on_auth_user_created'
            AND event_object_table = 'users'
        ) THEN '✅ Existe'
        ELSE '❌ Manquant'
    END as status;

-- =====================================================
-- 4. VÉRIFIER LA STRUCTURE DE LA TABLE PROFILES
-- =====================================================

-- Vérifier que la colonne interests existe
SELECT 
    'Colonne interests dans profiles' as test,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'profiles'
            AND column_name = 'interests'
        ) THEN '✅ Existe'
        ELSE '❌ Manquante'
    END as status;

-- Vérifier les colonnes essentielles de profiles
SELECT 
    'Colonnes essentielles profiles' as test,
    string_agg(column_name, ', ' ORDER BY ordinal_position) as columns,
    CASE 
        WHEN COUNT(*) >= 5 THEN '✅ OK'
        ELSE '❌ Incomplète'
    END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'profiles'
AND column_name IN ('id', 'username', 'email', 'role', 'interests');

-- =====================================================
-- 5. SIMULER UN TEST D'INSCRIPTION (SANS CRÉER D'UTILISATEUR)
-- =====================================================

-- Tester la logique de la fonction handle_new_user sans l'exécuter
DO $$
DECLARE
    test_user_data jsonb;
    test_result text;
BEGIN
    -- Simuler des métadonnées utilisateur
    test_user_data := '{"username": "test_user", "role": "standard"}'::jsonb;
    
    -- Vérifier que nous pouvons accéder aux données
    IF test_user_data->>'username' = 'test_user' THEN
        RAISE NOTICE '✅ Simulation extraction métadonnées: OK';
    ELSE
        RAISE NOTICE '❌ Simulation extraction métadonnées: Échec';
    END IF;
    
    -- Tester pour un utilisateur business
    test_user_data := '{"username": "test_business", "role": "business", "businessName": "Test Company"}'::jsonb;
    
    IF test_user_data->>'role' = 'business' AND test_user_data->>'businessName' = 'Test Company' THEN
        RAISE NOTICE '✅ Simulation données business: OK';
    ELSE
        RAISE NOTICE '❌ Simulation données business: Échec';
    END IF;
END $$;

-- =====================================================
-- 6. VÉRIFIER LES PERMISSIONS
-- =====================================================

-- Vérifier les permissions sur la table profiles
SELECT 
    'Permissions table profiles' as test,
    CASE 
        WHEN has_table_privilege('authenticated', 'profiles', 'INSERT') 
        THEN '✅ INSERT autorisé'
        ELSE '❌ INSERT refusé'
    END as status;

-- =====================================================
-- 7. RÉSUMÉ DU DIAGNOSTIC
-- =====================================================

SELECT 
    '=== RÉSUMÉ DU DIAGNOSTIC ===' as section,
    '' as details
UNION ALL
SELECT 
    'Fonctions nécessaires:',
    CASE 
        WHEN (
            SELECT COUNT(*) FROM information_schema.routines 
            WHERE routine_schema = 'public' 
            AND routine_name IN ('clean_interests', 'handle_new_user')
        ) = 2 THEN '✅ Toutes présentes'
        ELSE '❌ Manquantes'
    END
UNION ALL
SELECT 
    'Triggers nécessaires:',
    CASE 
        WHEN (
            SELECT COUNT(*) FROM information_schema.triggers 
            WHERE trigger_schema = 'public' 
            AND trigger_name IN ('clean_interests_trigger', 'on_auth_user_created')
        ) = 2 THEN '✅ Tous présents'
        ELSE '❌ Manquants'
    END
UNION ALL
SELECT 
    'Structure table profiles:',
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'profiles'
        ) THEN '✅ Table existe'
        ELSE '❌ Table manquante'
    END
UNION ALL
SELECT 
    'Test fonction clean_interests:',
    CASE 
        WHEN clean_interests('test, data') = 'test, data'
        THEN '✅ Fonctionne'
        ELSE '❌ Problème'
    END;

-- =====================================================
-- 8. INSTRUCTIONS POUR TESTER L'INSCRIPTION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== INSTRUCTIONS POUR TESTER L''INSCRIPTION ===';
    RAISE NOTICE '';
    RAISE NOTICE '1. Appliquez d''abord la migration de correction:';
    RAISE NOTICE '   - Copiez le contenu de supabase/migrations/20250602000003_fix_clean_interests_function.sql';
    RAISE NOTICE '   - Exécutez-le dans l''éditeur SQL de Supabase';
    RAISE NOTICE '';
    RAISE NOTICE '2. Testez l''inscription:';
    RAISE NOTICE '   - Allez sur votre application (http://localhost:5173)';
    RAISE NOTICE '   - Cliquez sur "S''inscrire"';
    RAISE NOTICE '   - Créez un compte utilisateur ou entreprise';
    RAISE NOTICE '';
    RAISE NOTICE '3. Vérifiez dans Supabase:';
    RAISE NOTICE '   - Table profiles: nouveau profil créé';
    RAISE NOTICE '   - Table business_profiles: profil business si applicable';
    RAISE NOTICE '   - Aucune erreur dans les logs';
    RAISE NOTICE '';
    RAISE NOTICE '4. En cas de problème:';
    RAISE NOTICE '   - Vérifiez les logs Supabase';
    RAISE NOTICE '   - Consultez la console du navigateur';
    RAISE NOTICE '   - Relancez ce script de diagnostic';
    RAISE NOTICE '';
END $$;
