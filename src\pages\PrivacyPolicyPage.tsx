import React from 'react';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import { Shield, Eye, Lock, UserCheck, Database, AlertTriangle } from 'lucide-react';

const PrivacyPolicyPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* En-tête */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4 flex items-center justify-center">
            <Shield className="mr-3 text-blue-600" size={40} />
            Politique de Confidentialité
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Dernière mise à jour : Décembre 2024
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Chez Customeroom, nous nous engageons à protéger votre vie privée et vos données personnelles.
          </p>
        </div>

        {/* Introduction */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900 flex items-center">
              <Eye className="mr-3 text-blue-600" size={24} />
              Introduction
            </h2>
          </CardHeader>
          <CardBody>
            <p className="text-gray-700 leading-relaxed">
              Cette politique de confidentialité explique comment Customeroom collecte, utilise, 
              stocke et protège vos informations personnelles lorsque vous utilisez notre plateforme. 
              En utilisant nos services, vous acceptez les pratiques décrites dans cette politique.
            </p>
          </CardBody>
        </Card>

        {/* Données Collectées */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900 flex items-center">
              <Database className="mr-3 text-green-600" size={24} />
              Données que Nous Collectons
            </h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Informations d'Identification</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Nom complet et pseudonyme</li>
                  <li>Adresse email</li>
                  <li>Numéro de téléphone</li>
                  <li>Photo de profil</li>
                  <li>Informations de l'entreprise (pour les comptes business)</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Données d'Utilisation</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Avis et commentaires publiés</li>
                  <li>Interactions avec d'autres utilisateurs</li>
                  <li>Historique de navigation sur la plateforme</li>
                  <li>Préférences et paramètres de compte</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Données Techniques</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Adresse IP et localisation approximative</li>
                  <li>Type de navigateur et système d'exploitation</li>
                  <li>Cookies et technologies similaires</li>
                  <li>Journaux d'activité et données de performance</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Utilisation des Données */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900 flex items-center">
              <UserCheck className="mr-3 text-purple-600" size={24} />
              Comment Nous Utilisons Vos Données
            </h2>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Services Principaux</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Création et gestion de votre compte</li>
                  <li>Publication et modération des avis</li>
                  <li>Recommandations personnalisées</li>
                  <li>Communication entre utilisateurs</li>
                </ul>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Amélioration des Services</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Analyse des tendances d'utilisation</li>
                  <li>Développement de nouvelles fonctionnalités</li>
                  <li>Prévention de la fraude</li>
                  <li>Support client et assistance</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Partage des Données */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900 flex items-center">
              <Lock className="mr-3 text-red-600" size={24} />
              Partage et Protection des Données
            </h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Nous Ne Vendons Jamais Vos Données</h3>
                <p className="text-gray-700">
                  Customeroom ne vend, ne loue, ni ne partage vos données personnelles avec des tiers 
                  à des fins commerciales. Vos informations restent confidentielles.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Partage Limité</h3>
                <p className="text-gray-700 mb-3">
                  Nous pouvons partager certaines données uniquement dans les cas suivants :
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Avec votre consentement explicite</li>
                  <li>Pour se conformer aux obligations légales</li>
                  <li>Avec nos prestataires de services (sous contrat strict)</li>
                  <li>En cas de fusion ou acquisition (avec notification préalable)</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Mesures de Sécurité</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Chiffrement des données sensibles</li>
                  <li>Accès restreint aux données personnelles</li>
                  <li>Surveillance continue des systèmes</li>
                  <li>Sauvegardes sécurisées et régulières</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Vos Droits */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900">Vos Droits et Contrôles</h2>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Droits d'Accès</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Consulter vos données personnelles</li>
                  <li>Télécharger vos informations</li>
                  <li>Corriger les données inexactes</li>
                  <li>Supprimer votre compte</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Contrôles de Confidentialité</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Paramètres de visibilité du profil</li>
                  <li>Gestion des notifications</li>
                  <li>Contrôle des cookies</li>
                  <li>Préférences de communication</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Cookies */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900">Politique des Cookies</h2>
          </CardHeader>
          <CardBody>
            <p className="text-gray-700 mb-4">
              Nous utilisons des cookies pour améliorer votre expérience sur Customeroom :
            </p>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong className="text-gray-900">Cookies Essentiels :</strong>
                  <span className="text-gray-700"> Nécessaires au fonctionnement de la plateforme</span>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong className="text-gray-900">Cookies de Performance :</strong>
                  <span className="text-gray-700"> Nous aident à améliorer nos services</span>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong className="text-gray-900">Cookies de Préférences :</strong>
                  <span className="text-gray-700"> Mémorisent vos choix et paramètres</span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Contact */}
        <Card>
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900 flex items-center">
              <AlertTriangle className="mr-3 text-orange-600" size={24} />
              Questions et Contact
            </h2>
          </CardHeader>
          <CardBody>
            <p className="text-gray-700 mb-4">
              Pour toute question concernant cette politique de confidentialité ou vos données personnelles :
            </p>
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-gray-700">
                <strong>Email :</strong> <EMAIL><br />
                <strong>Téléphone :</strong> +225 01 23 45 67<br />
                <strong>Adresse :</strong> Abidjan, Côte d'Ivoire
              </p>
            </div>
            <p className="text-sm text-gray-500 mt-4">
              Nous nous engageons à répondre à vos demandes dans un délai de 30 jours maximum.
            </p>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default PrivacyPolicyPage;
