/* Global Data Sync - Styles Ultra-Avancés */

/* Indicateur de synchronisation global */
.global-sync-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  background: white;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.sync-indicator-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sync-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sync-icon .spinning {
  color: #3b82f6;
  animation: spin 1s linear infinite;
}

.sync-icon .success {
  color: #10b981;
}

.sync-icon .error {
  color: #ef4444;
}

.sync-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Barre de statut de synchronisation */
.sync-status-bar {
  background: white;
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.sync-status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.sync-mode {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mode-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 6px;
  background: #f8fafc;
}

.mode-indicator .real-mode {
  color: #10b981;
}

.mode-indicator .demo-mode {
  color: #f59e0b;
}

.mode-text {
  font-size: 0.8rem;
  font-weight: 500;
  color: #374151;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
}

.connection-status .online {
  color: #10b981;
}

.connection-status .offline {
  color: #ef4444;
}

.connection-text {
  font-size: 0.75rem;
  color: #6b7280;
}

.sync-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.data-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  font-size: 0.75rem;
  color: #6b7280;
  padding: 2px 6px;
  background: #f3f4f6;
  border-radius: 4px;
}

.sync-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.last-sync {
  font-size: 0.75rem;
  color: #9ca3af;
}

.sync-button {
  width: 24px;
  height: 24px;
  border: none;
  background: #f3f4f6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.sync-button:hover:not(:disabled) {
  background: #e5e7eb;
  color: #374151;
  transform: scale(1.05);
}

.sync-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.sync-button .spinning {
  animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .global-sync-indicator {
    top: 10px;
    right: 10px;
    padding: 8px 12px;
  }
  
  .sync-status-content {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .sync-mode {
    justify-content: space-between;
  }
  
  .sync-info {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .data-stats {
    justify-content: space-between;
  }
  
  .sync-controls {
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .data-stats {
    flex-direction: column;
    gap: 4px;
  }
  
  .stat-item {
    text-align: center;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .global-sync-indicator {
    background: #1f2937;
    border-color: #374151;
  }
  
  .sync-text {
    color: #f9fafb;
  }
  
  .sync-status-bar {
    background: #1f2937;
    border-color: #374151;
  }
  
  .mode-indicator {
    background: #374151;
  }
  
  .mode-text {
    color: #f9fafb;
  }
  
  .connection-text {
    color: #d1d5db;
  }
  
  .stat-item {
    background: #374151;
    color: #d1d5db;
  }
  
  .last-sync {
    color: #9ca3af;
  }
  
  .sync-button {
    background: #374151;
    color: #d1d5db;
  }
  
  .sync-button:hover:not(:disabled) {
    background: #4b5563;
    color: #f9fafb;
  }
}

/* Animations avancées */
.sync-status-bar {
  transition: all 0.3s ease;
}

.sync-status-bar:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.mode-indicator {
  transition: all 0.2s ease;
}

.mode-indicator:hover {
  background: #e2e8f0;
  transform: scale(1.02);
}

.stat-item {
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: #e5e7eb;
  transform: scale(1.05);
}

/* États de synchronisation */
.sync-status-bar.syncing {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.sync-status-bar.success {
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}

.sync-status-bar.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

/* Pulse animation pour les mises à jour */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.sync-status-bar.updating {
  animation: pulse 2s infinite;
}

/* Accessibility */
.sync-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.global-sync-indicator:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .global-sync-indicator,
  .sync-status-bar,
  .mode-indicator,
  .stat-item,
  .sync-button {
    animation: none;
    transition: none;
  }
  
  .sync-button:hover,
  .sync-status-bar:hover,
  .mode-indicator:hover,
  .stat-item:hover {
    transform: none;
  }
  
  .spinning {
    animation: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .global-sync-indicator {
    border: 2px solid #000;
  }
  
  .sync-status-bar {
    border: 2px solid #000;
  }
  
  .mode-indicator {
    border: 1px solid #000;
  }
  
  .stat-item {
    border: 1px solid #000;
  }
  
  .sync-button {
    border: 1px solid #000;
  }
}
