import { IProduct, QualityBadge } from '../types';
import { REAL_PRODUCTS } from '../data/realProducts';
import { getMockProductComments, getMockProductRatings } from '../data/mockReviewsData';
import { UserBehaviorAnalysisService, IUserBehaviorProfile } from './userBehaviorAnalysisService';

// Interface pour les recommandations IA
export interface IAIRecommendation {
  id: string;
  product: IProduct;
  score: number; // Score de recommandation (0-100)
  reasons: string[]; // Raisons de la recommandation
  category: 'trending' | 'personalized' | 'similar' | 'quality' | 'budget';
  confidence: number; // Niveau de confiance (0-100)
  aiInsight: string; // Analyse IA personnalisée
}

// Interface pour l'assistant IA
export interface IAIAssistant {
  question: string;
  answer: string;
  relatedProducts?: IProduct[];
  confidence: number;
  timestamp: Date;
}

// Interface pour les préférences utilisateur
export interface IUserPreferences {
  userId: string;
  favoriteCategories: string[];
  priceRange: { min: number; max: number };
  qualityPreference: QualityBadge[];
  ratedProducts: string[];
  commentedProducts: string[];
  recommendedProducts: string[];
}

// Service d'IA pour les recommandations
export class AIRecommendationService {

  // Analyser les préférences utilisateur basées sur son activité comportementale
  static async analyzeUserPreferences(userId: string): Promise<IUserPreferences> {
    try {
      // Obtenir le profil comportemental complet
      const behaviorProfile = await UserBehaviorAnalysisService.generateUserBehaviorProfile(userId);

      // Extraire les catégories préférées du profil comportemental
      const favoriteCategories = Object.entries(behaviorProfile.preferences.categories)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .map(([category]) => category);

      // Si pas assez de données comportementales, utiliser une analyse basique
      if (favoriteCategories.length === 0) {
        return this.getFallbackPreferences(userId);
      }

      // Analyser la gamme de prix basée sur l'intention d'achat
      const priceRange = this.calculatePriceRangeFromBehavior(behaviorProfile);

      // Déterminer les préférences de qualité basées sur le sentiment
      const qualityPreference = this.determineQualityPreference(behaviorProfile);

      // Récupérer les produits avec lesquels l'utilisateur a interagi
      const interactedProducts = await this.getUserInteractedProducts(userId);

      return {
        userId,
        favoriteCategories,
        priceRange,
        qualityPreference,
        ratedProducts: interactedProducts.rated,
        commentedProducts: interactedProducts.commented,
        recommendedProducts: interactedProducts.recommended
      };
    } catch (error) {
      console.error('Erreur lors de l\'analyse des préférences:', error);
      return this.getFallbackPreferences(userId);
    }
  }

  // Méthode de fallback pour les préférences
  private static getFallbackPreferences(userId: string): IUserPreferences {
    // Simulation basique si pas de données comportementales
    const userComments = REAL_PRODUCTS.filter(() => Math.random() > 0.7);
    const userRatings = REAL_PRODUCTS.filter(() => Math.random() > 0.6);
    const userRecommendations = REAL_PRODUCTS.filter(() => Math.random() > 0.8);

    const categoryCount: { [key: string]: number } = {};
    [...userComments, ...userRatings, ...userRecommendations].forEach(product => {
      categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;
    });

    const favoriteCategories = Object.entries(categoryCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([category]) => category);

    const prices = [...userComments, ...userRatings].map(p => p.price);
    const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length || 50000;

    return {
      userId,
      favoriteCategories,
      priceRange: {
        min: Math.max(0, avgPrice * 0.5),
        max: avgPrice * 1.5
      },
      qualityPreference: [QualityBadge.PREMIUM, QualityBadge.EXCELLENT, QualityBadge.GOLD],
      ratedProducts: userRatings.map(p => p.id),
      commentedProducts: userComments.map(p => p.id),
      recommendedProducts: userRecommendations.map(p => p.id)
    };
  }

  // Calculer la gamme de prix basée sur le comportement
  private static calculatePriceRangeFromBehavior(profile: IUserBehaviorProfile): { min: number; max: number } {
    // Analyser l'intention d'achat pour déterminer le budget
    const purchaseScore = profile.purchaseIntent.score;

    let basePrice = 50000; // Prix de base en F CFA

    // Ajuster selon le niveau d'engagement
    if (profile.interactionPatterns.engagementLevel === 'high') {
      basePrice *= 1.5; // Utilisateurs très engagés = budget plus élevé
    } else if (profile.interactionPatterns.engagementLevel === 'low') {
      basePrice *= 0.7; // Utilisateurs peu engagés = budget plus serré
    }

    // Ajuster selon le sentiment général
    if (profile.sentimentAnalysis.overallSentiment === 'positive') {
      basePrice *= 1.2; // Sentiment positif = plus disposé à dépenser
    } else if (profile.sentimentAnalysis.overallSentiment === 'negative') {
      basePrice *= 0.8; // Sentiment négatif = plus prudent
    }

    return {
      min: Math.max(1000, basePrice * 0.3),
      max: basePrice * 2
    };
  }

  // Déterminer les préférences de qualité
  private static determineQualityPreference(profile: IUserBehaviorProfile): QualityBadge[] {
    const preferences: QualityBadge[] = [];

    // Si l'utilisateur a un sentiment positif et un engagement élevé
    if (profile.sentimentAnalysis.overallSentiment === 'positive' &&
        profile.interactionPatterns.engagementLevel === 'high') {
      preferences.push(QualityBadge.PREMIUM, QualityBadge.EXCELLENT, QualityBadge.GOLD);
    } else if (profile.purchaseIntent.score > 70) {
      preferences.push(QualityBadge.EXCELLENT, QualityBadge.GOLD, QualityBadge.SILVER);
    } else {
      preferences.push(QualityBadge.GOLD, QualityBadge.SILVER, QualityBadge.BRONZE);
    }

    return preferences;
  }

  // Récupérer les produits avec lesquels l'utilisateur a interagi
  private static async getUserInteractedProducts(userId: string): Promise<{
    rated: string[];
    commented: string[];
    recommended: string[];
  }> {
    // Simulation - à remplacer par de vraies requêtes Supabase
    return {
      rated: REAL_PRODUCTS.filter(() => Math.random() > 0.8).map(p => p.id),
      commented: REAL_PRODUCTS.filter(() => Math.random() > 0.9).map(p => p.id),
      recommended: REAL_PRODUCTS.filter(() => Math.random() > 0.95).map(p => p.id)
    };
  }

  // Calculer le score de recommandation pour un produit
  static calculateRecommendationScore(product: IProduct, preferences: IUserPreferences): number {
    let score = 0;

    // Score basé sur la catégorie (30%)
    if (preferences.favoriteCategories.includes(product.category)) {
      const categoryIndex = preferences.favoriteCategories.indexOf(product.category);
      score += (30 - categoryIndex * 5); // Premier choix = 30, deuxième = 25, etc.
    }

    // Score basé sur le prix (20%)
    if (product.price >= preferences.priceRange.min && product.price <= preferences.priceRange.max) {
      score += 20;
    } else {
      // Pénalité si hors budget
      const deviation = Math.min(
        Math.abs(product.price - preferences.priceRange.min),
        Math.abs(product.price - preferences.priceRange.max)
      ) / preferences.priceRange.max;
      score += Math.max(0, 20 - deviation * 20);
    }

    // Score basé sur la qualité (25%)
    if (preferences.qualityPreference.includes(product.qualityBadge || QualityBadge.NONE)) {
      score += 25;
    }

    // Score basé sur les notes (15%)
    score += (product.averageRating / 5) * 15;

    // Score basé sur la popularité (10%)
    const comments = getMockProductComments(product.id);
    const ratings = getMockProductRatings(product.id);
    const popularityScore = Math.min(10, (comments.length + ratings.length) * 2);
    score += popularityScore;

    // Bonus pour les produits non encore vus par l'utilisateur
    if (!preferences.ratedProducts.includes(product.id) &&
        !preferences.commentedProducts.includes(product.id)) {
      score += 5;
    }

    return Math.min(100, Math.max(0, score));
  }

  // Générer des raisons de recommandation
  static generateReasons(product: IProduct, preferences: IUserPreferences, score: number): string[] {
    const reasons: string[] = [];

    if (preferences.favoriteCategories.includes(product.category)) {
      reasons.push(`Correspond à votre catégorie préférée : ${product.category}`);
    }

    if (product.price >= preferences.priceRange.min && product.price <= preferences.priceRange.max) {
      reasons.push('Dans votre gamme de prix habituelle');
    }

    if (product.averageRating >= 4.5) {
      reasons.push(`Excellentes notes : ${product.averageRating}/5 étoiles`);
    }

    if (product.qualityBadge && [QualityBadge.PREMIUM, QualityBadge.EXCELLENT, QualityBadge.GOLD].includes(product.qualityBadge)) {
      reasons.push('Produit de haute qualité certifié');
    }

    if (product.negotiable) {
      reasons.push('Prix négociable - Économies possibles');
    }

    const comments = getMockProductComments(product.id);
    if (comments.length > 2) {
      const positiveComments = comments.filter(c => c.userRating && c.userRating >= 4);
      if (positiveComments.length > 0) {
        reasons.push(`${positiveComments.length} avis positifs récents`);
      }
    }

    if (score > 80) {
      reasons.push('Recommandation forte de notre IA');
    }

    return reasons.slice(0, 4); // Limiter à 4 raisons max
  }

  // Générer une analyse IA personnalisée
  static generateAIInsight(product: IProduct, preferences: IUserPreferences, score: number): string {
    const insights = [
      `Basé sur votre historique d'achats en ${product.category}, ce produit correspond parfaitement à vos goûts.`,
      `Notre IA détecte une forte compatibilité (${score}%) avec vos préférences.`,
      `Les utilisateurs ayant un profil similaire au vôtre ont adoré ce produit.`,
      `Excellent rapport qualité-prix dans votre gamme budgétaire habituelle.`,
      `Tendance actuelle : ce type de produit gagne en popularité parmi les utilisateurs comme vous.`,
      `Recommandation personnalisée : ce produit complèterait parfaitement vos achats précédents.`,
      `Notre algorithme prédit une satisfaction de ${Math.round(score)}% pour ce produit.`
    ];

    // Sélectionner un insight basé sur le score et les préférences
    let selectedInsight = insights[Math.floor(Math.random() * insights.length)];

    if (score > 90) {
      selectedInsight = `🎯 Recommandation exceptionnelle ! ${selectedInsight}`;
    } else if (score > 75) {
      selectedInsight = `✨ Très bon choix ! ${selectedInsight}`;
    } else if (score > 60) {
      selectedInsight = `👍 Bonne option ! ${selectedInsight}`;
    }

    return selectedInsight;
  }

  // Obtenir les recommandations personnalisées pour un utilisateur
  static async getPersonalizedRecommendations(userId: string, limit: number = 10): Promise<IAIRecommendation[]> {
    const preferences = await this.analyzeUserPreferences(userId);

    const recommendations: IAIRecommendation[] = REAL_PRODUCTS
      .map(product => {
        const score = this.calculateRecommendationScore(product, preferences);
        const reasons = this.generateReasons(product, preferences, score);
        const aiInsight = this.generateAIInsight(product, preferences, score);

        let category: IAIRecommendation['category'] = 'personalized';

        // Déterminer la catégorie de recommandation
        if (score > 85) category = 'quality';
        else if (product.price < preferences.priceRange.min) category = 'budget';
        else if (preferences.favoriteCategories.includes(product.category)) category = 'similar';
        else if (product.averageRating >= 4.5) category = 'trending';

        return {
          id: `rec-${product.id}-${Date.now()}`,
          product,
          score,
          reasons,
          category,
          confidence: Math.min(95, score + Math.random() * 10),
          aiInsight
        };
      })
      .filter(rec => rec.score > 40) // Filtrer les recommandations faibles
      .sort((a, b) => b.score - a.score) // Trier par score décroissant
      .slice(0, limit);

    return recommendations;
  }

  // Assistant IA pour répondre aux questions
  static async askAIAssistant(question: string, userId: string): Promise<IAIAssistant> {
    const preferences = await this.analyzeUserPreferences(userId);

    // Obtenir le profil comportemental pour des réponses plus personnalisées
    let behaviorProfile: IUserBehaviorProfile | null = null;
    try {
      behaviorProfile = await UserBehaviorAnalysisService.generateUserBehaviorProfile(userId);
    } catch (error) {
      console.error('Erreur lors de la récupération du profil comportemental:', error);
    }

    // Analyser la question et générer une réponse contextuelle
    const lowerQuestion = question.toLowerCase();
    let answer = '';
    let relatedProducts: IProduct[] = [];
    let confidence = 85;

    if (lowerQuestion.includes('recommand') || lowerQuestion.includes('conseil')) {
      const topRecommendations = await this.getPersonalizedRecommendations(userId, 3);

      // Personnaliser la réponse selon le profil comportemental
      if (behaviorProfile) {
        const sentiment = behaviorProfile.sentimentAnalysis.overallSentiment;
        const engagement = behaviorProfile.interactionPatterns.engagementLevel;
        const purchaseIntent = behaviorProfile.purchaseIntent.score;

        if (sentiment === 'positive' && engagement === 'high') {
          answer = `🌟 Excellent ! Votre profil montre que vous appréciez la qualité. Je vous recommande ${topRecommendations[0]?.product.name} avec ${topRecommendations[0]?.score}% de compatibilité. Vos commentaires positifs montrent que vous savez reconnaître les bons produits !`;
        } else if (purchaseIntent > 70) {
          answer = `🎯 Parfait timing ! Votre activité récente indique une forte intention d'achat. ${topRecommendations[0]?.product.name} correspond exactement à vos recherches avec ${topRecommendations[0]?.score}% de compatibilité.`;
        } else {
          answer = `💡 Basé sur votre historique d'interactions, je vous recommande ${topRecommendations[0]?.product.name}. Score de compatibilité : ${topRecommendations[0]?.score}%.`;
        }
      } else {
        answer = `Basé sur vos préférences, je vous recommande particulièrement ${topRecommendations[0]?.product.name}. Ce produit a un score de compatibilité de ${topRecommendations[0]?.score}% avec votre profil.`;
      }

      relatedProducts = topRecommendations.map(r => r.product);
    } else if (lowerQuestion.includes('prix') || lowerQuestion.includes('budget')) {
      const budgetProducts = REAL_PRODUCTS.filter(p =>
        p.price >= preferences.priceRange.min && p.price <= preferences.priceRange.max
      ).slice(0, 3);

      // Personnaliser selon le profil comportemental
      if (behaviorProfile) {
        const purchaseIntent = behaviorProfile.purchaseIntent.score;
        const sentiment = behaviorProfile.sentimentAnalysis.overallSentiment;

        if (purchaseIntent > 80) {
          answer = `💰 Votre profil indique une forte intention d'achat ! Votre budget optimal semble être ${Math.round(preferences.priceRange.min)} - ${Math.round(preferences.priceRange.max)} F CFA. Voici mes meilleures recommandations dans cette gamme.`;
          confidence = 95;
        } else if (sentiment === 'negative') {
          answer = `🤔 Je comprends votre prudence. Voici des options sûres dans votre budget (${Math.round(preferences.priceRange.min)} - ${Math.round(preferences.priceRange.max)} F CFA) avec d'excellents avis.`;
          confidence = 80;
        } else {
          answer = `💡 Basé sur vos interactions passées, votre budget optimal est ${Math.round(preferences.priceRange.min)} - ${Math.round(preferences.priceRange.max)} F CFA. Voici les meilleures options.`;
        }
      } else {
        answer = `Selon votre budget habituel (${Math.round(preferences.priceRange.min)} - ${Math.round(preferences.priceRange.max)} F CFA), voici les meilleures options disponibles.`;
      }

      relatedProducts = budgetProducts;
    } else if (lowerQuestion.includes('qualité') || lowerQuestion.includes('meilleur')) {
      const qualityProducts = REAL_PRODUCTS
        .filter(p => p.averageRating >= 4.5)
        .sort((a, b) => b.averageRating - a.averageRating)
        .slice(0, 3);
      answer = `Pour la meilleure qualité, je recommande ces produits avec d'excellentes notes et certifications.`;
      relatedProducts = qualityProducts;
    } else if (lowerQuestion.includes('beauté') || lowerQuestion.includes('cosmétique')) {
      const beautyProducts = REAL_PRODUCTS.filter(p => p.category === 'Beauté').slice(0, 3);
      answer = `Voici ma sélection de produits de beauté, choisis selon vos préférences et les avis positifs.`;
      relatedProducts = beautyProducts;
    } else if (lowerQuestion.includes('électronique') || lowerQuestion.includes('tech')) {
      const techProducts = REAL_PRODUCTS.filter(p => p.category === 'Électronique').slice(0, 3);
      answer = `Pour l'électronique, voici les produits les mieux notés qui correspondent à vos critères.`;
      relatedProducts = techProducts;
    } else if (lowerQuestion.includes('boisson') || lowerQuestion.includes('boire') || lowerQuestion.includes('drink')) {
      const drinkProducts = REAL_PRODUCTS.filter(p =>
        p.category === 'Alimentation' ||
        p.name.toLowerCase().includes('jus') ||
        p.name.toLowerCase().includes('eau') ||
        p.name.toLowerCase().includes('thé') ||
        p.name.toLowerCase().includes('café') ||
        p.description.toLowerCase().includes('boisson')
      ).slice(0, 3);

      if (drinkProducts.length > 0) {
        answer = `🥤 Voici mes recommandations de boissons disponibles sur la plateforme. Ces produits ont de bons avis et correspondent à différents goûts.`;
        relatedProducts = drinkProducts;
      } else {
        answer = `🥤 Je n'ai pas trouvé de boissons spécifiques dans notre catalogue actuel, mais je peux vous recommander des produits alimentaires de qualité. Vous pouvez aussi chercher "jus", "eau" ou "thé" dans la barre de recherche.`;
        relatedProducts = REAL_PRODUCTS.filter(p => p.category === 'Alimentation').slice(0, 3);
      }
    } else if (this.containsProductName(lowerQuestion)) {
      // Analyser une question sur un produit spécifique
      const productAnalysis = await this.analyzeSpecificProduct(lowerQuestion, userId);
      answer = productAnalysis.answer;
      relatedProducts = productAnalysis.relatedProducts;
      confidence = productAnalysis.confidence;
    } else {
      // Réponse générale
      answer = `Je peux vous aider à trouver les meilleurs produits selon vos préférences. Vos catégories favorites sont : ${preferences.favoriteCategories.join(', ')}. Que recherchez-vous spécifiquement ?`;
      const generalRecs = await this.getPersonalizedRecommendations(userId, 3);
      relatedProducts = generalRecs.map(r => r.product);
      confidence = 70;
    }

    return {
      question,
      answer,
      relatedProducts,
      confidence,
      timestamp: new Date()
    };
  }

  // Obtenir les tendances du marketplace
  static getTrendingRecommendations(): IAIRecommendation[] {
    return REAL_PRODUCTS
      .map(product => ({
        id: `trend-${product.id}`,
        product,
        score: product.averageRating * 20,
        reasons: [
          `Note moyenne : ${product.averageRating}/5`,
          'Produit populaire',
          'Tendance actuelle'
        ],
        category: 'trending' as const,
        confidence: 80,
        aiInsight: `Ce produit fait partie des tendances actuelles avec une note de ${product.averageRating}/5.`
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 6);
  }

  // Vérifier si la question contient un nom de produit
  static containsProductName(question: string): boolean {
    const lowerQuestion = question.toLowerCase();
    return REAL_PRODUCTS.some(product =>
      lowerQuestion.includes(product.name.toLowerCase()) ||
      product.name.toLowerCase().split(' ').some(word =>
        word.length > 3 && lowerQuestion.includes(word)
      )
    );
  }

  // Analyser un produit spécifique
  static async analyzeSpecificProduct(question: string, userId: string): Promise<{
    answer: string;
    relatedProducts: IProduct[];
    confidence: number;
  }> {
    const lowerQuestion = question.toLowerCase();

    // Trouver le produit mentionné
    const mentionedProduct = REAL_PRODUCTS.find(product =>
      lowerQuestion.includes(product.name.toLowerCase()) ||
      product.name.toLowerCase().split(' ').some(word =>
        word.length > 3 && lowerQuestion.includes(word)
      )
    );

    if (!mentionedProduct) {
      return {
        answer: "Je n'ai pas trouvé ce produit spécifique dans notre catalogue. Pouvez-vous me donner plus de détails ou vérifier l'orthographe ?",
        relatedProducts: [],
        confidence: 30
      };
    }

    // Analyser les commentaires du produit
    const productComments = getMockProductComments(mentionedProduct.id);

    // Calculer les statistiques
    const averageRating = mentionedProduct.averageRating;
    const totalComments = productComments.length;
    const positiveComments = productComments.filter(c => c.userRating && c.userRating >= 4).length;
    const verifiedPurchases = productComments.filter(c => c.isVerifiedPurchase).length;

    // Analyser le sentiment des commentaires
    let overallSentiment = 'neutre';
    if (positiveComments / Math.max(1, totalComments) > 0.7) {
      overallSentiment = 'très positif';
    } else if (positiveComments / Math.max(1, totalComments) > 0.5) {
      overallSentiment = 'positif';
    } else if (positiveComments / Math.max(1, totalComments) < 0.3) {
      overallSentiment = 'mitigé';
    }

    // Générer une analyse détaillée
    let analysis = `📊 **Analyse de ${mentionedProduct.name}** :\n\n`;

    // Note et popularité
    analysis += `⭐ **Note moyenne** : ${averageRating}/5 étoiles\n`;
    analysis += `💬 **Avis clients** : ${totalComments} commentaires (${verifiedPurchases} achats vérifiés)\n`;
    analysis += `😊 **Sentiment général** : ${overallSentiment}\n\n`;

    // Prix et rapport qualité-prix
    const priceCategory = mentionedProduct.price < 20000 ? 'abordable' :
                         mentionedProduct.price < 100000 ? 'moyen de gamme' : 'premium';
    analysis += `💰 **Prix** : ${new Intl.NumberFormat('fr-FR').format(mentionedProduct.price)} F CFA (${priceCategory})\n`;

    // Qualité
    if (mentionedProduct.qualityBadge) {
      analysis += `🏆 **Qualité certifiée** : ${mentionedProduct.qualityBadge}\n`;
    }

    // Points forts basés sur les commentaires
    analysis += `\n✅ **Points forts détectés** :\n`;
    if (averageRating >= 4.5) {
      analysis += `• Excellente satisfaction client\n`;
    }
    if (verifiedPurchases > totalComments * 0.6) {
      analysis += `• Nombreux achats vérifiés (${Math.round(verifiedPurchases/totalComments*100)}%)\n`;
    }
    if (mentionedProduct.negotiable) {
      analysis += `• Prix négociable\n`;
    }

    // Recommandation personnalisée
    const preferences = await this.analyzeUserPreferences(userId);
    const isInBudget = mentionedProduct.price >= preferences.priceRange.min &&
                      mentionedProduct.price <= preferences.priceRange.max;
    const isPreferredCategory = preferences.favoriteCategories.includes(mentionedProduct.category);

    analysis += `\n🎯 **Mon avis pour vous** :\n`;
    if (isInBudget && isPreferredCategory) {
      analysis += `• Parfait match avec vos préférences et budget !\n`;
    } else if (isInBudget) {
      analysis += `• Dans votre budget habituel\n`;
    } else if (isPreferredCategory) {
      analysis += `• Correspond à vos catégories préférées\n`;
    }

    if (averageRating >= 4.0 && positiveComments > totalComments * 0.6) {
      analysis += `• Je recommande ce produit basé sur les avis positifs\n`;
    } else if (averageRating < 3.5) {
      analysis += `• Soyez prudent, les avis sont mitigés\n`;
    }

    // Produits similaires
    const similarProducts = REAL_PRODUCTS
      .filter(p => p.id !== mentionedProduct.id && p.category === mentionedProduct.category)
      .sort((a, b) => b.averageRating - a.averageRating)
      .slice(0, 3);

    const confidence = Math.min(95, 60 + (totalComments * 5) + (averageRating * 5));

    return {
      answer: analysis,
      relatedProducts: [mentionedProduct, ...similarProducts],
      confidence
    };
  }
}
