import React from 'react';
import { Crown, Gift, Star, Shield, Clock, AlertTriangle, CheckCircle } from 'lucide-react';

interface SubscriptionBadgeSimpleProps {
  businessId: string;
  size?: 'sm' | 'md' | 'lg';
  showDetails?: boolean;
}

const SubscriptionBadgeSimple: React.FC<SubscriptionBadgeSimpleProps> = ({ 
  businessId, 
  size = 'md', 
  showDetails = false 
}) => {
  // Données simulées pour la démonstration
  const mockSubscriptionStatus = {
    isActive: true,
    plan: 'free-trial',
    planName: 'Essai Gratuit',
    daysRemaining: 7,
    isTrialPeriod: true
  };

  const getPlanConfig = (plan: string) => {
    switch (plan) {
      case 'free-trial':
        return {
          icon: <Gift size={size === 'sm' ? 14 : size === 'md' ? 16 : 20} />,
          color: 'bg-green-100 text-green-800 border-green-200',
          label: 'Essai Gratuit'
        };
      case 'starter':
        return {
          icon: <Star size={size === 'sm' ? 14 : size === 'md' ? 16 : 20} />,
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          label: 'Starter'
        };
      case 'professional':
        return {
          icon: <Crown size={size === 'sm' ? 14 : size === 'md' ? 16 : 20} />,
          color: 'bg-purple-100 text-purple-800 border-purple-200',
          label: 'Professional'
        };
      case 'enterprise':
        return {
          icon: <Shield size={size === 'sm' ? 14 : size === 'md' ? 16 : 20} />,
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          label: 'Enterprise'
        };
      default:
        return {
          icon: <Star size={size === 'sm' ? 14 : size === 'md' ? 16 : 20} />,
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          label: 'Plan Inconnu'
        };
    }
  };

  const getStatusIcon = () => {
    if (!mockSubscriptionStatus.isActive) {
      return <AlertTriangle size={size === 'sm' ? 12 : size === 'md' ? 14 : 16} className="text-red-500" />;
    }
    if (mockSubscriptionStatus.daysRemaining <= 3) {
      return <Clock size={size === 'sm' ? 12 : size === 'md' ? 14 : 16} className="text-yellow-500" />;
    }
    return <CheckCircle size={size === 'sm' ? 12 : size === 'md' ? 14 : 16} className="text-green-500" />;
  };

  const planConfig = getPlanConfig(mockSubscriptionStatus.plan);
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  };

  return (
    <div className="inline-flex items-center space-x-2">
      {/* Badge principal */}
      <span className={`inline-flex items-center space-x-1 rounded-full border font-medium ${planConfig.color} ${sizeClasses[size]}`}>
        {planConfig.icon}
        <span>{planConfig.label}</span>
      </span>

      {/* Indicateur de statut */}
      {showDetails && (
        <div className="flex items-center space-x-1">
          {getStatusIcon()}
          <span className={`font-medium ${
            !mockSubscriptionStatus.isActive ? 'text-red-600' :
            mockSubscriptionStatus.daysRemaining <= 3 ? 'text-yellow-600' :
            'text-green-600'
          } ${size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'}`}>
            {mockSubscriptionStatus.daysRemaining} jour{mockSubscriptionStatus.daysRemaining > 1 ? 's' : ''}
          </span>
        </div>
      )}

      {/* Alerte d'expiration */}
      {mockSubscriptionStatus.isTrialPeriod && mockSubscriptionStatus.daysRemaining <= 3 && (
        <span className={`inline-flex items-center space-x-1 px-2 py-0.5 rounded-full bg-yellow-100 text-yellow-800 border border-yellow-200 ${
          size === 'sm' ? 'text-xs' : 'text-sm'
        }`}>
          <AlertTriangle size={12} />
          <span>Expire bientôt</span>
        </span>
      )}
    </div>
  );
};

export default SubscriptionBadgeSimple;
