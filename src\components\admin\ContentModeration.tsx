import React, { useState, useEffect } from 'react';
import {
  MessageSquare,
  FileText,
  Star,
  Megaphone,
  Search,
  Filter,
  MoreVertical,
  Eye,
  CheckCircle,
  XCircle,
  Flag,
  Trash2,
  Edit,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  Tag,
  TrendingUp,
  Shield,
  Download,
  RefreshCw,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
  Image,
  ExternalLink
} from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import { AdminService } from '../../services/adminService';
import { ContentModerationAction } from '../../types/admin';
import { supabase } from '../../lib/supabase';

// Types pour le contenu à modérer
interface ContentItem {
  id: string;
  type: 'post' | 'comment' | 'review' | 'ad' | 'ad_comment';
  content: string;
  author: {
    id: string;
    username: string;
    email: string;
    profile_picture?: string;
    role: string;
  };
  target?: {
    id: string;
    name: string;
    type: string;
  };
  status: 'pending' | 'approved' | 'rejected' | 'flagged' | 'removed';
  is_flagged: boolean;
  flag_reason?: string;
  moderator_notes?: string;
  created_at: string;
  updated_at: string;
  metadata?: {
    rating?: number;
    images?: string[];
    business_name?: string;
    product_name?: string;
    category?: string;
    tags?: string[];
    reports_count?: number;
    likes_count?: number;
    replies_count?: number;
  };
}

interface ModerationStats {
  total: number;
  pending: number;
  flagged: number;
  approved: number;
  rejected: number;
  removed: number;
  todayProcessed: number;
}

const ContentModeration: React.FC = () => {
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedPriority, setSelectedPriority] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [showActionModal, setShowActionModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showBulkActionModal, setShowBulkActionModal] = useState(false);
  const [actionItem, setActionItem] = useState<ContentItem | null>(null);
  const [detailItem, setDetailItem] = useState<ContentItem | null>(null);
  const [stats, setStats] = useState<ModerationStats>({
    total: 0,
    pending: 0,
    flagged: 0,
    approved: 0,
    rejected: 0,
    removed: 0,
    todayProcessed: 0
  });

  useEffect(() => {
    loadContentItems();
  }, [sortBy, sortOrder]);

  const loadContentItems = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔍 Début du chargement du contenu...');

      // Charger les posts avec une requête simplifiée
      const { data: postsData, error: postsError } = await supabase
        .from('posts')
        .select(`
          id,
          type,
          description,
          business_name,
          product_name,
          category,
          rating,
          created_at,
          updated_at,
          user_id
        `)
        .order('created_at', { ascending: false })
        .limit(50);

      if (postsError) {
        console.error('Erreur lors du chargement des posts:', postsError);
        setError(`Erreur posts: ${postsError.message}`);
      } else {
        console.log('✅ Posts chargés:', postsData?.length || 0);
      }

      // Charger les commentaires de publicités avec requête simplifiée
      const { data: adCommentsData, error: adCommentsError } = await supabase
        .from('ad_comments')
        .select(`
          id,
          content,
          is_approved,
          is_flagged,
          flagged_reason,
          created_at,
          updated_at,
          user_id,
          campaign_id
        `)
        .order('created_at', { ascending: false })
        .limit(50);

      if (adCommentsError) {
        console.error('Erreur lors du chargement des commentaires:', adCommentsError);
        setError(`Erreur commentaires: ${adCommentsError.message}`);
      } else {
        console.log('✅ Commentaires pub chargés:', adCommentsData?.length || 0);
      }

      // Charger les commentaires marketplace avec requête simplifiée
      const { data: marketCommentsData, error: marketCommentsError } = await supabase
        .from('marketcomen')
        .select(`
          id,
          comment,
          is_approved,
          product_id,
          is_verified_purchase,
          created_at,
          updated_at,
          user_id
        `)
        .order('created_at', { ascending: false })
        .limit(50);

      if (marketCommentsError) {
        console.error('Erreur lors du chargement des commentaires marketplace:', marketCommentsError);
        setError(`Erreur marketplace: ${marketCommentsError.message}`);
      } else {
        console.log('✅ Commentaires marketplace chargés:', marketCommentsData?.length || 0);
      }

      // Récupérer les informations des utilisateurs
      const allUserIds = new Set<string>();

      // Collecter tous les user_ids
      postsData?.forEach(post => post.user_id && allUserIds.add(post.user_id));
      adCommentsData?.forEach(comment => comment.user_id && allUserIds.add(comment.user_id));
      marketCommentsData?.forEach(comment => comment.user_id && allUserIds.add(comment.user_id));

      console.log('👥 IDs utilisateurs trouvés:', allUserIds.size);

      // Charger les informations des utilisateurs
      let usersData: any[] = [];
      if (allUserIds.size > 0) {
        const { data: userData, error: userError } = await supabase
          .from('profiles')
          .select('id, username, email, profile_picture, role')
          .in('id', Array.from(allUserIds));

        if (userError) {
          console.error('Erreur lors du chargement des utilisateurs:', userError);
        } else {
          usersData = userData || [];
          console.log('✅ Utilisateurs chargés:', usersData.length);
        }
      }

      // Créer un map des utilisateurs pour un accès rapide
      const usersMap = new Map();
      usersData.forEach(user => {
        usersMap.set(user.id, user);
      });

      // Transformer les données en format uniforme
      const allContent: ContentItem[] = [];

      // Ajouter les posts
      if (postsData && postsData.length > 0) {
        console.log('📝 Traitement des posts:', postsData.length);
        postsData.forEach(post => {
          const user = usersMap.get(post.user_id);
          allContent.push({
            id: post.id,
            type: 'post',
            content: post.description || '',
            author: {
              id: post.user_id,
              username: user?.username || 'Utilisateur supprimé',
              email: user?.email || '',
              profile_picture: user?.profile_picture,
              role: user?.role || 'standard'
            },
            status: 'approved', // Les posts sont généralement approuvés par défaut
            is_flagged: false,
            created_at: post.created_at,
            updated_at: post.updated_at,
            metadata: {
              rating: post.rating,
              images: post.images || [],
              business_name: post.business_name,
              product_name: post.product_name,
              category: post.category,
              tags: post.tags || []
            }
          });
        });
      }

      // Ajouter les commentaires de publicités
      if (adCommentsData && adCommentsData.length > 0) {
        console.log('💬 Traitement des commentaires pub:', adCommentsData.length);
        adCommentsData.forEach(comment => {
          const user = usersMap.get(comment.user_id);
          allContent.push({
            id: comment.id,
            type: 'ad_comment',
            content: comment.content,
            author: {
              id: comment.user_id,
              username: user?.username || 'Utilisateur supprimé',
              email: user?.email || '',
              profile_picture: user?.profile_picture,
              role: user?.role || 'standard'
            },
            target: {
              id: comment.campaign_id,
              name: `Publicité ${comment.campaign_id}`,
              type: 'ad_campaign'
            },
            status: comment.is_approved ? 'approved' : 'pending',
            is_flagged: comment.is_flagged || false,
            flag_reason: comment.flagged_reason,
            created_at: comment.created_at,
            updated_at: comment.updated_at
          });
        });
      }

      // Ajouter les commentaires marketplace
      if (marketCommentsData && marketCommentsData.length > 0) {
        console.log('⭐ Traitement des avis marketplace:', marketCommentsData.length);
        marketCommentsData.forEach(comment => {
          const user = usersMap.get(comment.user_id);
          allContent.push({
            id: comment.id,
            type: 'review',
            content: comment.comment,
            author: {
              id: comment.user_id,
              username: user?.username || 'Utilisateur supprimé',
              email: user?.email || '',
              profile_picture: user?.profile_picture,
              role: user?.role || 'standard'
            },
            target: {
              id: comment.product_id,
              name: `Produit ${comment.product_id}`,
              type: 'product'
            },
            status: comment.is_approved ? 'approved' : 'pending',
            is_flagged: false,
            created_at: comment.created_at,
            updated_at: comment.updated_at,
            metadata: {
              is_verified_purchase: comment.is_verified_purchase
            }
          });
        });
      }

      // Trier le contenu
      allContent.sort((a, b) => {
        const aValue = a[sortBy as keyof ContentItem] as string;
        const bValue = b[sortBy as keyof ContentItem] as string;
        if (sortOrder === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });

      console.log('📊 Contenu total traité:', allContent.length);
      setContentItems(allContent);

      // Calculer les statistiques
      const newStats: ModerationStats = {
        total: allContent.length,
        pending: allContent.filter(item => item.status === 'pending').length,
        flagged: allContent.filter(item => item.is_flagged).length,
        approved: allContent.filter(item => item.status === 'approved').length,
        rejected: allContent.filter(item => item.status === 'rejected').length,
        removed: allContent.filter(item => item.status === 'removed').length,
        todayProcessed: allContent.filter(item => {
          const today = new Date().toDateString();
          return new Date(item.updated_at).toDateString() === today;
        }).length
      };

      console.log('📈 Statistiques calculées:', newStats);
      setStats(newStats);

      // Si aucun contenu n'est trouvé, afficher un message informatif
      if (allContent.length === 0) {
        console.warn('⚠️ Aucun contenu trouvé. Vérifiez que les tables contiennent des données.');
        setError('Aucun contenu trouvé. Les tables semblent vides ou inaccessibles.');
      }

    } catch (error) {
      console.error('Erreur dans loadContentItems:', error);
      setError(`Erreur inattendue: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    } finally {
      setLoading(false);
    }
  };

  // Filtrage du contenu
  const filteredContent = contentItems.filter(item => {
    const matchesSearch = !searchTerm ||
      item.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.author.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.author.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.metadata?.business_name && item.metadata.business_name.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesType = selectedType === 'all' || item.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || item.status === selectedStatus;
    const matchesPriority = selectedPriority === 'all' ||
      (selectedPriority === 'flagged' && item.is_flagged) ||
      (selectedPriority === 'pending' && item.status === 'pending') ||
      (selectedPriority === 'high' && (item.is_flagged || item.status === 'pending'));

    return matchesSearch && matchesType && matchesStatus && matchesPriority;
  });

  // Pagination
  const totalPages = Math.ceil(filteredContent.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedContent = filteredContent.slice(startIndex, startIndex + itemsPerPage);

  const handleContentAction = async (action: ContentModerationAction) => {
    try {
      const success = await AdminService.executeContentModerationAction(action);
      if (success) {
        await loadContentItems();
        setShowActionModal(false);
        setActionItem(null);
      }
    } catch (error) {
      console.error('Erreur lors de l\'action de modération:', error);
    }
  };

  const handleBulkAction = async (actionType: string) => {
    if (selectedItems.length === 0) return;

    const reason = prompt(`Raison pour l'action "${actionType}" sur ${selectedItems.length} éléments:`, '');
    if (!reason) return;

    try {
      for (const itemId of selectedItems) {
        const item = contentItems.find(c => c.id === itemId);
        if (item) {
          await AdminService.executeContentModerationAction({
            contentId: itemId,
            contentType: item.type,
            action: actionType as any,
            reason
          });
        }
      }

      setSelectedItems([]);
      setShowBulkActionModal(false);
      await loadContentItems();
    } catch (error) {
      console.error('Erreur lors de l\'action en lot:', error);
    }
  };

  const exportData = () => {
    const csvContent = [
      ['ID', 'Type', 'Contenu', 'Auteur', 'Statut', 'Signalé', 'Date création'].join(','),
      ...filteredContent.map(item => [
        item.id,
        item.type,
        `"${item.content.replace(/"/g, '""').substring(0, 100)}..."`,
        item.author.username,
        item.status,
        item.is_flagged ? 'Oui' : 'Non',
        new Date(item.created_at).toLocaleDateString('fr-FR')
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `moderation_content_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'post': return <FileText size={16} className="text-blue-500" />;
      case 'comment': return <MessageSquare size={16} className="text-green-500" />;
      case 'review': return <Star size={16} className="text-yellow-500" />;
      case 'ad': return <Megaphone size={16} className="text-purple-500" />;
      case 'ad_comment': return <MessageCircle size={16} className="text-orange-500" />;
      default: return <FileText size={16} className="text-gray-500" />;
    }
  };

  const getContentTypeLabel = (type: string) => {
    switch (type) {
      case 'post': return 'Publication';
      case 'comment': return 'Commentaire';
      case 'review': return 'Avis produit';
      case 'ad': return 'Publicité';
      case 'ad_comment': return 'Commentaire pub';
      default: return type;
    }
  };

  const getStatusColor = (status: string, isFlagged: boolean) => {
    if (isFlagged) return 'bg-red-100 text-red-800';
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'removed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string, isFlagged: boolean) => {
    if (isFlagged) return 'Signalé';
    switch (status) {
      case 'approved': return 'Approuvé';
      case 'pending': return 'En attente';
      case 'rejected': return 'Rejeté';
      case 'removed': return 'Supprimé';
      default: return status;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Chargement du contenu à modérer...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Modération de Contenu</h1>
          <p className="text-gray-600 mt-2">Gérer et modérer le contenu de la plateforme</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            leftIcon={<Download size={16} />}
            onClick={exportData}
            disabled={filteredContent.length === 0}
          >
            Exporter
          </Button>
          <Button
            variant="outline"
            leftIcon={<RefreshCw size={16} />}
            onClick={() => loadContentItems()}
          >
            Actualiser
          </Button>
          <Button
            variant="outline"
            leftIcon={<AlertTriangle size={16} />}
            onClick={() => {
              console.log('🔍 DEBUG - État actuel:');
              console.log('- contentItems:', contentItems.length);
              console.log('- filteredContent:', filteredContent.length);
              console.log('- stats:', stats);
              console.log('- error:', error);
              console.log('- loading:', loading);
              alert(`Debug Info:\n- Contenu total: ${contentItems.length}\n- Contenu filtré: ${filteredContent.length}\n- Erreur: ${error || 'Aucune'}\n- Chargement: ${loading ? 'Oui' : 'Non'}\n\nVoir console pour plus de détails`);
            }}
          >
            Debug
          </Button>
        </div>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card className="cursor-pointer hover:shadow-lg transition-shadow">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <div className="p-2 rounded-full bg-blue-100">
                <FileText size={20} className="text-blue-600" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">En attente</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
              <div className="p-2 rounded-full bg-yellow-100">
                <Clock size={20} className="text-yellow-600" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Signalés</p>
                <p className="text-2xl font-bold text-red-600">{stats.flagged}</p>
              </div>
              <div className="p-2 rounded-full bg-red-100">
                <Flag size={20} className="text-red-600" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Approuvés</p>
                <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
              </div>
              <div className="p-2 rounded-full bg-green-100">
                <CheckCircle size={20} className="text-green-600" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rejetés</p>
                <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
              </div>
              <div className="p-2 rounded-full bg-red-100">
                <XCircle size={20} className="text-red-600" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Traités aujourd'hui</p>
                <p className="text-2xl font-bold text-blue-600">{stats.todayProcessed}</p>
              </div>
              <div className="p-2 rounded-full bg-blue-100">
                <TrendingUp size={20} className="text-blue-600" />
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Affichage des erreurs */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardBody className="p-4">
            <div className="flex items-center space-x-2 text-red-700">
              <AlertTriangle size={20} />
              <div>
                <h3 className="font-medium">Erreur de chargement</h3>
                <p className="text-sm">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => {
                    setError(null);
                    loadContentItems();
                  }}
                >
                  Réessayer
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Filtres et recherche */}
      <Card>
        <CardBody className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Recherche */}
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Rechercher par contenu, auteur, email..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Filtre par type */}
            <div>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
              >
                <option value="all">Tous les types</option>
                <option value="post">Publications</option>
                <option value="ad_comment">Commentaires pub</option>
                <option value="review">Avis produits</option>
                <option value="comment">Commentaires</option>
              </select>
            </div>

            {/* Filtre par statut */}
            <div>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
              >
                <option value="all">Tous les statuts</option>
                <option value="pending">En attente</option>
                <option value="approved">Approuvés</option>
                <option value="rejected">Rejetés</option>
                <option value="removed">Supprimés</option>
              </select>
            </div>

            {/* Filtre par priorité */}
            <div>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={selectedPriority}
                onChange={(e) => setSelectedPriority(e.target.value)}
              >
                <option value="all">Toutes priorités</option>
                <option value="high">Priorité haute</option>
                <option value="flagged">Signalés</option>
                <option value="pending">En attente</option>
              </select>
            </div>
          </div>

          {/* Actions en lot */}
          {selectedItems.length > 0 && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-900">
                  {selectedItems.length} élément(s) sélectionné(s)
                </span>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowBulkActionModal(true)}
                  >
                    Actions en lot
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSelectedItems([])}
                  >
                    Désélectionner
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Liste de contenu */}
      <Card>
        <CardBody className="p-0">
          {paginatedContent.length === 0 ? (
            <div className="text-center py-12">
              <MessageSquare size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun contenu trouvé</h3>
              {contentItems.length === 0 ? (
                <div className="space-y-3">
                  <p className="text-gray-600">Aucun contenu n'est disponible pour la modération.</p>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-md mx-auto">
                    <p className="text-sm text-yellow-800 mb-2">
                      <strong>Suggestions :</strong>
                    </p>
                    <ul className="text-sm text-yellow-700 space-y-1 text-left">
                      <li>• Vérifiez que les tables contiennent des données</li>
                      <li>• Exécutez le script de création de contenu de test</li>
                      <li>• Vérifiez les permissions d'accès aux tables</li>
                      <li>• Consultez la console pour les erreurs</li>
                    </ul>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => {
                      console.log('🔄 Rechargement forcé du contenu...');
                      loadContentItems();
                    }}
                    className="mt-4"
                  >
                    Recharger le contenu
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  <p className="text-gray-600">Aucun contenu ne correspond aux critères de recherche.</p>
                  <div className="flex justify-center space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSearchTerm('');
                        setSelectedType('all');
                        setSelectedStatus('all');
                        setSelectedPriority('all');
                      }}
                    >
                      Réinitialiser les filtres
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <>
              {/* En-tête du tableau */}
              <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    className="mr-4"
                    checked={selectedItems.length === paginatedContent.length}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedItems(paginatedContent.map(item => item.id));
                      } else {
                        setSelectedItems([]);
                      }
                    }}
                  />
                  <div className="grid grid-cols-12 gap-4 w-full text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="col-span-1">Type</div>
                    <div className="col-span-4">Contenu</div>
                    <div className="col-span-2">Auteur</div>
                    <div className="col-span-2">Cible</div>
                    <div className="col-span-1">Statut</div>
                    <div className="col-span-1">Date</div>
                    <div className="col-span-1">Actions</div>
                  </div>
                </div>
              </div>

              {/* Lignes de contenu */}
              <div className="divide-y divide-gray-200">
                {paginatedContent.map((item) => (
                  <div key={item.id} className="px-6 py-4 hover:bg-gray-50">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        className="mr-4"
                        checked={selectedItems.includes(item.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedItems([...selectedItems, item.id]);
                          } else {
                            setSelectedItems(selectedItems.filter(id => id !== item.id));
                          }
                        }}
                      />
                      <div className="grid grid-cols-12 gap-4 w-full">
                        {/* Type */}
                        <div className="col-span-1">
                          <div className="flex items-center space-x-2">
                            {getContentTypeIcon(item.type)}
                            <span className="text-xs font-medium text-gray-600">
                              {getContentTypeLabel(item.type)}
                            </span>
                          </div>
                        </div>

                        {/* Contenu */}
                        <div className="col-span-4">
                          <div className="flex items-start space-x-2">
                            <div className="flex-1">
                              <p className="text-sm text-gray-900 line-clamp-2">
                                {item.content.substring(0, 150)}
                                {item.content.length > 150 && '...'}
                              </p>
                              {item.metadata?.business_name && (
                                <p className="text-xs text-gray-500 mt-1">
                                  Entreprise: {item.metadata.business_name}
                                </p>
                              )}
                              {item.metadata?.rating && (
                                <div className="flex items-center mt-1">
                                  <Star size={12} className="text-yellow-400 fill-current" />
                                  <span className="text-xs text-gray-500 ml-1">
                                    {item.metadata.rating}/5
                                  </span>
                                </div>
                              )}
                              {item.is_flagged && (
                                <div className="flex items-center mt-1">
                                  <Flag size={12} className="text-red-500" />
                                  <span className="text-xs text-red-600 ml-1">
                                    {item.flag_reason || 'Signalé'}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Auteur */}
                        <div className="col-span-2">
                          <div className="flex items-center space-x-2">
                            <img
                              src={item.author.profile_picture || '/default-avatar.png'}
                              alt={item.author.username}
                              className="w-8 h-8 rounded-full object-cover"
                            />
                            <div>
                              <p className="text-sm font-medium text-gray-900">
                                {item.author.username}
                              </p>
                              <p className="text-xs text-gray-500">
                                {item.author.role}
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Cible */}
                        <div className="col-span-2">
                          {item.target ? (
                            <div>
                              <p className="text-sm text-gray-900">
                                {item.target.name}
                              </p>
                              <p className="text-xs text-gray-500">
                                {item.target.type}
                              </p>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-400">-</span>
                          )}
                        </div>

                        {/* Statut */}
                        <div className="col-span-1">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(item.status, item.is_flagged)}`}>
                            {getStatusLabel(item.status, item.is_flagged)}
                          </span>
                        </div>

                        {/* Date */}
                        <div className="col-span-1">
                          <p className="text-xs text-gray-500">
                            {new Date(item.created_at).toLocaleDateString('fr-FR')}
                          </p>
                          <p className="text-xs text-gray-400">
                            {new Date(item.created_at).toLocaleTimeString('fr-FR', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </p>
                        </div>

                        {/* Actions */}
                        <div className="col-span-1">
                          <div className="flex items-center space-x-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setDetailItem(item);
                                setShowDetailModal(true);
                              }}
                            >
                              <Eye size={14} />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setActionItem(item);
                                setShowActionModal(true);
                              }}
                            >
                              <MoreVertical size={14} />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </CardBody>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Affichage de {startIndex + 1} à {Math.min(startIndex + itemsPerPage, filteredContent.length)} sur {filteredContent.length} éléments
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  Précédent
                </Button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                      >
                        {page}
                      </Button>
                    );
                  })}
                  {totalPages > 5 && (
                    <>
                      <span className="text-gray-500">...</span>
                      <Button
                        variant={currentPage === totalPages ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(totalPages)}
                      >
                        {totalPages}
                      </Button>
                    </>
                  )}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  Suivant
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Modals */}
      {showActionModal && actionItem && (
        <ContentActionModal
          content={actionItem}
          onClose={() => {
            setShowActionModal(false);
            setActionItem(null);
          }}
          onAction={handleContentAction}
        />
      )}

      {showDetailModal && detailItem && (
        <ContentDetailModal
          content={detailItem}
          onClose={() => {
            setShowDetailModal(false);
            setDetailItem(null);
          }}
        />
      )}

      {showBulkActionModal && (
        <BulkContentActionModal
          selectedCount={selectedItems.length}
          onClose={() => setShowBulkActionModal(false)}
          onAction={handleBulkAction}
        />
      )}
    </div>
  );
};

// Modal d'actions de modération
interface ContentActionModalProps {
  content: ContentItem;
  onClose: () => void;
  onAction: (action: ContentModerationAction) => void;
}

const ContentActionModal: React.FC<ContentActionModalProps> = ({ content, onClose, onAction }) => (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg p-6 w-full max-w-md">
      <h3 className="text-lg font-semibold mb-4">Actions de modération</h3>
      <div className="space-y-2">
        {content.status !== 'approved' && (
          <Button
            variant="outline"
            className="w-full justify-start text-green-600 hover:text-green-700"
            leftIcon={<CheckCircle size={16} />}
            onClick={() => {
              const reason = prompt('Raison de l\'approbation (optionnel):', '');
              onAction({
                contentId: content.id,
                contentType: content.type,
                action: 'approve',
                reason: reason || 'Contenu approuvé par modérateur'
              });
              onClose();
            }}
          >
            Approuver le contenu
          </Button>
        )}

        {content.status !== 'rejected' && (
          <Button
            variant="outline"
            className="w-full justify-start text-red-600 hover:text-red-700"
            leftIcon={<XCircle size={16} />}
            onClick={() => {
              const reason = prompt('Raison du rejet:', '');
              if (reason) {
                onAction({
                  contentId: content.id,
                  contentType: content.type,
                  action: 'reject',
                  reason
                });
                onClose();
              }
            }}
          >
            Rejeter le contenu
          </Button>
        )}

        {!content.is_flagged && (
          <Button
            variant="outline"
            className="w-full justify-start text-orange-600 hover:text-orange-700"
            leftIcon={<Flag size={16} />}
            onClick={() => {
              const reason = prompt('Raison du signalement:', '');
              if (reason) {
                onAction({
                  contentId: content.id,
                  contentType: content.type,
                  action: 'flag',
                  reason
                });
                onClose();
              }
            }}
          >
            Signaler le contenu
          </Button>
        )}

        <Button
          variant="outline"
          className="w-full justify-start text-red-600 hover:text-red-700"
          leftIcon={<Trash2 size={16} />}
          onClick={() => {
            const reason = prompt('Raison de la suppression:', '');
            if (reason && window.confirm('Êtes-vous sûr de vouloir supprimer ce contenu ?')) {
              onAction({
                contentId: content.id,
                contentType: content.type,
                action: 'remove',
                reason
              });
              onClose();
            }
          }}
        >
          Supprimer le contenu
        </Button>

        <Button
          variant="outline"
          className="w-full justify-start text-gray-600 hover:text-gray-700"
          leftIcon={<Eye size={16} />}
          onClick={() => {
            // Ouvrir le contenu dans le contexte approprié
            if (content.type === 'post') {
              window.open(`/post/${content.id}`, '_blank');
            } else if (content.type === 'ad_comment' && content.target) {
              window.open(`/ad/${content.target.id}`, '_blank');
            }
          }}
        >
          Voir dans le contexte
        </Button>
      </div>

      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 mb-3">
          <strong>Informations:</strong><br/>
          • Type: {getContentTypeLabel(content.type)}<br/>
          • Auteur: {content.author.username} ({content.author.role})<br/>
          • Statut: {getStatusLabel(content.status, content.is_flagged)}<br/>
          • Créé le: {new Date(content.created_at).toLocaleDateString('fr-FR')}<br/>
          {content.target && `• Cible: ${content.target.name}`}
        </div>

        <Button variant="outline" onClick={onClose} className="w-full">
          Fermer
        </Button>
      </div>
    </div>
  </div>
);

// Modal de détails du contenu
interface ContentDetailModalProps {
  content: ContentItem;
  onClose: () => void;
}

const ContentDetailModal: React.FC<ContentDetailModalProps> = ({ content, onClose }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            {getContentTypeIcon(content.type)}
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                Détails du {getContentTypeLabel(content.type).toLowerCase()}
              </h2>
              <p className="text-gray-600">ID: {content.id}</p>
            </div>
          </div>
          <Button variant="outline" onClick={onClose}>
            <XCircle size={20} />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Contenu principal */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Contenu</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-gray-900 whitespace-pre-wrap">{content.content}</p>
            </div>
          </div>

          {/* Informations de l'auteur */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Auteur</h3>
            <div className="flex items-center space-x-4 bg-gray-50 p-4 rounded-lg">
              <img
                src={content.author.profile_picture || '/default-avatar.png'}
                alt={content.author.username}
                className="w-12 h-12 rounded-full object-cover"
              />
              <div>
                <p className="font-medium text-gray-900">{content.author.username}</p>
                <p className="text-sm text-gray-600">{content.author.email}</p>
                <p className="text-sm text-gray-500">Rôle: {content.author.role}</p>
              </div>
            </div>
          </div>

          {/* Cible (si applicable) */}
          {content.target && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Cible</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="font-medium text-gray-900">{content.target.name}</p>
                <p className="text-sm text-gray-600">Type: {content.target.type}</p>
                <p className="text-sm text-gray-500">ID: {content.target.id}</p>
              </div>
            </div>
          )}

          {/* Métadonnées */}
          {content.metadata && Object.keys(content.metadata).length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Métadonnées</h3>
              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                {content.metadata.rating && (
                  <div className="flex items-center space-x-2">
                    <Star size={16} className="text-yellow-400 fill-current" />
                    <span>Note: {content.metadata.rating}/5</span>
                  </div>
                )}
                {content.metadata.business_name && (
                  <p><strong>Entreprise:</strong> {content.metadata.business_name}</p>
                )}
                {content.metadata.product_name && (
                  <p><strong>Produit:</strong> {content.metadata.product_name}</p>
                )}
                {content.metadata.category && (
                  <p><strong>Catégorie:</strong> {content.metadata.category}</p>
                )}
                {content.metadata.tags && content.metadata.tags.length > 0 && (
                  <div>
                    <strong>Tags:</strong>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {content.metadata.tags.map((tag, index) => (
                        <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                {content.metadata.images && content.metadata.images.length > 0 && (
                  <div>
                    <strong>Images:</strong>
                    <div className="grid grid-cols-4 gap-2 mt-2">
                      {content.metadata.images.map((image, index) => (
                        <img
                          key={index}
                          src={image}
                          alt={`Image ${index + 1}`}
                          className="w-full h-20 object-cover rounded"
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Statut et modération */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Statut de modération</h3>
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <div className="flex items-center space-x-2">
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(content.status, content.is_flagged)}`}>
                  {getStatusLabel(content.status, content.is_flagged)}
                </span>
              </div>
              {content.is_flagged && content.flag_reason && (
                <p><strong>Raison du signalement:</strong> {content.flag_reason}</p>
              )}
              {content.moderator_notes && (
                <p><strong>Notes du modérateur:</strong> {content.moderator_notes}</p>
              )}
              <p><strong>Créé le:</strong> {formatDate(content.created_at)}</p>
              <p><strong>Modifié le:</strong> {formatDate(content.updated_at)}</p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <Button onClick={onClose}>
            Fermer
          </Button>
        </div>
      </div>
    </div>
  );
};

// Modal d'actions en lot
interface BulkContentActionModalProps {
  selectedCount: number;
  onClose: () => void;
  onAction: (action: string) => void;
}

const BulkContentActionModal: React.FC<BulkContentActionModalProps> = ({ selectedCount, onClose, onAction }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Actions en lot ({selectedCount} éléments)</h3>

        <div className="space-y-2">
          <Button
            variant="outline"
            className="w-full justify-start text-green-600 hover:text-green-700"
            onClick={() => onAction('approve')}
          >
            <CheckCircle size={16} className="mr-2" />
            Approuver tout le contenu sélectionné
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start text-red-600 hover:text-red-700"
            onClick={() => onAction('reject')}
          >
            <XCircle size={16} className="mr-2" />
            Rejeter tout le contenu sélectionné
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start text-orange-600 hover:text-orange-700"
            onClick={() => onAction('flag')}
          >
            <Flag size={16} className="mr-2" />
            Signaler tout le contenu sélectionné
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start text-red-600 hover:text-red-700"
            onClick={() => onAction('remove')}
          >
            <Trash2 size={16} className="mr-2" />
            Supprimer tout le contenu sélectionné
          </Button>
        </div>

        <div className="flex justify-end space-x-2 mt-6">
          <Button variant="outline" onClick={onClose}>
            Annuler
          </Button>
        </div>
      </div>
    </div>
  );
};

// Fonctions utilitaires (déplacées ici pour éviter les erreurs)
const getContentTypeIcon = (type: string) => {
  switch (type) {
    case 'post': return <FileText size={16} className="text-blue-500" />;
    case 'comment': return <MessageSquare size={16} className="text-green-500" />;
    case 'review': return <Star size={16} className="text-yellow-500" />;
    case 'ad': return <Megaphone size={16} className="text-purple-500" />;
    case 'ad_comment': return <MessageCircle size={16} className="text-orange-500" />;
    default: return <FileText size={16} className="text-gray-500" />;
  }
};

const getContentTypeLabel = (type: string) => {
  switch (type) {
    case 'post': return 'Publication';
    case 'comment': return 'Commentaire';
    case 'review': return 'Avis produit';
    case 'ad': return 'Publicité';
    case 'ad_comment': return 'Commentaire pub';
    default: return type;
  }
};

const getStatusColor = (status: string, isFlagged: boolean) => {
  if (isFlagged) return 'bg-red-100 text-red-800';
  switch (status) {
    case 'approved': return 'bg-green-100 text-green-800';
    case 'pending': return 'bg-yellow-100 text-yellow-800';
    case 'rejected': return 'bg-red-100 text-red-800';
    case 'removed': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getStatusLabel = (status: string, isFlagged: boolean) => {
  if (isFlagged) return 'Signalé';
  switch (status) {
    case 'approved': return 'Approuvé';
    case 'pending': return 'En attente';
    case 'rejected': return 'Rejeté';
    case 'removed': return 'Supprimé';
    default: return status;
  }
};

export default ContentModeration;