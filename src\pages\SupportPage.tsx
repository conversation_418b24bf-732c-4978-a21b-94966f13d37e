import React, { useState, useEffect } from 'react';
import {
  ChevronDown,
  ChevronUp,
  Mail,
  Phone,
  MessageSquare,
  Search,
  Book,
  Video,
  FileText,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  HelpCircle,
  ExternalLink,
  Download,
  Star,
  MessageCircle,
  Headphones,
  Ticket,
  Send,
  ThumbsUp,
  ThumbsDown,
  Eye,
  Filter,
  Plus,
  Calendar,
  Tag,
  Archive,
  Bell,
  Globe,
  Zap,
  Award,
  TrendingUp,
  BarChart3,
  X
} from 'lucide-react';
import Button from '../components/ui/Button';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import { useAuth } from '../context/AuthContext';
import {
  SupportService,
  ChatConversation,
  ChatMessage,
  SupportTicket,
  TicketMessage,
  CommunityPost
} from '../services/supportService';

interface FAQItemProps {
  question: string;
  answer: string;
  helpful?: number;
  notHelpful?: number;
  onRate?: (helpful: boolean) => void;
}

// Les interfaces sont maintenant importées du service

const FAQItem: React.FC<FAQItemProps> = ({ question, answer, helpful = 0, notHelpful = 0, onRate }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [userRating, setUserRating] = useState<'helpful' | 'not-helpful' | null>(null);

  const handleRate = (isHelpful: boolean) => {
    setUserRating(isHelpful ? 'helpful' : 'not-helpful');
    onRate?.(isHelpful);
  };

  return (
    <div className="border-b border-gray-200 py-4">
      <button
        className="flex justify-between items-center w-full text-left text-gray-800 hover:text-blue-600 focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="font-medium">{question}</span>
        {isOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </button>
      {isOpen && (
        <div className="mt-3">
          <div className="text-gray-600 text-sm mb-4">
            <p>{answer}</p>
          </div>

          {/* Rating Section */}
          <div className="flex items-center justify-between pt-3 border-t border-gray-100">
            <span className="text-sm text-gray-500">Cette réponse vous a-t-elle aidé ?</span>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => handleRate(true)}
                className={`flex items-center space-x-1 px-3 py-1 rounded-full text-sm transition-colors ${
                  userRating === 'helpful'
                    ? 'bg-green-100 text-green-700'
                    : 'text-gray-500 hover:bg-green-50 hover:text-green-600'
                }`}
              >
                <ThumbsUp className="w-4 h-4" />
                <span>{helpful}</span>
              </button>
              <button
                onClick={() => handleRate(false)}
                className={`flex items-center space-x-1 px-3 py-1 rounded-full text-sm transition-colors ${
                  userRating === 'not-helpful'
                    ? 'bg-red-100 text-red-700'
                    : 'text-gray-500 hover:bg-red-50 hover:text-red-600'
                }`}
              >
                <ThumbsDown className="w-4 h-4" />
                <span>{notHelpful}</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const SupportPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState<'faq' | 'guides' | 'contact' | 'status' | 'tickets' | 'community'>('faq');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFaqCategory, setSelectedFaqCategory] = useState('toutes');
  const [chatOpen, setChatOpen] = useState(false);
  const [currentConversation, setCurrentConversation] = useState<ChatConversation | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [newChatMessage, setNewChatMessage] = useState('');
  const [contactForm, setContactForm] = useState({
    email: currentUser?.email || '',
    subject: '',
    category: 'general',
    message: '',
    priority: 'normal'
  });
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [ticketMessages, setTicketMessages] = useState<TicketMessage[]>([]);
  const [newTicketMessage, setNewTicketMessage] = useState('');
  const [communityPosts, setCommunityPosts] = useState<CommunityPost[]>([]);
  const [selectedCommunityCategory, setSelectedCommunityCategory] = useState('all');
  const [loading, setLoading] = useState(false);
  const [chatSubscription, setChatSubscription] = useState<any>(null);
  const [ticketSubscription, setTicketSubscription] = useState<any>(null);
  const [showNewTicketModal, setShowNewTicketModal] = useState(false);
  const [newTicketForm, setNewTicketForm] = useState({
    subject: '',
    description: '',
    category: 'general' as const,
    priority: 'normal' as const
  });
  const [showNewPostModal, setShowNewPostModal] = useState(false);
  const [newPostForm, setNewPostForm] = useState({
    title: '',
    content: '',
    category: 'general' as const
  });
  const [selectedPost, setSelectedPost] = useState<CommunityPost | null>(null);
  const [showPostDetail, setShowPostDetail] = useState(false);
  const [newReply, setNewReply] = useState('');

  // Chargement des données depuis la base de données
  useEffect(() => {
    if (currentUser?.id) {
      loadUserData();
    }
  }, [currentUser]);

  const loadUserData = async () => {
    if (!currentUser?.id) return;

    setLoading(true);
    try {
      // Charger les tickets de l'utilisateur
      const userTickets = await SupportService.getUserTickets(currentUser.id);
      setTickets(userTickets);

      // Charger les posts communautaires
      const posts = await SupportService.getCommunityPosts(
        selectedCommunityCategory === 'all' ? undefined : selectedCommunityCategory
      );
      setCommunityPosts(posts);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setLoading(false);
    }
  };

  // Recharger les posts communautaires quand la catégorie change
  useEffect(() => {
    if (activeTab === 'community') {
      loadCommunityPosts();
    }
  }, [selectedCommunityCategory, activeTab]);

  const loadCommunityPosts = async () => {
    try {
      const posts = await SupportService.getCommunityPosts(
        selectedCommunityCategory === 'all' ? undefined : selectedCommunityCategory
      );
      setCommunityPosts(posts);
    } catch (error) {
      console.error('Erreur lors du chargement des posts:', error);
    }
  };

  const faqs = [
    {
      question: "Comment puis-je modifier mon profil ?",
      answer: "Vous pouvez modifier votre profil en accédant à votre page de profil et en cliquant sur le bouton 'Modifier le profil'. Vous pourrez y changer votre photo, vos informations personnelles et vos préférences.",
      category: "profil",
      helpful: 23,
      notHelpful: 2
    },
    {
      question: "Comment puis-je poster un avis ?",
      answer: "Pour poster un avis, cliquez sur 'Créer un post' sur la page d'accueil. Choisissez le type d'avis (Coup de cœur ou Coup de gueule), remplissez les informations sur l'entreprise et le produit, donnez une note et décrivez votre expérience.",
      category: "avis",
      helpful: 18,
      notHelpful: 1
    },
    {
      question: "Que faire si j'oublie mon mot de passe ?",
      answer: "Sur la page de connexion, cliquez sur 'Mot de passe oublié ?'. Entrez votre adresse email et vous recevrez un lien pour réinitialiser votre mot de passe. Le lien est valide pendant 24 heures.",
      category: "compte",
      helpful: 31,
      notHelpful: 0
    },
    {
      question: "Comment fonctionne le système de recommandation ?",
      answer: "Vous pouvez recommander un post en cliquant sur l'étoile 'Recommander'. Les posts les plus recommandés apparaissent dans la section Recommandations et influencent le classement des produits.",
      category: "recommandations",
      helpful: 12,
      notHelpful: 3
    },
    {
      question: "Comment utiliser le marketplace ?",
      answer: "Le marketplace permet aux entreprises de vendre leurs produits. Vous pouvez parcourir les produits, les filtrer par catégorie, négocier les prix et passer commande. Seules les entreprises peuvent vendre sur le marketplace.",
      category: "marketplace",
      helpful: 27,
      notHelpful: 2
    },
    {
      question: "Qu'est-ce que l'IA de recommandation ?",
      answer: "L'IA analyse vos préférences, les avis que vous avez donnés et les tendances pour vous suggérer des produits et services qui pourraient vous intéresser. Plus vous utilisez la plateforme, plus les recommandations deviennent précises.",
      category: "ia",
      helpful: 19,
      notHelpful: 4
    },
    {
      question: "Comment devenir une entreprise vérifiée ?",
      answer: "Pour obtenir le statut d'entreprise vérifiée, vous devez fournir un document officiel d'enregistrement de votre entreprise, maintenir une note moyenne élevée et respecter nos conditions d'utilisation.",
      category: "business",
      helpful: 15,
      notHelpful: 1
    },
    {
      question: "Comment signaler un contenu inapproprié ?",
      answer: "Cliquez sur les trois points (...) à côté du contenu concerné et sélectionnez 'Signaler'. Choisissez la raison du signalement et notre équipe de modération examinera le contenu dans les 24 heures.",
      category: "moderation",
      helpful: 8,
      notHelpful: 0
    }
  ];

  const guides = [
    {
      title: "Guide de démarrage rapide",
      description: "Apprenez les bases de Customeroom en 5 minutes",
      icon: <Book className="w-6 h-6" />,
      duration: "5 min",
      type: "guide"
    },
    {
      title: "Comment créer un avis efficace",
      description: "Rédigez des avis utiles qui aident la communauté",
      icon: <FileText className="w-6 h-6" />,
      duration: "3 min",
      type: "guide"
    },
    {
      title: "Utiliser l'IA de recommandation",
      description: "Maximisez vos découvertes avec notre IA",
      icon: <Video className="w-6 h-6" />,
      duration: "7 min",
      type: "video"
    },
    {
      title: "Guide du marketplace",
      description: "Achetez et vendez en toute sécurité",
      icon: <Users className="w-6 h-6" />,
      duration: "10 min",
      type: "guide"
    }
  ];

  const systemStatus = [
    { service: "Plateforme principale", status: "operational", lastUpdate: "Il y a 2 minutes" },
    { service: "IA de recommandation", status: "operational", lastUpdate: "Il y a 5 minutes" },
    { service: "Marketplace", status: "operational", lastUpdate: "Il y a 1 minute" },
    { service: "Notifications", status: "maintenance", lastUpdate: "Il y a 30 minutes" },
    { service: "API", status: "operational", lastUpdate: "Il y a 3 minutes" }
  ];

  const filteredFaqs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.category.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory = selectedFaqCategory === 'toutes' ||
      faq.category.toLowerCase() === selectedFaqCategory.toLowerCase();

    return matchesSearch && matchesCategory;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'operational':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'maintenance':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'down':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <HelpCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'operational':
        return 'Opérationnel';
      case 'maintenance':
        return 'Maintenance';
      case 'down':
        return 'Indisponible';
      default:
        return 'Inconnu';
    }
  };

  // Fonctions utilitaires
  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Formulaire de contact soumis:', contactForm);
    alert('Votre message a été envoyé ! Nous vous répondrons dans les plus brefs délais.');
    setContactForm({
      email: currentUser?.email || '',
      subject: '',
      category: 'general',
      message: '',
      priority: 'normal'
    });
  };

  const handleChatSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newChatMessage.trim() || !currentUser?.id) return;

    try {
      // Créer une conversation si elle n'existe pas
      if (!currentConversation) {
        const conversation = await SupportService.createChatConversation(
          currentUser.id,
          'Support Chat'
        );
        setCurrentConversation(conversation);

        // S'abonner aux messages en temps réel
        const subscription = SupportService.subscribeToChatMessages(
          conversation.id,
          (message) => {
            setChatMessages(prev => [...prev, message]);
          }
        );
        setChatSubscription(subscription);

        // Charger les messages existants
        const messages = await SupportService.getConversationMessages(conversation.id);
        setChatMessages(messages);
      }

      // Envoyer le message
      if (currentConversation) {
        await SupportService.sendChatMessage(
          currentConversation.id,
          currentUser.id,
          newChatMessage
        );
        setNewChatMessage('');
      }
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error);
    }
  };

  const handleTicketReply = async (ticketId: string) => {
    if (!newTicketMessage.trim() || !currentUser?.id) return;

    try {
      await SupportService.addTicketMessage(
        ticketId,
        currentUser.id,
        newTicketMessage
      );
      setNewTicketMessage('');

      // Recharger les messages du ticket
      if (selectedTicket) {
        const messages = await SupportService.getTicketMessages(ticketId);
        setTicketMessages(messages);
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout du message:', error);
    }
  };

  // Fonction pour ouvrir un ticket et charger ses messages
  const openTicket = async (ticket: SupportTicket) => {
    setSelectedTicket(ticket);

    try {
      const messages = await SupportService.getTicketMessages(ticket.id);
      setTicketMessages(messages);

      // S'abonner aux nouveaux messages
      if (ticketSubscription) {
        SupportService.unsubscribe(ticketSubscription);
      }

      const subscription = SupportService.subscribeToTicketMessages(
        ticket.id,
        (message) => {
          setTicketMessages(prev => [...prev, message]);
        }
      );
      setTicketSubscription(subscription);
    } catch (error) {
      console.error('Erreur lors du chargement des messages:', error);
    }
  };

  // Fonction pour créer un nouveau ticket
  const createNewTicket = async (subject: string, description: string, category: any, priority: any) => {
    if (!currentUser?.id) return;

    try {
      const ticket = await SupportService.createSupportTicket(
        currentUser.id,
        subject,
        description,
        category,
        priority
      );

      // Recharger les tickets
      const userTickets = await SupportService.getUserTickets(currentUser.id);
      setTickets(userTickets);

      return ticket;
    } catch (error) {
      console.error('Erreur lors de la création du ticket:', error);
      throw error;
    }
  };

  // Fonction pour créer un nouveau post communautaire
  const createNewPost = async (title: string, content: string, category: string) => {
    if (!currentUser?.id) return;

    try {
      const post = await SupportService.createCommunityPost(
        currentUser.id,
        title,
        content,
        category
      );

      // Recharger les posts communautaires
      await loadCommunityPosts();

      return post;
    } catch (error) {
      console.error('Erreur lors de la création du post:', error);
      throw error;
    }
  };

  // Fonction pour voter sur un post
  const handleVotePost = async (postId: string, voteType: 'up' | 'down') => {
    if (!currentUser?.id) return;

    try {
      await SupportService.voteCommunityPost(currentUser.id, postId, voteType);

      // Recharger les posts pour voir les nouveaux votes
      await loadCommunityPosts();

      console.log(`Vote ${voteType} ajouté au post ${postId}`);
    } catch (error) {
      console.error('Erreur lors du vote:', error);
    }
  };

  // Fonction pour ouvrir les détails d'un post
  const openPostDetail = async (post: CommunityPost) => {
    setSelectedPost(post);
    setShowPostDetail(true);

    try {
      // Charger les réponses du post
      const replies = await SupportService.getCommunityReplies(post.id);
      console.log('Réponses chargées:', replies);
    } catch (error) {
      console.error('Erreur lors du chargement des réponses:', error);
    }
  };

  // Fonction pour ajouter une réponse
  const handleAddReply = async (postId: string) => {
    if (!currentUser?.id || !newReply.trim()) return;

    try {
      await SupportService.addCommunityReply(
        postId,
        currentUser.id,
        newReply.trim()
      );

      setNewReply('');

      // Recharger les posts pour voir le nouveau compteur de réponses
      await loadCommunityPosts();

      console.log('Réponse ajoutée avec succès');
    } catch (error) {
      console.error('Erreur lors de l\'ajout de la réponse:', error);
    }
  };

  // Nettoyer les abonnements
  useEffect(() => {
    return () => {
      if (chatSubscription) {
        SupportService.unsubscribe(chatSubscription);
      }
      if (ticketSubscription) {
        SupportService.unsubscribe(ticketSubscription);
      }
    };
  }, [chatSubscription, ticketSubscription]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'normal': return 'text-blue-600 bg-blue-100';
      case 'low': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'text-green-600 bg-green-100';
      case 'in-progress': return 'text-yellow-600 bg-yellow-100';
      case 'resolved': return 'text-blue-600 bg-blue-100';
      case 'closed': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-10 text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-3">Support & Aide</h1>
        <p className="text-lg text-gray-600">Nous sommes là pour vous aider. Trouvez des réponses à vos questions ou contactez notre équipe.</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardBody className="p-4 text-center">
            <Clock className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">&lt; 2h</div>
            <div className="text-sm text-gray-600">Temps de réponse moyen</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="p-4 text-center">
            <MessageCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">98%</div>
            <div className="text-sm text-gray-600">Satisfaction client</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="p-4 text-center">
            <Headphones className="w-8 h-8 text-purple-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">24/7</div>
            <div className="text-sm text-gray-600">Support disponible</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="p-4 text-center">
            <Users className="w-8 h-8 text-orange-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">50k+</div>
            <div className="text-sm text-gray-600">Utilisateurs aidés</div>
          </CardBody>
        </Card>
      </div>

      {/* Navigation Tabs */}
      <div className="mb-8">
        <nav className="flex flex-wrap gap-4 border-b border-gray-200">
          {[
            { id: 'faq', label: 'FAQ', icon: <HelpCircle className="w-5 h-5" /> },
            { id: 'guides', label: 'Guides', icon: <Book className="w-5 h-5" /> },
            { id: 'tickets', label: 'Mes Tickets', icon: <Ticket className="w-5 h-5" /> },
            { id: 'community', label: 'Communauté', icon: <Users className="w-5 h-5" /> },
            { id: 'contact', label: 'Contact', icon: <MessageSquare className="w-5 h-5" /> },
            { id: 'status', label: 'Statut', icon: <CheckCircle className="w-5 h-5" /> }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.icon}
              <span>{tab.label}</span>
              {tab.id === 'tickets' && tickets.filter(t => t.status === 'open' || t.status === 'in-progress').length > 0 && (
                <span className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5 ml-1">
                  {tickets.filter(t => t.status === 'open' || t.status === 'in-progress').length}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[600px]">
        {activeTab === 'faq' && (
          <div>
            {/* Search Bar */}
            <div className="mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Rechercher dans la FAQ..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* FAQ Categories */}
            <div className="mb-6">
              <div className="flex flex-wrap gap-2">
                {[
                  { id: 'toutes', label: 'Toutes' },
                  { id: 'profil', label: 'Profil' },
                  { id: 'avis', label: 'Avis' },
                  { id: 'compte', label: 'Compte' },
                  { id: 'marketplace', label: 'Marketplace' },
                  { id: 'ia', label: 'IA' },
                  { id: 'business', label: 'Business' }
                ].map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedFaqCategory(category.id)}
                    className={`px-4 py-2 text-sm font-medium rounded-full border transition-colors ${
                      selectedFaqCategory === category.id
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500'
                    }`}
                  >
                    {category.label}
                  </button>
                ))}
              </div>
            </div>

            {/* FAQ Items */}
            <Card>
              <CardBody className="p-6">
                {filteredFaqs.length > 0 ? (
                  filteredFaqs.map((faq, index) => (
                    <FAQItem
                      key={index}
                      question={faq.question}
                      answer={faq.answer}
                      helpful={faq.helpful}
                      notHelpful={faq.notHelpful}
                      onRate={async (helpful) => {
                        if (!currentUser?.id) return;
                        try {
                          await SupportService.rateFAQ(
                            currentUser.id,
                            faq.question,
                            helpful
                          );
                          console.log(`FAQ "${faq.question}" rated as ${helpful ? 'helpful' : 'not helpful'}`);
                        } catch (error) {
                          console.error('Erreur lors de la notation:', error);
                        }
                      }}
                    />
                  ))
                ) : (
                  <div className="text-center py-8">
                    <HelpCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Aucun résultat trouvé</h3>
                    <p className="text-gray-600">Essayez de modifier votre recherche ou contactez notre équipe.</p>
                  </div>
                )}
              </CardBody>
            </Card>
          </div>
        )}

        {activeTab === 'guides' && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Guide Categories Sidebar */}
            <div className="lg:col-span-1">
              <Card>
                <CardBody className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">📚 Catégories</h3>
                  <div className="space-y-2">
                    {[
                      { name: 'Démarrage', icon: '🚀', count: 8, color: 'blue' },
                      { name: 'Avis & Recommandations', icon: '⭐', count: 12, color: 'yellow' },
                      { name: 'Marketplace', icon: '🛒', count: 15, color: 'green' },
                      { name: 'IA & Personnalisation', icon: '🤖', count: 6, color: 'purple' },
                      { name: 'Compte & Sécurité', icon: '🔒', count: 10, color: 'red' },
                      { name: 'Entreprises', icon: '🏢', count: 18, color: 'indigo' },
                      { name: 'Paiements', icon: '💳', count: 7, color: 'pink' },
                      { name: 'Mobile', icon: '📱', count: 5, color: 'gray' }
                    ].map((category) => (
                      <button
                        key={category.name}
                        className="w-full text-left px-3 py-3 rounded-lg hover:bg-blue-50 hover:text-blue-700 text-gray-700 transition-colors group"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <span className="text-lg">{category.icon}</span>
                            <span className="text-sm font-medium">{category.name}</span>
                          </div>
                          <span className="text-xs bg-gray-100 group-hover:bg-blue-100 text-gray-600 group-hover:text-blue-600 px-2 py-1 rounded-full">
                            {category.count}
                          </span>
                        </div>
                      </button>
                    ))}
                  </div>
                </CardBody>
              </Card>

              {/* Quick Actions */}
              <Card className="mt-6">
                <CardBody className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">⚡ Actions Rapides</h3>
                  <div className="space-y-3">
                    <button className="w-full flex items-center space-x-3 p-3 rounded-lg bg-blue-50 hover:bg-blue-100 text-blue-700 transition-colors">
                      <Video className="w-5 h-5" />
                      <span className="text-sm font-medium">Tutoriels Vidéo</span>
                    </button>
                    <button className="w-full flex items-center space-x-3 p-3 rounded-lg bg-green-50 hover:bg-green-100 text-green-700 transition-colors">
                      <Download className="w-5 h-5" />
                      <span className="text-sm font-medium">Guide PDF</span>
                    </button>
                    <button className="w-full flex items-center space-x-3 p-3 rounded-lg bg-purple-50 hover:bg-purple-100 text-purple-700 transition-colors">
                      <Headphones className="w-5 h-5" />
                      <span className="text-sm font-medium">Formation Live</span>
                    </button>
                    <button className="w-full flex items-center space-x-3 p-3 rounded-lg bg-orange-50 hover:bg-orange-100 text-orange-700 transition-colors">
                      <Book className="w-5 h-5" />
                      <span className="text-sm font-medium">Documentation</span>
                    </button>
                  </div>
                </CardBody>
              </Card>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Search and Filters */}
              <div className="mb-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="text"
                      placeholder="Rechercher dans les guides..."
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <select className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                      <option>Tous les niveaux</option>
                      <option>Débutant</option>
                      <option>Intermédiaire</option>
                      <option>Avancé</option>
                    </select>
                    <select className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                      <option>Tous les formats</option>
                      <option>Article</option>
                      <option>Vidéo</option>
                      <option>PDF</option>
                      <option>Interactif</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Featured Guides */}
              <div className="mb-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">🌟 Guides Populaires</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {[
                    {
                      title: 'Guide Complet du Débutant',
                      description: 'Tout ce que vous devez savoir pour commencer sur customeroom : création de compte, premier avis, découverte des fonctionnalités',
                      duration: '15 min',
                      difficulty: 'Débutant',
                      format: 'Article + Vidéo',
                      views: '12.5k',
                      rating: 4.9,
                      image: '📚',
                      tags: ['Essentiel', 'Nouveau'],
                      updated: '2 jours'
                    },
                    {
                      title: 'Maximiser vos Recommandations IA',
                      description: 'Techniques avancées pour obtenir les meilleures suggestions personnalisées et découvrir des produits parfaits',
                      duration: '12 min',
                      difficulty: 'Intermédiaire',
                      format: 'Tutoriel Interactif',
                      views: '8.2k',
                      rating: 4.8,
                      image: '🤖',
                      tags: ['IA', 'Populaire'],
                      updated: '1 semaine'
                    }
                  ].map((guide, index) => (
                    <Card key={index} className="hover:shadow-lg transition-all duration-300 cursor-pointer group border-l-4 border-l-blue-500">
                      <CardBody className="p-6">
                        <div className="flex items-start space-x-4">
                          <div className="text-4xl">{guide.image}</div>
                          <div className="flex-1">
                            <div className="flex items-start justify-between mb-2">
                              <h4 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                                {guide.title}
                              </h4>
                              <ExternalLink className="w-5 h-5 text-gray-400 group-hover:text-blue-500 transition-colors" />
                            </div>
                            <p className="text-gray-600 text-sm mb-3 leading-relaxed">{guide.description}</p>

                            <div className="flex flex-wrap gap-2 mb-4">
                              {guide.tags.map((tag) => (
                                <span key={tag} className="bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full font-medium">
                                  {tag}
                                </span>
                              ))}
                            </div>

                            <div className="flex items-center justify-between text-sm">
                              <div className="flex items-center space-x-4 text-gray-500">
                                <div className="flex items-center space-x-1">
                                  <Clock className="w-4 h-4" />
                                  <span>{guide.duration}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <Eye className="w-4 h-4" />
                                  <span>{guide.views}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <span className="text-yellow-500">⭐</span>
                                  <span>{guide.rating}</span>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                                  {guide.difficulty}
                                </span>
                                <span className="text-xs text-gray-500">
                                  Mis à jour il y a {guide.updated}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              </div>

              {/* All Guides Section */}
              <div className="mb-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">📖 Tous les Guides</h3>
                <div className="space-y-4">
                  {[
                    {
                      title: 'Comment créer votre premier avis',
                      description: 'Guide étape par étape pour publier votre premier avis produit avec photos, conseils et bonnes pratiques',
                      duration: '5 min',
                      difficulty: 'Débutant',
                      format: 'Article',
                      category: 'Avis & Recommandations',
                      updated: '2 jours',
                      icon: '⭐',
                      views: '15.2k',
                      rating: 4.7
                    },
                    {
                      title: 'Configurer votre profil d\'entreprise',
                      description: 'Optimisez votre présence professionnelle, ajoutez vos informations et attirez plus de clients',
                      duration: '10 min',
                      difficulty: 'Intermédiaire',
                      format: 'Vidéo + PDF',
                      category: 'Entreprises',
                      updated: '1 semaine',
                      icon: '🏢',
                      views: '9.8k',
                      rating: 4.6
                    },
                    {
                      title: 'Vendre efficacement sur la marketplace',
                      description: 'Stratégies avancées pour maximiser vos ventes, optimiser vos annonces et gérer vos commandes',
                      duration: '20 min',
                      difficulty: 'Avancé',
                      format: 'Série vidéo',
                      category: 'Marketplace',
                      updated: '3 jours',
                      icon: '🛒',
                      views: '7.1k',
                      rating: 4.8
                    },
                    {
                      title: 'Sécuriser votre compte',
                      description: 'Meilleures pratiques pour protéger vos données, sécuriser vos transactions et éviter les fraudes',
                      duration: '8 min',
                      difficulty: 'Débutant',
                      format: 'Article',
                      category: 'Sécurité',
                      updated: '5 jours',
                      icon: '🔒',
                      views: '11.3k',
                      rating: 4.5
                    },
                    {
                      title: 'Utiliser l\'IA pour découvrir de nouveaux produits',
                      description: 'Personnalisez vos recommandations, configurez vos préférences et découvrez des produits parfaits',
                      duration: '12 min',
                      difficulty: 'Intermédiaire',
                      format: 'Tutoriel interactif',
                      category: 'IA & Personnalisation',
                      updated: '1 jour',
                      icon: '🤖',
                      views: '6.9k',
                      rating: 4.9
                    },
                    {
                      title: 'Gérer vos commandes et livraisons',
                      description: 'Suivez vos achats, gérez les retours, communiquez avec les vendeurs et résolvez les problèmes',
                      duration: '7 min',
                      difficulty: 'Débutant',
                      format: 'Guide illustré',
                      category: 'Marketplace',
                      updated: '4 jours',
                      icon: '📦',
                      views: '8.7k',
                      rating: 4.4
                    }
                  ].map((guide, index) => (
                    <Card key={index} className="hover:shadow-md transition-all duration-200 cursor-pointer group">
                      <CardBody className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-4 flex-1">
                            <div className="text-2xl">{guide.icon}</div>
                            <div className="flex-1">
                              <div className="flex items-start justify-between mb-2">
                                <h4 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                                  {guide.title}
                                </h4>
                                <div className="flex items-center space-x-2 ml-4">
                                  <span className="text-xs text-gray-500">Mis à jour il y a {guide.updated}</span>
                                  <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors" />
                                </div>
                              </div>
                              <p className="text-gray-600 text-sm mb-3 leading-relaxed">{guide.description}</p>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4 text-sm text-gray-500">
                                  <div className="flex items-center space-x-1">
                                    <Clock className="w-4 h-4" />
                                    <span>{guide.duration}</span>
                                  </div>
                                  <div className="flex items-center space-x-1">
                                    <Eye className="w-4 h-4" />
                                    <span>{guide.views}</span>
                                  </div>
                                  <div className="flex items-center space-x-1">
                                    <span className="text-yellow-500">⭐</span>
                                    <span>{guide.rating}</span>
                                  </div>
                                  <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                                    {guide.difficulty}
                                  </span>
                                  <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs">
                                    {guide.format}
                                  </span>
                                </div>
                                <span className="text-gray-500 text-xs bg-gray-50 px-2 py-1 rounded">
                                  {guide.category}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Resources Section */}
              <div className="mb-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">📚 Ressources Téléchargeables</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="hover:shadow-md transition-shadow cursor-pointer group">
                    <CardBody className="p-6 text-center">
                      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-red-200 transition-colors">
                        <FileText className="w-8 h-8 text-red-600" />
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">Guide PDF Complet</h4>
                      <p className="text-sm text-gray-600 mb-3">Documentation complète de 50 pages avec captures d'écran</p>
                      <div className="text-xs text-gray-500 mb-3">2.5 MB • Français</div>
                      <Button variant="outline" size="sm" className="w-full">
                        <Download className="w-4 h-4 mr-2" />
                        Télécharger
                      </Button>
                    </CardBody>
                  </Card>

                  <Card className="hover:shadow-md transition-shadow cursor-pointer group">
                    <CardBody className="p-6 text-center">
                      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                        <Video className="w-8 h-8 text-blue-600" />
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">Tutoriels Vidéo</h4>
                      <p className="text-sm text-gray-600 mb-3">Playlist de 15 vidéos couvrant toutes les fonctionnalités</p>
                      <div className="text-xs text-gray-500 mb-3">2h 30min • HD</div>
                      <Button variant="outline" size="sm" className="w-full">
                        <Video className="w-4 h-4 mr-2" />
                        Regarder
                      </Button>
                    </CardBody>
                  </Card>

                  <Card className="hover:shadow-md transition-shadow cursor-pointer group">
                    <CardBody className="p-6 text-center">
                      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                        <Book className="w-8 h-8 text-green-600" />
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">Manuel Utilisateur</h4>
                      <p className="text-sm text-gray-600 mb-3">Documentation technique détaillée pour les développeurs</p>
                      <div className="text-xs text-gray-500 mb-3">API • SDK</div>
                      <Button variant="outline" size="sm" className="w-full">
                        <Book className="w-4 h-4 mr-2" />
                        Consulter
                      </Button>
                    </CardBody>
                  </Card>
                </div>
              </div>

              {/* Load More */}
              <div className="text-center">
                <Button variant="outline" className="flex items-center space-x-2 mx-auto">
                  <span>Charger plus de guides</span>
                  <ExternalLink className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'tickets' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-semibold text-gray-900">Mes Tickets de Support</h2>
              <Button
                className="flex items-center space-x-2"
                onClick={() => setShowNewTicketModal(true)}
              >
                <Plus className="w-4 h-4" />
                <span>Nouveau Ticket</span>
              </Button>
            </div>

            {/* Tickets List */}
            <div className="space-y-4 mb-8">
              {tickets.map((ticket) => (
                <Card key={ticket.id} className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardBody className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="font-semibold text-gray-900">{ticket.subject}</h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                            {ticket.priority.toUpperCase()}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                            {ticket.status.replace('-', ' ').toUpperCase()}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">
                          Ticket #{ticket.id} • Créé le {new Date(ticket.created_at).toLocaleDateString('fr-FR')}
                        </p>
                        <p className="text-gray-700 line-clamp-2">
                          {ticket.description}
                        </p>
                      </div>
                      <div className="text-right text-sm text-gray-500">
                        <div>Dernière mise à jour</div>
                        <div>{new Date(ticket.updated_at).toLocaleDateString('fr-FR')}</div>
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t border-gray-100">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openTicket(ticket)}
                      >
                        Voir les détails
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>

            {/* Ticket Detail Modal */}
            {selectedTicket && (
              <Card className="mb-8">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">{selectedTicket.subject}</h3>
                      <p className="text-sm text-gray-600">Ticket #{selectedTicket.id}</p>
                    </div>
                    <Button variant="outline" size="sm" onClick={() => setSelectedTicket(null)}>
                      Fermer
                    </Button>
                  </div>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="space-y-4 mb-6">
                    {ticketMessages.map((message) => (
                      <div key={message.id} className={`flex ${message.sender_id === currentUser?.id ? 'justify-end' : 'justify-start'}`}>
                        <div className={`max-w-3xl p-4 rounded-lg ${
                          message.sender_id === currentUser?.id
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-100 text-gray-900'
                        }`}>
                          <div className="font-medium text-sm mb-1">
                            {message.sender?.username || 'Utilisateur'}
                          </div>
                          <div>{message.content}</div>
                          <div className="text-xs mt-2 opacity-75">
                            {new Date(message.created_at).toLocaleString('fr-FR')}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Reply Form */}
                  <form onSubmit={(e) => { e.preventDefault(); handleTicketReply(selectedTicket.id); }}>
                    <div className="flex space-x-3">
                      <textarea
                        value={newTicketMessage}
                        onChange={(e) => setNewTicketMessage(e.target.value)}
                        placeholder="Tapez votre réponse..."
                        className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows={3}
                      />
                      <Button type="submit" className="self-end">
                        <Send className="w-4 h-4" />
                      </Button>
                    </div>
                  </form>
                </CardBody>
              </Card>
            )}
          </div>
        )}

        {activeTab === 'community' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-semibold text-gray-900">Forum Communautaire</h2>
              <Button
                className="flex items-center space-x-2"
                onClick={() => setShowNewPostModal(true)}
              >
                <Plus className="w-4 h-4" />
                <span>Nouvelle Discussion</span>
              </Button>
            </div>

            {/* Community Categories */}
            <div className="mb-6">
              <div className="flex flex-wrap gap-2">
                {[
                  { id: 'all', label: 'Toutes' },
                  { id: 'conseils', label: 'Conseils' },
                  { id: 'bugs', label: 'Bugs' },
                  { id: 'features', label: 'Fonctionnalités' },
                  { id: 'general', label: 'Général' }
                ].map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCommunityCategory(category.id)}
                    className={`px-4 py-2 text-sm font-medium rounded-full border transition-colors ${
                      selectedCommunityCategory === category.id
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {category.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Community Posts */}
            <div className="space-y-4">
              {communityPosts
                .filter(post => selectedCommunityCategory === 'all' || post.category === selectedCommunityCategory)
                .map((post) => (
                <Card key={post.id} className="hover:shadow-md transition-shadow">
                  <CardBody className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="font-semibold text-gray-900 hover:text-blue-600 cursor-pointer">
                            {post.title}
                          </h3>
                          {post.is_solved && (
                            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full flex items-center">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Résolu
                            </span>
                          )}
                        </div>

                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{post.content}</p>

                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>Par {post.author?.username || 'Utilisateur'}</span>
                          <span>{new Date(post.created_at).toLocaleDateString('fr-FR')}</span>
                          <div className="flex items-center space-x-1">
                            <Eye className="w-4 h-4" />
                            <span>{post.views_count}</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 mt-3">
                          {post.tags.map((tag) => (
                            <span key={tag} className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                              #{tag}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div className="text-right">
                        <div className="flex items-center space-x-4 text-sm">
                          <button
                            onClick={() => handleVotePost(post.id, 'up')}
                            className="flex items-center space-x-1 text-gray-500 hover:text-green-600 transition-colors"
                          >
                            <ThumbsUp className="w-4 h-4" />
                            <span>{post.votes_up - post.votes_down}</span>
                          </button>
                          <button
                            onClick={() => openPostDetail(post)}
                            className="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors"
                          >
                            <MessageCircle className="w-4 h-4" />
                            <span>{post.replies_count}</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'contact' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Contact Methods */}
            <div className="space-y-6">
              <Card>
                <CardBody className="p-6">
                  <h3 className="text-xl font-medium text-gray-900 mb-4 flex items-center">
                    <Mail className="w-6 h-6 mr-2 text-blue-600" />
                    Support par Email
                  </h3>
                  <p className="text-gray-600 text-sm mb-3">
                    Pour toute question générale ou support technique
                  </p>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-blue-600 hover:underline font-medium"
                  >
                    <EMAIL>
                  </a>
                  <div className="mt-3 text-sm text-gray-500">
                    Réponse sous 2 heures en moyenne
                  </div>
                </CardBody>
              </Card>

              <Card>
                <CardBody className="p-6">
                  <h3 className="text-xl font-medium text-gray-900 mb-4 flex items-center">
                    <Phone className="w-6 h-6 mr-2 text-green-600" />
                    Support Téléphonique
                  </h3>
                  <p className="text-gray-600 text-sm mb-3">
                    Disponible du Lundi au Vendredi, de 9h à 18h
                  </p>
                  <p className="text-gray-800 font-medium text-lg">+225 27 XX XX XX XX</p>
                  <div className="mt-3 text-sm text-gray-500">
                    Gratuit depuis la Côte d'Ivoire
                  </div>
                </CardBody>
              </Card>

              <Card>
                <CardBody className="p-6">
                  <h3 className="text-xl font-medium text-gray-900 mb-4 flex items-center">
                    <MessageCircle className="w-6 h-6 mr-2 text-purple-600" />
                    Chat en Direct
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Discutez directement avec notre équipe support
                  </p>
                  <Button
                    className="w-full"
                    onClick={() => setChatOpen(true)}
                  >
                    Démarrer une conversation
                  </Button>
                  <div className="mt-3 text-sm text-gray-500">
                    Disponible 24h/24, 7j/7
                  </div>
                </CardBody>
              </Card>
            </div>

            {/* Contact Form */}
            <div>
              <Card>
                <CardHeader>
                  <h3 className="text-xl font-semibold text-gray-900">
                    Envoyer un Message
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Décrivez votre problème et nous vous répondrons rapidement
                  </p>
                </CardHeader>
                <CardBody className="p-6">
                  <form onSubmit={handleContactSubmit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Adresse Email
                      </label>
                      <input
                        type="email"
                        value={contactForm.email}
                        onChange={(e) => setContactForm({...contactForm, email: e.target.value})}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Catégorie
                      </label>
                      <select
                        value={contactForm.category}
                        onChange={(e) => setContactForm({...contactForm, category: e.target.value})}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="general">Question générale</option>
                        <option value="technical">Problème technique</option>
                        <option value="account">Problème de compte</option>
                        <option value="billing">Facturation</option>
                        <option value="feature">Demande de fonctionnalité</option>
                        <option value="bug">Signaler un bug</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Priorité
                      </label>
                      <select
                        value={contactForm.priority}
                        onChange={(e) => setContactForm({...contactForm, priority: e.target.value})}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="low">Faible</option>
                        <option value="normal">Normale</option>
                        <option value="high">Élevée</option>
                        <option value="urgent">Urgente</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Sujet
                      </label>
                      <input
                        type="text"
                        value={contactForm.subject}
                        onChange={(e) => setContactForm({...contactForm, subject: e.target.value})}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Résumez votre demande en quelques mots"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Message
                      </label>
                      <textarea
                        value={contactForm.message}
                        onChange={(e) => setContactForm({...contactForm, message: e.target.value})}
                        rows={6}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Décrivez votre problème ou votre question en détail..."
                        required
                      />
                    </div>

                    <Button type="submit" className="w-full">
                      Envoyer le Message
                    </Button>
                  </form>
                </CardBody>
              </Card>
            </div>
          </div>
        )}

        {activeTab === 'status' && (
          <div>
            <div className="mb-6">
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">Statut des Services</h2>
              <p className="text-gray-600">Surveillez l'état de nos services en temps réel</p>
            </div>

            <Card className="mb-6">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">État Général</h3>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-green-600 font-medium">Tous les systèmes opérationnels</span>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  Dernière mise à jour : {new Date().toLocaleString('fr-FR')}
                </div>
              </CardBody>
            </Card>

            <div className="space-y-4">
              {systemStatus.map((service, index) => (
                <Card key={index}>
                  <CardBody className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(service.status)}
                        <div>
                          <h4 className="font-medium text-gray-900">{service.service}</h4>
                          <p className="text-sm text-gray-600">{getStatusText(service.status)}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-500">{service.lastUpdate}</div>
                        {service.status === 'maintenance' && (
                          <div className="text-xs text-yellow-600 mt-1">
                            Maintenance programmée
                          </div>
                        )}
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>

            {/* Incident History */}
            <Card className="mt-8">
              <CardHeader>
                <h3 className="text-xl font-semibold text-gray-900">Historique des Incidents</h3>
              </CardHeader>
              <CardBody className="p-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3 p-4 bg-green-50 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-gray-900">Maintenance programmée terminée</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        Mise à jour du système de notifications - Durée : 30 minutes
                      </p>
                      <div className="text-xs text-gray-500 mt-2">Il y a 2 heures</div>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
                    <AlertCircle className="w-5 h-5 text-blue-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-gray-900">Amélioration des performances</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        Optimisation de la base de données pour des temps de réponse plus rapides
                      </p>
                      <div className="text-xs text-gray-500 mt-2">Il y a 1 jour</div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        )}
      </div>

      {/* Chat Widget */}
      {chatOpen && (
        <div className="fixed bottom-4 right-4 w-96 h-96 bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col z-50">
          {/* Chat Header */}
          <div className="bg-blue-500 text-white p-4 rounded-t-lg flex justify-between items-center">
            <div>
              <h3 className="font-semibold">Chat Support</h3>
              <p className="text-sm opacity-90">En ligne</p>
            </div>
            <button
              onClick={() => setChatOpen(false)}
              className="text-white hover:bg-blue-600 rounded p-1"
            >
              ×
            </button>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 p-4 overflow-y-auto space-y-3">
            {chatMessages.length === 0 ? (
              <div className="text-center text-gray-500 text-sm">
                <MessageCircle className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                <p>Bonjour ! Comment puis-je vous aider aujourd'hui ?</p>
              </div>
            ) : (
              chatMessages.map((message) => (
                <div key={message.id} className={`flex ${message.sender_id === currentUser?.id ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-xs p-3 rounded-lg text-sm ${
                    message.sender_id === currentUser?.id
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}>
                    <div>{message.content}</div>
                    <div className="text-xs mt-1 opacity-75">
                      {new Date(message.created_at).toLocaleTimeString('fr-FR', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Chat Input */}
          <form onSubmit={handleChatSubmit} className="p-4 border-t border-gray-200">
            <div className="flex space-x-2">
              <input
                type="text"
                value={newChatMessage}
                onChange={(e) => setNewChatMessage(e.target.value)}
                placeholder="Tapez votre message..."
                className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
              <Button type="submit" size="sm">
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Floating Chat Button */}
      {!chatOpen && (
        <button
          onClick={() => setChatOpen(true)}
          className="fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-4 rounded-full shadow-lg transition-all duration-200 hover:scale-110 z-40"
        >
          <MessageCircle className="w-6 h-6" />
        </button>
      )}

      {/* Quick Help Section */}
      <div className="mt-12 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Besoin d'aide immédiate ?</h2>
          <p className="text-gray-600">Accédez rapidement aux ressources les plus utiles</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Book className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Guide de démarrage</h3>
              <p className="text-sm text-gray-600 mb-4">Apprenez les bases en 5 minutes</p>
              <Button variant="outline" size="sm" className="w-full">
                Commencer
              </Button>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Chat en direct</h3>
              <p className="text-sm text-gray-600 mb-4">Parlez à un expert maintenant</p>
              <Button variant="outline" size="sm" className="w-full">
                Démarrer le chat
              </Button>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Video className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Tutoriels vidéo</h3>
              <p className="text-sm text-gray-600 mb-4">Apprenez en regardant</p>
              <Button variant="outline" size="sm" className="w-full">
                Voir les vidéos
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-12 text-center">
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Vous ne trouvez pas ce que vous cherchez ?</h3>
          <p className="text-gray-600 mb-4">
            Notre équipe support est là pour vous aider. Contactez-nous et nous vous répondrons rapidement.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              onClick={() => setActiveTab('contact')}
              className="flex items-center space-x-2"
            >
              <Mail className="w-4 h-4" />
              <span>Nous contacter</span>
            </Button>
            <Button
              variant="outline"
              className="flex items-center space-x-2"
            >
              <Phone className="w-4 h-4" />
              <span>Appeler le support</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Modal de création de ticket */}
      {showNewTicketModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-semibold text-gray-900">Créer un nouveau ticket</h2>
                <button
                  onClick={() => setShowNewTicketModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <form onSubmit={async (e) => {
                e.preventDefault();
                if (!currentUser?.id || !newTicketForm.subject.trim() || !newTicketForm.description.trim()) return;

                try {
                  setLoading(true);

                  await createNewTicket(
                    newTicketForm.subject.trim(),
                    newTicketForm.description.trim(),
                    newTicketForm.category,
                    newTicketForm.priority
                  );

                  // Réinitialiser le formulaire et fermer le modal
                  setNewTicketForm({
                    subject: '',
                    description: '',
                    category: 'general',
                    priority: 'normal'
                  });
                  setShowNewTicketModal(false);

                  console.log('Ticket créé avec succès');
                } catch (error) {
                  console.error('Erreur lors de la création du ticket:', error);
                } finally {
                  setLoading(false);
                }
              }}>
                <div className="space-y-6">
                  {/* Sujet */}
                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                      Sujet *
                    </label>
                    <input
                      type="text"
                      id="subject"
                      value={newTicketForm.subject}
                      onChange={(e) => setNewTicketForm(prev => ({ ...prev, subject: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Décrivez brièvement votre problème..."
                      required
                    />
                  </div>

                  {/* Catégorie */}
                  <div>
                    <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                      Catégorie
                    </label>
                    <select
                      id="category"
                      value={newTicketForm.category}
                      onChange={(e) => setNewTicketForm(prev => ({ ...prev, category: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="general">Général</option>
                      <option value="technical">Technique</option>
                      <option value="billing">Facturation</option>
                      <option value="account">Compte</option>
                      <option value="feature">Nouvelle fonctionnalité</option>
                      <option value="bug">Bug</option>
                    </select>
                  </div>

                  {/* Priorité */}
                  <div>
                    <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-2">
                      Priorité
                    </label>
                    <select
                      id="priority"
                      value={newTicketForm.priority}
                      onChange={(e) => setNewTicketForm(prev => ({ ...prev, priority: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="low">Faible</option>
                      <option value="normal">Normale</option>
                      <option value="high">Élevée</option>
                      <option value="urgent">Urgente</option>
                    </select>
                  </div>

                  {/* Description */}
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                      Description *
                    </label>
                    <textarea
                      id="description"
                      value={newTicketForm.description}
                      onChange={(e) => setNewTicketForm(prev => ({ ...prev, description: e.target.value }))}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Décrivez votre problème en détail. Plus vous donnez d'informations, plus nous pourrons vous aider rapidement..."
                      required
                    />
                  </div>

                  {/* Boutons */}
                  <div className="flex justify-end space-x-3 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowNewTicketModal(false)}
                      disabled={loading}
                    >
                      Annuler
                    </Button>
                    <Button
                      type="submit"
                      disabled={loading || !newTicketForm.subject.trim() || !newTicketForm.description.trim()}
                      className="flex items-center space-x-2"
                    >
                      {loading ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>Création...</span>
                        </>
                      ) : (
                        <>
                          <Plus className="w-4 h-4" />
                          <span>Créer le ticket</span>
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Modal de création de post communautaire */}
      {showNewPostModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-semibold text-gray-900">Créer une nouvelle discussion</h2>
                <button
                  onClick={() => setShowNewPostModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <form onSubmit={async (e) => {
                e.preventDefault();
                if (!currentUser?.id || !newPostForm.title.trim() || !newPostForm.content.trim()) return;

                try {
                  setLoading(true);

                  await createNewPost(
                    newPostForm.title.trim(),
                    newPostForm.content.trim(),
                    newPostForm.category
                  );

                  // Réinitialiser le formulaire et fermer le modal
                  setNewPostForm({
                    title: '',
                    content: '',
                    category: 'general'
                  });
                  setShowNewPostModal(false);

                  console.log('Post créé avec succès');
                } catch (error) {
                  console.error('Erreur lors de la création du post:', error);
                } finally {
                  setLoading(false);
                }
              }}>
                <div className="space-y-6">
                  {/* Titre */}
                  <div>
                    <label htmlFor="post-title" className="block text-sm font-medium text-gray-700 mb-2">
                      Titre de la discussion *
                    </label>
                    <input
                      type="text"
                      id="post-title"
                      value={newPostForm.title}
                      onChange={(e) => setNewPostForm(prev => ({ ...prev, title: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Posez votre question ou décrivez votre sujet..."
                      required
                    />
                  </div>

                  {/* Catégorie */}
                  <div>
                    <label htmlFor="post-category" className="block text-sm font-medium text-gray-700 mb-2">
                      Catégorie
                    </label>
                    <select
                      id="post-category"
                      value={newPostForm.category}
                      onChange={(e) => setNewPostForm(prev => ({ ...prev, category: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="general">Général</option>
                      <option value="conseils">Conseils</option>
                      <option value="bugs">Bugs</option>
                      <option value="features">Fonctionnalités</option>
                      <option value="questions">Questions</option>
                    </select>
                  </div>

                  {/* Contenu */}
                  <div>
                    <label htmlFor="post-content" className="block text-sm font-medium text-gray-700 mb-2">
                      Contenu de votre message *
                    </label>
                    <textarea
                      id="post-content"
                      value={newPostForm.content}
                      onChange={(e) => setNewPostForm(prev => ({ ...prev, content: e.target.value }))}
                      rows={8}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Décrivez votre question ou partagez votre expérience en détail. Plus vous donnez d'informations, plus la communauté pourra vous aider..."
                      required
                    />
                  </div>

                  {/* Conseils */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Conseils pour une bonne discussion :</h4>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Soyez précis dans votre titre</li>
                      <li>• Donnez du contexte dans votre message</li>
                      <li>• Utilisez la bonne catégorie</li>
                      <li>• Soyez respectueux envers la communauté</li>
                    </ul>
                  </div>

                  {/* Boutons */}
                  <div className="flex justify-end space-x-3 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowNewPostModal(false)}
                      disabled={loading}
                    >
                      Annuler
                    </Button>
                    <Button
                      type="submit"
                      disabled={loading || !newPostForm.title.trim() || !newPostForm.content.trim()}
                      className="flex items-center space-x-2"
                    >
                      {loading ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>Publication...</span>
                        </>
                      ) : (
                        <>
                          <Plus className="w-4 h-4" />
                          <span>Publier la discussion</span>
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Modal de détails du post avec réponses */}
      {showPostDetail && selectedPost && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-6">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h2 className="text-2xl font-semibold text-gray-900">{selectedPost.title}</h2>
                    {selectedPost.is_solved && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full flex items-center">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Résolu
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                    <span>Par {selectedPost.author?.username || 'Utilisateur'}</span>
                    <span>{new Date(selectedPost.created_at).toLocaleDateString('fr-FR')}</span>
                    <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                      {selectedPost.category}
                    </span>
                  </div>
                </div>
                <button
                  onClick={() => setShowPostDetail(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* Contenu du post */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <p className="text-gray-700">{selectedPost.content}</p>
              </div>

              {/* Actions du post */}
              <div className="flex items-center space-x-6 mb-6 pb-6 border-b border-gray-200">
                <button
                  onClick={() => handleVotePost(selectedPost.id, 'up')}
                  className="flex items-center space-x-2 text-gray-500 hover:text-green-600 transition-colors"
                >
                  <ThumbsUp className="w-5 h-5" />
                  <span>{selectedPost.votes_up - selectedPost.votes_down} votes</span>
                </button>
                <div className="flex items-center space-x-2 text-gray-500">
                  <MessageCircle className="w-5 h-5" />
                  <span>{selectedPost.replies_count} réponses</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-500">
                  <Eye className="w-5 h-5" />
                  <span>{selectedPost.views_count} vues</span>
                </div>
              </div>

              {/* Section des réponses */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Réponses ({selectedPost.replies_count})
                </h3>

                {/* Formulaire d'ajout de réponse */}
                <form onSubmit={(e) => { e.preventDefault(); handleAddReply(selectedPost.id); }} className="mb-6">
                  <div className="space-y-3">
                    <textarea
                      value={newReply}
                      onChange={(e) => setNewReply(e.target.value)}
                      placeholder="Écrivez votre réponse..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      rows={4}
                    />
                    <div className="flex justify-end">
                      <Button
                        type="submit"
                        disabled={!newReply.trim()}
                        className="flex items-center space-x-2"
                      >
                        <Send className="w-4 h-4" />
                        <span>Répondre</span>
                      </Button>
                    </div>
                  </div>
                </form>

                {/* Liste des réponses (placeholder) */}
                <div className="space-y-4">
                  <div className="text-center py-8 text-gray-500">
                    <MessageCircle className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                    <p>Les réponses s'afficheront ici une fois implémentées</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SupportPage;
