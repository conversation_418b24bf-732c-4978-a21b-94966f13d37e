-- Script pour ajouter la fonctionnalité de likes aux posts
-- À exécuter dans l'éditeur SQL de Supabase

-- =====================================================
-- 1. AJOUTER LA COLONNE LIKES À LA TABLE POSTS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Ajout de la colonne likes à la table posts...';
    
    -- Ajout de la colonne likes
    ALTER TABLE posts
    ADD COLUMN IF NOT EXISTS likes UUID[] DEFAULT ARRAY[]::UUID[];
    
    -- Ajout de la colonne shares
    ALTER TABLE posts
    ADD COLUMN IF NOT EXISTS shares UUID[] DEFAULT ARRAY[]::UUID[];
    
    RAISE NOTICE '✅ Colonnes likes et shares ajoutées';
END $$;

-- =====================================================
-- 2. CRÉER DES INDEX POUR LES PERFORMANCES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Création des index pour les performances...';
    
    -- Index pour les likes
    CREATE INDEX IF NOT EXISTS posts_likes_idx ON posts USING GIN (likes);
    
    -- Index pour les recommendations
    CREATE INDEX IF NOT EXISTS posts_recommendations_idx ON posts USING GIN (recommendations);
    
    -- Index pour les shares
    CREATE INDEX IF NOT EXISTS posts_shares_idx ON posts USING GIN (shares);
    
    RAISE NOTICE '✅ Index créés';
END $$;

-- =====================================================
-- 3. METTRE À JOUR LA VUE posts_with_author_details
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Mise à jour de la vue posts_with_author_details...';
    
    -- Recréer la vue pour inclure les nouvelles colonnes
    CREATE OR REPLACE VIEW posts_with_author_details AS
    SELECT
      p.*,
      u.username as author_username,
      u.profile_picture as author_profile_picture,
      u.status as author_status,
      u.role as author_role,
      COALESCE(
        (
          SELECT json_agg(
            json_build_object(
              'id', c.id,
              'userId', c.user_id,
              'username', cu.username,
              'profilePicture', cu.profile_picture,
              'content', c.content,
              'hasUsedProduct', c.has_used_product,
              'rating', c.rating,
              'createdAt', c.created_at,
              'authorRole', cu.role,
              'businessName', CASE WHEN cu.role = 'business' THEN cu.username ELSE NULL END
            ) ORDER BY c.created_at ASC
          )
          FROM comments c
          LEFT JOIN profiles cu ON c.user_id = cu.id
          WHERE c.post_id = p.id
        ),
        '[]'::json
      ) as comments
    FROM posts p
    LEFT JOIN profiles u ON p.user_id = u.id;
    
    -- Accorder l'accès à la vue
    GRANT SELECT ON posts_with_author_details TO authenticated;
    
    RAISE NOTICE '✅ Vue mise à jour';
END $$;

-- =====================================================
-- 4. INITIALISER LES DONNÉES EXISTANTES
-- =====================================================

DO $$
DECLARE
    posts_updated INTEGER;
BEGIN
    RAISE NOTICE '🔧 Initialisation des données existantes...';
    
    -- Initialiser les colonnes pour tous les posts existants
    UPDATE posts 
    SET 
        likes = COALESCE(likes, ARRAY[]::UUID[]),
        shares = COALESCE(shares, ARRAY[]::UUID[])
    WHERE likes IS NULL OR shares IS NULL;
    
    GET DIAGNOSTICS posts_updated = ROW_COUNT;
    
    RAISE NOTICE '✅ % posts mis à jour avec les nouvelles colonnes', posts_updated;
END $$;

-- =====================================================
-- 5. VÉRIFICATION FINALE
-- =====================================================

DO $$
DECLARE
    total_posts INTEGER;
    posts_with_likes INTEGER;
    posts_with_shares INTEGER;
BEGIN
    RAISE NOTICE '🔍 Vérification finale...';
    
    -- Compter le total de posts
    SELECT COUNT(*) INTO total_posts FROM posts;
    
    -- Compter les posts avec la colonne likes
    SELECT COUNT(*) INTO posts_with_likes FROM posts WHERE likes IS NOT NULL;
    
    -- Compter les posts avec la colonne shares
    SELECT COUNT(*) INTO posts_with_shares FROM posts WHERE shares IS NOT NULL;
    
    RAISE NOTICE '';
    RAISE NOTICE '📊 RÉSULTATS:';
    RAISE NOTICE '=============';
    RAISE NOTICE 'Total posts: %', total_posts;
    RAISE NOTICE 'Posts avec likes: %', posts_with_likes;
    RAISE NOTICE 'Posts avec shares: %', posts_with_shares;
    
    IF posts_with_likes = total_posts AND posts_with_shares = total_posts THEN
        RAISE NOTICE '✅ Migration réussie ! Tous les posts ont les nouvelles colonnes.';
    ELSE
        RAISE NOTICE '⚠️ Attention : Certains posts n''ont pas les nouvelles colonnes.';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 Le système de likes est maintenant opérationnel !';
END $$;
