# 🧪 Guide de Test - Réponses des Entreprises aux Publicités

## 📋 Vue d'Ensemble

Ce guide vous permet de tester le nouveau système permettant aux entreprises de voir et répondre aux commentaires sur leurs publicités.

## 🚀 Prérequis

### 1. **Exécuter la Migration SQL**
```sql
-- Dans Supabase SQL Editor, exécutez :
-- supabase/migrations/20250131000001_create_ad_business_responses.sql
```

### 2. **Créer les Campagnes de Test**
```sql
-- Dans Supabase SQL Editor, exécutez :
-- scripts/create-test-ad-campaigns.sql
```

### 3. **Avoir des Comptes de Test**
- Un compte **entreprise** (role: 'business')
- Un compte **utilisateur standard** (role: 'standard')

## 🧪 Scénarios de Test

### **Test 1 : Accès à l'Onglet "Mes publicités"**

#### Étapes :
1. **Connexion** : Se connecter avec un compte entreprise
2. **Navigation** : Aller sur le profil business
3. **Vérification** : Chercher l'onglet "Mes publicités" dans la barre latérale gauche
4. **Clic** : Cliquer sur l'onglet

#### Résultat Attendu :
- ✅ Onglet "Mes publicités" visible avec icône mégaphone
- ✅ Page avec deux sous-onglets : "Vue d'ensemble" et "Ads Comments"
- ✅ Statistiques des campagnes affichées

### **Test 2 : Voir les Commentaires sur les Publicités**

#### Étapes :
1. **Accès** : Aller dans "Mes publicités" → "Ads Comments"
2. **Vérification** : Voir la liste des commentaires reçus
3. **Détails** : Vérifier les informations affichées

#### Résultat Attendu :
- ✅ Liste des commentaires sur les publicités de l'entreprise
- ✅ Nom de l'utilisateur qui a commenté
- ✅ Contenu du commentaire
- ✅ Date du commentaire
- ✅ Nom de la campagne concernée

### **Test 3 : Répondre à un Commentaire**

#### Étapes :
1. **Sélection** : Choisir un commentaire sans réponse
2. **Clic** : Cliquer sur le bouton "Répondre"
3. **Saisie** : Écrire une réponse (ex: "Merci pour votre commentaire !")
4. **Publication** : Cliquer sur "Répondre" pour publier

#### Résultat Attendu :
- ✅ Formulaire de réponse s'affiche
- ✅ Compteur de caractères (max 1000)
- ✅ Réponse publiée avec succès
- ✅ Réponse visible dans l'interface

### **Test 4 : Voir les Réponses dans le Fil d'Actualité**

#### Étapes :
1. **Navigation** : Aller sur la page d'accueil
2. **Recherche** : Trouver la publicité avec le commentaire
3. **Clic** : Cliquer sur "Voir les commentaires"
4. **Vérification** : Voir la réponse de l'entreprise

#### Résultat Attendu :
- ✅ Commentaire original visible
- ✅ Réponse de l'entreprise sous le commentaire
- ✅ Design bleu pour la réponse entreprise
- ✅ Badge "Entreprise" visible
- ✅ Nom de l'entreprise affiché

### **Test 5 : Restrictions et Sécurité**

#### Étapes :
1. **Test Unicité** : Tenter de répondre deux fois au même commentaire
2. **Test Propriété** : Tenter de répondre à un commentaire d'une autre entreprise
3. **Test Validation** : Tenter de publier une réponse vide

#### Résultat Attendu :
- ✅ Impossible de répondre deux fois au même commentaire
- ✅ Impossible de répondre aux commentaires d'autres entreprises
- ✅ Validation du contenu (non vide, max 1000 caractères)

## 📊 Points de Contrôle

### **Interface Profil Business**
- [ ] Onglet "Mes publicités" présent
- [ ] Sous-onglet "Ads Comments" fonctionnel
- [ ] Statistiques des campagnes affichées
- [ ] Liste des commentaires chargée
- [ ] Bouton "Répondre" visible sur commentaires sans réponse

### **Formulaire de Réponse**
- [ ] Textarea pour saisir la réponse
- [ ] Compteur de caractères (0/1000)
- [ ] Boutons "Annuler" et "Répondre"
- [ ] Validation du contenu
- [ ] Message de succès après publication

### **Affichage Fil d'Actualité**
- [ ] Section commentaires visible sur publicités
- [ ] Bouton "Voir les commentaires" fonctionnel
- [ ] Commentaires originaux affichés
- [ ] Réponses entreprises avec design différencié
- [ ] Badge "Entreprise" présent
- [ ] Horodatage correct

### **Sécurité et Validation**
- [ ] Seules les entreprises propriétaires peuvent répondre
- [ ] Une seule réponse par commentaire
- [ ] Validation côté frontend et backend
- [ ] Politiques RLS respectées

## 🔍 Dépannage

### **Problème : Onglet "Mes publicités" absent**
**Solutions :**
1. Vérifier que l'utilisateur a le rôle 'business'
2. Vérifier l'import du composant BusinessAdsManager
3. Redémarrer l'application

### **Problème : Aucun commentaire affiché**
**Solutions :**
1. Vérifier que des commentaires existent sur les publicités de l'entreprise
2. Vérifier les politiques RLS en base de données
3. Consulter les logs de la console

### **Problème : Impossible de répondre**
**Solutions :**
1. Vérifier que l'entreprise est propriétaire de la publicité
2. Vérifier qu'il n'y a pas déjà une réponse
3. Vérifier la migration de la table ad_business_responses

### **Problème : Réponses non visibles dans le fil**
**Solutions :**
1. Vérifier que la réponse est approuvée (is_approved = true)
2. Vérifier le service getAdCommentsWithResponses
3. Actualiser la page

## 📝 Données de Test

### **Créer des Commentaires de Test**
```sql
-- Insérer un commentaire de test (remplacez les UUIDs)
INSERT INTO ad_comments (campaign_id, user_id, content, is_approved)
VALUES (
  '00000000-0000-0000-0000-000000000001', -- ID campagne
  'user-standard-uuid',                    -- ID utilisateur standard
  'Super produit ! Je recommande vivement cette huile de beauté.',
  true
);
```

### **Vérifier les Réponses**
```sql
-- Voir les réponses créées
SELECT 
  abr.*,
  ac.content as comment_content,
  p.username as business_name
FROM ad_business_responses abr
JOIN ad_comments ac ON abr.comment_id = ac.id
JOIN profiles p ON abr.business_id = p.id
ORDER BY abr.created_at DESC;
```

## 🎯 Checklist de Validation

### **Fonctionnalités Core**
- [ ] Onglet "Mes publicités" accessible
- [ ] Sous-onglet "Ads Comments" fonctionnel
- [ ] Liste des commentaires chargée
- [ ] Formulaire de réponse opérationnel
- [ ] Publication de réponses réussie

### **Affichage Public**
- [ ] Réponses visibles dans le fil d'actualité
- [ ] Design différencié pour les entreprises
- [ ] Badge "Entreprise" affiché
- [ ] Horodatage correct

### **Sécurité**
- [ ] Restrictions respectées
- [ ] Validation des permissions
- [ ] Politiques RLS actives
- [ ] Contraintes d'unicité respectées

### **UX/UI**
- [ ] Interface intuitive
- [ ] Messages d'erreur clairs
- [ ] Feedback utilisateur approprié
- [ ] Design cohérent

## 📞 Support

### **En cas de problème :**
1. **Console navigateur** : Vérifier les erreurs JavaScript
2. **Logs Supabase** : Consulter les erreurs de base de données
3. **Network tab** : Vérifier les requêtes API
4. **Documentation** : Consulter GUIDE_REPONSES_ENTREPRISES_PUBLICITES.md

### **Contacts :**
- **Développement** : Équipe frontend/backend
- **Base de données** : Équipe DevOps
- **Tests** : Équipe QA

---

## ✅ Résumé

Une fois tous les tests validés, le système de réponses des entreprises aux publicités est **opérationnel** et prêt pour la production !

**Fonctionnalités testées :**
- ✅ Interface de gestion des commentaires
- ✅ Système de réponses sécurisé
- ✅ Affichage dans le fil d'actualité
- ✅ Restrictions et validations
- ✅ Expérience utilisateur complète

**Prêt à améliorer l'engagement client !** 🚀
