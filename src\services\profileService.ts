import { supabase } from '../lib/supabase';
import { IUser, IProfileFormData, IProfileCompletionStats, IProfileUpdateResponse } from '../types';

export class ProfileService {
  /**
   * Met à jour le profil utilisateur avec les nouveaux champs du formulaire
   */
  static async updateProfile(
    userId: string, 
    formData: IProfileFormData
  ): Promise<IProfileUpdateResponse> {
    try {
      // Préparer les données pour la base de données
      const updateData: any = {
        username: formData.username,
        city: formData.city || null,
        country: formData.country || null,
        age: formData.age ? parseInt(formData.age) : null,
        gender: formData.gender || null,
        bio: formData.bio || null,
        website: formData.website || null,
        phone: formData.phone || null,
        profession: formData.profession || null,
        interests: formData.interests || null,
        updated_at: new Date().toISOString()
      };

      // Gérer l'upload des images si nécessaire
      if (formData.profilePictureFile) {
        const profilePictureUrl = await this.uploadProfilePicture(userId, formData.profilePictureFile);
        if (profilePictureUrl) {
          updateData.profile_picture = profilePictureUrl;
        }
      }

      if (formData.coverPhotoFile) {
        const coverPhotoUrl = await this.uploadCoverPhoto(userId, formData.coverPhotoFile);
        if (coverPhotoUrl) {
          updateData.cover_photo_url = coverPhotoUrl;
        }
      }

      // Utiliser la fonction SQL personnalisée pour la mise à jour
      const { data, error } = await supabase.rpc('update_user_profile', {
        profile_id: userId,
        new_username: updateData.username,
        new_city: updateData.city,
        new_country: updateData.country,
        new_age: updateData.age,
        new_gender: updateData.gender,
        new_bio: updateData.bio,
        new_website: updateData.website,
        new_phone: updateData.phone,
        new_profession: updateData.profession,
        new_interests: updateData.interests,
        new_profile_picture: updateData.profile_picture,
        new_cover_photo_url: updateData.cover_photo_url
      });

      if (error) {
        console.error('Erreur lors de la mise à jour du profil:', error);
        return { error: error.message };
      }

      return data as IProfileUpdateResponse;
    } catch (error) {
      console.error('Erreur dans updateProfile:', error);
      return { error: 'Une erreur inattendue s\'est produite' };
    }
  }

  /**
   * Récupère les statistiques de complétion du profil
   */
  static async getProfileCompletionStats(userId: string): Promise<IProfileCompletionStats | null> {
    try {
      const { data, error } = await supabase.rpc('get_profile_completion_stats', {
        profile_id: userId
      });

      if (error) {
        console.error('Erreur lors de la récupération des statistiques:', error);
        return null;
      }

      return data as IProfileCompletionStats;
    } catch (error) {
      console.error('Erreur dans getProfileCompletionStats:', error);
      return null;
    }
  }

  /**
   * Upload d'une photo de profil
   */
  private static async uploadProfilePicture(userId: string, file: File): Promise<string | null> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}/profile.${fileExt}`;

      const { data, error } = await supabase.storage
        .from('profile-pictures')
        .upload(fileName, file, {
          upsert: true,
          contentType: file.type
        });

      if (error) {
        console.error('Erreur upload photo de profil:', error);
        return null;
      }

      // Récupérer l'URL publique
      const { data: urlData } = supabase.storage
        .from('profile-pictures')
        .getPublicUrl(fileName);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Erreur dans uploadProfilePicture:', error);
      return null;
    }
  }

  /**
   * Upload d'une photo de couverture
   */
  private static async uploadCoverPhoto(userId: string, file: File): Promise<string | null> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}/cover.${fileExt}`;

      const { data, error } = await supabase.storage
        .from('cover-photos')
        .upload(fileName, file, {
          upsert: true,
          contentType: file.type
        });

      if (error) {
        console.error('Erreur upload photo de couverture:', error);
        return null;
      }

      // Récupérer l'URL publique
      const { data: urlData } = supabase.storage
        .from('cover-photos')
        .getPublicUrl(fileName);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Erreur dans uploadCoverPhoto:', error);
      return null;
    }
  }

  /**
   * Récupère un profil utilisateur complet avec tous les champs
   */
  static async getFullProfile(userId: string): Promise<IUser | null> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          username,
          email,
          profile_picture,
          cover_photo_url,
          role,
          status,
          city,
          country,
          age,
          gender,
          bio,
          website,
          phone,
          profession,
          interests,
          post_count,
          comment_count,
          followers_count,
          following_count,
          created_at,
          updated_at,
          profile_completed_at
        `)
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Erreur lors de la récupération du profil:', error);
        return null;
      }

      // Convertir les données de la DB vers le format IUser
      const user: IUser = {
        id: data.id,
        username: data.username,
        email: data.email,
        profilePicture: data.profile_picture,
        coverPhotoUrl: data.cover_photo_url,
        role: data.role,
        status: data.status,
        city: data.city,
        country: data.country,
        age: data.age,
        gender: data.gender,
        bio: data.bio,
        website: data.website,
        phone: data.phone,
        profession: data.profession,
        interests: data.interests,
        post_count: data.post_count || 0,
        comment_count: data.comment_count || 0,
        followers_count: data.followers_count || 0,
        following_count: data.following_count || 0,
        createdAt: new Date(data.created_at),
        profile_completed_at: data.profile_completed_at
      };

      return user;
    } catch (error) {
      console.error('Erreur dans getFullProfile:', error);
      return null;
    }
  }

  /**
   * Valide les données du formulaire avant soumission
   */
  static validateFormData(formData: IProfileFormData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validation du nom d'utilisateur
    if (!formData.username || formData.username.trim().length < 3) {
      errors.push('Le nom d\'utilisateur doit contenir au moins 3 caractères');
    }

    if (formData.username && formData.username.length > 30) {
      errors.push('Le nom d\'utilisateur ne peut pas dépasser 30 caractères');
    }

    // Validation de l'âge
    if (formData.age) {
      const age = parseInt(formData.age);
      if (isNaN(age) || age < 13 || age > 120) {
        errors.push('L\'âge doit être compris entre 13 et 120 ans');
      }
    }

    // Validation de la biographie
    if (formData.bio && formData.bio.length > 500) {
      errors.push('La biographie ne peut pas dépasser 500 caractères');
    }

    // Validation du site web
    if (formData.website && formData.website.trim() !== '') {
      const urlPattern = /^https?:\/\/[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(\/.*)?$/;
      if (!urlPattern.test(formData.website)) {
        errors.push('Le format du site web n\'est pas valide (ex: https://exemple.com)');
      }
    }

    // Validation du téléphone
    if (formData.phone && formData.phone.trim() !== '') {
      const phonePattern = /^\+?[1-9]\d{1,14}$/;
      if (!phonePattern.test(formData.phone.replace(/\s/g, ''))) {
        errors.push('Le format du numéro de téléphone n\'est pas valide');
      }
    }

    // Validation de la profession
    if (formData.profession && formData.profession.length > 100) {
      errors.push('La profession ne peut pas dépasser 100 caractères');
    }

    // Validation des centres d'intérêt
    if (formData.interests && formData.interests.length > 500) {
      errors.push('Les centres d\'intérêt ne peuvent pas dépasser 500 caractères');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Nettoie et formate les centres d'intérêt
   */
  static cleanInterests(interests: string): string {
    if (!interests || interests.trim() === '') {
      return '';
    }
    
    // Nettoyer les espaces en trop et normaliser les virgules
    return interests
      .split(',')
      .map(interest => interest.trim())
      .filter(interest => interest.length > 0)
      .join(', ');
  }
}
