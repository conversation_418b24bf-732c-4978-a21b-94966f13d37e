import React, { useState, useEffect } from 'react';
import CreatePostForm from '../components/posts/CreatePostForm';
import PostCard from '../components/posts/PostCard';
import Card, { CardBody } from '../components/ui/Card';
import { usePosts } from '../context/PostsContext';
import { useAuth } from '../context/AuthContext';
import Button from '../components/ui/Button';
import { Filter, TrendingUp, Clock, Star, TestTube } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import FeedAdCard, { FeedAd } from '../components/ads/FeedAdCard';
import AdButtonTest from '../components/ads/AdButtonTest';
import { DEFAULT_IMAGES_SIZED } from '../constants/defaultImages';

import '../utils/fixUserRoles'; // Import pour exposer les fonctions de debug

// Données mockées pour les publicités dans le fil d'actualité
// Utilisation d'UUIDs valides pour compatibilité avec la base de données
const mockFeedAds: FeedAd[] = [
  {
    id: '00000000-0000-0000-0000-000000000001',
    title: 'Promotion Été - Huile de Beauté',
    description: 'Profitez de notre offre spéciale été avec 20% de réduction sur notre huile de beauté Olgane.',
    imageUrl: DEFAULT_IMAGES_SIZED.PROMO_BANNER,
    targetUrl: '#',
    businessName: 'Olgane Cosmetics',
    businessLogo: DEFAULT_IMAGES_SIZED.BUSINESS_LOGO,
    discount: 20
  },
  {
    id: '00000000-0000-0000-0000-000000000002',
    title: 'Lancement Crème Hydratante',
    description: 'Découvrez notre nouvelle crème hydratante visage pour une peau éclatante et revitalisée.',
    imageUrl: DEFAULT_IMAGES_SIZED.PROMO_BANNER,
    targetUrl: '#',
    businessName: 'Olgane Cosmetics',
    businessLogo: DEFAULT_IMAGES_SIZED.BUSINESS_LOGO
  },
  {
    id: '00000000-0000-0000-0000-000000000003',
    title: 'Promo Flash Sérum Anti-âge',
    description: 'Offre flash de 48h : -30% sur notre sérum anti-âge révolutionnaire.',
    imageUrl: DEFAULT_IMAGES_SIZED.PROMO_BANNER,
    targetUrl: '#',
    businessName: 'Olgane Cosmetics',
    businessLogo: DEFAULT_IMAGES_SIZED.BUSINESS_LOGO,
    discount: 30
  }
];

const HomePage: React.FC = () => {
  const { posts, loading } = usePosts();
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [activeFilter, setActiveFilter] = useState('recent');



  // État pour les publicités actuellement affichées dans le fil d'actualité
  const [currentAdIndex, setCurrentAdIndex] = useState(0);
  const [feedAds, setFeedAds] = useState<FeedAd[]>(mockFeedAds);

  // Rotation automatique des publicités
  useEffect(() => {
    const adRotationInterval = setInterval(() => {
      setCurrentAdIndex(prevIndex => (prevIndex + 1) % feedAds.length);
    }, 60000); // Rotation toutes les 60 secondes

    return () => clearInterval(adRotationInterval);
  }, [feedAds.length]);

  const filters = [
    { id: 'recent', label: 'Les plus récents', icon: <Clock size={16} /> },
    { id: 'trending', label: 'Tendances', icon: <TrendingUp size={16} /> },
    { id: 'top', label: 'Mieux notés', icon: <Star size={16} /> },
  ];

  // Sort posts based on active filter
  const sortedPosts = React.useMemo(() => {
    if (activeFilter === 'recent') {
      return [...posts].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    } else if (activeFilter === 'trending') {
      return [...posts].sort((a, b) => (b.likes.length + b.comments.length * 2) - (a.likes.length + a.comments.length * 2));
    } else if (activeFilter === 'top') {
      return [...posts].sort((a, b) => b.rating - a.rating);
    }
    return posts;
  }, [posts, activeFilter]);

  // DEBUG: Log all post types to help debug missing posts
  useEffect(() => {
    if (posts && posts.length > 0) {
      // eslint-disable-next-line no-console
      console.log('Types de posts chargés sur la HomePage:', posts.map(p => p.type));
    }
  }, [posts]);

  return (
    <div className="w-full max-w-2xl mx-auto px-4">
      {isAuthenticated && <CreatePostForm />}

      {/* Filters */}
      <Card className="mb-4">
        <CardBody className="p-3">
          <div className="flex items-center">
            <Filter size={16} className="text-gray-500 mr-2" />
            <span className="text-sm font-medium text-gray-700 mr-4">Filtrer:</span>
            <div className="flex space-x-2 overflow-x-auto scrollbar-hide">
              {filters.map((filter) => (
                <Button
                  key={filter.id}
                  variant={activeFilter === filter.id ? 'primary' : 'outline'}
                  size="sm"
                  leftIcon={filter.icon}
                  onClick={() => setActiveFilter(filter.id)}
                >
                  {filter.label}
                </Button>
              ))}
            </div>
          </div>
        </CardBody>
      </Card>



      {/* Debug: Post Type Statistics */}
      {false && process.env.NODE_ENV === 'development' && Object.keys(typeStats).length > 0 && (
        <Card className="mb-4 border-yellow-200 bg-yellow-50">
          <CardBody className="p-3">
            <div className="text-sm space-y-2">
              <div>
                <strong>Debug - Types de posts:</strong>
                {Object.entries(typeStats).map(([type, count]) => (
                  <span key={type} className="ml-2 px-2 py-1 bg-yellow-200 rounded text-xs">
                    {type}: {count}
                  </span>
                ))}
              </div>
              <div>
                <strong>Debug - Noms d'entreprises:</strong>
                <span className="ml-2 px-2 py-1 bg-green-200 rounded text-xs">
                  ✅ Valides: {validBusinessNames}
                </span>
                <span className="ml-2 px-2 py-1 bg-red-200 rounded text-xs">
                  ❌ Invalides: {invalidBusinessNames}
                </span>
                <span className="ml-2 px-2 py-1 bg-blue-200 rounded text-xs">
                  🏢 Entreprises uniques: {uniqueBusinesses}
                </span>
              </div>
              <div>
                <strong>Debug - Images:</strong>
                <span className="ml-2 px-2 py-1 bg-green-200 rounded text-xs">
                  📷 Avec images: {postsWithImages}
                </span>
                <span className="ml-2 px-2 py-1 bg-gray-200 rounded text-xs">
                  📭 Sans images: {postsWithoutImages}
                </span>
                <span className="ml-2 px-2 py-1 bg-blue-200 rounded text-xs">
                  🖼️ Total images: {totalImages}
                </span>
                <span className="ml-2 px-2 py-1 bg-purple-200 rounded text-xs">
                  📊 Taux de réussite: {imageSuccessRate}%
                </span>
              </div>
              <div>
                <strong>Debug - Rôles utilisateur:</strong>
                <span className="ml-2 px-2 py-1 bg-blue-200 rounded text-xs">
                  🏢 Entreprises: {businessPosts}
                </span>
                <span className="ml-2 px-2 py-1 bg-gray-200 rounded text-xs">
                  👤 Membres: {memberPosts}
                </span>
                <span className="ml-2 px-2 py-1 bg-red-200 rounded text-xs">
                  👑 Admins: {adminPosts}
                </span>
                <span className="ml-2 px-2 py-1 bg-orange-200 rounded text-xs">
                  ⚠️ Rôles manquants: {missingRoles}
                </span>
              </div>
              <div>
                <strong>Debug - Cohérence Type/Rôle:</strong>
                <span className="ml-2 px-2 py-1 bg-green-200 rounded text-xs">
                  ✅ Valides: {validPosts}
                </span>
                <span className="ml-2 px-2 py-1 bg-red-200 rounded text-xs">
                  ❌ Invalides: {invalidPosts}
                </span>
                <span className="ml-2 px-2 py-1 bg-blue-200 rounded text-xs">
                  🏢 Demandes d'avis: {businessReviews}
                </span>
                <span className="ml-2 px-2 py-1 bg-purple-200 rounded text-xs">
                  👤 Avis utilisateurs: {userOpinions}
                </span>
                <span className="ml-2 px-2 py-1 bg-yellow-200 rounded text-xs">
                  📊 Taux de cohérence: {validationRate}%
                </span>
              </div>

              {/* Boutons de test rapide */}
              <div className="mt-3 pt-3 border-t border-yellow-300">
                <strong>Tests rapides:</strong>
                <div className="flex flex-wrap gap-2 mt-2">
                  <Button
                    size="sm"
                    variant="outline"
                    leftIcon={<TestTube size={14} />}
                    onClick={() => navigate('/ads-test')}
                    className="text-xs"
                  >
                    Test Publicités
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    leftIcon={<TestTube size={14} />}
                    onClick={() => navigate('/features')}
                    className="text-xs"
                  >
                    Fonctionnalités
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    leftIcon={<TestTube size={14} />}
                    onClick={() => navigate('/business-subscription')}
                    className="text-xs"
                  >
                    Abonnements Business
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    leftIcon={<TestTube size={14} />}
                    onClick={() => navigate('/offers-test')}
                    className="text-xs"
                  >
                    Test Offres
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    leftIcon={<TestTube size={14} />}
                    onClick={() => navigate('/offers-layout-test')}
                    className="text-xs"
                  >
                    Layout 2 Col
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    leftIcon={<TestTube size={14} />}
                    onClick={() => navigate('/ad-restrictions-test')}
                    className="text-xs"
                  >
                    Restrictions Pub
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    leftIcon={<TestTube size={14} />}
                    onClick={() => navigate('/ad-buttons-test')}
                    className="text-xs"
                  >
                    Boutons Pub
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    leftIcon={<TestTube size={14} />}
                    onClick={() => navigate('/ad-diagnostic')}
                    className="text-xs"
                  >
                    Diagnostic Pub
                  </Button>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}



      {loading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <>
          {sortedPosts.length > 0 ? (
            // Insérer une publicité tous les 5 posts
            sortedPosts.map((post, index) => {
              // Déterminer si nous devons afficher une publicité après ce post
              const shouldShowAd = (index + 1) % 5 === 0 && index < sortedPosts.length - 1;

              return (
                <React.Fragment key={post.id}>
                  <PostCard post={post} />
                  {shouldShowAd && (
                    <FeedAdCard
                      ad={feedAds[(currentAdIndex + Math.floor(index / 5)) % feedAds.length]}
                    />
                  )}
                </React.Fragment>
              );
            })
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">Aucune publication disponible.</p>
              {isAuthenticated && (
                <p className="mt-2 text-gray-600">Soyez le premier à partager votre expérience!</p>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default HomePage;