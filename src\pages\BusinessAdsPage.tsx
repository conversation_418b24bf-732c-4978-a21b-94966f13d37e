import React, { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { UserRole } from '../types';
import { createAdImageProps } from '../utils/imageUtils';
import { AdBillingProvider } from '../context/AdBillingContext';
import {
  Megaphone, DollarSign, Filter, Search, ArrowDownUp, ChevronDown, Eye, Edit, Trash2, Plus,
  TrendingUp, Target, BarChart3, PieChart, Calendar, Bell, Download, Settings,
  Zap, Brain, Users, Clock, AlertTriangle, CheckCircle, XCircle, Pause, Wallet
} from 'lucide-react';
import Button from '../components/ui/Button';
import Card, { CardBody } from '../components/ui/Card';
import AdCampaignDetails from '../components/ads/AdCampaignDetails';
import AdCampaignForm from '../components/ads/AdCampaignForm';
import AdPerformanceCharts from '../components/ads/AdPerformanceCharts';
import AdvancedAnalyticsDashboard from '../components/ads/AdvancedAnalyticsDashboard';
import AIAssistant from '../components/ads/AIAssistant';
import AdvancedExport from '../components/ads/AdvancedExport';
import AdWalletManager from '../components/ads/AdWalletManager';
import DemoModeNotification from '../components/ads/DemoModeNotification';
import { formatAmount } from '../utils/formatUtils';
import '../styles/BusinessAds.css';
import '../styles/AdvancedAnalytics.css';
import '../styles/AdStatistics.css';
import '../styles/AdCampaignForm.css';



// Types pour les publicités
export interface AdCampaign {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  targetAudience?: string[];
  budget: number;
  dailyBudget: number;
  startDate: string | Date;
  endDate: string | Date;
  status: 'active' | 'paused' | 'completed' | 'draft';
  impressions: number;
  clicks: number;
  conversions?: number;
  ctr: number;
  cpc?: number;
  spent: number;
  remainingBudget?: number;
  showInOffersAndPromotions: boolean;
  showInRightSidebar: boolean;
  showInNewsFeed: boolean;
  discount?: number;
  targeting?: {
    demographics?: {
      ageRanges?: string[];
      genders?: string[];
      interests?: string[];
    };
    location?: {
      countries?: string[];
      cities?: string[];
      radius?: number;
    };
    timing?: {
      daysOfWeek?: string[];
      timeOfDay?: string[];
    };
    devices?: string[];
    languages?: string[];
  };
  createdAt: string;
  updatedAt: string;
}

// Type pour les statistiques des publicités
export interface AdStats {
  totalCampaigns: number;
  activeCampaigns: number;
  totalBudget: number;
  totalSpent: number;
  totalImpressions: number;
  totalClicks: number;
  averageCTR: number;
}

// Type pour le formulaire de création/édition de publicité
export interface AdFormData {
  title: string;
  description: string;
  imageUrl: string;
  targetAudience: string[];
  budget: string;
  dailyBudget: string;
  startDate: string;
  endDate: string;
  status: 'active' | 'paused' | 'draft';
  showInOffersAndPromotions: boolean;
  showInRightSidebar: boolean;
  showInNewsFeed: boolean;
  discount: string;
  targeting: {
    demographics: {
      ageRanges: string[];
      genders: string[];
      interests: string[];
    };
    location: {
      countries: string[];
      cities: string[];
      radius: number;
    };
    timing: {
      daysOfWeek: string[];
      timeOfDay: string[];
    };
    devices: string[];
    languages: string[];
  };
}

const BusinessAdsPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [adCampaigns, setAdCampaigns] = useState<AdCampaign[]>([]);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('date');
  const [sortOrder, setSortOrder] = useState<string>('desc');
  const [showAdForm, setShowAdForm] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState<AdCampaign | null>(null);
  const [selectedViewCampaign, setSelectedViewCampaign] = useState<AdCampaign | null>(null);

  // États pour les nouvelles fonctionnalités
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [showAIAssistant, setShowAIAssistant] = useState(false);
  const [showBudgetManager, setShowBudgetManager] = useState(false);
  const [showReports, setShowReports] = useState(false);
  const [showAdvancedExport, setShowAdvancedExport] = useState(false);
  const [showWalletManager, setShowWalletManager] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');
  const [notifications, setNotifications] = useState<Array<{
    id: string;
    type: 'success' | 'warning' | 'error' | 'info';
    title: string;
    message: string;
    timestamp: Date;
  }>>([]);
  const [darkMode, setDarkMode] = useState(false);

  // État du formulaire
  const [formData, setFormData] = useState<AdFormData>({
    title: '',
    description: '',
    imageUrl: '',
    targetAudience: [],
    budget: '',
    dailyBudget: '',
    startDate: '',
    endDate: '',
    status: 'draft',
    showInOffersAndPromotions: false,
    showInRightSidebar: false,
    showInNewsFeed: false,
    discount: '',
    targeting: {
      demographics: {
        ageRanges: [],
        genders: [],
        interests: []
      },
      location: {
        countries: [],
        cities: [],
        radius: 0
      },
      timing: {
        daysOfWeek: [],
        timeOfDay: []
      },
      devices: [],
      languages: []
    }
  });

  // État pour les erreurs de formulaire et l'aperçu d'image
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [imagePreview, setImagePreview] = useState<string>('');

  const [adStats, setAdStats] = useState<AdStats>({
    totalCampaigns: 0,
    activeCampaigns: 0,
    totalBudget: 0,
    totalSpent: 0,
    totalImpressions: 0,
    totalClicks: 0,
    averageCTR: 0
  });

  // Analytics avancées
  const [advancedAnalytics, setAdvancedAnalytics] = useState({
    roas: 4.2, // Return on Ad Spend
    cpa: 2500, // Cost per Acquisition
    conversionRate: 3.8,
    avgOrderValue: 18500,
    lifetimeValue: 45000,
    reachRate: 85.2,
    frequencyRate: 2.3,
    brandAwareness: 67.8,
    engagementRate: 5.4,
    videoCompletionRate: 78.5,
    socialShares: 1250,
    websiteTraffic: 8500,
    leadGeneration: 340,
    customerRetention: 72.3,
    marketShare: 12.8
  });

  // Données pour les graphiques
  const [chartData, setChartData] = useState({
    performance: {
      labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
      impressions: [12500, 15200, 18300, 16800, 22100, 19500, 17200],
      clicks: [850, 1020, 1240, 1150, 1480, 1320, 1180],
      conversions: [125, 148, 180, 165, 215, 192, 170],
      spend: [5000, 6200, 7500, 6800, 8900, 7800, 6900]
    },
    audience: {
      demographics: {
        age: [
          { range: '18-24', percentage: 15.2 },
          { range: '25-34', percentage: 32.8 },
          { range: '35-44', percentage: 28.5 },
          { range: '45-54', percentage: 16.3 },
          { range: '55+', percentage: 7.2 }
        ],
        gender: [
          { type: 'Femmes', percentage: 68.5 },
          { type: 'Hommes', percentage: 31.5 }
        ],
        interests: [
          { category: 'Beauté', percentage: 45.2 },
          { category: 'Mode', percentage: 32.8 },
          { category: 'Santé', percentage: 28.5 },
          { category: 'Lifestyle', percentage: 22.1 },
          { category: 'Shopping', percentage: 18.7 }
        ]
      },
      geography: [
        { city: 'Dakar', percentage: 52.3 },
        { city: 'Thiès', percentage: 18.7 },
        { city: 'Saint-Louis', percentage: 12.4 },
        { city: 'Kaolack', percentage: 8.9 },
        { city: 'Autres', percentage: 7.7 }
      ]
    },
    budget: {
      allocation: [
        { campaign: 'Promotion Été', spent: 45000, budget: 150000 },
        { campaign: 'Lancement Crème', spent: 65000, budget: 200000 },
        { campaign: 'Black Friday', spent: 0, budget: 300000 }
      ],
      timeline: [
        { month: 'Jan', budget: 250000, spent: 235000 },
        { month: 'Fév', budget: 280000, spent: 275000 },
        { month: 'Mar', budget: 320000, spent: 310000 },
        { month: 'Avr', budget: 350000, spent: 340000 },
        { month: 'Mai', budget: 400000, spent: 110000 }
      ]
    }
  });

  // Fonction pour formater la date pour l'affichage
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };

  // Fonction pour formater la date pour les champs input date
  const formatDateForInput = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Fonctions pour les nouvelles fonctionnalités
  const generateAIRecommendations = () => {
    return [
      {
        type: 'optimization',
        title: 'Optimisation du ciblage',
        description: 'Votre audience 25-34 ans montre un CTR 40% plus élevé. Augmentez le budget pour ce segment.',
        impact: 'high',
        estimatedImprovement: '+25% CTR'
      },
      {
        type: 'budget',
        title: 'Réallocation budgétaire',
        description: 'La campagne "Promotion Été" performe mieux le weekend. Ajustez la planification.',
        impact: 'medium',
        estimatedImprovement: '+15% conversions'
      },
      {
        type: 'creative',
        title: 'Test créatif',
        description: 'Les visuels avec des personnes génèrent 30% plus d\'engagement. Testez de nouvelles créations.',
        impact: 'medium',
        estimatedImprovement: '+20% engagement'
      }
    ];
  };

  const generatePerformancePredictions = () => {
    return {
      nextWeek: {
        impressions: 125000,
        clicks: 8500,
        conversions: 1200,
        spend: 52000,
        confidence: 85
      },
      nextMonth: {
        impressions: 520000,
        clicks: 35000,
        conversions: 4800,
        spend: 215000,
        confidence: 78
      }
    };
  };

  const exportReport = (format: 'pdf' | 'excel') => {
    // Ouvrir le modal d'export avancé
    setShowAdvancedExport(true);
  };

  const handleAdvancedExport = (format: 'pdf' | 'excel', options: any) => {
    // Ajouter une notification
    addNotification('success', 'Export réussi', `Rapport ${format.toUpperCase()} téléchargé avec succès`);
  };

  const addNotification = (type: 'success' | 'warning' | 'error' | 'info', title: string, message: string) => {
    const newNotification = {
      id: Date.now().toString(),
      type,
      title,
      message,
      timestamp: new Date()
    };
    setNotifications(prev => [newNotification, ...prev.slice(0, 4)]); // Garder seulement 5 notifications
  };

  const optimizeCampaignBudget = (campaignId: string) => {
    // Simulation d'optimisation automatique
    const updatedCampaigns = adCampaigns.map(campaign => {
      if (campaign.id === campaignId) {
        const optimizedBudget = Math.round(campaign.dailyBudget * 1.2); // Augmentation de 20%
        addNotification('info', 'Budget optimisé', `Budget quotidien ajusté à ${formatAmount(optimizedBudget)} pour une meilleure performance`);
        return { ...campaign, dailyBudget: optimizedBudget };
      }
      return campaign;
    });
    setAdCampaigns(updatedCampaigns);
  };



  // Fonction pour créer une nouvelle campagne
  const handleCreateCampaign = (e?: React.MouseEvent) => {
    // Empêcher le comportement par défaut si l'événement est fourni
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Réinitialiser le formulaire
    setFormData({
      title: '',
      description: '',
      imageUrl: '',
      targetAudience: [],
      budget: '',
      dailyBudget: '',
      startDate: formatDateForInput(new Date()),
      endDate: formatDateForInput(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)), // +30 jours par défaut
      status: 'draft',
      showInOffersAndPromotions: false,
      showInRightSidebar: false,
      showInNewsFeed: false,
      discount: '',
      targeting: {
        demographics: {
          ageRanges: [],
          genders: [],
          interests: []
        },
        location: {
          countries: ['Sénégal'],
          cities: [],
          radius: 0
        },
        timing: {
          daysOfWeek: ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'],
          timeOfDay: ['morning', 'afternoon', 'evening', 'night']
        },
        devices: ['Mobile', 'Desktop', 'Tablet'],
        languages: ['Français']
      }
    });
    setImagePreview('');
    setFormErrors({});
    setSelectedCampaign(null);
    setIsEditing(false);
    setShowAdForm(true);

    // Retourner false pour empêcher la navigation par défaut
    return false;
  };

  // Fonction pour éditer une campagne existante
  const handleEditCampaign = (campaign: AdCampaign) => {
    setFormData({
      title: campaign.title,
      description: campaign.description,
      imageUrl: campaign.imageUrl,
      targetAudience: campaign.targetAudience || [],
      budget: campaign.budget.toString(),
      dailyBudget: campaign.dailyBudget.toString(),
      startDate: formatDateForInput(typeof campaign.startDate === 'string' ? new Date(campaign.startDate) : campaign.startDate),
      endDate: formatDateForInput(typeof campaign.endDate === 'string' ? new Date(campaign.endDate) : campaign.endDate),
      status: campaign.status as 'active' | 'paused' | 'draft',
      showInOffersAndPromotions: campaign.showInOffersAndPromotions,
      showInRightSidebar: campaign.showInRightSidebar,
      showInNewsFeed: campaign.showInNewsFeed,
      discount: typeof campaign.discount === 'number' ? campaign.discount.toString() : '',
      targeting: campaign.targeting ? {
        demographics: {
          ageRanges: campaign.targeting.demographics?.ageRanges || [],
          genders: campaign.targeting.demographics?.genders || [],
          interests: campaign.targeting.demographics?.interests || []
        },
        location: {
          countries: campaign.targeting.location?.countries || [],
          cities: campaign.targeting.location?.cities || [],
          radius: campaign.targeting.location?.radius || 0
        },
        timing: {
          daysOfWeek: campaign.targeting.timing?.daysOfWeek || [],
          timeOfDay: campaign.targeting.timing?.timeOfDay || []
        },
        devices: campaign.targeting.devices || [],
        languages: campaign.targeting.languages || []
      } : {
        demographics: { ageRanges: [], genders: [], interests: [] },
        location: { countries: [], cities: [], radius: 0 },
        timing: { daysOfWeek: [], timeOfDay: [] },
        devices: [],
        languages: []
      }
    });
    setImagePreview(campaign.imageUrl);
    setFormErrors({});
    setSelectedCampaign(campaign);
    setIsEditing(true);
    setShowAdForm(true);
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'paused':
        return 'En pause';
      case 'completed':
        return 'Terminée';
      case 'draft':
        return 'Brouillon';
      default:
        return status;
    }
  };

  const handleChangeStatus = (campaignId: string, newStatus: 'active' | 'paused' | 'completed' | 'draft') => {
    const updatedCampaigns = adCampaigns.map(campaign => {
      if (campaign.id === campaignId) {
        return { ...campaign, status: newStatus };
      }
      return campaign;
    });

    setAdCampaigns(updatedCampaigns);

    const activeCampaigns = updatedCampaigns.filter(campaign => campaign.status === 'active').length;
    setAdStats({
      ...adStats,
      activeCampaigns
    });
  };

  useEffect(() => {
    if (currentUser && currentUser.role === UserRole.BUSINESS) {
      const mockAdCampaigns: AdCampaign[] = [
        {
          id: 'AD-001',
          title: 'Promotion Été - Huile de Beauté',
          description: 'Profitez de notre offre spéciale été avec 20% de réduction sur notre huile de beauté Olgane.',
          imageUrl: 'https://via.placeholder.com/600x300?text=Promo+Ete',
          targetAudience: ['Femmes', '25-45 ans', 'Intérêt beauté'],
          budget: 150000,
          dailyBudget: 5000,
          startDate: new Date('2025-05-01'),
          endDate: new Date('2025-06-30'),
          status: 'active',
          impressions: 12500,
          clicks: 850,
          conversions: 125,
          ctr: 6.8,
          cpc: 176.47,
          spent: 45000,
          remainingBudget: 105000,
          showInOffersAndPromotions: true,
          showInRightSidebar: true,
          showInNewsFeed: true,
          discount: 20,
          targeting: {
            demographics: {
              ageRanges: ['25-34', '35-44'],
              genders: ['Femmes'],
              interests: ['Beauté', 'Santé', 'Mode']
            },
            location: {
              countries: ['Sénégal'],
              cities: ['Dakar', 'Thiès'],
              radius: 0
            },
            timing: {
              daysOfWeek: ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'],
              timeOfDay: ['Matin', 'Après-midi', 'Soir', 'Nuit']
            },
            devices: ['Mobile', 'Desktop', 'Tablet'],
            languages: ['Français']
          },
          createdAt: new Date('2025-04-01').toISOString(),
          updatedAt: new Date('2025-05-01').toISOString()
        },
        {
          id: 'AD-002',
          title: 'Lancement Crème Hydratante',
          description: 'Découvrez notre nouvelle crème hydratante visage pour une peau éclatante et revitalisée.',
          imageUrl: 'https://via.placeholder.com/600x300?text=Nouvelle+Creme',
          targetAudience: ['Tous genres', '20-65 ans', 'Soins de la peau'],
          budget: 200000,
          dailyBudget: 6500,
          startDate: new Date('2025-04-15'),
          endDate: new Date('2025-07-15'),
          status: 'active',
          impressions: 18200,
          clicks: 1240,
          conversions: 210,
          ctr: 6.81,
          cpc: 161.29,
          spent: 65000,
          remainingBudget: 135000,
          showInOffersAndPromotions: true,
          showInRightSidebar: false,
          showInNewsFeed: true,
          discount: 15,
          targeting: {
            demographics: {
              ageRanges: ['20-34', '35-44'],
              genders: ['Tous genres'],
              interests: ['Soins de la peau', 'Santé', 'Bien-être']
            },
            location: {
              countries: ['Sénégal'],
              cities: ['Dakar', 'Thiès', 'Saint-Louis'],
              radius: 0
            },
            timing: {
              daysOfWeek: ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'],
              timeOfDay: ['Matin', 'Après-midi', 'Soir', 'Nuit']
            },
            devices: ['Mobile', 'Desktop', 'Tablet'],
            languages: ['Français']
          },
          createdAt: new Date('2025-03-15').toISOString(),
          updatedAt: new Date('2025-04-15').toISOString()
        }
      ];

      setAdCampaigns(mockAdCampaigns);

      const totalCampaigns = mockAdCampaigns.length;
      const activeCampaigns = mockAdCampaigns.filter(campaign => campaign.status === 'active').length;
      const totalBudget = mockAdCampaigns.reduce((sum, campaign) => sum + campaign.budget, 0);
      const totalSpent = mockAdCampaigns.reduce((sum, campaign) => sum + campaign.spent, 0);
      const totalImpressions = mockAdCampaigns.reduce((sum, campaign) => sum + campaign.impressions, 0);
      const totalClicks = mockAdCampaigns.reduce((sum, campaign) => sum + campaign.clicks, 0);
      const averageCTR = totalClicks > 0 && totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;

      setAdStats({
        totalCampaigns,
        activeCampaigns,
        totalBudget,
        totalSpent,
        totalImpressions,
        totalClicks,
        averageCTR
      });

      setLoading(false);
    }
  }, [currentUser]);

  if (!currentUser || currentUser.role !== UserRole.BUSINESS) {
    return <Navigate to="/profile" replace />;
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="sr-only">Chargement...</span>
          </div>
          <p className="mt-2">Chargement de vos campagnes publicitaires...</p>
        </div>
      </div>
    );
  }

  const filteredCampaigns = adCampaigns.filter(campaign => {
    if (filterStatus !== 'all' && campaign.status !== filterStatus) {
      return false;
    }

    // Filtre par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        campaign.title.toLowerCase().includes(query) ||
        campaign.description.toLowerCase().includes(query) ||
        (campaign.targetAudience || []).some(target => target.toLowerCase().includes(query))
      );
    }

    return true;
  });

  // Trier les campagnes
  const sortedCampaigns = [...filteredCampaigns].sort((a, b) => {
    if (sortBy === 'date') {
      const aDate = typeof a.startDate === 'string' ? new Date(a.startDate) : a.startDate;
      const bDate = typeof b.startDate === 'string' ? new Date(b.startDate) : b.startDate;
      return sortOrder === 'asc'
        ? aDate.getTime() - bDate.getTime()
        : bDate.getTime() - aDate.getTime();
    } else if (sortBy === 'budget') {
      return sortOrder === 'asc'
        ? a.budget - b.budget
        : b.budget - a.budget;
    } else {
      // Performance (CTR)
      return sortOrder === 'asc'
        ? a.ctr - b.ctr
        : b.ctr - a.ctr;
    }
  });

  // Fonction pour gérer la soumission du formulaire de campagne
  const handleAdFormSubmit = (campaignData: AdCampaign) => {
    // Si nous éditons une campagne existante
    if (isEditing && selectedCampaign) {
      const updatedCampaigns = adCampaigns.map(campaign =>
        campaign.id === campaignData.id ? {
          ...campaignData,
          updatedAt: new Date().toISOString()
        } : campaign
      );
      setAdCampaigns(updatedCampaigns);
    } else {
      // Si nous créons une nouvelle campagne
      const newCampaignWithDates = {
        ...campaignData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      setAdCampaigns([...adCampaigns, newCampaignWithDates]);
    }

    // Mettre à jour les statistiques
    const updatedCampaigns = isEditing && selectedCampaign
      ? adCampaigns.map(campaign => campaign.id === campaignData.id ? campaignData : campaign)
      : [...adCampaigns, campaignData];

    const totalCampaigns = updatedCampaigns.length;
    const activeCampaigns = updatedCampaigns.filter(campaign => campaign.status === 'active').length;
    const totalBudget = updatedCampaigns.reduce((sum, campaign) => sum + campaign.budget, 0);
    const totalSpent = updatedCampaigns.reduce((sum, campaign) => sum + campaign.spent, 0);
    const totalImpressions = updatedCampaigns.reduce((sum, campaign) => sum + campaign.impressions, 0);
    const totalClicks = updatedCampaigns.reduce((sum, campaign) => sum + campaign.clicks, 0);
    const averageCTR = totalClicks > 0 && totalImpressions > 0
      ? (totalClicks / totalImpressions) * 100
      : 0;

    setAdStats({
      totalCampaigns,
      activeCampaigns,
      totalBudget,
      totalSpent,
      totalImpressions,
      totalClicks,
      averageCTR
    });

    // Fermer le formulaire
    setShowAdForm(false);
  };

  // Rendu du composant de formulaire de campagne
  const renderAdForm = () => {
    if (!showAdForm) return null;

    return (
      <AdCampaignForm
        isOpen={showAdForm}
        onClose={() => setShowAdForm(false)}
        onSubmit={handleAdFormSubmit}
        campaign={isEditing ? selectedCampaign : null}
        isEditing={isEditing}
      />
    );
  };

  // Fonction utilitaire pour la classe CSS du statut
  const getStatusClass = (status: string) => {
    switch (status) {
      case 'active':
        return 'status-active';
      case 'paused':
        return 'status-paused';
      case 'completed':
        return 'status-completed';
      case 'draft':
        return 'status-draft';
      default:
        return '';
    }
  };

  return (
    <div className={`business-ads-container ${darkMode ? 'dark-mode' : ''}`}>
      {/* Notification du mode démonstration */}
      <DemoModeNotification />

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="notifications-container">
          {notifications.map(notification => (
            <div key={notification.id} className={`notification ${notification.type}`}>
              <div className="notification-content">
                <strong>{notification.title}</strong>
                <p>{notification.message}</p>
              </div>
              <button
                className="notification-close"
                onClick={() => setNotifications(prev => prev.filter(n => n.id !== notification.id))}
              >
                <X size={16} />
              </button>
            </div>
          ))}
        </div>
      )}

      <div className="ads-header">
        <div className="header-content">
          <h1 className="ads-title">Mes Publicités</h1>
          <p className="ads-subtitle">Gérez et optimisez vos campagnes publicitaires avec l'IA</p>
        </div>

        {/* Barre d'outils avancée */}
        <div className="advanced-toolbar">
          <button
            className={`toolbar-btn analytics ${showAnalytics ? 'active' : ''}`}
            onClick={() => setShowAnalytics(!showAnalytics)}
            title="Analytics avancés"
          >
            <BarChart3 size={18} />
            Analytics
          </button>

          <button
            className="toolbar-btn ai-assistant"
            onClick={() => setShowAIAssistant(true)}
            title="Assistant IA"
          >
            <Brain size={18} />
            Assistant IA
          </button>

          <button
            className={`toolbar-btn reports ${showReports ? 'active' : ''}`}
            onClick={() => setShowReports(!showReports)}
            title="Rapports"
          >
            <Download size={18} />
            Rapports
          </button>

          <button
            className="toolbar-btn notifications"
            onClick={() => {/* Toggle notifications */}}
            title="Notifications"
          >
            <Bell size={18} />
            {notifications.length > 0 && (
              <span className="notification-badge">{notifications.length}</span>
            )}
          </button>

          <button
            className="toolbar-btn wallet"
            onClick={() => setShowWalletManager(true)}
            title="Portefeuille publicitaire"
          >
            <Wallet size={18} />
            Portefeuille
          </button>

          <button
            className="toolbar-btn settings"
            onClick={() => setDarkMode(!darkMode)}
            title="Paramètres"
          >
            <Settings size={18} />
          </button>

          <Button
            onClick={handleCreateCampaign}
            className="create-ad-button"
          >
            <Plus size={20} />
            Créer une publicité
          </Button>
        </div>
      </div>

      {/* Dashboard Analytics Avancé */}
      {showAnalytics && (
        <AdvancedAnalyticsDashboard
          analytics={advancedAnalytics}
          chartData={chartData}
          timeRange={selectedTimeRange}
          onTimeRangeChange={setSelectedTimeRange}
          onExport={exportReport}
        />
      )}

      {/* Rapports rapides */}
      {showReports && (
        <div className="quick-reports">
          <div className="reports-header">
            <h3>Rapports rapides</h3>
            <div className="reports-actions">
              <button
                className="report-btn pdf"
                onClick={() => exportReport('pdf')}
              >
                <Download size={16} />
                Export PDF
              </button>
              <button
                className="report-btn excel"
                onClick={() => exportReport('excel')}
              >
                <Download size={16} />
                Export Excel
              </button>
            </div>
          </div>

          <div className="reports-grid">
            <div className="report-card">
              <h4>Performance hebdomadaire</h4>
              <p>Analyse détaillée des 7 derniers jours</p>
              <div className="report-metrics">
                <span>ROAS: {advancedAnalytics.roas}x</span>
                <span>CTR: {adStats.averageCTR.toFixed(1)}%</span>
              </div>
            </div>

            <div className="report-card">
              <h4>Analyse d'audience</h4>
              <p>Segmentation et insights comportementaux</p>
              <div className="report-metrics">
                <span>Portée: {advancedAnalytics.reachRate}%</span>
                <span>Engagement: {advancedAnalytics.engagementRate}%</span>
              </div>
            </div>

            <div className="report-card">
              <h4>Optimisations suggérées</h4>
              <p>Recommandations basées sur l'IA</p>
              <div className="report-metrics">
                <span>Potentiel: +25% ROI</span>
                <span>Actions: 3 disponibles</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Statistiques des campagnes */}
      <div className="ads-stats-container">
        <Card className="stats-card">
          <CardBody>
            <div className="stats-icon">
              <Megaphone size={24} />
            </div>
            <div className="stats-content">
              <h3>Campagnes</h3>
              <div className="stats-value">{adStats.totalCampaigns}</div>
              <div className="stats-label">Total</div>
            </div>
          </CardBody>
        </Card>

        <Card className="stats-card">
          <CardBody>
            <div className="stats-icon active">
              <Megaphone size={24} />
            </div>
            <div className="stats-content">
              <h3>Campagnes actives</h3>
              <div className="stats-value">{adStats.activeCampaigns}</div>
              <div className="stats-label">En cours</div>
            </div>
          </CardBody>
        </Card>

        <Card className="stats-card">
          <CardBody>
            <div className="stats-icon">
              <DollarSign size={24} />
            </div>
            <div className="stats-content">
              <h3>Budget total</h3>
              <div className="stats-value">{formatAmount(adStats.totalBudget)}</div>
              <div className="stats-label">Alloué</div>
            </div>
          </CardBody>
        </Card>

        <Card className="stats-card">
          <CardBody>
            <div className="stats-icon spent">
              <DollarSign size={24} />
            </div>
            <div className="stats-content">
              <h3>Dépenses</h3>
              <div className="stats-value">{formatAmount(adStats.totalSpent)}</div>
              <div className="stats-label">Utilisé</div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Filtres et recherche */}
      <div className="ads-filters">
        <div className="search-container">
          <Search size={16} />
          <input
            type="text"
            placeholder="Rechercher une campagne..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="filter-container">
          <Filter size={16} />
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <option value="all">Tous les statuts</option>
            <option value="active">Actives</option>
            <option value="paused">En pause</option>
            <option value="completed">Terminées</option>
            <option value="draft">Brouillons</option>
          </select>
          <ChevronDown size={16} />
        </div>

        <div className="sort-container">
          <ArrowDownUp size={16} />
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
          >
            <option value="date">Date</option>
            <option value="budget">Budget</option>
            <option value="performance">Performance</option>
          </select>
          <ChevronDown size={16} />
        </div>

        <div className="sort-order">
          <button
            className={sortOrder === 'asc' ? 'active' : ''}
            onClick={() => setSortOrder('asc')}
          >
            A-Z
          </button>
          <button
            className={sortOrder === 'desc' ? 'active' : ''}
            onClick={() => setSortOrder('desc')}
          >
            Z-A
          </button>
        </div>
      </div>

      {/* Liste des campagnes */}
      <div className="ads-list">
        {sortedCampaigns.length === 0 ? (
          <div className="no-ads">
            <p>Aucune campagne publicitaire ne correspond à vos critères.</p>
          </div>
        ) : (
          sortedCampaigns.map(campaign => (
            <Card key={campaign.id} className="ad-card">
              <CardBody>
                <div className="ad-image">
                  <img {...createAdImageProps(campaign.imageUrl, campaign.title, 'promo')} />
                </div>
                <div className="ad-content">
                  <div className="ad-header">
                    <h3>{campaign.title}</h3>
                    <div className={`ad-status ${getStatusClass(campaign.status)}`}>
                      <span className={getStatusClass(campaign.status)}>{getStatusLabel(campaign.status)}</span>
                    </div>
                  </div>
                  <p className="ad-description">{campaign.description}</p>
                  <div className="ad-details">
                    <div className="ad-detail">
                      <span className="label">Budget:</span>
                      <span className="date-value">{formatDate(typeof campaign.startDate === 'string' ? new Date(campaign.startDate) : campaign.startDate)}</span>
                    </div>
                    <div className="ad-date">
                      <span className="date-label">Fin:</span>
                      <span className="date-value">{formatDate(typeof campaign.endDate === 'string' ? new Date(campaign.endDate) : campaign.endDate)} - {formatDate(campaign.endDate)}</span>
                    </div>
                    <div className="ad-detail">
                      <span className="label">Impressions:</span>
                      <span className="value">{campaign.impressions.toLocaleString()}</span>
                    </div>
                    <div className="ad-detail">
                      <span className="label">Clics:</span>
                      <span className="value">{campaign.clicks.toLocaleString()}</span>
                    </div>
                    <div className="ad-detail">
                      <span className="label">CTR:</span>
                      <span className="value">{campaign.ctr.toFixed(2)}%</span>
                    </div>
                  </div>
                  <div className="ad-actions">
                    <Button className="action-btn view" onClick={() => setSelectedViewCampaign(campaign)}>
                      <Eye size={16} />
                      Voir
                    </Button>
                    <Button className="action-btn edit" onClick={() => handleEditCampaign(campaign)}>
                      <Edit size={16} />
                      Modifier
                    </Button>
                    <Button
                      variant="danger"
                      className="action-btn delete"
                      onClick={() => {}}>
                      <Trash2 size={16} />
                      Supprimer
                    </Button>
                    {campaign.status === 'active' ? (
                      <Button
                        variant="danger"
                        onClick={() => handleChangeStatus(campaign.id, 'completed' as 'active' | 'paused' | 'completed' | 'draft')}
                      >
                        Mettre en pause
                      </Button>
                    ) : campaign.status === 'paused' ? (
                      <Button
                        className="action-btn resume"
                        onClick={() => handleChangeStatus(campaign.id, 'active')}
                      >
                        Activer
                      </Button>
                    ) : null}
                  </div>
                </div>
              </CardBody>
            </Card>
          ))
        )}
      </div>

      {/* Formulaire modal */}
      {renderAdForm()}

      {/* Détails de la campagne */}
      {selectedViewCampaign && (
        <AdCampaignDetails
          campaign={selectedViewCampaign}
          allCampaigns={adCampaigns}
          onClose={() => setSelectedViewCampaign(null)}
          onEdit={handleEditCampaign}
          onDelete={(campaignId) => {
            if (window.confirm('Êtes-vous sûr de vouloir supprimer cette campagne ?')) {
              const updatedCampaigns = adCampaigns.filter(c => c.id !== campaignId);
              setAdCampaigns(updatedCampaigns);
              setSelectedViewCampaign(null);

              // Mettre à jour les statistiques
              const totalCampaigns = updatedCampaigns.length;
              const activeCampaigns = updatedCampaigns.filter(campaign => campaign.status === 'active').length;
              const totalBudget = updatedCampaigns.reduce((sum, campaign) => sum + campaign.budget, 0);
              const totalSpent = updatedCampaigns.reduce((sum, campaign) => sum + campaign.spent, 0);
              const totalImpressions = updatedCampaigns.reduce((sum, campaign) => sum + campaign.impressions, 0);
              const totalClicks = updatedCampaigns.reduce((sum, campaign) => sum + campaign.clicks, 0);
              const averageCTR = totalClicks > 0 && totalImpressions > 0
                ? (totalClicks / totalImpressions) * 100
                : 0;

              setAdStats({
                totalCampaigns,
                activeCampaigns,
                totalBudget,
                totalSpent,
                totalImpressions,
                totalClicks,
                averageCTR
              });
            }
          }}
          onChangeStatus={handleChangeStatus}
        />
      )}

      {/* Assistant IA */}
      <AIAssistant
        isOpen={showAIAssistant}
        onClose={() => setShowAIAssistant(false)}
        campaigns={adCampaigns}
        analytics={advancedAnalytics}
        onOptimize={optimizeCampaignBudget}
      />

      {/* Export Avancé */}
      <AdvancedExport
        isOpen={showAdvancedExport}
        onClose={() => setShowAdvancedExport(false)}
        data={{
          campaigns: adCampaigns,
          analytics: advancedAnalytics,
          chartData: chartData,
          timeRange: selectedTimeRange,
          generatedAt: new Date().toISOString()
        }}
        onExport={handleAdvancedExport}
      />

      {/* Gestionnaire de portefeuille */}
      <AdWalletManager
        isOpen={showWalletManager}
        onClose={() => setShowWalletManager(false)}
      />
    </div>
  );
};

// Wrapper avec le provider de facturation
const BusinessAdsPageWithBilling: React.FC = () => {
  return (
    <AdBillingProvider>
      <BusinessAdsPage />
    </AdBillingProvider>
  );
};

export default BusinessAdsPageWithBilling;
