-- Script pour appliquer toutes les migrations d'abonnement
-- Exécuter ce script dans l'éditeur SQL de Supabase

-- 1. <PERSON><PERSON><PERSON> ou modifier la table des plans d'abonnement
CREATE TABLE IF NOT EXISTS public.subscription_plans (
  id text PRIMARY KEY,
  name text NOT NULL,
  description text,
  price integer NOT NULL, -- Prix en centimes
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Ajouter les colonnes manquantes si elles n'existent pas
DO $$
BEGIN
  -- Ajouter la colonne duration si elle n'existe pas
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                 WHERE table_name = 'subscription_plans'
                 AND column_name = 'duration') THEN
    ALTER TABLE public.subscription_plans ADD COLUMN duration integer NOT NULL DEFAULT 30;
  END IF;

  -- Ajouter la colonne features si elle n'existe pas
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                 WHERE table_name = 'subscription_plans'
                 AND column_name = 'features') THEN
    ALTER TABLE public.subscription_plans ADD COLUMN features text[] DEFAULT '{}';
  END IF;
END $$;

-- Insérer les plans par défaut
INSERT INTO public.subscription_plans (id, name, description, price, duration, features, is_active) VALUES
  ('trial', 'Essai Gratuit', 'Essai gratuit de 7 jours', 0, 7, ARRAY['basic-ai', 'basic-dashboard', 'basic-reviews'], true),
  ('monthly', 'Plan Mensuel', 'Abonnement mensuel', 2500, 30, ARRAY['basic-ai', 'basic-dashboard', 'basic-reviews', 'pdf-export'], true),
  ('yearly', 'Plan Annuel', 'Abonnement annuel avec réduction', 25000, 365, ARRAY['advanced-ai', 'custom-dashboard', 'unlimited-reviews', 'api-access'], true)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  price = EXCLUDED.price,
  duration = EXCLUDED.duration,
  features = EXCLUDED.features,
  is_active = EXCLUDED.is_active,
  updated_at = now();

-- 2. Créer la table des abonnements business
CREATE TABLE IF NOT EXISTS public.business_subscriptions (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_id text NOT NULL,
  start_date timestamptz NOT NULL,
  end_date timestamptz NOT NULL,
  is_active boolean DEFAULT true,
  is_trial boolean DEFAULT false,
  auto_renew boolean DEFAULT false,
  payment_status text DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 3. Créer la table des codes d'abonnement
CREATE TABLE IF NOT EXISTS public.subscription_codes (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  code text NOT NULL UNIQUE,
  business_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  business_name text NOT NULL,
  plan_type text NOT NULL CHECK (plan_type IN ('trial', 'monthly', 'yearly')),
  amount integer NOT NULL, -- Montant en centimes
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'validated', 'rejected', 'expired')),
  generated_at timestamptz DEFAULT now(),
  expires_at timestamptz NOT NULL,
  validated_at timestamptz,
  validated_by uuid REFERENCES auth.users(id),
  rejection_reason text,
  admin_notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 4. Créer la table des notifications d'abonnement
CREATE TABLE IF NOT EXISTS public.subscription_notifications (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('activation', 'expiration_warning', 'expired', 'renewal')),
  title text NOT NULL,
  message text NOT NULL,
  plan_name text NOT NULL,
  days_remaining integer,
  is_read boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 5. Créer les index
CREATE INDEX IF NOT EXISTS idx_subscription_plans_is_active ON public.subscription_plans(is_active);
CREATE INDEX IF NOT EXISTS idx_business_subscriptions_business_id ON public.business_subscriptions(business_id);
CREATE INDEX IF NOT EXISTS idx_business_subscriptions_is_active ON public.business_subscriptions(is_active);
CREATE INDEX IF NOT EXISTS idx_subscription_codes_business_id ON public.subscription_codes(business_id);
CREATE INDEX IF NOT EXISTS idx_subscription_codes_code ON public.subscription_codes(code);
CREATE INDEX IF NOT EXISTS idx_subscription_codes_status ON public.subscription_codes(status);
CREATE INDEX IF NOT EXISTS idx_subscription_notifications_business_id ON public.subscription_notifications(business_id);

-- 6. Activer RLS sur toutes les tables
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_notifications ENABLE ROW LEVEL SECURITY;

-- 7. Créer les politiques RLS
-- Plans d'abonnement
CREATE POLICY "Authenticated users can view active plans" ON public.subscription_plans
  FOR SELECT USING (is_active = true);

-- Abonnements business
CREATE POLICY "Users can view their own subscriptions" ON public.business_subscriptions
  FOR SELECT USING (auth.uid() = business_id);

CREATE POLICY "System can manage subscriptions" ON public.business_subscriptions
  FOR ALL WITH CHECK (true);

-- Codes d'abonnement
CREATE POLICY "Users can view their own subscription codes" ON public.subscription_codes
  FOR SELECT USING (auth.uid() = business_id);

CREATE POLICY "System can manage subscription codes" ON public.subscription_codes
  FOR ALL WITH CHECK (true);

-- Notifications d'abonnement
CREATE POLICY "Users can view their own subscription notifications" ON public.subscription_notifications
  FOR SELECT USING (auth.uid() = business_id);

CREATE POLICY "Users can update their own subscription notifications" ON public.subscription_notifications
  FOR UPDATE USING (auth.uid() = business_id);

CREATE POLICY "System can insert subscription notifications" ON public.subscription_notifications
  FOR INSERT WITH CHECK (true);

-- 8. Créer la procédure stockée
CREATE OR REPLACE FUNCTION public.create_subscription_from_code(p_code_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_code_record record;
  v_start_date timestamptz;
  v_end_date timestamptz;
BEGIN
  -- Récupérer les détails du code d'abonnement
  SELECT * INTO v_code_record
  FROM public.subscription_codes
  WHERE id = p_code_id AND status = 'validated';

  IF NOT FOUND THEN
    RAISE NOTICE 'Code non trouvé ou non validé: %', p_code_id;
    RETURN FALSE;
  END IF;

  -- Désactiver tout abonnement actif existant
  UPDATE public.business_subscriptions
  SET is_active = FALSE, updated_at = now()
  WHERE business_id = v_code_record.business_id AND is_active = TRUE;

  -- Calculer les dates
  v_start_date := now();
  IF v_code_record.plan_type = 'monthly' THEN
    v_end_date := v_start_date + interval '1 month';
  ELSIF v_code_record.plan_type = 'yearly' THEN
    v_end_date := v_start_date + interval '1 year';
  ELSIF v_code_record.plan_type = 'trial' THEN
    v_end_date := v_start_date + interval '7 days';
  ELSE
    v_end_date := v_start_date + interval '1 month';
  END IF;

  -- Insérer le nouvel abonnement
  INSERT INTO public.business_subscriptions (
    business_id, plan_id, start_date, end_date, is_active, is_trial, auto_renew, payment_status
  ) VALUES (
    v_code_record.business_id, v_code_record.plan_type, v_start_date, v_end_date, 
    TRUE, v_code_record.plan_type = 'trial', FALSE, 'paid'
  );

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Erreur dans create_subscription_from_code: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- 9. Donner les permissions
GRANT SELECT ON public.subscription_plans TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.business_subscriptions TO authenticated;
GRANT SELECT, INSERT ON public.subscription_codes TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.subscription_notifications TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_subscription_from_code(uuid) TO authenticated;

-- Permissions pour service_role
GRANT ALL ON public.subscription_plans TO service_role;
GRANT ALL ON public.business_subscriptions TO service_role;
GRANT ALL ON public.subscription_codes TO service_role;
GRANT ALL ON public.subscription_notifications TO service_role;
GRANT EXECUTE ON FUNCTION public.create_subscription_from_code(uuid) TO service_role;

-- Message de confirmation
SELECT 'Toutes les tables et fonctions d''abonnement ont été créées avec succès!' as message;
