import React from 'react';
import { IPost } from '../../types';
import { getAverageRatingForPost, getCategoryRankForPost } from '../../lib/utils';
import { Star, Award, MessageSquare, ThumbsUp } from 'lucide-react'; // Award for medal placeholder

interface BusinessProductSummaryCardProps {
  post: IPost;
  allPosts: IPost[]; // Needed for category ranking
}

const BusinessProductSummaryCard: React.FC<BusinessProductSummaryCardProps> = ({ post, allPosts }) => {
  const { average: avgRating, count: reviewCount } = getAverageRatingForPost(post);
  const { rank, totalInCategory } = getCategoryRankForPost(post, allPosts);
  const recommendationCount = post.recommendations?.length || 0;

  const getMedalColor = (rank: number) => {
    if (rank === 1) return 'text-yellow-400'; // Gold
    if (rank === 2) return 'text-gray-400';   // Silver
    if (rank === 3) return 'text-orange-400'; // Bronze
    return 'text-transparent'; // No medal
  };

  return (
    <div className="bg-white shadow-md rounded-lg p-4 flex flex-col md:flex-row gap-4 hover:shadow-lg transition-shadow">
      <img 
        src={post.images[0] || 'https://via.placeholder.com/150?text=Produit'} 
        alt={post.productName}
        className="w-full md:w-32 h-32 object-cover rounded-md"
      />
      <div className="flex-1">
        <h3 className="text-lg font-semibold text-gray-800">{post.productName}</h3>
        {post.authorRole === 'business' && post.businessName && (
          <div className="text-xs text-blue-700 font-semibold mb-1">{post.businessName}</div>
        )}
        <p className="text-xs text-gray-500 mb-1">Catégorie: {post.category}</p>
        
        <div className="flex items-center my-1">
          {Array.from({ length: 5 }).map((_, i) => (
            <Star key={i} size={16} className={`mr-0.5 ${i < avgRating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`} />
          ))}
          <span className="ml-1 text-xs text-gray-600">({avgRating.toFixed(1)}/5 sur {reviewCount} avis)</span>
        </div>

        <div className="text-xs text-gray-600 space-y-0.5">
          <p className="flex items-center">
            <ThumbsUp size={14} className="mr-1 text-blue-500" /> 
            Recommandations: <span className="font-medium ml-1">{recommendationCount}</span>
          </p>
          {rank > 0 && (
            <p className="flex items-center">
              <Award size={14} className={`mr-1 ${getMedalColor(rank)} ${rank > 3 ? 'opacity-50' : ''}`} />
              Rang Catégorie: <span className="font-medium ml-1">#{rank}</span> sur {totalInCategory}
              {rank <=3 && <span className={`font-bold ml-1 ${getMedalColor(rank)}`}>({rank === 1 ? "Or" : rank === 2 ? "Argent" : "Bronze"})</span>}
            </p>
          )}
        </div>
      </div>
      {/* Optionally, add a button to view the full post/product */}
      {/* <Button size="sm" variant="outline" className="mt-auto md:mt-0 md:ml-auto">Voir Détails</Button> */}
    </div>
  );
};

export default BusinessProductSummaryCard;
