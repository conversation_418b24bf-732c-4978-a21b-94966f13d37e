import React from 'react';
import ProfilePage from '../components/ProfilePage';
import BusinessProfilePage from '../pages/BusinessProfilePage';
import { useParams } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { UserRole } from '../types';

const ProfileRoute: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const { currentUser } = useAuth();

  // Si aucun userId n'est fourni dans l'URL, afficher le profil de l'utilisateur connecté
  if (!userId && !currentUser) {
    return <div>Utilisateur non trouvé</div>;
  }

  // Si l'utilisateur connecté est une entreprise et qu'il consulte son propre profil
  if (!userId && currentUser?.role === UserRole.BUSINESS) {
    return <BusinessProfilePage />;
  }

  // Pour tous les autres cas (utilisateurs normaux ou consultation d'autres profils)
  return <ProfilePage userId={userId || currentUser?.id} />;
};

export default ProfileRoute;