import React, { useState } from 'react';
import { Heart, MessageCircle, Share2 } from 'lucide-react';

const AdButtonTest: React.FC = () => {
  const [clicked, setClicked] = useState<string | null>(null);

  const handleClick = (buttonType: string) => {
    console.log(`🖱️ Test: Clic sur ${buttonType}`);
    setClicked(buttonType);
    setTimeout(() => setClicked(null), 1000);
  };

  return (
    <div className="p-4 bg-yellow-50 border-2 border-yellow-300 rounded-lg mb-4">
      <h3 className="text-lg font-bold text-yellow-800 mb-3">🧪 Test des Boutons Publicité</h3>
      <div className="flex gap-4">
        <button
          onClick={() => handleClick('like')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
            clicked === 'like' 
              ? 'bg-green-200 text-green-800' 
              : 'bg-white border border-gray-300 hover:bg-gray-50'
          }`}
        >
          <Heart size={18} />
          <span>Test Like</span>
        </button>
        
        <button
          onClick={() => handleClick('comment')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
            clicked === 'comment' 
              ? 'bg-green-200 text-green-800' 
              : 'bg-white border border-gray-300 hover:bg-gray-50'
          }`}
        >
          <MessageCircle size={18} />
          <span>Test Comment</span>
        </button>
        
        <button
          onClick={() => handleClick('share')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
            clicked === 'share' 
              ? 'bg-green-200 text-green-800' 
              : 'bg-white border border-gray-300 hover:bg-gray-50'
          }`}
        >
          <Share2 size={18} />
          <span>Test Share</span>
        </button>
      </div>
      
      {clicked && (
        <div className="mt-3 p-2 bg-green-100 border border-green-300 rounded text-green-800">
          ✅ Bouton "{clicked}" cliqué avec succès !
        </div>
      )}
      
      <div className="mt-3 text-sm text-yellow-700">
        <p>💡 Ce composant teste si les clics fonctionnent correctement.</p>
        <p>Si ces boutons fonctionnent mais pas ceux des publicités, le problème vient d'ailleurs.</p>
      </div>
    </div>
  );
};

export default AdButtonTest;
