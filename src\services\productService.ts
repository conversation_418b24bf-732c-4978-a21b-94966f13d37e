import { supabase } from '../lib/supabase'; 
import { IProduct } from '../types/index'; 
import { toast } from 'react-toastify'; 

// Ajouter un nouveau produit 
export const addProduct = async (product: Partial<IProduct>, businessId: string): Promise<IProduct | null> => {
  // Map camelCase to snake_case for DB
  const productWithBusinessId = {
    ...product,
    business_id: businessId,
    average_rating: product.averageRating,
    quality_badge: product.qualityBadge,
  };
  delete productWithBusinessId.averageRating;
  delete productWithBusinessId.qualityBadge;
  delete productWithBusinessId.businessId;
  const { data, error } = await supabase
    .from('products')
    .insert([productWithBusinessId]) // Supabase insert expects an array
    .select()
    .single();
  if (error) {
    console.error('Error adding product:', error);
    return null;
  }
  return data as IProduct;
};

// Mettre à jour un produit existant
export const updateProduct = async (productId: string, updates: Partial<IProduct>): Promise<IProduct | null> => {
  // Map camelCase to snake_case for DB
  const updatesForDb = {
    ...updates,
    average_rating: updates.averageRating,
    quality_badge: updates.qualityBadge,
  };
  delete updatesForDb.averageRating;
  delete updatesForDb.qualityBadge;
  delete updatesForDb.businessId;
  const { data, error } = await supabase
    .from('products')
    .update(updatesForDb)
    .eq('id', productId)
    .select()
    .single();
  if (error) {
    console.error('Error updating product:', error);
    return null;
  }
  return data as IProduct;
};

// Récupérer tous les produits d'une entreprise spécifique
export const getProductsByBusinessId = async (businessId: string): Promise<IProduct[]> => {
  try {
    // Assurez-vous que businessId est une chaîne non vide
    if (!businessId) {
      console.error('Erreur: businessId est manquant pour getProductsByBusinessId');
      return [];
    }
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('business_id', businessId);
      
    if (error) {
      console.error('Erreur Supabase lors de la récupération des produits:', error);
      throw error;
    }
    
    if (!data) return [];
    return (data as any[]).map(d => ({
      ...d,
      businessId: d.business_id,
      averageRating: d.average_rating,
      qualityBadge: d.quality_badge,
      createdAt: d.created_at,
      images: Array.isArray(d.images) ? d.images : [], // <-- Correction ici
    }));
  } catch (error) {
    console.error('Erreur lors de la récupération des produits par businessId:', error);
    // Afficher une notification d'erreur si l'erreur est une instance d'Error
    if (error instanceof Error) {
      toast.error(`Erreur lors de la récupération des produits: ${error.message}`);
    } else {
      toast.error('Erreur lors de la récupération des produits.');
    }
    return [];
  }
};

// Supprimer un produit
export const deleteProduct = async (productId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', productId);
      
    if (error) throw error;
    
    return true;
  } catch (error) {
    console.error('Erreur lors de la suppression du produit:', error);
    return false;
  }
};

// Publier un produit sur la marketplace
export const publishToMarketplace = async (productId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('products')
      .update({ isOnMarketplace: true })
      .eq('id', productId);
      
    if (error) throw error;
    
    return true;
  } catch (error) {
    console.error('Erreur lors de la publication sur la marketplace:', error);
    return false;
  }
};

// Récupérer tous les produits de la marketplace
export const getMarketplaceProducts = async (): Promise<IProduct[]> => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('isOnMarketplace', true);
      
    if (error) throw error;
    
    return data as IProduct[];
  } catch (error) {
    console.error('Erreur lors de la récupération des produits de la marketplace:', error);
    return [];
  }
};