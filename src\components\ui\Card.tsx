import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hoverable?: boolean;
  bordered?: boolean;
  shadow?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  className = '',
  hoverable = false,
  bordered = true,
  shadow = true,
}) => {
  const baseStyles = 'rounded-lg overflow-hidden';
  const hoverStyles = hoverable ? 'transition-all duration-200 hover:translate-y-[-4px]' : '';
  const borderStyles = bordered ? 'border border-gray-200' : '';
  const shadowStyles = shadow ? 'shadow-md' : '';

  return (
    <div
      className={`
        ${baseStyles}
        ${hoverStyles}
        ${borderStyles}
        ${shadowStyles}
        ${className}
      `}
    >
      {children}
    </div>
  );
};

export const CardHeader: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  return (
    <div className={`px-4 py-3 border-b border-gray-200 ${className}`}>
      {children}
    </div>
  );
};

export const CardBody: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  return <div className={`p-4 ${className}`}>{children}</div>;
};

export const CardFooter: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  return (
    <div className={`px-4 py-3 bg-gray-50 border-t border-gray-200 ${className}`}>
      {children}
    </div>
  );
};

export default Card;