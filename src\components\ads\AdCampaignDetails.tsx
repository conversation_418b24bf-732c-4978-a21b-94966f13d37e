import React, { useEffect } from 'react';
import { AdCampaign } from '../../types';
import { formatAmount } from '../../utils/formatUtils';
import { createAdImageProps } from '../../utils/imageUtils';
import { X, Calendar, DollarSign, Users, Target, Globe, Clock, Edit, Trash2, PauseCircle, PlayCircle } from 'lucide-react';
import Button from '../ui/Button';
import AdPerformanceCharts from './AdPerformanceCharts';
import '../../styles/AdCampaignDetails.css';

interface AdCampaignDetailsProps {
  campaign: AdCampaign;
  allCampaigns: AdCampaign[];
  onClose: () => void;
  onEdit: (campaign: AdCampaign) => void;
  onDelete: (campaignId: string) => void;
  onChangeStatus: (campaignId: string, status: string) => void;
}

const AdCampaignDetails: React.FC<AdCampaignDetailsProps> = ({
  campaign,
  allCampaigns,
  onClose,
  onEdit,
  onDelete,
  onChangeStatus
}) => {
  // Fonction pour formater la date pour l'affichage
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };



  // Calculer le pourcentage du budget utilisé
  const budgetPercentage = (campaign.spent / campaign.budget) * 100;

  // Calculer le temps restant de la campagne
  const calculateTimeRemaining = () => {
    const today = new Date();
    const endDate = new Date(campaign.endDate);

    if (today > endDate) {
      return 'Campagne terminée';
    }

    const diffTime = Math.abs(endDate.getTime() - today.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return `${diffDays} jour${diffDays > 1 ? 's' : ''} restant${diffDays > 1 ? 's' : ''}`;
  };

  // Obtenir la classe CSS pour le statut
  const getStatusClass = (status: string) => {
    switch (status) {
      case 'active':
        return 'status-active';
      case 'paused':
        return 'status-paused';
      case 'completed':
        return 'status-completed';
      case 'draft':
        return 'status-draft';
      default:
        return '';
    }
  };

  // Obtenir le libellé pour le statut
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'paused':
        return 'En pause';
      case 'completed':
        return 'Terminée';
      case 'draft':
        return 'Brouillon';
      default:
        return status;
    }
  };

  // Gérer la fermeture avec la touche Escape
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [onClose]);

  const handleOverlayClick = (e: React.MouseEvent) => {
    // Fermer la modal si on clique sur l'overlay (pas sur le contenu)
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="ad-campaign-details-overlay" onClick={handleOverlayClick}>
      <div className="ad-campaign-details">
        <div className="details-header">
          <h2>{campaign.title}</h2>
          <button
            className="close-btn"
            onClick={onClose}
            title="Fermer (Échap)"
            aria-label="Fermer la fenêtre"
          >
            <X size={20} />
          </button>
        </div>

        <div className="details-content">
          <div className="details-main">
            <div className="campaign-image">
              <img {...createAdImageProps(campaign.imageUrl, campaign.title, 'promo')} />
              <div className={`campaign-status ${getStatusClass(campaign.status)}`}>
                {getStatusLabel(campaign.status)}
              </div>
            </div>

            <div className="campaign-info">
              <p className="campaign-description">{campaign.description}</p>

              <div className="info-grid">
                <div className="info-item">
                  <Calendar size={16} />
                  <span className="info-label">Période:</span>
                  <span className="info-value">{formatDate(campaign.startDate)} - {formatDate(campaign.endDate)}</span>
                </div>

                <div className="info-item">
                  <Clock size={16} />
                  <span className="info-label">Temps restant:</span>
                  <span className="info-value">{calculateTimeRemaining()}</span>
                </div>

                <div className="info-item">
                  <DollarSign size={16} />
                  <span className="info-label">Budget total:</span>
                  <span className="info-value">{formatAmount(campaign.budget)}</span>
                </div>

                <div className="info-item">
                  <DollarSign size={16} />
                  <span className="info-label">Budget quotidien:</span>
                  <span className="info-value">{formatAmount(campaign.dailyBudget)}</span>
                </div>

                <div className="info-item">
                  <DollarSign size={16} />
                  <span className="info-label">Dépensé:</span>
                  <span className="info-value">{formatAmount(campaign.spent)}</span>
                </div>

                <div className="info-item">
                  <DollarSign size={16} />
                  <span className="info-label">Restant:</span>
                  <span className="info-value">{formatAmount(campaign.budget - campaign.spent)}</span>
                </div>
              </div>

              <div className="budget-progress">
                <div className="progress-label">
                  <span>Utilisation du budget</span>
                  <span>{budgetPercentage.toFixed(1)}%</span>
                </div>
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{ width: `${Math.min(budgetPercentage, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <div className="details-targeting">
            <h3>Ciblage</h3>

            <div className="targeting-grid">
              <div className="targeting-item">
                <Target size={16} />
                <span className="targeting-label">Public cible:</span>
                <div className="targeting-tags">
                  {campaign.targetAudience.map((audience, index) => (
                    <span key={index} className="targeting-tag">{audience}</span>
                  ))}
                </div>
              </div>

              <div className="targeting-item">
                <Globe size={16} />
                <span className="targeting-label">Localisation:</span>
                <div className="targeting-tags">
                  {campaign.targeting.location.countries.map((country, index) => (
                    <span key={index} className="targeting-tag">{country}</span>
                  ))}
                  {campaign.targeting.location.cities.map((city, index) => (
                    <span key={index} className="targeting-tag">{city}</span>
                  ))}
                </div>
              </div>

              <div className="targeting-item">
                <Users size={16} />
                <span className="targeting-label">Démographie:</span>
                <div className="targeting-tags">
                  {campaign.targeting.demographics.ageRanges.map((age, index) => (
                    <span key={index} className="targeting-tag">{age}</span>
                  ))}
                  {campaign.targeting.demographics.genders.map((gender, index) => (
                    <span key={index} className="targeting-tag">{gender}</span>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="details-placement">
            <h3>Emplacements</h3>
            <div className="placement-grid">
              <div className={`placement-item ${campaign.showInNewsFeed ? 'active' : ''}`}>
                Fil d'actualité
              </div>
              <div className={`placement-item ${campaign.showInRightSidebar ? 'active' : ''}`}>
                Barre latérale
              </div>
              <div className={`placement-item ${campaign.showInOffersAndPromotions ? 'active' : ''}`}>
                Offres et promotions
              </div>
            </div>
          </div>

          <AdPerformanceCharts campaign={campaign} allCampaigns={allCampaigns} />

          <div className="details-actions">
            <Button
              className="action-btn edit"
              onClick={() => onEdit(campaign)}
            >
              <Edit size={16} />
              Modifier
            </Button>

            <Button
              className="action-btn delete"
              onClick={() => {
                if (window.confirm('Êtes-vous sûr de vouloir supprimer cette campagne ?')) {
                  onDelete(campaign.id);
                }
              }}
            >
              <Trash2 size={16} />
              Supprimer
            </Button>

            {campaign.status === 'active' ? (
              <Button
                className="action-btn pause"
                onClick={() => onChangeStatus(campaign.id, 'paused')}
              >
                <PauseCircle size={16} />
                Mettre en pause
              </Button>
            ) : campaign.status === 'paused' ? (
              <Button
                className="action-btn resume"
                onClick={() => onChangeStatus(campaign.id, 'active')}
              >
                <PlayCircle size={16} />
                Activer
              </Button>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdCampaignDetails;
