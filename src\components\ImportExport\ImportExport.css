/* Import/Export Modals - Styles Ultra-Avancés */

/* Modal Base */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.modal-container.large {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-title h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.import-steps {
  display: flex;
  gap: 8px;
  margin-left: 16px;
}

.import-steps span {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  background: #f3f4f6;
  color: #6b7280;
  transition: all 0.2s ease;
}

.import-steps span.active {
  background: #3b82f6;
  color: white;
}

.import-steps span.completed {
  background: #10b981;
  color: white;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.modal-close:hover {
  background: #e5e7eb;
  color: #374151;
  transform: scale(1.05);
}

/* Modal Content */
.modal-content {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

/* Upload Step */
.upload-step {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.upload-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.upload-info p {
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.template-download {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 12px;
}

.btn-template {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-template:hover {
  background: #059669;
  transform: translateY(-1px);
}

.file-upload-area {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upload-zone {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fafafa;
}

.upload-zone:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.upload-zone h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 12px 0 8px 0;
}

.upload-zone p {
  color: #6b7280;
  margin: 0 0 8px 0;
}

.upload-zone small {
  color: #9ca3af;
  font-size: 0.75rem;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #dbeafe;
  border: 1px solid #93c5fd;
  border-radius: 8px;
  color: #1e40af;
}

.file-size {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Import Options */
.import-options h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.option-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.option-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

/* Mapping Step */
.mapping-step {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.mapping-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.mapping-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.csv-preview h4,
.field-mapping h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.csv-table {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  max-height: 300px;
  overflow-y: auto;
}

.csv-table table {
  width: 100%;
  border-collapse: collapse;
}

.csv-table th,
.csv-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #f3f4f6;
  font-size: 0.875rem;
}

.csv-table th {
  background: #f8fafc;
  font-weight: 600;
  color: #374151;
}

.csv-table td {
  color: #6b7280;
}

.mapping-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.mapping-item {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 12px;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.csv-column {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.csv-column strong {
  font-size: 0.875rem;
  color: #374151;
}

.csv-column small {
  font-size: 0.75rem;
  color: #9ca3af;
}

.arrow {
  color: #6b7280;
  font-weight: bold;
}

.product-field select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
}

.mapping-actions {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

/* Preview Step */
.preview-step {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.preview-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.preview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid;
}

.stat-card.success {
  background: #f0fdf4;
  border-color: #bbf7d0;
  color: #166534;
}

.stat-card.error {
  background: #fef2f2;
  border-color: #fecaca;
  color: #991b1b;
}

.stat-card.warning {
  background: #fffbeb;
  border-color: #fed7aa;
  color: #92400e;
}

.stat-card div {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-card strong {
  font-size: 1.25rem;
  font-weight: 700;
}

.stat-card span {
  font-size: 0.875rem;
  opacity: 0.8;
}

.errors-section,
.warnings-section {
  margin-top: 16px;
}

.errors-section h4,
.warnings-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.errors-list,
.warnings-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.error-item,
.warning-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
}

.error-item {
  background: #fef2f2;
  color: #991b1b;
}

.warning-item {
  background: #fffbeb;
  color: #92400e;
}

.more-errors {
  padding: 8px 12px;
  text-align: center;
  color: #6b7280;
  font-size: 0.875rem;
  font-style: italic;
}

.preview-actions {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

/* Processing Step */
.processing-step,
.complete-step {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.processing-animation,
.complete-animation {
  text-align: center;
}

.processing-animation h3,
.complete-animation h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 16px 0 8px 0;
}

.processing-animation p,
.complete-animation p {
  color: #6b7280;
  margin: 0;
}

.spinning {
  animation: spin 1s linear infinite;
  color: #3b82f6;
}

.success {
  color: #10b981;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Export Modal Specific */
.export-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f3f4f6;
}

.export-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.export-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.format-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.format-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.format-card:hover {
  border-color: #3b82f6;
  background: #f8fafc;
  color: #3b82f6;
  transform: translateY(-2px);
}

.format-card.active {
  border-color: #3b82f6;
  background: #dbeafe;
  color: #1e40af;
}

.format-card span {
  font-weight: 600;
}

.format-card small {
  font-size: 0.75rem;
  opacity: 0.7;
}

.scope-options h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.scope-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.scope-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.scope-option:hover {
  background: #f1f5f9;
}

.scope-option input[type="radio"] {
  accent-color: #3b82f6;
}

/* Fields Selection */
.fields-selection {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.field-category {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.category-header {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.category-toggle {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-toggle:hover {
  background: #f1f5f9;
}

.category-toggle .all-selected {
  color: #10b981;
}

.category-toggle .partial-selected {
  color: #f59e0b;
}

.category-toggle span {
  font-weight: 600;
  color: #374151;
}

.category-toggle small {
  color: #6b7280;
  margin-left: auto;
}

.category-fields {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  padding: 16px;
}

.field-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.field-option input[type="checkbox"] {
  accent-color: #3b82f6;
}

/* Filters */
.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.checkbox-item input[type="checkbox"] {
  accent-color: #3b82f6;
}

.range-inputs,
.date-inputs {
  display: flex;
  gap: 8px;
}

.range-inputs input,
.date-inputs input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

/* Additional Options */
.additional-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Export Summary */
.export-summary {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
}

.summary-stats {
  display: flex;
  justify-content: center;
  gap: 24px;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #374151;
  font-weight: 500;
}

/* Modal Actions */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
  margin-top: 24px;
}

.btn-primary,
.btn-secondary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 0.875rem;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-container.large {
    max-height: 95vh;
  }
  
  .modal-header {
    padding: 16px;
  }
  
  .modal-content {
    padding: 16px;
  }
  
  .mapping-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .format-grid {
    grid-template-columns: 1fr;
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .summary-stats {
    flex-direction: column;
    gap: 12px;
  }
  
  .modal-actions {
    flex-direction: column-reverse;
  }
  
  .btn-primary,
  .btn-secondary {
    width: 100%;
    justify-content: center;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .modal-container.large {
    background: #1f2937;
  }
  
  .modal-header {
    background: #374151;
    border-color: #4b5563;
  }
  
  .modal-title h2 {
    color: #f9fafb;
  }
  
  .upload-info h3,
  .mapping-info h3,
  .preview-info h3,
  .export-section h3 {
    color: #f9fafb;
  }
  
  .upload-info p,
  .mapping-info p,
  .preview-info p {
    color: #d1d5db;
  }
}

/* Accessibility */
.modal-container:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.btn-primary:focus,
.btn-secondary:focus,
.modal-close:focus,
.format-card:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .modal-overlay,
  .modal-container,
  .format-card,
  .btn-primary,
  .btn-secondary,
  .spinning {
    animation: none;
    transition: none;
  }
  
  .format-card:hover,
  .btn-primary:hover,
  .modal-close:hover {
    transform: none;
  }
}
