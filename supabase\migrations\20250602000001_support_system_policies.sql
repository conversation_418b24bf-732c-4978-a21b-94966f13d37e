-- Politiques RLS et triggers pour le système de support

-- =====================================================
-- ACTIVATION DE RLS SUR TOUTES LES TABLES
-- =====================================================
ALTER TABLE chat_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_replies ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE faq_ratings ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- POLITIQUES POUR CHAT_CONVERSATIONS
-- =====================================================
CREATE POLICY "Users can view their own conversations"
  ON chat_conversations FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id OR auth.uid() = agent_id);

CREATE POLICY "Users can create conversations"
  ON chat_conversations FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users and agents can update conversations"
  ON chat_conversations FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id OR auth.uid() = agent_id)
  WITH CHECK (auth.uid() = user_id OR auth.uid() = agent_id);

-- =====================================================
-- POLITIQUES POUR CHAT_MESSAGES
-- =====================================================
CREATE POLICY "Users can view messages in their conversations"
  ON chat_messages FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM chat_conversations 
      WHERE id = conversation_id 
      AND (user_id = auth.uid() OR agent_id = auth.uid())
    )
  );

CREATE POLICY "Users can send messages in their conversations"
  ON chat_messages FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() = sender_id AND
    EXISTS (
      SELECT 1 FROM chat_conversations 
      WHERE id = conversation_id 
      AND (user_id = auth.uid() OR agent_id = auth.uid())
    )
  );

CREATE POLICY "Users can update their own messages"
  ON chat_messages FOR UPDATE
  TO authenticated
  USING (auth.uid() = sender_id)
  WITH CHECK (auth.uid() = sender_id);

-- =====================================================
-- POLITIQUES POUR SUPPORT_TICKETS
-- =====================================================
CREATE POLICY "Users can view their own tickets"
  ON support_tickets FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id OR auth.uid() = assigned_agent_id);

CREATE POLICY "Users can create tickets"
  ON support_tickets FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users and agents can update tickets"
  ON support_tickets FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id OR auth.uid() = assigned_agent_id)
  WITH CHECK (auth.uid() = user_id OR auth.uid() = assigned_agent_id);

-- =====================================================
-- POLITIQUES POUR TICKET_MESSAGES
-- =====================================================
CREATE POLICY "Users can view messages in their tickets"
  ON ticket_messages FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM support_tickets 
      WHERE id = ticket_id 
      AND (user_id = auth.uid() OR assigned_agent_id = auth.uid())
    )
  );

CREATE POLICY "Users can send messages in their tickets"
  ON ticket_messages FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() = sender_id AND
    EXISTS (
      SELECT 1 FROM support_tickets 
      WHERE id = ticket_id 
      AND (user_id = auth.uid() OR assigned_agent_id = auth.uid())
    )
  );

-- =====================================================
-- POLITIQUES POUR COMMUNITY_POSTS
-- =====================================================
CREATE POLICY "Anyone can view approved community posts"
  ON community_posts FOR SELECT
  TO authenticated
  USING (is_approved = true);

CREATE POLICY "Users can create community posts"
  ON community_posts FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update their own posts"
  ON community_posts FOR UPDATE
  TO authenticated
  USING (auth.uid() = author_id)
  WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own posts"
  ON community_posts FOR DELETE
  TO authenticated
  USING (auth.uid() = author_id);

-- =====================================================
-- POLITIQUES POUR COMMUNITY_REPLIES
-- =====================================================
CREATE POLICY "Anyone can view approved replies"
  ON community_replies FOR SELECT
  TO authenticated
  USING (is_approved = true);

CREATE POLICY "Users can create replies"
  ON community_replies FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update their own replies"
  ON community_replies FOR UPDATE
  TO authenticated
  USING (auth.uid() = author_id)
  WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own replies"
  ON community_replies FOR DELETE
  TO authenticated
  USING (auth.uid() = author_id);

-- =====================================================
-- POLITIQUES POUR COMMUNITY_VOTES
-- =====================================================
CREATE POLICY "Users can view all votes"
  ON community_votes FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can create votes"
  ON community_votes FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own votes"
  ON community_votes FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own votes"
  ON community_votes FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- =====================================================
-- POLITIQUES POUR FAQ_RATINGS
-- =====================================================
CREATE POLICY "Users can view all FAQ ratings"
  ON faq_ratings FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can create FAQ ratings"
  ON faq_ratings FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own FAQ ratings"
  ON faq_ratings FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- =====================================================
-- TRIGGERS POUR MISE À JOUR AUTOMATIQUE
-- =====================================================

-- Fonction pour mettre à jour updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour updated_at
CREATE TRIGGER update_chat_conversations_updated_at
    BEFORE UPDATE ON chat_conversations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_support_tickets_updated_at
    BEFORE UPDATE ON support_tickets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ticket_messages_updated_at
    BEFORE UPDATE ON ticket_messages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_community_posts_updated_at
    BEFORE UPDATE ON community_posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_community_replies_updated_at
    BEFORE UPDATE ON community_replies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- TRIGGERS POUR COMPTEURS AUTOMATIQUES
-- =====================================================

-- Fonction pour mettre à jour le compteur de réponses
CREATE OR REPLACE FUNCTION update_replies_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE community_posts 
        SET replies_count = replies_count + 1 
        WHERE id = NEW.post_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE community_posts 
        SET replies_count = replies_count - 1 
        WHERE id = OLD.post_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- Trigger pour le compteur de réponses
CREATE TRIGGER update_community_posts_replies_count
    AFTER INSERT OR DELETE ON community_replies
    FOR EACH ROW EXECUTE FUNCTION update_replies_count();

-- Fonction pour mettre à jour les compteurs de votes
CREATE OR REPLACE FUNCTION update_votes_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.post_id IS NOT NULL THEN
            IF NEW.vote_type = 'up' THEN
                UPDATE community_posts SET votes_up = votes_up + 1 WHERE id = NEW.post_id;
            ELSE
                UPDATE community_posts SET votes_down = votes_down + 1 WHERE id = NEW.post_id;
            END IF;
        ELSIF NEW.reply_id IS NOT NULL THEN
            IF NEW.vote_type = 'up' THEN
                UPDATE community_replies SET votes_up = votes_up + 1 WHERE id = NEW.reply_id;
            ELSE
                UPDATE community_replies SET votes_down = votes_down + 1 WHERE id = NEW.reply_id;
            END IF;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        IF OLD.post_id IS NOT NULL THEN
            IF OLD.vote_type = 'up' THEN
                UPDATE community_posts SET votes_up = votes_up - 1 WHERE id = OLD.post_id;
            ELSE
                UPDATE community_posts SET votes_down = votes_down - 1 WHERE id = OLD.post_id;
            END IF;
        ELSIF OLD.reply_id IS NOT NULL THEN
            IF OLD.vote_type = 'up' THEN
                UPDATE community_replies SET votes_up = votes_up - 1 WHERE id = OLD.reply_id;
            ELSE
                UPDATE community_replies SET votes_down = votes_down - 1 WHERE id = OLD.reply_id;
            END IF;
        END IF;
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        -- Gérer le changement de vote
        IF OLD.post_id IS NOT NULL THEN
            -- Retirer l'ancien vote
            IF OLD.vote_type = 'up' THEN
                UPDATE community_posts SET votes_up = votes_up - 1 WHERE id = OLD.post_id;
            ELSE
                UPDATE community_posts SET votes_down = votes_down - 1 WHERE id = OLD.post_id;
            END IF;
            -- Ajouter le nouveau vote
            IF NEW.vote_type = 'up' THEN
                UPDATE community_posts SET votes_up = votes_up + 1 WHERE id = NEW.post_id;
            ELSE
                UPDATE community_posts SET votes_down = votes_down + 1 WHERE id = NEW.post_id;
            END IF;
        ELSIF OLD.reply_id IS NOT NULL THEN
            -- Retirer l'ancien vote
            IF OLD.vote_type = 'up' THEN
                UPDATE community_replies SET votes_up = votes_up - 1 WHERE id = OLD.reply_id;
            ELSE
                UPDATE community_replies SET votes_down = votes_down - 1 WHERE id = OLD.reply_id;
            END IF;
            -- Ajouter le nouveau vote
            IF NEW.vote_type = 'up' THEN
                UPDATE community_replies SET votes_up = votes_up + 1 WHERE id = NEW.reply_id;
            ELSE
                UPDATE community_replies SET votes_down = votes_down + 1 WHERE id = NEW.reply_id;
            END IF;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- Trigger pour les compteurs de votes
CREATE TRIGGER update_community_votes_count
    AFTER INSERT OR UPDATE OR DELETE ON community_votes
    FOR EACH ROW EXECUTE FUNCTION update_votes_count();
