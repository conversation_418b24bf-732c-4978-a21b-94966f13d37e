import { supabase } from '../lib/supabase';
import { Database } from '../lib/database.types';

// Types pour les commandes réelles
export interface RealOrder {
  id: string;
  orderNumber: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  items: RealOrderItem[];
  subtotal: number;
  shippingCost: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: string;
  shippingAddress: RealShippingAddress;
  trackingNumber?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  estimatedDelivery?: Date;
  priority: 'low' | 'normal' | 'high' | 'urgent';
}

export interface RealOrderItem {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  sku?: string;
}

export interface RealShippingAddress {
  name: string;
  phone: string;
  email: string;
  street: string;
  city: string;
  postalCode: string;
  country: string;
  instructions?: string;
}

type DatabaseOrder = Database['public']['Tables']['orders']['Row'];
type DatabaseProduct = Database['public']['Tables']['products']['Row'];
type DatabaseProfile = Database['public']['Tables']['profiles']['Row'];

export class OrdersService {
  /**
   * Récupère toutes les commandes d'une entreprise
   */
  static async getBusinessOrders(businessId: string): Promise<RealOrder[]> {
    try {
      const { data: orders, error } = await supabase
        .from('orders')
        .select(`
          *,
          product:products(*),
          buyer:profiles!orders_buyer_id_fkey(*),
          seller:profiles!orders_seller_id_fkey(*)
        `)
        .eq('seller_id', businessId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erreur lors de la récupération des commandes:', error);
        return [];
      }

      return orders?.map(order => this.transformDatabaseOrderToRealOrder(order)) || [];
    } catch (error) {
      console.error('Erreur dans getBusinessOrders:', error);
      return [];
    }
  }

  /**
   * Récupère les commandes par statut
   */
  static async getOrdersByStatus(businessId: string, status: string): Promise<RealOrder[]> {
    try {
      const { data: orders, error } = await supabase
        .from('orders')
        .select(`
          *,
          product:products(*),
          buyer:profiles!orders_buyer_id_fkey(*),
          seller:profiles!orders_seller_id_fkey(*)
        `)
        .eq('seller_id', businessId)
        .eq('status', status)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erreur lors de la récupération des commandes par statut:', error);
        return [];
      }

      return orders?.map(order => this.transformDatabaseOrderToRealOrder(order)) || [];
    } catch (error) {
      console.error('Erreur dans getOrdersByStatus:', error);
      return [];
    }
  }

  /**
   * Récupère les commandes par période
   */
  static async getOrdersByPeriod(businessId: string, days: number): Promise<RealOrder[]> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data: orders, error } = await supabase
        .from('orders')
        .select(`
          *,
          product:products(*),
          buyer:profiles!orders_buyer_id_fkey(*),
          seller:profiles!orders_seller_id_fkey(*)
        `)
        .eq('seller_id', businessId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erreur lors de la récupération des commandes par période:', error);
        return [];
      }

      return orders?.map(order => this.transformDatabaseOrderToRealOrder(order)) || [];
    } catch (error) {
      console.error('Erreur dans getOrdersByPeriod:', error);
      return [];
    }
  }

  /**
   * Met à jour le statut d'une commande
   */
  static async updateOrderStatus(orderId: string, newStatus: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('orders')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (error) {
        console.error('Erreur lors de la mise à jour du statut:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erreur dans updateOrderStatus:', error);
      return false;
    }
  }

  /**
   * Ajoute un numéro de suivi à une commande
   */
  static async addTrackingNumber(orderId: string, trackingNumber: string): Promise<boolean> {
    try {
      // Mise à jour de la commande avec le numéro de suivi
      const { error } = await supabase
        .from('orders')
        .update({ 
          status: 'shipped', // Automatiquement marquer comme expédiée
          tracking_number: trackingNumber, // Utilisation de la nouvelle colonne
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (error) {
        console.error('Erreur lors de l\'ajout du numéro de suivi:', error);
        return false;
      }

      // Synchroniser avec le système de suivi de commande
      try {
        const { data: order } = await supabase
          .from('orders')
          .select('buyer_id, seller_id')
          .eq('id', orderId)
          .single();

        if (order) {
          // Vérifier si un suivi existe déjà pour cette commande
          const { data: existingTracking } = await supabase
            .from('order_tracking')
            .select('id')
            .eq('order_id', orderId)
            .single();

          if (!existingTracking) {
            // Si aucun suivi n'existe, l'initialiser
            const { OrderTrackingService } = await import('./orderTrackingService');
            await OrderTrackingService.initializeTracking(orderId, order.buyer_id, order.seller_id);
          }
        }
      } catch (syncError) {
        console.warn('Avertissement: Synchronisation avec le système de suivi échouée:', syncError);
        // Ne pas échouer l'opération principale si la synchronisation échoue
      }

      return true;
    } catch (error) {
      console.error('Erreur dans addTrackingNumber:', error);
      return false;
    }
  }

  /**
   * Calcule les statistiques des commandes
   */
  static async getOrderStatistics(businessId: string): Promise<{
    total: number;
    pending: number;
    processing: number;
    shipped: number;
    delivered: number;
    cancelled: number;
    totalRevenue: number;
    averageOrderValue: number;
  }> {
    try {
      const orders = await this.getBusinessOrders(businessId);
      
      const stats = {
        total: orders.length,
        pending: orders.filter(o => o.status === 'pending').length,
        processing: orders.filter(o => o.status === 'processing').length,
        shipped: orders.filter(o => o.status === 'shipped').length,
        delivered: orders.filter(o => o.status === 'delivered').length,
        cancelled: orders.filter(o => o.status === 'cancelled').length,
        totalRevenue: orders.reduce((sum, order) => sum + order.totalAmount, 0),
        averageOrderValue: 0
      };

      stats.averageOrderValue = stats.total > 0 ? stats.totalRevenue / stats.total : 0;
      
      return stats;
    } catch (error) {
      console.error('Erreur dans getOrderStatistics:', error);
      return {
        total: 0,
        pending: 0,
        processing: 0,
        shipped: 0,
        delivered: 0,
        cancelled: 0,
        totalRevenue: 0,
        averageOrderValue: 0
      };
    }
  }

  /**
   * Transforme une commande de la base de données en RealOrder
   */
  private static transformDatabaseOrderToRealOrder(dbOrder: any): RealOrder {
    const product = dbOrder.product;
    const buyer = dbOrder.buyer;
    
    // Générer un numéro de commande lisible
    const orderNumber = `CMD-${new Date(dbOrder.created_at).getFullYear()}-${dbOrder.id.slice(0, 8).toUpperCase()}`;
    
    // Calculer les montants
    const subtotal = dbOrder.total_price;
    const shippingCost = Math.round(subtotal * 0.05); // 5% de frais de port
    const taxAmount = 0; // Pas de taxes pour l'instant
    const discountAmount = 0; // Pas de remises pour l'instant
    
    // Créer l'item de commande
    const orderItem: RealOrderItem = {
      id: `item-${dbOrder.id}`,
      productId: product?.id || '',
      productName: product?.name || 'Produit inconnu',
      productImage: product?.images?.[0] || '/api/placeholder/80/80',
      quantity: dbOrder.quantity,
      unitPrice: product?.price || 0,
      subtotal: dbOrder.total_price,
      sku: product?.id?.slice(0, 8).toUpperCase()
    };

    // Créer l'adresse de livraison (simulée pour l'instant)
    const shippingAddress: RealShippingAddress = {
      name: buyer?.username || 'Client inconnu',
      phone: buyer?.phone || '+225 XX XX XX XX XX',
      email: buyer?.email || '<EMAIL>',
      street: 'Adresse non spécifiée',
      city: buyer?.city || 'Abidjan',
      postalCode: '00225',
      country: 'Côte d\'Ivoire',
      instructions: 'Livraison standard'
    };

    // Déterminer la priorité basée sur le montant
    let priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal';
    if (subtotal > 50000) priority = 'high';
    if (subtotal > 100000) priority = 'urgent';
    if (subtotal < 10000) priority = 'low';

    return {
      id: dbOrder.id,
      orderNumber,
      customerId: buyer?.id || '',
      customerName: buyer?.username || 'Client inconnu',
      customerEmail: buyer?.email || '<EMAIL>',
      customerPhone: buyer?.phone,
      items: [orderItem],
      subtotal,
      shippingCost,
      taxAmount,
      discountAmount,
      totalAmount: subtotal + shippingCost,
      status: this.mapDatabaseStatusToRealStatus(dbOrder.status),
      paymentStatus: dbOrder.status === 'pending' ? 'pending' : 'paid',
      paymentMethod: 'Mobile Money',
      shippingAddress,
      trackingNumber: (dbOrder as any).tracking_number,
      notes: undefined,
      createdAt: new Date(dbOrder.created_at),
      updatedAt: new Date(dbOrder.updated_at || dbOrder.created_at),
      estimatedDelivery: this.calculateEstimatedDelivery(new Date(dbOrder.created_at)),
      priority
    };
  }

  /**
   * Mappe les statuts de la base de données vers les statuts RealOrder
   */
  private static mapDatabaseStatusToRealStatus(dbStatus: string): RealOrder['status'] {
    switch (dbStatus) {
      case 'pending': return 'pending';
      case 'confirmed': return 'confirmed';
      case 'processing': return 'processing';
      case 'shipped': return 'shipped';
      case 'delivered': return 'delivered';
      case 'cancelled': return 'cancelled';
      case 'refunded': return 'refunded';
      default: return 'pending';
    }
  }

  /**
   * Calcule la date de livraison estimée
   */
  private static calculateEstimatedDelivery(orderDate: Date): Date {
    const estimatedDate = new Date(orderDate);
    estimatedDate.setDate(estimatedDate.getDate() + 3); // 3 jours de délai
    return estimatedDate;
  }
}
