/* Data Integration Test - Styles Ultra-Avancés */
.data-integration-test {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin: 20px 0;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.test-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.test-title h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.test-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-button:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.test-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Test Summary */
.test-summary {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.summary-score {
  display: flex;
  align-items: center;
  gap: 16px;
}

.score-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.25rem;
  position: relative;
}

.score-circle.success {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.score-circle.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.score-circle.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.score-details h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.score-details p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.summary-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: white;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #374151;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Test Results */
.test-results {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-result {
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid;
  transition: all 0.2s ease;
}

.test-result.success {
  background: #f0fdf4;
  border-left-color: #10b981;
}

.test-result.warning {
  background: #fffbeb;
  border-left-color: #f59e0b;
}

.test-result.error {
  background: #fef2f2;
  border-left-color: #ef4444;
}

.test-result.loading {
  background: #f0f9ff;
  border-left-color: #3b82f6;
}

.test-result:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-success {
  color: #10b981;
}

.status-warning {
  color: #f59e0b;
}

.status-error {
  color: #ef4444;
}

.status-loading {
  color: #3b82f6;
}

.result-name {
  font-weight: 600;
  color: #1f2937;
}

.result-message {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.result-details {
  font-size: 0.8rem;
  color: #6b7280;
  margin-top: 4px;
  padding-left: 24px;
}

/* Test Recommendations */
.test-recommendations {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.test-recommendations h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recommendation {
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.875rem;
  line-height: 1.4;
}

.recommendation.success {
  background: #f0fdf4;
  color: #065f46;
  border: 1px solid #bbf7d0;
}

.recommendation.warning {
  background: #fffbeb;
  color: #92400e;
  border: 1px solid #fde68a;
}

.recommendation.error {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.recommendation.info {
  background: #f0f9ff;
  color: #1e40af;
  border: 1px solid #bfdbfe;
}

/* Responsive Design */
@media (max-width: 768px) {
  .data-integration-test {
    padding: 16px;
    margin: 16px 0;
  }
  
  .test-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .test-summary {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .summary-score {
    justify-content: center;
  }
  
  .summary-stats {
    justify-content: space-between;
  }
  
  .result-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .result-details {
    padding-left: 0;
  }
}

@media (max-width: 480px) {
  .summary-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .stat-item {
    justify-content: center;
  }
  
  .score-circle {
    width: 60px;
    height: 60px;
    font-size: 1rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .data-integration-test {
    background: #1f2937;
    border-color: #374151;
  }
  
  .test-title h2 {
    color: #f9fafb;
  }
  
  .test-summary {
    background: #374151;
  }
  
  .score-details h3 {
    color: #f9fafb;
  }
  
  .score-details p {
    color: #d1d5db;
  }
  
  .stat-item {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .result-name {
    color: #f9fafb;
  }
  
  .result-message {
    color: #d1d5db;
  }
  
  .result-details {
    color: #9ca3af;
  }
  
  .test-recommendations h3 {
    color: #f9fafb;
  }
}

/* Animations avancées */
.test-result {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-circle {
  animation: scaleIn 0.5s ease;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Accessibility */
.test-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.test-result:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .test-result,
  .score-circle,
  .test-button,
  .spinning {
    animation: none;
    transition: none;
  }
  
  .test-result:hover,
  .test-button:hover {
    transform: none;
  }
}
