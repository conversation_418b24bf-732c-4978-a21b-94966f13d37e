import React, { useState, useEffect } from 'react';
import { Heart, RefreshCw, Database, User, CheckCircle, XCircle } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { offerInteractionService } from '../../services/offerInteractionService';
import { realCameroonianOffers } from '../../data/realOffersData';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';

/**
 * Composant de test pour vérifier la persistance des likes d'offres
 */
const OfferLikePersistenceTest: React.FC = () => {
  const { currentUser } = useAuth();
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  const testOffer = realCameroonianOffers[0]; // Utiliser la première offre pour les tests

  useEffect(() => {
    if (currentUser && testOffer) {
      loadTestData();
    }
  }, [currentUser]);

  const loadTestData = async () => {
    if (!currentUser || !testOffer) return;

    setIsLoading(true);
    try {
      const hasLiked = await offerInteractionService.hasUserLikedOffer(testOffer.id, currentUser.id);
      const likesCount = await offerInteractionService.getOfferLikesCount(testOffer.id);
      const sharesCount = await offerInteractionService.getOfferSharesCount(testOffer.id);

      setTestResults({
        hasLiked,
        likesCount,
        sharesCount,
        offerId: testOffer.id,
        offerTitle: testOffer.title,
        userId: currentUser.id,
        userName: currentUser.username || currentUser.email
      });

      setLastRefresh(new Date());
    } catch (error) {
      console.error('Erreur lors du chargement des données de test:', error);
      setTestResults({
        error: error.message,
        offerId: testOffer.id,
        userId: currentUser.id
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testLike = async () => {
    if (!currentUser || !testOffer) return;

    setIsLoading(true);
    try {
      const success = await offerInteractionService.likeOffer(testOffer.id, currentUser.id);
      console.log('✅ Test like réussi:', success);
      
      // Recharger les données après le like
      await loadTestData();
    } catch (error) {
      console.error('❌ Erreur lors du test like:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testUnlike = async () => {
    if (!currentUser || !testOffer) return;

    setIsLoading(true);
    try {
      const success = await offerInteractionService.unlikeOffer(testOffer.id, currentUser.id);
      console.log('✅ Test unlike réussi:', success);
      
      // Recharger les données après l'unlike
      await loadTestData();
    } catch (error) {
      console.error('❌ Erreur lors du test unlike:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const simulatePageRefresh = () => {
    // Simuler un rafraîchissement en rechargeant les données
    loadTestData();
  };

  if (!currentUser) {
    return (
      <Card className="border-yellow-200 bg-yellow-50">
        <CardBody>
          <div className="flex items-center space-x-3">
            <User className="text-yellow-600" size={24} />
            <div>
              <h3 className="font-semibold text-yellow-900">Connexion requise</h3>
              <p className="text-sm text-yellow-700">
                Connectez-vous pour tester la persistance des likes
              </p>
            </div>
          </div>
        </CardBody>
      </Card>
    );
  }

  if (!testOffer) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardBody>
          <div className="flex items-center space-x-3">
            <XCircle className="text-red-600" size={24} />
            <div>
              <h3 className="font-semibold text-red-900">Aucune offre de test</h3>
              <p className="text-sm text-red-700">
                Aucune offre disponible pour les tests
              </p>
            </div>
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardBody>
        <div className="space-y-4">
          {/* En-tête */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Database className="text-blue-600" size={24} />
              <div>
                <h3 className="font-semibold text-blue-900">Test de Persistance des Likes</h3>
                <p className="text-sm text-blue-700">
                  Vérification de la sauvegarde en base de données
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              leftIcon={<RefreshCw size={16} />}
              onClick={simulatePageRefresh}
              disabled={isLoading}
              className="text-blue-600 border-blue-300 hover:bg-blue-100"
            >
              Simuler Refresh
            </Button>
          </div>

          {/* Informations de l'offre de test */}
          <div className="bg-white p-3 rounded-lg border border-blue-200">
            <h4 className="font-medium text-gray-900 mb-1">Offre de test :</h4>
            <p className="text-sm text-gray-600">{testOffer.title}</p>
            <p className="text-xs text-gray-500">ID: {testOffer.id}</p>
          </div>

          {/* Résultats des tests */}
          {testResults.error ? (
            <div className="bg-red-100 border border-red-300 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <XCircle className="text-red-600" size={16} />
                <span className="font-medium text-red-900">Erreur</span>
              </div>
              <p className="text-sm text-red-700 mt-1">{testResults.error}</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {/* Statut du like */}
              <div className="bg-white p-3 rounded-lg border border-gray-200">
                <div className="flex items-center space-x-2 mb-1">
                  <Heart 
                    className={testResults.hasLiked ? 'text-red-500' : 'text-gray-400'} 
                    size={16} 
                    fill={testResults.hasLiked ? 'currentColor' : 'none'}
                  />
                  <span className="font-medium text-gray-900">Statut Like</span>
                </div>
                <p className={`text-sm ${testResults.hasLiked ? 'text-red-600' : 'text-gray-600'}`}>
                  {testResults.hasLiked ? 'Liké' : 'Pas liké'}
                </p>
              </div>

              {/* Nombre de likes */}
              <div className="bg-white p-3 rounded-lg border border-gray-200">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="font-medium text-gray-900">Total Likes</span>
                </div>
                <p className="text-sm text-gray-600">
                  {testResults.likesCount || 0} like(s)
                </p>
              </div>

              {/* Nombre de partages */}
              <div className="bg-white p-3 rounded-lg border border-gray-200">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="font-medium text-gray-900">Total Partages</span>
                </div>
                <p className="text-sm text-gray-600">
                  {testResults.sharesCount || 0} partage(s)
                </p>
              </div>
            </div>
          )}

          {/* Actions de test */}
          <div className="flex space-x-3">
            <Button
              variant={testResults.hasLiked ? 'outline' : 'primary'}
              size="sm"
              leftIcon={<Heart size={16} />}
              onClick={testResults.hasLiked ? testUnlike : testLike}
              disabled={isLoading}
              className={testResults.hasLiked ? 'text-red-600 border-red-300' : ''}
            >
              {testResults.hasLiked ? 'Retirer Like' : 'Ajouter Like'}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              leftIcon={<RefreshCw size={16} />}
              onClick={loadTestData}
              disabled={isLoading}
            >
              Recharger Données
            </Button>
          </div>

          {/* Informations de debug */}
          <div className="bg-gray-100 p-3 rounded-lg text-xs text-gray-600">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div>
                <strong>Utilisateur :</strong> {testResults.userName}
              </div>
              <div>
                <strong>User ID :</strong> {testResults.userId}
              </div>
              <div>
                <strong>Offer ID :</strong> {testResults.offerId}
              </div>
              <div>
                <strong>Dernier refresh :</strong> {lastRefresh?.toLocaleTimeString() || 'Jamais'}
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-blue-100 p-3 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Instructions de test :</h4>
            <ol className="text-sm text-blue-800 space-y-1">
              <li>1. Cliquez sur "Ajouter Like" pour liker l'offre</li>
              <li>2. Cliquez sur "Simuler Refresh" pour vérifier la persistance</li>
              <li>3. Rafraîchissez la page entière (F5) pour tester la vraie persistance</li>
              <li>4. Vérifiez que le nombre de likes reste correct</li>
            </ol>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default OfferLikePersistenceTest;
