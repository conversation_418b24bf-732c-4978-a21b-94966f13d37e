/* Bulk Actions Bar - Styles Ultra-Avancés */
.bulk-actions-bar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  animation: slideUp 0.3s ease;
  max-width: 90vw;
  width: 100%;
  max-width: 1000px;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.bulk-actions-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

/* Selection Info */
.selection-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.selection-count {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #374151;
}

.selection-count strong {
  color: #1f2937;
}

.selection-controls {
  display: flex;
  align-items: center;
}

.select-all-btn,
.deselect-all-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.75rem;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-all-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.deselect-all-btn:hover {
  background: #fee2e2;
  color: #dc2626;
  border-color: #fecaca;
}

/* Bulk Actions */
.bulk-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.quick-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-right: 16px;
  border-right: 1px solid #e5e7eb;
}

.main-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bulk-action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

/* Quick Action Buttons */
.bulk-action-btn.price {
  border-color: #dbeafe;
  color: #1e40af;
}

.bulk-action-btn.price:hover {
  background: #dbeafe;
  border-color: #3b82f6;
  transform: translateY(-1px);
}

.bulk-action-btn.category {
  border-color: #d1fae5;
  color: #065f46;
}

.bulk-action-btn.category:hover {
  background: #d1fae5;
  border-color: #10b981;
  transform: translateY(-1px);
}

.bulk-action-btn.stock {
  border-color: #fef3c7;
  color: #92400e;
}

.bulk-action-btn.stock:hover {
  background: #fef3c7;
  border-color: #f59e0b;
  transform: translateY(-1px);
}

.bulk-action-btn.status {
  border-color: #e0e7ff;
  color: #3730a3;
}

.bulk-action-btn.status:hover {
  background: #e0e7ff;
  border-color: #6366f1;
  transform: translateY(-1px);
}

.bulk-action-btn.discount {
  border-color: #fce7f3;
  color: #be185d;
}

.bulk-action-btn.discount:hover {
  background: #fce7f3;
  border-color: #ec4899;
  transform: translateY(-1px);
}

/* Main Action Buttons */
.bulk-action-btn.edit {
  border-color: #3b82f6;
  color: #1e40af;
  background: #dbeafe;
}

.bulk-action-btn.edit:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-1px);
}

.bulk-action-btn.delete {
  border-color: #ef4444;
  color: #dc2626;
  background: #fee2e2;
}

.bulk-action-btn.delete:hover {
  background: #ef4444;
  color: white;
  transform: translateY(-1px);
}

/* Close Button */
.close-bulk-actions {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  flex-shrink: 0;
}

.close-bulk-actions:hover {
  background: #e5e7eb;
  color: #374151;
  transform: scale(1.05);
}

/* Responsive Design */
@media (max-width: 768px) {
  .bulk-actions-bar {
    bottom: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    max-width: none;
  }
  
  .bulk-actions-content {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .selection-info {
    justify-content: space-between;
  }
  
  .bulk-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .quick-actions {
    padding-right: 0;
    border-right: none;
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e7eb;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .main-actions {
    justify-content: space-between;
  }
  
  .bulk-action-btn {
    flex: 1;
    justify-content: center;
    min-width: 0;
  }
  
  .bulk-action-btn span {
    display: none;
  }
  
  .close-bulk-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
  }
}

@media (max-width: 480px) {
  .quick-actions {
    grid-template-columns: repeat(3, 1fr);
    display: grid;
    gap: 6px;
  }
  
  .main-actions {
    gap: 6px;
  }
  
  .bulk-action-btn {
    padding: 6px 8px;
    font-size: 0.7rem;
  }
  
  .selection-count {
    font-size: 0.8rem;
  }
  
  .select-all-btn,
  .deselect-all-btn {
    padding: 4px 8px;
    font-size: 0.7rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .bulk-actions-content {
    background: rgba(31, 41, 55, 0.95);
    border-color: #374151;
  }
  
  .selection-count {
    color: #d1d5db;
  }
  
  .selection-count strong {
    color: #f9fafb;
  }
  
  .select-all-btn,
  .deselect-all-btn {
    background: #374151;
    border-color: #4b5563;
    color: #d1d5db;
  }
  
  .select-all-btn:hover {
    background: #4b5563;
    color: #f9fafb;
  }
  
  .deselect-all-btn:hover {
    background: #7f1d1d;
    color: #fecaca;
    border-color: #991b1b;
  }
  
  .bulk-action-btn {
    background: #374151;
  }
  
  .close-bulk-actions {
    background: #374151;
    color: #d1d5db;
  }
  
  .close-bulk-actions:hover {
    background: #4b5563;
    color: #f9fafb;
  }
}

/* Animations avancées */
.bulk-action-btn {
  position: relative;
  overflow: hidden;
}

.bulk-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.bulk-action-btn:hover::before {
  left: 100%;
}

/* Pulse animation pour attirer l'attention */
.bulk-actions-bar {
  animation: slideUp 0.3s ease, pulse 2s ease-in-out 2s 3;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  }
  50% {
    box-shadow: 0 10px 40px rgba(59, 130, 246, 0.3);
  }
}

/* Accessibility */
.bulk-action-btn:focus,
.select-all-btn:focus,
.deselect-all-btn:focus,
.close-bulk-actions:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .bulk-actions-bar,
  .bulk-action-btn,
  .select-all-btn,
  .deselect-all-btn,
  .close-bulk-actions {
    animation: none;
    transition: none;
  }
  
  .bulk-action-btn:hover,
  .close-bulk-actions:hover {
    transform: none;
  }
  
  .bulk-action-btn::before {
    display: none;
  }
}
