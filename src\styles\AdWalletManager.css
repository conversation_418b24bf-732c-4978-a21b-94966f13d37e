/* Gestionnaire de portefeuille publicitaire */
.ad-wallet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.ad-wallet-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.ad-wallet-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  min-height: 80px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-left h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-button {
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid rgba(255, 255, 255, 0.4);
  border-radius: 10px;
  padding: 0.5rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1000;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.close-button:hover {
  background: white;
  color: #1f2937;
  border-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  transform: translateY(-2px) scale(1.05);
}

.close-button:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Alertes de facturation */
.billing-alerts {
  background: #fef3c7;
  border-bottom: 1px solid #f59e0b;
  padding: 1rem 1.5rem;
}

.alerts-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: #92400e;
  font-weight: 600;
}

.clear-all-btn {
  margin-left: auto;
  background: none;
  border: none;
  color: #92400e;
  cursor: pointer;
  font-size: 0.875rem;
  text-decoration: underline;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.alert {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
}

.alert-low_balance {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.alert-payment_failed {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.alert-campaign_paused {
  background: #fffbeb;
  color: #92400e;
  border: 1px solid #fed7aa;
}

.alert button {
  margin-left: auto;
  background: none;
  border: none;
  cursor: pointer;
  opacity: 0.7;
}

.alert button:hover {
  opacity: 1;
}

/* Navigation */
.wallet-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 500;
  color: #6b7280;
  transition: all 0.2s;
}

.tab:hover {
  background: #f3f4f6;
  color: #374151;
}

.tab.active {
  background: white;
  color: #4f46e5;
  border-bottom: 2px solid #4f46e5;
}

/* Contenu */
.wallet-content {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

/* Vue d'ensemble */
.overview-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
}

.balance-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.balance-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 500;
}

.toggle-balance {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 6px;
  padding: 0.5rem;
  color: white;
  cursor: pointer;
}

.balance-amount {
  margin-bottom: 1rem;
}

.amount {
  font-size: 2.5rem;
  font-weight: 700;
}

.amount-hidden {
  font-size: 2.5rem;
  font-weight: 700;
  letter-spacing: 0.1em;
}

.balance-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.status-warning {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #fbbf24;
}

.status-good {
  color: #d1fae5;
}

/* Grille de statistiques */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  padding: 0.75rem;
  border-radius: 10px;
  background: #f3f4f6;
}

.stat-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.stat-content p {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
}

/* Transactions récentes */
.recent-transactions h4 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-weight: 600;
}

.transactions-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.transaction-icon {
  padding: 0.5rem;
  border-radius: 8px;
  background: white;
}

.transaction-details {
  flex: 1;
}

.transaction-description {
  margin: 0 0 0.25rem 0;
  font-weight: 500;
  color: #1f2937;
}

.transaction-date {
  font-size: 0.875rem;
  color: #6b7280;
}

.transaction-amount {
  font-weight: 600;
  font-size: 1rem;
}

/* Onglet Recharge */
.recharge-tab {
  max-width: 500px;
  margin: 0 auto;
}

.recharge-form h3 {
  margin: 0 0 1.5rem 0;
  text-align: center;
  color: #1f2937;
}

.quick-amounts {
  margin-bottom: 1.5rem;
}

.quick-amounts h4 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-weight: 600;
}

.amount-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.amount-btn {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.amount-btn:hover {
  border-color: #4f46e5;
  background: #f8fafc;
}

.amount-btn.selected {
  border-color: #4f46e5;
  background: #4f46e5;
  color: white;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.input-hint {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.recharge-summary {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.summary-row.total {
  border-top: 1px solid #e5e7eb;
  padding-top: 0.5rem;
  margin-top: 0.5rem;
  font-weight: 600;
}

.recharge-btn {
  width: 100%;
  padding: 0.875rem;
  font-size: 1rem;
  font-weight: 600;
}

/* Onglet Transactions */
.transactions-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.transactions-header h3 {
  margin: 0;
  color: #1f2937;
}

.transactions-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.filter-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
}

.transactions-table {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  align-items: center;
}

.table-row:hover {
  background: #f9fafb;
}

.transaction-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-completed {
  background: #d1fae5;
  color: #065f46;
}

.status-pending {
  background: #fef3c7;
  color: #92400e;
}

.status-failed {
  background: #fee2e2;
  color: #991b1b;
}

/* Onglet Méthodes */
.methods-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.methods-header h3 {
  margin: 0;
  color: #1f2937;
}

.payment-methods-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.payment-method-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
}

.method-icon {
  padding: 0.75rem;
  background: #f3f4f6;
  border-radius: 10px;
}

.method-details {
  flex: 1;
}

.method-details h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
  font-weight: 600;
}

.method-details p {
  margin: 0 0 0.25rem 0;
  color: #6b7280;
}

.method-type {
  font-size: 0.75rem;
  color: #9ca3af;
  text-transform: uppercase;
  font-weight: 500;
}

.method-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.default-badge {
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.action-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #f9fafb;
}

.action-btn.danger {
  color: #dc2626;
  border-color: #fecaca;
}

.action-btn.danger:hover {
  background: #fef2f2;
}

/* Formulaire d'ajout de méthode */
.add-method-form {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
}

.add-method-form h4 {
  margin: 0 0 1.5rem 0;
  color: #1f2937;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

/* Classes utilitaires pour les couleurs */
.text-green-500 { color: #10b981; }
.text-red-500 { color: #ef4444; }
.text-blue-500 { color: #3b82f6; }
.text-purple-500 { color: #8b5cf6; }
.text-green-600 { color: #059669; }
.text-red-600 { color: #dc2626; }
.text-blue-600 { color: #2563eb; }
.text-purple-600 { color: #7c3aed; }

/* Responsive */
@media (max-width: 768px) {
  .ad-wallet-modal {
    margin: 0;
    border-radius: 0;
    height: 100vh;
    max-height: none;
  }

  .wallet-tabs {
    overflow-x: auto;
  }

  .tab {
    white-space: nowrap;
    min-width: 120px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .amount-buttons {
    grid-template-columns: repeat(2, 1fr);
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .table-header > div,
  .table-row > div {
    padding: 0.25rem 0;
  }

  .transactions-filters {
    flex-direction: column;
  }

  .methods-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .payment-method-card {
    flex-direction: column;
    text-align: center;
  }

  .method-actions {
    justify-content: center;
  }
}

/* Styles pour le composant de démonstration */
.demo-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.demo-section h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-weight: 600;
  font-size: 1.125rem;
}

.wallet-status {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.status-item:last-child {
  border-bottom: none;
}

.status-item .label {
  color: #6b7280;
  font-weight: 500;
}

.status-item .value {
  color: #1f2937;
  font-weight: 600;
}

.status-active {
  color: #10b981;
  text-transform: capitalize;
}

.demo-controls {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.demo-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.demo-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s;
}

.demo-btn.recharge {
  background: #10b981;
  color: white;
}

.demo-btn.recharge:hover {
  background: #059669;
}

.demo-btn.charge {
  background: #ef4444;
  color: white;
}

.demo-btn.charge:hover {
  background: #dc2626;
}

.demo-btn.charge:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.demo-btn.alert {
  border-color: #f59e0b;
  color: #f59e0b;
}

.demo-btn.alert:hover {
  background: #fef3c7;
}

.no-wallet,
.no-transactions,
.no-methods {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  background: #f9fafb;
  border-radius: 8px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  border-radius: 12px;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #991b1b;
  margin-top: 1rem;
}

.alert-date {
  font-size: 0.75rem;
  color: #6b7280;
  margin-left: auto;
}
