import React from 'react';
import { Link } from 'react-router-dom';
import { Star, TrendingUp } from 'lucide-react';
import { DEFAULT_IMAGES } from '../../constants/defaultImages';

interface Product {
  id: string;
  name: string;
  category: string;
  rating: number;
  image?: string;
  businessName: string;
}

interface TopRatedProductsProps {
  className?: string;
}

// Données mockées pour les produits mieux notés
const mockTopProducts: Product[] = [
  {
    id: '1',
    name: '<PERSON><PERSON>',
    category: 'Dexima',
    rating: 5.0,
    image: DEFAULT_IMAGES.PRODUCT,
    businessName: 'Dexima'
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    category: 'Dexima',
    rating: 5.0,
    image: DEFAULT_IMAGES.PRODUCT,
    businessName: 'Dexima'
  },
  {
    id: '3',
    name: '<PERSON><PERSON>',
    category: 'Dexima',
    rating: 5.0,
    image: DEFAULT_IMAGES.PRODUCT,
    businessName: 'Dexima'
  }
];

const TopRatedProducts: React.FC<TopRatedProductsProps> = ({ className = '' }) => {
  return (
    <div className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden ${className}`}>
      <div className="bg-gradient-to-r from-orange-500 to-red-600 px-4 py-3">
        <div className="flex items-center text-white">
          <div className="bg-white/20 rounded-lg p-1.5 mr-2">
            <TrendingUp size={16} />
          </div>
          <h3 className="font-semibold text-sm">Produits Mieux Notés</h3>
        </div>
      </div>

      <div className="p-4">
        <div className="space-y-3">
          {mockTopProducts.map((product) => (
            <div key={product.id} className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer border border-gray-100">
              <div className="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = DEFAULT_IMAGES.PRODUCT;
                  }}
                />
              </div>

              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-gray-900 text-sm truncate">
                  {product.name}
                </h4>
                <p className="text-xs text-gray-500 truncate font-medium">
                  {product.category}
                </p>
                <div className="flex items-center mt-1">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        size={12}
                        className={`${
                          i < Math.floor(product.rating)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-xs font-bold text-gray-900 ml-1">
                    {product.rating.toFixed(1)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-4 pt-3 border-t border-gray-100">
          <Link
            to="/top-rated-products"
            className="block w-full text-center text-sm text-blue-600 hover:text-blue-700 font-semibold transition-colors bg-blue-50 hover:bg-blue-100 py-2 rounded-lg"
          >
            Voir tous les classements
          </Link>
        </div>
      </div>
    </div>
  );
};

export default TopRatedProducts;
