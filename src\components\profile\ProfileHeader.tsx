import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useFollow } from '../../context/FollowContext';
import { UserRole } from '../../types';
import NotificationsDropdown from '../notifications/NotificationsDropdown';
import { MapPin, Calendar, Link as LinkIcon, Globe, Check } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import Button from '../ui/Button';
import Image from '../ui/Image';
import { fixAvatarImagePath } from '../../utils/imageUtils';

interface ProfileHeaderProps {
  username: string;
  profilePicture: string;
  coverPhoto: string;
  location?: string;
  websiteUrl?: string;
  joinDate?: string;
  isOwnProfile: boolean;
  userId: string;
  userRole?: UserRole;
  followersCount?: number;
  followingCount?: number;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  username,
  profilePicture,
  coverPhoto,
  location,
  websiteUrl,
  joinDate,
  isOwnProfile,
  userId,
  userRole,
  followersCount = 0,
  followingCount = 0
}) => {
  const { isFollowing, followUser, unfollowUser, getFollowersDetails, getFollowingDetails } = useFollow();
  const [following, setFollowing] = useState<boolean>(false);
  const [followers, setFollowers] = useState<number>(followersCount);
  const [followings, setFollowings] = useState<number>(followingCount);
  const navigate = useNavigate();
  
  useEffect(() => {
    const checkFollowStatus = async () => {
      if (userId) {
        const isUserFollowed = await isFollowing(userId);
        setFollowing(isUserFollowed);
      }
    };
    
    checkFollowStatus();
  }, [userId, isFollowing]);
  
  useEffect(() => {
    const fetchFollowCounts = async () => {
      if (userId) {
        try {
          const followersList = await getFollowersDetails(userId);
          const followingList = await getFollowingDetails(userId);
          
          setFollowers(followersList.length);
          setFollowings(followingList.length);
        } catch (error) {
          console.error("Erreur lors de la récupération des abonnés/abonnements:", error);
        }
      }
    };
    
    fetchFollowCounts();
  }, [userId, getFollowersDetails, getFollowingDetails]);

  const handleFollowToggle = async () => {
    try {
      if (following) {
        await unfollowUser(userId);
        setFollowing(false);
        setFollowers(prev => Math.max(0, prev - 1));
      } else {
        await followUser(userId);
        setFollowing(true);
        setFollowers(prev => prev + 1);
      }
    } catch (error) {
      console.error("Erreur lors de la modification de l'abonnement:", error);
    }
  };
  
  const navigateToNetwork = (tab: 'followers' | 'following') => {
    navigate(`/network/${userId}/${tab}`);
  };

  return (
    <div className="w-full bg-white rounded-lg shadow-sm overflow-hidden mb-4">
      {/* Cover Photo */}
      <div className="h-48 w-full relative">
        <Image 
          src={coverPhoto}
          alt="Couverture"
          type="cover"
          className="w-full h-full object-cover"
        />
        
        {/* Profile Name Overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-4 text-white">
          <h1 className="text-2xl font-bold">{username}</h1>
        </div>
        
        {/* Follow Button for Non-Own Profiles */}
        {!isOwnProfile && (
          <div className="absolute top-4 right-4 z-10">
            <Button
              variant="primary"
              onClick={handleFollowToggle}
              className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-4 py-1 shadow flex items-center gap-1 text-sm"
            >
              {following ? 'Abonné' : 'Suivre'}
            </Button>
          </div>
        )}
        
        {/* Avatar overlapping the cover photo */}
        <div className="absolute left-4 -bottom-12 z-20">
          <div className="relative">
            <Image 
              src={profilePicture}
              alt={`Profil de ${username}`}
              type="avatar"
              size="large"
              className="w-24 h-24 rounded-full border-4 border-white shadow-lg object-cover"
            />
            <div className="absolute -bottom-1 -right-1 bg-blue-600 rounded-full p-1 border-2 border-white">
              <Check size={14} className="text-white" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Profile Info Section */}
      <div className="pt-16 pb-4 px-4">
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          {location && (
            <div className="flex items-center">
              <MapPin size={16} className="mr-1" />
              <span>{location}</span>
            </div>
          )}
          {joinDate && (
            <div className="flex items-center">
              <Calendar size={16} className="mr-1" />
              <span>Membre depuis {joinDate}</span>
            </div>
          )}
          {websiteUrl && (
            <div className="flex items-center">
              <Globe size={16} className="mr-1" />
              <a href={websiteUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                {websiteUrl.replace(/(^\w+:|^)\/\//, '')}
              </a>
            </div>
          )}
        </div>
        
        {/* Network Stats */}
        <div className="mt-4 flex items-center space-x-6">
          <button 
            onClick={() => navigateToNetwork('followers')}
            className="text-sm text-gray-600 hover:text-gray-900"
          >
            <span className="font-semibold">{followers}</span> abonnés
          </button>
          <button 
            onClick={() => navigateToNetwork('following')}
            className="text-sm text-gray-600 hover:text-gray-900"
          >
            <span className="font-semibold">{followings}</span> abonnements
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader;
