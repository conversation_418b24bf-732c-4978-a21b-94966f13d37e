/**
 * Utilitaires pour la gestion et le formatage des dates
 */

/**
 * Formate une date en temps relatif (ex: "il y a 2 heures")
 * @param date - La date à formater
 * @returns Une chaîne représentant le temps écoulé
 */
export const formatTimeAgo = (date: Date): string => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  // Moins d'une minute
  if (diffInSeconds < 60) {
    return 'À l\'instant';
  }

  // Moins d'une heure
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `Il y a ${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''}`;
  }

  // Moins d'un jour
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `Il y a ${diffInHours} heure${diffInHours > 1 ? 's' : ''}`;
  }

  // Moins d'une semaine
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `Il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;
  }

  // Moins d'un mois
  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `Il y a ${diffInWeeks} semaine${diffInWeeks > 1 ? 's' : ''}`;
  }

  // Moins d'un an
  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `Il y a ${diffInMonths} mois`;
  }

  // Plus d'un an
  const diffInYears = Math.floor(diffInDays / 365);
  return `Il y a ${diffInYears} an${diffInYears > 1 ? 's' : ''}`;
};

/**
 * Formate une date en format français (ex: "15 janvier 2024")
 * @param date - La date à formater
 * @returns Une chaîne formatée en français
 */
export const formatDateFrench = (date: Date): string => {
  const options: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  };
  
  return date.toLocaleDateString('fr-FR', options);
};

/**
 * Formate une date avec l'heure en format français (ex: "15 janvier 2024 à 14h30")
 * @param date - La date à formater
 * @returns Une chaîne formatée avec date et heure
 */
export const formatDateTimeFrench = (date: Date): string => {
  const dateOptions: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  };
  
  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit'
  };
  
  const dateStr = date.toLocaleDateString('fr-FR', dateOptions);
  const timeStr = date.toLocaleTimeString('fr-FR', timeOptions);
  
  return `${dateStr} à ${timeStr}`;
};

/**
 * Formate une date en format court (ex: "15/01/2024")
 * @param date - La date à formater
 * @returns Une chaîne formatée en format court
 */
export const formatDateShort = (date: Date): string => {
  return date.toLocaleDateString('fr-FR');
};

/**
 * Vérifie si une date est aujourd'hui
 * @param date - La date à vérifier
 * @returns true si la date est aujourd'hui
 */
export const isToday = (date: Date): boolean => {
  const today = new Date();
  return date.toDateString() === today.toDateString();
};

/**
 * Vérifie si une date est hier
 * @param date - La date à vérifier
 * @returns true si la date est hier
 */
export const isYesterday = (date: Date): boolean => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return date.toDateString() === yesterday.toDateString();
};

/**
 * Formate une date de manière intelligente selon sa proximité
 * @param date - La date à formater
 * @returns Une chaîne formatée de manière contextuelle
 */
export const formatDateSmart = (date: Date): string => {
  if (isToday(date)) {
    return `Aujourd'hui à ${date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}`;
  }
  
  if (isYesterday(date)) {
    return `Hier à ${date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}`;
  }
  
  const now = new Date();
  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffInDays < 7) {
    const dayName = date.toLocaleDateString('fr-FR', { weekday: 'long' });
    const time = date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
    return `${dayName} à ${time}`;
  }
  
  return formatDateTimeFrench(date);
};

/**
 * Calcule la différence entre deux dates en jours
 * @param date1 - Première date
 * @param date2 - Deuxième date
 * @returns Le nombre de jours de différence
 */
export const daysDifference = (date1: Date, date2: Date): number => {
  const diffInTime = Math.abs(date2.getTime() - date1.getTime());
  return Math.ceil(diffInTime / (1000 * 60 * 60 * 24));
};

/**
 * Ajoute un nombre de jours à une date
 * @param date - La date de base
 * @param days - Le nombre de jours à ajouter
 * @returns Une nouvelle date
 */
export const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

/**
 * Soustrait un nombre de jours à une date
 * @param date - La date de base
 * @param days - Le nombre de jours à soustraire
 * @returns Une nouvelle date
 */
export const subtractDays = (date: Date, days: number): Date => {
  return addDays(date, -days);
};

/**
 * Obtient le début de la journée (00:00:00)
 * @param date - La date
 * @returns Une nouvelle date au début de la journée
 */
export const startOfDay = (date: Date): Date => {
  const result = new Date(date);
  result.setHours(0, 0, 0, 0);
  return result;
};

/**
 * Obtient la fin de la journée (23:59:59)
 * @param date - La date
 * @returns Une nouvelle date à la fin de la journée
 */
export const endOfDay = (date: Date): Date => {
  const result = new Date(date);
  result.setHours(23, 59, 59, 999);
  return result;
};

/**
 * Formate une durée en secondes en format lisible
 * @param seconds - Le nombre de secondes
 * @returns Une chaîne formatée (ex: "2h 30min")
 */
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`;
  }
  
  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) {
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}min ${remainingSeconds}s` : `${minutes}min`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  if (hours < 24) {
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`;
  }
  
  const days = Math.floor(hours / 24);
  const remainingHours = hours % 24;
  return remainingHours > 0 ? `${days}j ${remainingHours}h` : `${days}j`;
};

/**
 * Valide si une chaîne est une date valide
 * @param dateString - La chaîne à valider
 * @returns true si c'est une date valide
 */
export const isValidDate = (dateString: string): boolean => {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
};

/**
 * Convertit une date en timestamp Unix
 * @param date - La date à convertir
 * @returns Le timestamp Unix en secondes
 */
export const toUnixTimestamp = (date: Date): number => {
  return Math.floor(date.getTime() / 1000);
};

/**
 * Convertit un timestamp Unix en date
 * @param timestamp - Le timestamp Unix en secondes
 * @returns Une nouvelle date
 */
export const fromUnixTimestamp = (timestamp: number): Date => {
  return new Date(timestamp * 1000);
};
