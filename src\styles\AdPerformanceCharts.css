.ad-performance-charts {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-top: 2rem;
}

.ad-performance-charts h2 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.75rem;
}

.ad-performance-charts h3 {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: #555;
  text-align: center;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.chart-container {
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.campaign-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  padding: 1rem;
}

.stat-card {
  background-color: white;
  border-radius: 6px;
  padding: 1rem;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
}

/* Styles spécifiques pour les différents types de statistiques */
.stat-card:nth-child(1) .stat-value {
  color: #8884d8;
}

.stat-card:nth-child(2) .stat-value {
  color: #82ca9d;
}

.stat-card:nth-child(3) .stat-value {
  color: #ffc658;
}

.stat-card:nth-child(4) .stat-value {
  color: #ff8042;
}

.stat-card:nth-child(5) .stat-value {
  color: #0088fe;
}

.stat-card:nth-child(6) .stat-value {
  color: #00c49f;
}

/* Responsive */
@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .campaign-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .campaign-stats {
    grid-template-columns: 1fr;
  }
}
