import { useEffect } from 'react';
import { IPost } from '../types/index';
import { processImages } from '../utils/imageUtils';

/**
 * Hook pour valider et surveiller les images dans les posts
 */
export const usePostImageValidation = (posts: IPost[]) => {
  useEffect(() => {
    if (posts.length > 0) {
      const postsWithImages = posts.filter(post => post.images && post.images.length > 0);
      const postsWithoutImages = posts.filter(post => !post.images || post.images.length === 0);

      if (process.env.NODE_ENV === 'development') {
        console.log('=== VALIDATION DES IMAGES DE POSTS ===');
        console.log(`Posts avec images: ${postsWithImages.length}`);
        console.log(`Posts sans images: ${postsWithoutImages.length}`);

        // Analyser les URLs d'images
        postsWithImages.forEach(post => {
          console.log(`Post ${post.id}:`, {
            imageCount: post.images.length,
            images: post.images,
            businessName: post.businessName,
            productName: post.productName
          });

          // Vérifier la validité des URLs
          post.images.forEach((imageUrl, index) => {
            if (!imageUrl || typeof imageUrl !== 'string' || imageUrl.trim() === '') {
              console.warn(`Image ${index} invalide pour le post ${post.id}:`, imageUrl);
            } else if (!imageUrl.startsWith('http')) {
              console.warn(`Image ${index} sans protocole HTTP pour le post ${post.id}:`, imageUrl);
            }
          });
        });

        console.log('=====================================');
      }
    }
  }, [posts]);

  // Statistiques sur les images
  const imageStats = posts.reduce((acc, post) => {
    // Utiliser la fonction robuste pour traiter les images
    const validImages = processImages(post.images);

    if (validImages.length > 0) {
      acc.postsWithImages++;
      acc.totalImages += validImages.length;
      acc.validImages += validImages.length;
    } else {
      acc.postsWithoutImages++;
    }

    return acc;
  }, {
    postsWithImages: 0,
    postsWithoutImages: 0,
    totalImages: 0,
    validImages: 0,
    invalidImages: 0
  });

  return {
    postsWithImages: imageStats.postsWithImages,
    postsWithoutImages: imageStats.postsWithoutImages,
    totalImages: imageStats.totalImages,
    validImages: imageStats.validImages,
    invalidImages: imageStats.invalidImages,
    imageSuccessRate: imageStats.totalImages > 0 ?
      Math.round((imageStats.validImages / imageStats.totalImages) * 100) : 0
  };
};
