import React, { useState } from 'react';
import {
  X,
  AlertTriangle,
  Clock,
  Ban,
  CheckCircle,
  Calendar,
  FileText,
  User,
  Building2
} from 'lucide-react';
import Modal from '../ui/Modal';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';
import { formatAmount } from '../../utils/formatUtils';
import { AdminSubscription } from '../../services/adminPaymentService';

interface SuspendSubscriptionModalProps {
  subscription: AdminSubscription | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (subscriptionId: string, reason: string, suspensionType: 'temporary' | 'permanent', duration?: number) => void;
}

const SuspendSubscriptionModal: React.FC<SuspendSubscriptionModalProps> = ({
  subscription,
  isOpen,
  onClose,
  onConfirm
}) => {
  const [suspensionType, setSuspensionType] = useState<'temporary' | 'permanent'>('temporary');
  const [reason, setReason] = useState('');
  const [duration, setDuration] = useState(30); // jours
  const [customReason, setCustomReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const predefinedReasons = [
    'Violation des conditions d\'utilisation',
    'Paiement en retard ou échoué',
    'Activité suspecte détectée',
    'Demande de l\'entreprise',
    'Maintenance technique',
    'Enquête en cours',
    'Non-conformité réglementaire',
    'Autre (préciser ci-dessous)'
  ];

  const handleSubmit = async () => {
    if (!subscription || !reason) return;

    const finalReason = reason === 'Autre (préciser ci-dessous)' ? customReason : reason;
    
    if (!finalReason.trim()) {
      alert('Veuillez préciser la raison de la suspension.');
      return;
    }

    setIsSubmitting(true);
    try {
      await onConfirm(
        subscription.id, 
        finalReason, 
        suspensionType, 
        suspensionType === 'temporary' ? duration : undefined
      );
      onClose();
      resetForm();
    } catch (error) {
      console.error('Erreur lors de la suspension:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setSuspensionType('temporary');
    setReason('');
    setCustomReason('');
    setDuration(30);
  };

  const calculateSuspensionEndDate = () => {
    if (suspensionType === 'permanent') return null;
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + duration);
    return endDate;
  };

  const getSuspensionImpact = () => {
    if (!subscription) return null;

    const impacts = [];
    
    if (suspensionType === 'temporary') {
      impacts.push('Accès temporairement suspendu');
      impacts.push('Réactivation automatique prévue');
      impacts.push('Données conservées');
      if (subscription.autoRenewal) {
        impacts.push('Renouvellement automatique suspendu');
      }
    } else {
      impacts.push('Accès définitivement suspendu');
      impacts.push('Réactivation manuelle requise');
      impacts.push('Données conservées (30 jours)');
      impacts.push('Renouvellement automatique annulé');
    }

    return impacts;
  };

  if (!subscription) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <div className="p-6">
        {/* En-tête */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 rounded-full">
              <Ban className="text-red-600" size={20} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Suspendre l'abonnement</h2>
              <p className="text-gray-600">{subscription.businessName}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Informations de l'abonnement */}
        <Card className="mb-6">
          <CardBody className="p-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center space-x-2">
                <Building2 size={16} className="text-gray-400" />
                <span className="text-gray-600">Entreprise:</span>
                <span className="font-medium">{subscription.businessName}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar size={16} className="text-gray-400" />
                <span className="text-gray-600">Plan:</span>
                <span className="font-medium">
                  {subscription.planType === 'yearly' ? 'Annuel' : 
                   subscription.planType === 'monthly' ? 'Mensuel' : 'Essai'}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-gray-600">Montant:</span>
                <span className="font-medium">{formatAmount(subscription.amount)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-gray-600">Statut actuel:</span>
                <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                  <CheckCircle size={12} className="mr-1" />
                  Actif
                </span>
              </div>
            </div>
          </CardBody>
        </Card>

        <div className="space-y-6">
          {/* Type de suspension */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Type de suspension
            </label>
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={() => setSuspensionType('temporary')}
                className={`p-4 border-2 rounded-lg text-left transition-colors ${
                  suspensionType === 'temporary'
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2 mb-2">
                  <Clock size={16} className="text-orange-500" />
                  <span className="font-medium">Temporaire</span>
                </div>
                <p className="text-sm text-gray-600">
                  Suspension pour une durée déterminée avec réactivation automatique
                </p>
              </button>
              
              <button
                onClick={() => setSuspensionType('permanent')}
                className={`p-4 border-2 rounded-lg text-left transition-colors ${
                  suspensionType === 'permanent'
                    ? 'border-red-500 bg-red-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2 mb-2">
                  <Ban size={16} className="text-red-500" />
                  <span className="font-medium">Permanente</span>
                </div>
                <p className="text-sm text-gray-600">
                  Suspension définitive nécessitant une réactivation manuelle
                </p>
              </button>
            </div>
          </div>

          {/* Durée (pour suspension temporaire) */}
          {suspensionType === 'temporary' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Durée de la suspension
              </label>
              <div className="grid grid-cols-4 gap-2">
                {[7, 15, 30, 60].map((days) => (
                  <button
                    key={days}
                    onClick={() => setDuration(days)}
                    className={`p-2 text-sm border rounded-lg transition-colors ${
                      duration === days
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    {days} jours
                  </button>
                ))}
              </div>
              <div className="mt-2">
                <input
                  type="number"
                  min="1"
                  max="365"
                  value={duration}
                  onChange={(e) => setDuration(parseInt(e.target.value) || 1)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Nombre de jours personnalisé"
                />
              </div>
              {calculateSuspensionEndDate() && (
                <p className="text-sm text-gray-600 mt-2">
                  Réactivation prévue le: {calculateSuspensionEndDate()!.toLocaleDateString('fr-FR', {
                    day: '2-digit',
                    month: 'long',
                    year: 'numeric'
                  })}
                </p>
              )}
            </div>
          )}

          {/* Raison de la suspension */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Raison de la suspension *
            </label>
            <div className="space-y-2">
              {predefinedReasons.map((predefinedReason) => (
                <label key={predefinedReason} className="flex items-center">
                  <input
                    type="radio"
                    name="reason"
                    value={predefinedReason}
                    checked={reason === predefinedReason}
                    onChange={(e) => setReason(e.target.value)}
                    className="mr-2"
                  />
                  <span className="text-sm">{predefinedReason}</span>
                </label>
              ))}
            </div>
            
            {reason === 'Autre (préciser ci-dessous)' && (
              <textarea
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                placeholder="Veuillez préciser la raison de la suspension..."
                className="mt-2 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                required
              />
            )}
          </div>

          {/* Impact de la suspension */}
          <Card>
            <CardBody className="p-4">
              <div className="flex items-center space-x-2 mb-3">
                <AlertTriangle className="text-orange-500" size={16} />
                <h4 className="font-medium text-gray-900">Impact de la suspension</h4>
              </div>
              <ul className="space-y-1 text-sm text-gray-600">
                {getSuspensionImpact()?.map((impact, index) => (
                  <li key={index} className="flex items-center space-x-2">
                    <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                    <span>{impact}</span>
                  </li>
                ))}
              </ul>
            </CardBody>
          </Card>

          {/* Avertissement */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="text-red-500 mt-0.5" size={16} />
              <div>
                <h4 className="font-medium text-red-800 mb-1">Attention</h4>
                <p className="text-sm text-red-700">
                  Cette action suspendra immédiatement l'accès de l'entreprise à la plateforme. 
                  L'entreprise sera notifiée par email de cette suspension.
                  {suspensionType === 'permanent' && ' Une suspension permanente nécessitera une intervention manuelle pour la réactivation.'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Annuler
          </Button>
          <Button
            variant="danger"
            onClick={handleSubmit}
            disabled={!reason || isSubmitting}
            leftIcon={isSubmitting ? <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div> : <Ban size={16} />}
          >
            {isSubmitting ? 'Suspension...' : `Suspendre ${suspensionType === 'temporary' ? 'temporairement' : 'définitivement'}`}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default SuspendSubscriptionModal;
