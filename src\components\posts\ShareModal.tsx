import React, { useState } from 'react';
import { X, Copy, Facebook, Twitter, Linkedin, Mail, MessageCircle, Link2, Users, Heart } from 'lucide-react';
import { IPost } from '../../types/index';
import Button from '../ui/Button';
import Modal from '../ui/Modal';

interface ShareModalProps {
  post: IPost;
  isOpen: boolean;
  onClose: () => void;
  onShareComplete: (platform: string) => void;
}

const ShareModal: React.FC<ShareModalProps> = ({ post, isOpen, onClose, onShareComplete }) => {
  const [copied, setCopied] = useState(false);
  const [customMessage, setCustomMessage] = useState('');

  const postUrl = `${window.location.origin}/posts/${post.id}`;
  const shareTitle = `Découvrez ce post sur ${post.businessName}`;
  const shareText = post.description 
    ? post.description.substring(0, 100) + '...' 
    : `Avis sur ${post.productName}`;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(postUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      onShareComplete('copy_link');
    } catch (error) {
      console.error('Erreur lors de la copie:', error);
    }
  };

  const handleSocialShare = (platform: string, url: string) => {
    // Ajouter le message personnalisé si présent
    let finalUrl = url;
    if (customMessage && platform !== 'email') {
      const encodedMessage = encodeURIComponent(customMessage + '\n\n');
      if (platform === 'twitter') {
        finalUrl = url.replace(encodeURIComponent(shareText), encodedMessage + encodeURIComponent(shareText));
      } else if (platform === 'whatsapp') {
        finalUrl = url.replace(encodeURIComponent(shareText), encodedMessage + encodeURIComponent(shareText));
      }
    }

    window.open(finalUrl, '_blank', 'width=600,height=400');
    onShareComplete(platform);
    onClose();
  };

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: shareTitle,
          text: shareText,
          url: postUrl,
        });
        onShareComplete('native_share');
        onClose();
      } catch (error) {
        console.error('Erreur lors du partage natif:', error);
      }
    }
  };

  const shareOptions = [
    {
      name: 'Copier le lien',
      icon: Copy,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100 hover:bg-gray-200',
      action: handleCopyLink,
      description: 'Copier le lien dans le presse-papiers'
    },
    {
      name: 'Facebook',
      icon: Facebook,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 hover:bg-blue-200',
      action: () => handleSocialShare('facebook', 
        `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(postUrl)}&quote=${encodeURIComponent(shareText)}`
      ),
      description: 'Partager sur Facebook'
    },
    {
      name: 'Twitter',
      icon: Twitter,
      color: 'text-blue-400',
      bgColor: 'bg-blue-50 hover:bg-blue-100',
      action: () => handleSocialShare('twitter',
        `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(postUrl)}`
      ),
      description: 'Partager sur Twitter'
    },
    {
      name: 'LinkedIn',
      icon: Linkedin,
      color: 'text-blue-700',
      bgColor: 'bg-blue-100 hover:bg-blue-200',
      action: () => handleSocialShare('linkedin',
        `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(postUrl)}`
      ),
      description: 'Partager sur LinkedIn'
    },
    {
      name: 'Email',
      icon: Mail,
      color: 'text-green-600',
      bgColor: 'bg-green-100 hover:bg-green-200',
      action: () => handleSocialShare('email',
        `mailto:?subject=${encodeURIComponent(shareTitle)}&body=${encodeURIComponent(shareText + '\n\n' + postUrl)}`
      ),
      description: 'Partager par email'
    },
    {
      name: 'WhatsApp',
      icon: MessageCircle,
      color: 'text-green-500',
      bgColor: 'bg-green-100 hover:bg-green-200',
      action: () => handleSocialShare('whatsapp',
        `https://wa.me/?text=${encodeURIComponent(shareText + '\n' + postUrl)}`
      ),
      description: 'Partager sur WhatsApp'
    }
  ];

  // Ajouter l'option de partage natif si disponible
  if (navigator.share) {
    shareOptions.unshift({
      name: 'Partager',
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 hover:bg-purple-200',
      action: handleNativeShare,
      description: 'Utiliser le menu de partage du système'
    });
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Partager cette publication" size="lg">
      <div>
        {/* Aperçu du post */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
          <div className="flex items-center mb-3">
            <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
              {post.businessName?.charAt(0) || post.username?.charAt(0)}
            </div>
            <div className="ml-3">
              <h4 className="font-semibold text-gray-900">{post.businessName || post.username}</h4>
              <p className="text-sm text-gray-600">{post.productName}</p>
            </div>
          </div>
          <p className="text-gray-700 text-sm line-clamp-3">
            {post.description || `Avis sur ${post.productName}`}
          </p>
          <div className="mt-2 flex items-center text-sm text-gray-500">
            <Heart size={16} className="mr-1" />
            <span>{post.likes?.length || 0} j'aime</span>
            <span className="mx-2">•</span>
            <span>{post.comments?.length || 0} commentaires</span>
            <span className="mx-2">•</span>
            <span>{post.shares?.length || 0} partages</span>
          </div>
        </div>

        {/* Options de partage */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Choisir une plateforme</h4>
          <div className="grid grid-cols-2 gap-3">
            {shareOptions.map((option) => (
              <button
                key={option.name}
                onClick={option.action}
                className={`flex items-center p-3 rounded-lg transition-all duration-200 ${option.bgColor} group hover:shadow-md`}
              >
                <div className={`p-2 rounded-full ${option.color} group-hover:scale-110 transition-transform`}>
                  <option.icon size={20} />
                </div>
                <div className="ml-3 text-left">
                  <div className="font-medium text-gray-900">{option.name}</div>
                  <div className="text-xs text-gray-600">{option.description}</div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Message personnalisé */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Message personnalisé (optionnel)
          </label>
          <textarea
            value={customMessage}
            onChange={(e) => setCustomMessage(e.target.value)}
            placeholder="Ajoutez votre propre message..."
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            rows={3}
          />
        </div>

        {/* Lien à copier */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Lien de la publication
          </label>
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={postUrl}
              readOnly
              className="flex-1 p-3 bg-gray-50 border border-gray-300 rounded-lg text-sm text-gray-600"
            />
            <Button
              onClick={handleCopyLink}
              variant={copied ? 'success' : 'outline'}
              size="sm"
              className="flex items-center"
            >
              {copied ? (
                <>
                  <span className="mr-2">✓</span>
                  Copié
                </>
              ) : (
                <>
                  <Copy size={16} className="mr-2" />
                  Copier
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            Fermer
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ShareModal;
