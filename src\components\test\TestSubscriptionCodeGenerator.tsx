import React from 'react';
import SubscriptionCodeGenerator from '../business/SubscriptionCodeGenerator';

const TestSubscriptionCodeGenerator: React.FC = () => {
  const handleCodeGenerated = (code: string) => {
    console.log('Code généré dans le test:', code);
    alert(`Code généré avec succès: ${code}`);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Test - Générateur de Code d'Abonnement
          </h1>
          <p className="text-gray-600">
            Testez la génération de codes de validation pour les abonnements
          </p>
        </div>

        <SubscriptionCodeGenerator
          businessId="test_business_123"
          businessName="Entreprise de Test"
          onCodeGenerated={handleCodeGenerated}
        />

        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Instructions de test</h2>
          <div className="space-y-2 text-sm text-gray-600">
            <p>1. <strong>Sélectionnez un plan</strong> en cliquant sur l'une des cartes</p>
            <p>2. <strong>Le code sera généré automatiquement</strong> après la sélection</p>
            <p>3. <strong>Observez les étapes</strong> en haut de la page qui se mettent à jour</p>
            <p>4. <strong>Le code apparaîtra</strong> dans une zone verte avec les instructions</p>
            <p>5. <strong>Vous pouvez copier le code</strong> en cliquant sur l'icône de copie</p>
            <p>6. <strong>Générez un nouveau code</strong> si nécessaire avec le bouton correspondant</p>
          </div>
        </div>

        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-800 mb-2">Codes d'exemple attendus</h3>
          <div className="text-sm text-blue-700 space-y-1">
            <p>• <strong>Essai gratuit (0 F CFA):</strong> 0000-0001, 0000-0002, etc.</p>
            <p>• <strong>Plan mensuel (25,000 F CFA):</strong> 0250-0001, 0250-0002, etc.</p>
            <p>• <strong>Plan annuel (240,000 F CFA):</strong> 2400-0001, 2400-0002, etc.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestSubscriptionCodeGenerator;
