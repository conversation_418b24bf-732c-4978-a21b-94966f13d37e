import React from 'react';

interface ChartData {
  name: string;
  value: number;
  color: string;
  icon?: string;
}

interface BarChartProps {
  data: ChartData[];
  title?: string;
  height?: number;
}

interface PieChartProps {
  data: ChartData[];
  title?: string;
  size?: number;
}

interface LineChartProps {
  data: any[];
  title?: string;
  height?: number;
}

// Composant BarChart personnalisé
export const CustomBarChart: React.FC<BarChartProps> = ({ data, title, height = 300 }) => {
  const maxValue = Math.max(...data.map(d => d.value));
  
  return (
    <div className="w-full">
      {title && <h4 className="text-lg font-semibold text-gray-900 mb-4">{title}</h4>}
      <div className="space-y-3" style={{ height }}>
        {data.map((item, index) => (
          <div key={index} className="flex items-center space-x-3">
            <div className="flex items-center space-x-2 w-32">
              {item.icon && <span className="text-lg">{item.icon}</span>}
              <span className="text-sm font-medium text-gray-700 truncate">{item.name}</span>
            </div>
            <div className="flex-1 flex items-center space-x-2">
              <div className="flex-1 bg-gray-200 rounded-full h-6 relative overflow-hidden">
                <div
                  className="h-full rounded-full transition-all duration-500 ease-out flex items-center justify-end pr-2"
                  style={{
                    backgroundColor: item.color,
                    width: `${Math.max(5, (item.value / maxValue) * 100)}%`
                  }}
                >
                  <span className="text-white text-xs font-bold">{item.value}</span>
                </div>
              </div>
              <span className="text-sm font-bold text-gray-900 w-8 text-right">{item.value}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Composant PieChart personnalisé (Donut Chart)
export const CustomPieChart: React.FC<PieChartProps> = ({ data, title, size = 200 }) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  let currentAngle = 0;
  
  const radius = size / 2 - 20;
  const innerRadius = radius * 0.6;
  const centerX = size / 2;
  const centerY = size / 2;

  const createPath = (startAngle: number, endAngle: number, outerRadius: number, innerRadius: number) => {
    const startAngleRad = (startAngle * Math.PI) / 180;
    const endAngleRad = (endAngle * Math.PI) / 180;
    
    const x1 = centerX + outerRadius * Math.cos(startAngleRad);
    const y1 = centerY + outerRadius * Math.sin(startAngleRad);
    const x2 = centerX + outerRadius * Math.cos(endAngleRad);
    const y2 = centerY + outerRadius * Math.sin(endAngleRad);
    
    const x3 = centerX + innerRadius * Math.cos(endAngleRad);
    const y3 = centerY + innerRadius * Math.sin(endAngleRad);
    const x4 = centerX + innerRadius * Math.cos(startAngleRad);
    const y4 = centerY + innerRadius * Math.sin(startAngleRad);
    
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
    
    return `M ${x1} ${y1} A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2} L ${x3} ${y3} A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4} Z`;
  };

  return (
    <div className="w-full flex flex-col items-center">
      {title && <h4 className="text-lg font-semibold text-gray-900 mb-4">{title}</h4>}
      <div className="relative">
        <svg width={size} height={size} className="transform -rotate-90">
          {data.map((item, index) => {
            const percentage = (item.value / total) * 100;
            const angle = (percentage / 100) * 360;
            const path = createPath(currentAngle, currentAngle + angle, radius, innerRadius);
            currentAngle += angle;
            
            return (
              <path
                key={index}
                d={path}
                fill={item.color}
                className="transition-all duration-300 hover:opacity-80"
              />
            );
          })}
        </svg>
        
        {/* Centre du donut avec total */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{total}</div>
            <div className="text-sm text-gray-600">Total</div>
          </div>
        </div>
      </div>
      
      {/* Légende */}
      <div className="mt-4 grid grid-cols-1 gap-2">
        {data.map((item, index) => (
          <div key={index} className="flex items-center space-x-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: item.color }}
            ></div>
            <span className="text-sm text-gray-700">{item.name}</span>
            <span className="text-sm font-medium text-gray-900">
              {item.value} ({((item.value / total) * 100).toFixed(1)}%)
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

// Composant LineChart personnalisé
export const CustomLineChart: React.FC<LineChartProps> = ({ data, title, height = 300 }) => {
  if (!data || data.length === 0) {
    return (
      <div className="w-full h-64 flex items-center justify-center bg-gray-50 rounded-lg">
        <p className="text-gray-500">Aucune donnée disponible</p>
      </div>
    );
  }

  const width = 600;
  const padding = 40;
  const chartWidth = width - 2 * padding;
  const chartHeight = height - 2 * padding;

  // Trouver les valeurs min/max pour l'échelle
  const allValues = data.flatMap(d => [d.coupDeCoeur, d.coupDeGueule, d.engagement]);
  const maxValue = Math.max(...allValues);
  const minValue = Math.min(...allValues);

  // Créer les points pour les lignes
  const createPoints = (values: number[]) => {
    return values.map((value, index) => {
      const x = padding + (index / (values.length - 1)) * chartWidth;
      const y = padding + chartHeight - ((value - minValue) / (maxValue - minValue)) * chartHeight;
      return `${x},${y}`;
    }).join(' ');
  };

  const coupDeCoeurPoints = createPoints(data.map(d => d.coupDeCoeur));
  const coupDeGueulePoints = createPoints(data.map(d => d.coupDeGueule));
  const engagementPoints = createPoints(data.map(d => d.engagement / 10)); // Échelle réduite pour l'engagement

  return (
    <div className="w-full">
      {title && <h4 className="text-lg font-semibold text-gray-900 mb-4">{title}</h4>}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <svg width={width} height={height} className="w-full">
          {/* Grille de fond */}
          <defs>
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width={width} height={height} fill="url(#grid)" />
          
          {/* Lignes de données */}
          <polyline
            points={coupDeCoeurPoints}
            fill="none"
            stroke="#10B981"
            strokeWidth="3"
            className="transition-all duration-300"
          />
          <polyline
            points={coupDeGueulePoints}
            fill="none"
            stroke="#EF4444"
            strokeWidth="3"
            className="transition-all duration-300"
          />
          <polyline
            points={engagementPoints}
            fill="none"
            stroke="#8B5CF6"
            strokeWidth="3"
            strokeDasharray="5,5"
            className="transition-all duration-300"
          />
          
          {/* Points de données */}
          {data.map((_, index) => {
            const x = padding + (index / (data.length - 1)) * chartWidth;
            return (
              <g key={index}>
                <circle
                  cx={x}
                  cy={padding + chartHeight - ((data[index].coupDeCoeur - minValue) / (maxValue - minValue)) * chartHeight}
                  r="4"
                  fill="#10B981"
                  className="hover:r-6 transition-all duration-200"
                />
                <circle
                  cx={x}
                  cy={padding + chartHeight - ((data[index].coupDeGueule - minValue) / (maxValue - minValue)) * chartHeight}
                  r="4"
                  fill="#EF4444"
                  className="hover:r-6 transition-all duration-200"
                />
                <circle
                  cx={x}
                  cy={padding + chartHeight - ((data[index].engagement / 10 - minValue) / (maxValue - minValue)) * chartHeight}
                  r="4"
                  fill="#8B5CF6"
                  className="hover:r-6 transition-all duration-200"
                />
              </g>
            );
          })}
          
          {/* Axes */}
          <line x1={padding} y1={padding} x2={padding} y2={height - padding} stroke="#6B7280" strokeWidth="2"/>
          <line x1={padding} y1={height - padding} x2={width - padding} y2={height - padding} stroke="#6B7280" strokeWidth="2"/>
        </svg>
        
        {/* Légende */}
        <div className="mt-4 flex justify-center space-x-6">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-0.5 bg-green-500"></div>
            <span className="text-sm text-gray-700">Coups de Cœur</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-0.5 bg-red-500"></div>
            <span className="text-sm text-gray-700">Coups de Gueule</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-0.5 bg-purple-500 border-dashed border-t-2"></div>
            <span className="text-sm text-gray-700">Engagement</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Composant de gauge pour la satisfaction
export const SatisfactionGauge: React.FC<{ value: number; size?: number }> = ({ value, size = 200 }) => {
  const radius = size / 2 - 20;
  const circumference = Math.PI * radius; // Demi-cercle
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (value / 100) * circumference;
  
  const getColor = (value: number) => {
    if (value >= 80) return '#10B981'; // Vert
    if (value >= 60) return '#F59E0B'; // Jaune
    if (value >= 40) return '#EF4444'; // Rouge
    return '#6B7280'; // Gris
  };

  return (
    <div className="flex flex-col items-center">
      <div className="relative">
        <svg width={size} height={size / 2 + 40} className="transform rotate-0">
          {/* Fond du gauge */}
          <path
            d={`M 20 ${size / 2} A ${radius} ${radius} 0 0 1 ${size - 20} ${size / 2}`}
            fill="none"
            stroke="#E5E7EB"
            strokeWidth="12"
            strokeLinecap="round"
          />
          {/* Progression du gauge */}
          <path
            d={`M 20 ${size / 2} A ${radius} ${radius} 0 0 1 ${size - 20} ${size / 2}`}
            fill="none"
            stroke={getColor(value)}
            strokeWidth="12"
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            className="transition-all duration-1000 ease-out"
          />
        </svg>
        
        {/* Valeur au centre */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center mt-4">
            <div className="text-3xl font-bold text-gray-900">{value.toFixed(1)}%</div>
            <div className="text-sm text-gray-600">Satisfaction</div>
          </div>
        </div>
      </div>
      
      {/* Échelle */}
      <div className="flex justify-between w-full mt-2 text-xs text-gray-500">
        <span>0%</span>
        <span>50%</span>
        <span>100%</span>
      </div>
    </div>
  );
};
