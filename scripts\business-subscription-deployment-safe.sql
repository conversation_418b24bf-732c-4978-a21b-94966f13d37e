-- DÉPLOIEMENT SÉCURISÉ DES ABONNEMENTS ENTREPRISE CUSTOMEROOM
-- Version qui gère les contraintes existantes
-- Exécutez ce script dans Supabase SQL Editor

-- =====================================================
-- 1. CRÉER/METTRE À JOUR LES TABLES D'ABONNEMENT
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '💳 Configuration sécurisée des abonnements entreprise...';
    
    -- Table des plans d'abonnement (création seulement si n'existe pas)
    CREATE TABLE IF NOT EXISTS subscription_plans (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        currency VARCHAR(3) NOT NULL DEFAULT 'XOF',
        duration_days INTEGER NOT NULL DEFAULT 30,
        features JSONB NOT NULL DEFAULT '[]',
        is_active BOOLEAN NOT NULL DEFAULT true,
        is_trial BOOLEAN NOT NULL DEFAULT false,
        plan_type VARCHAR(20) NOT NULL DEFAULT 'monthly' CHECK (plan_type IN ('trial', 'monthly', 'yearly')),
        original_price DECIMAL(10,2),
        savings_text VARCHAR(100),
        is_popular BOOLEAN NOT NULL DEFAULT false,
        max_reviews INTEGER,
        max_products INTEGER,
        has_ai_advisor BOOLEAN NOT NULL DEFAULT false,
        has_advanced_analytics BOOLEAN NOT NULL DEFAULT false,
        has_api_access BOOLEAN NOT NULL DEFAULT false,
        has_priority_support BOOLEAN NOT NULL DEFAULT false,
        has_phone_support BOOLEAN NOT NULL DEFAULT false,
        has_dedicated_manager BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Table des abonnements business (création seulement si n'existe pas)
    CREATE TABLE IF NOT EXISTS business_subscriptions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        business_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
        plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(id),
        start_date TIMESTAMP WITH TIME ZONE NOT NULL,
        end_date TIMESTAMP WITH TIME ZONE NOT NULL,
        is_active BOOLEAN NOT NULL DEFAULT true,
        is_trial BOOLEAN NOT NULL DEFAULT false,
        auto_renew BOOLEAN NOT NULL DEFAULT false,
        trial_used BOOLEAN NOT NULL DEFAULT false,
        cancelled_at TIMESTAMP WITH TIME ZONE,
        cancellation_reason TEXT,
        payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
        payment_method VARCHAR(50),
        stripe_subscription_id VARCHAR(100),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Index pour les performances
    CREATE INDEX IF NOT EXISTS idx_business_subscriptions_business_id ON business_subscriptions(business_id);
    CREATE INDEX IF NOT EXISTS idx_business_subscriptions_active ON business_subscriptions(is_active);
    CREATE INDEX IF NOT EXISTS idx_business_subscriptions_trial ON business_subscriptions(is_trial);
    CREATE INDEX IF NOT EXISTS idx_business_subscriptions_end_date ON business_subscriptions(end_date);

    -- Table des codes d'abonnement (nouvelle)
    CREATE TABLE IF NOT EXISTS subscription_codes (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        code VARCHAR(8) NOT NULL UNIQUE,
        business_id UUID NOT NULL,
        business_name VARCHAR(255) NOT NULL,
        plan_type VARCHAR(20) NOT NULL CHECK (plan_type IN ('trial', 'monthly', 'yearly')),
        amount INTEGER NOT NULL DEFAULT 0,
        status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'validated', 'rejected', 'expired')),
        generated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        validated_at TIMESTAMP WITH TIME ZONE,
        validated_by UUID,
        rejection_reason TEXT,
        admin_notes TEXT,
        payment_reference VARCHAR(100),
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        
        -- Contraintes
        CONSTRAINT fk_subscription_codes_business FOREIGN KEY (business_id) REFERENCES profiles(id) ON DELETE CASCADE
    );

    -- Index pour optimiser les requêtes des codes
    CREATE INDEX IF NOT EXISTS idx_subscription_codes_business_id ON subscription_codes(business_id);
    CREATE INDEX IF NOT EXISTS idx_subscription_codes_status ON subscription_codes(status);
    CREATE INDEX IF NOT EXISTS idx_subscription_codes_expires_at ON subscription_codes(expires_at);
    CREATE INDEX IF NOT EXISTS idx_subscription_codes_code ON subscription_codes(code);
    CREATE INDEX IF NOT EXISTS idx_subscription_codes_generated_at ON subscription_codes(generated_at);
    
    RAISE NOTICE '✅ Tables d''abonnement et codes configurées';
    
END $$;

-- =====================================================
-- 2. INSÉRER/METTRE À JOUR LES PLANS D'ABONNEMENT
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📋 Configuration des plans d''abonnement...';
    
    -- Utiliser UPSERT (INSERT ... ON CONFLICT) pour éviter les erreurs
    
    -- Plan Essai Gratuit 7 jours
    INSERT INTO subscription_plans (
        id, name, description, price, currency, duration_days, 
        plan_type, is_trial, is_popular, features,
        has_ai_advisor, has_advanced_analytics, has_api_access, 
        has_priority_support, has_phone_support, has_dedicated_manager
    ) VALUES (
        'trial-7days',
        'Essai Gratuit',
        'Découvrez toutes nos fonctionnalités pendant 7 jours',
        0.00,
        'XOF',
        7,
        'trial',
        true,
        false,
        '[
            "Accès complet à toutes les fonctionnalités",
            "Gestion illimitée des avis clients", 
            "Tableaux de bord interactifs",
            "Notifications en temps réel",
            "Support par email",
            "Aucun engagement"
        ]'::jsonb,
        true, true, false, false, false, false
    ) ON CONFLICT (id) DO UPDATE SET
        name = EXCLUDED.name,
        description = EXCLUDED.description,
        price = EXCLUDED.price,
        features = EXCLUDED.features,
        updated_at = CURRENT_TIMESTAMP;
    
    -- Plan Mensuel
    INSERT INTO subscription_plans (
        id, name, description, price, currency, duration_days,
        plan_type, is_trial, is_popular, features,
        has_ai_advisor, has_advanced_analytics, has_api_access,
        has_priority_support, has_phone_support, has_dedicated_manager
    ) VALUES (
        'monthly-plan',
        'Abonnement Mensuel', 
        'Plan mensuel flexible pour votre entreprise',
        25000.00,
        'XOF',
        30,
        'monthly',
        false,
        false,
        '[
            "Toutes les fonctionnalités incluses",
            "Gestion illimitée des avis",
            "Analytics avancées", 
            "Support prioritaire",
            "Intégrations API",
            "Sauvegarde automatique",
            "Rapports personnalisés"
        ]'::jsonb,
        true, true, true, true, false, false
    ) ON CONFLICT (id) DO UPDATE SET
        name = EXCLUDED.name,
        description = EXCLUDED.description,
        price = EXCLUDED.price,
        features = EXCLUDED.features,
        updated_at = CURRENT_TIMESTAMP;
    
    -- Plan Annuel (avec réduction)
    INSERT INTO subscription_plans (
        id, name, description, price, original_price, currency, duration_days,
        plan_type, is_trial, is_popular, savings_text, features,
        has_ai_advisor, has_advanced_analytics, has_api_access,
        has_priority_support, has_phone_support, has_dedicated_manager
    ) VALUES (
        'yearly-plan',
        'Abonnement Annuel',
        'Économisez 20% avec notre plan annuel', 
        240000.00,
        300000.00,
        'XOF',
        365,
        'yearly',
        false,
        true,
        'Économisez 60 000 F CFA',
        '[
            "Toutes les fonctionnalités Premium",
            "Gestion illimitée des avis",
            "Analytics avancées + IA",
            "Support prioritaire 24/7", 
            "Intégrations API complètes",
            "Sauvegarde automatique",
            "Rapports personnalisés",
            "Conseiller dédié",
            "Formation personnalisée"
        ]'::jsonb,
        true, true, true, true, true, true
    ) ON CONFLICT (id) DO UPDATE SET
        name = EXCLUDED.name,
        description = EXCLUDED.description,
        price = EXCLUDED.price,
        original_price = EXCLUDED.original_price,
        savings_text = EXCLUDED.savings_text,
        features = EXCLUDED.features,
        updated_at = CURRENT_TIMESTAMP;
    
    RAISE NOTICE '✅ Plans d''abonnement configurés avec UPSERT';
    
END $$;

-- =====================================================
-- 3. CRÉER LES FONCTIONS DE GESTION DES ABONNEMENTS
-- =====================================================

-- Fonction pour vérifier si un utilisateur a déjà utilisé l'essai
CREATE OR REPLACE FUNCTION has_used_trial(p_business_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    trial_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO trial_count
    FROM business_subscriptions
    WHERE business_id = p_business_id AND is_trial = true;
    
    RETURN trial_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour obtenir l'abonnement actuel d'une entreprise
CREATE OR REPLACE FUNCTION get_current_subscription(p_business_id UUID)
RETURNS TABLE(
    subscription_id UUID,
    plan_id VARCHAR(50),
    plan_name VARCHAR(100),
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN,
    is_trial BOOLEAN,
    days_remaining INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bs.id,
        bs.plan_id,
        sp.name,
        bs.start_date,
        bs.end_date,
        bs.is_active,
        bs.is_trial,
        GREATEST(0, EXTRACT(DAY FROM (bs.end_date - CURRENT_TIMESTAMP))::INTEGER)
    FROM business_subscriptions bs
    JOIN subscription_plans sp ON bs.plan_id = sp.id
    WHERE bs.business_id = p_business_id 
    AND bs.is_active = true
    ORDER BY bs.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. MESSAGE DE SUCCÈS
-- =====================================================

DO $$
DECLARE
    total_plans integer;
    total_codes integer;
BEGIN
    SELECT COUNT(*) INTO total_plans FROM subscription_plans;
    SELECT COUNT(*) INTO total_codes FROM subscription_codes;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '🎉 DÉPLOIEMENT SÉCURISÉ TERMINÉ AVEC SUCCÈS !';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '💳 CONFIGURATION CRÉÉE:';
    RAISE NOTICE '   📋 Plans d''abonnement: %', total_plans;
    RAISE NOTICE '   🔐 Codes d''abonnement: %', total_codes;
    RAISE NOTICE '';
    RAISE NOTICE '✅ Tables créées sans erreur de contrainte';
    RAISE NOTICE '✅ Plans configurés avec UPSERT';
    RAISE NOTICE '✅ Fonctions SQL opérationnelles';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 SYSTÈME PRÊT POUR LES TESTS !';
    RAISE NOTICE '';
END $$;
