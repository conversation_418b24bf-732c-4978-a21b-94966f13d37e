import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { IBusinessUser } from '../../types';
import { X, Upload } from 'lucide-react';
import '../../styles/EditBusinessProfile.css';

interface EditBusinessProfileFormProps {
  businessData: IBusinessUser;
  onClose: () => void;
  onUpdate: (updatedData: IBusinessUser) => void;
}

const EditBusinessProfileForm: React.FC<EditBusinessProfileFormProps> = ({
  businessData,
  onClose,
  onUpdate
}) => {
  const [formData, setFormData] = useState({
    businessName: businessData.businessName || '',
    businessDescription: businessData.businessDescription || '',
    businessCategory: businessData.businessCategory || '',
    website: businessData.website || '',
    phone: businessData.phone || '',
    address: businessData.address || '',
    city: businessData.city || '',
    country: businessData.country || ''
  });
  
  const [profilePicture, setProfilePicture] = useState<File | null>(null);
  const [coverPhoto, setCoverPhoto] = useState<File | null>(null);
  const [profilePicturePreview, setProfilePicturePreview] = useState<string>(businessData.profilePicture || '');
  const [coverPhotoPreview, setCoverPhotoPreview] = useState<string>(businessData.coverPhotoUrl || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Effet pour scroll vers le haut quand le formulaire s'ouvre
  useEffect(() => {
    // Scroll vers le haut de la page
    window.scrollTo({ top: 0, behavior: 'smooth' });
    // Empêcher le scroll du body quand la modal est ouverte
    document.body.style.overflow = 'hidden';

    // Cleanup function pour restaurer le scroll
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, type: 'profile' | 'cover') => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validation de la taille et du type de fichier
    if (file.size > 5 * 1024 * 1024) { // 5MB max
      setError(`L'image est trop volumineuse. Taille maximum: 5MB`);
      return;
    }

    if (!file.type.startsWith('image/')) {
      setError(`Seules les images sont acceptées`);
      return;
    }

    if (type === 'profile') {
      setProfilePicture(file);
      setProfilePicturePreview(URL.createObjectURL(file));
    } else {
      setCoverPhoto(file);
      setCoverPhotoPreview(URL.createObjectURL(file));
    }
  };

  const uploadFile = async (file: File, path: string): Promise<string> => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
    const filePath = `${path}/${fileName}`;

    const { error: uploadError } = await supabase.storage
      .from('profile-images')
      .upload(filePath, file);

    if (uploadError) {
      throw new Error(`Erreur lors du téléchargement: ${uploadError.message}`);
    }
    const { data } = supabase.storage.from('profile-images').getPublicUrl(filePath);
    return data.publicUrl;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      let profilePictureUrl = businessData.profilePicture;
      let coverPhotoUrl = businessData.coverPhotoUrl;

      // Upload new profile picture if changed
      if (profilePicture) {
        profilePictureUrl = await uploadFile(profilePicture, 'avatars');
      }

      // Upload new cover photo if changed
      if (coverPhoto) {
        coverPhotoUrl = await uploadFile(coverPhoto, 'covers');
      }

      // Update profile in Supabase
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          city: formData.city,
          country: formData.country,
          profile_picture: profilePictureUrl,
          cover_photo_url: coverPhotoUrl
        })
        .eq('id', businessData.id);

      if (profileError) throw profileError;

      // Simuler la mise à jour des données business (dans une vraie application, cela irait dans la table business_profiles)
      // Dans notre cas, nous mettons à jour l'état local

      const updatedBusinessData: IBusinessUser = {
        ...businessData,
        businessName: formData.businessName,
        businessDescription: formData.businessDescription,
        businessCategory: formData.businessCategory,
        website: formData.website,
        phone: formData.phone,
        address: formData.address,
        city: formData.city,
        country: formData.country,
        profilePicture: profilePictureUrl,
        coverPhotoUrl: coverPhotoUrl
      };

      onUpdate(updatedBusinessData);
      setSuccess('Profil mis à jour avec succès!');
      
      // Fermer le formulaire après un court délai
      setTimeout(() => {
        onClose();
      }, 2000);

    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil:', error);
      setError('Une erreur est survenue lors de la mise à jour du profil.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="edit-profile-overlay">
      <div className="edit-profile-modal">
        <div className="edit-profile-header">
          <h2>Modifier le profil de l'entreprise</h2>
          <button className="close-button" onClick={onClose}>
            <X size={24} />
          </button>
        </div>

        {error && <div className="error-message">{error}</div>}
        {success && <div className="success-message">{success}</div>}

        <form onSubmit={handleSubmit} className="edit-profile-form">
          <div className="form-images">
            <div className="form-image-container">
              <label>Photo de profil</label>
              <div 
                className="profile-image-preview" 
                style={{ backgroundImage: `url(${profilePicturePreview || '/default-business-avatar.png'})` }}
              >
                <input 
                  type="file" 
                  id="profilePicture" 
                  onChange={(e) => handleFileChange(e, 'profile')} 
                  accept="image/*"
                />
                <label htmlFor="profilePicture" className="upload-icon">
                  <Upload size={20} />
                </label>
              </div>
            </div>

            <div className="form-image-container">
              <label>Photo de couverture</label>
              <div 
                className="cover-image-preview" 
                style={{ backgroundImage: `url(${coverPhotoPreview || '/default-business-cover.jpg'})` }}
              >
                <input 
                  type="file" 
                  id="coverPhoto" 
                  onChange={(e) => handleFileChange(e, 'cover')} 
                  accept="image/*"
                />
                <label htmlFor="coverPhoto" className="upload-icon">
                  <Upload size={20} />
                </label>
              </div>
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="businessName">Nom de l'entreprise</label>
              <input
                type="text"
                id="businessName"
                name="businessName"
                value={formData.businessName}
                onChange={handleChange}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="businessCategory">Catégorie</label>
              <select
                id="businessCategory"
                name="businessCategory"
                value={formData.businessCategory}
                onChange={handleChange}
              >
                <option value="">Sélectionner une catégorie</option>
                <option value="Beauté et Bien-être">Beauté et Bien-être</option>
                <option value="Alimentation">Alimentation</option>
                <option value="Mode et Accessoires">Mode et Accessoires</option>
                <option value="Technologie">Technologie</option>
                <option value="Services">Services</option>
                <option value="Artisanat">Artisanat</option>
                <option value="Autre">Autre</option>
              </select>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="businessDescription">Description de l'entreprise</label>
            <textarea
              id="businessDescription"
              name="businessDescription"
              value={formData.businessDescription}
              onChange={handleChange}
              rows={4}
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="website">Site web</label>
              <input
                type="url"
                id="website"
                name="website"
                value={formData.website}
                onChange={handleChange}
                placeholder="https://www.example.com"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="phone">Téléphone</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="+33 1 23 45 67 89"
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="address">Adresse</label>
            <input
              type="text"
              id="address"
              name="address"
              value={formData.address}
              onChange={handleChange}
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="city">Ville</label>
              <input
                type="text"
                id="city"
                name="city"
                value={formData.city}
                onChange={handleChange}
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="country">Pays</label>
              <input
                type="text"
                id="country"
                name="country"
                value={formData.country}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="form-actions">
            <button 
              type="button" 
              className="cancel-button" 
              onClick={onClose}
              disabled={isSubmitting}
            >
              Annuler
            </button>
            <button 
              type="submit" 
              className="save-button" 
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Enregistrement...' : 'Enregistrer les modifications'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditBusinessProfileForm;
