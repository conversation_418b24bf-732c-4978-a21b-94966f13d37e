.business-reviews-container {
  width: 100%;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.reviews-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.reviews-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.reviews-subtitle {
  font-size: 1rem;
  color: #6b7280;
}

.reviews-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  border: 1px solid #e5e7eb;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
}

.reviews-filters {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-input {
  width: 100%;
  padding: 10px 10px 10px 40px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.filter-container {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-dropdown, .sort-dropdown {
  position: relative;
  display: inline-block;
}

.filter-button, .sort-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.filter-button:hover, .sort-button:hover {
  background-color: #f3f4f6;
}

.filter-dropdown-content, .sort-dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  min-width: 180px;
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 4px;
}

.filter-dropdown:hover .filter-dropdown-content,
.sort-dropdown:hover .sort-dropdown-content {
  display: block;
}

.filter-option, .sort-option {
  padding: 8px 16px;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
  transition: background-color 0.2s;
}

.filter-option:hover, .sort-option:hover {
  background-color: #f9fafb;
}

.filter-option.selected, .sort-option.selected {
  background-color: #eff6ff;
  color: #3b82f6;
  font-weight: 500;
}

.no-reviews {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: #6b7280;
  text-align: center;
}

.no-reviews svg {
  margin-bottom: 16px;
  color: #d1d5db;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.review-card {
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.review-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.review-card.pending-response {
  border-left: 4px solid #f59e0b;
}

.review-header {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
  background-color: #f9fafb;
}

.review-user-info, .review-product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar, .user-avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-avatar-placeholder {
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.25rem;
}

.user-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.review-date, .response-date {
  font-size: 0.75rem;
  color: #6b7280;
}

.product-image {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  object-fit: cover;
}

.product-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.product-category {
  font-size: 0.75rem;
  color: #6b7280;
}

.review-content {
  padding: 16px;
}

.review-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.stars {
  display: flex;
  gap: 2px;
}

.star-filled {
  color: #f59e0b;
}

.star-empty {
  color: #d1d5db;
}

.verified-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: #10b981;
  background-color: #ecfdf5;
  padding: 2px 6px;
  border-radius: 9999px;
}

.review-comment {
  margin: 0 0 12px 0;
  font-size: 0.875rem;
  color: #4b5563;
  line-height: 1.5;
}

.review-feedback {
  display: flex;
  gap: 16px;
}

.feedback-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: #6b7280;
}

.business-response {
  padding: 16px;
  background-color: #f3f4f6;
  border-top: 1px solid #e5e7eb;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.business-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.response-text {
  margin: 0;
  font-size: 0.875rem;
  color: #4b5563;
  line-height: 1.5;
}

.review-actions {
  padding: 12px 16px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
}

.respond-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background-color: #eff6ff;
  color: #3b82f6;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.respond-button:hover {
  background-color: #dbeafe;
}

/* Modal de réponse */
.review-response-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.response-modal-content {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.response-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.response-modal-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.response-target {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 4px 0 0 0;
}

.response-target strong {
  color: #3b82f6;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  transition: color 0.2s;
}

.close-button:hover {
  color: #1f2937;
}

.response-modal-body {
  padding: 16px;
}

.original-review {
  background-color: #f9fafb;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.review-product-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.review-user-name {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 8px;
}

.response-form {
  margin-bottom: 16px;
}

.response-author-info {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
}

.author-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.business-name-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 12px;
  background-color: #10b981;
  color: white;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
}

.response-form label {
  display: block;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8px;
}

.response-form textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 120px;
  transition: border-color 0.2s;
}

.response-form textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.response-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid #e5e7eb;
}

.cancel-button {
  padding: 8px 16px;
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button:hover {
  background-color: #e5e7eb;
}

.submit-button {
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover {
  background-color: #2563eb;
}

.submit-button:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .review-header {
    flex-direction: column;
    gap: 12px;
  }

  .reviews-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-container {
    flex-direction: column;
  }
}
