import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { IUser, UserRole, UserStatus, IBusinessUser, BusinessStatus } from '../types/index.ts';
import { UserStatusUpdateService } from '../services/userStatusUpdateService';

// Define ProfileUpdateData type before AuthContextType
export type ProfileUpdateData = {
  username?: string;
  city?: string;
  country?: string;
  age?: string | number;
  gender?: string;
  profilePicture?: string;
  profilePictureFile?: File | null;
  coverPhotoUrl?: string;
  coverPhotoFile?: File | null;
};

interface AuthContextType {
  currentUser: IUser | IBusinessUser | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: Partial<IUser>, password: string) => Promise<void>;
  registerBusiness: (businessData: Partial<IBusinessUser>, password: string) => Promise<void>;
  logout: () => Promise<void>;
  updateUserProfile: (userId: string, updates: ProfileUpdateData) => Promise<void>;
  updateUserStatsAndStatus: (action: 'new_post' | 'new_comment') => Promise<void>;
  checkAndUpdateUserStatus: (action?: 'post_created' | 'comment_created' | 'like_received' | 'follow_received' | 'profile_updated') => Promise<void>;
  followUser: (targetUserId: string) => Promise<void>; // Added
  unfollowUser: (targetUserId: string) => Promise<void>; // Added
  simulateBusinessUser: () => void; // Added for testing
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType>({
  currentUser: null,
  loading: true,
  error: null,
  login: async () => {},
  register: async () => {},
  registerBusiness: async () => {},
  logout: async () => {},
  updateUserProfile: async () => {},
  updateUserStatsAndStatus: async () => {},
  checkAndUpdateUserStatus: async () => {},
  followUser: async () => {}, // Added
  unfollowUser: async () => {}, // Added
  simulateBusinessUser: () => {}, // Added for testing
  isAuthenticated: false,
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<IUser | IBusinessUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mode test pour simuler un utilisateur business
  const simulateBusinessUser = () => {
    const mockBusinessUser: IBusinessUser = {
      id: 'business-test-123',
      username: 'Dexima',
      email: '<EMAIL>',
      role: UserRole.BUSINESS,
      status: UserStatus.MEMBER,
      businessName: 'Dexima',
      businessDescription: 'Entreprise spécialisée dans les produits de beauté et bien-être naturels',
      businessStatus: BusinessStatus.VERIFIED,
      profilePicture: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=150&h=150&fit=crop&crop=face',
      coverPhotoUrl: 'https://images.unsplash.com/photo-**********-8c89e6adf883?w=800&h=300&fit=crop',
      createdAt: new Date('2023-01-15T00:00:00Z'),
      followers_count: 1250,
      following_count: 89,
      post_count: 45,
      comment_count: 123,
      recommendations_count: 67,
      salesCount: 234,
      wallet: 125000,
      catalog: []
    };
    setCurrentUser(mockBusinessUser);
    setLoading(false);
  };

  useEffect(() => {
    // Check active session
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (session) {
        fetchUserProfile(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      if (session) {
        fetchUserProfile(session.user.id);
      } else {
        setCurrentUser(null);
      }
    });

    // Ajouter un abonnement en temps réel aux changements de profil
    const profileSubscription = supabase
      .channel('profile-changes')
      .on('postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'profiles', filter: `id=eq.${currentUser?.id}` },
        (payload) => {
          if (payload.new && currentUser) {
            setCurrentUser(prev => {
              if (!prev) return null;
              return {
                ...prev,
                ...payload.new,
                profilePicture: payload.new.profile_picture,
                coverPhotoUrl: payload.new.cover_photo_url,
                status: payload.new.status as UserStatus
              } as IUser | IBusinessUser;
            });
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
      profileSubscription.unsubscribe();
    };
  }, [currentUser?.id]);

  const fetchUserProfile = async (userId: string) => {
    setLoading(true);
    try {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError) {
        if (profileError.code === 'PGRST116') { // Resource not found
          setCurrentUser(null);
          console.warn(`Profile not found for user ID: ${userId}. This might happen if the profile creation trigger failed or user was deleted.`);
          return;
        }
        throw profileError;
      }

      if (profile.role === UserRole.BUSINESS) {
        const { data: businessProfile, error: businessError } = await supabase
          .from('business_profiles')
          .select('*')
          .eq('id', userId)
          .single();

        if (businessError && businessError.code !== 'PGRST116') throw businessError;

        const businessUserData: IBusinessUser = {
          id: profile.id,
          username: profile.username,
          email: profile.email, // Assuming email is in profiles table, or fetch from auth.user if needed
          profilePicture: profile.profile_picture,
          role: profile.role as UserRole,
          status: profile.status as UserStatus || UserStatus.NEWBIE, // Default to NEWBIE
          followers_count: profile.followers_count || 0,
          following_count: profile.following_count || 0,
          createdAt: new Date(profile.created_at),
          coverPhotoUrl: profile.cover_photo_url,
          country: profile.country,
          city: profile.city,
          age: profile.age,
          gender: profile.gender,
          post_count: profile.post_count || 0,
          comment_count: profile.comment_count || 0,
          recommendations_count: profile.recommendations_count || 0,
          businessName: businessProfile?.business_name || '',
          businessStatus: businessProfile?.business_status,
          businessDescription: businessProfile?.business_description || '',
          businessCategory: businessProfile?.business_category,
          website: businessProfile?.website,
          phone: businessProfile?.phone,

          catalog: [],
        };

        // Fetch products for the business
        console.log('Fetching products for businessId:', userId); // Add this line
        const { data: productsData, error: productsError } = await supabase
          .from('products')
          .select('*')
          .eq('business_id', userId);

        if (productsError) {
          console.error('Error fetching products:', productsError);
        } else {
          businessUserData.catalog = productsData || [];
        }

        setCurrentUser(businessUserData);
      } else {
        const userData: IUser = {
          id: profile.id,
          username: profile.username,
          email: profile.email, // Assuming email is in profiles table
          profilePicture: profile.profile_picture,
          role: profile.role as UserRole,
          status: profile.status as UserStatus || UserStatus.NEWBIE, // Default to NEWBIE
          followers_count: profile.followers_count || 0,
          following_count: profile.following_count || 0,
          createdAt: new Date(profile.created_at),
          coverPhotoUrl: profile.cover_photo_url,
          country: profile.country,
          city: profile.city,
          age: profile.age,
          gender: profile.gender,
          post_count: profile.post_count || 0,
          comment_count: profile.comment_count || 0,
          recommendations_count: profile.recommendations_count || 0,
        };
        setCurrentUser(userData);
      }
    } catch (err) {
      console.error('Error fetching user profile:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch user profile');
      setCurrentUser(null); // Ensure currentUser is null on error
    } finally {
      setLoading(false);
    }
  };

  const validateUsername = (username: string): string => {
    const sanitizedUsername = username.trim().replace(/\s+/g, '_');
    if (sanitizedUsername.length < 3) throw new Error('Username must be at least 3 characters long');
    if (sanitizedUsername.length > 30) throw new Error('Username must be less than 30 characters long');
    if (!/^[a-zA-Z0-9_-]+$/.test(sanitizedUsername)) throw new Error('Username can only contain letters, numbers, underscores, and hyphens');
    return sanitizedUsername;
  };

  const login = async (email: string, password: string) => {
    // ... (login logic remains the same)
    try {
      setLoading(true);
      setError(null);
      const { error } = await supabase.auth.signInWithPassword({ email, password });
      if (error) throw error;
      // onAuthStateChange will trigger fetchUserProfile
    } catch (err) {
      console.error('Error logging in:', err);
      setError(err instanceof Error ? err.message : 'Failed to login');
      throw err;
    } finally {
      setLoading(false); // setLoading(false) was missing here
    }
  };

  const register = async (userData: Partial<IUser>, password: string) => {
    // ... (register logic remains the same)
    try {
      setLoading(true);
      setError(null);
      if (!userData.email || !userData.username) throw new Error('Email and username are required');
      const sanitizedUsername = validateUsername(userData.username);
      const { data: existingUser, error: checkError } = await supabase.from('profiles').select('username').eq('username', sanitizedUsername).maybeSingle();
      if (checkError) throw new Error('Error checking username availability');
      if (existingUser) throw new Error('Username is already taken');
      const { data: authData, error: signUpError } = await supabase.auth.signUp({
        email: userData.email, password, options: { data: { username: sanitizedUsername, role: UserRole.STANDARD } }
      });
      if (signUpError) throw signUpError;
      if (!authData.user) throw new Error('No user returned from sign up');
      // Profile creation handled by trigger, onAuthStateChange will fetch it.
    } catch (err) {
      console.error('Error registering:', err);
      setError(err instanceof Error ? err.message : 'Failed to register');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const registerBusiness = async (businessData: Partial<IBusinessUser>, password: string) => {
    // ... (registerBusiness logic remains the same)
    try {
      setLoading(true);
      setError(null);
      if (!businessData.email || !businessData.username || !businessData.businessName) throw new Error('Email, username, and business name are required');
      const sanitizedUsername = validateUsername(businessData.username);
      const { data: existingUser } = await supabase.from('profiles').select('username').eq('username', sanitizedUsername).maybeSingle();
      if (existingUser) throw new Error('Username is already taken');
      const sanitizedBusinessName = businessData.businessName.trim();
      const { data: existingBusiness } = await supabase.from('business_profiles').select('business_name').eq('business_name', sanitizedBusinessName).maybeSingle();
      if (existingBusiness) throw new Error('Business name is already taken');
      const { data: authData, error: signUpError } = await supabase.auth.signUp({
        email: businessData.email, password, options: { data: {
          username: sanitizedUsername, role: UserRole.BUSINESS,
          businessName: sanitizedBusinessName, businessDescription: businessData.businessDescription?.trim() || ''
        }}
      });
      if (signUpError) throw signUpError;
      if (!authData.user) throw new Error('No user returned from sign up');
    } catch (err) {
      console.error('Error registering business:', err);
      setError(err instanceof Error ? err.message : 'Failed to register business');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const uploadProfileImage = async (userId: string, file: File, bucket: string, folder: string): Promise<string> => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${folder}/${userId}/${Date.now()}.${fileExt}`;
    const { error: uploadError } = await supabase.storage.from(bucket).upload(fileName, file, { upsert: true });
    if (uploadError) {
      console.error(`Error uploading to ${bucket}:`, JSON.stringify(uploadError, null, 2));
      throw new Error(`Failed to upload ${folder === 'avatars' ? 'profile picture' : 'cover photo'}.`);
    }
    const { data: { publicUrl } } = supabase.storage.from(bucket).getPublicUrl(fileName);
    if (!publicUrl) throw new Error('Failed to get public URL for uploaded image.');
    return publicUrl;
  };

  // Utility: Only allow valid profile fields in update
  const PROFILE_FIELDS = [
    'username', 'country', 'city', 'age', 'gender', 'profile_picture', 'cover_photo_url', 'status', 'profile_completed_at'
  ];
  function filterProfileFields(updates: { [key: string]: any }) {
    const filtered: { [key: string]: any } = {};
    for (const key of PROFILE_FIELDS) {
      if (updates[key] !== undefined) filtered[key] = updates[key];
    }
    return filtered;
  }

  const updateUserProfile = async (userId: string, updates: ProfileUpdateData) => {
    if (!currentUser) {
      setError("Utilisateur non authentifié pour la mise à jour du profil.");
      return;
    }
    try {
      setLoading(true);
      setError(null);

      const dbUpdates: { [key: string]: any } = {};
      let newAvatarUrl = currentUser.profilePicture;
      let newCoverUrl = currentUser.coverPhotoUrl;

      if (updates.username) dbUpdates.username = updates.username;
      if (updates.country) dbUpdates.country = updates.country;
      if (updates.city) dbUpdates.city = updates.city;
      if (updates.age !== undefined) {
        // Convertir en nombre ou null si vide
        const ageValue = updates.age === '' || updates.age === null
          ? null
          : typeof updates.age === 'string'
            ? parseInt(updates.age)
            : updates.age;
        if (ageValue === null || (ageValue >= 13 && ageValue <= 120)) {
          dbUpdates.age = ageValue;
        }
      }
      if (updates.gender !== undefined) {
        // Valider le genre
        const genderValue = updates.gender === '' ? null : updates.gender;
        if (genderValue === null || ['Homme', 'Femme'].includes(genderValue)) {
          dbUpdates.gender = genderValue;
        }
      }

      if (updates.profilePictureFile) {
        newAvatarUrl = await uploadProfileImage(userId, updates.profilePictureFile, 'profile-images', 'avatars');
        dbUpdates.profile_picture = newAvatarUrl;
      } else if (updates.profilePicture !== undefined) {
        dbUpdates.profile_picture = updates.profilePicture;
        newAvatarUrl = updates.profilePicture;
      }

      if (updates.coverPhotoFile) {
        newCoverUrl = await uploadProfileImage(userId, updates.coverPhotoFile, 'profile-images', 'covers');
        dbUpdates.cover_photo_url = newCoverUrl;
      } else if (updates.coverPhotoUrl !== undefined) {
        dbUpdates.cover_photo_url = updates.coverPhotoUrl;
        newCoverUrl = updates.coverPhotoUrl;
      }

      // Check for status update from NEWBIE to MEMBER
      let newStatus = currentUser.status;
      let profileCompletedDateSet = (currentUser as any).profile_completed_at;

      if (currentUser.status === UserStatus.NEWBIE) {
        const isProfileNowComplete =
          (newAvatarUrl || dbUpdates.profile_picture) && // Check against potentially updated URL or existing one if not changed
          (newCoverUrl || dbUpdates.cover_photo_url) &&
          (updates.country || currentUser.country) &&
          (updates.city || currentUser.city);

        if (isProfileNowComplete) {
          newStatus = UserStatus.MEMBER;
          dbUpdates.status = newStatus;
          if (!profileCompletedDateSet) { // Only set if not already completed
            dbUpdates.profile_completed_at = new Date().toISOString();
            profileCompletedDateSet = dbUpdates.profile_completed_at;
          }
        }
      }

      // Filter to only valid fields before sending to Supabase
      const filteredDbUpdates = filterProfileFields(dbUpdates);

      if (Object.keys(filteredDbUpdates).length > 0) {
        const { error: updateError, data: updatedProfile } = await supabase
          .from('profiles')
          .update(filteredDbUpdates)
          .eq('id', userId)
          .select()
          .single();

        if (updateError) {
          console.error('Supabase profile update error:', updateError);
          throw updateError;
        }

        // Utiliser les données retournées par Supabase plutôt que de construire manuellement
        if (updatedProfile) {
          setCurrentUser(prev => {
            if (!prev) return null;
            const updatedUser = {
              ...prev,
              ...updatedProfile,
              profilePicture: updatedProfile.profile_picture,
              coverPhotoUrl: updatedProfile.cover_photo_url,
              status: updatedProfile.status as UserStatus
            };
            return updatedUser as IUser | IBusinessUser;
          });
        }
      }

      alert('Profil mis à jour avec succès !');
      if (newStatus !== currentUser.status && newStatus === UserStatus.MEMBER) {
        alert(`Félicitations ! Votre profil est complet. Votre statut est maintenant : ${newStatus}.`);
      }

      // Déclencher la vérification automatique du statut après mise à jour du profil
      await checkAndUpdateUserStatus('profile_updated');

    } catch (err) {
      console.error('Error updating user profile:', err);
      setError(err instanceof Error ? err.message : 'Failed to update profile');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateUserStatsAndStatus = async (action: 'new_post' | 'new_comment') => {
    if (!currentUser) return;

    const updates: { post_count?: number, comment_count?: number, status?: UserStatus } = {};
    let newStatus = currentUser.status;
    let newPostCount = (currentUser as any).post_count || 0; // Assuming post_count is on profile
    let newCommentCount = (currentUser as any).comment_count || 0;

    if (action === 'new_post') {
      newPostCount += 1;
      updates.post_count = newPostCount;
    } else if (action === 'new_comment') {
      newCommentCount += 1;
      updates.comment_count = newCommentCount;
    }

    // Check for MEMBER to CONTRIBUTOR transition
    if (currentUser.status === UserStatus.MEMBER && (newPostCount >= 1 || newCommentCount >= 1)) {
      newStatus = UserStatus.CONTRIBUTOR;
      updates.status = newStatus;
    }
    // TODO: Add logic for DISCOVERER, INFLUENCER, LEADER based on more complex metrics
    // This would likely involve fetching more data (likes on posts, comment likes, etc.)
    // and is better suited for backend logic or more dedicated functions.

    if (Object.keys(updates).length > 0) {
      try {
        const { error: updateError } = await supabase
          .from('profiles')
          .update(updates)
          .eq('id', currentUser.id);

        if (updateError) {
          console.error('Error updating user stats/status:', updateError);
          // Handle error, maybe revert optimistic update if any
          return;
        }

        // Update local currentUser state
        setCurrentUser(prev => {
          if (!prev) return null;
          const updatedUser = {
            ...prev,
            status: newStatus, // Always update status
            post_count: newPostCount, // Update counts
            comment_count: newCommentCount,
          };
          // Preserve business specific fields if it's a business user
          if (prev.role === UserRole.BUSINESS && 'businessName' in prev) {
            // This casting might be needed if IBusinessUser has these count fields directly
            (updatedUser as IBusinessUser).post_count = newPostCount;
            (updatedUser as IBusinessUser).comment_count = newCommentCount;
          }
          return updatedUser as IUser | IBusinessUser;
        });

        if (newStatus !== currentUser.status && newStatus === UserStatus.CONTRIBUTOR) {
          alert(`Bravo ! Vous êtes maintenant un ${newStatus}. Continuez à contribuer !`);
        }

      } catch (err) {
        console.error('Failed to update user stats/status:', err);
      }
    }
  };

  const checkAndUpdateUserStatus = async (action?: 'post_created' | 'comment_created' | 'like_received' | 'follow_received' | 'profile_updated') => {
    if (!currentUser) return;

    try {
      const result = await UserStatusUpdateService.updateUserStatusIfNeeded(currentUser.id);

      if (result.updated && result.newStatus) {
        // Mettre à jour l'utilisateur local
        setCurrentUser(prev => {
          if (!prev) return null;
          return {
            ...prev,
            status: result.newStatus!
          } as IUser | IBusinessUser;
        });

        // Afficher une notification de félicitations
        if (result.message) {
          alert(result.message);
        }

        console.log(`🎉 Statut mis à jour: ${result.oldStatus} → ${result.newStatus}`);
      }

      // Si une action spécifique est fournie, déclencher la vérification automatique
      if (action) {
        await UserStatusUpdateService.checkAndUpdateAfterAction(currentUser.id, action);
      }
    } catch (error) {
      console.error('Erreur lors de la vérification du statut:', error);
    }
  };

  const followUser = async (targetUserId: string) => {
    if (!currentUser || currentUser.id === targetUserId) return;

    try {
      // Mise à jour optimiste
      setCurrentUser(prev => {
        if (!prev) return null;
        return {
          ...prev,
          following_count: (prev.following_count || 0) + 1
        } as IUser | IBusinessUser;
      });

      const { error } = await supabase
        .from('followers')
        .insert({ follower_id: currentUser.id, following_id: targetUserId });

      if (error) {
        if (error.code === '23505') {
          console.warn('Attempted to follow a user already being followed.');
          // Annuler la mise à jour optimiste
          setCurrentUser(prev => {
            if (!prev) return null;
            return {
              ...prev,
              following_count: (prev.following_count || 0) - 1
            } as IUser | IBusinessUser;
          });
        } else {
          throw error;
        }
      }

      // Récupérer les données à jour
      fetchUserProfile(currentUser.id);

      // Déclencher la vérification automatique du statut pour l'utilisateur qui suit
      await checkAndUpdateUserStatus('follow_received');
    } catch (err) {
      console.error("Error following user:", err);
      // Annuler la mise à jour optimiste en cas d'erreur
      setCurrentUser(prev => {
        if (!prev) return null;
        return {
          ...prev,
          following_count: (prev.following_count || 0) - 1
        } as IUser | IBusinessUser;
      });
    }
  };

  const unfollowUser = async (targetUserId: string) => {
    if (!currentUser || currentUser.id === targetUserId) return;

    try {
      const { error } = await supabase
        .from('followers')
        .delete()
        .eq('follower_id', currentUser.id)
        .eq('following_id', targetUserId);

      if (error) throw error;

      // Optimistically update count or re-fetch profile for updated counts
      fetchUserProfile(currentUser.id);

    } catch (err) {
      console.error("Error unfollowing user:", err);
      setError(err instanceof Error ? err.message : 'Failed to unfollow user');
      // Consider reverting optimistic updates if any were made
    }
  };


  const logout = async () => {
    try {
      setLoading(true);
      setError(null);
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      setCurrentUser(null);
    } catch (err) {
      console.error('Error logging out:', err);
      setError(err instanceof Error ? err.message : 'Failed to logout');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{
      currentUser,
      loading,
      error,
      login,
      register,
      registerBusiness,
      logout,
      updateUserProfile,
      updateUserStatsAndStatus,
      checkAndUpdateUserStatus,
      followUser, // Added
      unfollowUser, // Added
      simulateBusinessUser, // Added for testing
      isAuthenticated: !!currentUser
    }}>
      {children}
    </AuthContext.Provider>
  );
};




