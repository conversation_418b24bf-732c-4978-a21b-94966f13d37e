import React, { useState } from 'react';
import { TrendingUp, DollarSign, Package, Users, Calendar, Download, BarChart3, PieChart } from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';

interface SaleData {
  id: string;
  productName: string;
  customerName: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  saleDate: string;
  paymentMethod: 'cash' | 'card' | 'mobile' | 'bank_transfer';
  status: 'completed' | 'pending' | 'refunded';
}

interface BusinessSalesManagerProps {
  businessId: string;
}

const BusinessSalesManager: React.FC<BusinessSalesManagerProps> = ({ businessId }) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month' | 'year'>('month');
  const [viewMode, setViewMode] = useState<'overview' | 'details'>('overview');

  // Données fictives pour la démonstration
  const salesData: SaleData[] = [
    {
      id: '1',
      productName: 'Crème hydratante visage',
      customerName: 'Aminata Traoré',
      quantity: 2,
      unitPrice: 15000,
      totalAmount: 30000,
      saleDate: '2025-01-15T10:30:00Z',
      paymentMethod: 'mobile',
      status: 'completed'
    },
    {
      id: '2',
      productName: 'Sérum anti-âge',
      customerName: 'Kouassi Jean',
      quantity: 1,
      unitPrice: 25000,
      totalAmount: 25000,
      saleDate: '2025-01-14T14:20:00Z',
      paymentMethod: 'card',
      status: 'completed'
    },
    {
      id: '3',
      productName: 'Parfum femme 50ml',
      customerName: 'Fatou Diallo',
      quantity: 1,
      unitPrice: 35000,
      totalAmount: 35000,
      saleDate: '2025-01-13T09:15:00Z',
      paymentMethod: 'cash',
      status: 'completed'
    },
    {
      id: '4',
      productName: 'Kit de maquillage complet',
      customerName: 'Mariam Koné',
      quantity: 1,
      unitPrice: 45000,
      totalAmount: 45000,
      saleDate: '2025-01-12T16:45:00Z',
      paymentMethod: 'mobile',
      status: 'completed'
    },
    {
      id: '5',
      productName: 'Crème hydratante visage',
      customerName: 'Awa Sanogo',
      quantity: 3,
      unitPrice: 15000,
      totalAmount: 45000,
      saleDate: '2025-01-11T11:20:00Z',
      paymentMethod: 'bank_transfer',
      status: 'completed'
    }
  ];

  const totalRevenue = salesData.reduce((sum, sale) => sum + sale.totalAmount, 0);
  const totalSales = salesData.length;
  const totalProducts = salesData.reduce((sum, sale) => sum + sale.quantity, 0);
  const uniqueCustomers = new Set(salesData.map(sale => sale.customerName)).size;

  const getPaymentMethodLabel = (method: SaleData['paymentMethod']) => {
    switch (method) {
      case 'cash':
        return 'Espèces';
      case 'card':
        return 'Carte bancaire';
      case 'mobile':
        return 'Mobile Money';
      case 'bank_transfer':
        return 'Virement bancaire';
      default:
        return 'Inconnu';
    }
  };

  const getStatusColor = (status: SaleData['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'refunded':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: SaleData['status']) => {
    switch (status) {
      case 'completed':
        return 'Terminée';
      case 'pending':
        return 'En attente';
      case 'refunded':
        return 'Remboursée';
      default:
        return 'Inconnu';
    }
  };

  const exportSalesData = () => {
    // Ici, vous implémenteriez l'export des données de vente
    console.log('Export des données de vente...');
  };

  const periods = [
    { key: 'today', label: 'Aujourd\'hui' },
    { key: 'week', label: 'Cette semaine' },
    { key: 'month', label: 'Ce mois' },
    { key: 'year', label: 'Cette année' },
  ];

  // Calcul des données par méthode de paiement
  const paymentMethodStats = salesData.reduce((acc, sale) => {
    acc[sale.paymentMethod] = (acc[sale.paymentMethod] || 0) + sale.totalAmount;
    return acc;
  }, {} as Record<string, number>);

  // Top produits vendus
  const productStats = salesData.reduce((acc, sale) => {
    if (!acc[sale.productName]) {
      acc[sale.productName] = { quantity: 0, revenue: 0 };
    }
    acc[sale.productName].quantity += sale.quantity;
    acc[sale.productName].revenue += sale.totalAmount;
    return acc;
  }, {} as Record<string, { quantity: number; revenue: number }>);

  const topProducts = Object.entries(productStats)
    .sort((a, b) => b[1].revenue - a[1].revenue)
    .slice(0, 5);

  return (
    <div className="space-y-6">
      {/* En-tête avec sélecteur de période */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gestion des ventes</h2>
          <p className="text-gray-600">Analysez vos performances de vente</p>
        </div>
        <div className="flex space-x-2">
          {periods.map((period) => (
            <button
              key={period.key}
              onClick={() => setSelectedPeriod(period.key as any)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedPeriod === period.key
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {period.label}
            </button>
          ))}
        </div>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardBody>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <DollarSign size={20} />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">{totalRevenue.toLocaleString()} F CFA</h3>
                <p className="text-sm text-gray-500">Chiffre d'affaires</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <TrendingUp size={20} />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">{totalSales}</h3>
                <p className="text-sm text-gray-500">Ventes réalisées</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                <Package size={20} />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">{totalProducts}</h3>
                <p className="text-sm text-gray-500">Produits vendus</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-orange-100 text-orange-600">
                <Users size={20} />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">{uniqueCustomers}</h3>
                <p className="text-sm text-gray-500">Clients uniques</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Boutons d'action */}
      <div className="flex flex-wrap gap-4">
        <Button
          variant={viewMode === 'overview' ? 'primary' : 'outline'}
          leftIcon={<BarChart3 size={16} />}
          onClick={() => setViewMode('overview')}
        >
          Vue d'ensemble
        </Button>
        <Button
          variant={viewMode === 'details' ? 'primary' : 'outline'}
          leftIcon={<Calendar size={16} />}
          onClick={() => setViewMode('details')}
        >
          Détails des ventes
        </Button>
        <Button
          variant="outline"
          leftIcon={<Download size={16} />}
          onClick={exportSalesData}
        >
          Exporter (PDF)
        </Button>
      </div>

      {viewMode === 'overview' ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top produits */}
          <Card>
            <CardBody>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <PieChart size={20} className="mr-2" />
                Top produits vendus
              </h3>
              <div className="space-y-3">
                {topProducts.map(([productName, stats], index) => (
                  <div key={productName} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 text-xs font-medium flex items-center justify-center mr-3">
                        {index + 1}
                      </span>
                      <div>
                        <p className="font-medium text-gray-900">{productName}</p>
                        <p className="text-sm text-gray-500">{stats.quantity} unités vendues</p>
                      </div>
                    </div>
                    <span className="font-medium text-gray-900">{stats.revenue.toLocaleString()} F CFA</span>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>

          {/* Répartition par méthode de paiement */}
          <Card>
            <CardBody>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <BarChart3 size={20} className="mr-2" />
                Méthodes de paiement
              </h3>
              <div className="space-y-3">
                {Object.entries(paymentMethodStats).map(([method, amount]) => (
                  <div key={method} className="flex items-center justify-between">
                    <span className="text-gray-700">{getPaymentMethodLabel(method as SaleData['paymentMethod'])}</span>
                    <span className="font-medium text-gray-900">{amount.toLocaleString()} F CFA</span>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </div>
      ) : (
        /* Détails des ventes */
        <Card>
          <CardBody>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Historique détaillé des ventes</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Produit
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Client
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantité
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Prix unitaire
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Paiement
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {salesData.map((sale) => (
                    <tr key={sale.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{sale.productName}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{sale.customerName}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{sale.quantity}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{sale.unitPrice.toLocaleString()} F CFA</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{sale.totalAmount.toLocaleString()} F CFA</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {new Date(sale.saleDate).toLocaleDateString('fr-FR')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{getPaymentMethodLabel(sale.paymentMethod)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(sale.status)}`}>
                          {getStatusLabel(sale.status)}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default BusinessSalesManager;
