import React, { useState, useEffect } from 'react';
import { Check<PERSON><PERSON><PERSON>, XCircle, Clock, AlertTriangle, RefreshCw } from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';

interface ImageStatus {
  url: string;
  type: 'offer' | 'logo';
  businessName: string;
  status: 'loading' | 'success' | 'error' | 'pending';
  loadTime?: number;
  error?: string;
}

const OfferImageDiagnostic: React.FC = () => {
  const [imageStatuses, setImageStatuses] = useState<ImageStatus[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  // Images des offres à tester
  const testImages = [
    {
      url: 'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=600&h=300&fit=crop&crop=center',
      type: 'offer' as const,
      businessName: 'Olgane Cosmétiques'
    },
    {
      url: 'https://images.unsplash.com/photo-1570194065650-d99fb4bedf0a?w=600&h=300&fit=crop&crop=center',
      type: 'offer' as const,
      businessName: 'NatureSkin'
    },
    {
      url: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=600&h=300&fit=crop&crop=center',
      type: 'offer' as const,
      businessName: 'Bio Savons'
    },
    {
      url: 'https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=600&h=300&fit=crop&crop=center',
      type: 'offer' as const,
      businessName: 'Capillus'
    },
    {
      url: 'https://images.unsplash.com/photo-1541643600914-78b084683601?w=600&h=300&fit=crop&crop=center',
      type: 'offer' as const,
      businessName: 'Parfums de France'
    },
    {
      url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=600&h=300&fit=crop&crop=center',
      type: 'offer' as const,
      businessName: 'TechStore'
    },
    {
      url: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600&h=300&fit=crop&crop=center',
      type: 'offer' as const,
      businessName: 'FashionHub'
    },
    {
      url: 'https://images.unsplash.com/photo-1512389142860-9c449e58a543?w=600&h=300&fit=crop&crop=center',
      type: 'offer' as const,
      businessName: 'HomeDecor'
    },
    // Logos
    {
      url: 'https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?w=50&h=50&fit=crop&crop=center',
      type: 'logo' as const,
      businessName: 'Olgane Cosmétiques'
    },
    {
      url: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=50&h=50&fit=crop&crop=center',
      type: 'logo' as const,
      businessName: 'NatureSkin'
    }
  ];

  const runDiagnostic = () => {
    setIsRunning(true);
    
    // Initialiser les statuts
    setImageStatuses(testImages.map(img => ({ 
      ...img, 
      status: 'pending' as const 
    })));

    // Tester chaque image
    testImages.forEach((imageData, index) => {
      testImage(imageData, index);
    });
  };

  const testImage = (imageData: typeof testImages[0], index: number) => {
    const startTime = Date.now();
    
    // Mettre à jour le statut à "loading"
    setImageStatuses(prev => 
      prev.map((item, i) => 
        i === index ? { ...item, status: 'loading' } : item
      )
    );

    const img = new Image();
    
    img.onload = () => {
      const loadTime = Date.now() - startTime;
      setImageStatuses(prev => 
        prev.map((item, i) => 
          i === index ? { ...item, status: 'success', loadTime } : item
        )
      );
      
      // Vérifier si c'est la dernière image
      if (index === testImages.length - 1) {
        setTimeout(() => setIsRunning(false), 500);
      }
    };

    img.onerror = () => {
      setImageStatuses(prev => 
        prev.map((item, i) => 
          i === index ? { 
            ...item, 
            status: 'error', 
            error: 'Impossible de charger l\'image'
          } : item
        )
      );
      
      // Vérifier si c'est la dernière image
      if (index === testImages.length - 1) {
        setTimeout(() => setIsRunning(false), 500);
      }
    };

    img.src = imageData.url;
  };

  useEffect(() => {
    // Lancer le diagnostic automatiquement au montage
    runDiagnostic();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'loading':
        return <Clock className="text-yellow-500 animate-spin" size={16} />;
      case 'success':
        return <CheckCircle className="text-green-500" size={16} />;
      case 'error':
        return <XCircle className="text-red-500" size={16} />;
      default:
        return <AlertTriangle className="text-gray-400" size={16} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'loading':
        return 'border-yellow-200 bg-yellow-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const successCount = imageStatuses.filter(img => img.status === 'success').length;
  const errorCount = imageStatuses.filter(img => img.status === 'error').length;
  const loadingCount = imageStatuses.filter(img => img.status === 'loading').length;
  const averageLoadTime = imageStatuses
    .filter(img => img.loadTime)
    .reduce((acc, img) => acc + (img.loadTime || 0), 0) / 
    imageStatuses.filter(img => img.loadTime).length || 0;

  const offerImages = imageStatuses.filter(img => img.type === 'offer');
  const logoImages = imageStatuses.filter(img => img.type === 'logo');

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">🔍 Diagnostic Images Offres</h3>
          <Button
            onClick={runDiagnostic}
            disabled={isRunning}
            size="sm"
            leftIcon={isRunning ? <RefreshCw className="animate-spin" size={14} /> : <RefreshCw size={14} />}
          >
            {isRunning ? 'Test en cours...' : 'Relancer'}
          </Button>
        </div>
      </CardHeader>
      <CardBody>
        {/* Résumé global */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="text-2xl font-bold text-green-600">{successCount}</div>
            <div className="text-sm text-green-700">Succès</div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
            <div className="text-2xl font-bold text-red-600">{errorCount}</div>
            <div className="text-sm text-red-700">Erreurs</div>
          </div>
          <div className="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="text-2xl font-bold text-yellow-600">{loadingCount}</div>
            <div className="text-sm text-yellow-700">En cours</div>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="text-2xl font-bold text-blue-600">
              {averageLoadTime ? Math.round(averageLoadTime) : 0}ms
            </div>
            <div className="text-sm text-blue-700">Temps moyen</div>
          </div>
        </div>

        {/* Images des offres */}
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 mb-3">Images des offres (600x300)</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {offerImages.map((image, index) => (
              <div 
                key={index}
                className={`flex items-center justify-between p-3 rounded-lg border ${getStatusColor(image.status)}`}
              >
                <div className="flex items-center space-x-3">
                  {getStatusIcon(image.status)}
                  <div>
                    <div className="font-medium text-sm">{image.businessName}</div>
                    <div className="text-xs text-gray-500">Offre principale</div>
                  </div>
                </div>
                <div className="text-right">
                  {image.loadTime && (
                    <div className="text-xs text-gray-600">{image.loadTime}ms</div>
                  )}
                  {image.error && (
                    <div className="text-xs text-red-600">Erreur</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Logos */}
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 mb-3">Logos business (50x50)</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {logoImages.map((image, index) => (
              <div 
                key={index}
                className={`flex items-center justify-between p-3 rounded-lg border ${getStatusColor(image.status)}`}
              >
                <div className="flex items-center space-x-3">
                  {getStatusIcon(image.status)}
                  <div>
                    <div className="font-medium text-sm">{image.businessName}</div>
                    <div className="text-xs text-gray-500">Logo</div>
                  </div>
                </div>
                <div className="text-right">
                  {image.loadTime && (
                    <div className="text-xs text-gray-600">{image.loadTime}ms</div>
                  )}
                  {image.error && (
                    <div className="text-xs text-red-600">Erreur</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Conseils */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h5 className="font-medium text-blue-900 mb-2">💡 Informations</h5>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Toutes les images proviennent d'Unsplash avec des paramètres optimisés</li>
            <li>• Les images d'offres sont redimensionnées à 600x300 pixels</li>
            <li>• Les logos sont redimensionnés à 50x50 pixels</li>
            <li>• Un système de fallback est en place en cas d'erreur de chargement</li>
          </ul>
        </div>
      </CardBody>
    </Card>
  );
};

export default OfferImageDiagnostic;
