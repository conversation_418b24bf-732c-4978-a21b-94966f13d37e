import React, { useState } from 'react';
import { ShoppingCart, MessageCircle, Star, Heart, Eye, Zap, Package, TrendingUp, Crown, Shield, Award } from 'lucide-react';
import { IProduct, QualityBadge } from '../../types';
import Button from '../ui/Button';
import { DEFAULT_IMAGES_SIZED } from '../../constants/defaultImages';

interface ProductCardProps {
  product: IProduct;
  onNegotiateClick: (product: IProduct) => void;
  onBuyClick: (product: IProduct) => void;
  onPreviewClick?: (product: IProduct) => void;
}

const QualityBadgeConfig: Record<QualityBadge, {
  colors: string;
  icon: React.ReactNode;
  label: string;
  gradient: string;
}> = {
  [QualityBadge.STANDARD]: {
    colors: 'bg-gray-100 text-gray-700 border-gray-200',
    icon: <Package size={12} />,
    label: 'Standard',
    gradient: 'from-gray-100 to-gray-200'
  },
  [QualityBadge.VERIFIED]: {
    colors: 'bg-blue-100 text-blue-700 border-blue-200',
    icon: <Shield size={12} />,
    label: 'Vérifié',
    gradient: 'from-blue-100 to-blue-200'
  },
  [QualityBadge.PREMIUM]: {
    colors: 'bg-purple-100 text-purple-700 border-purple-200',
    icon: <Crown size={12} />,
    label: 'Premium',
    gradient: 'from-purple-100 to-purple-200'
  },
  [QualityBadge.EXCELLENT]: {
    colors: 'bg-green-100 text-green-700 border-green-200',
    icon: <Award size={12} />,
    label: 'Excellent',
    gradient: 'from-green-100 to-green-200'
  },
  [QualityBadge.TOP_QUALITY]: {
    colors: 'bg-indigo-100 text-indigo-700 border-indigo-200',
    icon: <TrendingUp size={12} />,
    label: 'Top Qualité',
    gradient: 'from-indigo-100 to-indigo-200'
  },
  [QualityBadge.GOLD]: {
    colors: 'bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900 border-yellow-300',
    icon: <Crown size={12} />,
    label: 'Gold',
    gradient: 'from-yellow-400 to-yellow-500'
  },
  [QualityBadge.SILVER]: {
    colors: 'bg-gradient-to-r from-gray-300 to-gray-400 text-gray-800 border-gray-300',
    icon: <Award size={12} />,
    label: 'Silver',
    gradient: 'from-gray-300 to-gray-400'
  },
  [QualityBadge.BRONZE]: {
    colors: 'bg-gradient-to-r from-orange-400 to-orange-500 text-orange-900 border-orange-300',
    icon: <Award size={12} />,
    label: 'Bronze',
    gradient: 'from-orange-400 to-orange-500'
  },
  [QualityBadge.NONE]: {
    colors: 'hidden',
    icon: null,
    label: '',
    gradient: ''
  }
};

const ProductCard: React.FC<ProductCardProps> = ({ product, onNegotiateClick, onBuyClick, onPreviewClick }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    e.currentTarget.src = DEFAULT_IMAGES_SIZED.PRODUCT_MEDIUM;
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price);
  };

  const getRatingStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} size={14} className="text-yellow-400 fill-current" />);
    }

    if (hasHalfStar) {
      stars.push(<Star key="half" size={14} className="text-yellow-400 fill-current opacity-50" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<Star key={`empty-${i}`} size={14} className="text-gray-300" />);
    }

    return stars;
  };

  const badgeConfig = QualityBadgeConfig[product.qualityBadge || QualityBadge.NONE];

  return (
    <div
      className="group relative bg-white rounded-2xl shadow-sm hover:shadow-xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-gray-200"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Image Container */}
      <div className="relative overflow-hidden">
        <div className="aspect-w-16 aspect-h-12">
          <img
            src={product.images[0] || DEFAULT_IMAGES_SIZED.PRODUCT_MEDIUM}
            alt={product.name}
            className={`w-full h-64 object-cover transition-all duration-700 ${
              isHovered ? 'scale-110' : 'scale-100'
            }`}
            onError={handleImageError}
          />
        </div>

        {/* Overlay Gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Top Badges */}
        <div className="absolute top-3 left-3 right-3 flex justify-between items-start">
          {/* Negotiable Badge */}
          {product.negotiable && (
            <div className="flex items-center bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs font-semibold px-3 py-1.5 rounded-full shadow-lg backdrop-blur-sm">
              <Zap size={12} className="mr-1" />
              Négociable
            </div>
          )}

          {/* Quality Badge */}
          {product.qualityBadge && product.qualityBadge !== QualityBadge.NONE && (
            <div className={`flex items-center text-xs font-semibold px-3 py-1.5 rounded-full border shadow-lg backdrop-blur-sm ${badgeConfig.colors}`}>
              {badgeConfig.icon}
              <span className="ml-1">{badgeConfig.label}</span>
            </div>
          )}
        </div>

        {/* Favorite Button */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            setIsFavorite(!isFavorite);
          }}
          className={`absolute top-3 right-3 p-2 rounded-full backdrop-blur-sm transition-all duration-300 ${
            isFavorite
              ? 'bg-red-500 text-white shadow-lg'
              : 'bg-white/80 text-gray-600 hover:bg-white hover:text-red-500'
          } ${product.qualityBadge && product.qualityBadge !== QualityBadge.NONE ? 'mt-12' : ''}`}
        >
          <Heart size={16} className={isFavorite ? 'fill-current' : ''} />
        </button>

        {/* Quick Actions (visible on hover) */}
        <div className={`absolute bottom-3 left-3 right-3 flex gap-2 transition-all duration-300 ${
          isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onPreviewClick?.(product);
            }}
            className="flex-1 bg-white/90 backdrop-blur-sm text-gray-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-white hover:text-blue-600 transition-colors flex items-center justify-center shadow-lg"
          >
            <Eye size={14} className="mr-1" />
            Aperçu rapide
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-5">
        {/* Category */}
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded-full">
            {product.category}
          </span>
          {product.stock !== undefined && product.stock < 10 && product.stock > 0 && (
            <span className="text-xs font-medium text-red-600 bg-red-50 px-2 py-1 rounded-full">
              Plus que {product.stock}
            </span>
          )}
          {product.stock === 0 && (
            <span className="text-xs font-medium text-red-700 bg-red-100 px-2 py-1 rounded-full">
              Épuisé
            </span>
          )}
        </div>

        {/* Product Name */}
        <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2 leading-tight" title={product.name}>
          {product.name}
        </h3>

        {/* Description */}
        <p className="text-sm text-gray-600 mb-4 line-clamp-2 leading-relaxed">
          {product.description}
        </p>

        {/* Rating */}
        <div className="flex items-center mb-4">
          <div className="flex items-center mr-2">
            {getRatingStars(product.averageRating)}
          </div>
          <span className="text-sm font-medium text-gray-700">
            {product.averageRating.toFixed(1)}
          </span>
          <span className="text-xs text-gray-500 ml-1">
            (24 avis)
          </span>
        </div>

        {/* Price */}
        <div className="flex items-center justify-between mb-5">
          <div>
            <div className="text-2xl font-bold text-gray-900">
              {formatPrice(product.price)}
              <span className="text-lg font-semibold text-gray-600 ml-1">F CFA</span>
            </div>
            {product.negotiable && (
              <div className="text-xs text-green-600 font-medium">
                Prix négociable
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <Button
            variant="primary"
            fullWidth
            leftIcon={<ShoppingCart size={18} />}
            disabled={product.stock === 0}
            onClick={() => onBuyClick(product)}
            className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            Acheter
          </Button>

          {product.negotiable && (
            <Button
              variant="outline"
              leftIcon={<MessageCircle size={18} />}
              onClick={() => onNegotiateClick(product)}
              className="px-4 py-3 border-2 border-gray-200 text-gray-700 hover:border-blue-500 hover:text-blue-600 rounded-xl transition-all duration-300 transform hover:scale-105"
            >
              <span className="hidden sm:inline">Négocier</span>
              <span className="sm:hidden">💬</span>
            </Button>
          )}
        </div>
      </div>

      {/* Hover Effect Border */}
      <div className={`absolute inset-0 rounded-2xl border-2 border-blue-500 transition-opacity duration-300 pointer-events-none ${
        isHovered ? 'opacity-100' : 'opacity-0'
      }`} />
    </div>
  );
};

export default ProductCard;
