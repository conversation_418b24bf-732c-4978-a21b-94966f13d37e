import React, { useState } from 'react';
import { useAdBilling } from '../../context/AdBillingContext';
import { 
  Wallet, 
  CreditCard, 
  Plus, 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown,
  RefreshCw,
  CheckCircle,
  X
} from 'lucide-react';
import Button from '../ui/Button';

interface AdBillingDemoProps {
  isOpen: boolean;
  onClose: () => void;
}

const AdBillingDemo: React.FC<AdBillingDemoProps> = ({ isOpen, onClose }) => {
  const {
    wallet,
    transactions,
    paymentMethods,
    billingAlerts,
    loading,
    error,
    rechargeWallet,
    addPaymentMethod,
    chargeCampaign,
    canAffordCampaign,
    createBillingAlert
  } = useAdBilling();

  const [demoAmount, setDemoAmount] = useState('25000');
  const [demoDescription, setDemoDescription] = useState('Test de facturation');

  if (!isOpen) return null;

  const handleDemoRecharge = async () => {
    try {
      // Ajouter une méthode de paiement de démonstration si aucune n'existe
      if (paymentMethods.length === 0) {
        await addPaymentMethod({
          type: 'mobile_money',
          provider: 'Orange Money',
          accountNumber: '+225 07 XX XX XX XX',
          accountName: 'Compte de démonstration',
          isDefault: true,
          isActive: true
        });
      }

      // Effectuer la recharge
      const paymentMethodId = paymentMethods[0]?.id || 'demo-method';
      await rechargeWallet(parseFloat(demoAmount), paymentMethodId);
      
      alert('Recharge de démonstration effectuée avec succès !');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Erreur lors de la recharge de démonstration');
    }
  };

  const handleDemoCharge = async () => {
    try {
      const campaignId = `DEMO-${Date.now()}`;
      const amount = parseFloat(demoAmount);
      
      const success = await chargeCampaign(campaignId, amount, demoDescription);
      
      if (success) {
        alert('Facturation de démonstration effectuée avec succès !');
      } else {
        alert('Échec de la facturation - Solde insuffisant');
      }
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Erreur lors de la facturation de démonstration');
    }
  };

  const handleCreateAlert = async () => {
    try {
      await createBillingAlert(
        'low_balance',
        'Alerte de démonstration : Votre solde est faible. Pensez à recharger votre compte.',
        10000
      );
      alert('Alerte de démonstration créée !');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Erreur lors de la création de l\'alerte');
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('fr-FR').format(amount);
  };

  return (
    <div className="ad-wallet-overlay">
      <div className="ad-wallet-modal">
        <div className="ad-wallet-header">
          <div className="header-left">
            <Wallet size={24} />
            <h2>Démonstration du système de facturation</h2>
          </div>
          <button className="close-button" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="wallet-content">
          {/* État du wallet */}
          <div className="demo-section">
            <h3>État du portefeuille</h3>
            {wallet ? (
              <div className="wallet-status">
                <div className="status-item">
                  <span className="label">Solde actuel :</span>
                  <span className="value">{formatAmount(wallet.balance)} F CFA</span>
                </div>
                <div className="status-item">
                  <span className="label">Total rechargé :</span>
                  <span className="value">{formatAmount(wallet.totalRecharged)} F CFA</span>
                </div>
                <div className="status-item">
                  <span className="label">Total dépensé :</span>
                  <span className="value">{formatAmount(wallet.totalSpent)} F CFA</span>
                </div>
                <div className="status-item">
                  <span className="label">Statut :</span>
                  <span className={`status status-${wallet.status}`}>{wallet.status}</span>
                </div>
              </div>
            ) : (
              <div className="no-wallet">
                <p>Aucun portefeuille trouvé. Un portefeuille sera créé automatiquement lors de la première utilisation.</p>
              </div>
            )}
          </div>

          {/* Actions de démonstration */}
          <div className="demo-section">
            <h3>Actions de démonstration</h3>
            
            <div className="demo-controls">
              <div className="form-group">
                <label htmlFor="demo-amount">Montant (F CFA)</label>
                <input
                  type="number"
                  id="demo-amount"
                  value={demoAmount}
                  onChange={(e) => setDemoAmount(e.target.value)}
                  min="1000"
                  step="1000"
                />
              </div>

              <div className="form-group">
                <label htmlFor="demo-description">Description</label>
                <input
                  type="text"
                  id="demo-description"
                  value={demoDescription}
                  onChange={(e) => setDemoDescription(e.target.value)}
                  placeholder="Description de la transaction"
                />
              </div>

              <div className="demo-actions">
                <Button
                  onClick={handleDemoRecharge}
                  disabled={loading}
                  className="demo-btn recharge"
                >
                  <Plus size={16} />
                  Recharger {formatAmount(parseFloat(demoAmount) || 0)} F CFA
                </Button>

                <Button
                  onClick={handleDemoCharge}
                  disabled={loading || !canAffordCampaign(parseFloat(demoAmount) || 0)}
                  className="demo-btn charge"
                >
                  <TrendingDown size={16} />
                  Facturer {formatAmount(parseFloat(demoAmount) || 0)} F CFA
                </Button>

                <Button
                  onClick={handleCreateAlert}
                  disabled={loading}
                  variant="outline"
                  className="demo-btn alert"
                >
                  <AlertTriangle size={16} />
                  Créer une alerte
                </Button>
              </div>

              {/* Vérification du solde */}
              {demoAmount && (
                <div className="balance-check">
                  {canAffordCampaign(parseFloat(demoAmount)) ? (
                    <div className="check-success">
                      <CheckCircle size={16} />
                      <span>Solde suffisant pour cette transaction</span>
                    </div>
                  ) : (
                    <div className="check-error">
                      <AlertTriangle size={16} />
                      <span>Solde insuffisant - Rechargez d'abord votre compte</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Alertes actives */}
          {billingAlerts.length > 0 && (
            <div className="demo-section">
              <h3>Alertes actives ({billingAlerts.length})</h3>
              <div className="alerts-list">
                {billingAlerts.map(alert => (
                  <div key={alert.id} className={`alert alert-${alert.type}`}>
                    <AlertTriangle size={16} />
                    <span>{alert.message}</span>
                    <span className="alert-date">
                      {alert.createdAt.toLocaleDateString('fr-FR')}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Transactions récentes */}
          <div className="demo-section">
            <h3>Transactions récentes ({transactions.length})</h3>
            {transactions.length > 0 ? (
              <div className="transactions-list">
                {transactions.slice(0, 5).map(transaction => (
                  <div key={transaction.id} className="transaction-item">
                    <div className="transaction-icon">
                      {transaction.type === 'recharge' ? (
                        <TrendingUp className="text-green-500" size={16} />
                      ) : (
                        <TrendingDown className="text-red-500" size={16} />
                      )}
                    </div>
                    <div className="transaction-details">
                      <p className="transaction-description">{transaction.description}</p>
                      <span className="transaction-date">
                        {transaction.createdAt.toLocaleDateString('fr-FR')}
                      </span>
                    </div>
                    <div className={`transaction-amount ${
                      transaction.type === 'spend' ? 'text-red-600' : 'text-green-600'
                    }`}>
                      {transaction.type === 'spend' ? '-' : '+'}
                      {formatAmount(transaction.amount)} F CFA
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-transactions">
                <p>Aucune transaction pour le moment. Utilisez les boutons ci-dessus pour tester le système.</p>
              </div>
            )}
          </div>

          {/* Méthodes de paiement */}
          <div className="demo-section">
            <h3>Méthodes de paiement ({paymentMethods.length})</h3>
            {paymentMethods.length > 0 ? (
              <div className="payment-methods-list">
                {paymentMethods.map(method => (
                  <div key={method.id} className="payment-method-card">
                    <div className="method-icon">
                      <CreditCard size={20} />
                    </div>
                    <div className="method-details">
                      <h4>{method.provider}</h4>
                      <p>{method.accountNumber}</p>
                      <span className="method-type">{method.type}</span>
                    </div>
                    {method.isDefault && (
                      <span className="default-badge">Par défaut</span>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-methods">
                <p>Aucune méthode de paiement configurée. Une méthode de démonstration sera créée automatiquement.</p>
              </div>
            )}
          </div>

          {/* État de chargement */}
          {loading && (
            <div className="loading-overlay">
              <RefreshCw size={24} className="spinning" />
              <p>Traitement en cours...</p>
            </div>
          )}

          {/* Erreurs */}
          {error && (
            <div className="error-message">
              <AlertTriangle size={16} />
              <span>{error}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdBillingDemo;
