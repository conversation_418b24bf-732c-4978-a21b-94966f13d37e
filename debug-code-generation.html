<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Génération de Codes d'Abonnement</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9fafb;
            color: #111827;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 24px;
            text-align: center;
        }
        .section {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }
        .section h2 {
            margin: 0 0 16px 0;
            color: #1f2937;
            font-size: 1.5rem;
        }
        .problem {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .solution {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .code-block {
            background: #1f2937;
            color: #10b981;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 12px 0;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            align-items: center;
        }
        .checklist li:last-child {
            border-bottom: none;
        }
        .checklist li::before {
            content: "🔍";
            margin-right: 8px;
        }
        .steps {
            counter-reset: step-counter;
            list-style: none;
            padding: 0;
        }
        .steps li {
            counter-increment: step-counter;
            padding: 12px 0;
            border-bottom: 1px solid #e5e7eb;
            position: relative;
            padding-left: 40px;
        }
        .steps li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 12px;
            background: #3b82f6;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .error-box {
            background: #fef2f2;
            border: 2px solid #ef4444;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .error-title {
            color: #dc2626;
            font-weight: 600;
            margin: 0 0 12px 0;
        }
        .fix-box {
            background: #f0fdf4;
            border: 2px solid #10b981;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .fix-title {
            color: #059669;
            font-weight: 600;
            margin: 0 0 12px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- En-tête -->
        <div class="header">
            <h1>🐛 Debug - Problème de Génération de Codes</h1>
            <p>Diagnostic et solutions pour résoudre le problème "je ne vois pas de génération de code"</p>
        </div>

        <!-- Problème identifié -->
        <div class="section">
            <h2>🔍 Problème Identifié</h2>
            <div class="error-box">
                <div class="error-title">❌ Symptôme</div>
                <p>Quand vous cliquez sur "Choisir ce plan", aucun code n'est généré et affiché.</p>
            </div>

            <h3>Causes Possibles :</h3>
            <ul class="checklist">
                <li>Import incorrect du service (casse différente)</li>
                <li>Paramètres incorrects passés au service</li>
                <li>Erreur JavaScript non visible</li>
                <li>Problème de state React</li>
                <li>Service non accessible</li>
            </ul>
        </div>

        <!-- Corrections apportées -->
        <div class="section">
            <h2>🔧 Corrections Apportées</h2>
            
            <div class="fix-box">
                <div class="fix-title">✅ 1. Import Corrigé</div>
                <p><strong>Avant :</strong></p>
                <div class="code-block">import { SubscriptionCodeService } from '../../services/SubscriptionCodeService';</div>
                <p><strong>Après :</strong></p>
                <div class="code-block">import { SubscriptionCodeService } from '../../services/subscriptionCodeService';</div>
            </div>

            <div class="fix-box">
                <div class="fix-title">✅ 2. Paramètres Corrigés</div>
                <p><strong>Avant :</strong></p>
                <div class="code-block">SubscriptionCodeService.createSubscriptionCode({
  businessId: businessInfo.id,
  businessName: businessInfo.name,
  planType: plan.type,
  planName: plan.name,
  amount: plan.price
});</div>
                <p><strong>Après :</strong></p>
                <div class="code-block">SubscriptionCodeService.createSubscriptionCode(
  businessInfo.id,
  businessInfo.name,
  plan.type,
  plan.price
);</div>
            </div>

            <div class="fix-box">
                <div class="fix-title">✅ 3. Propriétés Corrigées</div>
                <p><strong>Problème :</strong> <code>expiresAt</code> n'existe pas</p>
                <p><strong>Solution :</strong> Utiliser <code>expires_at</code></p>
                <div class="code-block">// Avant
new Date(generatedCode.expiresAt)

// Après  
new Date(generatedCode.expires_at)</div>
            </div>
        </div>

        <!-- Étapes de diagnostic -->
        <div class="section">
            <h2>🔍 Étapes de Diagnostic</h2>
            <ol class="steps">
                <li><strong>Ouvrez la console du navigateur</strong> (F12 → Console)</li>
                <li><strong>Cliquez sur "Choisir ce plan"</strong> dans l'onglet Mon abonnement</li>
                <li><strong>Vérifiez les logs</strong> dans la console :
                    <div class="code-block">🧪 Test de génération de code: {planType: "monthly", amount: 25000}
📝 Code d'abonnement généré: {id: "code_...", code: "02500001", ...}</div>
                </li>
                <li><strong>Vérifiez les erreurs</strong> éventuelles en rouge</li>
                <li><strong>Observez l'état du spinner</strong> "Génération du code..."</li>
                <li><strong>Confirmez l'affichage</strong> de la section verte avec le code</li>
            </ol>
        </div>

        <!-- Test manuel -->
        <div class="section">
            <h2>🧪 Test Manuel du Service</h2>
            <p>Pour tester directement le service, ouvrez la console et exécutez :</p>
            <div class="code-block">// Test direct du service
import { SubscriptionCodeService } from './src/services/subscriptionCodeService.js';

const testCode = await SubscriptionCodeService.createSubscriptionCode(
  'test_business_123',
  'Entreprise Test', 
  'monthly',
  25000
);

console.log('Code généré:', testCode);</div>
        </div>

        <!-- Vérifications à faire -->
        <div class="section">
            <h2>✅ Vérifications à Faire</h2>
            <ul class="checklist">
                <li>Le fichier <code>subscriptionCodeService.ts</code> existe dans <code>src/services/</code></li>
                <li>Le service exporte bien <code>SubscriptionCodeService</code></li>
                <li>La méthode <code>createSubscriptionCode</code> est accessible</li>
                <li>Les paramètres sont passés dans le bon ordre</li>
                <li>Le state <code>generatedCode</code> est mis à jour</li>
                <li>La section d'affichage du code est conditionnelle sur <code>generatedCode</code></li>
            </ul>
        </div>

        <!-- Solutions alternatives -->
        <div class="section">
            <h2>🔄 Solutions Alternatives</h2>
            
            <div class="solution">
                <h3>Option 1 : Test avec Mock Direct</h3>
                <p>Si le service ne fonctionne pas, créer un mock temporaire :</p>
                <div class="code-block">const handleChoosePlan = async (planId: string) => {
  setGeneratingCode(planId);
  
  // Mock temporaire pour test
  setTimeout(() => {
    const mockCode = {
      id: 'test_' + Date.now(),
      code: '02500001',
      planName: 'Plan Mensuel',
      amount: 25000,
      expires_at: new Date(Date.now() + 24*60*60*1000).toISOString(),
      status: 'pending'
    };
    
    setGeneratedCode(mockCode);
    setGeneratingCode(null);
    setSuccess('Code généré avec succès !');
  }, 1000);
};</div>
            </div>

            <div class="solution">
                <h3>Option 2 : Logs de Debug</h3>
                <p>Ajouter des logs détaillés pour identifier le problème :</p>
                <div class="code-block">const handleChoosePlan = async (planId: string) => {
  console.log('🎯 Début génération code pour plan:', planId);
  console.log('📋 Business info:', businessInfo);
  console.log('📦 Plan sélectionné:', plan);
  
  try {
    setGeneratingCode(planId);
    console.log('⏳ État loading activé');
    
    const codeData = await SubscriptionCodeService.createSubscriptionCode(...);
    console.log('✅ Code reçu du service:', codeData);
    
    if (codeData) {
      setGeneratedCode(codeData);
      console.log('💾 Code sauvé dans state');
    }
  } catch (error) {
    console.error('❌ Erreur détaillée:', error);
  }
};</div>
            </div>
        </div>

        <!-- Prochaines étapes -->
        <div class="section">
            <h2>🚀 Prochaines Étapes</h2>
            <ol class="steps">
                <li><strong>Testez avec les corrections</strong> appliquées</li>
                <li><strong>Vérifiez la console</strong> pour les logs et erreurs</li>
                <li><strong>Si ça ne marche toujours pas</strong>, utilisez le mock temporaire</li>
                <li><strong>Une fois fonctionnel</strong>, testez avec différents plans</li>
                <li><strong>Vérifiez</strong> que les codes apparaissent dans le dashboard admin</li>
            </ol>
        </div>

        <!-- Contact -->
        <div class="section">
            <h2>💬 Si le Problème Persiste</h2>
            <div class="problem">
                <p><strong>Partagez ces informations :</strong></p>
                <ul>
                    <li>Messages d'erreur dans la console (F12)</li>
                    <li>Logs affichés quand vous cliquez sur "Choisir ce plan"</li>
                    <li>État du spinner (s'affiche-t-il ?)</li>
                    <li>Version du navigateur utilisé</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
