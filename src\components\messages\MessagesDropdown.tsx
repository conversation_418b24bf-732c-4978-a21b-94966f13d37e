import React, { useState, useEffect, useRef } from 'react';
import { MessageSquare, Send, User, Clock, CheckCheck, Check, Search, Settings, MoreHorizontal } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import Avatar from '../ui/Avatar';
import Button from '../ui/Button';

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  content: string;
  timestamp: Date;
  isRead: boolean;
  conversationId: string;
}

interface Conversation {
  id: string;
  participantId: string;
  participantName: string;
  participantAvatar?: string;
  lastMessage: Message;
  unreadCount: number;
  isOnline: boolean;
  lastSeen?: Date;
}

interface MessagesDropdownProps {
  className?: string;
}

const MessagesDropdown: React.FC<MessagesDropdownProps> = ({ className = '' }) => {
  const { currentUser } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Données de démonstration
  const mockConversations: Conversation[] = [
    {
      id: 'conv-1',
      participantId: 'user-1',
      participantName: 'Dexima',
      participantAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
      lastMessage: {
        id: 'msg-1',
        senderId: 'user-1',
        senderName: 'Dexima',
        content: 'Bonjour ! J\'ai une question sur votre produit Olgane...',
        timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
        isRead: false,
        conversationId: 'conv-1'
      },
      unreadCount: 2,
      isOnline: true
    },
    {
      id: 'conv-2',
      participantId: 'user-2',
      participantName: 'Soro Chantal',
      participantAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face',
      lastMessage: {
        id: 'msg-2',
        senderId: 'user-2',
        senderName: 'Soro Chantal',
        content: 'Merci pour votre aide ! Le produit est parfait.',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        isRead: true,
        conversationId: 'conv-2'
      },
      unreadCount: 0,
      isOnline: false,
      lastSeen: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
    },
    {
      id: 'conv-3',
      participantId: 'user-3',
      participantName: 'KOUASSI Ange',
      participantAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      lastMessage: {
        id: 'msg-3',
        senderId: currentUser?.id || 'current-user',
        senderName: currentUser?.username || 'Vous',
        content: 'D\'accord, je vous envoie les détails par email.',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        isRead: true,
        conversationId: 'conv-3'
      },
      unreadCount: 0,
      isOnline: false,
      lastSeen: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4 hours ago
    }
  ];

  useEffect(() => {
    setConversations(mockConversations);
  }, [currentUser]);

  // Fermer le dropdown quand on clique à l'extérieur
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSelectedConversation(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
    setSelectedConversation(null);
  };

  const totalUnreadCount = conversations.reduce((sum, conv) => sum + conv.unreadCount, 0);

  const filteredConversations = conversations.filter(conv =>
    conv.participantName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatTime = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'À l\'instant';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}j`;
  };

  const handleSendMessage = () => {
    if (!newMessage.trim() || !selectedConversation) return;
    
    // Simuler l'envoi d'un message
    const message: Message = {
      id: `msg-${Date.now()}`,
      senderId: currentUser?.id || 'current-user',
      senderName: currentUser?.username || 'Vous',
      content: newMessage.trim(),
      timestamp: new Date(),
      isRead: false,
      conversationId: selectedConversation.id
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');
  };

  const openConversation = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    
    // Simuler le chargement des messages
    const mockMessages: Message[] = [
      {
        id: 'msg-old-1',
        senderId: conversation.participantId,
        senderName: conversation.participantName,
        content: 'Bonjour ! Comment allez-vous ?',
        timestamp: new Date(Date.now() - 60 * 60 * 1000),
        isRead: true,
        conversationId: conversation.id
      },
      conversation.lastMessage
    ];
    
    setMessages(mockMessages);
    
    // Marquer comme lu
    if (conversation.unreadCount > 0) {
      setConversations(prev => 
        prev.map(conv => 
          conv.id === conversation.id 
            ? { ...conv, unreadCount: 0 }
            : conv
        )
      );
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        className="relative p-2 rounded-full text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
        onClick={toggleDropdown}
        aria-label="Messages"
      >
        <MessageSquare size={24} />
        {totalUnreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
            {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
          {!selectedConversation ? (
            // Liste des conversations
            <>
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900">Messages</h3>
                  <button className="p-1 rounded-full hover:bg-gray-100">
                    <Settings size={16} className="text-gray-500" />
                  </button>
                </div>
                
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Rechercher une conversation..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-9 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>
              </div>

              <div className="max-h-80 overflow-y-auto">
                {filteredConversations.length === 0 ? (
                  <div className="p-6 text-center text-gray-500">
                    <MessageSquare size={48} className="mx-auto mb-2 text-gray-300" />
                    <p>Aucune conversation trouvée</p>
                  </div>
                ) : (
                  filteredConversations.map((conversation) => (
                    <div
                      key={conversation.id}
                      className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                      onClick={() => openConversation(conversation)}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="relative">
                          <Avatar
                            src={conversation.participantAvatar}
                            alt={conversation.participantName}
                            size="sm"
                          />
                          {conversation.isOnline && (
                            <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></span>
                          )}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium text-gray-900 truncate">
                              {conversation.participantName}
                            </h4>
                            <span className="text-xs text-gray-500">
                              {formatTime(conversation.lastMessage.timestamp)}
                            </span>
                          </div>
                          
                          <div className="flex items-center justify-between mt-1">
                            <p className="text-sm text-gray-600 truncate">
                              {conversation.lastMessage.senderId === currentUser?.id ? 'Vous: ' : ''}
                              {conversation.lastMessage.content}
                            </p>
                            
                            {conversation.unreadCount > 0 && (
                              <span className="bg-blue-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center ml-2">
                                {conversation.unreadCount}
                              </span>
                            )}
                          </div>
                          
                          {!conversation.isOnline && conversation.lastSeen && (
                            <p className="text-xs text-gray-400 mt-1">
                              Vu {formatTime(conversation.lastSeen)}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              <div className="p-3 border-t border-gray-200">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => console.log('Ouvrir toutes les conversations')}
                >
                  Voir toutes les conversations
                </Button>
              </div>
            </>
          ) : (
            // Vue de conversation
            <>
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setSelectedConversation(null)}
                    className="p-1 rounded-full hover:bg-gray-100"
                  >
                    ←
                  </button>
                  
                  <div className="relative">
                    <Avatar
                      src={selectedConversation.participantAvatar}
                      alt={selectedConversation.participantName}
                      size="sm"
                    />
                    {selectedConversation.isOnline && (
                      <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></span>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900">
                      {selectedConversation.participantName}
                    </h4>
                    <p className="text-xs text-gray-500">
                      {selectedConversation.isOnline ? 'En ligne' : `Vu ${formatTime(selectedConversation.lastSeen!)}`}
                    </p>
                  </div>
                  
                  <button className="p-1 rounded-full hover:bg-gray-100">
                    <MoreHorizontal size={16} className="text-gray-500" />
                  </button>
                </div>
              </div>

              <div className="h-64 overflow-y-auto p-3 space-y-3">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.senderId === currentUser?.id ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                        message.senderId === currentUser?.id
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <p>{message.content}</p>
                      <div className={`flex items-center justify-end mt-1 space-x-1 text-xs ${
                        message.senderId === currentUser?.id ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        <span>{formatTime(message.timestamp)}</span>
                        {message.senderId === currentUser?.id && (
                          message.isRead ? <CheckCheck size={12} /> : <Check size={12} />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="p-3 border-t border-gray-200">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    placeholder="Tapez votre message..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim()}
                    size="sm"
                    className="px-3"
                  >
                    <Send size={16} />
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default MessagesDropdown;
