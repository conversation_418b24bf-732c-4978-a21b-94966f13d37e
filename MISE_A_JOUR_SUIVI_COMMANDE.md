# Mise à jour du système de suivi de commande

## Problème résolu

Le système de suivi de commande présentait un problème de synchronisation entre la table `orders` et le système de suivi dédié (`order_tracking`). La colonne `tracking_number` était manquante dans la table `orders`, ce qui empêchait le bon fonctionnement du système de suivi.

## Modifications apportées

### 1. Nouvelle migration SQL

Une nouvelle migration a été créée pour ajouter la colonne `tracking_number` à la table `orders` :

```sql
-- Migration pour ajouter la colonne tracking_number à la table orders
ALTER TABLE orders
ADD COLUMN IF NOT EXISTS tracking_number VARCHAR(50);

-- Ajout d'un index pour améliorer les performances des recherches par numéro de suivi
CREATE INDEX IF NOT EXISTS idx_orders_tracking_number ON orders(tracking_number);

-- Commentaire sur la colonne pour la documentation
COMMENT ON COLUMN orders.tracking_number IS 'Numéro de suivi pour le système de tracking des commandes';
```

### 2. Mise à jour du service OrdersService

Le service `OrdersService` a été mis à jour pour utiliser la nouvelle colonne `tracking_number` :

- La méthode `addTrackingNumber` enregistre maintenant le numéro de suivi dans la colonne dédiée
- Une synchronisation avec le système de suivi de commande a été ajoutée pour s'assurer que chaque commande avec un numéro de suivi est correctement initialisée dans le système de suivi
- La fonction `transformDatabaseOrderToRealOrder` a été mise à jour pour récupérer le numéro de suivi depuis la base de données

## Comment appliquer la mise à jour

Pour appliquer cette mise à jour à votre environnement de développement ou de production, suivez ces étapes :

### Environnement de développement

```bash
# Appliquer la migration à votre base de données locale
npx supabase migration up
```

### Environnement de production

```bash
# Générer un script SQL pour la migration
npx supabase db push

# Ou appliquer directement via l'interface Supabase
# 1. Connectez-vous à votre projet Supabase
# 2. Allez dans "SQL Editor"
# 3. Exécutez le contenu du fichier de migration
```

## Vérification

Pour vérifier que la mise à jour a été correctement appliquée :

1. Ajoutez un numéro de suivi à une commande existante
2. Vérifiez que le numéro apparaît dans l'interface utilisateur
3. Vérifiez que le système de suivi de commande affiche correctement les informations de suivi

## Support

Si vous rencontrez des problèmes avec cette mise à jour, veuillez contacter l'équipe de développement.