import React, { useState, useMemo } from 'react';
import { MessageCircle, Star, ThumbsUp, Search, Filter, Calendar, AlertCircle, Eye, Reply } from 'lucide-react';
import { usePosts } from '../../context/PostsContext';
import { useAuth } from '../../context/AuthContext';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import { IPost, IComment, PostType, UserRole } from '../../types';

interface BusinessReviewsDashboardProps {
  businessName: string;
}

const BusinessReviewsDashboard: React.FC<BusinessReviewsDashboardProps> = ({ businessName }) => {
  const { posts, addComment } = usePosts();
  const { currentUser } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterRating, setFilterRating] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');

  // Filtrer les posts concernant cette entreprise
  const businessPosts = useMemo(() => {
    return posts.filter(post => 
      post.businessName.toLowerCase() === businessName.toLowerCase()
    );
  }, [posts, businessName]);

  // Calculer les statistiques
  const stats = useMemo(() => {
    const totalReviews = businessPosts.length;
    const totalComments = businessPosts.reduce((sum, post) => sum + (post.comments?.length || 0), 0);
    
    // Calculer la note moyenne
    let totalRating = 0;
    let ratingCount = 0;
    
    businessPosts.forEach(post => {
      if (post.rating && post.rating > 0) {
        totalRating += post.rating;
        ratingCount++;
      }
      
      post.comments?.forEach(comment => {
        if (comment.hasUsedProduct && comment.rating && comment.rating > 0) {
          totalRating += comment.rating;
          ratingCount++;
        }
      });
    });
    
    const averageRating = ratingCount > 0 ? totalRating / ratingCount : 0;
    
    // Compter les avis positifs (4-5 étoiles)
    const positiveReviews = businessPosts.filter(post => 
      (post.rating && post.rating >= 4) || post.type === PostType.FAVORITE
    ).length;
    
    // Compter les avis en attente de réponse
    const pendingResponses = businessPosts.filter(post => 
      post.comments?.some(comment => 
        comment.hasUsedProduct && !post.comments?.some(reply => 
          reply.userId === currentUser?.id && reply.content.includes('@' + comment.username)
        )
      )
    ).length;

    return {
      totalReviews,
      averageRating,
      positiveReviews,
      pendingResponses,
      totalComments
    };
  }, [businessPosts, currentUser?.id]);

  // Filtrer et trier les posts
  const filteredPosts = useMemo(() => {
    let filtered = businessPosts;

    // Filtrer par recherche
    if (searchQuery) {
      filtered = filtered.filter(post =>
        post.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.productName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.username?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filtrer par note
    if (filterRating !== 'all') {
      const rating = parseInt(filterRating);
      filtered = filtered.filter(post => post.rating === rating);
    }

    // Filtrer par catégorie
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(post => post.category === selectedCategory);
    }

    // Trier
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [businessPosts, searchQuery, filterRating, selectedCategory, sortBy]);

  // Obtenir les catégories uniques
  const categories = useMemo(() => {
    const cats = [...new Set(businessPosts.map(post => post.category).filter(Boolean))];
    return cats;
  }, [businessPosts]);

  const handleReply = async (postId: string) => {
    if (!replyText.trim() || !currentUser) return;

    try {
      // Récupérer le nom de l'entreprise depuis le profil utilisateur ou les props
      const enterpriseName = (currentUser as any).businessName || businessName || currentUser.username;

      await addComment(postId, {
        userId: currentUser.id,
        username: enterpriseName, // Utiliser le nom de l'entreprise
        profilePicture: currentUser.profilePicture || '',
        content: replyText,
        hasUsedProduct: false,
        createdAt: new Date(),
        authorRole: UserRole.BUSINESS, // Marquer comme réponse d'entreprise
        businessName: enterpriseName, // Ajouter le nom de l'entreprise
      });

      setReplyText('');
      setReplyingTo(null);
    } catch (error) {
      console.error('Erreur lors de la réponse:', error);
    }
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const getTypeColor = (type: PostType) => {
    switch (type) {
      case PostType.FAVORITE:
        return 'text-green-600 bg-green-50';
      case PostType.COMPLAINT:
        return 'text-red-600 bg-red-50';
      case PostType.REVIEW:
        return 'text-blue-600 bg-blue-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getTypeLabel = (type: PostType) => {
    switch (type) {
      case PostType.FAVORITE:
        return 'Coup de coeur';
      case PostType.COMPLAINT:
        return 'Coup de gueule';
      case PostType.REVIEW:
        return 'Demande d\'avis';
      default:
        return 'Avis';
    }
  };

  return (
    <div className="business-reviews-dashboard">
      {/* En-tête avec titre */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Avis sur vos produits</h2>
        <p className="text-gray-600">Consultez et répondez aux avis de vos clients</p>
      </div>

      {/* Tableau de bord des statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Total des avis */}
        <Card>
          <CardBody className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <MessageCircle size={24} className="text-blue-500" />
            </div>
            <div className="text-2xl font-bold text-gray-900">{stats.totalReviews}</div>
            <div className="text-sm text-gray-500">Avis totaux</div>
          </CardBody>
        </Card>

        {/* Note moyenne */}
        <Card>
          <CardBody className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Star size={24} className="text-yellow-500" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {stats.averageRating.toFixed(1)}
            </div>
            <div className="text-sm text-gray-500">Note moyenne</div>
          </CardBody>
        </Card>

        {/* Avis positifs */}
        <Card>
          <CardBody className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <ThumbsUp size={24} className="text-green-500" />
            </div>
            <div className="text-2xl font-bold text-gray-900">{stats.positiveReviews}</div>
            <div className="text-sm text-gray-500">Avis positifs</div>
          </CardBody>
        </Card>

        {/* En attente de réponse */}
        <Card>
          <CardBody className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <AlertCircle size={24} className="text-orange-500" />
            </div>
            <div className="text-2xl font-bold text-gray-900">{stats.pendingResponses}</div>
            <div className="text-sm text-gray-500">En attente de réponse</div>
          </CardBody>
        </Card>
      </div>

      {/* Filtres et recherche */}
      <Card className="mb-6">
        <CardBody className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            {/* Recherche */}
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Rechercher un avis..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Filtre par note */}
            <div className="flex items-center space-x-2">
              <Filter size={16} className="text-gray-500" />
              <select
                value={filterRating}
                onChange={(e) => setFilterRating(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Toutes les notes</option>
                <option value="5">5 étoiles</option>
                <option value="4">4 étoiles</option>
                <option value="3">3 étoiles</option>
                <option value="2">2 étoiles</option>
                <option value="1">1 étoile</option>
              </select>
            </div>

            {/* Filtre par catégorie */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Toutes catégories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>

            {/* Tri */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="date">Trier par: Date (↓)</option>
              <option value="rating">Trier par: Note</option>
            </select>
          </div>
        </CardBody>
      </Card>

      {/* Liste des avis */}
      <div className="space-y-4">
        {filteredPosts.length === 0 ? (
          <Card>
            <CardBody className="p-8 text-center">
              <AlertCircle size={48} className="mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun avis trouvé</h3>
              <p className="text-gray-500">
                {searchQuery || filterRating !== 'all' || selectedCategory !== 'all'
                  ? 'Aucun avis ne correspond à vos critères de recherche.'
                  : 'Aucun avis n\'a encore été publié sur vos produits.'}
              </p>
            </CardBody>
          </Card>
        ) : (
          filteredPosts.map(post => (
            <Card key={post.id} className="hover:shadow-md transition-shadow">
              <CardBody className="p-6">
                {/* En-tête de l'avis */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <img
                      src={post.userProfilePicture || '/default-avatar.png'}
                      alt={post.username}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <h4 className="font-medium text-gray-900">{post.username}</h4>
                      <p className="text-sm text-gray-500">{formatDate(post.createdAt)}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(post.type)}`}>
                      {getTypeLabel(post.type)}
                    </span>
                    {post.rating && (
                      <div className="flex items-center">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star
                            key={i}
                            size={16}
                            className={i < post.rating! ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Contenu de l'avis */}
                <div className="mb-4">
                  <h5 className="font-medium text-gray-900 mb-1">{post.productName}</h5>
                  <p className="text-gray-700">{post.description}</p>
                </div>

                {/* Images */}
                {post.images && post.images.length > 0 && (
                  <div className="flex space-x-2 mb-4">
                    {post.images.slice(0, 3).map((image, index) => (
                      <img
                        key={index}
                        src={image}
                        alt={`Image ${index + 1}`}
                        className="w-16 h-16 rounded-md object-cover"
                      />
                    ))}
                    {post.images.length > 3 && (
                      <div className="w-16 h-16 rounded-md bg-gray-100 flex items-center justify-center text-sm text-gray-500">
                        +{post.images.length - 3}
                      </div>
                    )}
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span className="flex items-center">
                      <ThumbsUp size={16} className="mr-1" />
                      {post.likes?.length || 0}
                    </span>
                    <span className="flex items-center">
                      <MessageCircle size={16} className="mr-1" />
                      {post.comments?.length || 0}
                    </span>
                    <span className="flex items-center">
                      <Eye size={16} className="mr-1" />
                      Vu
                    </span>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setReplyingTo(replyingTo === post.id ? null : post.id)}
                    leftIcon={<Reply size={16} />}
                  >
                    Répondre
                  </Button>
                </div>

                {/* Formulaire de réponse */}
                {replyingTo === post.id && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="flex space-x-3">
                      <img
                        src={currentUser?.profilePicture || '/default-avatar.png'}
                        alt={currentUser?.username}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                      <div className="flex-1">
                        <textarea
                          value={replyText}
                          onChange={(e) => setReplyText(e.target.value)}
                          placeholder="Répondre à cet avis..."
                          className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          rows={3}
                        />
                        <div className="flex justify-end space-x-2 mt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setReplyingTo(null);
                              setReplyText('');
                            }}
                          >
                            Annuler
                          </Button>
                          <Button
                            variant="primary"
                            size="sm"
                            onClick={() => handleReply(post.id)}
                            disabled={!replyText.trim()}
                          >
                            Répondre
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Commentaires existants */}
                {post.comments && post.comments.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <h6 className="font-medium text-gray-900 mb-3">
                      Commentaires ({post.comments.length})
                    </h6>
                    <div className="space-y-3">
                      {post.comments.slice(0, 2).map((comment: IComment) => (
                        <div key={comment.id} className="flex space-x-3">
                          <img
                            src={comment.profilePicture || '/default-avatar.png'}
                            alt={comment.username}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                          <div className="flex-1">
                            <div className="bg-gray-50 rounded-lg p-3">
                              <div className="flex items-center justify-between mb-1">
                                <span className="font-medium text-sm text-gray-900">
                                  {comment.username}
                                </span>
                                <span className="text-xs text-gray-500">
                                  {formatDate(comment.createdAt)}
                                </span>
                              </div>
                              <p className="text-sm text-gray-700">{comment.content}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                      {post.comments.length > 2 && (
                        <button className="text-sm text-blue-600 hover:text-blue-800">
                          Voir tous les commentaires ({post.comments.length})
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default BusinessReviewsDashboard;
