import { supabase } from '../lib/supabase';

// Types pour l'analyse business
export interface CustomerInsight {
  id: string;
  customerId: string;
  customerName: string;
  totalInteractions: number;
  averageRating: number;
  favoriteCategories: string[];
  sentimentScore: number; // -1 à 1
  purchaseFrequency: 'low' | 'medium' | 'high';
  loyaltyLevel: 'new' | 'regular' | 'loyal' | 'champion';
  lastInteraction: string;
  preferredPriceRange: { min: number; max: number };
  complaints: string[];
  suggestions: string[];
}

export interface ProductAnalysis {
  productId: string;
  productName: string;
  category: string;
  averageRating: number;
  totalReviews: number;
  sentimentTrend: 'improving' | 'stable' | 'declining';
  commonComplaints: Array<{ issue: string; frequency: number; severity: 'low' | 'medium' | 'high' }>;
  positiveAspects: Array<{ aspect: string; frequency: number }>;
  improvementSuggestions: string[];
  competitorComparisons: Array<{ competitor: string; advantage: string; disadvantage: string }>;
}

export interface MarketTrend {
  category: string;
  trendDirection: 'rising' | 'stable' | 'declining';
  demandLevel: number; // 0-100
  seasonality: string[];
  emergingNeeds: string[];
  priceExpectations: { min: number; max: number; preferred: number };
  targetDemographics: string[];
  competitorGaps: string[];
}

export interface BusinessRecommendation {
  id: string;
  type: 'product_improvement' | 'new_product' | 'marketing_strategy' | 'customer_retention' | 'pricing_strategy';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  description: string;
  expectedImpact: string;
  implementationDifficulty: 'easy' | 'medium' | 'hard';
  estimatedCost: 'low' | 'medium' | 'high';
  timeframe: string;
  kpis: string[];
  actionSteps: string[];
  dataSupport: {
    customerFeedback: number;
    marketDemand: number;
    competitiveAdvantage: number;
  };
}

export interface NewProductProposal {
  id: string;
  proposedName: string;
  category: string;
  targetMarket: string[];
  keyFeatures: string[];
  uniqueSellingPoints: string[];
  estimatedDemand: number;
  suggestedPriceRange: { min: number; max: number };
  developmentComplexity: 'low' | 'medium' | 'high';
  marketGap: string;
  customerNeedsSatisfied: string[];
  competitiveAdvantages: string[];
  riskFactors: string[];
  successProbability: number; // 0-100
}

export class BusinessAIAnalysisService {
  /**
   * Analyse complète des clients pour une entreprise
   */
  static async analyzeCustomerBase(businessId: string): Promise<CustomerInsight[]> {
    try {
      // Récupérer tous les posts et interactions liés à l'entreprise
      const { data: posts, error } = await supabase
        .from('posts_with_author_details')
        .select('*')
        .eq('business_id', businessId);

      if (error) {
        console.error('Erreur lors de la récupération des posts:', error);
        return [];
      }

      // Analyser les données par client
      const customerMap = new Map<string, any>();
      
      posts?.forEach(post => {
        const customerId = post.user_id;
        if (!customerMap.has(customerId)) {
          customerMap.set(customerId, {
            id: customerId,
            customerId,
            customerName: post.username || 'Client anonyme',
            posts: [],
            ratings: [],
            categories: [],
            interactions: 0
          });
        }
        
        const customer = customerMap.get(customerId);
        customer.posts.push(post);
        customer.ratings.push(post.rating || 0);
        customer.categories.push(post.category);
        customer.interactions++;
      });

      // Transformer en insights
      const insights: CustomerInsight[] = Array.from(customerMap.values()).map(customer => {
        const avgRating = customer.ratings.length > 0 
          ? customer.ratings.reduce((a: number, b: number) => a + b, 0) / customer.ratings.length 
          : 0;

        const categoryFreq = this.calculateCategoryFrequency(customer.categories);
        const sentimentScore = this.calculateSentimentScore(customer.posts);
        const loyaltyLevel = this.determineLoyaltyLevel(customer.interactions, avgRating);
        const purchaseFreq = this.determinePurchaseFrequency(customer.interactions);

        return {
          id: customer.id,
          customerId: customer.customerId,
          customerName: customer.customerName,
          totalInteractions: customer.interactions,
          averageRating: avgRating,
          favoriteCategories: Object.keys(categoryFreq).slice(0, 3),
          sentimentScore,
          purchaseFrequency: purchaseFreq,
          loyaltyLevel,
          lastInteraction: customer.posts[customer.posts.length - 1]?.created_at || '',
          preferredPriceRange: this.calculatePriceRange(customer.posts),
          complaints: this.extractComplaints(customer.posts),
          suggestions: this.extractSuggestions(customer.posts)
        };
      });

      return insights;
    } catch (error) {
      console.error('Erreur dans analyzeCustomerBase:', error);
      return [];
    }
  }

  /**
   * Analyse des produits de l'entreprise
   */
  static async analyzeProducts(businessId: string): Promise<ProductAnalysis[]> {
    try {
      // Récupérer les produits et leurs avis
      const { data: products, error } = await supabase
        .from('marketplace_products')
        .select(`
          *,
          marketcomments(*),
          marketrating(*)
        `)
        .eq('business_id', businessId);

      if (error) {
        console.error('Erreur lors de la récupération des produits:', error);
        return [];
      }

      const analyses: ProductAnalysis[] = products?.map(product => {
        const comments = product.marketcomments || [];
        const ratings = product.marketrating || [];
        
        const avgRating = ratings.length > 0 
          ? ratings.reduce((sum: number, r: any) => sum + r.rating, 0) / ratings.length 
          : 0;

        const sentimentTrend = this.analyzeSentimentTrend(comments);
        const complaints = this.analyzeComplaints(comments);
        const positiveAspects = this.analyzePositiveAspects(comments);

        return {
          productId: product.id,
          productName: product.name,
          category: product.category,
          averageRating: avgRating,
          totalReviews: comments.length,
          sentimentTrend,
          commonComplaints: complaints,
          positiveAspects,
          improvementSuggestions: this.generateImprovementSuggestions(complaints, positiveAspects),
          competitorComparisons: this.generateCompetitorComparisons(product.category)
        };
      }) || [];

      return analyses;
    } catch (error) {
      console.error('Erreur dans analyzeProducts:', error);
      return [];
    }
  }

  /**
   * Analyse des tendances du marché
   */
  static async analyzeMarketTrends(businessId: string): Promise<MarketTrend[]> {
    try {
      // Récupérer toutes les données du marché
      const { data: allPosts, error } = await supabase
        .from('posts_with_author_details')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1000);

      if (error) {
        console.error('Erreur lors de la récupération des tendances:', error);
        return [];
      }

      // Analyser par catégorie
      const categoryData = this.groupByCategory(allPosts || []);
      
      const trends: MarketTrend[] = Object.entries(categoryData).map(([category, data]: [string, any]) => {
        return {
          category,
          trendDirection: this.calculateTrendDirection(data),
          demandLevel: this.calculateDemandLevel(data),
          seasonality: this.analyzeSeasonality(data),
          emergingNeeds: this.identifyEmergingNeeds(data),
          priceExpectations: this.analyzePriceExpectations(data),
          targetDemographics: this.identifyTargetDemographics(data),
          competitorGaps: this.identifyCompetitorGaps(data)
        };
      });

      return trends;
    } catch (error) {
      console.error('Erreur dans analyzeMarketTrends:', error);
      return [];
    }
  }

  // Méthodes utilitaires
  private static calculateCategoryFrequency(categories: string[]): Record<string, number> {
    const freq: Record<string, number> = {};
    categories.forEach(cat => {
      if (cat) freq[cat] = (freq[cat] || 0) + 1;
    });
    return freq;
  }

  private static calculateSentimentScore(posts: any[]): number {
    let totalSentiment = 0;
    let count = 0;

    posts.forEach(post => {
      if (post.rating) {
        // Convertir la note (1-5) en sentiment (-1 à 1)
        const sentiment = (post.rating - 3) / 2;
        totalSentiment += sentiment;
        count++;
      }
    });

    return count > 0 ? totalSentiment / count : 0;
  }

  private static determineLoyaltyLevel(interactions: number, avgRating: number): CustomerInsight['loyaltyLevel'] {
    if (interactions >= 10 && avgRating >= 4) return 'champion';
    if (interactions >= 5 && avgRating >= 3.5) return 'loyal';
    if (interactions >= 2) return 'regular';
    return 'new';
  }

  private static determinePurchaseFrequency(interactions: number): CustomerInsight['purchaseFrequency'] {
    if (interactions >= 8) return 'high';
    if (interactions >= 3) return 'medium';
    return 'low';
  }

  private static calculatePriceRange(posts: any[]): { min: number; max: number } {
    const prices = posts.map(p => p.price).filter(p => p && p > 0);
    if (prices.length === 0) return { min: 0, max: 100000 };
    
    return {
      min: Math.min(...prices),
      max: Math.max(...prices)
    };
  }

  private static extractComplaints(posts: any[]): string[] {
    const complaints: string[] = [];
    posts.forEach(post => {
      if (post.rating && post.rating <= 2 && post.description) {
        complaints.push(post.description);
      }
    });
    return complaints.slice(0, 5); // Limiter à 5 plaintes
  }

  private static extractSuggestions(posts: any[]): string[] {
    const suggestions: string[] = [];
    posts.forEach(post => {
      if (post.description && post.description.toLowerCase().includes('suggère')) {
        suggestions.push(post.description);
      }
    });
    return suggestions.slice(0, 3);
  }

  private static analyzeSentimentTrend(comments: any[]): ProductAnalysis['sentimentTrend'] {
    if (comments.length < 2) return 'stable';
    
    // Analyser les 30 derniers jours vs les 30 précédents
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

    const recentComments = comments.filter(c => new Date(c.created_at) > thirtyDaysAgo);
    const olderComments = comments.filter(c => 
      new Date(c.created_at) > sixtyDaysAgo && new Date(c.created_at) <= thirtyDaysAgo
    );

    if (recentComments.length === 0 || olderComments.length === 0) return 'stable';

    const recentAvg = recentComments.reduce((sum, c) => sum + (c.rating || 3), 0) / recentComments.length;
    const olderAvg = olderComments.reduce((sum, c) => sum + (c.rating || 3), 0) / olderComments.length;

    const diff = recentAvg - olderAvg;
    if (diff > 0.3) return 'improving';
    if (diff < -0.3) return 'declining';
    return 'stable';
  }

  private static analyzeComplaints(comments: any[]): ProductAnalysis['commonComplaints'] {
    const complaints: Array<{ issue: string; frequency: number; severity: 'low' | 'medium' | 'high' }> = [];
    
    // Mots-clés pour identifier les plaintes
    const complaintKeywords = {
      'qualité': { severity: 'high' as const, variations: ['qualité', 'mauvaise qualité', 'défaut'] },
      'prix': { severity: 'medium' as const, variations: ['cher', 'prix', 'coût', 'expensive'] },
      'livraison': { severity: 'medium' as const, variations: ['livraison', 'délai', 'retard'] },
      'service': { severity: 'high' as const, variations: ['service', 'accueil', 'personnel'] },
      'emballage': { severity: 'low' as const, variations: ['emballage', 'packaging', 'boîte'] }
    };

    Object.entries(complaintKeywords).forEach(([issue, config]) => {
      let frequency = 0;
      comments.forEach(comment => {
        if (comment.rating && comment.rating <= 2 && comment.comment) {
          const text = comment.comment.toLowerCase();
          if (config.variations.some(variation => text.includes(variation))) {
            frequency++;
          }
        }
      });

      if (frequency > 0) {
        complaints.push({
          issue,
          frequency,
          severity: config.severity
        });
      }
    });

    return complaints.sort((a, b) => b.frequency - a.frequency).slice(0, 5);
  }

  private static analyzePositiveAspects(comments: any[]): ProductAnalysis['positiveAspects'] {
    const aspects: Array<{ aspect: string; frequency: number }> = [];
    
    const positiveKeywords = {
      'qualité': ['excellente qualité', 'bonne qualité', 'qualité', 'solide'],
      'prix': ['bon prix', 'abordable', 'pas cher', 'rapport qualité-prix'],
      'design': ['beau', 'joli', 'design', 'esthétique'],
      'efficacité': ['efficace', 'fonctionne bien', 'performant'],
      'service': ['bon service', 'excellent service', 'accueil']
    };

    Object.entries(positiveKeywords).forEach(([aspect, keywords]) => {
      let frequency = 0;
      comments.forEach(comment => {
        if (comment.rating && comment.rating >= 4 && comment.comment) {
          const text = comment.comment.toLowerCase();
          if (keywords.some(keyword => text.includes(keyword))) {
            frequency++;
          }
        }
      });

      if (frequency > 0) {
        aspects.push({ aspect, frequency });
      }
    });

    return aspects.sort((a, b) => b.frequency - a.frequency).slice(0, 5);
  }

  private static generateImprovementSuggestions(
    complaints: ProductAnalysis['commonComplaints'],
    positiveAspects: ProductAnalysis['positiveAspects']
  ): string[] {
    const suggestions: string[] = [];

    complaints.forEach(complaint => {
      switch (complaint.issue) {
        case 'qualité':
          suggestions.push('Améliorer le contrôle qualité et les matériaux utilisés');
          break;
        case 'prix':
          suggestions.push('Revoir la stratégie de prix ou proposer des options plus abordables');
          break;
        case 'livraison':
          suggestions.push('Optimiser la logistique et communiquer sur les délais');
          break;
        case 'service':
          suggestions.push('Former le personnel et améliorer l\'expérience client');
          break;
        case 'emballage':
          suggestions.push('Repenser l\'emballage pour une meilleure protection');
          break;
      }
    });

    return suggestions.slice(0, 3);
  }

  private static generateCompetitorComparisons(category: string): ProductAnalysis['competitorComparisons'] {
    // Simulation de comparaisons concurrentielles basées sur la catégorie
    const comparisons = [
      {
        competitor: 'Concurrent A',
        advantage: 'Prix plus compétitif',
        disadvantage: 'Qualité perçue comme inférieure'
      },
      {
        competitor: 'Concurrent B',
        advantage: 'Meilleur service client',
        disadvantage: 'Gamme de produits plus limitée'
      }
    ];

    return comparisons;
  }

  private static groupByCategory(posts: any[]): Record<string, any[]> {
    const grouped: Record<string, any[]> = {};
    posts.forEach(post => {
      const category = post.category || 'Autre';
      if (!grouped[category]) grouped[category] = [];
      grouped[category].push(post);
    });
    return grouped;
  }

  private static calculateTrendDirection(data: any[]): MarketTrend['trendDirection'] {
    if (data.length < 2) return 'stable';
    
    // Comparer les 30 derniers jours avec les 30 précédents
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

    const recentPosts = data.filter(d => new Date(d.created_at) > thirtyDaysAgo);
    const olderPosts = data.filter(d => 
      new Date(d.created_at) > sixtyDaysAgo && new Date(d.created_at) <= thirtyDaysAgo
    );

    if (recentPosts.length > olderPosts.length * 1.2) return 'rising';
    if (recentPosts.length < olderPosts.length * 0.8) return 'declining';
    return 'stable';
  }

  private static calculateDemandLevel(data: any[]): number {
    // Calculer le niveau de demande basé sur le nombre d'interactions récentes
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const recentPosts = data.filter(d => new Date(d.created_at) > thirtyDaysAgo);
    
    // Normaliser sur une échelle de 0-100
    const maxExpected = 50; // Nombre maximum attendu de posts par mois
    return Math.min(100, (recentPosts.length / maxExpected) * 100);
  }

  private static analyzeSeasonality(data: any[]): string[] {
    const monthlyData: Record<string, number> = {};
    
    data.forEach(item => {
      const month = new Date(item.created_at).toLocaleString('fr-FR', { month: 'long' });
      monthlyData[month] = (monthlyData[month] || 0) + 1;
    });

    // Identifier les mois avec le plus d'activité
    const sortedMonths = Object.entries(monthlyData)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([month]) => month);

    return sortedMonths;
  }

  private static identifyEmergingNeeds(data: any[]): string[] {
    const needs: string[] = [];
    
    // Analyser les descriptions pour identifier des besoins émergents
    const keywords = ['besoin', 'manque', 'souhaite', 'voudrait', 'aimerait'];
    
    data.forEach(item => {
      if (item.description) {
        const text = item.description.toLowerCase();
        keywords.forEach(keyword => {
          if (text.includes(keyword)) {
            // Extraire le contexte autour du mot-clé
            const sentences = text.split('.');
            sentences.forEach(sentence => {
              if (sentence.includes(keyword)) {
                needs.push(sentence.trim());
              }
            });
          }
        });
      }
    });

    return [...new Set(needs)].slice(0, 5); // Supprimer les doublons et limiter
  }

  private static analyzePriceExpectations(data: any[]): MarketTrend['priceExpectations'] {
    const prices = data.map(d => d.price).filter(p => p && p > 0);
    
    if (prices.length === 0) {
      return { min: 0, max: 100000, preferred: 50000 };
    }

    const sorted = prices.sort((a, b) => a - b);
    const min = sorted[0];
    const max = sorted[sorted.length - 1];
    const preferred = sorted[Math.floor(sorted.length / 2)]; // Médiane

    return { min, max, preferred };
  }

  private static identifyTargetDemographics(data: any[]): string[] {
    // Analyser les profils des utilisateurs actifs dans cette catégorie
    const demographics = ['Jeunes adultes (18-30)', 'Adultes (30-50)', 'Seniors (50+)'];
    return demographics.slice(0, 2); // Simulation
  }

  private static identifyCompetitorGaps(data: any[]): string[] {
    const gaps: string[] = [];
    
    // Analyser les plaintes pour identifier les lacunes du marché
    data.forEach(item => {
      if (item.rating && item.rating <= 2 && item.description) {
        const text = item.description.toLowerCase();
        if (text.includes('pas disponible') || text.includes('manque')) {
          gaps.push(item.description);
        }
      }
    });

    return [...new Set(gaps)].slice(0, 3);
  }
}
