import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { UserRole } from '../../types';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import { CreditCard, ArrowRight, CheckCircle } from 'lucide-react';

const QuickSubscriptionAccess: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  
  const isBusinessUser = currentUser?.role === UserRole.BUSINESS;

  if (!isBusinessUser) {
    return null; // Ne pas afficher pour les utilisateurs non-entreprise
  }

  return (
    <Card className="mb-4 border-2 border-green-400 bg-green-50">
      <CardHeader>
        <h3 className="text-lg font-semibold text-green-800 flex items-center">
          <CreditCard className="mr-2" size={20} />
          🎉 Nouveau Système d'Abonnements Disponible !
        </h3>
      </CardHeader>
      <CardBody>
        <div className="space-y-4">
          <div className="text-green-700">
            <p className="mb-3">
              <strong>Bonjour {(currentUser as any)?.businessName || currentUser?.username} !</strong>
            </p>
            <p className="mb-3">
              Le nouveau système d'abonnements entreprise est maintenant disponible avec :
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>🎁 <strong>Essai Gratuit</strong> - 7 jours (0 F CFA)</li>
              <li>📅 <strong>Abonnement Mensuel</strong> - 30 jours (25 000 F CFA)</li>
              <li>👑 <strong>Abonnement Annuel</strong> - 365 jours (240 000 F CFA) - Économisez 60 000 F CFA !</li>
            </ul>
          </div>
          
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={() => navigate('/business-subscription')}
              variant="primary"
              className="flex items-center"
            >
              <CreditCard className="mr-2" size={16} />
              Accéder aux Abonnements
              <ArrowRight className="ml-2" size={16} />
            </Button>

            <Button
              onClick={() => navigate('/profile')}
              variant="outline"
              className="flex items-center"
            >
              Profil Entreprise
            </Button>
          </div>
          
          <div className="mt-4 p-3 bg-white rounded-lg border border-green-200">
            <div className="flex items-start space-x-2">
              <CheckCircle className="text-green-600 mt-0.5" size={16} />
              <div className="text-sm text-green-700">
                <strong>Fonctionnalités Premium :</strong>
                <div className="mt-1 text-xs">
                  Analytics avancées • Support prioritaire • Intégrations API • Conseiller IA • Rapports personnalisés
                </div>
              </div>
            </div>
          </div>
          
          <div className="text-xs text-green-600 italic">
            💡 Astuce : Vous pouvez aussi accéder aux abonnements via l'onglet "Mon abonnement" dans votre profil entreprise.
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default QuickSubscriptionAccess;
