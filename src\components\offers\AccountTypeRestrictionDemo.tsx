import React, { useState } from 'react';
import { User, Building2, UserX, Heart, Share2, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, XCircle } from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import { UserRole } from '../../types/index';

interface AccountTypeRestrictionDemoProps {
  currentUserRole?: UserRole | null;
}

const AccountTypeRestrictionDemo: React.FC<AccountTypeRestrictionDemoProps> = ({ currentUserRole }) => {
  const [selectedAccountType, setSelectedAccountType] = useState<UserRole | null>(currentUserRole || null);

  const accountTypes = [
    {
      type: null,
      label: 'Non connecté',
      icon: <UserX className="text-gray-500" size={20} />,
      color: 'gray',
      description: 'Utilisateur non authentifié'
    },
    {
      type: UserRole.STANDARD,
      label: 'Utilisateur Particulier',
      icon: <User className="text-blue-500" size={20} />,
      color: 'blue',
      description: 'Consommateur standard'
    },
    {
      type: UserRole.BUSINESS,
      label: 'Compte Entreprise',
      icon: <Building2 className="text-orange-500" size={20} />,
      color: 'orange',
      description: 'Compte professionnel'
    }
  ];

  const getPermissions = (accountType: UserRole | null) => {
    switch (accountType) {
      case UserRole.STANDARD:
        return {
          canLike: true,
          canShare: true,
          canComment: true,
          restrictions: []
        };
      case UserRole.BUSINESS:
        return {
          canLike: false,
          canShare: true,
          canComment: false,
          restrictions: [
            'Ne peut pas liker les offres',
            'Ne peut pas commenter les publicités',
            'Peut seulement partager pour promouvoir'
          ]
        };
      default: // Non connecté
        return {
          canLike: false,
          canShare: false,
          canComment: false,
          restrictions: [
            'Doit se connecter pour interagir',
            'Aucune interaction possible',
            'Consultation seulement'
          ]
        };
    }
  };

  const permissions = getPermissions(selectedAccountType);

  const simulateAction = (action: string, allowed: boolean) => {
    if (allowed) {
      alert(`✅ Action "${action}" autorisée pour ce type de compte !`);
    } else {
      const messages = {
        'Liker': selectedAccountType === UserRole.BUSINESS 
          ? 'Seuls les utilisateurs particuliers peuvent liker les offres et promotions.'
          : 'Connectez-vous pour liker les offres.',
        'Partager': 'Connectez-vous pour partager les offres.',
        'Commenter': selectedAccountType === UserRole.BUSINESS
          ? 'Les entreprises ne peuvent pas commenter les publicités.'
          : 'Connectez-vous pour commenter.'
      };
      alert(`❌ ${messages[action as keyof typeof messages]}`);
    }
  };

  return (
    <Card className="border-2 border-purple-300 bg-purple-50">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <AlertTriangle className="text-purple-600" size={20} />
          <h3 className="font-semibold text-purple-800">Demo : Restrictions par Type de Compte</h3>
        </div>
      </CardHeader>
      <CardBody>
        <div className="space-y-6">
          {/* Sélecteur de type de compte */}
          <div>
            <h4 className="font-medium text-gray-800 mb-3">Simuler un type de compte :</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {accountTypes.map((account) => (
                <button
                  key={account.label}
                  onClick={() => setSelectedAccountType(account.type)}
                  className={`p-3 rounded-lg border-2 transition-all ${
                    selectedAccountType === account.type
                      ? `border-${account.color}-500 bg-${account.color}-100`
                      : 'border-gray-200 bg-white hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-2 mb-1">
                    {account.icon}
                    <span className="font-medium text-sm">{account.label}</span>
                  </div>
                  <p className="text-xs text-gray-600">{account.description}</p>
                </button>
              ))}
            </div>
          </div>

          {/* Affichage des permissions */}
          {selectedAccountType !== undefined && (
            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-medium text-gray-800 mb-3">
                Permissions pour : {accountTypes.find(a => a.type === selectedAccountType)?.label}
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className={`p-3 rounded-lg ${permissions.canLike ? 'bg-green-100' : 'bg-red-100'}`}>
                  <div className="flex items-center space-x-2 mb-1">
                    {permissions.canLike ? (
                      <CheckCircle className="text-green-600" size={16} />
                    ) : (
                      <XCircle className="text-red-600" size={16} />
                    )}
                    <span className="font-medium text-sm">Liker</span>
                  </div>
                  <p className={`text-xs ${permissions.canLike ? 'text-green-700' : 'text-red-700'}`}>
                    {permissions.canLike ? 'Autorisé' : 'Interdit'}
                  </p>
                </div>

                <div className={`p-3 rounded-lg ${permissions.canShare ? 'bg-green-100' : 'bg-red-100'}`}>
                  <div className="flex items-center space-x-2 mb-1">
                    {permissions.canShare ? (
                      <CheckCircle className="text-green-600" size={16} />
                    ) : (
                      <XCircle className="text-red-600" size={16} />
                    )}
                    <span className="font-medium text-sm">Partager</span>
                  </div>
                  <p className={`text-xs ${permissions.canShare ? 'text-green-700' : 'text-red-700'}`}>
                    {permissions.canShare ? 'Autorisé' : 'Interdit'}
                  </p>
                </div>

                <div className={`p-3 rounded-lg ${permissions.canComment ? 'bg-green-100' : 'bg-red-100'}`}>
                  <div className="flex items-center space-x-2 mb-1">
                    {permissions.canComment ? (
                      <CheckCircle className="text-green-600" size={16} />
                    ) : (
                      <XCircle className="text-red-600" size={16} />
                    )}
                    <span className="font-medium text-sm">Commenter</span>
                  </div>
                  <p className={`text-xs ${permissions.canComment ? 'text-green-700' : 'text-red-700'}`}>
                    {permissions.canComment ? 'Autorisé' : 'Interdit'}
                  </p>
                </div>
              </div>

              {/* Restrictions */}
              {permissions.restrictions.length > 0 && (
                <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
                  <h5 className="font-medium text-yellow-800 mb-2">Restrictions :</h5>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    {permissions.restrictions.map((restriction, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-yellow-600 mt-0.5">•</span>
                        <span>{restriction}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Boutons de test */}
          {selectedAccountType !== undefined && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-800 mb-3">Tester les interactions :</h4>
              <div className="flex flex-wrap gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  leftIcon={<Heart size={16} />}
                  className={`transition-colors ${
                    permissions.canLike 
                      ? 'text-red-600 hover:text-red-700 border-red-300'
                      : 'text-gray-400 cursor-not-allowed border-gray-300'
                  }`}
                  onClick={() => simulateAction('Liker', permissions.canLike)}
                  disabled={!permissions.canLike}
                >
                  Tester Like
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  leftIcon={<Share2 size={16} />}
                  className={`transition-colors ${
                    permissions.canShare 
                      ? 'text-blue-600 hover:text-blue-700 border-blue-300'
                      : 'text-gray-400 cursor-not-allowed border-gray-300'
                  }`}
                  onClick={() => simulateAction('Partager', permissions.canShare)}
                  disabled={!permissions.canShare}
                >
                  Tester Partage
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className={`transition-colors ${
                    permissions.canComment 
                      ? 'text-green-600 hover:text-green-700 border-green-300'
                      : 'text-gray-400 cursor-not-allowed border-gray-300'
                  }`}
                  onClick={() => simulateAction('Commenter', permissions.canComment)}
                  disabled={!permissions.canComment}
                >
                  Tester Commentaire
                </Button>
              </div>
            </div>
          )}

          {/* Informations */}
          <div className="text-xs text-purple-700 bg-purple-100 p-3 rounded">
            <p><strong>💡 Ce composant de démonstration :</strong></p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>Simule les différents types de comptes</li>
              <li>Montre les permissions et restrictions</li>
              <li>Teste les interactions en temps réel</li>
              <li>Affiche les messages d'erreur appropriés</li>
            </ul>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default AccountTypeRestrictionDemo;
