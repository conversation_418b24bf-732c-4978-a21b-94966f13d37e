import React, { useState, useEffect } from 'react';
import {
  Brain, TrendingUp, Lightbulb, Target, Star, AlertCircle,
  ThumbsUp, ThumbsDown, Users, DollarSign, Clock, Zap,
  BarChart3, PieChart, RefreshCw, Download, Settings,
  ChevronRight, Plus, Eye, CheckCircle, X
} from 'lucide-react';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';
import { useAuth } from '../../context/AuthContext';
import { formatAmount } from '../../utils/formatUtils';
import {
  AIRecommendationsService,
  ProductImprovement,
  NewProductSuggestion,
  MarketTrend,
  AIInsights
} from '../../services/aiRecommendationsService';
import '../../styles/AIRecommendationsManager.css';

interface AIRecommendationsManagerProps {
  businessId: string;
}

const AIRecommendationsManager: React.FC<AIRecommendationsManagerProps> = ({ businessId }) => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'improvements' | 'new-products' | 'trends' | 'insights'>('overview');

  // États pour les données IA
  const [improvements, setImprovements] = useState<ProductImprovement[]>([]);
  const [newProductSuggestions, setNewProductSuggestions] = useState<NewProductSuggestion[]>([]);
  const [marketTrends, setMarketTrends] = useState<MarketTrend[]>([]);
  const [aiInsights, setAiInsights] = useState<AIInsights | null>(null);

  // États pour les actions
  const [selectedImprovements, setSelectedImprovements] = useState<string[]>([]);
  const [selectedSuggestions, setSelectedSuggestions] = useState<string[]>([]);
  const [showImplementationModal, setShowImplementationModal] = useState<string | null>(null);

  useEffect(() => {
    loadAIRecommendations();
  }, [businessId]);

  const loadAIRecommendations = async () => {
    setLoading(true);
    try {
      const [improvementsData, suggestionsData, trendsData, insightsData] = await Promise.all([
        AIRecommendationsService.generateProductImprovements(businessId),
        AIRecommendationsService.generateNewProductSuggestions(businessId),
        AIRecommendationsService.analyzeMarketTrends(businessId),
        AIRecommendationsService.generateAIInsights(businessId)
      ]);

      setImprovements(improvementsData);
      setNewProductSuggestions(suggestionsData);
      setMarketTrends(trendsData);
      setAiInsights(insightsData);
    } catch (error) {
      console.error('Erreur lors du chargement des recommandations IA:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent': return <AlertCircle className="text-red-500" size={16} />;
      case 'high': return <TrendingUp className="text-orange-500" size={16} />;
      case 'medium': return <Target className="text-yellow-500" size={16} />;
      case 'low': return <Clock className="text-green-500" size={16} />;
      default: return <Clock className="text-gray-500" size={16} />;
    }
  };

  const getDemandIcon = (demand: string) => {
    switch (demand) {
      case 'very_high': return '🔥';
      case 'high': return '📈';
      case 'medium': return '📊';
      case 'low': return '📉';
      default: return '📊';
    }
  };

  const getCompetitionColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="ai-recommendations-loading">
        <Brain size={48} className="animate-pulse" />
        <h3>L'IA analyse vos données...</h3>
        <p>Analyse des avis clients et génération de recommandations personnalisées</p>
      </div>
    );
  }

  return (
    <div className="ai-recommendations-manager">
      {/* En-tête */}
      <div className="ai-header">
        <div className="header-content">
          <div className="header-left">
            <div className="page-title">
              <Brain size={32} className="text-purple-600" />
              <div>
                <h1>Recommandations IA</h1>
                <p>Insights et suggestions basés sur l'analyse de vos avis clients</p>
              </div>
            </div>
          </div>

          <div className="header-actions">
            <Button onClick={loadAIRecommendations} className="refresh-btn">
              <RefreshCw size={16} />
              Actualiser l'analyse
            </Button>
            <Button className="export-btn">
              <Download size={16} />
              Exporter le rapport
            </Button>
          </div>
        </div>

        {/* Métriques rapides */}
        {aiInsights && (
          <div className="quick-metrics">
            <div className="metric-card sentiment">
              <div className="metric-icon">
                <ThumbsUp size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{aiInsights.overallSentiment.positive}%</div>
                <div className="metric-label">Avis positifs</div>
              </div>
            </div>

            <div className="metric-card improvements">
              <div className="metric-icon">
                <Lightbulb size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{improvements.length}</div>
                <div className="metric-label">Améliorations suggérées</div>
              </div>
            </div>

            <div className="metric-card new-products">
              <div className="metric-icon">
                <Plus size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{newProductSuggestions.length}</div>
                <div className="metric-label">Nouveaux produits</div>
              </div>
            </div>

            <div className="metric-card trends">
              <div className="metric-icon">
                <TrendingUp size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{marketTrends.length}</div>
                <div className="metric-label">Tendances détectées</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Onglets */}
      <div className="ai-tabs">
        <button
          className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          <BarChart3 size={16} />
          Vue d'ensemble
        </button>
        <button
          className={`tab ${activeTab === 'improvements' ? 'active' : ''}`}
          onClick={() => setActiveTab('improvements')}
        >
          <Lightbulb size={16} />
          Améliorations ({improvements.length})
        </button>
        <button
          className={`tab ${activeTab === 'new-products' ? 'active' : ''}`}
          onClick={() => setActiveTab('new-products')}
        >
          <Plus size={16} />
          Nouveaux produits ({newProductSuggestions.length})
        </button>
        <button
          className={`tab ${activeTab === 'trends' ? 'active' : ''}`}
          onClick={() => setActiveTab('trends')}
        >
          <TrendingUp size={16} />
          Tendances ({marketTrends.length})
        </button>
        <button
          className={`tab ${activeTab === 'insights' ? 'active' : ''}`}
          onClick={() => setActiveTab('insights')}
        >
          <Brain size={16} />
          Insights détaillés
        </button>
      </div>

      {/* Contenu des onglets */}
      <div className="ai-content">
        {activeTab === 'overview' && (
          <div className="overview-tab">
            {/* Résumé des recommandations prioritaires */}
            <Card>
              <CardBody>
                <h3>🚀 Actions prioritaires recommandées</h3>
                <div className="priority-actions">
                  {improvements
                    .filter(imp => imp.priority === 'urgent' || imp.priority === 'high')
                    .slice(0, 3)
                    .map(improvement => (
                      <div key={improvement.id} className="priority-action">
                        <div className="action-header">
                          {getPriorityIcon(improvement.priority)}
                          <span className="action-title">{improvement.suggestion}</span>
                        </div>
                        <div className="action-details">
                          <span className="product-name">{improvement.productName}</span>
                          <span className="impact">{improvement.potentialImpact}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </CardBody>
            </Card>

            {/* Opportunités de nouveaux produits */}
            <Card>
              <CardBody>
                <h3>💡 Opportunités de marché détectées</h3>
                <div className="market-opportunities">
                  {newProductSuggestions
                    .filter(suggestion => suggestion.marketDemand === 'high' || suggestion.marketDemand === 'very_high')
                    .slice(0, 2)
                    .map(suggestion => (
                      <div key={suggestion.id} className="opportunity-card">
                        <div className="opportunity-header">
                          <span className="demand-indicator">{getDemandIcon(suggestion.marketDemand)}</span>
                          <div>
                            <h4>{suggestion.suggestedName}</h4>
                            <p>{suggestion.description}</p>
                          </div>
                        </div>
                        <div className="opportunity-metrics">
                          <div className="metric">
                            <DollarSign size={14} />
                            <span>Revenus potentiels: {formatAmount(suggestion.potentialRevenue)}</span>
                          </div>
                          <div className="metric">
                            <Clock size={14} />
                            <span>Temps de développement: {suggestion.timeToMarket}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </CardBody>
            </Card>

            {/* Sentiment général */}
            {aiInsights && (
              <Card>
                <CardBody>
                  <h3>📊 Analyse du sentiment client</h3>
                  <div className="sentiment-analysis">
                    <div className="sentiment-chart">
                      <div className="sentiment-bar">
                        <div
                          className="sentiment-positive"
                          style={{ width: `${aiInsights.overallSentiment.positive}%` }}
                        ></div>
                        <div
                          className="sentiment-neutral"
                          style={{ width: `${aiInsights.overallSentiment.neutral}%` }}
                        ></div>
                        <div
                          className="sentiment-negative"
                          style={{ width: `${aiInsights.overallSentiment.negative}%` }}
                        ></div>
                      </div>
                      <div className="sentiment-labels">
                        <span className="positive">{aiInsights.overallSentiment.positive}% Positif</span>
                        <span className="neutral">{aiInsights.overallSentiment.neutral}% Neutre</span>
                        <span className="negative">{aiInsights.overallSentiment.negative}% Négatif</span>
                      </div>
                    </div>

                    <div className="sentiment-insights">
                      <div className="compliments">
                        <h4>👍 Points forts identifiés</h4>
                        <ul>
                          {aiInsights.topCompliments.slice(0, 3).map((compliment, index) => (
                            <li key={index}>{compliment}</li>
                          ))}
                        </ul>
                      </div>

                      <div className="complaints">
                        <h4>⚠️ Points d'amélioration</h4>
                        <ul>
                          {aiInsights.topComplaints.slice(0, 3).map((complaint, index) => (
                            <li key={index}>{complaint}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            )}
          </div>
        )}

        {activeTab === 'improvements' && (
          <div className="improvements-tab">
            <div className="tab-header">
              <h3>💡 Suggestions d'amélioration de vos produits</h3>
              <p>Basées sur l'analyse de {improvements.reduce((sum, imp) => sum + imp.basedOnReviews, 0)} avis clients</p>
            </div>

            <div className="improvements-list">
              {improvements.map(improvement => (
                <Card key={improvement.id} className="improvement-card">
                  <CardBody>
                    <div className="improvement-header">
                      <div className="improvement-info">
                        <div className="improvement-title">
                          {getPriorityIcon(improvement.priority)}
                          <h4>{improvement.productName}</h4>
                          <span className={`priority-badge ${improvement.priority}`}>
                            {improvement.priority}
                          </span>
                        </div>
                        <div className="improvement-type">
                          {improvement.improvementType === 'quality' && '🔧 Qualité'}
                          {improvement.improvementType === 'price' && '💰 Prix'}
                          {improvement.improvementType === 'features' && '✨ Fonctionnalités'}
                          {improvement.improvementType === 'design' && '🎨 Design'}
                          {improvement.improvementType === 'service' && '🤝 Service'}
                        </div>
                      </div>

                      <div className="improvement-actions">
                        <Button size="sm" variant="outline">
                          <Eye size={14} />
                          Détails
                        </Button>
                        <Button size="sm">
                          <CheckCircle size={14} />
                          Implémenter
                        </Button>
                      </div>
                    </div>

                    <div className="improvement-content">
                      <p className="suggestion">{improvement.suggestion}</p>

                      <div className="improvement-metrics">
                        <div className="metric">
                          <Users size={14} />
                          <span>Basé sur {improvement.basedOnReviews} avis</span>
                        </div>
                        <div className="metric">
                          <TrendingUp size={14} />
                          <span>{improvement.potentialImpact}</span>
                        </div>
                        <div className="metric">
                          <DollarSign size={14} />
                          <span>Coût: {improvement.estimatedCost}</span>
                        </div>
                        <div className="metric">
                          <Clock size={14} />
                          <span>{improvement.implementationTime}</span>
                        </div>
                      </div>

                      <div className="improvement-keywords">
                        {improvement.keywords.map((keyword, index) => (
                          <span key={index} className="keyword-tag">{keyword}</span>
                        ))}
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'new-products' && (
          <div className="new-products-tab">
            <div className="tab-header">
              <h3>🚀 Suggestions de nouveaux produits</h3>
              <p>Opportunités identifiées grâce à l'analyse des demandes clients</p>
            </div>

            <div className="suggestions-grid">
              {newProductSuggestions.map(suggestion => (
                <Card key={suggestion.id} className="suggestion-card">
                  <CardBody>
                    <div className="suggestion-header">
                      <div className="suggestion-title">
                        <span className="demand-indicator">{getDemandIcon(suggestion.marketDemand)}</span>
                        <h4>{suggestion.suggestedName}</h4>
                      </div>
                      <div className="suggestion-badges">
                        <span className={`demand-badge ${suggestion.marketDemand}`}>
                          {suggestion.marketDemand.replace('_', ' ')}
                        </span>
                        <span className={`competition-badge ${getCompetitionColor(suggestion.competitionLevel)}`}>
                          Concurrence {suggestion.competitionLevel}
                        </span>
                      </div>
                    </div>

                    <p className="suggestion-description">{suggestion.description}</p>

                    <div className="suggestion-metrics">
                      <div className="metric-row">
                        <div className="metric">
                          <DollarSign size={14} />
                          <span>Prix cible: {formatAmount(suggestion.targetPrice)}</span>
                        </div>
                        <div className="metric">
                          <TrendingUp size={14} />
                          <span>Revenus potentiels: {formatAmount(suggestion.potentialRevenue)}</span>
                        </div>
                      </div>
                      <div className="metric-row">
                        <div className="metric">
                          <Clock size={14} />
                          <span>Temps de développement: {suggestion.timeToMarket}</span>
                        </div>
                        <div className="metric">
                          <Zap size={14} />
                          <span>Complexité: {suggestion.developmentComplexity}</span>
                        </div>
                      </div>
                    </div>

                    <div className="target-audience">
                      <h5>🎯 Audience cible</h5>
                      <p>{suggestion.targetAudience}</p>
                    </div>

                    <div className="key-features">
                      <h5>✨ Caractéristiques clés</h5>
                      <div className="features-list">
                        {suggestion.keyFeatures.map((feature, index) => (
                          <span key={index} className="feature-tag">{feature}</span>
                        ))}
                      </div>
                    </div>

                    <div className="based-on-comments">
                      <h5>💬 Basé sur les commentaires</h5>
                      <div className="comments-list">
                        {suggestion.basedOnComments.slice(0, 2).map((comment, index) => (
                          <div key={index} className="comment-item">
                            <span className="quote-mark">"</span>
                            <span className="comment-text">{comment}</span>
                            <span className="quote-mark">"</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="suggestion-actions">
                      <Button variant="outline" size="sm">
                        <Eye size={14} />
                        Analyser en détail
                      </Button>
                      <Button size="sm">
                        <Plus size={14} />
                        Ajouter au plan de développement
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'trends' && (
          <div className="trends-tab">
            <div className="tab-header">
              <h3>📈 Tendances du marché</h3>
              <p>Analyse des tendances sectorielles et opportunités de croissance</p>
            </div>

            <div className="trends-list">
              {marketTrends.map(trend => (
                <Card key={trend.id} className="trend-card">
                  <CardBody>
                    <div className="trend-header">
                      <div className="trend-info">
                        <h4>{trend.trend}</h4>
                        <span className="trend-category">{trend.category}</span>
                      </div>
                      <div className="trend-metrics">
                        <div className="growth-indicator">
                          <TrendingUp size={16} />
                          <span>+{trend.growth}%</span>
                        </div>
                        <div className="relevance-score">
                          <Star size={16} />
                          <span>{trend.relevanceScore}/10</span>
                        </div>
                      </div>
                    </div>

                    <p className="trend-description">{trend.description}</p>

                    <div className="trend-opportunity">
                      <h5>🎯 Opportunité pour votre entreprise</h5>
                      <p>{trend.opportunity}</p>
                    </div>

                    <div className="trend-action">
                      <h5>⚡ Action recommandée</h5>
                      <p>{trend.actionRequired}</p>
                      <span className={`timeframe-badge ${trend.timeframe}`}>
                        {trend.timeframe === 'short' ? 'Court terme' :
                         trend.timeframe === 'medium' ? 'Moyen terme' : 'Long terme'}
                      </span>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'insights' && aiInsights && (
          <div className="insights-tab">
            <div className="tab-header">
              <h3>🧠 Insights détaillés</h3>
              <p>Analyse approfondie des retours clients et du marché</p>
            </div>

            <div className="insights-grid">
              {/* Perception des prix */}
              <Card>
                <CardBody>
                  <h4>💰 Perception des prix</h4>
                  <div className="price-perception">
                    <div className="perception-item">
                      <span className="perception-label">Trop cher</span>
                      <div className="perception-bar">
                        <div
                          className="perception-fill expensive"
                          style={{ width: `${aiInsights.pricePerceptions.tooExpensive}%` }}
                        ></div>
                      </div>
                      <span className="perception-value">{aiInsights.pricePerceptions.tooExpensive}%</span>
                    </div>
                    <div className="perception-item">
                      <span className="perception-label">Prix correct</span>
                      <div className="perception-bar">
                        <div
                          className="perception-fill fair"
                          style={{ width: `${aiInsights.pricePerceptions.fairPrice}%` }}
                        ></div>
                      </div>
                      <span className="perception-value">{aiInsights.pricePerceptions.fairPrice}%</span>
                    </div>
                    <div className="perception-item">
                      <span className="perception-label">Bon rapport qualité/prix</span>
                      <div className="perception-bar">
                        <div
                          className="perception-fill good-value"
                          style={{ width: `${aiInsights.pricePerceptions.goodValue}%` }}
                        ></div>
                      </div>
                      <span className="perception-value">{aiInsights.pricePerceptions.goodValue}%</span>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Besoins émergents */}
              <Card>
                <CardBody>
                  <h4>🌱 Besoins émergents détectés</h4>
                  <div className="emerging-needs">
                    {aiInsights.emergingNeeds.map((need, index) => (
                      <div key={index} className="need-item">
                        <Lightbulb size={16} />
                        <span>{need}</span>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>

              {/* Mentions de concurrents */}
              <Card>
                <CardBody>
                  <h4>🏆 Positionnement concurrentiel</h4>
                  <div className="competitor-mentions">
                    {aiInsights.competitorMentions.map((mention, index) => (
                      <div key={index} className="mention-item">
                        <Target size={16} />
                        <span>{mention}</span>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>

              {/* Top compliments détaillés */}
              <Card>
                <CardBody>
                  <h4>👍 Points forts reconnus</h4>
                  <div className="detailed-compliments">
                    {aiInsights.topCompliments.map((compliment, index) => (
                      <div key={index} className="compliment-item">
                        <CheckCircle size={16} className="text-green-500" />
                        <span>{compliment}</span>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>

              {/* Points d'amélioration détaillés */}
              <Card>
                <CardBody>
                  <h4>⚠️ Axes d'amélioration</h4>
                  <div className="detailed-complaints">
                    {aiInsights.topComplaints.map((complaint, index) => (
                      <div key={index} className="complaint-item">
                        <AlertCircle size={16} className="text-orange-500" />
                        <span>{complaint}</span>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIRecommendationsManager;
