import React, { useState, useEffect } from 'react';
import {
  Database, TrendingUp, MessageCircle, Heart, Share2,
  Star, Package, Users, BarChart3, Eye, CheckCircle
} from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import { RealBusinessDataService } from '../../services/realBusinessDataService';
import PostAnalysisBreakdown from './PostAnalysisBreakdown';

interface RealDataSummaryProps {
  businessId: string;
  isVisible: boolean;
}

const RealDataSummary: React.FC<RealDataSummaryProps> = ({ businessId, isVisible }) => {
  const [businessData, setBusinessData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isVisible && businessId) {
      loadBusinessData();
    }
  }, [isVisible, businessId]);

  const loadBusinessData = async () => {
    setLoading(true);
    try {
      const data = await RealBusinessDataService.getCompleteBusinessData(businessId);
      setBusinessData(data);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isVisible || !businessData) return null;

  const { posts, products, ratings, stats } = businessData;

  // Calculer les métriques d'engagement
  const totalLikes = ratings.reduce((sum: number, r: any) => sum + (r.likesCount || 0), 0);
  const totalShares = ratings.reduce((sum: number, r: any) => sum + (r.sharesCount || 0), 0);
  const totalRecommendations = ratings.reduce((sum: number, r: any) => sum + (r.recommendationsCount || 0), 0);
  const totalComments = ratings.reduce((sum: number, r: any) => sum + (r.commentsCount || 0), 0);

  // Analyser les types de posts
  const coupDeCoeur = posts.filter((p: any) => p.type === 'coup_de_coeur').length;
  const coupDeGueule = posts.filter((p: any) => p.type === 'coup_de_gueule').length;
  const businessPosts = ratings.filter((r: any) => r.source === 'business_post').length;

  // Calculer la note moyenne
  const ratingsWithValues = ratings.filter((r: any) => r.rating);
  const averageRating = ratingsWithValues.length > 0
    ? ratingsWithValues.reduce((sum: number, r: any) => sum + r.rating, 0) / ratingsWithValues.length
    : 0;

  return (
    <Card className="border-l-4 border-green-500 bg-green-50">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <Database className="text-green-600" size={24} />
          <div>
            <h3 className="text-lg font-semibold text-green-900">
              Données Réelles de Dexima
            </h3>
            <p className="text-green-700 text-sm">
              Analyse basée sur {businessData.metadata.totalDataPoints} points de données réels
            </p>
          </div>
        </div>
      </CardHeader>

      <CardBody>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            <span className="ml-2 text-green-700">Chargement des données...</span>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Métriques principales */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="flex items-center space-x-2">
                  <MessageCircle className="text-blue-600" size={20} />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{posts.length}</p>
                    <p className="text-sm text-gray-600">Posts</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="flex items-center space-x-2">
                  <Package className="text-purple-600" size={20} />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{products.length}</p>
                    <p className="text-sm text-gray-600">Produits</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="flex items-center space-x-2">
                  <Star className="text-yellow-600" size={20} />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{averageRating.toFixed(1)}</p>
                    <p className="text-sm text-gray-600">Note moyenne</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="flex items-center space-x-2">
                  <BarChart3 className="text-green-600" size={20} />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{ratings.length}</p>
                    <p className="text-sm text-gray-600">Interactions</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Analyse des posts */}
            <div className="bg-white rounded-lg p-4 border border-green-200">
              <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                <TrendingUp className="mr-2 text-blue-600" size={18} />
                Analyse des Posts
              </h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{coupDeCoeur}</div>
                  <div className="text-sm text-gray-600">Coups de cœur</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{coupDeGueule}</div>
                  <div className="text-sm text-gray-600">Coups de gueule</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{businessPosts}</div>
                  <div className="text-sm text-gray-600">Demandes d'avis</div>
                </div>
              </div>
            </div>

            {/* Engagement */}
            <div className="bg-white rounded-lg p-4 border border-green-200">
              <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                <Users className="mr-2 text-purple-600" size={18} />
                Engagement Client
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex items-center space-x-2">
                  <Heart className="text-red-500" size={16} />
                  <div>
                    <div className="font-semibold text-gray-900">{totalLikes}</div>
                    <div className="text-xs text-gray-600">J'aime</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Share2 className="text-blue-500" size={16} />
                  <div>
                    <div className="font-semibold text-gray-900">{totalShares}</div>
                    <div className="text-xs text-gray-600">Partages</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="text-green-500" size={16} />
                  <div>
                    <div className="font-semibold text-gray-900">{totalRecommendations}</div>
                    <div className="text-xs text-gray-600">Recommandations</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <MessageCircle className="text-purple-500" size={16} />
                  <div>
                    <div className="font-semibold text-gray-900">{totalComments}</div>
                    <div className="text-xs text-gray-600">Commentaires</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Qualité des données */}
            <div className="bg-white rounded-lg p-4 border border-green-200">
              <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                <Eye className="mr-2 text-indigo-600" size={18} />
                Qualité des Données
              </h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Posts analysés</span>
                  <span className="font-semibold text-gray-900">{posts.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Produits marketplace</span>
                  <span className="font-semibold text-gray-900">{products.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Interactions totales</span>
                  <span className="font-semibold text-gray-900">{ratings.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Points de données</span>
                  <span className="font-semibold text-green-600">{businessData.metadata.totalDataPoints}</span>
                </div>
              </div>
            </div>

            {/* Dernière mise à jour */}
            <div className="text-center text-sm text-gray-500">
              Dernière mise à jour : {new Date(businessData.metadata.lastUpdated).toLocaleString('fr-FR')}
            </div>
          </div>
        )}
      </CardBody>

      {/* Analyse détaillée des posts */}
      {!loading && businessData && (
        <div className="mt-6">
          <PostAnalysisBreakdown
            posts={businessData.posts}
            isVisible={true}
          />
        </div>
      )}
    </Card>
  );
};

export default RealDataSummary;
