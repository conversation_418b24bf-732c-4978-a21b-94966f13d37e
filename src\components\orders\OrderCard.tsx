import React, { useState } from 'react';
import {
  Package,
  Calendar,
  Truck,
  ChevronDown,
  MapPin,
  MessageCircle,
  Star,
  X,
  RefreshCw,
  Eye,
  Clock,
  Check,
  ShoppingBag,
  Activity
} from 'lucide-react';
import Button from '../ui/Button';
import { formatAmount } from '../../utils/formatUtils';
import OrderTrackingWidget from './OrderTrackingWidget';

// Types
interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  imageUrl: string;
  businessName: string;
  businessId: string;
  category: string;
  sku?: string;
}

interface TrackingEvent {
  date: string;
  status: string;
  description: string;
  location?: string;
}

interface Order {
  id: string;
  date: string;
  total: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  items: OrderItem[];
  trackingNumber?: string;
  estimatedDelivery?: string;
  shippingCost: number;
  paymentMethod: string;
  shippingAddress: {
    name: string;
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
  trackingHistory?: TrackingEvent[];
  canCancel: boolean;
  canReturn: boolean;
  hasReview: boolean;
}

interface OrderCardProps {
  order: Order;
  isExpanded: boolean;
  onToggleExpand: () => void;
}

const OrderCard: React.FC<OrderCardProps> = ({ order, isExpanded, onToggleExpand }) => {
  const [showTracking, setShowTracking] = useState(false);
  // Fonction pour traduire le statut de la commande
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return 'En attente';
      case 'processing': return 'En traitement';
      case 'shipped': return 'Expédiée';
      case 'delivered': return 'Livrée';
      case 'cancelled': return 'Annulée';
      default: return status;
    }
  };

  // Fonction pour obtenir l'icône du statut
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock size={16} />;
      case 'processing': return <Clock size={16} />;
      case 'shipped': return <Truck size={16} />;
      case 'delivered': return <Check size={16} />;
      case 'cancelled': return <X size={16} />;
      default: return <Clock size={16} />;
    }
  };

  // Fonction pour formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-200">
      {/* Header de la commande */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-blue-50 rounded-lg">
              <Package className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Commande #{order.id}</h3>
              <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                <div className="flex items-center space-x-1">
                  <Calendar size={14} />
                  <span>{formatDate(order.date)}</span>
                </div>
                {order.trackingNumber && (
                  <div className="flex items-center space-x-1">
                    <Truck size={14} />
                    <span>{order.trackingNumber}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
              order.status === 'delivered' ? 'bg-green-100 text-green-800' :
              order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
              order.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
              order.status === 'pending' ? 'bg-orange-100 text-orange-800' :
              'bg-red-100 text-red-800'
            }`}>
              {getStatusIcon(order.status)}
              <span className="ml-1">{getStatusLabel(order.status)}</span>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">Total</p>
              <p className="text-lg font-bold text-gray-900">{formatAmount(order.total)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Aperçu des articles */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-sm font-medium text-gray-900">Articles ({order.items.length})</h4>
          <Button
            variant="outline"
            size="sm"
            onClick={onToggleExpand}
            className="flex items-center space-x-2"
          >
            <span>{isExpanded ? 'Masquer' : 'Voir'} les détails</span>
            <ChevronDown className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
          </Button>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {order.items.slice(0, isExpanded ? order.items.length : 2).map(item => (
            <div key={item.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <img
                src={item.imageUrl}
                alt={item.name}
                className="w-12 h-12 object-cover rounded-lg"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'https://via.placeholder.com/48x48?text=IMG';
                }}
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">{item.name}</p>
                <p className="text-xs text-gray-500">{item.businessName}</p>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-xs text-gray-500">Qté: {item.quantity}</span>
                  <span className="text-xs font-medium text-gray-900">{formatAmount(item.price * item.quantity)}</span>
                </div>
              </div>
            </div>
          ))}
          {!isExpanded && order.items.length > 2 && (
            <div className="flex items-center justify-center p-3 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
              <span className="text-sm text-gray-500">+{order.items.length - 2} autres articles</span>
            </div>
          )}
        </div>
      </div>

      {/* Détails étendus */}
      {isExpanded && (
        <div className="border-t border-gray-100">
          {/* Informations de livraison et suivi */}
          <div className="p-6 bg-gray-50">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Informations de livraison */}
              <div className="bg-white rounded-lg p-4">
                <h5 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                  <MapPin className="w-4 h-4 mr-2 text-gray-500" />
                  Informations de livraison
                </h5>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Destinataire:</span> {order.shippingAddress.name}</p>
                  <p><span className="font-medium">Adresse:</span> {order.shippingAddress.street}</p>
                  <p>{order.shippingAddress.city}, {order.shippingAddress.postalCode}</p>
                  <p>{order.shippingAddress.country}</p>
                  {order.estimatedDelivery && (
                    <p className="text-blue-600">
                      <span className="font-medium">Livraison estimée:</span> {formatDate(order.estimatedDelivery)}
                    </p>
                  )}
                </div>
              </div>

              {/* Suivi de commande */}
              {order.trackingHistory && (
                <div className="bg-white rounded-lg p-4">
                  <h5 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <Truck className="w-4 h-4 mr-2 text-gray-500" />
                    Suivi de commande
                  </h5>
                  <div className="space-y-3">
                    {order.trackingHistory.map((event, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className={`w-2 h-2 rounded-full mt-2 ${
                          index === 0 ? 'bg-green-500' : 'bg-gray-300'
                        }`}></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">{event.status}</p>
                          <p className="text-xs text-gray-500">{event.description}</p>
                          <p className="text-xs text-gray-400">{formatDate(event.date)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="p-6 bg-white border-t border-gray-100">
            <div className="flex flex-wrap gap-3">
              <Button
                variant="primary"
                className="flex items-center space-x-2"
                onClick={() => setShowTracking(!showTracking)}
              >
                <Activity size={16} />
                <span>{showTracking ? 'Masquer' : 'Suivi en temps réel'}</span>
              </Button>

              <Button variant="outline" className="flex items-center space-x-2">
                <MessageCircle size={16} />
                <span>Contacter le vendeur</span>
              </Button>

              {order.status === 'delivered' && !order.hasReview && (
                <Button variant="outline" className="flex items-center space-x-2">
                  <Star size={16} />
                  <span>Laisser un avis</span>
                </Button>
              )}

              {order.canCancel && (
                <Button variant="outline" className="flex items-center space-x-2 text-red-600 border-red-300 hover:bg-red-50">
                  <X size={16} />
                  <span>Annuler la commande</span>
                </Button>
              )}

              {order.canReturn && (
                <Button variant="outline" className="flex items-center space-x-2">
                  <RefreshCw size={16} />
                  <span>Retourner</span>
                </Button>
              )}

              <Button variant="outline" className="flex items-center space-x-2">
                <Eye size={16} />
                <span>Voir la facture</span>
              </Button>
            </div>
          </div>

          {/* Widget de suivi en temps réel */}
          {showTracking && (
            <div className="p-6 bg-gray-50 border-t border-gray-100">
              <OrderTrackingWidget
                orderId={order.id}
                className="shadow-none border-0"
                showNotifications={false}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default OrderCard;
