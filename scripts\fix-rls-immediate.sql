-- =====================================================
-- CORRECTION IMMÉDIATE DES POLITIQUES RLS
-- Script à exécuter dans l'éditeur SQL de Supabase
-- =====================================================

-- 1. Supprimer toutes les politiques problématiques
DROP POLICY IF EXISTS "Everyone can view ad engagement metrics" ON ad_engagement_metrics;
DROP POLICY IF EXISTS "Users can add comments on ads" ON ad_comments;
DROP POLICY IF EXISTS "Users can like ads" ON ad_likes;
DROP POLICY IF EXISTS "Users can unlike their own likes" ON ad_likes;
DROP POLICY IF EXISTS "Users can share ads" ON ad_shares;
DROP POLICY IF EXISTS "business_cannot_comment_ads" ON ad_comments;
DROP POLICY IF EXISTS "regular_users_can_comment_ads" ON ad_comments;

-- 2. <PERSON><PERSON>er des politiques permissives pour ad_engagement_metrics
CREATE POLICY "allow_all_ad_metrics_operations" ON ad_engagement_metrics
  FOR ALL TO authenticated
  USING (true)
  WITH CHECK (true);

-- 3. Politiques pour ad_likes (permettre aux utilisateurs non-entreprise)
CREATE POLICY "authenticated_can_view_ad_likes" ON ad_likes
  FOR SELECT TO authenticated
  USING (true);

CREATE POLICY "regular_users_can_like_ads" ON ad_likes
  FOR INSERT TO authenticated
  WITH CHECK (
    auth.uid() = user_id 
    AND NOT EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'business'
    )
  );

CREATE POLICY "users_can_unlike_ads" ON ad_likes
  FOR DELETE TO authenticated
  USING (auth.uid() = user_id);

-- 4. Politiques pour ad_comments
CREATE POLICY "authenticated_can_view_ad_comments" ON ad_comments
  FOR SELECT TO authenticated
  USING (is_approved = true);

CREATE POLICY "regular_users_can_comment_ads_new" ON ad_comments
  FOR INSERT TO authenticated
  WITH CHECK (
    auth.uid() = user_id 
    AND NOT EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'business'
    )
  );

CREATE POLICY "users_can_update_own_ad_comments" ON ad_comments
  FOR UPDATE TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "users_can_delete_own_ad_comments" ON ad_comments
  FOR DELETE TO authenticated
  USING (auth.uid() = user_id);

-- 5. Politiques pour ad_shares
CREATE POLICY "authenticated_can_view_ad_shares" ON ad_shares
  FOR SELECT TO authenticated
  USING (true);

CREATE POLICY "users_can_share_ads_new" ON ad_shares
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- 6. Vérifier que RLS est activé
ALTER TABLE ad_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_engagement_metrics ENABLE ROW LEVEL SECURITY;

-- 7. Afficher les politiques créées
SELECT 
  tablename,
  policyname,
  cmd,
  permissive
FROM pg_policies 
WHERE tablename IN ('ad_likes', 'ad_comments', 'ad_shares', 'ad_engagement_metrics')
ORDER BY tablename, policyname;

-- Message de confirmation
SELECT 'Politiques RLS corrigées avec succès ! Testez maintenant les boutons.' as status;
