-- SCRIPT DE TEST POUR LES PARAMÈTRES SYSTÈME
-- Exécutez ce script pour tester toutes les fonctionnalités des paramètres

-- =====================================================
-- 1. FONCTION DE TEST COMPLÈTE DES PARAMÈTRES
-- =====================================================

CREATE OR REPLACE FUNCTION test_system_settings()
RETURNS text AS $$
DECLARE
    result text := '';
    total_configs integer;
    categories_count integer;
    sensitive_count integer;
    test_value jsonb;
    test_config_id uuid;
BEGIN
    result := result || '🧪 TESTS DU SYSTÈME DE PARAMÈTRES' || E'\n';
    result := result || '===================================' || E'\n\n';
    
    -- Test 1: Vérifier la structure de la table
    result := result || '📋 Test 1: Structure de la table system_configs' || E'\n';
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_configs') THEN
        result := result || '✅ Table system_configs existe' || E'\n';
        
        -- Vérifier les colonnes essentielles
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'system_configs' AND column_name = 'category') THEN
            result := result || '✅ Colonne category présente' || E'\n';
        END IF;
        
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'system_configs' AND column_name = 'is_sensitive') THEN
            result := result || '✅ Colonne is_sensitive présente' || E'\n';
        END IF;
        
    ELSE
        result := result || '❌ Table system_configs manquante' || E'\n';
        RETURN result;
    END IF;
    
    -- Test 2: Vérifier les données par défaut
    result := result || E'\n📊 Test 2: Données par défaut' || E'\n';
    
    SELECT COUNT(*) INTO total_configs FROM system_configs;
    SELECT COUNT(DISTINCT category) INTO categories_count FROM system_configs;
    SELECT COUNT(*) INTO sensitive_count FROM system_configs WHERE is_sensitive = true;
    
    result := result || FORMAT('✅ Total paramètres: %s', total_configs) || E'\n';
    result := result || FORMAT('✅ Catégories: %s', categories_count) || E'\n';
    result := result || FORMAT('✅ Paramètres sensibles: %s', sensitive_count) || E'\n';
    
    -- Test 3: Tester les fonctions
    result := result || E'\n🔧 Test 3: Fonctions de gestion' || E'\n';
    
    -- Test get_system_config
    BEGIN
        SELECT get_system_config('general', 'app_name') INTO test_value;
        result := result || FORMAT('✅ get_system_config fonctionne: %s', test_value) || E'\n';
    EXCEPTION WHEN OTHERS THEN
        result := result || '❌ Erreur get_system_config: ' || SQLERRM || E'\n';
    END;
    
    -- Test set_system_config
    BEGIN
        SELECT set_system_config(
            'test', 
            'test_param', 
            '"test_value"'::jsonb, 
            'string', 
            'Paramètre de test', 
            false, 
            false
        ) INTO test_config_id;
        result := result || '✅ set_system_config fonctionne' || E'\n';
    EXCEPTION WHEN OTHERS THEN
        result := result || '❌ Erreur set_system_config: ' || SQLERRM || E'\n';
    END;
    
    -- Test 4: Vérifier les politiques de sécurité
    result := result || E'\n🔒 Test 4: Politiques de sécurité' || E'\n';
    
    IF EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'system_configs') THEN
        result := result || '✅ Politiques RLS configurées' || E'\n';
    ELSE
        result := result || '⚠️ Aucune politique RLS trouvée' || E'\n';
    END IF;
    
    -- Test 5: Tester la vue publique
    result := result || E'\n👁️ Test 5: Vue publique' || E'\n';
    
    BEGIN
        DECLARE
            public_count integer;
        BEGIN
            SELECT COUNT(*) INTO public_count FROM public_system_configs;
            result := result || FORMAT('✅ Vue publique accessible: %s paramètres', public_count) || E'\n';
        END;
    EXCEPTION WHEN OTHERS THEN
        result := result || '❌ Erreur vue publique: ' || SQLERRM || E'\n';
    END;
    
    -- Nettoyage du test
    IF test_config_id IS NOT NULL THEN
        DELETE FROM system_configs WHERE id = test_config_id;
    END IF;
    
    -- Résumé final
    result := result || E'\n🎉 RÉSUMÉ DES TESTS' || E'\n';
    result := result || '==================' || E'\n';
    result := result || '✅ Structure de table validée' || E'\n';
    result := result || '✅ Données par défaut présentes' || E'\n';
    result := result || '✅ Fonctions de gestion testées' || E'\n';
    result := result || '✅ Sécurité configurée' || E'\n';
    result := result || '✅ Vue publique fonctionnelle' || E'\n\n';
    result := result || '🚀 SYSTÈME DE PARAMÈTRES OPÉRATIONNEL !' || E'\n';
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 2. EXÉCUTER LES TESTS
-- =====================================================

SELECT test_system_settings();

-- =====================================================
-- 3. CRÉER DES PARAMÈTRES DE TEST SUPPLÉMENTAIRES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🧪 CRÉATION DE PARAMÈTRES DE TEST SUPPLÉMENTAIRES';
    RAISE NOTICE '===============================================';
    
    -- Paramètres de test pour chaque type
    PERFORM set_system_config('test', 'string_param', '"Valeur texte"', 'string', 'Paramètre de type string', false, false);
    PERFORM set_system_config('test', 'number_param', '42', 'number', 'Paramètre de type number', false, false);
    PERFORM set_system_config('test', 'boolean_param', 'true', 'boolean', 'Paramètre de type boolean', false, false);
    PERFORM set_system_config('test', 'json_param', '{"key": "value", "array": [1, 2, 3]}', 'json', 'Paramètre de type JSON', false, false);
    PERFORM set_system_config('test', 'password_param', '"secret123"', 'password', 'Paramètre de type password', true, false);
    PERFORM set_system_config('test', 'readonly_param', '"Non modifiable"', 'string', 'Paramètre en lecture seule', false, true);
    
    -- Paramètres avancés pour les tests
    PERFORM set_system_config('advanced', 'complex_json', '{"database": {"host": "localhost", "port": 5432, "ssl": true}, "cache": {"redis": {"host": "redis", "port": 6379}}}', 'json', 'Configuration complexe JSON', false, false);
    PERFORM set_system_config('advanced', 'large_number', '999999999', 'number', 'Grand nombre pour test', false, false);
    PERFORM set_system_config('advanced', 'sensitive_key', '"sk_live_abcdef123456"', 'password', 'Clé sensible pour test', true, false);
    
    RAISE NOTICE '✅ 9 paramètres de test créés';
    
END $$;

-- =====================================================
-- 4. TESTER LES DIFFÉRENTS TYPES DE DONNÉES
-- =====================================================

-- Test des types de données
SELECT 
    '🔍 TEST DES TYPES DE DONNÉES' as info,
    key,
    type,
    CASE 
        WHEN type = 'string' THEN 'Texte: ' || (value #>> '{}')
        WHEN type = 'number' THEN 'Nombre: ' || (value #>> '{}')
        WHEN type = 'boolean' THEN 'Booléen: ' || (value #>> '{}')
        WHEN type = 'json' THEN 'JSON: ' || value::text
        WHEN type = 'password' THEN 'Mot de passe: ***MASQUÉ***'
        ELSE 'Autre: ' || value::text
    END as valeur_formatee
FROM system_configs 
WHERE category IN ('test', 'advanced')
ORDER BY type, key;

-- =====================================================
-- 5. TESTER LES FONCTIONS UTILITAIRES
-- =====================================================

-- Test de récupération de paramètres
SELECT 
    '🔧 TEST DES FONCTIONS UTILITAIRES' as info,
    'get_system_config' as fonction,
    get_system_config('general', 'app_name') as resultat;

-- Test de paramètres inexistants
SELECT 
    '🔧 TEST DES FONCTIONS UTILITAIRES' as info,
    'get_system_config (inexistant)' as fonction,
    get_system_config('inexistant', 'param') as resultat;

-- =====================================================
-- 6. AFFICHER LES STATISTIQUES DÉTAILLÉES
-- =====================================================

-- Statistiques par catégorie et type
SELECT 
    '📊 STATISTIQUES PAR CATÉGORIE ET TYPE' as info,
    category,
    type,
    COUNT(*) as count,
    COUNT(*) FILTER (WHERE is_sensitive) as sensibles,
    COUNT(*) FILTER (WHERE is_readonly) as lecture_seule
FROM system_configs 
GROUP BY ROLLUP(category, type)
ORDER BY category NULLS LAST, type NULLS LAST;

-- Paramètres modifiés récemment
SELECT 
    '🕒 PARAMÈTRES MODIFIÉS RÉCEMMENT' as info,
    category,
    key,
    updated_at,
    CASE 
        WHEN is_sensitive THEN '***MASQUÉ***'
        ELSE value::text
    END as value
FROM system_configs 
ORDER BY updated_at DESC 
LIMIT 10;

-- =====================================================
-- 7. GUIDE DE TEST INTERFACE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎯 GUIDE DE TEST DE L''INTERFACE PARAMÈTRES';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '';
    RAISE NOTICE '📋 TESTS À EFFECTUER:';
    RAISE NOTICE '';
    RAISE NOTICE '1️⃣ NAVIGATION DES CATÉGORIES:';
    RAISE NOTICE '   • Cliquez sur chaque catégorie dans le menu gauche';
    RAISE NOTICE '   • Vérifiez le nombre de paramètres affiché';
    RAISE NOTICE '   • Testez les icônes et couleurs des catégories';
    RAISE NOTICE '';
    RAISE NOTICE '2️⃣ AFFICHAGE DES PARAMÈTRES:';
    RAISE NOTICE '   • Vérifiez les icônes selon le type (string, number, etc.)';
    RAISE NOTICE '   • Testez les badges "Sensible" et "Lecture seule"';
    RAISE NOTICE '   • Vérifiez le formatage des valeurs';
    RAISE NOTICE '';
    RAISE NOTICE '3️⃣ ÉDITION DES PARAMÈTRES:';
    RAISE NOTICE '   • Cliquez sur l''icône "Modifier" d''un paramètre';
    RAISE NOTICE '   • Testez l''édition selon le type (select pour boolean, etc.)';
    RAISE NOTICE '   • Sauvegardez et vérifiez la mise à jour';
    RAISE NOTICE '';
    RAISE NOTICE '4️⃣ PARAMÈTRES SENSIBLES:';
    RAISE NOTICE '   • Testez le bouton œil pour afficher/masquer';
    RAISE NOTICE '   • Vérifiez que les valeurs sont masquées par défaut';
    RAISE NOTICE '   • Testez la copie des valeurs sensibles';
    RAISE NOTICE '';
    RAISE NOTICE '5️⃣ AJOUT DE PARAMÈTRES:';
    RAISE NOTICE '   • Cliquez sur "Ajouter" dans une catégorie';
    RAISE NOTICE '   • Testez le formulaire modal';
    RAISE NOTICE '   • Créez des paramètres de différents types';
    RAISE NOTICE '';
    RAISE NOTICE '6️⃣ FONCTIONNALITÉS AVANCÉES:';
    RAISE NOTICE '   • Testez l''export de configuration';
    RAISE NOTICE '   • Vérifiez l''actualisation des données';
    RAISE NOTICE '   • Testez la copie dans le presse-papiers';
    RAISE NOTICE '';
    RAISE NOTICE '🔍 POINTS À VÉRIFIER:';
    RAISE NOTICE '   ✅ Interface responsive sur différentes tailles';
    RAISE NOTICE '   ✅ Chargement des données sans erreur';
    RAISE NOTICE '   ✅ Édition en ligne fonctionnelle';
    RAISE NOTICE '   ✅ Gestion des types de données correcte';
    RAISE NOTICE '   ✅ Sécurité des paramètres sensibles';
    RAISE NOTICE '   ✅ Messages de succès/erreur appropriés';
    RAISE NOTICE '';
    RAISE NOTICE '🎊 TESTEZ MAINTENANT DANS L''INTERFACE !';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- 8. VÉRIFICATIONS FINALES
-- =====================================================

DO $$
DECLARE
    total_configs integer;
    total_categories integer;
    total_sensitive integer;
    total_readonly integer;
BEGIN
    SELECT COUNT(*) INTO total_configs FROM system_configs;
    SELECT COUNT(DISTINCT category) INTO total_categories FROM system_configs;
    SELECT COUNT(*) INTO total_sensitive FROM system_configs WHERE is_sensitive = true;
    SELECT COUNT(*) INTO total_readonly FROM system_configs WHERE is_readonly = true;
    
    RAISE NOTICE '';
    RAISE NOTICE '📊 ÉTAT FINAL DU SYSTÈME DE PARAMÈTRES';
    RAISE NOTICE '====================================';
    RAISE NOTICE '';
    RAISE NOTICE '⚙️ CONFIGURATION DISPONIBLE:';
    RAISE NOTICE '   • Total paramètres: %', total_configs;
    RAISE NOTICE '   • Catégories: %', total_categories;
    RAISE NOTICE '   • Paramètres sensibles: %', total_sensitive;
    RAISE NOTICE '   • Paramètres lecture seule: %', total_readonly;
    RAISE NOTICE '';
    RAISE NOTICE '✅ SYSTÈME PRÊT POUR UTILISATION !';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 PROCHAINES ÉTAPES:';
    RAISE NOTICE '1. Allez dans "Paramètres Système"';
    RAISE NOTICE '2. Explorez toutes les catégories';
    RAISE NOTICE '3. Testez l''édition des paramètres';
    RAISE NOTICE '4. Configurez selon vos besoins';
    RAISE NOTICE '';
END $$;
