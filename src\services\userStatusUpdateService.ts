import { supabase } from '../lib/supabase';
import { UserStatus } from '../types';
import { UserStatusService } from './userStatusService';

export interface UserMetrics {
  profileComplete: boolean;
  postCount: number;
  commentCount: number;
  likesReceived: number;
  followersCount: number;
  recommendationsCount: number;
  accountAgeDays: number;
  qualityScore: number;
  engagementRate: number;
}

export class UserStatusUpdateService {
  /**
   * Récupère les métriques complètes d'un utilisateur
   */
  static async getUserMetrics(userId: string): Promise<UserMetrics> {
    try {
      // Récupérer le profil utilisateur
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError || !profile) {
        throw new Error('Profil utilisateur introuvable');
      }

      // Vérifier si le profil est complet
      const profileComplete = !!(
        profile.profile_picture &&
        profile.cover_photo_url &&
        profile.country &&
        profile.city
      );

      // Calculer l'âge du compte en jours
      const accountAgeDays = Math.floor(
        (Date.now() - new Date(profile.created_at).getTime()) / (1000 * 60 * 60 * 24)
      );

      // Récupérer le nombre de posts
      const { count: postCount } = await supabase
        .from('posts')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      // Récupérer le nombre de commentaires
      const { count: commentCount } = await supabase
        .from('comments')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      // Récupérer le nombre de likes reçus sur les posts
      const { data: postsWithLikes } = await supabase
        .from('posts')
        .select('likes')
        .eq('user_id', userId);

      let likesReceived = 0;
      if (postsWithLikes) {
        likesReceived = postsWithLikes.reduce((total, post) => {
          const likes = Array.isArray(post.likes) ? post.likes.length : 0;
          return total + likes;
        }, 0);
      }

      // Récupérer le nombre de recommandations reçues
      const { data: postsWithRecommendations } = await supabase
        .from('posts')
        .select('recommendations')
        .eq('user_id', userId);

      let recommendationsCount = 0;
      if (postsWithRecommendations) {
        recommendationsCount = postsWithRecommendations.reduce((total, post) => {
          const recommendations = Array.isArray(post.recommendations) ? post.recommendations.length : 0;
          return total + recommendations;
        }, 0);
      }

      // Récupérer le nombre de followers
      const { count: followersCount } = await supabase
        .from('follows')
        .select('*', { count: 'exact', head: true })
        .eq('following_id', userId);

      // Calculer le score de qualité (basé sur le ratio likes/posts)
      const qualityScore = postCount > 0 ? Math.min(10, Math.round((likesReceived / postCount) * 2)) : 0;

      // Calculer le taux d'engagement (basé sur l'activité récente)
      const engagementRate = this.calculateEngagementRate(
        postCount || 0,
        commentCount || 0,
        likesReceived,
        accountAgeDays
      );

      return {
        profileComplete,
        postCount: postCount || 0,
        commentCount: commentCount || 0,
        likesReceived,
        followersCount: followersCount || 0,
        recommendationsCount,
        accountAgeDays,
        qualityScore,
        engagementRate
      };
    } catch (error) {
      console.error('Erreur lors de la récupération des métriques:', error);
      throw error;
    }
  }

  /**
   * Calcule le taux d'engagement d'un utilisateur
   */
  private static calculateEngagementRate(
    postCount: number,
    commentCount: number,
    likesReceived: number,
    accountAgeDays: number
  ): number {
    if (accountAgeDays === 0) return 0;

    // Calculer l'activité totale
    const totalActivity = postCount + commentCount + (likesReceived * 0.1);
    
    // Calculer le taux d'engagement par jour
    const dailyEngagement = totalActivity / Math.max(accountAgeDays, 1);
    
    // Convertir en pourcentage (max 100%)
    return Math.min(100, Math.round(dailyEngagement * 10));
  }

  /**
   * Détermine le statut approprié basé sur les métriques
   */
  static determineUserStatus(metrics: UserMetrics): UserStatus {
    // Vérifier LEADER
    if (
      metrics.profileComplete &&
      metrics.postCount >= 200 &&
      metrics.commentCount >= 500 &&
      metrics.likesReceived >= 2000 &&
      metrics.followersCount >= 500 &&
      metrics.recommendationsCount >= 1000 &&
      metrics.accountAgeDays >= 180 &&
      metrics.qualityScore >= 9 &&
      metrics.engagementRate >= 25
    ) {
      return UserStatus.LEADER;
    }

    // Vérifier INFLUENCER
    if (
      metrics.profileComplete &&
      metrics.postCount >= 50 &&
      metrics.commentCount >= 100 &&
      metrics.likesReceived >= 500 &&
      metrics.followersCount >= 100 &&
      metrics.recommendationsCount >= 200 &&
      metrics.accountAgeDays >= 90 &&
      metrics.qualityScore >= 8 &&
      metrics.engagementRate >= 15
    ) {
      return UserStatus.INFLUENCER;
    }

    // Vérifier DISCOVERER
    if (
      metrics.profileComplete &&
      metrics.postCount >= 10 &&
      metrics.commentCount >= 25 &&
      metrics.likesReceived >= 50 &&
      metrics.followersCount >= 20 &&
      metrics.accountAgeDays >= 30
    ) {
      return UserStatus.DISCOVERER;
    }

    // Vérifier CONTRIBUTOR
    if (
      metrics.profileComplete &&
      (metrics.postCount >= 1 || metrics.commentCount >= 1)
    ) {
      return UserStatus.CONTRIBUTOR;
    }

    // Vérifier MEMBER
    if (metrics.profileComplete) {
      return UserStatus.MEMBER;
    }

    // Par défaut NEWBIE
    return UserStatus.NEWBIE;
  }

  /**
   * Met à jour le statut d'un utilisateur si nécessaire
   */
  static async updateUserStatusIfNeeded(userId: string): Promise<{
    updated: boolean;
    oldStatus?: UserStatus;
    newStatus?: UserStatus;
    message?: string;
  }> {
    try {
      // Récupérer le statut actuel
      const { data: currentProfile, error: profileError } = await supabase
        .from('profiles')
        .select('status')
        .eq('id', userId)
        .single();

      if (profileError || !currentProfile) {
        throw new Error('Profil utilisateur introuvable');
      }

      const currentStatus = currentProfile.status as UserStatus;

      // Récupérer les métriques
      const metrics = await this.getUserMetrics(userId);

      // Déterminer le nouveau statut
      const newStatus = this.determineUserStatus(metrics);

      // Vérifier si une mise à jour est nécessaire
      if (currentStatus === newStatus) {
        return {
          updated: false,
          message: 'Aucune mise à jour nécessaire'
        };
      }

      // Vérifier que c'est une progression (pas de régression)
      const statusOrder = [
        UserStatus.NEWBIE,
        UserStatus.MEMBER,
        UserStatus.CONTRIBUTOR,
        UserStatus.DISCOVERER,
        UserStatus.INFLUENCER,
        UserStatus.LEADER
      ];

      const currentIndex = statusOrder.indexOf(currentStatus);
      const newIndex = statusOrder.indexOf(newStatus);

      if (newIndex <= currentIndex) {
        return {
          updated: false,
          message: 'Pas de progression de statut détectée'
        };
      }

      // Mettre à jour le statut dans la base de données
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (updateError) {
        throw updateError;
      }

      return {
        updated: true,
        oldStatus: currentStatus,
        newStatus,
        message: `Félicitations ! Vous êtes passé de ${UserStatusService.getStatusLevel(currentStatus)?.name} à ${UserStatusService.getStatusLevel(newStatus)?.name} !`
      };

    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error);
      throw error;
    }
  }

  /**
   * Vérifie et met à jour le statut après une action utilisateur
   */
  static async checkAndUpdateAfterAction(
    userId: string,
    action: 'post_created' | 'comment_created' | 'like_received' | 'follow_received' | 'profile_updated'
  ): Promise<void> {
    try {
      // Attendre un peu pour que les données soient cohérentes
      await new Promise(resolve => setTimeout(resolve, 1000));

      const result = await this.updateUserStatusIfNeeded(userId);

      if (result.updated && result.newStatus && result.message) {
        // Envoyer une notification à l'utilisateur
        await this.sendStatusUpdateNotification(userId, result.newStatus, result.message);
        
        console.log(`🎉 Statut mis à jour pour l'utilisateur ${userId}: ${result.oldStatus} → ${result.newStatus}`);
      }
    } catch (error) {
      console.error('Erreur lors de la vérification du statut après action:', error);
    }
  }

  /**
   * Envoie une notification de mise à jour de statut
   */
  private static async sendStatusUpdateNotification(
    userId: string,
    newStatus: UserStatus,
    message: string
  ): Promise<void> {
    try {
      const statusLevel = UserStatusService.getStatusLevel(newStatus);
      
      await supabase
        .from('notifications')
        .insert({
          user_id: userId,
          type: 'status_update',
          title: '🎉 Nouveau statut débloqué !',
          message,
          data: {
            new_status: newStatus,
            status_name: statusLevel?.name,
            benefits: statusLevel?.benefits
          },
          read: false,
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('Erreur lors de l\'envoi de la notification:', error);
    }
  }

  /**
   * Vérifie tous les utilisateurs et met à jour leurs statuts (fonction d'administration)
   */
  static async updateAllUsersStatus(): Promise<{
    processed: number;
    updated: number;
    errors: number;
  }> {
    try {
      const { data: users, error } = await supabase
        .from('profiles')
        .select('id');

      if (error || !users) {
        throw error;
      }

      let processed = 0;
      let updated = 0;
      let errors = 0;

      for (const user of users) {
        try {
          const result = await this.updateUserStatusIfNeeded(user.id);
          processed++;
          if (result.updated) {
            updated++;
          }
        } catch (error) {
          errors++;
          console.error(`Erreur pour l'utilisateur ${user.id}:`, error);
        }
      }

      return { processed, updated, errors };
    } catch (error) {
      console.error('Erreur lors de la mise à jour globale:', error);
      throw error;
    }
  }
}
