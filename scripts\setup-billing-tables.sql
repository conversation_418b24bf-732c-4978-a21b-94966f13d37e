-- Script pour créer les tables de facturation publicitaire
-- Exécutez ce script dans l'éditeur SQL de Supabase

-- 1. Table des portefeuilles publicitaires
CREATE TABLE IF NOT EXISTS ad_wallets (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  balance numeric DEFAULT 0 CHECK (balance >= 0),
  total_spent numeric DEFAULT 0 CHECK (total_spent >= 0),
  total_recharged numeric DEFAULT 0 CHECK (total_recharged >= 0),
  currency text NOT NULL DEFAULT 'XOF',
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'blocked')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Contraintes
  UNIQUE(business_id)
);

-- 2. Table des transactions publicitaires
CREATE TABLE IF NOT EXISTS ad_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  wallet_id uuid NOT NULL REFERENCES ad_wallets(id) ON DELETE CASCADE,
  campaign_id text, -- Réf<PERSON>rence à la campagne (peut être null pour les recharges)
  type text NOT NULL CHECK (type IN ('recharge', 'spend', 'refund', 'bonus')),
  amount numeric NOT NULL CHECK (amount > 0),
  description text NOT NULL,
  payment_method text,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 3. Table des méthodes de paiement
CREATE TABLE IF NOT EXISTS payment_methods (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('mobile_money', 'bank_card', 'bank_transfer', 'crypto')),
  provider text NOT NULL, -- Orange Money, MTN Money, Visa, etc.
  account_number text NOT NULL,
  account_name text NOT NULL,
  is_default boolean DEFAULT false,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 4. Table des alertes de facturation
CREATE TABLE IF NOT EXISTS billing_alerts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('low_balance', 'campaign_paused', 'payment_failed', 'budget_exceeded')),
  message text NOT NULL,
  threshold numeric,
  is_read boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- 5. Table des factures publicitaires
CREATE TABLE IF NOT EXISTS ad_invoices (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  campaign_id text,
  invoice_number text UNIQUE NOT NULL,
  total_amount numeric NOT NULL CHECK (total_amount >= 0),
  currency text NOT NULL DEFAULT 'XOF',
  status text NOT NULL DEFAULT 'generated' CHECK (status IN ('generated', 'sent', 'paid', 'cancelled')),
  due_date timestamptz,
  paid_at timestamptz,
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Activation RLS
ALTER TABLE ad_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_invoices ENABLE ROW LEVEL SECURITY;

-- Politiques pour ad_wallets
CREATE POLICY "Les entreprises peuvent voir leur propre wallet"
  ON ad_wallets
  FOR SELECT
  USING (business_id = auth.uid());

CREATE POLICY "Les entreprises peuvent mettre à jour leur propre wallet"
  ON ad_wallets
  FOR UPDATE
  USING (business_id = auth.uid())
  WITH CHECK (business_id = auth.uid());

CREATE POLICY "Les entreprises peuvent créer leur propre wallet"
  ON ad_wallets
  FOR INSERT
  WITH CHECK (business_id = auth.uid());

-- Politiques pour ad_transactions
CREATE POLICY "Les entreprises peuvent voir leurs propres transactions"
  ON ad_transactions
  FOR SELECT
  USING (
    wallet_id IN (
      SELECT id FROM ad_wallets WHERE business_id = auth.uid()
    )
  );

CREATE POLICY "Les entreprises peuvent créer leurs propres transactions"
  ON ad_transactions
  FOR INSERT
  WITH CHECK (
    wallet_id IN (
      SELECT id FROM ad_wallets WHERE business_id = auth.uid()
    )
  );

-- Politiques pour payment_methods
CREATE POLICY "Les entreprises peuvent voir leurs propres méthodes de paiement"
  ON payment_methods
  FOR SELECT
  USING (business_id = auth.uid());

CREATE POLICY "Les entreprises peuvent gérer leurs propres méthodes de paiement"
  ON payment_methods
  FOR ALL
  USING (business_id = auth.uid())
  WITH CHECK (business_id = auth.uid());

-- Politiques pour billing_alerts
CREATE POLICY "Les entreprises peuvent voir leurs propres alertes"
  ON billing_alerts
  FOR SELECT
  USING (business_id = auth.uid());

CREATE POLICY "Les entreprises peuvent gérer leurs propres alertes"
  ON billing_alerts
  FOR ALL
  USING (business_id = auth.uid())
  WITH CHECK (business_id = auth.uid());

-- Politiques pour ad_invoices
CREATE POLICY "Les entreprises peuvent voir leurs propres factures"
  ON ad_invoices
  FOR SELECT
  USING (business_id = auth.uid());

CREATE POLICY "Les entreprises peuvent créer leurs propres factures"
  ON ad_invoices
  FOR INSERT
  WITH CHECK (business_id = auth.uid());

-- Index pour les performances
CREATE INDEX idx_ad_wallets_business_id ON ad_wallets(business_id);
CREATE INDEX idx_ad_transactions_wallet_id ON ad_transactions(wallet_id);
CREATE INDEX idx_ad_transactions_campaign_id ON ad_transactions(campaign_id);
CREATE INDEX idx_ad_transactions_type ON ad_transactions(type);
CREATE INDEX idx_ad_transactions_status ON ad_transactions(status);
CREATE INDEX idx_ad_transactions_created_at ON ad_transactions(created_at);
CREATE INDEX idx_payment_methods_business_id ON payment_methods(business_id);
CREATE INDEX idx_payment_methods_is_default ON payment_methods(is_default);
CREATE INDEX idx_billing_alerts_business_id ON billing_alerts(business_id);
CREATE INDEX idx_billing_alerts_is_read ON billing_alerts(is_read);
CREATE INDEX idx_ad_invoices_business_id ON ad_invoices(business_id);
CREATE INDEX idx_ad_invoices_campaign_id ON ad_invoices(campaign_id);

-- Fonctions pour automatiser les mises à jour
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour updated_at
CREATE TRIGGER update_ad_wallets_updated_at
  BEFORE UPDATE ON ad_wallets
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ad_transactions_updated_at
  BEFORE UPDATE ON ad_transactions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_methods_updated_at
  BEFORE UPDATE ON payment_methods
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ad_invoices_updated_at
  BEFORE UPDATE ON ad_invoices
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Fonction pour générer des numéros de facture uniques
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
  new_number TEXT;
  counter INTEGER;
BEGIN
  -- Format: INV-YYYYMM-NNNN
  SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number FROM 12) AS INTEGER)), 0) + 1
  INTO counter
  FROM ad_invoices
  WHERE invoice_number LIKE 'INV-' || TO_CHAR(NOW(), 'YYYYMM') || '-%';
  
  new_number := 'INV-' || TO_CHAR(NOW(), 'YYYYMM') || '-' || LPAD(counter::TEXT, 4, '0');
  
  RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour générer automatiquement les numéros de facture
CREATE OR REPLACE FUNCTION set_invoice_number()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.invoice_number IS NULL THEN
    NEW.invoice_number := generate_invoice_number();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_ad_invoice_number
  BEFORE INSERT ON ad_invoices
  FOR EACH ROW
  EXECUTE FUNCTION set_invoice_number();

-- Fonction pour vérifier le solde avant dépense
CREATE OR REPLACE FUNCTION check_wallet_balance()
RETURNS TRIGGER AS $$
DECLARE
  current_balance NUMERIC;
BEGIN
  IF NEW.type = 'spend' THEN
    SELECT balance INTO current_balance
    FROM ad_wallets
    WHERE id = NEW.wallet_id;
    
    IF current_balance < NEW.amount THEN
      RAISE EXCEPTION 'Solde insuffisant. Solde actuel: %, Montant requis: %', current_balance, NEW.amount;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER check_balance_before_spend
  BEFORE INSERT ON ad_transactions
  FOR EACH ROW
  EXECUTE FUNCTION check_wallet_balance();

-- Commentaires sur les tables
COMMENT ON TABLE ad_wallets IS 'Portefeuilles publicitaires des entreprises';
COMMENT ON TABLE ad_transactions IS 'Historique des transactions publicitaires';
COMMENT ON TABLE payment_methods IS 'Méthodes de paiement configurées par les entreprises';
COMMENT ON TABLE billing_alerts IS 'Alertes de facturation et notifications';
COMMENT ON TABLE ad_invoices IS 'Factures générées pour les campagnes publicitaires';
