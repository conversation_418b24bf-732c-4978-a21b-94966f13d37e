import React, { useState, useEffect } from 'react';
import {
  CreditCard, Smartphone, Building, CheckCircle, XCircle,
  ArrowLeft, ArrowRight, Lock, Shield, AlertTriangle,
  Download, RefreshCw, Clock
} from 'lucide-react';
import Card, { Card<PERSON>ody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import { PaymentService, PaymentMethod, PaymentIntent, PaymentReceipt } from '../../services/paymentService';

interface PaymentFlowProps {
  planId: string;
  planName: string;
  amount: number;
  businessId: string;
  onSuccess: (receipt: PaymentReceipt) => void;
  onCancel: () => void;
}

type PaymentStep = 'method' | 'details' | 'processing' | 'success' | 'error';

const PaymentFlow: React.FC<PaymentFlowProps> = ({
  planId,
  planName,
  amount,
  businessId,
  onSuccess,
  onCancel
}) => {
  const [currentStep, setCurrentStep] = useState<PaymentStep>('method');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [paymentIntent, setPaymentIntent] = useState<PaymentIntent | null>(null);
  const [paymentReceipt, setPaymentReceipt] = useState<PaymentReceipt | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentMethods] = useState<PaymentMethod[]>(PaymentService.getAvailablePaymentMethods());

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price) + ' F CFA';
  };

  const getPaymentMethodIcon = (method: PaymentMethod) => {
    switch (method.type) {
      case 'card':
        return <CreditCard size={24} className="text-blue-600" />;
      case 'mobile_money':
        return <Smartphone size={24} className="text-green-600" />;
      case 'bank_transfer':
        return <Building size={24} className="text-purple-600" />;
      default:
        return <CreditCard size={24} className="text-gray-600" />;
    }
  };

  const handlePaymentMethodSelect = (method: PaymentMethod) => {
    setSelectedPaymentMethod(method);
    setCurrentStep('details');
  };

  const handlePaymentConfirm = async () => {
    if (!selectedPaymentMethod) return;

    setLoading(true);
    setError(null);
    setCurrentStep('processing');

    try {
      // Créer l'intention de paiement
      const intent = await PaymentService.createPaymentIntent(
        businessId,
        planId,
        amount,
        selectedPaymentMethod.id
      );
      setPaymentIntent(intent);

      // Confirmer le paiement
      const confirmedIntent = await PaymentService.confirmPayment(intent.id);

      if (confirmedIntent.status === 'succeeded') {
        // Récupérer le reçu
        const receipt = await PaymentService.getPaymentReceipt(intent.id);
        if (receipt) {
          setPaymentReceipt(receipt);
          setCurrentStep('success');
          onSuccess(receipt);
        }
      } else {
        setError('Le paiement a échoué. Veuillez réessayer.');
        setCurrentStep('error');
      }

    } catch (err) {
      console.error('Erreur lors du paiement:', err);
      setError('Une erreur est survenue lors du traitement du paiement.');
      setCurrentStep('error');
    } finally {
      setLoading(false);
    }
  };

  const renderPaymentMethodStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Choisissez votre méthode de paiement</h2>
        <p className="text-gray-600">Sélectionnez comment vous souhaitez payer votre abonnement</p>
      </div>

      <div className="space-y-4">
        {paymentMethods.map((method) => (
          <Card
            key={method.id}
            className="cursor-pointer hover:shadow-md transition-shadow border-2 border-transparent hover:border-blue-200"
            onClick={() => handlePaymentMethodSelect(method)}
          >
            <CardBody>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {getPaymentMethodIcon(method)}
                  <div>
                    <h3 className="font-medium text-gray-900">{method.provider}</h3>
                    <p className="text-sm text-gray-600">
                      {method.type === 'card' && `•••• •••• •••• ${method.last4}`}
                      {method.type === 'mobile_money' && method.phoneNumber}
                      {method.type === 'bank_transfer' && 'Virement bancaire'}
                    </p>
                  </div>
                </div>
                <ArrowRight size={20} className="text-gray-400" />
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          <ArrowLeft size={16} className="mr-2" />
          Annuler
        </Button>
      </div>
    </div>
  );

  const renderPaymentDetailsStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Confirmez votre paiement</h2>
        <p className="text-gray-600">Vérifiez les détails avant de procéder au paiement</p>
      </div>

      {/* Résumé de la commande */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">Résumé de la commande</h3>
        </CardHeader>
        <CardBody>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Plan sélectionné</span>
              <span className="font-medium">{planName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Durée</span>
              <span className="font-medium">1 mois</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Sous-total</span>
              <span className="font-medium">{formatPrice(amount)}</span>
            </div>
            <div className="border-t pt-3">
              <div className="flex justify-between text-lg font-bold">
                <span>Total</span>
                <span>{formatPrice(amount)}</span>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Méthode de paiement sélectionnée */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">Méthode de paiement</h3>
        </CardHeader>
        <CardBody>
          <div className="flex items-center space-x-4">
            {selectedPaymentMethod && getPaymentMethodIcon(selectedPaymentMethod)}
            <div>
              <h4 className="font-medium text-gray-900">{selectedPaymentMethod?.provider}</h4>
              <p className="text-sm text-gray-600">
                {selectedPaymentMethod?.type === 'card' && `•••• •••• •••• ${selectedPaymentMethod.last4}`}
                {selectedPaymentMethod?.type === 'mobile_money' && selectedPaymentMethod.phoneNumber}
              </p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Sécurité */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Shield className="text-green-600 mt-0.5" size={20} />
          <div>
            <h4 className="font-medium text-green-800">Paiement sécurisé</h4>
            <p className="text-green-700 text-sm mt-1">
              Vos informations de paiement sont protégées par un chiffrement SSL 256 bits.
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={() => setCurrentStep('method')}>
          <ArrowLeft size={16} className="mr-2" />
          Retour
        </Button>
        <Button onClick={handlePaymentConfirm} disabled={loading}>
          <Lock size={16} className="mr-2" />
          Confirmer le paiement
        </Button>
      </div>
    </div>
  );

  const renderProcessingStep = () => (
    <div className="text-center space-y-6">
      <div className="flex justify-center">
        <RefreshCw className="animate-spin text-blue-600" size={48} />
      </div>
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Traitement du paiement</h2>
        <p className="text-gray-600">Veuillez patienter pendant que nous traitons votre paiement...</p>
      </div>
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Clock className="text-blue-600 mt-0.5" size={20} />
          <div>
            <h4 className="font-medium text-blue-800">Traitement en cours</h4>
            <p className="text-blue-700 text-sm mt-1">
              Cela peut prendre quelques secondes. Ne fermez pas cette fenêtre.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSuccessStep = () => (
    <div className="text-center space-y-6">
      <div className="flex justify-center">
        <CheckCircle className="text-green-600" size={64} />
      </div>
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Paiement réussi !</h2>
        <p className="text-gray-600">Votre abonnement a été activé avec succès.</p>
      </div>

      {paymentReceipt && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">Détails du paiement</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-3 text-left">
              <div className="flex justify-between">
                <span className="text-gray-600">Numéro de facture</span>
                <span className="font-medium">{paymentReceipt.invoiceNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">ID de transaction</span>
                <span className="font-medium font-mono text-sm">{paymentReceipt.transactionId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Montant payé</span>
                <span className="font-medium">{formatPrice(paymentReceipt.amount)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date de paiement</span>
                <span className="font-medium">
                  {new Date(paymentReceipt.paidAt).toLocaleDateString('fr-FR', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      <div className="flex justify-center space-x-4">
        <Button variant="outline" onClick={() => window.print()}>
          <Download size={16} className="mr-2" />
          Télécharger le reçu
        </Button>
        <Button onClick={() => onSuccess(paymentReceipt!)}>
          <CheckCircle size={16} className="mr-2" />
          Continuer
        </Button>
      </div>
    </div>
  );

  const renderErrorStep = () => (
    <div className="text-center space-y-6">
      <div className="flex justify-center">
        <XCircle className="text-red-600" size={64} />
      </div>
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Paiement échoué</h2>
        <p className="text-gray-600">{error}</p>
      </div>

      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="text-red-600 mt-0.5" size={20} />
          <div>
            <h4 className="font-medium text-red-800">Que faire maintenant ?</h4>
            <ul className="text-red-700 text-sm mt-1 list-disc list-inside">
              <li>Vérifiez vos informations de paiement</li>
              <li>Assurez-vous d'avoir suffisamment de fonds</li>
              <li>Contactez votre banque si le problème persiste</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-center space-x-4">
        <Button variant="outline" onClick={onCancel}>
          Annuler
        </Button>
        <Button onClick={() => setCurrentStep('method')}>
          <RefreshCw size={16} className="mr-2" />
          Réessayer
        </Button>
      </div>
    </div>
  );

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardBody className="p-8">
          {currentStep === 'method' && renderPaymentMethodStep()}
          {currentStep === 'details' && renderPaymentDetailsStep()}
          {currentStep === 'processing' && renderProcessingStep()}
          {currentStep === 'success' && renderSuccessStep()}
          {currentStep === 'error' && renderErrorStep()}
        </CardBody>
      </Card>
    </div>
  );
};

export default PaymentFlow;
