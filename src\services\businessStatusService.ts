import { BusinessStatus } from '../types';
import { Building2, CheckCircle, Star, Award, Crown, Shield } from 'lucide-react';

export interface BusinessStatusLevel {
  status: BusinessStatus;
  name: string;
  description: string;
  icon: any;
  color: string;
  bgColor: string;
  textColor: string;
  criteria: BusinessStatusCriteria;
  benefits: string[];
  nextLevel?: BusinessStatus;
}

export interface BusinessStatusCriteria {
  profileComplete?: boolean;
  minProducts?: number;
  minSales?: number;
  minReviews?: number;
  minRating?: number;
  accountAge?: number; // en jours
  verificationRequired?: boolean;
  businessDocumentRequired?: boolean;
  minRevenue?: number; // en F CFA
  specialRequirements?: string[];
}

export class BusinessStatusService {
  /**
   * Configuration des niveaux de statut d'entreprise
   */
  static readonly STATUS_LEVELS: Record<BusinessStatus, BusinessStatusLevel> = {
    [BusinessStatus.NEW]: {
      status: BusinessStatus.NEW,
      name: 'Nouveau',
      description: 'Bienvenue ! Complétez votre profil d\'entreprise pour commencer.',
      icon: Building2,
      color: '#6B7280',
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-600',
      criteria: {
        profileComplete: false
      },
      benefits: [
        'Accès aux fonctionnalités de base',
        'Création de profil entreprise gratuite',
        'Possibilité d\'ajouter des produits'
      ],
      nextLevel: BusinessStatus.ACTIVE
    },

    [BusinessStatus.ACTIVE]: {
      status: BusinessStatus.ACTIVE,
      name: 'Actif',
      description: 'Entreprise active avec un profil complet et des premiers produits.',
      icon: CheckCircle,
      color: '#10B981',
      bgColor: 'bg-green-100',
      textColor: 'text-green-700',
      criteria: {
        profileComplete: true,
        minProducts: 1,
        accountAge: 7
      },
      benefits: [
        'Badge entreprise active',
        'Visibilité dans les recherches',
        'Accès au marketplace',
        'Possibilité de recevoir des avis',
        'Statistiques de base'
      ],
      nextLevel: BusinessStatus.VERIFIED
    },

    [BusinessStatus.VERIFIED]: {
      status: BusinessStatus.VERIFIED,
      name: 'Vérifié',
      description: 'Entreprise vérifiée avec documents officiels et bonne réputation.',
      icon: Shield,
      color: '#3B82F6',
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-700',
      criteria: {
        profileComplete: true,
        minProducts: 5,
        minSales: 10,
        minReviews: 5,
        minRating: 3.5,
        accountAge: 30,
        verificationRequired: true,
        businessDocumentRequired: true
      },
      benefits: [
        'Badge vérifié bleu',
        'Confiance client renforcée',
        'Priorité dans les résultats de recherche',
        'Accès aux outils marketing avancés',
        'Support client prioritaire',
        'Statistiques détaillées'
      ],
      nextLevel: BusinessStatus.PREMIUM
    },

    [BusinessStatus.PREMIUM]: {
      status: BusinessStatus.PREMIUM,
      name: 'Premium',
      description: 'Entreprise premium avec excellent service et forte activité.',
      icon: Star,
      color: '#F59E0B',
      bgColor: 'bg-yellow-100',
      textColor: 'text-yellow-700',
      criteria: {
        profileComplete: true,
        minProducts: 20,
        minSales: 100,
        minReviews: 50,
        minRating: 4.0,
        accountAge: 90,
        verificationRequired: true,
        businessDocumentRequired: true,
        minRevenue: 500000 // 500k F CFA
      },
      benefits: [
        'Badge premium doré',
        'Mise en avant dans les résultats',
        'Outils d\'analyse avancés',
        'Campagnes publicitaires privilégiées',
        'Support dédié',
        'Accès aux événements exclusifs',
        'Commission réduite sur les ventes'
      ],
      nextLevel: BusinessStatus.FEATURED
    },

    [BusinessStatus.FEATURED]: {
      status: BusinessStatus.FEATURED,
      name: 'Mis en Avant',
      description: 'Entreprise d\'excellence mise en avant sur la plateforme.',
      icon: Award,
      color: '#8B5CF6',
      bgColor: 'bg-purple-100',
      textColor: 'text-purple-700',
      criteria: {
        profileComplete: true,
        minProducts: 50,
        minSales: 500,
        minReviews: 200,
        minRating: 4.5,
        accountAge: 180,
        verificationRequired: true,
        businessDocumentRequired: true,
        minRevenue: 2000000, // 2M F CFA
        specialRequirements: [
          'Excellence du service client',
          'Innovation dans les produits',
          'Contribution à la communauté'
        ]
      },
      benefits: [
        'Badge featured violet',
        'Mise en avant sur la page d\'accueil',
        'Partenariats exclusifs',
        'Accès aux données de marché',
        'Programme d\'ambassadeur',
        'Événements VIP',
        'Commission minimale'
      ],
      nextLevel: BusinessStatus.PARTNER
    },

    [BusinessStatus.PARTNER]: {
      status: BusinessStatus.PARTNER,
      name: 'Partenaire Officiel',
      description: 'Partenaire stratégique de Customeroom avec statut d\'élite.',
      icon: Crown,
      color: '#EF4444',
      bgColor: 'bg-red-100',
      textColor: 'text-red-700',
      criteria: {
        profileComplete: true,
        minProducts: 100,
        minSales: 2000,
        minReviews: 1000,
        minRating: 4.8,
        accountAge: 365,
        verificationRequired: true,
        businessDocumentRequired: true,
        minRevenue: ********, // 10M F CFA
        specialRequirements: [
          'Partenariat stratégique signé',
          'Leadership dans le secteur',
          'Contribution exceptionnelle à la plateforme',
          'Respect exemplaire des valeurs'
        ]
      },
      benefits: [
        'Badge partenaire rouge couronne',
        'Statut de partenaire officiel',
        'Co-marketing avec Customeroom',
        'Accès aux nouvelles fonctionnalités en avant-première',
        'Participation aux décisions stratégiques',
        'Revenus de parrainage',
        'Aucune commission sur les ventes'
      ]
    },

    [BusinessStatus.PENDING]: {
      status: BusinessStatus.PENDING,
      name: 'En Attente',
      description: 'Vérification en cours. Votre dossier est en cours d\'examen.',
      icon: CheckCircle,
      color: '#F59E0B',
      bgColor: 'bg-yellow-100',
      textColor: 'text-yellow-700',
      criteria: {
        verificationRequired: true
      },
      benefits: [
        'Accès aux fonctionnalités de base',
        'Vérification en cours'
      ]
    },

    [BusinessStatus.INACTIVE]: {
      status: BusinessStatus.INACTIVE,
      name: 'Inactif',
      description: 'Compte temporairement inactif. Contactez le support pour réactivation.',
      icon: Building2,
      color: '#EF4444',
      bgColor: 'bg-red-100',
      textColor: 'text-red-700',
      criteria: {},
      benefits: [
        'Accès limité',
        'Possibilité de réactivation'
      ]
    }
  };

  /**
   * Récupère les informations d'un niveau de statut
   */
  static getStatusLevel(status: BusinessStatus): BusinessStatusLevel | null {
    return this.STATUS_LEVELS[status] || null;
  }

  /**
   * Récupère tous les niveaux de statut dans l'ordre de progression
   */
  static getAllStatusLevels(): BusinessStatusLevel[] {
    const progressionOrder = [
      BusinessStatus.NEW,
      BusinessStatus.ACTIVE,
      BusinessStatus.VERIFIED,
      BusinessStatus.PREMIUM,
      BusinessStatus.FEATURED,
      BusinessStatus.PARTNER
    ];

    return progressionOrder.map(status => this.STATUS_LEVELS[status]);
  }

  /**
   * Vérifie si une entreprise remplit les critères pour un statut donné
   */
  static checkCriteria(
    targetStatus: BusinessStatus,
    businessMetrics: {
      profileComplete: boolean;
      productsCount: number;
      salesCount: number;
      reviewsCount: number;
      averageRating: number;
      accountAgeDays: number;
      isVerified: boolean;
      hasBusinessDocument: boolean;
      totalRevenue: number;
    }
  ): { eligible: boolean; missingCriteria: string[] } {
    const level = this.getStatusLevel(targetStatus);
    if (!level) {
      return { eligible: false, missingCriteria: ['Statut invalide'] };
    }

    const missingCriteria: string[] = [];
    const criteria = level.criteria;

    if (criteria.profileComplete && !businessMetrics.profileComplete) {
      missingCriteria.push('Profil d\'entreprise complet requis');
    }

    if (criteria.minProducts && businessMetrics.productsCount < criteria.minProducts) {
      missingCriteria.push(`Au moins ${criteria.minProducts} produits requis (actuellement: ${businessMetrics.productsCount})`);
    }

    if (criteria.minSales && businessMetrics.salesCount < criteria.minSales) {
      missingCriteria.push(`Au moins ${criteria.minSales} ventes requises (actuellement: ${businessMetrics.salesCount})`);
    }

    if (criteria.minReviews && businessMetrics.reviewsCount < criteria.minReviews) {
      missingCriteria.push(`Au moins ${criteria.minReviews} avis requis (actuellement: ${businessMetrics.reviewsCount})`);
    }

    if (criteria.minRating && businessMetrics.averageRating < criteria.minRating) {
      missingCriteria.push(`Note moyenne d'au moins ${criteria.minRating}/5 requise (actuellement: ${businessMetrics.averageRating.toFixed(1)})`);
    }

    if (criteria.accountAge && businessMetrics.accountAgeDays < criteria.accountAge) {
      missingCriteria.push(`Compte actif depuis au moins ${criteria.accountAge} jours (actuellement: ${businessMetrics.accountAgeDays})`);
    }

    if (criteria.verificationRequired && !businessMetrics.isVerified) {
      missingCriteria.push('Vérification d\'entreprise requise');
    }

    if (criteria.businessDocumentRequired && !businessMetrics.hasBusinessDocument) {
      missingCriteria.push('Document officiel d\'entreprise requis');
    }

    if (criteria.minRevenue && businessMetrics.totalRevenue < criteria.minRevenue) {
      missingCriteria.push(`Chiffre d'affaires d'au moins ${criteria.minRevenue.toLocaleString()} F CFA requis`);
    }

    if (criteria.specialRequirements) {
      criteria.specialRequirements.forEach(req => {
        missingCriteria.push(`Exigence spéciale: ${req}`);
      });
    }

    return {
      eligible: missingCriteria.length === 0,
      missingCriteria
    };
  }

  /**
   * Récupère les métriques d'une entreprise depuis la base de données
   */
  static async getBusinessMetricsFromDB(businessId: string): Promise<any> {
    const { supabase } = await import('../lib/supabase');

    try {
      const { data, error } = await supabase.rpc('calculate_business_metrics', {
        business_uuid: businessId
      });

      if (error) {
        console.error('Erreur lors du calcul des métriques:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Erreur dans getBusinessMetricsFromDB:', error);
      throw error;
    }
  }

  /**
   * Vérifie l'éligibilité d'une entreprise pour un statut via la base de données
   */
  static async checkEligibilityFromDB(businessId: string, targetStatus: BusinessStatus): Promise<any> {
    const { supabase } = await import('../lib/supabase');

    try {
      const { data, error } = await supabase.rpc('check_business_status_eligibility', {
        business_uuid: businessId,
        target_status: targetStatus
      });

      if (error) {
        console.error('Erreur lors de la vérification d\'éligibilité:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Erreur dans checkEligibilityFromDB:', error);
      throw error;
    }
  }

  /**
   * Met à jour le statut d'une entreprise si elle est éligible
   */
  static async updateBusinessStatusIfEligible(businessId: string): Promise<any> {
    const { supabase } = await import('../lib/supabase');

    try {
      const { data, error } = await supabase.rpc('update_business_status_if_eligible', {
        business_uuid: businessId
      });

      if (error) {
        console.error('Erreur lors de la mise à jour du statut:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Erreur dans updateBusinessStatusIfEligible:', error);
      throw error;
    }
  }

  /**
   * Récupère l'historique des changements de statut d'une entreprise
   */
  static async getStatusHistory(businessId: string): Promise<any[]> {
    const { supabase } = await import('../lib/supabase');

    try {
      const { data, error } = await supabase
        .from('business_status_history')
        .select('*')
        .eq('business_id', businessId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erreur lors de la récupération de l\'historique:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Erreur dans getStatusHistory:', error);
      throw error;
    }
  }

  /**
   * Récupère les critères depuis la base de données
   */
  static async getCriteriaFromDB(): Promise<any[]> {
    const { supabase } = await import('../lib/supabase');

    try {
      const { data, error } = await supabase
        .from('business_status_criteria')
        .select('*')
        .eq('is_active', true)
        .order('status_level');

      if (error) {
        console.error('Erreur lors de la récupération des critères:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Erreur dans getCriteriaFromDB:', error);
      throw error;
    }
  }
}
