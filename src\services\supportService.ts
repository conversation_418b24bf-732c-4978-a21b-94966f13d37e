import { supabase } from '../lib/supabase';

// Types pour le système de support
export interface ChatConversation {
  id: string;
  user_id: string;
  agent_id?: string;
  status: 'active' | 'waiting' | 'closed' | 'transferred';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  subject?: string;
  department: string;
  language: string;
  created_at: string;
  updated_at: string;
  closed_at?: string;
}

export interface ChatMessage {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  message_type: 'text' | 'image' | 'file' | 'system';
  attachments: string[];
  metadata: Record<string, any>;
  is_read: boolean;
  is_edited: boolean;
  edited_at?: string;
  created_at: string;
  sender?: {
    username: string;
    profile_picture?: string;
    role?: string;
  };
}

export interface SupportTicket {
  id: string;
  user_id: string;
  assigned_agent_id?: string;
  subject: string;
  description: string;
  category: 'general' | 'technical' | 'billing' | 'account' | 'feature' | 'bug';
  status: 'open' | 'in_progress' | 'waiting_user' | 'resolved' | 'closed';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  tags: string[];
  attachments: string[];
  metadata: Record<string, any>;
  satisfaction_rating?: number;
  satisfaction_comment?: string;
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  closed_at?: string;
}

export interface TicketMessage {
  id: string;
  ticket_id: string;
  sender_id: string;
  content: string;
  message_type: 'reply' | 'note' | 'system';
  attachments: string[];
  is_internal: boolean;
  created_at: string;
  updated_at: string;
  sender?: {
    username: string;
    profile_picture?: string;
    role?: string;
  };
}

export interface CommunityPost {
  id: string;
  author_id: string;
  title: string;
  content: string;
  category: 'general' | 'conseils' | 'bugs' | 'features' | 'questions';
  tags: string[];
  attachments: string[];
  votes_up: number;
  votes_down: number;
  views_count: number;
  replies_count: number;
  is_solved: boolean;
  is_pinned: boolean;
  is_locked: boolean;
  is_approved: boolean;
  is_flagged: boolean;
  flagged_reason?: string;
  created_at: string;
  updated_at: string;
  author?: {
    username: string;
    profile_picture?: string;
  };
}

export interface CommunityReply {
  id: string;
  post_id: string;
  author_id: string;
  parent_reply_id?: string;
  content: string;
  votes_up: number;
  votes_down: number;
  is_solution: boolean;
  is_approved: boolean;
  is_flagged: boolean;
  created_at: string;
  updated_at: string;
  author?: {
    username: string;
    profile_picture?: string;
  };
}

export class SupportService {
  // =====================================================
  // CHAT CONVERSATIONS
  // =====================================================

  /**
   * Créer une nouvelle conversation de chat
   */
  static async createChatConversation(
    userId: string,
    subject?: string,
    department: string = 'general'
  ): Promise<ChatConversation> {
    const { data, error } = await supabase
      .from('chat_conversations')
      .insert({
        user_id: userId,
        subject,
        department,
        status: 'waiting'
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  /**
   * Récupérer les conversations d'un utilisateur
   */
  static async getUserConversations(userId: string): Promise<ChatConversation[]> {
    const { data, error } = await supabase
      .from('chat_conversations')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  /**
   * Récupérer une conversation spécifique
   */
  static async getConversation(conversationId: string): Promise<ChatConversation | null> {
    const { data, error } = await supabase
      .from('chat_conversations')
      .select('*')
      .eq('id', conversationId)
      .single();

    if (error) return null;
    return data;
  }

  /**
   * Mettre à jour le statut d'une conversation
   */
  static async updateConversationStatus(
    conversationId: string,
    status: ChatConversation['status']
  ): Promise<void> {
    const updateData: any = { status };
    if (status === 'closed') {
      updateData.closed_at = new Date().toISOString();
    }

    const { error } = await supabase
      .from('chat_conversations')
      .update(updateData)
      .eq('id', conversationId);

    if (error) throw error;
  }

  // =====================================================
  // CHAT MESSAGES
  // =====================================================

  /**
   * Envoyer un message dans une conversation
   */
  static async sendChatMessage(
    conversationId: string,
    senderId: string,
    content: string,
    messageType: ChatMessage['message_type'] = 'text',
    attachments: string[] = []
  ): Promise<ChatMessage> {
    const { data, error } = await supabase
      .from('chat_messages')
      .insert({
        conversation_id: conversationId,
        sender_id: senderId,
        content,
        message_type: messageType,
        attachments
      })
      .select(`
        *,
        sender:profiles!sender_id(username, profile_picture, role)
      `)
      .single();

    if (error) throw error;

    // Mettre à jour la conversation
    await supabase
      .from('chat_conversations')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', conversationId);

    return data;
  }

  /**
   * Récupérer les messages d'une conversation
   */
  static async getConversationMessages(conversationId: string): Promise<ChatMessage[]> {
    const { data, error } = await supabase
      .from('chat_messages')
      .select(`
        *,
        sender:profiles!sender_id(username, profile_picture, role)
      `)
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  /**
   * Marquer les messages comme lus
   */
  static async markMessagesAsRead(conversationId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('chat_messages')
      .update({ is_read: true })
      .eq('conversation_id', conversationId)
      .neq('sender_id', userId);

    if (error) throw error;
  }

  // =====================================================
  // SUPPORT TICKETS
  // =====================================================

  /**
   * Créer un nouveau ticket de support
   */
  static async createSupportTicket(
    userId: string,
    subject: string,
    description: string,
    category: SupportTicket['category'],
    priority: SupportTicket['priority'] = 'normal'
  ): Promise<SupportTicket> {
    const { data, error } = await supabase
      .from('support_tickets')
      .insert({
        user_id: userId,
        subject,
        description,
        category,
        priority
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  /**
   * Récupérer les tickets d'un utilisateur
   */
  static async getUserTickets(userId: string): Promise<SupportTicket[]> {
    const { data, error } = await supabase
      .from('support_tickets')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  /**
   * Récupérer un ticket spécifique
   */
  static async getTicket(ticketId: string): Promise<SupportTicket | null> {
    const { data, error } = await supabase
      .from('support_tickets')
      .select('*')
      .eq('id', ticketId)
      .single();

    if (error) return null;
    return data;
  }

  /**
   * Mettre à jour le statut d'un ticket
   */
  static async updateTicketStatus(
    ticketId: string,
    status: SupportTicket['status']
  ): Promise<void> {
    const updateData: any = { status };
    if (status === 'resolved') {
      updateData.resolved_at = new Date().toISOString();
    } else if (status === 'closed') {
      updateData.closed_at = new Date().toISOString();
    }

    const { error } = await supabase
      .from('support_tickets')
      .update(updateData)
      .eq('id', ticketId);

    if (error) throw error;
  }

  // =====================================================
  // TICKET MESSAGES
  // =====================================================

  /**
   * Ajouter un message à un ticket
   */
  static async addTicketMessage(
    ticketId: string,
    senderId: string,
    content: string,
    messageType: TicketMessage['message_type'] = 'reply',
    isInternal: boolean = false
  ): Promise<TicketMessage> {
    const { data, error } = await supabase
      .from('ticket_messages')
      .insert({
        ticket_id: ticketId,
        sender_id: senderId,
        content,
        message_type: messageType,
        is_internal: isInternal
      })
      .select(`
        *,
        sender:profiles!sender_id(username, profile_picture, role)
      `)
      .single();

    if (error) throw error;

    // Mettre à jour le ticket
    await supabase
      .from('support_tickets')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', ticketId);

    return data;
  }

  /**
   * Récupérer les messages d'un ticket
   */
  static async getTicketMessages(ticketId: string): Promise<TicketMessage[]> {
    const { data, error } = await supabase
      .from('ticket_messages')
      .select(`
        *,
        sender:profiles!sender_id(username, profile_picture, role)
      `)
      .eq('ticket_id', ticketId)
      .eq('is_internal', false) // Exclure les notes internes
      .order('created_at', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  // =====================================================
  // COMMUNITY POSTS
  // =====================================================

  /**
   * Créer un nouveau post communautaire
   */
  static async createCommunityPost(
    authorId: string,
    title: string,
    content: string,
    category: CommunityPost['category'],
    tags: string[] = []
  ): Promise<CommunityPost> {
    const { data, error } = await supabase
      .from('community_posts')
      .insert({
        author_id: authorId,
        title,
        content,
        category,
        tags
      })
      .select(`
        *,
        author:profiles!author_id(username, profile_picture)
      `)
      .single();

    if (error) throw error;
    return data;
  }

  /**
   * Récupérer les posts communautaires
   */
  static async getCommunityPosts(
    category?: string,
    limit: number = 20,
    offset: number = 0
  ): Promise<CommunityPost[]> {
    let query = supabase
      .from('community_posts')
      .select(`
        *,
        author:profiles!author_id(username, profile_picture)
      `)
      .eq('is_approved', true)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  }

  /**
   * Récupérer un post spécifique
   */
  static async getCommunityPost(postId: string): Promise<CommunityPost | null> {
    const { data, error } = await supabase
      .from('community_posts')
      .select(`
        *,
        author:profiles!author_id(username, profile_picture)
      `)
      .eq('id', postId)
      .single();

    if (error) return null;

    // Incrémenter le compteur de vues
    await supabase
      .from('community_posts')
      .update({ views_count: data.views_count + 1 })
      .eq('id', postId);

    return { ...data, views_count: data.views_count + 1 };
  }

  /**
   * Voter pour un post
   */
  static async voteCommunityPost(
    userId: string,
    postId: string,
    voteType: 'up' | 'down'
  ): Promise<void> {
    const { error } = await supabase
      .from('community_votes')
      .upsert({
        user_id: userId,
        post_id: postId,
        vote_type: voteType
      });

    if (error) throw error;
  }

  /**
   * Supprimer un vote
   */
  static async removeCommunityVote(userId: string, postId: string): Promise<void> {
    const { error } = await supabase
      .from('community_votes')
      .delete()
      .eq('user_id', userId)
      .eq('post_id', postId);

    if (error) throw error;
  }

  /**
   * Marquer un post comme résolu
   */
  static async markPostAsSolved(postId: string, authorId: string): Promise<void> {
    const { error } = await supabase
      .from('community_posts')
      .update({ is_solved: true })
      .eq('id', postId)
      .eq('author_id', authorId);

    if (error) throw error;
  }

  // =====================================================
  // COMMUNITY REPLIES
  // =====================================================

  /**
   * Ajouter une réponse à un post
   */
  static async addCommunityReply(
    authorId: string,
    postId: string,
    content: string,
    parentReplyId?: string
  ): Promise<CommunityReply> {
    const { data, error } = await supabase
      .from('community_replies')
      .insert({
        author_id: authorId,
        post_id: postId,
        content,
        parent_reply_id: parentReplyId
      })
      .select(`
        *,
        author:profiles!author_id(username, profile_picture)
      `)
      .single();

    if (error) throw error;
    return data;
  }

  /**
   * Récupérer les réponses d'un post
   */
  static async getCommunityReplies(postId: string): Promise<CommunityReply[]> {
    const { data, error } = await supabase
      .from('community_replies')
      .select(`
        *,
        author:profiles!author_id(username, profile_picture)
      `)
      .eq('post_id', postId)
      .eq('is_approved', true)
      .order('created_at', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  /**
   * Voter pour une réponse
   */
  static async voteCommunityReply(
    userId: string,
    replyId: string,
    voteType: 'up' | 'down'
  ): Promise<void> {
    const { error } = await supabase
      .from('community_votes')
      .upsert({
        user_id: userId,
        reply_id: replyId,
        vote_type: voteType
      });

    if (error) throw error;
  }

  /**
   * Marquer une réponse comme solution
   */
  static async markReplyAsSolution(
    replyId: string,
    postId: string,
    authorId: string
  ): Promise<void> {
    // Vérifier que l'utilisateur est l'auteur du post
    const { data: post } = await supabase
      .from('community_posts')
      .select('author_id')
      .eq('id', postId)
      .single();

    if (!post || post.author_id !== authorId) {
      throw new Error('Seul l\'auteur du post peut marquer une solution');
    }

    // Marquer la réponse comme solution
    const { error } = await supabase
      .from('community_replies')
      .update({ is_solution: true })
      .eq('id', replyId);

    if (error) throw error;

    // Marquer le post comme résolu
    await this.markPostAsSolved(postId, authorId);
  }

  // =====================================================
  // FAQ RATINGS
  // =====================================================

  /**
   * Noter une FAQ
   */
  static async rateFAQ(
    userId: string,
    faqQuestion: string,
    isHelpful: boolean,
    feedback?: string
  ): Promise<void> {
    const { error } = await supabase
      .from('faq_ratings')
      .upsert({
        user_id: userId,
        faq_question: faqQuestion,
        is_helpful: isHelpful,
        feedback
      });

    if (error) throw error;
  }

  /**
   * Récupérer les statistiques d'une FAQ
   */
  static async getFAQStats(faqQuestion: string): Promise<{
    helpful: number;
    notHelpful: number;
    total: number;
  }> {
    const { data, error } = await supabase
      .from('faq_ratings')
      .select('is_helpful')
      .eq('faq_question', faqQuestion);

    if (error) throw error;

    const helpful = data?.filter(r => r.is_helpful).length || 0;
    const notHelpful = data?.filter(r => !r.is_helpful).length || 0;

    return {
      helpful,
      notHelpful,
      total: helpful + notHelpful
    };
  }

  // =====================================================
  // REAL-TIME SUBSCRIPTIONS
  // =====================================================

  /**
   * S'abonner aux messages de chat en temps réel
   */
  static subscribeToChatMessages(
    conversationId: string,
    onMessage: (message: ChatMessage) => void
  ) {
    return supabase
      .channel(`chat-messages-${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `conversation_id=eq.${conversationId}`
        },
        async (payload) => {
          // Récupérer les détails du sender
          const { data: message } = await supabase
            .from('chat_messages')
            .select(`
              *,
              sender:profiles!sender_id(username, profile_picture, role)
            `)
            .eq('id', payload.new.id)
            .single();

          if (message) {
            onMessage(message);
          }
        }
      )
      .subscribe();
  }

  /**
   * S'abonner aux messages de ticket en temps réel
   */
  static subscribeToTicketMessages(
    ticketId: string,
    onMessage: (message: TicketMessage) => void
  ) {
    return supabase
      .channel(`ticket-messages-${ticketId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'ticket_messages',
          filter: `ticket_id=eq.${ticketId}`
        },
        async (payload) => {
          // Récupérer les détails du sender
          const { data: message } = await supabase
            .from('ticket_messages')
            .select(`
              *,
              sender:profiles!sender_id(username, profile_picture, role)
            `)
            .eq('id', payload.new.id)
            .single();

          if (message) {
            onMessage(message);
          }
        }
      )
      .subscribe();
  }

  /**
   * Se désabonner d'un canal temps réel
   */
  static unsubscribe(subscription: any) {
    if (subscription) {
      supabase.removeChannel(subscription);
    }
  }
}
