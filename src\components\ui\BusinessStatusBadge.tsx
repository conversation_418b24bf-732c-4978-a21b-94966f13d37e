import React from 'react';
import { BusinessStatus } from '../../types';
import { 
  Star, 
  Shield, 
  Crown, 
  Award, 
  Zap, 
  Users,
  CheckCircle,
  Clock
} from 'lucide-react';

interface BusinessStatusBadgeProps {
  status: BusinessStatus;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'solid' | 'gradient';
  showIcon?: boolean;
  showText?: boolean;
  animated?: boolean;
  className?: string;
}

const BusinessStatusBadge: React.FC<BusinessStatusBadgeProps> = ({
  status,
  size = 'sm',
  variant = 'default',
  showIcon = true,
  showText = true,
  animated = false,
  className = ''
}) => {
  const getStatusConfig = (status: BusinessStatus) => {
    switch (status) {
      case BusinessStatus.NEW:
        return {
          name: 'Nouveau',
          icon: Star,
          description: 'Entreprise nouvellement inscrite'
        };
      case BusinessStatus.ACTIVE:
        return {
          name: 'Actif',
          icon: CheckCircle,
          description: 'Entreprise active et vérifiée'
        };
      case BusinessStatus.VERIFIED:
        return {
          name: 'Véri<PERSON><PERSON>',
          icon: Shield,
          description: 'Entreprise vérifiée et certifiée'
        };
      case BusinessStatus.PREMIUM:
        return {
          name: 'Premium',
          icon: Award,
          description: 'Entreprise premium avec avantages exclusifs'
        };
      case BusinessStatus.FEATURED:
        return {
          name: 'Featured',
          icon: Zap,
          description: 'Entreprise mise en avant'
        };
      case BusinessStatus.PARTNER:
        return {
          name: 'Partenaire',
          icon: Crown,
          description: 'Partenaire officiel Customeroom'
        };
      case BusinessStatus.PENDING:
        return {
          name: 'En Attente',
          icon: Clock,
          description: 'En cours de vérification'
        };
      case BusinessStatus.INACTIVE:
      default:
        return {
          name: 'Inactif',
          icon: Users,
          description: 'Entreprise inactive'
        };
    }
  };

  const statusConfig = getStatusConfig(status);
  const IconComponent = statusConfig.icon;

  // Tailles des badges
  const sizeClasses = {
    xs: {
      container: 'px-1.5 py-0.5 text-xs',
      icon: 10,
      gap: 'gap-1'
    },
    sm: {
      container: 'px-2 py-1 text-xs',
      icon: 12,
      gap: 'gap-1'
    },
    md: {
      container: 'px-3 py-1.5 text-sm',
      icon: 14,
      gap: 'gap-1.5'
    },
    lg: {
      container: 'px-4 py-2 text-base',
      icon: 16,
      gap: 'gap-2'
    }
  };

  const currentSize = sizeClasses[size];

  const getStatusClasses = () => {
    const baseClasses = `inline-flex items-center font-medium rounded-full transition-all duration-200 cursor-default`;

    switch (variant) {
      case 'outline':
        switch (status) {
          case BusinessStatus.NEW:
            return `${baseClasses} border-2 border-gray-300 text-gray-700 bg-white hover:bg-gray-50`;
          case BusinessStatus.ACTIVE:
            return `${baseClasses} border-2 border-green-300 text-green-700 bg-white hover:bg-green-50`;
          case BusinessStatus.VERIFIED:
            return `${baseClasses} border-2 border-blue-300 text-blue-700 bg-white hover:bg-blue-50`;
          case BusinessStatus.PREMIUM:
            return `${baseClasses} border-2 border-yellow-300 text-yellow-700 bg-white hover:bg-yellow-50`;
          case BusinessStatus.FEATURED:
            return `${baseClasses} border-2 border-purple-300 text-purple-700 bg-white hover:bg-purple-50`;
          case BusinessStatus.PARTNER:
            return `${baseClasses} border-2 border-red-300 text-red-700 bg-white hover:bg-red-50`;
          case BusinessStatus.PENDING:
            return `${baseClasses} border-2 border-orange-300 text-orange-700 bg-white hover:bg-orange-50`;
          default:
            return `${baseClasses} border-2 border-gray-300 text-gray-700 bg-white`;
        }

      case 'solid':
        switch (status) {
          case BusinessStatus.NEW:
            return `${baseClasses} bg-gray-600 text-white hover:bg-gray-700`;
          case BusinessStatus.ACTIVE:
            return `${baseClasses} bg-green-600 text-white hover:bg-green-700`;
          case BusinessStatus.VERIFIED:
            return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700`;
          case BusinessStatus.PREMIUM:
            return `${baseClasses} bg-yellow-600 text-white hover:bg-yellow-700`;
          case BusinessStatus.FEATURED:
            return `${baseClasses} bg-purple-600 text-white hover:bg-purple-700`;
          case BusinessStatus.PARTNER:
            return `${baseClasses} bg-red-600 text-white hover:bg-red-700`;
          case BusinessStatus.PENDING:
            return `${baseClasses} bg-orange-600 text-white hover:bg-orange-700`;
          default:
            return `${baseClasses} bg-gray-600 text-white`;
        }

      case 'gradient':
        switch (status) {
          case BusinessStatus.NEW:
            return `${baseClasses} bg-gradient-to-r from-gray-400 to-gray-600 text-white`;
          case BusinessStatus.ACTIVE:
            return `${baseClasses} bg-gradient-to-r from-green-400 to-green-600 text-white`;
          case BusinessStatus.VERIFIED:
            return `${baseClasses} bg-gradient-to-r from-blue-400 to-blue-600 text-white`;
          case BusinessStatus.PREMIUM:
            return `${baseClasses} bg-gradient-to-r from-yellow-400 to-orange-500 text-white`;
          case BusinessStatus.FEATURED:
            return `${baseClasses} bg-gradient-to-r from-purple-400 to-purple-600 text-white`;
          case BusinessStatus.PARTNER:
            return `${baseClasses} bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg`;
          case BusinessStatus.PENDING:
            return `${baseClasses} bg-gradient-to-r from-orange-400 to-orange-600 text-white`;
          default:
            return `${baseClasses} bg-gradient-to-r from-gray-400 to-gray-600 text-white`;
        }

      default: // 'default'
        switch (status) {
          case BusinessStatus.NEW:
            return `${baseClasses} bg-gray-100 text-gray-700 hover:bg-gray-200`;
          case BusinessStatus.ACTIVE:
            return `${baseClasses} bg-green-100 text-green-700 hover:bg-green-200`;
          case BusinessStatus.VERIFIED:
            return `${baseClasses} bg-blue-100 text-blue-700 hover:bg-blue-200`;
          case BusinessStatus.PREMIUM:
            return `${baseClasses} bg-yellow-100 text-yellow-700 hover:bg-yellow-200`;
          case BusinessStatus.FEATURED:
            return `${baseClasses} bg-purple-100 text-purple-700 hover:bg-purple-200`;
          case BusinessStatus.PARTNER:
            return `${baseClasses} bg-red-100 text-red-700 hover:bg-red-200`;
          case BusinessStatus.PENDING:
            return `${baseClasses} bg-orange-100 text-orange-700 hover:bg-orange-200`;
          default:
            return `${baseClasses} bg-gray-100 text-gray-700`;
        }
    }
  };

  const animationClasses = animated ? 'animate-pulse hover:animate-none' : '';

  return (
    <span
      className={`
        ${getStatusClasses()} ${currentSize.container} ${currentSize.gap}
        ${animationClasses} ${className}
      `}
      title={`${statusConfig.name} - ${statusConfig.description}`}
    >
      {showIcon && (
        <IconComponent size={currentSize.icon} className="flex-shrink-0" />
      )}
      {showText && (
        <span className="truncate">{statusConfig.name}</span>
      )}
    </span>
  );
};

export default BusinessStatusBadge;
