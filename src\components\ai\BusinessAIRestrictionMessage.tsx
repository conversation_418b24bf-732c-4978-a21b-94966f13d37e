import React from 'react';
import { Brain, Store, Users, TrendingUp, Bar<PERSON>hart3, ArrowRight } from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';

interface BusinessAIRestrictionMessageProps {
  onNavigateToProfile?: () => void;
  onNavigateToAnalytics?: () => void;
}

const BusinessAIRestrictionMessage: React.FC<BusinessAIRestrictionMessageProps> = ({
  onNavigateToProfile,
  onNavigateToAnalytics
}) => {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card className="text-center">
        <CardHeader>
          <div className="flex justify-center mb-4">
            <div className="relative">
              <div className="bg-blue-100 p-4 rounded-full">
                <Brain className="text-blue-600" size={48} />
              </div>
              <div className="absolute -top-1 -right-1 bg-orange-500 p-1 rounded-full">
                <Store className="text-white" size={16} />
              </div>
            </div>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Recommandations IA - Réservées aux Consommateurs
          </h1>
          <p className="text-lg text-gray-600">
            Cette fonctionnalité est spécialement conçue pour aider les consommateurs à découvrir des produits
          </p>
        </CardHeader>
        
        <CardBody className="space-y-6">
          {/* Explication */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">
              Pourquoi cette restriction ?
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div className="flex items-start space-x-3">
                <Users className="text-blue-500 mt-1 flex-shrink-0" size={20} />
                <div>
                  <h3 className="font-medium text-gray-900">Expérience Consommateur</h3>
                  <p className="text-sm text-gray-600">
                    L'IA analyse les préférences d'achat pour recommander des produits personnalisés aux consommateurs
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <TrendingUp className="text-green-500 mt-1 flex-shrink-0" size={20} />
                <div>
                  <h3 className="font-medium text-gray-900">Objectif Différent</h3>
                  <p className="text-sm text-gray-600">
                    Les entreprises ont besoin d'outils d'analyse et de gestion plutôt que de recommandations d'achat
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Alternatives pour les entreprises */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Outils Disponibles pour Votre Entreprise
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="hover:shadow-md transition-shadow">
                <CardBody className="text-center p-4">
                  <div className="bg-blue-100 p-3 rounded-full w-fit mx-auto mb-3">
                    <BarChart3 className="text-blue-600" size={24} />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Analytics Business</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Analysez les performances de vos produits et les tendances de vente
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    fullWidth
                    onClick={onNavigateToAnalytics}
                  >
                    Voir Analytics
                  </Button>
                </CardBody>
              </Card>

              <Card className="hover:shadow-md transition-shadow">
                <CardBody className="text-center p-4">
                  <div className="bg-green-100 p-3 rounded-full w-fit mx-auto mb-3">
                    <Store className="text-green-600" size={24} />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Gestion Produits</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Gérez votre catalogue, prix et inventaire efficacement
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    fullWidth
                    onClick={() => window.location.href = '/product-management'}
                  >
                    Gérer Produits
                  </Button>
                </CardBody>
              </Card>

              <Card className="hover:shadow-md transition-shadow">
                <CardBody className="text-center p-4">
                  <div className="bg-purple-100 p-3 rounded-full w-fit mx-auto mb-3">
                    <Users className="text-purple-600" size={24} />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Profil Entreprise</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Optimisez votre profil pour attirer plus de clients
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    fullWidth
                    onClick={onNavigateToProfile}
                  >
                    Mon Profil
                  </Button>
                </CardBody>
              </Card>
            </div>
          </div>

          {/* Message d'encouragement */}
          <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg">
            <div className="flex items-center justify-center space-x-3 mb-3">
              <div className="bg-green-100 p-2 rounded-full">
                <TrendingUp className="text-green-600" size={20} />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                Développez Votre Entreprise
              </h3>
            </div>
            <p className="text-gray-600 mb-4">
              Concentrez-vous sur la croissance de votre business avec nos outils dédiés aux entreprises. 
              Analysez vos ventes, optimisez vos produits et atteignez plus de clients !
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                variant="primary"
                onClick={onNavigateToProfile}
                leftIcon={<Store size={18} />}
              >
                Accéder à Mon Entreprise
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.href = '/business-sales'}
                leftIcon={<BarChart3 size={18} />}
              >
                Voir Mes Ventes
              </Button>
            </div>
          </div>

          {/* Note informative */}
          <div className="text-center">
            <p className="text-sm text-gray-500">
              💡 <strong>Astuce :</strong> Créez un compte consommateur séparé si vous souhaitez 
              également faire des achats et recevoir des recommandations personnalisées.
            </p>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default BusinessAIRestrictionMessage;
