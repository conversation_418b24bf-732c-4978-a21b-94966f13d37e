import React from 'react';
import { MapPin, Globe, Calendar, Phone, Briefcase, Heart, User as UserIcon, Building, FileText, Mail } from 'lucide-react';
import { IUser, IBusinessUser, UserRole } from '../../types';

interface UserInfoCardProps {
  user: IUser | IBusinessUser;
  className?: string;
}

const UserInfoCard: React.FC<UserInfoCardProps> = ({ user, className = '' }) => {
  // Fonction pour nettoyer et valider les chaînes de caractères
  const cleanString = (str: string | undefined | null): string => {
    if (!str) return '';
    return str.replace(/[\x00-\x1F\x7F-\x9F]/g, '').trim();
  };

  // Vérification de sécurité pour éviter les erreurs d'affichage
  if (!user || !user.username) {
    return (
      <div className={`bg-white rounded-xl shadow-sm border border-gray-100 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  const isBusinessUser = user.role === UserRole.BUSINESS;
  const businessUser = isBusinessUser ? user as IBusinessUser : null;

  const formatJoinDate = (date: Date | string) => {
    try {
      const d = typeof date === 'string' ? new Date(date) : date;
      const options: Intl.DateTimeFormatOptions = { month: 'long', year: 'numeric' };
      return d.toLocaleDateString('fr-FR', options);
    } catch (error) {
      return 'Date inconnue';
    }
  };

  return (
    <div className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden ${className}`}>
      {/* Header avec gradient */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-4">
        <div className="flex items-center text-white">
          <div className="bg-white/20 rounded-lg p-2 mr-3">
            <UserIcon size={20} />
          </div>
          <h3 className="text-lg font-semibold">
            {isBusinessUser ? 'Informations Entreprise' : 'Informations Personnelles'}
          </h3>
        </div>
      </div>

      {/* Contenu */}
      <div className="p-6">
        <div className="space-y-4">
          {/* Nom d'utilisateur */}
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <UserIcon size={16} className="text-blue-600" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Nom d'utilisateur</p>
              <p className="text-sm font-semibold text-gray-900 truncate">@{cleanString(user.username) || 'Non défini'}</p>
            </div>
          </div>

          {/* Nom de l'entreprise (si business) */}
          {isBusinessUser && businessUser?.businessName && (
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <Building size={16} className="text-purple-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Entreprise</p>
                <p className="text-sm font-semibold text-gray-900 truncate">{cleanString(businessUser.businessName)}</p>
              </div>
            </div>
          )}

          {/* Email */}
          {user.email && (
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <Mail size={16} className="text-green-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Email</p>
                <p className="text-sm font-semibold text-gray-900 truncate">{cleanString(user.email)}</p>
              </div>
            </div>
          )}

          {/* Localisation */}
          {(user.city || user.country) && (
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex-shrink-0 w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                <MapPin size={16} className="text-red-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Localisation</p>
                <p className="text-sm font-semibold text-gray-900 truncate">
                  {user.city && user.country ? `${cleanString(user.city)}, ${cleanString(user.country)}` : cleanString(user.city) || cleanString(user.country)}
                </p>
              </div>
            </div>
          )}

          {/* Téléphone */}
          {user.phone && (
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <Phone size={16} className="text-orange-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Téléphone</p>
                <p className="text-sm font-semibold text-gray-900 truncate">{cleanString(user.phone)}</p>
              </div>
            </div>
          )}

          {/* Site web */}
          {user.website && (
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex-shrink-0 w-8 h-8 bg-cyan-100 rounded-lg flex items-center justify-center">
                <Globe size={16} className="text-cyan-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Site web</p>
                <a
                  href={cleanString(user.website).startsWith('http') ? cleanString(user.website) : `https://${cleanString(user.website)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm font-semibold text-blue-600 hover:text-blue-800 truncate block"
                >
                  {cleanString(user.website)}
                </a>
              </div>
            </div>
          )}

          {/* Profession (pour les utilisateurs standards) */}
          {!isBusinessUser && user.profession && (
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                <Briefcase size={16} className="text-indigo-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Profession</p>
                <p className="text-sm font-semibold text-gray-900 truncate">{cleanString(user.profession)}</p>
              </div>
            </div>
          )}

          {/* Catégorie d'entreprise (pour les business) */}
          {isBusinessUser && businessUser?.businessCategory && (
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                <Briefcase size={16} className="text-indigo-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Catégorie</p>
                <p className="text-sm font-semibold text-gray-900 truncate">{cleanString(businessUser.businessCategory)}</p>
              </div>
            </div>
          )}

          {/* Centres d'intérêt */}
          {user.interests && (
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex-shrink-0 w-8 h-8 bg-pink-100 rounded-lg flex items-center justify-center">
                <Heart size={16} className="text-pink-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Centres d'intérêt</p>
                <p className="text-sm font-semibold text-gray-900">{cleanString(user.interests)}</p>
              </div>
            </div>
          )}

          {/* Biographie */}
          {(user.bio || (isBusinessUser && businessUser?.businessDescription)) && (
            <div className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mt-1">
                  <FileText size={16} className="text-emerald-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                    {isBusinessUser ? 'Description' : 'Biographie'}
                  </p>
                  <p className="text-sm text-gray-700 leading-relaxed">
                    {isBusinessUser ? cleanString(businessUser?.businessDescription) : cleanString(user.bio)}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Âge (pour les utilisateurs standards) */}
          {!isBusinessUser && user.age && (
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Calendar size={16} className="text-yellow-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Âge</p>
                <p className="text-sm font-semibold text-gray-900">{user.age} ans</p>
              </div>
            </div>
          )}

          {/* Année de fondation (pour les entreprises) */}
          {isBusinessUser && businessUser?.foundedYear && (
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Calendar size={16} className="text-yellow-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Fondée en</p>
                <p className="text-sm font-semibold text-gray-900">{businessUser.foundedYear}</p>
              </div>
            </div>
          )}

          {/* Date d'inscription */}
          <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-100">
            <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Calendar size={16} className="text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Membre depuis</p>
              <p className="text-sm font-semibold text-gray-900">{formatJoinDate(user.createdAt)}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserInfoCard;
