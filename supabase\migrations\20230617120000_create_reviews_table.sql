-- Migration pour créer la table posts (avis) et le bucket de stockage post-images

-- C<PERSON><PERSON> de la table posts
CREATE TABLE IF NOT EXISTS posts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type VARCHAR(20) NOT NULL CHECK (type IN ('coup_de_coeur', 'coup_de_gueule')),
  business_name VARCHAR(255) NOT NULL,
  product_name VARCHAR(255),
  category VARCHAR(100),
  description TEXT,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  images JSONB DEFAULT '[]'::jsonb,
  tags JSONB DEFAULT '[]'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Création d'un index sur user_id pour améliorer les performances des requêtes
CREATE INDEX IF NOT EXISTS posts_user_id_idx ON posts(user_id);
CREATE INDEX IF NOT EXISTS posts_type_idx ON posts(type);

-- Activation de RLS (Row Level Security) sur les tables
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;

-- Politiques RLS pour la table posts
-- Tout le monde peut voir tous les avis
CREATE POLICY "Les utilisateurs peuvent voir tous les posts"
ON posts FOR SELECT
USING (true);

-- Seul l'auteur peut modifier ou supprimer ses propres avis
CREATE POLICY "Les utilisateurs peuvent modifier leurs propres posts"
ON posts FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres posts"
ON posts FOR DELETE
USING (auth.uid() = user_id);

-- Seuls les utilisateurs authentifiés peuvent créer des avis
CREATE POLICY "Les utilisateurs authentifiés peuvent créer des posts"
ON posts FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Création d'une fonction pour mettre à jour le timestamp updated_at lors des modifications
CREATE OR REPLACE FUNCTION update_posts_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Création d'un trigger pour appeler la fonction lors des mises à jour
CREATE TRIGGER update_posts_updated_at
BEFORE UPDATE ON posts
FOR EACH ROW
EXECUTE FUNCTION update_posts_updated_at_column();

-- Création du bucket de stockage pour les images de posts
-- Cette commande doit être exécutée avec les privilèges appropriés
INSERT INTO storage.buckets (id, name, public)
VALUES ('post-images', 'post-images', true)
ON CONFLICT (id) DO NOTHING;

-- Suppression des politiques existantes pour le stockage (au cas où)
DROP POLICY IF EXISTS "Les images de posts sont accessibles publiquement" ON storage.objects;
DROP POLICY IF EXISTS "Les utilisateurs peuvent télécharger leurs propres images de posts" ON storage.objects;
DROP POLICY IF EXISTS "Les utilisateurs peuvent supprimer leurs propres images de posts" ON storage.objects;

-- Politique pour permettre l'accès public en lecture aux images de posts
CREATE POLICY "Les images de posts sont accessibles publiquement"
ON storage.objects FOR SELECT
USING (bucket_id = 'post-images');

-- Création d'une politique pour permettre aux utilisateurs authentifiés de télécharger leurs propres images
CREATE POLICY "Les utilisateurs peuvent télécharger leurs propres images de posts"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'post-images' AND 
  (
    -- Permet les chemins avec 'posts' comme premier segment et l'ID utilisateur comme deuxième segment
    ((storage.foldername(name))[1] = 'posts' AND (storage.foldername(name))[2] = auth.uid()::text)
  )
);

-- Politique pour permettre aux utilisateurs de supprimer leurs propres images
CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres images de posts"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'post-images' AND 
  (
    -- Permet les chemins avec 'posts' comme premier segment et l'ID utilisateur comme deuxième segment
    ((storage.foldername(name))[1] = 'posts' AND (storage.foldername(name))[2] = auth.uid()::text)
  )
);