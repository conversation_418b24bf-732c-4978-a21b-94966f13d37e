import React from 'react';
import { useAuth } from '../context/AuthContext';
import { usePosts } from '../context/PostsContext';
import PostCard from '../components/posts/PostCard';
import { ThumbsUp } from 'lucide-react';

const MyRecommendationsPage: React.FC = () => {
  const { currentUser } = useAuth();
  const { posts, loading } = usePosts();

  if (!currentUser) {
    // This page should ideally not be accessible if user is not logged in
    // Or App.tsx should handle redirecting to AuthPage
    return <div className="text-center py-10">Veuillez vous connecter pour voir vos recommandations.</div>;
  }

  const recommendedPosts = posts.filter(post => 
    post.recommendations?.includes(currentUser.id)
  );

  if (loading) {
    return <div className="text-center py-10">Chargement de vos recommandations...</div>;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8 flex items-center">
        <ThumbsUp size={32} className="mr-3 text-blue-600" />
        <h1 className="text-3xl font-bold text-gray-900">Mes Recommandations</h1>
      </div>

      {recommendedPosts.length === 0 && !loading && (
        <div className="text-center py-10 bg-white p-6 rounded-lg shadow">
          <ThumbsUp size={48} className="mx-auto text-gray-400 mb-4" />
          <p className="text-xl text-gray-700 font-semibold">Vous n'avez encore recommandé aucun post.</p>
          <p className="text-gray-500 mt-2">Explorez et cliquez sur l'étoile "Recommander" sur les posts que vous appréciez !</p>
        </div>
      )}

      {recommendedPosts.length > 0 && (
        <div className="space-y-6">
          {recommendedPosts.map(post => (
            <PostCard key={post.id} post={post} />
          ))}
        </div>
      )}
    </div>
  );
};

export default MyRecommendationsPage;
