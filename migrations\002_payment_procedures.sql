-- =====================================================
-- MIGRATION: Procédures Stockées pour le Système de Paiement
-- Version: 1.0.0
-- Date: 2024-12-19
-- Description: Procédures stockées pour automatiser les processus
--              de paiement, abonnement et génération de reçus
-- =====================================================

-- =====================================================
-- 1. PROCÉDURE POUR CRÉER UN ESSAI GRATUIT
-- =====================================================
CREATE OR REPLACE FUNCTION create_free_trial(
    p_business_id UUID
)
RETURNS TABLE(
    subscription_id UUID,
    plan_id TEXT,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    success BOOLEAN,
    message TEXT
) AS $$
DECLARE
    v_subscription_id UUID;
    v_start_date TIMESTAMP WITH TIME ZONE;
    v_end_date TIMESTAMP WITH TIME ZONE;
    v_trial_exists BOOLEAN;
BEGIN
    -- Vérifier si l'entreprise a déjà utilisé son essai gratuit
    SELECT EXISTS(
        SELECT 1 FROM business_subscriptions 
        WHERE business_id = p_business_id AND trial_used = true
    ) INTO v_trial_exists;
    
    IF v_trial_exists THEN
        RETURN QUERY SELECT 
            NULL::UUID, 
            NULL::TEXT, 
            NULL::TIMESTAMP WITH TIME ZONE, 
            NULL::TIMESTAMP WITH TIME ZONE,
            false, 
            'Essai gratuit déjà utilisé pour cette entreprise'::TEXT;
        RETURN;
    END IF;
    
    -- Désactiver tout abonnement actuel
    UPDATE business_subscriptions 
    SET is_active = false, updated_at = CURRENT_TIMESTAMP
    WHERE business_id = p_business_id AND is_active = true;
    
    -- Créer le nouvel abonnement d'essai
    v_start_date := CURRENT_TIMESTAMP;
    v_end_date := v_start_date + INTERVAL '7 days';
    
    INSERT INTO business_subscriptions (
        business_id, plan_id, start_date, end_date, 
        is_active, is_trial, trial_used, auto_renew,
        payment_status, payment_method, stripe_subscription_id
    ) VALUES (
        p_business_id, 'free-trial', v_start_date, v_end_date,
        true, true, true, false,
        'paid', NULL, NULL
    ) RETURNING id INTO v_subscription_id;
    
    -- Enregistrer l'événement
    INSERT INTO payment_events (payment_intent_id, event_type, event_data, source)
    VALUES (
        'trial_' || v_subscription_id::TEXT,
        'trial_created',
        jsonb_build_object(
            'business_id', p_business_id,
            'subscription_id', v_subscription_id,
            'plan_id', 'free-trial'
        ),
        'system'
    );
    
    RETURN QUERY SELECT 
        v_subscription_id, 
        'free-trial'::TEXT, 
        v_start_date, 
        v_end_date,
        true, 
        'Essai gratuit créé avec succès'::TEXT;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 2. PROCÉDURE POUR TRAITER UN PAIEMENT
-- =====================================================
CREATE OR REPLACE FUNCTION process_payment(
    p_payment_intent_id TEXT,
    p_processor_payment_id TEXT DEFAULT NULL,
    p_processor_response JSONB DEFAULT NULL
)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT,
    receipt_id TEXT
) AS $$
DECLARE
    v_payment_intent payment_intents%ROWTYPE;
    v_plan subscription_plans%ROWTYPE;
    v_receipt_id TEXT;
    v_invoice_number TEXT;
    v_subscription_id UUID;
    v_business_info JSONB;
BEGIN
    -- Récupérer l'intention de paiement
    SELECT * INTO v_payment_intent 
    FROM payment_intents 
    WHERE id = p_payment_intent_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'Intention de paiement non trouvée'::TEXT, NULL::TEXT;
        RETURN;
    END IF;
    
    IF v_payment_intent.status != 'pending' THEN
        RETURN QUERY SELECT false, 'Intention de paiement déjà traitée'::TEXT, NULL::TEXT;
        RETURN;
    END IF;
    
    -- Récupérer le plan
    SELECT * INTO v_plan 
    FROM subscription_plans 
    WHERE id = v_payment_intent.plan_id;
    
    -- Mettre à jour l'intention de paiement
    UPDATE payment_intents 
    SET 
        status = 'succeeded',
        processor_payment_id = p_processor_payment_id,
        processor_response = p_processor_response,
        processed_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_payment_intent_id;
    
    -- Créer l'abonnement
    INSERT INTO business_subscriptions (
        business_id, plan_id, 
        start_date, end_date,
        is_active, is_trial, auto_renew,
        payment_status, payment_method, stripe_subscription_id
    ) VALUES (
        v_payment_intent.business_id, 
        v_payment_intent.plan_id,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP + (v_plan.duration_days || ' days')::INTERVAL,
        true, false, true,
        'paid', v_payment_intent.payment_method_id, p_processor_payment_id
    ) RETURNING id INTO v_subscription_id;
    
    -- Récupérer les informations business pour le reçu
    SELECT jsonb_build_object(
        'name', COALESCE(bp.business_name, 'Entreprise'),
        'email', COALESCE(bp.email, u.email, ''),
        'address', bp.address,
        'tax_id', bp.tax_id
    ) INTO v_business_info
    FROM users u
    LEFT JOIN business_profiles bp ON u.id = bp.id
    WHERE u.id = v_payment_intent.business_id;
    
    -- Générer le reçu
    v_receipt_id := 'receipt_' || EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::BIGINT || '_' || 
                    substring(md5(random()::text), 1, 8);
    v_invoice_number := generate_invoice_number();
    
    INSERT INTO payment_receipts (
        id, payment_intent_id, business_id, plan_id,
        invoice_number, plan_name, amount, currency,
        payment_method, transaction_id, status,
        business_info, paid_at
    ) VALUES (
        v_receipt_id,
        p_payment_intent_id,
        v_payment_intent.business_id,
        v_payment_intent.plan_id,
        v_invoice_number,
        v_plan.name,
        v_payment_intent.amount,
        v_payment_intent.currency,
        (SELECT to_jsonb(pm.*) FROM payment_methods pm WHERE pm.id = v_payment_intent.payment_method_id),
        COALESCE(p_processor_payment_id, 'txn_' || EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::BIGINT),
        'paid',
        v_business_info,
        CURRENT_TIMESTAMP
    );
    
    -- Enregistrer l'événement
    INSERT INTO payment_events (payment_intent_id, event_type, event_data, source)
    VALUES (
        p_payment_intent_id,
        'payment_succeeded',
        jsonb_build_object(
            'receipt_id', v_receipt_id,
            'subscription_id', v_subscription_id,
            'amount', v_payment_intent.amount,
            'currency', v_payment_intent.currency
        ),
        'system'
    );
    
    RETURN QUERY SELECT true, 'Paiement traité avec succès'::TEXT, v_receipt_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. PROCÉDURE POUR ÉCHEC DE PAIEMENT
-- =====================================================
CREATE OR REPLACE FUNCTION fail_payment(
    p_payment_intent_id TEXT,
    p_failure_code TEXT,
    p_failure_message TEXT,
    p_processor_response JSONB DEFAULT NULL
)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT
) AS $$
BEGIN
    -- Mettre à jour l'intention de paiement
    UPDATE payment_intents 
    SET 
        status = 'failed',
        failure_code = p_failure_code,
        failure_message = p_failure_message,
        processor_response = p_processor_response,
        processed_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_payment_intent_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'Intention de paiement non trouvée'::TEXT;
        RETURN;
    END IF;
    
    -- Enregistrer l'événement
    INSERT INTO payment_events (payment_intent_id, event_type, event_data, source)
    VALUES (
        p_payment_intent_id,
        'payment_failed',
        jsonb_build_object(
            'failure_code', p_failure_code,
            'failure_message', p_failure_message
        ),
        'system'
    );
    
    RETURN QUERY SELECT true, 'Échec de paiement enregistré'::TEXT;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. PROCÉDURE POUR ANNULER UN ABONNEMENT
-- =====================================================
CREATE OR REPLACE FUNCTION cancel_subscription(
    p_business_id UUID,
    p_reason TEXT DEFAULT NULL,
    p_immediate BOOLEAN DEFAULT false
)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT,
    cancelled_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    v_subscription business_subscriptions%ROWTYPE;
    v_cancelled_at TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Récupérer l'abonnement actif
    SELECT * INTO v_subscription
    FROM business_subscriptions
    WHERE business_id = p_business_id AND is_active = true
    ORDER BY created_at DESC
    LIMIT 1;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'Aucun abonnement actif trouvé'::TEXT, NULL::TIMESTAMP WITH TIME ZONE;
        RETURN;
    END IF;
    
    -- Déterminer la date d'annulation
    IF p_immediate THEN
        v_cancelled_at := CURRENT_TIMESTAMP;
        -- Annulation immédiate
        UPDATE business_subscriptions
        SET 
            is_active = false,
            auto_renew = false,
            cancelled_at = v_cancelled_at,
            cancellation_reason = p_reason,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = v_subscription.id;
    ELSE
        v_cancelled_at := v_subscription.end_date;
        -- Annulation à la fin de la période
        UPDATE business_subscriptions
        SET 
            auto_renew = false,
            cancelled_at = v_cancelled_at,
            cancellation_reason = p_reason,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = v_subscription.id;
    END IF;
    
    -- Enregistrer l'événement
    INSERT INTO payment_events (payment_intent_id, event_type, event_data, source)
    VALUES (
        'cancel_' || v_subscription.id::TEXT,
        'subscription_cancelled',
        jsonb_build_object(
            'subscription_id', v_subscription.id,
            'business_id', p_business_id,
            'reason', p_reason,
            'immediate', p_immediate
        ),
        'system'
    );
    
    RETURN QUERY SELECT 
        true, 
        CASE 
            WHEN p_immediate THEN 'Abonnement annulé immédiatement'
            ELSE 'Abonnement sera annulé à la fin de la période'
        END::TEXT,
        v_cancelled_at;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. PROCÉDURE POUR APPLIQUER UN COUPON
-- =====================================================
CREATE OR REPLACE FUNCTION apply_coupon(
    p_payment_intent_id TEXT,
    p_coupon_code TEXT
)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT,
    discount_amount DECIMAL,
    final_amount DECIMAL
) AS $$
DECLARE
    v_payment_intent payment_intents%ROWTYPE;
    v_coupon subscription_coupons%ROWTYPE;
    v_discount_amount DECIMAL;
    v_final_amount DECIMAL;
    v_can_use BOOLEAN;
BEGIN
    -- Récupérer l'intention de paiement
    SELECT * INTO v_payment_intent 
    FROM payment_intents 
    WHERE id = p_payment_intent_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'Intention de paiement non trouvée'::TEXT, 0::DECIMAL, 0::DECIMAL;
        RETURN;
    END IF;
    
    -- Vérifier si le coupon peut être utilisé
    SELECT can_use_coupon(p_coupon_code, v_payment_intent.business_id, v_payment_intent.plan_id, v_payment_intent.amount)
    INTO v_can_use;
    
    IF NOT v_can_use THEN
        RETURN QUERY SELECT false, 'Coupon non valide ou non applicable'::TEXT, 0::DECIMAL, v_payment_intent.amount;
        RETURN;
    END IF;
    
    -- Récupérer le coupon
    SELECT * INTO v_coupon 
    FROM subscription_coupons 
    WHERE code = p_coupon_code AND is_active = true;
    
    -- Calculer la réduction
    SELECT calculate_coupon_discount(p_coupon_code, v_payment_intent.amount) INTO v_discount_amount;
    v_final_amount := v_payment_intent.amount - v_discount_amount;
    
    -- Mettre à jour l'intention de paiement
    UPDATE payment_intents
    SET 
        amount = v_final_amount,
        metadata = COALESCE(metadata, '{}'::jsonb) || jsonb_build_object(
            'coupon_code', p_coupon_code,
            'original_amount', v_payment_intent.amount,
            'discount_amount', v_discount_amount
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_payment_intent_id;
    
    -- Enregistrer l'utilisation du coupon
    INSERT INTO coupon_usages (
        coupon_id, business_id, payment_intent_id,
        discount_amount, original_amount, final_amount
    ) VALUES (
        v_coupon.id, v_payment_intent.business_id, p_payment_intent_id,
        v_discount_amount, v_payment_intent.amount, v_final_amount
    );
    
    -- Mettre à jour le compteur d'utilisation du coupon
    UPDATE subscription_coupons
    SET usage_count = usage_count + 1
    WHERE id = v_coupon.id;
    
    RETURN QUERY SELECT 
        true, 
        'Coupon appliqué avec succès'::TEXT, 
        v_discount_amount, 
        v_final_amount;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. PROCÉDURE POUR NETTOYER LES ABONNEMENTS EXPIRÉS
-- =====================================================
CREATE OR REPLACE FUNCTION cleanup_expired_subscriptions()
RETURNS TABLE(
    expired_count INTEGER,
    message TEXT
) AS $$
DECLARE
    v_expired_count INTEGER;
BEGIN
    -- Désactiver les abonnements expirés
    UPDATE business_subscriptions
    SET 
        is_active = false,
        updated_at = CURRENT_TIMESTAMP
    WHERE 
        is_active = true 
        AND end_date < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS v_expired_count = ROW_COUNT;
    
    -- Enregistrer l'événement de nettoyage
    IF v_expired_count > 0 THEN
        INSERT INTO payment_events (payment_intent_id, event_type, event_data, source)
        VALUES (
            'cleanup_' || EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::BIGINT,
            'subscriptions_expired',
            jsonb_build_object('expired_count', v_expired_count),
            'system'
        );
    END IF;
    
    RETURN QUERY SELECT 
        v_expired_count, 
        v_expired_count || ' abonnements expirés désactivés'::TEXT;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. FONCTION POUR OBTENIR LE STATUT D'ABONNEMENT
-- =====================================================
CREATE OR REPLACE FUNCTION get_subscription_status(p_business_id UUID)
RETURNS TABLE(
    is_active BOOLEAN,
    plan_id TEXT,
    plan_name TEXT,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    days_remaining INTEGER,
    is_trial BOOLEAN,
    auto_renew BOOLEAN,
    features JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bs.is_active,
        bs.plan_id,
        sp.name as plan_name,
        bs.start_date,
        bs.end_date,
        GREATEST(0, EXTRACT(DAYS FROM (bs.end_date - CURRENT_TIMESTAMP))::INTEGER) as days_remaining,
        bs.is_trial,
        bs.auto_renew,
        sp.features
    FROM business_subscriptions bs
    JOIN subscription_plans sp ON bs.plan_id = sp.id
    WHERE bs.business_id = p_business_id
    AND bs.is_active = true
    ORDER BY bs.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;
