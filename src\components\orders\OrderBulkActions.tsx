import React, { useState } from 'react';
import {
  CheckSquare, Download, Printer, Mail, Truck, CheckCircle,
  X, AlertTriangle, Package, FileText, MessageSquare
} from 'lucide-react';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';
import '../../styles/OrderBulkActions.css';

interface OrderBulkActionsProps {
  selectedOrderIds: string[];
  totalOrders: number;
  onSelectAll: () => void;
  onDeselectAll: () => void;
  onBulkStatusUpdate: (orderIds: string[], newStatus: string) => void;
  onBulkExport: (orderIds: string[], format: 'pdf' | 'excel' | 'csv') => void;
  onBulkPrint: (orderIds: string[]) => void;
  onBulkEmail: (orderIds: string[]) => void;
}

const OrderBulkActions: React.FC<OrderBulkActionsProps> = ({
  selectedOrderIds,
  totalOrders,
  onSelectAll,
  onDeselectAll,
  onBulkStatusUpdate,
  onBulkExport,
  onBulkPrint,
  onBulkEmail
}) => {
  const [showStatusMenu, setShowStatusMenu] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState<{
    type: string;
    data: any;
  } | null>(null);

  const selectedCount = selectedOrderIds.length;
  const isAllSelected = selectedCount === totalOrders && totalOrders > 0;
  const isPartiallySelected = selectedCount > 0 && selectedCount < totalOrders;

  const handleStatusUpdate = (newStatus: string) => {
    setPendingAction({
      type: 'status_update',
      data: { status: newStatus }
    });
    setShowConfirmDialog(true);
    setShowStatusMenu(false);
  };

  const handleExport = (format: 'pdf' | 'excel' | 'csv') => {
    onBulkExport(selectedOrderIds, format);
    setShowExportMenu(false);
  };

  const confirmAction = () => {
    if (pendingAction) {
      switch (pendingAction.type) {
        case 'status_update':
          onBulkStatusUpdate(selectedOrderIds, pendingAction.data.status);
          break;
      }
    }
    setShowConfirmDialog(false);
    setPendingAction(null);
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return 'En attente';
      case 'confirmed': return 'Confirmées';
      case 'processing': return 'En traitement';
      case 'shipped': return 'Expédiées';
      case 'delivered': return 'Livrées';
      case 'cancelled': return 'Annulées';
      default: return status;
    }
  };

  const getActionDescription = () => {
    if (!pendingAction) return '';
    
    switch (pendingAction.type) {
      case 'status_update':
        return `Changer le statut de ${selectedCount} commande${selectedCount > 1 ? 's' : ''} vers "${getStatusLabel(pendingAction.data.status)}"`;
      default:
        return '';
    }
  };

  if (selectedCount === 0) {
    return null;
  }

  return (
    <>
      <Card className="bulk-actions-card">
        <CardBody>
          <div className="bulk-actions-container">
            {/* Sélection */}
            <div className="selection-info">
              <div className="selection-checkbox">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={(input) => {
                    if (input) input.indeterminate = isPartiallySelected;
                  }}
                  onChange={isAllSelected ? onDeselectAll : onSelectAll}
                />
              </div>
              <div className="selection-text">
                <span className="selected-count">
                  {selectedCount} commande{selectedCount > 1 ? 's' : ''} sélectionnée{selectedCount > 1 ? 's' : ''}
                </span>
                {selectedCount < totalOrders && (
                  <button className="select-all-btn" onClick={onSelectAll}>
                    Tout sélectionner ({totalOrders})
                  </button>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="bulk-actions">
              {/* Changement de statut */}
              <div className="action-dropdown">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowStatusMenu(!showStatusMenu)}
                >
                  <CheckCircle size={14} />
                  Changer statut
                </Button>
                
                {showStatusMenu && (
                  <div className="dropdown-menu">
                    <button onClick={() => handleStatusUpdate('confirmed')}>
                      <CheckCircle size={14} />
                      Confirmer
                    </button>
                    <button onClick={() => handleStatusUpdate('processing')}>
                      <Package size={14} />
                      En traitement
                    </button>
                    <button onClick={() => handleStatusUpdate('shipped')}>
                      <Truck size={14} />
                      Expédier
                    </button>
                    <button onClick={() => handleStatusUpdate('delivered')}>
                      <CheckCircle size={14} />
                      Marquer livrées
                    </button>
                    <button onClick={() => handleStatusUpdate('cancelled')}>
                      <X size={14} />
                      Annuler
                    </button>
                  </div>
                )}
              </div>

              {/* Export */}
              <div className="action-dropdown">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowExportMenu(!showExportMenu)}
                >
                  <Download size={14} />
                  Exporter
                </Button>
                
                {showExportMenu && (
                  <div className="dropdown-menu">
                    <button onClick={() => handleExport('pdf')}>
                      <FileText size={14} />
                      PDF
                    </button>
                    <button onClick={() => handleExport('excel')}>
                      <FileText size={14} />
                      Excel
                    </button>
                    <button onClick={() => handleExport('csv')}>
                      <FileText size={14} />
                      CSV
                    </button>
                  </div>
                )}
              </div>

              {/* Impression */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkPrint(selectedOrderIds)}
              >
                <Printer size={14} />
                Imprimer
              </Button>

              {/* Email */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkEmail(selectedOrderIds)}
              >
                <Mail size={14} />
                Notifier clients
              </Button>

              {/* Désélectionner */}
              <Button
                variant="ghost"
                size="sm"
                onClick={onDeselectAll}
              >
                <X size={14} />
                Désélectionner
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Dialog de confirmation */}
      {showConfirmDialog && (
        <div className="confirmation-overlay">
          <div className="confirmation-dialog">
            <div className="confirmation-header">
              <AlertTriangle size={24} className="warning-icon" />
              <h3>Confirmer l'action</h3>
            </div>
            
            <div className="confirmation-body">
              <p>{getActionDescription()}</p>
              <p className="confirmation-warning">
                Cette action ne peut pas être annulée. Êtes-vous sûr de vouloir continuer ?
              </p>
            </div>
            
            <div className="confirmation-actions">
              <Button
                variant="outline"
                onClick={() => setShowConfirmDialog(false)}
              >
                Annuler
              </Button>
              <Button
                onClick={confirmAction}
              >
                Confirmer
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default OrderBulkActions;
