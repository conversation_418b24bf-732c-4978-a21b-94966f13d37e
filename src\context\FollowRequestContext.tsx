import React, { createContext, useContext, useState, useEffect } from 'react';
import { IFollowRequest, IPrivacySettings } from '../types';
import { FollowRequestService } from '../services/followRequestService';
import { useAuth } from './AuthContext';
import { supabase } from '../lib/supabase';

interface FollowRequestContextType {
  // Demandes de suivi
  receivedRequests: IFollowRequest[];
  sentRequests: IFollowRequest[];
  pendingRequestsCount: number;
  loading: boolean;
  
  // Actions
  sendFollowRequest: (targetId: string, message?: string) => Promise<{ success: boolean; error?: string }>;
  respondToRequest: (requestId: string, response: 'accepted' | 'rejected') => Promise<{ success: boolean; error?: string }>;
  cancelRequest: (requestId: string) => Promise<{ success: boolean; error?: string }>;
  
  // Paramètres de confidentialité
  privacySettings: IPrivacySettings | null;
  updatePrivacySettings: (settings: Partial<IPrivacySettings>) => Promise<{ success: boolean; error?: string }>;
  
  // Utilitaires
  refreshRequests: () => Promise<void>;
  isRequestPending: (targetId: string) => boolean;
}

const FollowRequestContext = createContext<FollowRequestContextType>({
  receivedRequests: [],
  sentRequests: [],
  pendingRequestsCount: 0,
  loading: false,
  sendFollowRequest: async () => ({ success: false }),
  respondToRequest: async () => ({ success: false }),
  cancelRequest: async () => ({ success: false }),
  privacySettings: null,
  updatePrivacySettings: async () => ({ success: false }),
  refreshRequests: async () => {},
  isRequestPending: () => false,
});

export const useFollowRequests = () => {
  const context = useContext(FollowRequestContext);
  if (!context) {
    throw new Error('useFollowRequests must be used within a FollowRequestProvider');
  }
  return context;
};

interface FollowRequestProviderProps {
  children: React.ReactNode;
}

export const FollowRequestProvider: React.FC<FollowRequestProviderProps> = ({ children }) => {
  const { currentUser } = useAuth();
  const [receivedRequests, setReceivedRequests] = useState<IFollowRequest[]>([]);
  const [sentRequests, setSentRequests] = useState<IFollowRequest[]>([]);
  const [privacySettings, setPrivacySettings] = useState<IPrivacySettings | null>(null);
  const [loading, setLoading] = useState(false);

  // Charger les données initiales
  useEffect(() => {
    if (currentUser) {
      loadInitialData();
      setupRealtimeSubscriptions();
    }
  }, [currentUser]);

  const loadInitialData = async () => {
    if (!currentUser) return;
    
    setLoading(true);
    try {
      await Promise.all([
        loadReceivedRequests(),
        loadSentRequests(),
        loadPrivacySettings()
      ]);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadReceivedRequests = async () => {
    if (!currentUser) return;
    
    try {
      const requests = await FollowRequestService.getReceivedFollowRequests(currentUser.id);
      setReceivedRequests(requests);
    } catch (error) {
      console.error('Erreur lors du chargement des demandes reçues:', error);
    }
  };

  const loadSentRequests = async () => {
    if (!currentUser) return;
    
    try {
      const requests = await FollowRequestService.getSentFollowRequests(currentUser.id);
      setSentRequests(requests);
    } catch (error) {
      console.error('Erreur lors du chargement des demandes envoyées:', error);
    }
  };

  const loadPrivacySettings = async () => {
    if (!currentUser) return;
    
    try {
      const { data, error } = await supabase
        .from('privacy_settings')
        .select('*')
        .eq('user_id', currentUser.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Erreur lors du chargement des paramètres:', error);
        return;
      }

      if (data) {
        setPrivacySettings({
          id: data.id,
          userId: data.user_id,
          requireFollowApproval: data.require_follow_approval,
          allowFollowRequests: data.allow_follow_requests,
          autoAcceptVerifiedUsers: data.auto_accept_verified_users,
          autoAcceptBusinessUsers: data.auto_accept_business_users,
          notifyFollowRequests: data.notify_follow_requests,
          notifyNewFollowers: data.notify_new_followers,
          notifyFollowAccepted: data.notify_follow_accepted,
          notifyFollowRejected: data.notify_follow_rejected,
          profileVisibility: data.profile_visibility,
          postsVisibility: data.posts_visibility,
          createdAt: new Date(data.created_at),
          updatedAt: new Date(data.updated_at)
        });
      } else {
        // Créer des paramètres par défaut
        const { data: newSettings } = await supabase
          .from('privacy_settings')
          .insert([{ user_id: currentUser.id }])
          .select()
          .single();

        if (newSettings) {
          setPrivacySettings({
            id: newSettings.id,
            userId: newSettings.user_id,
            requireFollowApproval: newSettings.require_follow_approval,
            allowFollowRequests: newSettings.allow_follow_requests,
            autoAcceptVerifiedUsers: newSettings.auto_accept_verified_users,
            autoAcceptBusinessUsers: newSettings.auto_accept_business_users,
            notifyFollowRequests: newSettings.notify_follow_requests,
            notifyNewFollowers: newSettings.notify_new_followers,
            notifyFollowAccepted: newSettings.notify_follow_accepted,
            notifyFollowRejected: newSettings.notify_follow_rejected,
            profileVisibility: newSettings.profile_visibility,
            postsVisibility: newSettings.posts_visibility,
            createdAt: new Date(newSettings.created_at),
            updatedAt: new Date(newSettings.updated_at)
          });
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement des paramètres de confidentialité:', error);
    }
  };

  const setupRealtimeSubscriptions = () => {
    if (!currentUser) return;

    // Écouter les nouvelles demandes de suivi reçues
    const receivedSubscription = supabase
      .channel('follow-requests-received')
      .on('postgres_changes', {
        event: 'INSERT',
        table: 'follow_requests',
        filter: `target_id=eq.${currentUser.id}`
      }, () => {
        loadReceivedRequests();
      })
      .on('postgres_changes', {
        event: 'UPDATE',
        table: 'follow_requests',
        filter: `target_id=eq.${currentUser.id}`
      }, () => {
        loadReceivedRequests();
      })
      .subscribe();

    // Écouter les mises à jour des demandes envoyées
    const sentSubscription = supabase
      .channel('follow-requests-sent')
      .on('postgres_changes', {
        event: 'UPDATE',
        table: 'follow_requests',
        filter: `requester_id=eq.${currentUser.id}`
      }, () => {
        loadSentRequests();
      })
      .subscribe();

    return () => {
      receivedSubscription.unsubscribe();
      sentSubscription.unsubscribe();
    };
  };

  const sendFollowRequest = async (targetId: string, message?: string) => {
    if (!currentUser) return { success: false, error: 'Non connecté' };

    const result = await FollowRequestService.createFollowRequest(currentUser.id, targetId, message);
    
    if (result.success) {
      // Recharger les demandes envoyées
      await loadSentRequests();
    }
    
    return result;
  };

  const respondToRequest = async (requestId: string, response: 'accepted' | 'rejected') => {
    if (!currentUser) return { success: false, error: 'Non connecté' };

    const result = await FollowRequestService.respondToFollowRequest(requestId, response, currentUser.id);
    
    if (result.success) {
      // Recharger les demandes reçues
      await loadReceivedRequests();
    }
    
    return result;
  };

  const cancelRequest = async (requestId: string) => {
    if (!currentUser) return { success: false, error: 'Non connecté' };

    const result = await FollowRequestService.cancelFollowRequest(requestId, currentUser.id);
    
    if (result.success) {
      // Recharger les demandes envoyées
      await loadSentRequests();
    }
    
    return result;
  };

  const updatePrivacySettings = async (settings: Partial<IPrivacySettings>) => {
    if (!currentUser || !privacySettings) return { success: false, error: 'Non connecté' };

    try {
      const { error } = await supabase
        .from('privacy_settings')
        .update({
          require_follow_approval: settings.requireFollowApproval,
          allow_follow_requests: settings.allowFollowRequests,
          auto_accept_verified_users: settings.autoAcceptVerifiedUsers,
          auto_accept_business_users: settings.autoAcceptBusinessUsers,
          notify_follow_requests: settings.notifyFollowRequests,
          notify_new_followers: settings.notifyNewFollowers,
          notify_follow_accepted: settings.notifyFollowAccepted,
          notify_follow_rejected: settings.notifyFollowRejected,
          profile_visibility: settings.profileVisibility,
          posts_visibility: settings.postsVisibility,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', currentUser.id);

      if (error) {
        return { success: false, error: 'Erreur lors de la mise à jour' };
      }

      // Mettre à jour l'état local
      setPrivacySettings(prev => prev ? { ...prev, ...settings } : null);
      
      return { success: true };

    } catch (error) {
      console.error('Erreur lors de la mise à jour des paramètres:', error);
      return { success: false, error: 'Erreur interne' };
    }
  };

  const refreshRequests = async () => {
    await loadInitialData();
  };

  const isRequestPending = (targetId: string): boolean => {
    return sentRequests.some(request => 
      request.targetId === targetId && request.status === 'pending'
    );
  };

  const pendingRequestsCount = receivedRequests.filter(req => req.status === 'pending').length;

  return (
    <FollowRequestContext.Provider value={{
      receivedRequests,
      sentRequests,
      pendingRequestsCount,
      loading,
      sendFollowRequest,
      respondToRequest,
      cancelRequest,
      privacySettings,
      updatePrivacySettings,
      refreshRequests,
      isRequestPending
    }}>
      {children}
    </FollowRequestContext.Provider>
  );
};

export default FollowRequestContext;
