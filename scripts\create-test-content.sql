-- SCRIPT RAPIDE POUR CRÉER DU CONTENU DE TEST
-- Exécutez ce script pour créer immédiatement du contenu visible dans la modération

-- =====================================================
-- 1. CRÉER UN UTILISATEUR DE TEST
-- =====================================================

DO $$
DECLARE
    test_user_id uuid := gen_random_uuid();
    test_business_id uuid := gen_random_uuid();
    test_campaign_id uuid := gen_random_uuid();
BEGIN
    RAISE NOTICE '🚀 CRÉATION RAPIDE DE CONTENU DE TEST';
    RAISE NOTICE '===================================';
    
    -- <PERSON><PERSON><PERSON> un utilisateur de test
    INSERT INTO profiles (
        id, username, email, role, status, is_verified,
        profile_picture, country, city, bio, created_at, updated_at
    ) VALUES (
        test_user_id,
        'test_content_user',
        '<EMAIL>',
        'standard',
        'member',
        true,
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        'France',
        'Paris',
        'Utilisateur de test pour la modération de contenu',
        NOW(),
        NOW()
    ) ON CONFLICT (email) DO UPDATE SET updated_at = NOW();
    
    RAISE NOTICE '✅ Utilisateur de test créé: %', test_user_id;
    
    -- Créer une entreprise de test
    INSERT INTO profiles (
        id, username, email, role, status, is_verified,
        profile_picture, country, city, bio, created_at, updated_at
    ) VALUES (
        test_business_id,
        'test_content_business',
        '<EMAIL>',
        'business',
        'contributor',
        true,
        'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=150',
        'France',
        'Lyon',
        'Entreprise de test pour la modération de contenu',
        NOW(),
        NOW()
    ) ON CONFLICT (email) DO UPDATE SET updated_at = NOW();
    
    INSERT INTO business_profiles (
        id, business_name, business_description, business_category,
        business_status, business_verified, created_at, updated_at
    ) VALUES (
        test_business_id,
        'Test Content Business',
        'Entreprise créée pour tester la modération de contenu',
        'Technology',
        'verified',
        true,
        NOW(),
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET updated_at = NOW();
    
    RAISE NOTICE '✅ Entreprise de test créée: %', test_business_id;
    
    -- Créer des posts de test variés
    INSERT INTO posts (
        id, user_id, type, business_name, product_name, category,
        description, rating, created_at, updated_at
    ) VALUES 
    (
        gen_random_uuid(),
        test_user_id,
        'coup_de_coeur',
        'Restaurant Le Délice',
        'Menu Gastronomique',
        'Restaurant',
        'Excellent restaurant ! La cuisine est raffinée, le service impeccable. Je recommande vivement le menu dégustation. Une expérience culinaire exceptionnelle.',
        5,
        NOW() - INTERVAL '2 days',
        NOW()
    ),
    (
        gen_random_uuid(),
        test_user_id,
        'coup_de_gueule',
        'TechStore Pro',
        'Smartphone X1',
        'Technology',
        'Très déçu de cet achat ! Le téléphone a des bugs constants, l''écran s''est fissuré après 2 jours. Service client inexistant. Je déconseille fortement.',
        1,
        NOW() - INTERVAL '1 day',
        NOW()
    ),
    (
        gen_random_uuid(),
        test_user_id,
        'coup_de_coeur',
        'Fashion Boutique',
        'Robe d''été',
        'Fashion',
        'Magnifique robe ! La qualité du tissu est excellente, la coupe parfaite. Livraison rapide et emballage soigné. Je recommande cette boutique.',
        4,
        NOW() - INTERVAL '5 hours',
        NOW()
    ),
    (
        gen_random_uuid(),
        test_user_id,
        'coup_de_gueule',
        'Service Express',
        'Livraison rapide',
        'Services',
        'Service de livraison catastrophique ! Colis arrivé en retard et endommagé. Aucun suivi, aucune excuse. À éviter absolument.',
        1,
        NOW() - INTERVAL '2 hours',
        NOW()
    ),
    (
        gen_random_uuid(),
        test_user_id,
        'coup_de_coeur',
        'Fitness Center Elite',
        'Abonnement Premium',
        'Sports & Fitness',
        'Salle de sport fantastique ! Équipements modernes, coachs compétents, ambiance motivante. Excellent rapport qualité-prix.',
        5,
        NOW() - INTERVAL '30 minutes',
        NOW()
    );
    
    RAISE NOTICE '✅ 5 posts de test créés';
    
    -- Créer une campagne publicitaire
    INSERT INTO ad_campaigns (
        id, business_id, title, description, budget, status,
        start_date, end_date, created_at, updated_at
    ) VALUES (
        test_campaign_id,
        test_business_id,
        'Promotion Spéciale - Test Content',
        'Campagne publicitaire de test pour la modération des commentaires',
        1500.00,
        'active',
        NOW(),
        NOW() + INTERVAL '30 days',
        NOW(),
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET updated_at = NOW();
    
    RAISE NOTICE '✅ Campagne publicitaire créée: %', test_campaign_id;
    
    -- Créer des commentaires de publicité
    INSERT INTO ad_comments (
        id, campaign_id, user_id, content, is_approved, is_flagged,
        flagged_reason, created_at, updated_at
    ) VALUES 
    (
        gen_random_uuid(),
        test_campaign_id,
        test_user_id,
        'Super promotion ! Cette entreprise a l''air très sérieuse et professionnelle. Je vais tester leurs services.',
        true,
        false,
        NULL,
        NOW() - INTERVAL '1 day',
        NOW()
    ),
    (
        gen_random_uuid(),
        test_campaign_id,
        test_user_id,
        'Publicité intéressante, mais j''aimerais plus d''informations sur les garanties et le service après-vente.',
        true,
        false,
        NULL,
        NOW() - INTERVAL '6 hours',
        NOW()
    ),
    (
        gen_random_uuid(),
        test_campaign_id,
        test_user_id,
        'Attention ! Cette publicité semble suspecte. Méfiez-vous des offres trop belles pour être vraies.',
        false,
        true,
        'Contenu signalé comme potentiellement trompeur',
        NOW() - INTERVAL '2 hours',
        NOW()
    ),
    (
        gen_random_uuid(),
        test_campaign_id,
        test_user_id,
        'Commentaire en attente de modération pour tester le système.',
        false,
        false,
        NULL,
        NOW() - INTERVAL '30 minutes',
        NOW()
    );
    
    RAISE NOTICE '✅ 4 commentaires de publicité créés';
    
    -- Créer des commentaires marketplace
    INSERT INTO marketcomen (
        id, product_id, user_id, comment, is_approved, is_verified_purchase,
        created_at, updated_at
    ) VALUES 
    (
        gen_random_uuid(),
        'PROD_TEST_001',
        test_user_id,
        'Produit excellent ! Conforme à la description, livraison rapide. Je recommande ce vendeur.',
        true,
        true,
        NOW() - INTERVAL '3 days',
        NOW()
    ),
    (
        gen_random_uuid(),
        'PROD_TEST_002',
        test_user_id,
        'Produit correct mais sans plus. La qualité pourrait être améliorée pour ce prix.',
        true,
        false,
        NOW() - INTERVAL '1 day',
        NOW()
    ),
    (
        gen_random_uuid(),
        'PROD_TEST_003',
        test_user_id,
        'Très déçu ! Produit défectueux dès réception. Le vendeur ne répond pas aux messages.',
        true,
        true,
        NOW() - INTERVAL '8 hours',
        NOW()
    ),
    (
        gen_random_uuid(),
        'PROD_TEST_004',
        test_user_id,
        'Avis en attente de validation pour tester le système de modération.',
        false,
        false,
        NOW() - INTERVAL '1 hour',
        NOW()
    );
    
    RAISE NOTICE '✅ 4 commentaires marketplace créés';
    
END $$;

-- =====================================================
-- 2. VÉRIFIER LE CONTENU CRÉÉ
-- =====================================================

DO $$
DECLARE
    posts_count integer;
    ad_comments_count integer;
    market_comments_count integer;
    total_content integer;
BEGIN
    SELECT COUNT(*) INTO posts_count FROM posts;
    SELECT COUNT(*) INTO ad_comments_count FROM ad_comments;
    SELECT COUNT(*) INTO market_comments_count FROM marketcomen;
    total_content := posts_count + ad_comments_count + market_comments_count;
    
    RAISE NOTICE '';
    RAISE NOTICE '📊 CONTENU DISPONIBLE MAINTENANT:';
    RAISE NOTICE '================================';
    RAISE NOTICE '📝 Posts: %', posts_count;
    RAISE NOTICE '💬 Commentaires publicités: %', ad_comments_count;
    RAISE NOTICE '⭐ Avis marketplace: %', market_comments_count;
    RAISE NOTICE '🎯 TOTAL: % éléments de contenu', total_content;
    RAISE NOTICE '';
    
    IF total_content > 0 THEN
        RAISE NOTICE '✅ SUCCÈS ! Du contenu est maintenant disponible.';
        RAISE NOTICE '';
        RAISE NOTICE '🎯 PROCHAINES ÉTAPES:';
        RAISE NOTICE '1. Allez dans "Modération Contenu" dans l''admin';
        RAISE NOTICE '2. Actualisez la page (F5)';
        RAISE NOTICE '3. Vous devriez voir % éléments', total_content;
        RAISE NOTICE '4. Testez les filtres et actions';
        RAISE NOTICE '';
        RAISE NOTICE '🔍 CONTENU DE TEST INCLUT:';
        RAISE NOTICE '• Posts avec différents types (coup de cœur/gueule)';
        RAISE NOTICE '• Commentaires de publicité (approuvés/en attente/signalés)';
        RAISE NOTICE '• Avis marketplace (approuvés/en attente)';
        RAISE NOTICE '• Différents statuts pour tester la modération';
    ELSE
        RAISE NOTICE '❌ PROBLÈME: Aucun contenu créé !';
        RAISE NOTICE 'Vérifiez les erreurs ci-dessus.';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 SCRIPT TERMINÉ AVEC SUCCÈS !';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- 3. AFFICHER UN ÉCHANTILLON DU CONTENU
-- =====================================================

-- Afficher quelques posts créés
SELECT 
    'POSTS CRÉÉS' as type,
    LEFT(description, 50) || '...' as apercu,
    business_name,
    type as post_type,
    created_at
FROM posts 
WHERE description LIKE '%test%' OR business_name LIKE '%Test%'
ORDER BY created_at DESC 
LIMIT 3;

-- Afficher quelques commentaires de pub
SELECT 
    'COMMENTAIRES PUB' as type,
    LEFT(content, 50) || '...' as apercu,
    is_approved,
    is_flagged,
    created_at
FROM ad_comments 
ORDER BY created_at DESC 
LIMIT 3;

-- Afficher quelques avis marketplace
SELECT 
    'AVIS MARKETPLACE' as type,
    LEFT(comment, 50) || '...' as apercu,
    product_id,
    is_approved,
    created_at
FROM marketcomen 
ORDER BY created_at DESC 
LIMIT 3;
