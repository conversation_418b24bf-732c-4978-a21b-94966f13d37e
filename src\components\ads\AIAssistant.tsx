import React, { useState, useEffect } from 'react';
import { 
  Brain, Zap, TrendingUp, Target, DollarSign, Users, 
  AlertTriangle, CheckCircle, Lightbulb, ArrowRight,
  X, MessageCircle, Send, Bot, User
} from 'lucide-react';
import { formatAmount, formatPercentage } from '../../utils/formatUtils';

interface AIAssistantProps {
  isOpen: boolean;
  onClose: () => void;
  campaigns: any[];
  analytics: any;
  onOptimize: (campaignId: string) => void;
}

interface Recommendation {
  id: string;
  type: 'optimization' | 'budget' | 'creative' | 'targeting' | 'timing';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  impact: string;
  action: string;
  campaignId?: string;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

const AIAssistant: React.FC<AIAssistantProps> = ({
  isOpen,
  onClose,
  campaigns,
  analytics,
  onOptimize
}) => {
  const [activeTab, setActiveTab] = useState<'recommendations' | 'chat' | 'predictions'>('recommendations');
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  useEffect(() => {
    if (isOpen) {
      generateRecommendations();
      initializeChat();
    }
  }, [isOpen, campaigns, analytics]);

  const generateRecommendations = () => {
    const newRecommendations: Recommendation[] = [
      {
        id: '1',
        type: 'optimization',
        priority: 'high',
        title: 'Optimiser le ciblage démographique',
        description: 'Votre audience 25-34 ans montre un CTR 40% plus élevé que les autres segments. Concentrez 60% de votre budget sur ce groupe.',
        impact: '+25% CTR, +15% conversions',
        action: 'Ajuster le ciblage',
        campaignId: campaigns[0]?.id
      },
      {
        id: '2',
        type: 'budget',
        priority: 'high',
        title: 'Réallocation budgétaire intelligente',
        description: 'La campagne "Promotion Été" performe 3x mieux le weekend. Augmentez le budget de 40% pour Samedi-Dimanche.',
        impact: '+30% ROAS',
        action: 'Optimiser la planification',
        campaignId: campaigns[0]?.id
      },
      {
        id: '3',
        type: 'creative',
        priority: 'medium',
        title: 'Test A/B des créations',
        description: 'Les visuels avec des personnes génèrent 35% plus d\'engagement. Testez 3 nouvelles variantes avec des modèles locaux.',
        impact: '+20% engagement',
        action: 'Créer des variantes'
      },
      {
        id: '4',
        type: 'timing',
        priority: 'medium',
        title: 'Optimisation horaire',
        description: 'Vos conversions sont 50% plus élevées entre 18h-21h. Concentrez 70% de votre budget quotidien sur cette période.',
        impact: '+18% conversions',
        action: 'Ajuster la planification'
      },
      {
        id: '5',
        type: 'targeting',
        priority: 'low',
        title: 'Expansion géographique',
        description: 'Kaolack et Ziguinchor montrent un potentiel élevé avec un CPC 25% plus bas. Testez ces nouvelles zones.',
        impact: '+12% portée, -25% CPC',
        action: 'Étendre le ciblage'
      }
    ];

    setRecommendations(newRecommendations);
  };

  const initializeChat = () => {
    const welcomeMessage: ChatMessage = {
      id: '1',
      type: 'ai',
      content: 'Bonjour ! Je suis votre assistant IA pour les publicités. Je peux vous aider à optimiser vos campagnes, analyser les performances et répondre à vos questions. Comment puis-je vous aider aujourd\'hui ?',
      timestamp: new Date()
    };
    setChatMessages([welcomeMessage]);
  };

  const handleSendMessage = async () => {
    if (!chatInput.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: chatInput,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsTyping(true);

    // Simuler une réponse de l'IA
    setTimeout(() => {
      const aiResponse = generateAIResponse(chatInput);
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, aiMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const generateAIResponse = (input: string): string => {
    const lowerInput = input.toLowerCase();
    
    if (lowerInput.includes('budget') || lowerInput.includes('coût')) {
      return `Votre budget total est de ${formatAmount(350000)} avec ${formatAmount(110000)} déjà dépensés. Je recommande d'augmenter le budget de la campagne "Promotion Été" de 20% car elle montre un ROAS de 4.2. Voulez-vous que je l'optimise automatiquement ?`;
    }
    
    if (lowerInput.includes('performance') || lowerInput.includes('résultat')) {
      return `Vos campagnes performent bien ! CTR moyen de 6.8%, taux de conversion de 3.8%. La campagne "Lancement Crème" est votre meilleure avec un ROAS de 4.5. Je suggère de dupliquer sa stratégie sur les autres campagnes.`;
    }
    
    if (lowerInput.includes('audience') || lowerInput.includes('ciblage')) {
      return `Votre audience principale (femmes 25-34 ans) représente 68.5% de vos conversions. Je recommande d'étendre vers les 35-44 ans qui montrent un potentiel similaire avec un CPC 15% plus bas.`;
    }
    
    if (lowerInput.includes('optimis') || lowerInput.includes('améliorer')) {
      return `Je peux optimiser vos campagnes en temps réel ! Mes suggestions actuelles : 1) Réallouer 30% du budget vers le weekend, 2) Tester des créations avec des modèles locaux, 3) Étendre le ciblage vers Kaolack. Voulez-vous que j'applique ces optimisations ?`;
    }
    
    return `Excellente question ! Basé sur vos données, je vois plusieurs opportunités d'amélioration. Vos campagnes ont un ROAS moyen de ${analytics.roas}x, ce qui est très bon. Je peux vous aider avec l'optimisation du budget, le ciblage, ou l'analyse des performances. Que souhaitez-vous explorer en priorité ?`;
  };

  const applyRecommendation = (recommendation: Recommendation) => {
    if (recommendation.campaignId && recommendation.type === 'budget') {
      onOptimize(recommendation.campaignId);
    }
    
    // Marquer la recommandation comme appliquée
    setRecommendations(prev => 
      prev.map(rec => 
        rec.id === recommendation.id 
          ? { ...rec, applied: true } 
          : rec
      )
    );
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#6b7280';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'optimization': return TrendingUp;
      case 'budget': return DollarSign;
      case 'creative': return Lightbulb;
      case 'targeting': return Target;
      case 'timing': return Users;
      default: return Zap;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="ai-assistant-modal">
      <div className="ai-assistant-content">
        <div className="ai-assistant-header">
          <div className="ai-title">
            <Brain className="ai-icon" />
            <div>
              <h2>Assistant IA Publicitaire</h2>
              <p>Optimisation intelligente de vos campagnes</p>
            </div>
          </div>
          <button className="close-btn" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="ai-assistant-tabs">
          <button 
            className={`ai-tab ${activeTab === 'recommendations' ? 'active' : ''}`}
            onClick={() => setActiveTab('recommendations')}
          >
            <Lightbulb size={16} />
            Recommandations
          </button>
          <button 
            className={`ai-tab ${activeTab === 'chat' ? 'active' : ''}`}
            onClick={() => setActiveTab('chat')}
          >
            <MessageCircle size={16} />
            Chat IA
          </button>
          <button 
            className={`ai-tab ${activeTab === 'predictions' ? 'active' : ''}`}
            onClick={() => setActiveTab('predictions')}
          >
            <TrendingUp size={16} />
            Prédictions
          </button>
        </div>

        <div className="ai-assistant-body">
          {activeTab === 'recommendations' && (
            <div className="recommendations-tab">
              <div className="recommendations-header">
                <h3>Recommandations personnalisées</h3>
                <p>Basées sur l'analyse de vos données en temps réel</p>
              </div>
              
              <div className="recommendations-list">
                {recommendations.map(rec => {
                  const IconComponent = getTypeIcon(rec.type);
                  return (
                    <div key={rec.id} className="recommendation-card">
                      <div className="rec-header">
                        <div className="rec-icon">
                          <IconComponent size={20} />
                        </div>
                        <div className="rec-priority" style={{ backgroundColor: getPriorityColor(rec.priority) }}>
                          {rec.priority === 'high' ? 'Haute' : rec.priority === 'medium' ? 'Moyenne' : 'Basse'}
                        </div>
                      </div>
                      
                      <div className="rec-content">
                        <h4>{rec.title}</h4>
                        <p>{rec.description}</p>
                        <div className="rec-impact">
                          <strong>Impact estimé :</strong> {rec.impact}
                        </div>
                      </div>
                      
                      <div className="rec-actions">
                        <button 
                          className="apply-btn"
                          onClick={() => applyRecommendation(rec)}
                        >
                          {rec.action}
                          <ArrowRight size={16} />
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === 'chat' && (
            <div className="chat-tab">
              <div className="chat-messages">
                {chatMessages.map(message => (
                  <div key={message.id} className={`chat-message ${message.type}`}>
                    <div className="message-avatar">
                      {message.type === 'ai' ? <Bot size={20} /> : <User size={20} />}
                    </div>
                    <div className="message-content">
                      <p>{message.content}</p>
                      <span className="message-time">
                        {message.timestamp.toLocaleTimeString('fr-FR', { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </span>
                    </div>
                  </div>
                ))}
                
                {isTyping && (
                  <div className="chat-message ai typing">
                    <div className="message-avatar">
                      <Bot size={20} />
                    </div>
                    <div className="message-content">
                      <div className="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="chat-input-container">
                <input
                  type="text"
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="Posez votre question sur vos publicités..."
                  className="chat-input"
                />
                <button 
                  className="send-btn"
                  onClick={handleSendMessage}
                  disabled={!chatInput.trim()}
                >
                  <Send size={16} />
                </button>
              </div>
            </div>
          )}

          {activeTab === 'predictions' && (
            <div className="predictions-tab">
              <div className="predictions-header">
                <h3>Prédictions de performance</h3>
                <p>Projections basées sur l'IA pour les 7 prochains jours</p>
              </div>
              
              <div className="predictions-grid">
                <div className="prediction-card">
                  <div className="prediction-metric">
                    <h4>Impressions prévues</h4>
                    <div className="prediction-value">125 000</div>
                    <div className="prediction-confidence">Confiance: 85%</div>
                  </div>
                </div>
                
                <div className="prediction-card">
                  <div className="prediction-metric">
                    <h4>Clics estimés</h4>
                    <div className="prediction-value">8 500</div>
                    <div className="prediction-confidence">Confiance: 82%</div>
                  </div>
                </div>
                
                <div className="prediction-card">
                  <div className="prediction-metric">
                    <h4>Conversions attendues</h4>
                    <div className="prediction-value">320</div>
                    <div className="prediction-confidence">Confiance: 78%</div>
                  </div>
                </div>
                
                <div className="prediction-card">
                  <div className="prediction-metric">
                    <h4>Dépense prévue</h4>
                    <div className="prediction-value">{formatAmount(52000)}</div>
                    <div className="prediction-confidence">Confiance: 88%</div>
                  </div>
                </div>
              </div>
              
              <div className="prediction-insights">
                <h4>Insights prédictifs</h4>
                <ul>
                  <li>📈 Performance optimale attendue Samedi (+40% conversions)</li>
                  <li>⚠️ Baisse probable Mardi (-15% CTR) - Ajustez le budget</li>
                  <li>🎯 Audience 25-34 ans continuera de surperformer</li>
                  <li>💡 Opportunité d'expansion vers Kaolack détectée</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIAssistant;
