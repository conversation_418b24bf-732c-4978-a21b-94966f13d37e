import React, { useState, useEffect } from 'react';
import { Brain, AlertTriangle, CheckCircle, Loader } from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';

interface AIAdvisorDebugProps {
  businessId: string;
}

const AIAdvisorDebug: React.FC<AIAdvisorDebugProps> = ({ businessId }) => {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [logs, setLogs] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  useEffect(() => {
    testAIAdvisorLoading();
  }, [businessId]);

  const testAIAdvisorLoading = async () => {
    setStatus('loading');
    setLogs([]);
    setError(null);
    
    addLog('🚀 Début du test de chargement IA Conseiller');
    addLog(`📋 Business ID: ${businessId}`);

    try {
      // Test 1: Import des services
      addLog('📦 Test d\'import des services...');
      const { RealBusinessDataService } = await import('../../services/realBusinessDataService');
      const { RealAIAnalysisService } = await import('../../services/realAIAnalysisService');
      addLog('✅ Services importés avec succès');

      // Test 2: Récupération des données
      addLog('🔍 Test de récupération des données...');
      const businessData = await RealBusinessDataService.getCompleteBusinessData(businessId);
      addLog(`✅ Données récupérées: ${businessData.metadata.totalDataPoints} points`);

      // Test 3: Analyse de la qualité des données
      addLog('📊 Test d\'analyse de la qualité...');
      const dataQuality = await RealBusinessDataService.hasEnoughDataForAnalysis(businessId);
      addLog(`✅ Qualité: ${dataQuality.dataQuality} (${dataQuality.dataPoints.total} points)`);

      // Test 4: Génération des recommandations
      addLog('🤖 Test de génération des recommandations...');
      const recommendations = await RealAIAnalysisService.generateStrategicRecommendations(businessId);
      addLog(`✅ Recommandations générées: ${recommendations.recommendations.length}`);

      // Test 5: Suggestions de produits
      addLog('💡 Test de suggestions de produits...');
      const productSuggestions = await RealAIAnalysisService.generateProductSuggestions(businessId);
      addLog(`✅ Suggestions générées: ${productSuggestions.suggestions.length}`);

      addLog('🎉 Tous les tests réussis !');
      setStatus('success');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      addLog(`❌ Erreur: ${errorMessage}`);
      setError(errorMessage);
      setStatus('error');
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <Brain className="text-blue-600" size={24} />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Debug IA Conseiller
            </h3>
            <p className="text-sm text-gray-600">
              Test de chargement des composants IA
            </p>
          </div>
          <div className="ml-auto">
            {status === 'loading' && <Loader className="animate-spin text-blue-600" size={20} />}
            {status === 'success' && <CheckCircle className="text-green-600" size={20} />}
            {status === 'error' && <AlertTriangle className="text-red-600" size={20} />}
          </div>
        </div>
      </CardHeader>
      
      <CardBody>
        <div className="space-y-4">
          {/* Status */}
          <div className={`p-3 rounded-lg ${
            status === 'loading' ? 'bg-blue-50 border border-blue-200' :
            status === 'success' ? 'bg-green-50 border border-green-200' :
            'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-center space-x-2">
              {status === 'loading' && <Loader className="animate-spin text-blue-600" size={16} />}
              {status === 'success' && <CheckCircle className="text-green-600" size={16} />}
              {status === 'error' && <AlertTriangle className="text-red-600" size={16} />}
              <span className={`font-medium ${
                status === 'loading' ? 'text-blue-800' :
                status === 'success' ? 'text-green-800' :
                'text-red-800'
              }`}>
                {status === 'loading' ? 'Test en cours...' :
                 status === 'success' ? 'Tous les tests réussis !' :
                 'Erreur détectée'}
              </span>
            </div>
            {error && (
              <p className="text-red-700 text-sm mt-2">
                {error}
              </p>
            )}
          </div>

          {/* Logs */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Logs de test :</h4>
            <div className="bg-gray-50 rounded-lg p-3 max-h-64 overflow-y-auto">
              {logs.length === 0 ? (
                <p className="text-gray-500 text-sm">Aucun log disponible</p>
              ) : (
                <div className="space-y-1">
                  {logs.map((log, index) => (
                    <div key={index} className="text-sm font-mono text-gray-700">
                      {log}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-2">
            <Button 
              onClick={testAIAdvisorLoading} 
              variant="outline" 
              size="sm"
              disabled={status === 'loading'}
            >
              Relancer le test
            </Button>
            {status === 'success' && (
              <Button 
                onClick={() => window.location.reload()} 
                variant="primary" 
                size="sm"
              >
                Recharger la page
              </Button>
            )}
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default AIAdvisorDebug;
