-- Script pour créer des données de test pour les publicités
-- Exécutez ce script dans Supabase pour tester les interactions

-- =====================================================
-- 1. CRÉER DES CAMPAGNES DE TEST
-- =====================================================

-- Insérer des campagnes de test avec des UUIDs valides
INSERT INTO ad_campaigns (
  id,
  business_id,
  title,
  description,
  image_url,
  target_url,
  bid_amount,
  daily_budget,
  total_budget,
  placements,
  status,
  start_date
) VALUES
-- Campagne 1
(
  '00000000-0000-0000-0000-000000000001'::uuid,
  (SELECT id FROM profiles WHERE role = 'business' LIMIT 1),
  'Promotion Été - Huile de Beauté',
  'Profitez de notre offre spéciale été avec 20% de réduction sur notre huile de beauté <PERSON>.',
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
  'https://example.com/promo-ete',
  250.00,
  5000.00,
  50000.00,
  '["newsfeed", "sidebar"]'::jsonb,
  'active',
  now()
),
-- Campagne 2
(
  '00000000-0000-0000-0000-000000000002'::uuid,
  (SELECT id FROM profiles WHERE role = 'business' LIMIT 1),
  'Lancement Crème Hydratante',
  'Découvrez notre nouvelle crème hydratante visage pour une peau éclatante et revitalisée.',
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
  'https://example.com/creme-hydratante',
  200.00,
  3000.00,
  30000.00,
  '["newsfeed"]'::jsonb,
  'active',
  now()
),
-- Campagne 3
(
  '00000000-0000-0000-0000-000000000003'::uuid,
  (SELECT id FROM profiles WHERE role = 'business' LIMIT 1),
  'Promo Flash Sérum Anti-âge',
  'Offre flash de 48h : -30% sur notre sérum anti-âge révolutionnaire.',
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
  'https://example.com/serum-anti-age',
  300.00,
  7000.00,
  70000.00,
  '["newsfeed", "sidebar", "marketplace"]'::jsonb,
  'active',
  now()
)
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  description = EXCLUDED.description,
  updated_at = now();

-- =====================================================
-- 2. CRÉER DES CRÉATIFS POUR LES CAMPAGNES
-- =====================================================

INSERT INTO ad_creatives (
  campaign_id,
  title,
  description,
  image_url,
  call_to_action,
  status
) VALUES
(
  '00000000-0000-0000-0000-000000000001'::uuid,
  'Promotion Été - Huile de Beauté',
  'Profitez de notre offre spéciale été avec 20% de réduction sur notre huile de beauté Olgane.',
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
  'En savoir plus',
  'active'
),
(
  '00000000-0000-0000-0000-000000000002'::uuid,
  'Lancement Crème Hydratante',
  'Découvrez notre nouvelle crème hydratante visage pour une peau éclatante et revitalisée.',
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
  'Découvrir',
  'active'
),
(
  '00000000-0000-0000-0000-000000000003'::uuid,
  'Promo Flash Sérum Anti-âge',
  'Offre flash de 48h : -30% sur notre sérum anti-âge révolutionnaire.',
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
  'Profiter de l\'offre',
  'active'
)
ON CONFLICT DO NOTHING;

-- =====================================================
-- 3. INITIALISER LES MÉTRIQUES D'ENGAGEMENT
-- =====================================================

INSERT INTO ad_engagement_metrics (
  campaign_id,
  total_likes,
  total_comments,
  total_shares,
  engagement_rate,
  last_updated
) VALUES
(
  '00000000-0000-0000-0000-000000000001'::uuid,
  0,
  0,
  0,
  0,
  now()
),
(
  '00000000-0000-0000-0000-000000000002'::uuid,
  0,
  0,
  0,
  0,
  now()
),
(
  '00000000-0000-0000-0000-000000000003'::uuid,
  0,
  0,
  0,
  0,
  now()
)
ON CONFLICT (campaign_id) DO UPDATE SET
  last_updated = now();

-- =====================================================
-- 4. AJOUTER QUELQUES INTERACTIONS DE TEST
-- =====================================================

-- Ajouter des likes de test (si des utilisateurs existent)
DO $$
DECLARE
  test_user_id uuid;
  test_user_id_2 uuid;
BEGIN
  -- Récupérer des utilisateurs de test
  SELECT id INTO test_user_id FROM profiles WHERE role = 'standard' LIMIT 1;
  SELECT id INTO test_user_id_2 FROM profiles WHERE role = 'standard' OFFSET 1 LIMIT 1;
  
  IF test_user_id IS NOT NULL THEN
    -- Likes de test
    INSERT INTO ad_likes (campaign_id, user_id) VALUES
    ('00000000-0000-0000-0000-000000000001'::uuid, test_user_id),
    ('00000000-0000-0000-0000-000000000002'::uuid, test_user_id)
    ON CONFLICT DO NOTHING;

    -- Commentaires de test
    INSERT INTO ad_comments (campaign_id, user_id, content) VALUES
    ('00000000-0000-0000-0000-000000000001'::uuid, test_user_id, 'Super produit ! Je recommande vivement cette huile de beauté.'),
    ('00000000-0000-0000-0000-000000000002'::uuid, test_user_id, 'J''ai hâte d''essayer cette nouvelle crème hydratante.')
    ON CONFLICT DO NOTHING;

    -- Partages de test
    INSERT INTO ad_shares (campaign_id, user_id, share_type) VALUES
    ('00000000-0000-0000-0000-000000000001'::uuid, test_user_id, 'facebook'),
    ('00000000-0000-0000-0000-000000000003'::uuid, test_user_id, 'whatsapp')
    ON CONFLICT DO NOTHING;
  END IF;
  
  IF test_user_id_2 IS NOT NULL THEN
    -- Likes d'un deuxième utilisateur
    INSERT INTO ad_likes (campaign_id, user_id) VALUES
    ('00000000-0000-0000-0000-000000000001'::uuid, test_user_id_2),
    ('00000000-0000-0000-0000-000000000003'::uuid, test_user_id_2)
    ON CONFLICT DO NOTHING;

    -- Commentaires d'un deuxième utilisateur
    INSERT INTO ad_comments (campaign_id, user_id, content) VALUES
    ('00000000-0000-0000-0000-000000000001'::uuid, test_user_id_2, 'Excellent rapport qualité-prix !'),
    ('00000000-0000-0000-0000-000000000003'::uuid, test_user_id_2, 'Promo intéressante, je vais en profiter.')
    ON CONFLICT DO NOTHING;
  END IF;
END $$;

-- =====================================================
-- 5. METTRE À JOUR LES MÉTRIQUES
-- =====================================================

-- Forcer la mise à jour des métriques pour toutes les campagnes
SELECT update_engagement_metrics('00000000-0000-0000-0000-000000000001'::uuid);
SELECT update_engagement_metrics('00000000-0000-0000-0000-000000000002'::uuid);
SELECT update_engagement_metrics('00000000-0000-0000-0000-000000000003'::uuid);

-- =====================================================
-- 6. VÉRIFICATION DES DONNÉES CRÉÉES
-- =====================================================

-- Afficher les campagnes créées
SELECT 
  id,
  title,
  status,
  impressions,
  clicks,
  created_at
FROM ad_campaigns 
WHERE id IN ('00000000-0000-0000-0000-000000000001'::uuid, '00000000-0000-0000-0000-000000000002'::uuid, '00000000-0000-0000-0000-000000000003'::uuid);

-- Afficher les métriques d'engagement
SELECT 
  campaign_id,
  total_likes,
  total_comments,
  total_shares,
  engagement_rate
FROM ad_engagement_metrics 
WHERE campaign_id IN ('00000000-0000-0000-0000-000000000001'::uuid, '00000000-0000-0000-0000-000000000002'::uuid, '00000000-0000-0000-0000-000000000003'::uuid);

-- Afficher les interactions créées
SELECT 
  'likes' as type,
  campaign_id,
  COUNT(*) as count
FROM ad_likes 
WHERE campaign_id IN ('00000000-0000-0000-0000-000000000001'::uuid, '00000000-0000-0000-0000-000000000002'::uuid, '00000000-0000-0000-0000-000000000003'::uuid)
GROUP BY campaign_id

UNION ALL

SELECT
  'comments' as type,
  campaign_id,
  COUNT(*) as count
FROM ad_comments
WHERE campaign_id IN ('00000000-0000-0000-0000-000000000001'::uuid, '00000000-0000-0000-0000-000000000002'::uuid, '00000000-0000-0000-0000-000000000003'::uuid)
GROUP BY campaign_id

UNION ALL

SELECT
  'shares' as type,
  campaign_id,
  COUNT(*) as count
FROM ad_shares
WHERE campaign_id IN ('00000000-0000-0000-0000-000000000001'::uuid, '00000000-0000-0000-0000-000000000002'::uuid, '00000000-0000-0000-0000-000000000003'::uuid)
GROUP BY campaign_id

ORDER BY campaign_id, type;

-- =====================================================
-- 7. INSTRUCTIONS POUR TESTER
-- =====================================================

/*
INSTRUCTIONS POUR TESTER LES INTERACTIONS :

1. Exécutez ce script dans Supabase Dashboard > SQL Editor

2. Modifiez votre code pour utiliser les IDs de test :
   - Remplacez 'MOCK-001' par 'AD-001'
   - Remplacez 'MOCK-002' par 'AD-002'
   - Remplacez 'MOCK-003' par 'AD-003'

3. Ou créez un composant de test avec ces IDs :

const testAds: FeedAd[] = [
  {
    id: '00000000-0000-0000-0000-000000000001',
    title: 'Promotion Été - Huile de Beauté',
    description: 'Profitez de notre offre spéciale été avec 20% de réduction...',
    imageUrl: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
    targetUrl: 'https://example.com/promo-ete',
    businessName: 'Olgane Cosmetics',
    businessLogo: '/images/default-business.jpg',
    discount: 20
  },
  // ... autres publicités avec les IDs UUID corrects
];

4. Les boutons d'interaction devraient maintenant fonctionner !

5. Vérifiez la console du navigateur pour les logs de debug

6. Utilisez le composant AdInteractionDebug pour diagnostiquer les problèmes
*/
