# Installation des dépendances Chart.js

Pour activer les graphiques interactifs, exécutez ces commandes :

```bash
npm install chart.js react-chartjs-2
```

ou avec yarn :

```bash
yarn add chart.js react-chartjs-2
```

## Dépendances installées :
- **chart.js** : Bibliothèque de graphiques JavaScript
- **react-chartjs-2** : Wrapper React pour Chart.js

## Fonctionnalités activées :
- 📈 Graphiques en ligne (évolution temporelle)
- 📊 Graphiques en barres (comparaisons)
- 🥧 Graphiques circulaires (répartitions)
- 🎯 Graphiques en anneau (pourcentages)
- ✨ Animations et interactions avancées
