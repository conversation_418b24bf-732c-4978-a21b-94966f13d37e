import { IMarketComment, IMarketRating, IProductStats } from '../types';

// Données simulées pour les commentaires du marketplace
export const MOCK_COMMENTS: IMarketComment[] = [
  {
    id: 'comment-1',
    productId: '1',
    userId: 'user-1',
    comment: 'Excellent smartphone ! L\'écran est magnifique et la batterie tient vraiment toute la journée. Je recommande vivement ce produit.',
    isVerifiedPurchase: true,
    isApproved: true,
    createdAt: new Date('2025-05-25T14:30:00Z'),
    updatedAt: new Date('2025-05-25T14:30:00Z'),
    username: '<PERSON>',
    profilePicture: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    userRating: 5,
    replies: [
      {
        id: 'reply-1',
        productId: '1',
        userId: 'user-3',
        comment: 'Je suis d\'accord, excellent rapport qualité-prix !',
        isVerifiedPurchase: false,
        isApproved: true,
        parentCommentId: 'comment-1',
        createdAt: new Date('2025-05-25T16:45:00Z'),
        updatedAt: new Date('2025-05-25T16:45:00Z'),
        username: 'Thomas L.',
        profilePicture: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
      }
    ]
  },
  {
    id: 'comment-2',
    productId: '1',
    userId: 'user-2',
    comment: 'Bon produit mais un peu cher. La qualité est au rendez-vous cependant. Livraison rapide et emballage soigné.',
    isVerifiedPurchase: false,
    isApproved: true,
    createdAt: new Date('2025-05-23T10:15:00Z'),
    updatedAt: new Date('2025-05-23T10:15:00Z'),
    username: 'Jean-Paul D.',
    profilePicture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    userRating: 4
  },
  {
    id: 'comment-3',
    productId: '2',
    userId: 'user-1',
    comment: 'Cette huile Olgane est fantastique ! Ma peau n\'a jamais été aussi douce. Produit 100% naturel comme promis.',
    isVerifiedPurchase: true,
    isApproved: true,
    createdAt: new Date('2025-05-22T09:20:00Z'),
    updatedAt: new Date('2025-05-22T09:20:00Z'),
    username: 'Marie K.',
    profilePicture: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    userRating: 5
  },
  {
    id: 'comment-4',
    productId: '2',
    userId: 'user-4',
    comment: 'Produit naturel de qualité, livraison rapide. Très content de cet achat local. Je recommande !',
    isVerifiedPurchase: true,
    isApproved: true,
    createdAt: new Date('2025-05-20T15:30:00Z'),
    updatedAt: new Date('2025-05-20T15:30:00Z'),
    username: 'Fatou S.',
    profilePicture: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    userRating: 5
  },
  {
    id: 'comment-5',
    productId: '3',
    userId: 'user-2',
    comment: 'Crème hydratante correcte, texture agréable mais l\'effet n\'est pas extraordinaire. Rapport qualité-prix moyen.',
    isVerifiedPurchase: false,
    isApproved: true,
    createdAt: new Date('2025-05-19T11:45:00Z'),
    updatedAt: new Date('2025-05-19T11:45:00Z'),
    username: 'Jean-Paul D.',
    profilePicture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    userRating: 3
  },
  {
    id: 'comment-6',
    productId: '5',
    userId: 'user-4',
    comment: 'Savon au karité excellent ! Parfait pour les peaux sensibles comme la mienne. Très doux et hydratant.',
    isVerifiedPurchase: true,
    isApproved: true,
    createdAt: new Date('2025-05-18T13:20:00Z'),
    updatedAt: new Date('2025-05-18T13:20:00Z'),
    username: 'Fatou S.',
    profilePicture: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    userRating: 5
  },
  {
    id: 'comment-7',
    productId: '6',
    userId: 'user-1',
    comment: 'Ordinateur portable très performant, parfait pour le travail. Livraison soignée et rapide. Très satisfaite !',
    isVerifiedPurchase: true,
    isApproved: true,
    createdAt: new Date('2025-05-17T16:10:00Z'),
    updatedAt: new Date('2025-05-17T16:10:00Z'),
    username: 'Marie K.',
    profilePicture: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    userRating: 5
  }
];

// Données simulées pour les notes du marketplace
export const MOCK_RATINGS: IMarketRating[] = [
  {
    id: 'rating-1',
    productId: '1',
    userId: 'user-1',
    rating: 5,
    isVerifiedPurchase: true,
    createdAt: new Date('2025-05-25T14:30:00Z'),
    updatedAt: new Date('2025-05-25T14:30:00Z')
  },
  {
    id: 'rating-2',
    productId: '1',
    userId: 'user-2',
    rating: 4,
    isVerifiedPurchase: false,
    createdAt: new Date('2025-05-23T10:15:00Z'),
    updatedAt: new Date('2025-05-23T10:15:00Z')
  },
  {
    id: 'rating-3',
    productId: '1',
    userId: 'user-5',
    rating: 5,
    isVerifiedPurchase: true,
    createdAt: new Date('2025-05-21T08:45:00Z'),
    updatedAt: new Date('2025-05-21T08:45:00Z')
  },
  {
    id: 'rating-4',
    productId: '2',
    userId: 'user-1',
    rating: 5,
    isVerifiedPurchase: true,
    createdAt: new Date('2025-05-22T09:20:00Z'),
    updatedAt: new Date('2025-05-22T09:20:00Z')
  },
  {
    id: 'rating-5',
    productId: '2',
    userId: 'user-4',
    rating: 5,
    isVerifiedPurchase: true,
    createdAt: new Date('2025-05-20T15:30:00Z'),
    updatedAt: new Date('2025-05-20T15:30:00Z')
  },
  {
    id: 'rating-6',
    productId: '3',
    userId: 'user-2',
    rating: 3,
    isVerifiedPurchase: false,
    createdAt: new Date('2025-05-19T11:45:00Z'),
    updatedAt: new Date('2025-05-19T11:45:00Z')
  },
  {
    id: 'rating-7',
    productId: '5',
    userId: 'user-4',
    rating: 5,
    isVerifiedPurchase: true,
    createdAt: new Date('2025-05-18T13:20:00Z'),
    updatedAt: new Date('2025-05-18T13:20:00Z')
  },
  {
    id: 'rating-8',
    productId: '6',
    userId: 'user-1',
    rating: 5,
    isVerifiedPurchase: true,
    createdAt: new Date('2025-05-17T16:10:00Z'),
    updatedAt: new Date('2025-05-17T16:10:00Z')
  }
];

// Fonction pour obtenir les commentaires d'un produit
export const getMockProductComments = (productId: string): IMarketComment[] => {
  return MOCK_COMMENTS.filter(comment => comment.productId === productId);
};

// Fonction pour obtenir les notes d'un produit
export const getMockProductRatings = (productId: string): IMarketRating[] => {
  return MOCK_RATINGS.filter(rating => rating.productId === productId);
};

// Fonction pour obtenir les statistiques d'un produit
export const getMockProductStats = (productId: string): IProductStats => {
  const ratings = getMockProductRatings(productId);
  const comments = getMockProductComments(productId);
  
  const totalRatings = ratings.length;
  const averageRating = totalRatings > 0 
    ? ratings.reduce((sum, rating) => sum + rating.rating, 0) / totalRatings 
    : 0;
  
  const verifiedRatings = ratings.filter(r => r.isVerifiedPurchase).length;
  const verifiedComments = comments.filter(c => c.isVerifiedPurchase).length;

  return {
    productId,
    totalRatings,
    averageRating: Math.round(averageRating * 10) / 10, // Arrondir à 1 décimale
    totalComments: comments.length,
    verifiedRatings,
    verifiedComments
  };
};

// Fonction pour obtenir la note d'un utilisateur pour un produit
export const getMockUserRating = (productId: string, userId: string): IMarketRating | null => {
  return MOCK_RATINGS.find(rating => rating.productId === productId && rating.userId === userId) || null;
};

// Fonction pour simuler l'ajout d'un commentaire
export const addMockComment = (commentData: {
  productId: string;
  userId: string;
  comment: string;
  parentCommentId?: string;
}): IMarketComment => {
  const newComment: IMarketComment = {
    id: `comment-${Date.now()}`,
    productId: commentData.productId,
    userId: commentData.userId,
    comment: commentData.comment,
    isVerifiedPurchase: Math.random() > 0.3, // 70% de chance d'être un achat vérifié
    isApproved: true,
    parentCommentId: commentData.parentCommentId,
    createdAt: new Date(),
    updatedAt: new Date(),
    username: 'Utilisateur',
    profilePicture: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face'
  };

  MOCK_COMMENTS.push(newComment);
  return newComment;
};

// Fonction pour simuler l'ajout/mise à jour d'une note
export const addOrUpdateMockRating = (ratingData: {
  productId: string;
  userId: string;
  rating: number;
}): IMarketRating => {
  const existingRatingIndex = MOCK_RATINGS.findIndex(
    r => r.productId === ratingData.productId && r.userId === ratingData.userId
  );

  const newRating: IMarketRating = {
    id: existingRatingIndex >= 0 ? MOCK_RATINGS[existingRatingIndex].id : `rating-${Date.now()}`,
    productId: ratingData.productId,
    userId: ratingData.userId,
    rating: ratingData.rating,
    isVerifiedPurchase: Math.random() > 0.3, // 70% de chance d'être un achat vérifié
    createdAt: existingRatingIndex >= 0 ? MOCK_RATINGS[existingRatingIndex].createdAt : new Date(),
    updatedAt: new Date()
  };

  if (existingRatingIndex >= 0) {
    MOCK_RATINGS[existingRatingIndex] = newRating;
  } else {
    MOCK_RATINGS.push(newRating);
  }

  return newRating;
};
