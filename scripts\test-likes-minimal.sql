-- Script de test minimal pour vérifier le système de likes
-- Version ultra-simplifiée qui teste uniquement les colonnes

-- =====================================================
-- 1. VÉRIFICATION DE LA STRUCTURE
-- =====================================================

DO $$
DECLARE
    likes_exists BOOLEAN;
    shares_exists BOOLEAN;
    total_posts INTEGER;
BEGIN
    RAISE NOTICE '🔍 VÉRIFICATION DE LA STRUCTURE';
    RAISE NOTICE '==============================';
    
    -- Vérifier que la colonne likes existe
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' 
        AND column_name = 'likes'
        AND table_schema = 'public'
    ) INTO likes_exists;
    
    -- Vérifier que la colonne shares existe
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' 
        AND column_name = 'shares'
        AND table_schema = 'public'
    ) INTO shares_exists;
    
    -- Compter les posts
    SELECT COUNT(*) INTO total_posts FROM posts;
    
    -- Afficher les résultats
    RAISE NOTICE 'Colonne "likes": %', CASE WHEN likes_exists THEN '✅ TROUVÉE' ELSE '❌ MANQUANTE' END;
    RAISE NOTICE 'Colonne "shares": %', CASE WHEN shares_exists THEN '✅ TROUVÉE' ELSE '❌ MANQUANTE' END;
    RAISE NOTICE 'Total posts: %', total_posts;
    
    -- Vérification globale
    IF likes_exists AND shares_exists THEN
        RAISE NOTICE '';
        RAISE NOTICE '✅ STRUCTURE CORRECTE';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '❌ STRUCTURE INCOMPLÈTE';
        RAISE NOTICE 'Veuillez exécuter le script add-likes-minimal.sql';
        RETURN;
    END IF;
END $$;

-- =====================================================
-- 2. VÉRIFICATION DES INDEX
-- =====================================================

DO $$
DECLARE
    likes_index_exists BOOLEAN;
    shares_index_exists BOOLEAN;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🚀 VÉRIFICATION DES INDEX';
    RAISE NOTICE '=========================';
    
    -- Vérifier l'index des likes
    SELECT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'posts' 
        AND indexname = 'posts_likes_idx'
    ) INTO likes_index_exists;
    
    -- Vérifier l'index des shares
    SELECT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'posts' 
        AND indexname = 'posts_shares_idx'
    ) INTO shares_index_exists;
    
    RAISE NOTICE 'Index "posts_likes_idx": %', CASE WHEN likes_index_exists THEN '✅ TROUVÉ' ELSE '❌ MANQUANT' END;
    RAISE NOTICE 'Index "posts_shares_idx": %', CASE WHEN shares_index_exists THEN '✅ TROUVÉ' ELSE '❌ MANQUANT' END;
END $$;

-- =====================================================
-- 3. VÉRIFICATION DES DONNÉES
-- =====================================================

DO $$
DECLARE
    total_posts INTEGER;
    posts_with_likes INTEGER;
    posts_with_shares INTEGER;
    sample_post RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📊 VÉRIFICATION DES DONNÉES';
    RAISE NOTICE '===========================';
    
    -- Compter le total de posts
    SELECT COUNT(*) INTO total_posts FROM posts;
    
    -- Compter les posts avec likes initialisés
    SELECT COUNT(*) INTO posts_with_likes 
    FROM posts 
    WHERE likes IS NOT NULL;
    
    -- Compter les posts avec shares initialisés
    SELECT COUNT(*) INTO posts_with_shares 
    FROM posts 
    WHERE shares IS NOT NULL;
    
    RAISE NOTICE 'Total posts: %', total_posts;
    RAISE NOTICE 'Posts avec likes: %', posts_with_likes;
    RAISE NOTICE 'Posts avec shares: %', posts_with_shares;
    
    -- Prendre un échantillon pour vérifier
    SELECT id, likes, shares 
    INTO sample_post
    FROM posts 
    LIMIT 1;
    
    IF sample_post.id IS NOT NULL THEN
        RAISE NOTICE '';
        RAISE NOTICE '📋 ÉCHANTILLON DE POST:';
        RAISE NOTICE 'ID: %', sample_post.id;
        RAISE NOTICE 'Likes: %', sample_post.likes;
        RAISE NOTICE 'Shares: %', sample_post.shares;
    END IF;
END $$;

-- =====================================================
-- 4. TEST FONCTIONNEL SIMPLE
-- =====================================================

DO $$
DECLARE
    test_post_id UUID;
    test_user_id UUID := '12345678-1234-1234-1234-123456789012'; -- UUID fictif
    initial_likes UUID[];
    updated_likes UUID[];
    test_success BOOLEAN := false;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🧪 TEST FONCTIONNEL SIMPLE';
    RAISE NOTICE '===========================';
    
    -- Prendre le premier post pour le test
    SELECT id, likes INTO test_post_id, initial_likes 
    FROM posts 
    LIMIT 1;
    
    IF test_post_id IS NOT NULL THEN
        RAISE NOTICE 'Test sur le post: %', test_post_id;
        RAISE NOTICE 'Likes initiaux: %', COALESCE(array_length(initial_likes, 1), 0);
        
        BEGIN
            -- Simuler l'ajout d'un like
            UPDATE posts 
            SET likes = array_append(COALESCE(likes, ARRAY[]::UUID[]), test_user_id)
            WHERE id = test_post_id;
            
            -- Vérifier le résultat
            SELECT likes INTO updated_likes 
            FROM posts 
            WHERE id = test_post_id;
            
            RAISE NOTICE 'Likes après ajout: %', COALESCE(array_length(updated_likes, 1), 0);
            
            -- Vérifier que le like a été ajouté
            IF test_user_id = ANY(updated_likes) THEN
                test_success := true;
                RAISE NOTICE '✅ Test réussi - Le like a été ajouté';
            ELSE
                RAISE NOTICE '❌ Test échoué - Le like n''a pas été ajouté';
            END IF;
            
            -- Remettre l'état initial
            UPDATE posts 
            SET likes = initial_likes
            WHERE id = test_post_id;
            
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE '❌ Erreur lors du test: %', SQLERRM;
        END;
    ELSE
        RAISE NOTICE '⚠️ Aucun post disponible pour le test';
    END IF;
    
    IF test_success THEN
        RAISE NOTICE '✅ Le système de likes fonctionne correctement';
    END IF;
END $$;

-- =====================================================
-- 5. RÉSUMÉ FINAL
-- =====================================================

DO $$
DECLARE
    total_posts INTEGER;
    total_users INTEGER;
    likes_column_ok BOOLEAN;
    shares_column_ok BOOLEAN;
    system_ready BOOLEAN := false;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎯 RÉSUMÉ FINAL';
    RAISE NOTICE '===============';
    
    -- Vérifications finales
    SELECT COUNT(*) INTO total_posts FROM posts;
    SELECT COUNT(*) INTO total_users FROM profiles;
    
    -- Vérifier les colonnes
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'likes'
    ) INTO likes_column_ok;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'shares'
    ) INTO shares_column_ok;
    
    system_ready := likes_column_ok AND shares_column_ok AND total_posts > 0;
    
    RAISE NOTICE 'Posts disponibles: %', total_posts;
    RAISE NOTICE 'Utilisateurs disponibles: %', total_users;
    RAISE NOTICE 'Colonne likes: %', CASE WHEN likes_column_ok THEN 'OK' ELSE 'MANQUANTE' END;
    RAISE NOTICE 'Colonne shares: %', CASE WHEN shares_column_ok THEN 'OK' ELSE 'MANQUANTE' END;
    
    IF system_ready THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 SYSTÈME DE LIKES PRÊT !';
        RAISE NOTICE '===========================';
        RAISE NOTICE '✅ Structure de base de données correcte';
        RAISE NOTICE '✅ Posts disponibles pour les tests';
        RAISE NOTICE '✅ Le bouton "J''aime" devrait fonctionner';
        RAISE NOTICE '';
        RAISE NOTICE '📱 TESTEZ MAINTENANT:';
        RAISE NOTICE '1. Redémarrez votre application (npm run dev)';
        RAISE NOTICE '2. Connectez-vous avec un compte utilisateur';
        RAISE NOTICE '3. Cliquez sur "J''aime" sur un post';
        RAISE NOTICE '4. Vérifiez que le compteur change de 0 à 1';
        RAISE NOTICE '5. Re-cliquez pour vérifier que ça repasse à 0';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '❌ SYSTÈME NON PRÊT';
        RAISE NOTICE '===================';
        RAISE NOTICE 'Veuillez d''abord exécuter le script add-likes-minimal.sql';
    END IF;
END $$;
