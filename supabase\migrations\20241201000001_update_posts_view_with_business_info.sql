-- Mettre à jour la vue pour inclure les nouvelles colonnes author_role et business_name des commentaires

CREATE OR REPLACE VIEW posts_with_author_details AS
SELECT 
  p.*,
  u.username as author_username,
  u.profile_picture as author_profile_picture,
  u.status as author_status,
  u.role as author_role,
  COALESCE(
    (
      SELECT json_agg(
        json_build_object(
          'id', c.id,
          'userId', c.user_id,
          'username', cu.username,
          'profilePicture', cu.profile_picture,
          'content', c.content,
          'hasUsedProduct', c.has_used_product,
          'rating', c.rating,
          'createdAt', c.created_at,
          'authorRole', c.author_role,
          'businessName', c.business_name
        )
      )
      FROM comments c
      LEFT JOIN profiles cu ON c.user_id = cu.id
      WHERE c.post_id = p.id
      ORDER BY c.created_at ASC
    ),
    '[]'::json
  ) as comments
FROM posts p
LEFT JOIN profiles u ON p.user_id = u.id;

-- Grant access to the view
GRANT SELECT ON posts_with_author_details TO authenticated;
