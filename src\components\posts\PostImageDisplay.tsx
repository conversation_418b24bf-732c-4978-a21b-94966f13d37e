import React, { useState } from 'react';
import { AlertCircle } from 'lucide-react';
import { processImages, ImageInput } from '../../utils/imageUtils';

interface PostImageDisplayProps {
  images: ImageInput;
  title?: string;
  postId: string;
  className?: string;
}

const PostImageDisplay: React.FC<PostImageDisplayProps> = ({
  images,
  title,
  postId,
  className = ''
}) => {
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());

  // Utiliser la fonction robuste pour traiter les images
  const validImages = processImages(images);



  const handleImageLoad = (imageUrl: string) => {
    setLoadedImages(prev => new Set([...prev, imageUrl]));
  };

  const handleImageError = (imageUrl: string) => {
    setImageErrors(prev => new Set([...prev, imageUrl]));
  };

  // Si aucune image valide, ne rien afficher
  if (validImages.length === 0) {
    return null;
  }

  return (
    <div className={`mb-3 ${className}`}>
      {/* Affichage de la première image */}
      <div className="relative aspect-video bg-gray-200 rounded-lg overflow-hidden">
        <img
          src={validImages[0]}
          alt={title || 'Image du post'}
          className="w-full h-full object-cover"
          onLoad={() => handleImageLoad(validImages[0])}
          onError={() => handleImageError(validImages[0])}
        />

        {/* Overlay avec titre si présent */}
        {title && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
            <h2 className="text-2xl font-bold text-white text-center p-4">{title}</h2>
          </div>
        )}

        {/* Indicateur d'erreur de chargement */}
        {imageErrors.has(validImages[0]) && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center text-gray-500">
              <AlertCircle size={48} className="mx-auto mb-2" />
              <p className="text-sm">Image non disponible</p>
            </div>
          </div>
        )}

        {/* Indicateur de plusieurs images */}
        {validImages.length > 1 && (
          <div className="absolute top-2 right-2 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-sm">
            +{validImages.length - 1} image{validImages.length > 2 ? 's' : ''}
          </div>
        )}
      </div>


    </div>
  );
};

export default PostImageDisplay;
