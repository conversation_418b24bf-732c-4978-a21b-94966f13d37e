.ad-campaign-details-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
  overflow-y: auto;
  cursor: pointer; /* Indiquer qu'on peut cliquer pour fermer */
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.ad-campaign-details {
  background-color: white;
  border-radius: 16px;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  cursor: default; /* Curseur normal pour le contenu */
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: scale(0.9) translateY(-20px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 2px solid #e9ecef;
  position: sticky;
  top: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  z-index: 50;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.details-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: white;
  font-weight: 600;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  color: white;
  padding: 0.75rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  width: 44px;
  height: 44px;
  min-width: 44px;
  min-height: 44px;
  z-index: 100;
  position: relative;
  backdrop-filter: blur(10px);
  animation: pulseClose 2s infinite;
}

@keyframes pulseClose {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(255, 255, 255, 0);
  }
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  animation: none; /* Arrêter l'animation au hover */
}

.close-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.4);
}

.details-content {
  padding: 1.5rem;
}

.details-main {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.campaign-image {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.campaign-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: opacity 0.3s ease;
}

/* Placeholder et gestion d'erreur d'image pour les détails de campagne */
.campaign-image-placeholder,
.campaign-image-error {
  width: 100%;
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border: 2px dashed #d1d5db;
  color: #6b7280;
  text-align: center;
  border-radius: 8px;
}

.campaign-image-error {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.campaign-image-placeholder .placeholder-icon,
.campaign-image-error .error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.campaign-image-placeholder p,
.campaign-image-error p {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

.campaign-image-error p {
  color: #991b1b;
}

.campaign-status {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  color: white;
}

.status-active {
  background-color: #4CAF50;
}

.status-paused {
  background-color: #FFC107;
}

.status-completed {
  background-color: #9E9E9E;
}

.status-draft {
  background-color: #2196F3;
}

.campaign-info {
  display: flex;
  flex-direction: column;
}

.campaign-description {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-label {
  font-weight: 600;
  color: #666;
  margin-right: 0.5rem;
}

.info-value {
  color: #333;
}

.budget-progress {
  margin-top: 1rem;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.progress-bar {
  height: 10px;
  background-color: #f0f0f0;
  border-radius: 5px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #4CAF50;
  border-radius: 5px;
  transition: width 0.3s ease;
}

.details-targeting, .details-placement {
  margin-bottom: 2rem;
}

.details-targeting h3, .details-placement h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #444;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.targeting-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.targeting-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.targeting-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #555;
}

.targeting-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.targeting-tag {
  background-color: #f0f0f0;
  color: #555;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
}

.placement-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.placement-item {
  padding: 1rem;
  border-radius: 6px;
  text-align: center;
  background-color: #f0f0f0;
  color: #666;
  font-weight: 500;
  opacity: 0.6;
}

.placement-item.active {
  background-color: #e3f2fd;
  color: #2196F3;
  font-weight: 600;
  opacity: 1;
}

.details-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.action-btn.edit {
  background-color: #2196F3;
  color: white;
}

.action-btn.edit:hover {
  background-color: #1976D2;
}

.action-btn.delete {
  background-color: #F44336;
  color: white;
}

.action-btn.delete:hover {
  background-color: #D32F2F;
}

.action-btn.pause {
  background-color: #FFC107;
  color: #333;
}

.action-btn.pause:hover {
  background-color: #FFA000;
}

.action-btn.resume {
  background-color: #4CAF50;
  color: white;
}

.action-btn.resume:hover {
  background-color: #388E3C;
}

/* Responsive */
@media (max-width: 768px) {
  .details-main {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .placement-grid {
    grid-template-columns: 1fr;
  }

  .details-actions {
    flex-direction: column;
  }
}
