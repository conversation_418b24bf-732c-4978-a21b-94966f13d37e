/* Business Products Page - Interface Ultra-Avancée */
.business-products-page {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Header Section */
.products-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.header-content h1.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.action-btn.primary {
  background: #3b82f6;
  color: white;
}

.action-btn.primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.action-btn.secondary {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.action-btn.secondary:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

/* KPIs Dashboard */
.products-kpis {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.kpi-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.kpi-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.kpi-card.total::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.kpi-card.revenue::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.kpi-card.price::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.kpi-card.stock::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.kpi-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.kpi-card.total .kpi-icon {
  background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
  color: #7c3aed;
}

.kpi-card.revenue .kpi-icon {
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
  color: #059669;
}

.kpi-card.price .kpi-icon {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #d97706;
}

.kpi-card.stock .kpi-icon {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #dc2626;
}

.kpi-content {
  flex: 1;
}

.kpi-content h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.kpi-value {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.kpi-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}

.kpi-trend.positive {
  color: #059669;
}

.kpi-trend.negative {
  color: #dc2626;
}

.kpi-trend.neutral {
  color: #64748b;
}

.kpi-trend.warning {
  color: #d97706;
}

/* Insights Section */
.products-insights {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.insights-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.insights-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-item {
  display: flex;
  gap: 16px;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid;
  transition: all 0.2s ease;
}

.insight-item.success {
  background: #f0fdf4;
  border-left-color: #22c55e;
}

.insight-item.warning {
  background: #fefce8;
  border-left-color: #eab308;
}

.insight-item.info {
  background: #eff6ff;
  border-left-color: #3b82f6;
}

.insight-item.error {
  background: #fef2f2;
  border-left-color: #ef4444;
}

.insight-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.insight-item.success .insight-icon {
  background: #dcfce7;
  color: #16a34a;
}

.insight-item.warning .insight-icon {
  background: #fef3c7;
  color: #ca8a04;
}

.insight-item.info .insight-icon {
  background: #dbeafe;
  color: #2563eb;
}

.insight-item.error .insight-icon {
  background: #fee2e2;
  color: #dc2626;
}

.insight-content {
  flex: 1;
}

.insight-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.insight-content p {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.insight-action {
  font-size: 0.8rem;
  font-weight: 500;
  color: #3b82f6;
  margin: 0;
}

/* Toolbar Section */
.products-toolbar {
  background: white;
  border-radius: 16px;
  padding: 20px 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.toolbar-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-container svg {
  position: absolute;
  left: 12px;
  color: #64748b;
  z-index: 1;
}

.search-input {
  padding: 10px 12px 10px 40px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.875rem;
  width: 300px;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.filter-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.view-toggle {
  display: flex;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.view-btn {
  padding: 8px 12px;
  border: none;
  background: white;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn.active {
  background: #3b82f6;
  color: white;
}

.bulk-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.bulk-btn {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bulk-btn:hover {
  background: #f9fafb;
}

.bulk-btn.danger:hover {
  background: #fef2f2;
  color: #dc2626;
  border-color: #fecaca;
}

/* Responsive Design */
@media (max-width: 768px) {
  .business-products-page {
    padding: 16px;
  }

  .products-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
  }

  .products-kpis {
    grid-template-columns: 1fr;
  }

  .kpi-card {
    padding: 20px;
  }

  .kpi-value {
    font-size: 1.5rem;
  }

  .toolbar-top {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .toolbar-left {
    flex-direction: column;
    gap: 12px;
  }

  .search-input {
    width: 100%;
  }
}

/* Filters Panel */
.filters-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  margin-bottom: 20px;
  border: 1px solid #e5e7eb;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.price-range {
  display: flex;
  gap: 8px;
  align-items: center;
}

.price-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
}

/* Products Grid */
.products-container {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Product Card */
.product-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.product-card.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Product Checkbox */
.product-checkbox {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 10;
}

.product-select {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 2px solid #d1d5db;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.product-select:checked {
  background: #3b82f6;
  border-color: #3b82f6;
}

.product-select:checked::after {
  content: '✓';
  color: white;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.product-image {
  position: relative;
  width: 100%;
  height: 200px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-badges {
  position: absolute;
  top: 12px;
  left: 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.product-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-featured {
  background: #fef3c7;
  color: #d97706;
}

.badge-trending {
  background: #dcfce7;
  color: #16a34a;
}

.badge-sale {
  background: #fee2e2;
  color: #dc2626;
}

.badge-out-of-stock {
  background: #f3f4f6;
  color: #6b7280;
}

.product-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-actions {
  opacity: 1;
}

.action-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.action-icon:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  transform: scale(1.1);
}

.action-icon.favorited {
  background: #fef2f2;
  color: #ef4444;
  border-color: #fecaca;
}

.action-icon.favorited:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.action-icon.delete:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.product-content {
  padding: 20px;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.product-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.4;
  flex: 1;
}

.product-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: 12px;
}

.status-active {
  background: #dcfce7;
  color: #16a34a;
}

.status-inactive {
  background: #f3f4f6;
  color: #6b7280;
}

.status-draft {
  background: #fef3c7;
  color: #d97706;
}

.status-out-of-stock {
  background: #fee2e2;
  color: #dc2626;
}

.product-category {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 8px;
}

.product-description {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.current-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
}

.original-price {
  font-size: 1rem;
  color: #9ca3af;
  text-decoration: line-through;
}

.discount-badge {
  padding: 2px 6px;
  background: #fee2e2;
  color: #dc2626;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.star {
  width: 14px;
  height: 14px;
  color: #fbbf24;
}

.rating-text {
  font-size: 0.8rem;
  color: #64748b;
}

.product-stock {
  font-size: 0.8rem;
  font-weight: 500;
}

.stock-good {
  color: #16a34a;
}

.stock-low {
  color: #d97706;
}

.stock-out {
  color: #dc2626;
}

.product-footer {
  display: flex;
  gap: 8px;
}

.product-btn {
  flex: 1;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #f9fafb;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.btn-secondary:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

/* List View Styles */
.product-card.list-view {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
}

.product-card.list-view .product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-right: 16px;
  flex-shrink: 0;
}

.product-card.list-view .product-content {
  flex: 1;
  padding: 0;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 120px;
  gap: 16px;
  align-items: center;
}

.product-card.list-view .product-description {
  display: none;
}

.product-card.list-view .product-footer {
  justify-content: flex-end;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
}

.empty-state svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-state p {
  margin: 0 0 20px 0;
  font-size: 0.875rem;
}

.empty-state button {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.empty-state button:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
}

.page-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0 16px;
}

/* Export Menu Styles */
.export-dropdown {
  position: relative;
}

.export-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  z-index: 100;
  padding: 20px;
  margin-top: 8px;
  animation: slideDown 0.2s ease;
}

.export-section {
  margin-bottom: 20px;
}

.export-section:last-child {
  margin-bottom: 0;
}

.export-section h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.export-option {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.export-option:hover {
  background: #f9fafb;
  border-color: #3b82f6;
}

.export-option.catalog {
  border-left: 4px solid #ef4444;
}

.export-option.inventory {
  border-left: 4px solid #10b981;
}

.export-option.import {
  border-left: 4px solid #059669;
}

.export-option.import:hover {
  background: #f0fdf4;
  border-color: #047857;
}

.export-option.export {
  border-left: 4px solid #3b82f6;
}

.export-option.export:hover {
  background: #eff6ff;
  border-color: #2563eb;
}

.export-option div {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.export-option span {
  font-weight: 600;
  color: #1f2937;
}

.export-option small {
  color: #64748b;
  font-size: 0.75rem;
}

.export-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  color: #374151;
}

.export-checkbox input {
  margin: 0;
}

/* Filter Count Badge */
.filter-count {
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 6px;
}

/* Selected Count */
.selected-count {
  font-size: 0.875rem;
  color: #3b82f6;
  font-weight: 500;
  margin-right: 12px;
}

/* Results Summary */
.results-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #64748b;
}

.select-all-btn {
  padding: 6px 12px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-all-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Product Card Enhancements */
.product-card {
  position: relative;
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
  pointer-events: none;
}

.product-card:hover::before {
  opacity: 1;
}

/* Trending Animation */
.badge-trending {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Sale Badge Animation */
.badge-sale {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

/* Stock Status Colors */
.stock-good {
  background: #dcfce7;
  color: #16a34a;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
}

.stock-low {
  background: #fef3c7;
  color: #d97706;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
}

.stock-out {
  background: #fee2e2;
  color: #dc2626;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
}

/* Enhanced Mobile Responsive */
@media (max-width: 768px) {
  .business-products-page {
    padding: 16px;
  }

  .products-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .products-kpis {
    grid-template-columns: 1fr;
  }

  .kpi-card {
    padding: 20px;
  }

  .kpi-value {
    font-size: 1.5rem;
  }

  .toolbar-top {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .toolbar-left {
    flex-direction: column;
    gap: 12px;
  }

  .search-input {
    width: 100%;
  }

  .toolbar-right {
    flex-direction: column;
    gap: 12px;
  }

  .bulk-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .product-card.list-view .product-content {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .export-menu {
    width: 280px;
    right: -20px;
  }

  .pagination {
    flex-wrap: wrap;
    gap: 4px;
  }

  .page-info {
    order: -1;
    width: 100%;
    text-align: center;
    margin: 0 0 12px 0;
  }
}

/* Améliorations pour l'affichage des images */
.product-image img {
  transition: transform 0.3s ease, opacity 0.3s ease;
  background: #f3f4f6;
}

.product-image img:hover {
  transform: scale(1.05);
}

/* Animation de chargement pour les images */
.product-image {
  background: linear-gradient(45deg, #f3f4f6 25%, transparent 25%),
              linear-gradient(-45deg, #f3f4f6 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, #f3f4f6 75%),
              linear-gradient(-45deg, transparent 75%, #f3f4f6 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.product-image img[src] {
  background: none;
}

/* Placeholder pour images manquantes */
.product-image .placeholder-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #9ca3af;
  font-size: 0.875rem;
  height: 100%;
}

/* Amélioration de la qualité d'affichage */
.product-image img {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Gestion des erreurs d'images */
.product-image img[src*="placeholder"] {
  opacity: 0.8;
  filter: grayscale(20%);
}
