import React, { useState } from 'react';
import { Edit, Trash2, MoreHorizontal } from 'lucide-react';
import { IPost } from '../../types/index';
import { useAuth } from '../../context/AuthContext';
import { usePosts } from '../../context/PostsContext';
import Button from '../ui/Button';

interface PostActionsProps {
  post: IPost;
  onEdit?: () => void;
}

const PostActions: React.FC<PostActionsProps> = ({ post, onEdit }) => {
  const { currentUser } = useAuth();
  const { deletePost } = usePosts();
  const [showMenu, setShowMenu] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Vérifier si l'utilisateur actuel est l'auteur du post
  const isAuthor = currentUser?.id === post.userId;

  if (!isAuthor) {
    return null; // Ne pas afficher les actions si ce n'est pas l'auteur
  }

  const handleEdit = () => {
    setShowMenu(false);
    if (onEdit) {
      onEdit();
    }
  };

  const handleDelete = async () => {
    setShowMenu(false);
    setIsDeleting(true);
    
    try {
      await deletePost(post.id);
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="relative">
      {/* Bouton menu */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setShowMenu(!showMenu)}
        className="p-1 hover:bg-gray-100 rounded-full"
        disabled={isDeleting}
      >
        <MoreHorizontal size={16} className="text-gray-500" />
      </Button>

      {/* Menu déroulant */}
      {showMenu && (
        <>
          {/* Overlay pour fermer le menu */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setShowMenu(false)}
          />
          
          {/* Menu */}
          <div className="absolute right-0 top-8 z-20 bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-[120px]">
            <button
              onClick={handleEdit}
              className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center"
            >
              <Edit size={14} className="mr-2" />
              Modifier
            </button>
            
            <button
              onClick={handleDelete}
              disabled={isDeleting}
              className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center disabled:opacity-50"
            >
              <Trash2 size={14} className="mr-2" />
              {isDeleting ? 'Suppression...' : 'Supprimer'}
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default PostActions;
