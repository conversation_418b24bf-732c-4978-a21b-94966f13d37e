-- Migration pour améliorer la table business_profiles avec tous les champs nécessaires
-- Cette migration ajoute les champs manquants pour le profil d'entreprise complet

-- Ajouter les nouvelles colonnes à la table business_profiles
ALTER TABLE business_profiles
ADD COLUMN IF NOT EXISTS business_address TEXT,
ADD COLUMN IF NOT EXISTS business_phone VARCHAR(20),
ADD COLUMN IF NOT EXISTS business_email VARCHAR(255),
ADD COLUMN IF NOT EXISTS business_website TEXT,
ADD COLUMN IF NOT EXISTS business_category VARCHAR(100),
ADD COLUMN IF NOT EXISTS business_license VARCHAR(100),
ADD COLUMN IF NOT EXISTS founded_year INTEGER,
ADD COLUMN IF NOT EXISTS employee_count INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS cover_photo_url TEXT,
ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3,2) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS total_reviews INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS followers_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS following_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT now(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT now();

-- Ajouter des contraintes pour valider les données
ALTER TABLE business_profiles
ADD CONSTRAINT IF NOT EXISTS business_email_format 
CHECK (business_email IS NULL OR business_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE business_profiles
ADD CONSTRAINT IF NOT EXISTS business_phone_format 
CHECK (business_phone IS NULL OR business_phone ~* '^\+?[0-9\s\-\(\)]{8,20}$');

ALTER TABLE business_profiles
ADD CONSTRAINT IF NOT EXISTS business_website_format 
CHECK (business_website IS NULL OR business_website ~* '^https?://[^\s]+$');

ALTER TABLE business_profiles
ADD CONSTRAINT IF NOT EXISTS founded_year_range 
CHECK (founded_year IS NULL OR (founded_year >= 1800 AND founded_year <= EXTRACT(YEAR FROM CURRENT_DATE)));

ALTER TABLE business_profiles
ADD CONSTRAINT IF NOT EXISTS employee_count_positive 
CHECK (employee_count > 0);

ALTER TABLE business_profiles
ADD CONSTRAINT IF NOT EXISTS average_rating_range 
CHECK (average_rating >= 0.0 AND average_rating <= 5.0);

ALTER TABLE business_profiles
ADD CONSTRAINT IF NOT EXISTS total_reviews_positive 
CHECK (total_reviews >= 0);

ALTER TABLE business_profiles
ADD CONSTRAINT IF NOT EXISTS followers_count_positive 
CHECK (followers_count >= 0);

ALTER TABLE business_profiles
ADD CONSTRAINT IF NOT EXISTS following_count_positive 
CHECK (following_count >= 0);

-- Ajouter des index pour améliorer les performances
CREATE INDEX IF NOT EXISTS business_profiles_category_idx ON business_profiles(business_category);
CREATE INDEX IF NOT EXISTS business_profiles_founded_year_idx ON business_profiles(founded_year);
CREATE INDEX IF NOT EXISTS business_profiles_rating_idx ON business_profiles(average_rating);
CREATE INDEX IF NOT EXISTS business_profiles_created_at_idx ON business_profiles(created_at);

-- Ajouter le trigger pour updated_at si la fonction existe
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_updated_at_column') THEN
        DROP TRIGGER IF EXISTS update_business_profiles_updated_at ON business_profiles;
        CREATE TRIGGER update_business_profiles_updated_at
        BEFORE UPDATE ON business_profiles
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Ajouter des commentaires pour documenter les nouvelles colonnes
COMMENT ON COLUMN business_profiles.business_address IS 'Adresse physique de l''entreprise';
COMMENT ON COLUMN business_profiles.business_phone IS 'Numéro de téléphone professionnel de l''entreprise';
COMMENT ON COLUMN business_profiles.business_email IS 'Adresse email professionnelle de l''entreprise';
COMMENT ON COLUMN business_profiles.business_website IS 'Site web de l''entreprise';
COMMENT ON COLUMN business_profiles.business_category IS 'Catégorie d''activité de l''entreprise';
COMMENT ON COLUMN business_profiles.business_license IS 'Numéro de licence ou RCCM de l''entreprise';
COMMENT ON COLUMN business_profiles.founded_year IS 'Année de création de l''entreprise';
COMMENT ON COLUMN business_profiles.employee_count IS 'Nombre d''employés de l''entreprise';
COMMENT ON COLUMN business_profiles.cover_photo_url IS 'URL de la photo de couverture de l''entreprise';
COMMENT ON COLUMN business_profiles.average_rating IS 'Note moyenne de l''entreprise (0-5)';
COMMENT ON COLUMN business_profiles.total_reviews IS 'Nombre total d''avis reçus';
COMMENT ON COLUMN business_profiles.followers_count IS 'Nombre de followers de l''entreprise';
COMMENT ON COLUMN business_profiles.following_count IS 'Nombre d''entreprises/utilisateurs suivis';

-- Mettre à jour les politiques RLS pour inclure les nouvelles colonnes
DROP POLICY IF EXISTS "Businesses can update their own profile" ON business_profiles;
CREATE POLICY "Businesses can update their own profile"
  ON business_profiles
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Ajouter une politique pour l'insertion
CREATE POLICY IF NOT EXISTS "Businesses can insert their own profile"
  ON business_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- Mettre à jour la fonction handle_new_user pour inclure les nouveaux champs
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, username, email, role)
  VALUES (
    new.id,
    new.raw_user_meta_data->>'username',
    new.email,
    COALESCE(new.raw_user_meta_data->>'role', 'standard')
  );

  -- Si l'utilisateur est une entreprise, créer le profil business
  IF (new.raw_user_meta_data->>'role' = 'business') THEN
    INSERT INTO public.business_profiles (
      id,
      business_name,
      business_description,
      business_category,
      business_phone,
      business_email,
      business_address,
      business_website,
      founded_year,
      employee_count
    )
    VALUES (
      new.id,
      new.raw_user_meta_data->>'businessName',
      new.raw_user_meta_data->>'businessDescription',
      new.raw_user_meta_data->>'businessCategory',
      new.raw_user_meta_data->>'businessPhone',
      new.raw_user_meta_data->>'businessEmail',
      new.raw_user_meta_data->>'businessAddress',
      new.raw_user_meta_data->>'businessWebsite',
      COALESCE((new.raw_user_meta_data->>'foundedYear')::integer, EXTRACT(YEAR FROM CURRENT_DATE)::integer),
      COALESCE((new.raw_user_meta_data->>'employeeCount')::integer, 1)
    );
  END IF;

  RETURN new;
END;
$$ language plpgsql security definer;
