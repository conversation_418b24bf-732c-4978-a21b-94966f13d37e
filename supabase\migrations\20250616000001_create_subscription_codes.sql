-- <PERSON><PERSON><PERSON> la table des codes d'abonnement
CREATE TABLE IF NOT EXISTS public.subscription_codes (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  code text NOT NULL UNIQUE,
  business_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  business_name text NOT NULL,
  plan_type text NOT NULL CHECK (plan_type IN ('trial', 'monthly', 'yearly')),
  amount integer NOT NULL, -- Montant en centimes
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'validated', 'rejected', 'expired')),
  generated_at timestamptz DEFAULT now(),
  expires_at timestamptz NOT NULL,
  validated_at timestamptz,
  validated_by uuid REFERENCES auth.users(id),
  rejection_reason text,
  admin_notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- <PERSON><PERSON><PERSON> des index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_subscription_codes_business_id ON public.subscription_codes(business_id);
CREATE INDEX IF NOT EXISTS idx_subscription_codes_code ON public.subscription_codes(code);
CREATE INDEX IF NOT EXISTS idx_subscription_codes_status ON public.subscription_codes(status);
CREATE INDEX IF NOT EXISTS idx_subscription_codes_expires_at ON public.subscription_codes(expires_at);
CREATE INDEX IF NOT EXISTS idx_subscription_codes_generated_at ON public.subscription_codes(generated_at DESC);

-- Activer RLS
ALTER TABLE public.subscription_codes ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre aux utilisateurs de voir leurs propres codes
CREATE POLICY "Users can view their own subscription codes" ON public.subscription_codes
  FOR SELECT USING (auth.uid() = business_id);

-- Politique pour permettre l'insertion de codes (pour le système)
CREATE POLICY "System can insert subscription codes" ON public.subscription_codes
  FOR INSERT WITH CHECK (true);

-- Politique pour permettre aux admins de voir tous les codes
CREATE POLICY "Admins can view all subscription codes" ON public.subscription_codes
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.admin_profiles 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

-- Politique pour permettre aux admins de mettre à jour les codes
CREATE POLICY "Admins can update subscription codes" ON public.subscription_codes
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.admin_profiles 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_subscription_codes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour automatiquement updated_at
CREATE TRIGGER update_subscription_codes_updated_at
  BEFORE UPDATE ON public.subscription_codes
  FOR EACH ROW
  EXECUTE FUNCTION update_subscription_codes_updated_at();

-- Donner les permissions nécessaires
GRANT SELECT, INSERT ON public.subscription_codes TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.subscription_codes TO service_role;
