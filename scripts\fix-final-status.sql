-- CORRECTION FINALE DU STATUT UTILISATEUR
-- Basé sur les statuts probables de votre système Customeroom

-- =====================================================
-- 1. DIAGNOSTIC COMPLET
-- =====================================================

-- Voir la contrainte valid_status
SELECT 
    'Contrainte valid_status:' as info,
    check_clause as definition
FROM information_schema.check_constraints 
WHERE constraint_name = 'valid_status';

-- Voir la définition de la colonne status
SELECT 
    'Colonne status:' as info,
    data_type || ' - Default: ' || COALESCE(column_default, 'NULL') as definition
FROM information_schema.columns 
WHERE table_name = 'profiles' AND column_name = 'status';

-- =====================================================
-- 2. TESTS AVEC LES STATUTS CUSTOMEROOM
-- =====================================================

-- Test avec les statuts de votre système (selon vos types TypeScript)
DO $$
DECLARE
    test_id uuid;
    statuts_a_tester text[] := ARRAY['newbie', 'member', 'contributor', 'discoverer', 'influencer', 'leader'];
    statut text;
    statut_valide text := NULL;
BEGIN
    RAISE NOTICE '🧪 TESTS DES STATUTS CUSTOMEROOM:';
    
    FOREACH statut IN ARRAY statuts_a_tester
    LOOP
        test_id := gen_random_uuid();
        BEGIN
            INSERT INTO profiles (id, username, email, role, status, created_at, updated_at)
            VALUES (test_id, 'test_' || statut, 'test_' || statut || '@example.com', 'standard', statut, now(), now());
            
            RAISE NOTICE '✅ Statut %: AUTORISÉ', upper(statut);
            
            -- Garder le premier statut qui marche
            IF statut_valide IS NULL THEN
                statut_valide := statut;
            END IF;
            
            DELETE FROM profiles WHERE id = test_id;
            
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE '❌ Statut %: REFUSÉ', upper(statut);
        END;
    END LOOP;
    
    IF statut_valide IS NOT NULL THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎯 STATUT VALIDE TROUVÉ: %', upper(statut_valide);
        RAISE NOTICE '';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '❌ AUCUN STATUT VALIDE TROUVÉ - Vérifiez la contrainte';
        RAISE NOTICE '';
    END IF;
END $$;

-- =====================================================
-- 3. FONCTION CORRIGÉE AVEC STATUT PAR DÉFAUT
-- =====================================================

-- Fonction avec le statut le plus probable (newbie ou member)
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  -- Insérer le profil utilisateur avec statut par défaut
  INSERT INTO public.profiles (
    id, 
    username, 
    email, 
    role,
    status,
    created_at,
    updated_at
  )
  VALUES (
    new.id,
    new.raw_user_meta_data->>'username',
    new.email,
    COALESCE(new.raw_user_meta_data->>'role', 'standard'),
    'member',  -- Statut par défaut le plus probable
    now(),
    now()
  );

  -- Si l'utilisateur est une entreprise, créer le profil business
  IF (new.raw_user_meta_data->>'role' = 'business') THEN
    INSERT INTO public.business_profiles (
      id,
      business_name,
      business_description,
      created_at,
      updated_at
    )
    VALUES (
      new.id,
      new.raw_user_meta_data->>'businessName',
      COALESCE(new.raw_user_meta_data->>'businessDescription', ''),
      now(),
      now()
    );
  END IF;

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 4. FONCTION ALTERNATIVE AVEC NEWBIE
-- =====================================================

-- Si 'member' ne marche pas, essayer avec 'newbie'
CREATE OR REPLACE FUNCTION public.handle_new_user_newbie()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (
    id, username, email, role, status, created_at, updated_at
  ) VALUES (
    new.id,
    new.raw_user_meta_data->>'username',
    new.email,
    COALESCE(new.raw_user_meta_data->>'role', 'standard'),
    'newbie',  -- Statut débutant
    now(),
    now()
  );

  IF (new.raw_user_meta_data->>'role' = 'business') THEN
    INSERT INTO public.business_profiles (
      id, business_name, business_description, created_at, updated_at
    ) VALUES (
      new.id,
      new.raw_user_meta_data->>'businessName',
      COALESCE(new.raw_user_meta_data->>'businessDescription', ''),
      now(),
      now()
    );
  END IF;

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 5. SOLUTION DE DERNIER RECOURS
-- =====================================================

-- Si aucun statut ne marche, essayer sans statut (NULL)
CREATE OR REPLACE FUNCTION public.handle_new_user_no_status()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (
    id, username, email, role, created_at, updated_at
  ) VALUES (
    new.id,
    new.raw_user_meta_data->>'username',
    new.email,
    COALESCE(new.raw_user_meta_data->>'role', 'standard'),
    now(),
    now()
  );

  IF (new.raw_user_meta_data->>'role' = 'business') THEN
    INSERT INTO public.business_profiles (
      id, business_name, business_description, created_at, updated_at
    ) VALUES (
      new.id,
      new.raw_user_meta_data->>'businessName',
      COALESCE(new.raw_user_meta_data->>'businessDescription', ''),
      now(),
      now()
    );
  END IF;

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 6. MISE À JOUR DU TRIGGER
-- =====================================================

-- Supprimer l'ancien trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Créer le nouveau trigger (commencer avec 'member')
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW 
  EXECUTE FUNCTION public.handle_new_user();

-- =====================================================
-- 7. TEST FINAL DE LA FONCTION
-- =====================================================

DO $$
DECLARE
    test_id uuid := gen_random_uuid();
    test_username text := 'test_final_' || extract(epoch from now())::text;
    test_email text := 'testfinal' || extract(epoch from now())::text || '@example.com';
BEGIN
    BEGIN
        -- Simuler l'insertion via la fonction handle_new_user
        INSERT INTO profiles (id, username, email, role, status, created_at, updated_at)
        VALUES (test_id, test_username, test_email, 'standard', 'member', now(), now());
        
        RAISE NOTICE '✅ TEST FINAL AVEC MEMBER: SUCCÈS';
        DELETE FROM profiles WHERE id = test_id;
        
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '❌ Test avec member échoué: %', SQLERRM;
        
        -- Essayer avec newbie
        BEGIN
            INSERT INTO profiles (id, username, email, role, status, created_at, updated_at)
            VALUES (test_id, test_username, test_email, 'standard', 'newbie', now(), now());
            
            RAISE NOTICE '✅ TEST AVEC NEWBIE: SUCCÈS';
            DELETE FROM profiles WHERE id = test_id;
            
            -- Changer la fonction pour utiliser newbie
            DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
            CREATE TRIGGER on_auth_user_created
              AFTER INSERT ON auth.users
              FOR EACH ROW 
              EXECUTE FUNCTION public.handle_new_user_newbie();
              
            RAISE NOTICE '🔄 Trigger mis à jour pour utiliser NEWBIE';
            
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE '❌ Test avec newbie aussi échoué: %', SQLERRM;
            RAISE NOTICE '🚨 Utilisation de la fonction sans statut';
            
            -- Utiliser la fonction sans statut
            DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
            CREATE TRIGGER on_auth_user_created
              AFTER INSERT ON auth.users
              FOR EACH ROW 
              EXECUTE FUNCTION public.handle_new_user_no_status();
        END;
    END;
END $$;

-- =====================================================
-- 8. INSTRUCTIONS FINALES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '🎉 CORRECTION DU STATUT TERMINÉE';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '📋 RÉSULTATS DES TESTS:';
    RAISE NOTICE 'Regardez les messages ci-dessus pour voir';
    RAISE NOTICE 'quels statuts sont autorisés.';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 TESTEZ MAINTENANT:';
    RAISE NOTICE '1. Allez sur http://localhost:5173';
    RAISE NOTICE '2. Cliquez sur S''inscrire';
    RAISE NOTICE '3. Créez un compte test';
    RAISE NOTICE '';
    RAISE NOTICE 'Si ça ne marche toujours pas:';
    RAISE NOTICE '- Vérifiez la contrainte valid_status';
    RAISE NOTICE '- Consultez les logs Supabase';
    RAISE NOTICE '- Essayez de désactiver temporairement la contrainte';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- 9. OPTION DE DÉSACTIVATION TEMPORAIRE
-- =====================================================

-- Si rien ne marche, vous pouvez temporairement désactiver la contrainte:
-- ALTER TABLE profiles DROP CONSTRAINT IF EXISTS valid_status;
-- (Puis la recréer plus tard avec les bonnes valeurs)

-- Voir toutes les contraintes sur profiles
SELECT 
    'Contraintes sur profiles:' as info,
    constraint_name || ' - ' || constraint_type as details
FROM information_schema.table_constraints 
WHERE table_name = 'profiles' 
AND constraint_type = 'CHECK';
