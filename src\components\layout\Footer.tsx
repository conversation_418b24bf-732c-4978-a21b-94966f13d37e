import React from 'react';
import { Link } from 'react-router-dom';
import { Heart, Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo et Description */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Heart className="text-white" size={20} />
              </div>
              <span className="text-xl font-bold">Customeroom</span>
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              La plateforme qui révolutionne la relation entre entreprises et clients en Afrique de l'Ouest. 
              Partagez vos expériences, découvrez de nouvelles entreprises et aidez la communauté à faire de meilleurs choix.
            </p>
            
            {/* Réseaux Sociaux */}
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Linkedin size={20} />
              </a>
            </div>
          </div>

          {/* Liens Rapides */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Liens Rapides</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-white transition-colors">
                  Accueil
                </Link>
              </li>
              <li>
                <Link to="/marketplace" className="text-gray-300 hover:text-white transition-colors">
                  Marketplace
                </Link>
              </li>
              <li>
                <Link to="/recommendations" className="text-gray-300 hover:text-white transition-colors">
                  Recommandations
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-300 hover:text-white transition-colors">
                  À propos
                </Link>
              </li>
            </ul>
          </div>

          {/* Support et Contact */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Support</h3>
            <ul className="space-y-2">
              <li>
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white transition-colors flex items-center">
                  <Mail size={16} className="mr-2" />
                  Support
                </a>
              </li>
              <li>
                <a href="tel:+22501234567" className="text-gray-300 hover:text-white transition-colors flex items-center">
                  <Phone size={16} className="mr-2" />
                  +225 01 23 45 67
                </a>
              </li>
              <li>
                <div className="text-gray-300 flex items-start">
                  <MapPin size={16} className="mr-2 mt-1 flex-shrink-0" />
                  <span>Abidjan, Côte d'Ivoire</span>
                </div>
              </li>
            </ul>
          </div>
        </div>

        {/* Séparateur */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            {/* Copyright */}
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              © 2024 Customeroom. Tous droits réservés.
            </div>

            {/* Liens Légaux */}
            <div className="flex flex-wrap justify-center md:justify-end space-x-6 text-sm">
              <Link 
                to="/about" 
                className="text-gray-400 hover:text-white transition-colors"
              >
                À propos
              </Link>
              <Link 
                to="/privacy" 
                className="text-gray-400 hover:text-white transition-colors"
              >
                Confidentialité
              </Link>
              <Link 
                to="/terms" 
                className="text-gray-400 hover:text-white transition-colors"
              >
                Conditions
              </Link>
              <a 
                href="mailto:<EMAIL>" 
                className="text-gray-400 hover:text-white transition-colors"
              >
                Contact
              </a>
            </div>
          </div>
        </div>

        {/* Message de Fierté Africaine */}
        <div className="text-center mt-6 pt-6 border-t border-gray-800">
          <p className="text-gray-400 text-sm">
            🌍 Fièrement développé en Afrique, pour l'Afrique
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
