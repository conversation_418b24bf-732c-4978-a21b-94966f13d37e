import { supabase } from '../lib/supabase';
import { IFollowRequest, IPrivacySettings, IUserBlock } from '../types';
import { NotificationType } from '../context/NotificationsContext';

export class FollowRequestService {

  /**
   * Valider qu'un ID est un UUID valide
   */
  private static isValidUUID(id: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  }

  /**
   * Récupérer les profils avec les informations business
   */
  private static async getProfilesWithBusinessInfo(userIds: string[]): Promise<any[]> {
    try {
      if (userIds.length === 0) return [];

      // Récupérer les profils de base
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, username, profile_picture, role, status')
        .in('id', userIds);

      if (profilesError) {
        console.error('Erreur lors de la récupération des profils:', profilesError);
        return [];
      }

      let profiles = profilesData || [];

      // Récupérer les informations business pour les comptes business
      const businessIds = profiles.filter(p => p.role === 'business').map(p => p.id);
      if (businessIds.length > 0) {
        const { data: businessData, error: businessError } = await supabase
          .from('business_profiles')
          .select('id, business_name')
          .in('id', businessIds);

        if (!businessError && businessData) {
          // Ajouter les noms d'entreprise aux profils
          profiles = profiles.map(profile => {
            const businessInfo = businessData.find(b => b.id === profile.id);
            return {
              ...profile,
              business_name: businessInfo?.business_name || null
            };
          });
        }
      }

      return profiles;
    } catch (error) {
      console.error('Exception lors de la récupération des profils avec business info:', error);
      return [];
    }
  }
  
  /**
   * Créer une demande de suivi
   */
  static async createFollowRequest(
    requesterId: string,
    targetId: string,
    message?: string
  ): Promise<{ success: boolean; requestId?: string; directFollow?: boolean; error?: string }> {
    try {
      // Vérifier que l'utilisateur ne se suit pas lui-même
      if (requesterId === targetId) {
        return { success: false, error: 'Vous ne pouvez pas vous suivre vous-même' };
      }

      // Vérifier s'il y a un blocage
      const { data: blockData } = await supabase
        .from('user_blocks')
        .select('id')
        .or(`and(blocker_id.eq.${targetId},blocked_id.eq.${requesterId}),and(blocker_id.eq.${requesterId},blocked_id.eq.${targetId})`)
        .limit(1);

      if (blockData && blockData.length > 0) {
        return { success: false, error: 'Impossible de suivre cet utilisateur' };
      }

      // Vérifier si déjà suivi
      const { data: existingFollow } = await supabase
        .from('followers')
        .select('id')
        .eq('follower_id', requesterId)
        .eq('following_id', targetId)
        .single();

      if (existingFollow) {
        return { success: false, error: 'Vous suivez déjà cet utilisateur' };
      }

      // Récupérer les paramètres de confidentialité de la cible
      let { data: privacySettings } = await supabase
        .from('privacy_settings')
        .select('*')
        .eq('user_id', targetId)
        .single();

      // Si pas de paramètres, créer des paramètres par défaut
      if (!privacySettings) {
        const { data: newSettings } = await supabase
          .from('privacy_settings')
          .insert([{ user_id: targetId }])
          .select()
          .single();
        privacySettings = newSettings;
      }

      // Si l'approbation n'est pas requise, suivre directement
      if (!privacySettings?.require_follow_approval) {
        const { error: followError } = await supabase
          .from('followers')
          .insert([{
            follower_id: requesterId,
            following_id: targetId,
            follow_type: 'follow',
            status: 'active'
          }]);

        if (followError) {
          return { success: false, error: 'Erreur lors du suivi' };
        }

        // Créer notification de nouveau follower
        await this.createFollowNotification(requesterId, targetId, 'follow');

        return { success: true, directFollow: true };
      }

      // Vérifier s'il y a déjà une demande en attente
      const { data: existingRequest } = await supabase
        .from('follow_requests')
        .select('id')
        .eq('requester_id', requesterId)
        .eq('target_id', targetId)
        .eq('status', 'pending')
        .single();

      if (existingRequest) {
        return { success: false, error: 'Demande de suivi déjà envoyée' };
      }

      // Créer la demande de suivi
      const { data: request, error: requestError } = await supabase
        .from('follow_requests')
        .insert([{
          requester_id: requesterId,
          target_id: targetId,
          message: message || null,
          status: 'pending',
          request_type: 'follow'
        }])
        .select()
        .single();

      if (requestError) {
        return { success: false, error: 'Erreur lors de la création de la demande' };
      }

      // Créer notification de demande de suivi
      await this.createFollowNotification(requesterId, targetId, 'follow_request');

      return { success: true, requestId: request.id };

    } catch (error) {
      console.error('Erreur lors de la création de la demande de suivi:', error);
      return { success: false, error: 'Erreur interne' };
    }
  }

  /**
   * Répondre à une demande de suivi
   */
  static async respondToFollowRequest(
    requestId: string,
    response: 'accepted' | 'rejected',
    targetId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Récupérer la demande
      const { data: request, error: requestError } = await supabase
        .from('follow_requests')
        .select('*')
        .eq('id', requestId)
        .eq('target_id', targetId)
        .eq('status', 'pending')
        .single();

      if (requestError || !request) {
        return { success: false, error: 'Demande de suivi non trouvée' };
      }

      // Mettre à jour le statut de la demande
      const { error: updateError } = await supabase
        .from('follow_requests')
        .update({
          status: response,
          responded_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (updateError) {
        return { success: false, error: 'Erreur lors de la mise à jour' };
      }

      // Si accepté, créer la relation de suivi
      if (response === 'accepted') {
        const { error: followError } = await supabase
          .from('followers')
          .insert([{
            follower_id: request.requester_id,
            following_id: request.target_id,
            follow_type: 'follow',
            status: 'active'
          }]);

        if (followError) {
          console.error('Erreur lors de la création du suivi:', followError);
        }

        // Créer notification d'acceptation
        await this.createFollowNotification(targetId, request.requester_id, 'follow_accepted');
      } else {
        // Créer notification de rejet (optionnel selon les paramètres)
        const { data: privacySettings } = await supabase
          .from('privacy_settings')
          .select('notify_follow_rejected')
          .eq('user_id', request.requester_id)
          .single();

        if (privacySettings?.notify_follow_rejected) {
          await this.createFollowNotification(targetId, request.requester_id, 'follow_rejected');
        }
      }

      return { success: true };

    } catch (error) {
      console.error('Erreur lors de la réponse à la demande:', error);
      return { success: false, error: 'Erreur interne' };
    }
  }

  /**
   * Récupérer les demandes de suivi reçues
   */
  static async getReceivedFollowRequests(userId: string): Promise<IFollowRequest[]> {
    try {
      // Récupérer les demandes sans jointure d'abord
      const { data: requests, error } = await supabase
        .from('follow_requests')
        .select('*')
        .eq('target_id', userId)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erreur lors de la récupération des demandes reçues:', error);
        return [];
      }

      if (!requests || requests.length === 0) {
        console.log('📭 Aucune demande de suivi reçue');
        return [];
      }

      console.log('📬 Demandes reçues trouvées:', requests.length);

      // Récupérer les profils des demandeurs séparément avec validation
      const requesterIds = requests
        .map(req => req.requester_id)
        .filter(id => id && typeof id === 'string' && id.trim().length > 0 && this.isValidUUID(id));

      let profiles = [];

      if (requesterIds.length > 0) {
        console.log('🔍 Récupération des profils demandeurs pour les IDs:', requesterIds);
        profiles = await this.getProfilesWithBusinessInfo(requesterIds);
        console.log('✅ Profils demandeurs récupérés:', profiles.length);
      }

      // Combiner les données avec gestion d'erreur
      return requests.map(item => {
        try {
          const profile = profiles?.find(p => p.id === item.requester_id);

          return {
            id: item.id || '',
            requesterId: item.requester_id || '',
            targetId: item.target_id || '',
            status: item.status || 'pending',
            requestType: item.request_type || 'follow',
            message: item.message || null,
            metadata: item.metadata || null,
            createdAt: item.created_at ? new Date(item.created_at) : new Date(),
            updatedAt: item.updated_at ? new Date(item.updated_at) : new Date(),
            respondedAt: item.responded_at ? new Date(item.responded_at) : undefined,
            requesterProfile: profile ? {
              id: profile.id,
              username: profile.username || 'Utilisateur inconnu',
              profilePicture: profile.profile_picture || '',
              role: profile.role || 'standard',
              status: profile.status || 'active',
              businessName: profile.business_name || '',
              // Autres champs par défaut
              email: '',
              following_count: 0,
              followers_count: 0,
              createdAt: new Date(),
              post_count: 0,
              comment_count: 0,
              recommendations_count: 0
            } : undefined
          };
        } catch (error) {
          console.error('Erreur lors du traitement d\'une demande:', error, item);
          // Retourner un objet par défaut en cas d'erreur
          return {
            id: item.id || '',
            requesterId: item.requester_id || '',
            targetId: item.target_id || '',
            status: 'pending' as const,
            requestType: 'follow' as const,
            message: null,
            metadata: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            respondedAt: undefined,
            requesterProfile: undefined
          };
        }
      }).filter(request => request.id); // Filtrer les demandes invalides

    } catch (error) {
      console.error('Erreur lors de la récupération des demandes:', error);
      return [];
    }
  }

  /**
   * Récupérer les demandes de suivi envoyées
   */
  static async getSentFollowRequests(userId: string): Promise<IFollowRequest[]> {
    try {
      // Récupérer les demandes sans jointure d'abord
      const { data: requests, error } = await supabase
        .from('follow_requests')
        .select('*')
        .eq('requester_id', userId)
        .in('status', ['pending', 'accepted', 'rejected'])
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erreur lors de la récupération des demandes envoyées:', error);
        return [];
      }

      if (!requests || requests.length === 0) {
        console.log('📭 Aucune demande de suivi envoyée');
        return [];
      }

      console.log('📤 Demandes envoyées trouvées:', requests.length);

      // Récupérer les profils des cibles séparément avec validation
      const targetIds = requests
        .map(req => req.target_id)
        .filter(id => id && typeof id === 'string' && id.trim().length > 0 && this.isValidUUID(id));

      let profiles = [];

      if (targetIds.length > 0) {
        console.log('🔍 Récupération des profils cibles pour les IDs:', targetIds);
        profiles = await this.getProfilesWithBusinessInfo(targetIds);
        console.log('✅ Profils cibles récupérés:', profiles.length);
      }

      // Combiner les données avec gestion d'erreur
      return requests.map(item => {
        try {
          const profile = profiles?.find(p => p.id === item.target_id);

          return {
            id: item.id || '',
            requesterId: item.requester_id || '',
            targetId: item.target_id || '',
            status: item.status || 'pending',
            requestType: item.request_type || 'follow',
            message: item.message || null,
            metadata: item.metadata || null,
            createdAt: item.created_at ? new Date(item.created_at) : new Date(),
            updatedAt: item.updated_at ? new Date(item.updated_at) : new Date(),
            respondedAt: item.responded_at ? new Date(item.responded_at) : undefined,
            targetProfile: profile ? {
              id: profile.id,
              username: profile.username || 'Utilisateur inconnu',
              profilePicture: profile.profile_picture || '',
              role: profile.role || 'standard',
              status: profile.status || 'active',
              businessName: profile.business_name || '',
              // Autres champs par défaut
              email: '',
              following_count: 0,
              followers_count: 0,
              createdAt: new Date(),
              post_count: 0,
              comment_count: 0,
              recommendations_count: 0
            } : undefined
          };
        } catch (error) {
          console.error('Erreur lors du traitement d\'une demande envoyée:', error, item);
          // Retourner un objet par défaut en cas d'erreur
          return {
            id: item.id || '',
            requesterId: item.requester_id || '',
            targetId: item.target_id || '',
            status: 'pending' as const,
            requestType: 'follow' as const,
            message: null,
            metadata: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            respondedAt: undefined,
            targetProfile: undefined
          };
        }
      }).filter(request => request.id); // Filtrer les demandes invalides

    } catch (error) {
      console.error('Erreur lors de la récupération des demandes envoyées:', error);
      return [];
    }
  }

  /**
   * Annuler une demande de suivi
   */
  static async cancelFollowRequest(requestId: string, requesterId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('follow_requests')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)
        .eq('requester_id', requesterId)
        .eq('status', 'pending');

      if (error) {
        return { success: false, error: 'Erreur lors de l\'annulation' };
      }

      return { success: true };

    } catch (error) {
      console.error('Erreur lors de l\'annulation de la demande:', error);
      return { success: false, error: 'Erreur interne' };
    }
  }

  /**
   * Créer une notification de suivi
   */
  private static async createFollowNotification(
    senderId: string,
    targetId: string,
    type: 'follow' | 'follow_request' | 'follow_accepted' | 'follow_rejected'
  ): Promise<void> {
    try {
      // Récupérer les infos de l'expéditeur
      const { data: senderProfile } = await supabase
        .from('profiles')
        .select('username, profile_picture')
        .eq('id', senderId)
        .single();

      if (!senderProfile) return;

      let message = '';
      let notificationType: NotificationType;

      switch (type) {
        case 'follow':
          message = `${senderProfile.username} vous suit maintenant`;
          notificationType = NotificationType.FOLLOW;
          break;
        case 'follow_request':
          message = `${senderProfile.username} souhaite vous suivre`;
          notificationType = NotificationType.FOLLOW_REQUEST;
          break;
        case 'follow_accepted':
          message = `${senderProfile.username} a accepté votre demande de suivi`;
          notificationType = NotificationType.FOLLOW_ACCEPTED;
          break;
        case 'follow_rejected':
          message = `${senderProfile.username} a refusé votre demande de suivi`;
          notificationType = NotificationType.FOLLOW_REJECTED;
          break;
      }

      // Créer la notification
      await supabase
        .from('notifications')
        .insert([{
          user_id: targetId,
          sender_id: senderId,
          sender_name: senderProfile.username,
          sender_profile_picture: senderProfile.profile_picture,
          type: notificationType,
          message,
          read: false
        }]);

    } catch (error) {
      console.error('Erreur lors de la création de la notification:', error);
    }
  }
}
