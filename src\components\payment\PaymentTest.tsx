import React, { useState } from 'react';
import { CreditCard, CheckCircle } from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';

const PaymentTest: React.FC = () => {
  const [step, setStep] = useState<'select' | 'success'>('select');

  const handlePayment = () => {
    // Simuler un paiement réussi
    setTimeout(() => {
      setStep('success');
    }, 2000);
  };

  if (step === 'success') {
    return (
      <Card className="max-w-md mx-auto">
        <CardBody className="text-center p-8">
          <CheckCircle className="mx-auto text-green-600 mb-4" size={64} />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Paiement réussi !</h2>
          <p className="text-gray-600 mb-4">Votre abonnement a été activé avec succès.</p>
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="text-sm text-gray-600">
              <div className="flex justify-between mb-2">
                <span>Numéro de facture :</span>
                <span className="font-medium">INV-2024-001234</span>
              </div>
              <div className="flex justify-between mb-2">
                <span>Montant :</span>
                <span className="font-medium">35,000 F CFA</span>
              </div>
              <div className="flex justify-between">
                <span>Date :</span>
                <span className="font-medium">{new Date().toLocaleDateString('fr-FR')}</span>
              </div>
            </div>
          </div>
          <Button onClick={() => setStep('select')} className="w-full">
            Nouveau test
          </Button>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card className="max-w-md mx-auto">
      <CardBody className="p-8">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Test de paiement</h2>
          <p className="text-gray-600">Testez le processus de paiement</p>
        </div>

        <div className="space-y-4 mb-6">
          <div className="border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-300">
            <div className="flex items-center space-x-3">
              <CreditCard className="text-blue-600" size={24} />
              <div>
                <h3 className="font-medium text-gray-900">Carte Visa</h3>
                <p className="text-sm text-gray-600">•••• •••• •••• 4242</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-gray-900 mb-2">Résumé</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Plan Professional</span>
              <span className="font-medium">35,000 F CFA</span>
            </div>
            <div className="flex justify-between border-t pt-2">
              <span className="font-medium">Total</span>
              <span className="font-bold">35,000 F CFA</span>
            </div>
          </div>
        </div>

        <Button onClick={handlePayment} className="w-full">
          <CreditCard size={16} className="mr-2" />
          Confirmer le paiement
        </Button>
      </CardBody>
    </Card>
  );
};

export default PaymentTest;
