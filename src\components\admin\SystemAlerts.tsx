import React, { useState, useEffect } from 'react';
import {
  AlertTriangle,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Search,
  Filter,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
  UserCheck,
  Calendar,
  Tag,
  TrendingUp,
  Shield,
  Download,
  RefreshCw,
  Plus,
  Bell,
  BellOff,
  Archive,
  MessageSquare,
  Settings,
  Zap,
  Database,
  Server,
  Users,
  Building,
  FileText,
  Activity
} from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import { AdminService } from '../../services/adminService';
import { AdminAlert, AlertPriority, AlertStatus } from '../../types/admin';
import { supabase } from '../../lib/supabase';

// Types pour les statistiques d'alertes
interface AlertStats {
  total: number;
  open: number;
  inProgress: number;
  resolved: number;
  dismissed: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
  todayCreated: number;
  avgResolutionTime: number;
}

// Type étendu pour les administrateurs dans les alertes
interface AlertAdminProfile {
  id: string;
  admin_code: string;
  department?: string;
  username?: string;
  email?: string;
  profile_picture?: string;
}

// Type étendu pour les alertes avec relations
interface ExtendedAdminAlert extends Omit<AdminAlert, 'assignee' | 'creator'> {
  assignee?: AlertAdminProfile;
  creator?: AlertAdminProfile;
}

// Fonctions utilitaires pour les alertes
const getPriorityIcon = (priority: AlertPriority) => {
  switch (priority) {
    case AlertPriority.CRITICAL: return <AlertTriangle size={16} className="text-red-500" />;
    case AlertPriority.HIGH: return <AlertCircle size={16} className="text-orange-500" />;
    case AlertPriority.MEDIUM: return <Info size={16} className="text-yellow-500" />;
    case AlertPriority.LOW: return <CheckCircle size={16} className="text-blue-500" />;
    default: return <Info size={16} className="text-gray-500" />;
  }
};

const getPriorityColor = (priority: AlertPriority) => {
  switch (priority) {
    case AlertPriority.CRITICAL: return 'bg-red-100 text-red-800 border-red-200';
    case AlertPriority.HIGH: return 'bg-orange-100 text-orange-800 border-orange-200';
    case AlertPriority.MEDIUM: return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case AlertPriority.LOW: return 'bg-blue-100 text-blue-800 border-blue-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getStatusIcon = (status: AlertStatus) => {
  switch (status) {
    case AlertStatus.OPEN: return <Bell size={16} className="text-red-500" />;
    case AlertStatus.IN_PROGRESS: return <Clock size={16} className="text-yellow-500" />;
    case AlertStatus.RESOLVED: return <CheckCircle size={16} className="text-green-500" />;
    case AlertStatus.DISMISSED: return <XCircle size={16} className="text-gray-500" />;
    default: return <Bell size={16} className="text-gray-500" />;
  }
};

const getStatusColor = (status: AlertStatus) => {
  switch (status) {
    case AlertStatus.OPEN: return 'bg-red-100 text-red-800';
    case AlertStatus.IN_PROGRESS: return 'bg-yellow-100 text-yellow-800';
    case AlertStatus.RESOLVED: return 'bg-green-100 text-green-800';
    case AlertStatus.DISMISSED: return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getStatusLabel = (status: AlertStatus) => {
  switch (status) {
    case AlertStatus.OPEN: return 'Ouverte';
    case AlertStatus.IN_PROGRESS: return 'En cours';
    case AlertStatus.RESOLVED: return 'Résolue';
    case AlertStatus.DISMISSED: return 'Ignorée';
    default: return status;
  }
};

const getAlertTypeIcon = (type: string) => {
  switch (type) {
    case 'security': return <Shield size={16} className="text-red-500" />;
    case 'system': return <Server size={16} className="text-blue-500" />;
    case 'database': return <Database size={16} className="text-purple-500" />;
    case 'user_action': return <Users size={16} className="text-green-500" />;
    case 'business': return <Building size={16} className="text-orange-500" />;
    case 'content': return <FileText size={16} className="text-yellow-500" />;
    case 'performance': return <Activity size={16} className="text-pink-500" />;
    case 'deployment': return <Zap size={16} className="text-indigo-500" />;
    default: return <AlertTriangle size={16} className="text-gray-500" />;
  }
};

// Types pour les actions d'alertes
interface AlertAction {
  alertId: string;
  action: 'assign' | 'resolve' | 'dismiss' | 'escalate' | 'update_priority';
  assigneeId?: string;
  resolutionNotes?: string;
  newPriority?: AlertPriority;
  reason?: string;
}

const SystemAlerts: React.FC = () => {
  const [alerts, setAlerts] = useState<ExtendedAdminAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedPriority, setSelectedPriority] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);
  const [selectedAlerts, setSelectedAlerts] = useState<string[]>([]);
  const [showActionModal, setShowActionModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showBulkActionModal, setShowBulkActionModal] = useState(false);
  const [actionAlert, setActionAlert] = useState<ExtendedAdminAlert | null>(null);
  const [detailAlert, setDetailAlert] = useState<ExtendedAdminAlert | null>(null);
  const [stats, setStats] = useState<AlertStats>({
    total: 0,
    open: 0,
    inProgress: 0,
    resolved: 0,
    dismissed: 0,
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
    todayCreated: 0,
    avgResolutionTime: 0
  });

  useEffect(() => {
    loadAlerts();
  }, [sortBy, sortOrder]);

  const loadAlerts = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔍 Chargement des alertes système...');

      // Charger les alertes avec les informations des administrateurs
      const { data: alertsData, error: alertsError } = await supabase
        .from('admin_alerts')
        .select(`
          id,
          alert_type,
          priority,
          title,
          message,
          details,
          assigned_to,
          created_by,
          status,
          resolution_notes,
          created_at,
          updated_at,
          resolved_at
        `)
        .order(sortBy, { ascending: sortOrder === 'asc' })
        .limit(100);

      if (alertsError) {
        console.error('Erreur lors du chargement des alertes:', alertsError);
        setError(`Erreur alertes: ${alertsError.message}`);
        return;
      }

      console.log('✅ Alertes chargées:', alertsData?.length || 0);

      // Récupérer les informations des administrateurs
      const adminIds = new Set<string>();
      alertsData?.forEach(alert => {
        if (alert.assigned_to) adminIds.add(alert.assigned_to);
        if (alert.created_by) adminIds.add(alert.created_by);
      });

      let adminsData: any[] = [];
      if (adminIds.size > 0) {
        const { data: adminProfiles, error: adminError } = await supabase
          .from('admin_profiles')
          .select(`
            id,
            admin_code,
            department,
            profiles!admin_profiles_user_id_fkey(
              id,
              username,
              email,
              profile_picture
            )
          `)
          .in('id', Array.from(adminIds));

        if (adminError) {
          console.error('Erreur lors du chargement des admins:', adminError);
        } else {
          adminsData = adminProfiles || [];
          console.log('✅ Administrateurs chargés:', adminsData.length);
        }
      }

      // Créer un map des administrateurs
      const adminsMap = new Map();
      adminsData.forEach(admin => {
        adminsMap.set(admin.id, {
          id: admin.id,
          admin_code: admin.admin_code,
          department: admin.department,
          username: admin.profiles?.username || 'Admin supprimé',
          email: admin.profiles?.email || '',
          profile_picture: admin.profiles?.profile_picture
        });
      });

      // Enrichir les alertes avec les informations des administrateurs
      const enrichedAlerts: AdminAlert[] = (alertsData || []).map(alert => ({
        ...alert,
        assignee: alert.assigned_to ? adminsMap.get(alert.assigned_to) : undefined,
        creator: alert.created_by ? adminsMap.get(alert.created_by) : undefined
      }));

      setAlerts(enrichedAlerts);

      // Calculer les statistiques
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      const newStats: AlertStats = {
        total: enrichedAlerts.length,
        open: enrichedAlerts.filter(alert => alert.status === 'open').length,
        inProgress: enrichedAlerts.filter(alert => alert.status === 'in_progress').length,
        resolved: enrichedAlerts.filter(alert => alert.status === 'resolved').length,
        dismissed: enrichedAlerts.filter(alert => alert.status === 'dismissed').length,
        critical: enrichedAlerts.filter(alert => alert.priority === 'critical').length,
        high: enrichedAlerts.filter(alert => alert.priority === 'high').length,
        medium: enrichedAlerts.filter(alert => alert.priority === 'medium').length,
        low: enrichedAlerts.filter(alert => alert.priority === 'low').length,
        todayCreated: enrichedAlerts.filter(alert => {
          const alertDate = new Date(alert.created_at);
          return alertDate >= today;
        }).length,
        avgResolutionTime: calculateAverageResolutionTime(enrichedAlerts)
      };

      console.log('📈 Statistiques calculées:', newStats);
      setStats(newStats);

    } catch (error) {
      console.error('Erreur dans loadAlerts:', error);
      setError(`Erreur inattendue: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    } finally {
      setLoading(false);
    }
  };

  const calculateAverageResolutionTime = (alerts: AdminAlert[]): number => {
    const resolvedAlerts = alerts.filter(alert => 
      alert.status === 'resolved' && alert.resolved_at
    );

    if (resolvedAlerts.length === 0) return 0;

    const totalTime = resolvedAlerts.reduce((sum, alert) => {
      const created = new Date(alert.created_at).getTime();
      const resolved = new Date(alert.resolved_at!).getTime();
      return sum + (resolved - created);
    }, 0);

    // Retourner en heures
    return Math.round(totalTime / (1000 * 60 * 60 * resolvedAlerts.length));
  };

  // Filtrage des alertes
  const filteredAlerts = alerts.filter(alert => {
    const matchesSearch = !searchTerm || 
      alert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.alert_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (alert.assignee?.username && alert.assignee.username.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesType = selectedType === 'all' || alert.alert_type === selectedType;
    const matchesStatus = selectedStatus === 'all' || alert.status === selectedStatus;
    const matchesPriority = selectedPriority === 'all' || alert.priority === selectedPriority;

    return matchesSearch && matchesType && matchesStatus && matchesPriority;
  });

  // Pagination
  const totalPages = Math.ceil(filteredAlerts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedAlerts = filteredAlerts.slice(startIndex, startIndex + itemsPerPage);

  const handleAlertAction = async (action: AlertAction) => {
    try {
      const success = await AdminService.executeAlertAction(action);
      if (success) {
        await loadAlerts();
        setShowActionModal(false);
        setActionAlert(null);
      }
    } catch (error) {
      console.error('Erreur lors de l\'action sur l\'alerte:', error);
    }
  };

  const handleBulkAction = async (actionType: string) => {
    if (selectedAlerts.length === 0) return;

    const reason = prompt(`Raison pour l'action "${actionType}" sur ${selectedAlerts.length} alertes:`, '');
    if (!reason) return;

    try {
      for (const alertId of selectedAlerts) {
        await AdminService.executeAlertAction({
          alertId,
          action: actionType as any,
          reason
        });
      }
      
      setSelectedAlerts([]);
      setShowBulkActionModal(false);
      await loadAlerts();
    } catch (error) {
      console.error('Erreur lors de l\'action en lot:', error);
    }
  };

  const exportData = () => {
    const csvContent = [
      ['ID', 'Type', 'Priorité', 'Titre', 'Statut', 'Assigné à', 'Créé le', 'Résolu le'].join(','),
      ...filteredAlerts.map(alert => [
        alert.id,
        alert.alert_type,
        alert.priority,
        `"${alert.title.replace(/"/g, '""')}"`,
        alert.status,
        alert.assignee?.username || 'Non assigné',
        new Date(alert.created_at).toLocaleDateString('fr-FR'),
        alert.resolved_at ? new Date(alert.resolved_at).toLocaleDateString('fr-FR') : 'Non résolu'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `alertes_systeme_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };



  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Chargement des alertes système...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Alertes Système</h1>
          <p className="text-gray-600 mt-2">Gérer et surveiller les alertes administratives</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            leftIcon={<Plus size={16} />}
            onClick={() => setShowCreateModal(true)}
          >
            Nouvelle alerte
          </Button>
          <Button
            variant="outline"
            leftIcon={<Download size={16} />}
            onClick={exportData}
            disabled={filteredAlerts.length === 0}
          >
            Exporter
          </Button>
          <Button
            leftIcon={<RefreshCw size={16} />}
            onClick={() => loadAlerts()}
          >
            Actualiser
          </Button>
        </div>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="cursor-pointer hover:shadow-lg transition-shadow">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <div className="p-2 rounded-full bg-blue-100">
                <Bell size={20} className="text-blue-600" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Ouvertes</p>
                <p className="text-2xl font-bold text-red-600">{stats.open}</p>
              </div>
              <div className="p-2 rounded-full bg-red-100">
                <AlertTriangle size={20} className="text-red-600" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">En cours</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.inProgress}</p>
              </div>
              <div className="p-2 rounded-full bg-yellow-100">
                <Clock size={20} className="text-yellow-600" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Critiques</p>
                <p className="text-2xl font-bold text-red-600">{stats.critical}</p>
              </div>
              <div className="p-2 rounded-full bg-red-100">
                <Zap size={20} className="text-red-600" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Aujourd'hui</p>
                <p className="text-2xl font-bold text-blue-600">{stats.todayCreated}</p>
              </div>
              <div className="p-2 rounded-full bg-blue-100">
                <Calendar size={20} className="text-blue-600" />
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Affichage des erreurs */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardBody className="p-4">
            <div className="flex items-center space-x-2 text-red-700">
              <AlertTriangle size={20} />
              <div>
                <h3 className="font-medium">Erreur de chargement</h3>
                <p className="text-sm">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => {
                    setError(null);
                    loadAlerts();
                  }}
                >
                  Réessayer
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Filtres et recherche */}
      <Card>
        <CardBody className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Recherche */}
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Rechercher par titre, message, type..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Filtre par type */}
            <div>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
              >
                <option value="all">Tous les types</option>
                <option value="security">Sécurité</option>
                <option value="system">Système</option>
                <option value="database">Base de données</option>
                <option value="user_action">Action utilisateur</option>
                <option value="business">Entreprise</option>
                <option value="content">Contenu</option>
                <option value="performance">Performance</option>
                <option value="deployment">Déploiement</option>
              </select>
            </div>

            {/* Filtre par statut */}
            <div>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
              >
                <option value="all">Tous les statuts</option>
                <option value="open">Ouvertes</option>
                <option value="in_progress">En cours</option>
                <option value="resolved">Résolues</option>
                <option value="dismissed">Ignorées</option>
              </select>
            </div>

            {/* Filtre par priorité */}
            <div>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={selectedPriority}
                onChange={(e) => setSelectedPriority(e.target.value)}
              >
                <option value="all">Toutes priorités</option>
                <option value="critical">Critique</option>
                <option value="high">Haute</option>
                <option value="medium">Moyenne</option>
                <option value="low">Basse</option>
              </select>
            </div>
          </div>

          {/* Actions en lot */}
          {selectedAlerts.length > 0 && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-900">
                  {selectedAlerts.length} alerte(s) sélectionnée(s)
                </span>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowBulkActionModal(true)}
                  >
                    Actions en lot
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSelectedAlerts([])}
                  >
                    Désélectionner
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Liste des alertes */}
      <Card>
        <CardBody className="p-0">
          {paginatedAlerts.length === 0 ? (
            <div className="text-center py-12">
              {alerts.length === 0 ? (
                <div className="space-y-3">
                  <Bell size={48} className="mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune alerte système</h3>
                  <p className="text-gray-600">Aucune alerte n'a été créée pour le moment.</p>
                  <Button
                    onClick={() => setShowCreateModal(true)}
                    className="mt-4"
                  >
                    Créer une alerte
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  <Bell size={48} className="mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune alerte trouvée</h3>
                  <p className="text-gray-600">Aucune alerte ne correspond aux critères de recherche.</p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedType('all');
                      setSelectedStatus('all');
                      setSelectedPriority('all');
                    }}
                  >
                    Réinitialiser les filtres
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <>
              {/* En-tête du tableau */}
              <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    className="mr-4"
                    checked={selectedAlerts.length === paginatedAlerts.length}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedAlerts(paginatedAlerts.map(alert => alert.id));
                      } else {
                        setSelectedAlerts([]);
                      }
                    }}
                  />
                  <div className="grid grid-cols-12 gap-4 w-full text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="col-span-1">Priorité</div>
                    <div className="col-span-1">Type</div>
                    <div className="col-span-3">Titre</div>
                    <div className="col-span-2">Assigné à</div>
                    <div className="col-span-2">Statut</div>
                    <div className="col-span-2">Date</div>
                    <div className="col-span-1">Actions</div>
                  </div>
                </div>
              </div>

              {/* Lignes d'alertes */}
              <div className="divide-y divide-gray-200">
                {paginatedAlerts.map((alert) => (
                  <div key={alert.id} className="px-6 py-4 hover:bg-gray-50">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        className="mr-4"
                        checked={selectedAlerts.includes(alert.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedAlerts([...selectedAlerts, alert.id]);
                          } else {
                            setSelectedAlerts(selectedAlerts.filter(id => id !== alert.id));
                          }
                        }}
                      />
                      <div className="grid grid-cols-12 gap-4 w-full">
                        {/* Priorité */}
                        <div className="col-span-1">
                          <div className="flex items-center space-x-2">
                            {getPriorityIcon(alert.priority)}
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getPriorityColor(alert.priority)}`}>
                              {alert.priority}
                            </span>
                          </div>
                        </div>

                        {/* Type */}
                        <div className="col-span-1">
                          <div className="flex items-center space-x-2">
                            {getAlertTypeIcon(alert.alert_type)}
                            <span className="text-xs text-gray-600 capitalize">
                              {alert.alert_type.replace('_', ' ')}
                            </span>
                          </div>
                        </div>

                        {/* Titre */}
                        <div className="col-span-3">
                          <div>
                            <p className="text-sm font-medium text-gray-900 line-clamp-1">
                              {alert.title}
                            </p>
                            <p className="text-xs text-gray-500 line-clamp-2 mt-1">
                              {alert.message}
                            </p>
                          </div>
                        </div>

                        {/* Assigné à */}
                        <div className="col-span-2">
                          {alert.assignee ? (
                            <div className="flex items-center space-x-2">
                              <img
                                src={alert.assignee.profile_picture || '/default-avatar.png'}
                                alt={alert.assignee.username}
                                className="w-6 h-6 rounded-full object-cover"
                              />
                              <div>
                                <p className="text-sm font-medium text-gray-900">
                                  {alert.assignee.username}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {alert.assignee.admin_code}
                                </p>
                              </div>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-400">Non assigné</span>
                          )}
                        </div>

                        {/* Statut */}
                        <div className="col-span-2">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(alert.status)}
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(alert.status)}`}>
                              {getStatusLabel(alert.status)}
                            </span>
                          </div>
                        </div>

                        {/* Date */}
                        <div className="col-span-2">
                          <p className="text-xs text-gray-500">
                            {new Date(alert.created_at).toLocaleDateString('fr-FR')}
                          </p>
                          <p className="text-xs text-gray-400">
                            {new Date(alert.created_at).toLocaleTimeString('fr-FR', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </p>
                        </div>

                        {/* Actions */}
                        <div className="col-span-1">
                          <div className="flex items-center space-x-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setDetailAlert(alert);
                                setShowDetailModal(true);
                              }}
                            >
                              <Eye size={14} />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setActionAlert(alert);
                                setShowActionModal(true);
                              }}
                            >
                              <MoreVertical size={14} />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </CardBody>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Affichage de {startIndex + 1} à {Math.min(startIndex + itemsPerPage, filteredAlerts.length)} sur {filteredAlerts.length} alertes
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  Précédent
                </Button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                      >
                        {page}
                      </Button>
                    );
                  })}
                  {totalPages > 5 && (
                    <>
                      <span className="text-gray-500">...</span>
                      <Button
                        variant={currentPage === totalPages ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(totalPages)}
                      >
                        {totalPages}
                      </Button>
                    </>
                  )}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  Suivant
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Modals - À implémenter */}
      {showActionModal && actionAlert && (
        <AlertActionModal
          alert={actionAlert}
          onClose={() => {
            setShowActionModal(false);
            setActionAlert(null);
          }}
          onAction={handleAlertAction}
        />
      )}

      {showDetailModal && detailAlert && (
        <AlertDetailModal
          alert={detailAlert}
          onClose={() => {
            setShowDetailModal(false);
            setDetailAlert(null);
          }}
        />
      )}

      {showCreateModal && (
        <CreateAlertModal
          onClose={() => setShowCreateModal(false)}
          onCreated={() => {
            setShowCreateModal(false);
            loadAlerts();
          }}
        />
      )}

      {showBulkActionModal && (
        <BulkAlertActionModal
          selectedCount={selectedAlerts.length}
          onClose={() => setShowBulkActionModal(false)}
          onAction={handleBulkAction}
        />
      )}
    </div>
  );
};

// Modal d'actions sur les alertes
interface AlertActionModalProps {
  alert: ExtendedAdminAlert;
  onClose: () => void;
  onAction: (action: AlertAction) => void;
}

const AlertActionModal: React.FC<AlertActionModalProps> = ({ alert, onClose, onAction }) => {
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [showResolveModal, setShowResolveModal] = useState(false);
  const [showDismissModal, setShowDismissModal] = useState(false);
  const [showEscalateModal, setShowEscalateModal] = useState(false);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-lg">
        {/* Header avec informations de l'alerte */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            {getAlertTypeIcon(alert.alert_type)}
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Actions pour l'alerte</h3>
              <p className="text-sm text-gray-600">{alert.title}</p>
            </div>
          </div>
          <Button variant="outline" size="sm" onClick={onClose}>
            <XCircle size={16} />
          </Button>
        </div>

        {/* Statut actuel */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getStatusIcon(alert.status)}
              <span className="text-sm font-medium text-gray-700">Statut actuel:</span>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(alert.status)}`}>
                {getStatusLabel(alert.status)}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              {getPriorityIcon(alert.priority)}
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getPriorityColor(alert.priority)}`}>
                {alert.priority}
              </span>
            </div>
          </div>
        </div>

        {/* Actions disponibles */}
        <div className="space-y-3">
          {/* Prendre en charge / Assigner */}
          {(alert.status === 'open' || alert.status === 'in_progress') && (
            <div className="border border-yellow-200 rounded-lg p-4 bg-yellow-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-yellow-100 rounded-full">
                    <UserCheck size={16} className="text-yellow-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-yellow-900">
                      {alert.status === 'open' ? 'Prendre en charge' : 'Réassigner'}
                    </h4>
                    <p className="text-sm text-yellow-700">
                      {alert.status === 'open'
                        ? 'Assigner cette alerte à un administrateur'
                        : 'Changer l\'administrateur assigné'
                      }
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-yellow-300 text-yellow-700 hover:bg-yellow-100"
                  onClick={() => setShowAssignModal(true)}
                >
                  {alert.status === 'open' ? 'Assigner' : 'Réassigner'}
                </Button>
              </div>
            </div>
          )}

          {/* Marquer comme résolue */}
          {alert.status !== 'resolved' && (
            <div className="border border-green-200 rounded-lg p-4 bg-green-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-100 rounded-full">
                    <CheckCircle size={16} className="text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-green-900">Marquer comme résolue</h4>
                    <p className="text-sm text-green-700">
                      Fermer cette alerte avec des notes de résolution
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-green-300 text-green-700 hover:bg-green-100"
                  onClick={() => setShowResolveModal(true)}
                >
                  Résoudre
                </Button>
              </div>
            </div>
          )}

          {/* Escalader la priorité */}
          {alert.priority !== AlertPriority.CRITICAL && (
            <div className="border border-orange-200 rounded-lg p-4 bg-orange-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-orange-100 rounded-full">
                    <TrendingUp size={16} className="text-orange-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-orange-900">Escalader la priorité</h4>
                    <p className="text-sm text-orange-700">
                      Augmenter l'importance de cette alerte
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-orange-300 text-orange-700 hover:bg-orange-100"
                  onClick={() => setShowEscalateModal(true)}
                >
                  Escalader
                </Button>
              </div>
            </div>
          )}

          {/* Ignorer l'alerte */}
          {alert.status !== 'dismissed' && (
            <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gray-100 rounded-full">
                    <XCircle size={16} className="text-gray-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Ignorer l'alerte</h4>
                    <p className="text-sm text-gray-700">
                      Marquer cette alerte comme non pertinente
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-gray-300 text-gray-700 hover:bg-gray-100"
                  onClick={() => setShowDismissModal(true)}
                >
                  Ignorer
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Informations de l'alerte */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4 text-xs text-gray-500">
            <div>
              <strong>Type:</strong> {alert.alert_type.replace('_', ' ')}
            </div>
            <div>
              <strong>Créée le:</strong> {new Date(alert.created_at).toLocaleDateString('fr-FR')}
            </div>
            <div>
              <strong>Priorité:</strong> {alert.priority}
            </div>
            <div>
              <strong>Statut:</strong> {alert.status}
            </div>
            {alert.assignee && (
              <div className="col-span-2">
                <strong>Assignée à:</strong> {alert.assignee.username || alert.assignee.admin_code || 'Admin'}
              </div>
            )}
          </div>
        </div>

        {/* Modals d'actions spécifiques */}
        {showAssignModal && (
          <AssignAlertModal
            alert={alert}
            onClose={() => setShowAssignModal(false)}
            onAssign={(assigneeId, reason) => {
              onAction({
                alertId: alert.id,
                action: 'assign',
                assigneeId,
                reason
              });
              setShowAssignModal(false);
              onClose();
            }}
          />
        )}

        {showResolveModal && (
          <ResolveAlertModal
            alert={alert}
            onClose={() => setShowResolveModal(false)}
            onResolve={(notes, reason) => {
              onAction({
                alertId: alert.id,
                action: 'resolve',
                resolutionNotes: notes,
                reason
              });
              setShowResolveModal(false);
              onClose();
            }}
          />
        )}

        {showDismissModal && (
          <DismissAlertModal
            alert={alert}
            onClose={() => setShowDismissModal(false)}
            onDismiss={(reason) => {
              onAction({
                alertId: alert.id,
                action: 'dismiss',
                reason
              });
              setShowDismissModal(false);
              onClose();
            }}
          />
        )}

        {showEscalateModal && (
          <EscalateAlertModal
            alert={alert}
            onClose={() => setShowEscalateModal(false)}
            onEscalate={(newPriority, reason) => {
              onAction({
                alertId: alert.id,
                action: 'escalate',
                newPriority,
                reason
              });
              setShowEscalateModal(false);
              onClose();
            }}
          />
        )}
      </div>
    </div>
  );
};

// Modal de détails d'alerte
interface AlertDetailModalProps {
  alert: ExtendedAdminAlert;
  onClose: () => void;
}

const AlertDetailModal: React.FC<AlertDetailModalProps> = ({ alert, onClose }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            {getAlertTypeIcon(alert.alert_type)}
            <div>
              <h2 className="text-xl font-bold text-gray-900">{alert.title}</h2>
              <p className="text-gray-600">ID: {alert.id}</p>
            </div>
          </div>
          <Button variant="outline" onClick={onClose}>
            <XCircle size={20} />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Message principal */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Message</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-gray-900">{alert.message}</p>
            </div>
          </div>

          {/* Détails techniques */}
          {alert.details && Object.keys(alert.details).length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Détails techniques</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                  {JSON.stringify(alert.details, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Informations de gestion */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Priorité et Type</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  {getPriorityIcon(alert.priority)}
                  <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full border ${getPriorityColor(alert.priority)}`}>
                    Priorité {alert.priority}
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  {getAlertTypeIcon(alert.alert_type)}
                  <span className="text-sm text-gray-700 capitalize">
                    Type: {alert.alert_type.replace('_', ' ')}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Statut</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(alert.status)}
                  <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(alert.status)}`}>
                    {alert.status}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Assignation */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Assignation</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              {alert.assignee ? (
                <div className="flex items-center space-x-4">
                  <img
                    src={alert.assignee.profile_picture || '/default-avatar.png'}
                    alt={alert.assignee.username}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div>
                    <p className="font-medium text-gray-900">{alert.assignee.username}</p>
                    <p className="text-sm text-gray-600">{alert.assignee.admin_code}</p>
                    <p className="text-sm text-gray-500">{alert.assignee.department}</p>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500">Alerte non assignée</p>
              )}
            </div>
          </div>

          {/* Résolution */}
          {alert.resolution_notes && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Notes de résolution</h3>
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <p className="text-green-800">{alert.resolution_notes}</p>
              </div>
            </div>
          )}

          {/* Historique */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Historique</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span className="text-gray-600">Créée le:</span>
                <span className="font-medium text-gray-900">{formatDate(alert.created_at)}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <Clock className="w-4 h-4 text-gray-400" />
                <span className="text-gray-600">Dernière mise à jour:</span>
                <span className="font-medium text-gray-900">{formatDate(alert.updated_at)}</span>
              </div>
              {alert.resolved_at && (
                <div className="flex items-center space-x-2 text-sm">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-gray-600">Résolue le:</span>
                  <span className="font-medium text-gray-900">{formatDate(alert.resolved_at)}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <Button onClick={onClose}>
            Fermer
          </Button>
        </div>
      </div>
    </div>
  );
};

// Modal de création d'alerte
interface CreateAlertModalProps {
  onClose: () => void;
  onCreated: () => void;
}

const CreateAlertModal: React.FC<CreateAlertModalProps> = ({ onClose, onCreated }) => {
  const [formData, setFormData] = useState({
    alert_type: 'system',
    priority: AlertPriority.MEDIUM,
    title: '',
    message: '',
    details: '{}'
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const { error } = await supabase
        .from('admin_alerts')
        .insert([{
          ...formData,
          details: JSON.parse(formData.details || '{}'),
          status: 'open',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }]);

      if (error) {
        console.error('Erreur lors de la création:', error);
        return;
      }

      onCreated();
    } catch (error) {
      console.error('Erreur:', error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-2xl">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Créer une nouvelle alerte</h2>
          <Button variant="outline" onClick={onClose}>
            <XCircle size={20} />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Type d'alerte</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                value={formData.alert_type}
                onChange={(e) => setFormData({...formData, alert_type: e.target.value})}
                required
              >
                <option value="system">Système</option>
                <option value="security">Sécurité</option>
                <option value="database">Base de données</option>
                <option value="user_action">Action utilisateur</option>
                <option value="business">Entreprise</option>
                <option value="content">Contenu</option>
                <option value="performance">Performance</option>
                <option value="deployment">Déploiement</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Priorité</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                value={formData.priority}
                onChange={(e) => setFormData({...formData, priority: e.target.value as AlertPriority})}
                required
              >
                <option value={AlertPriority.LOW}>Basse</option>
                <option value={AlertPriority.MEDIUM}>Moyenne</option>
                <option value={AlertPriority.HIGH}>Haute</option>
                <option value={AlertPriority.CRITICAL}>Critique</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Titre</label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              value={formData.title}
              onChange={(e) => setFormData({...formData, title: e.target.value})}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
            <textarea
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              rows={4}
              value={formData.message}
              onChange={(e) => setFormData({...formData, message: e.target.value})}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Détails (JSON)</label>
            <textarea
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 font-mono text-sm"
              rows={3}
              value={formData.details}
              onChange={(e) => setFormData({...formData, details: e.target.value})}
              placeholder='{"key": "value"}'
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Annuler
            </Button>
            <Button type="submit">
              Créer l'alerte
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Modal d'actions en lot
interface BulkAlertActionModalProps {
  selectedCount: number;
  onClose: () => void;
  onAction: (action: string) => void;
}

const BulkAlertActionModal: React.FC<BulkAlertActionModalProps> = ({ selectedCount, onClose, onAction }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Actions en lot ({selectedCount} alertes)</h3>

        <div className="space-y-2">
          <Button
            variant="outline"
            className="w-full justify-start text-green-600 hover:text-green-700"
            onClick={() => onAction('resolve')}
          >
            <CheckCircle size={16} className="mr-2" />
            Marquer comme résolues
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start text-gray-600 hover:text-gray-700"
            onClick={() => onAction('dismiss')}
          >
            <XCircle size={16} className="mr-2" />
            Ignorer toutes les alertes
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start text-yellow-600 hover:text-yellow-700"
            onClick={() => onAction('assign')}
          >
            <UserCheck size={16} className="mr-2" />
            Assigner à un administrateur
          </Button>
        </div>

        <div className="flex justify-end space-x-2 mt-6">
          <Button variant="outline" onClick={onClose}>
            Annuler
          </Button>
        </div>
      </div>
    </div>
  );
};

// Modal d'assignation d'alerte
interface AssignAlertModalProps {
  alert: ExtendedAdminAlert;
  onClose: () => void;
  onAssign: (assigneeId: string, reason: string) => void;
}

const AssignAlertModal: React.FC<AssignAlertModalProps> = ({ alert, onClose, onAssign }) => {
  const [selectedAdmin, setSelectedAdmin] = useState('');
  const [reason, setReason] = useState('');
  const [admins, setAdmins] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAdmins();
  }, []);

  const loadAdmins = async () => {
    try {
      const { data, error } = await supabase
        .from('admin_profiles')
        .select(`
          id,
          admin_code,
          department,
          is_active,
          profiles!admin_profiles_user_id_fkey(
            username,
            email,
            profile_picture
          )
        `)
        .eq('is_active', true);

      if (error) {
        console.error('Erreur lors du chargement des admins:', error);
        return;
      }

      setAdmins(data || []);
    } catch (error) {
      console.error('Erreur:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedAdmin && reason.trim()) {
      onAssign(selectedAdmin, reason);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Assigner l'alerte</h3>
          <Button variant="outline" size="sm" onClick={onClose}>
            <XCircle size={16} />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Administrateur
            </label>
            {loading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              </div>
            ) : (
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                value={selectedAdmin}
                onChange={(e) => setSelectedAdmin(e.target.value)}
                required
              >
                <option value="">Sélectionner un administrateur</option>
                {admins.map((admin) => (
                  <option key={admin.id} value={admin.id}>
                    {admin.profiles?.username || admin.admin_code} - {admin.department}
                  </option>
                ))}
              </select>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Raison de l'assignation
            </label>
            <textarea
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              rows={3}
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Expliquez pourquoi vous assignez cette alerte à cet administrateur..."
              required
            />
          </div>

          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="flex items-start space-x-2">
              <Info size={16} className="text-blue-500 mt-0.5" />
              <div className="text-sm text-blue-700">
                <strong>Alerte:</strong> {alert.title}<br/>
                <strong>Type:</strong> {alert.alert_type}<br/>
                <strong>Priorité:</strong> {alert.priority}
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Annuler
            </Button>
            <Button type="submit" disabled={!selectedAdmin || !reason.trim()}>
              Assigner
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Modal de résolution d'alerte
interface ResolveAlertModalProps {
  alert: ExtendedAdminAlert;
  onClose: () => void;
  onResolve: (notes: string, reason: string) => void;
}

const ResolveAlertModal: React.FC<ResolveAlertModalProps> = ({ alert, onClose, onResolve }) => {
  const [notes, setNotes] = useState('');
  const [reason, setReason] = useState('');
  const [actionsTaken, setActionsTaken] = useState<string[]>([]);

  const commonActions = [
    'Problème résolu automatiquement',
    'Intervention manuelle effectuée',
    'Configuration mise à jour',
    'Ressources ajoutées',
    'Processus redémarré',
    'Maintenance préventive',
    'Formation utilisateur',
    'Politique mise à jour'
  ];

  const handleActionToggle = (action: string) => {
    setActionsTaken(prev =>
      prev.includes(action)
        ? prev.filter(a => a !== action)
        : [...prev, action]
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (notes.trim() && reason.trim()) {
      const fullNotes = `${notes}\n\nActions prises: ${actionsTaken.join(', ')}`;
      onResolve(fullNotes, reason);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div className="bg-white rounded-lg p-6 w-full max-w-lg">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Résoudre l'alerte</h3>
          <Button variant="outline" size="sm" onClick={onClose}>
            <XCircle size={16} />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Actions prises (optionnel)
            </label>
            <div className="grid grid-cols-2 gap-2">
              {commonActions.map((action) => (
                <label key={action} className="flex items-center space-x-2 text-sm">
                  <input
                    type="checkbox"
                    checked={actionsTaken.includes(action)}
                    onChange={() => handleActionToggle(action)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-gray-700">{action}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes de résolution *
            </label>
            <textarea
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              rows={4}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Décrivez comment l'alerte a été résolue, les étapes prises, et toute information pertinente..."
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Résumé de la résolution *
            </label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Résumé court de la résolution..."
              required
            />
          </div>

          <div className="bg-green-50 p-3 rounded-lg">
            <div className="flex items-start space-x-2">
              <CheckCircle size={16} className="text-green-500 mt-0.5" />
              <div className="text-sm text-green-700">
                <strong>Alerte à résoudre:</strong> {alert.title}<br/>
                <strong>Créée le:</strong> {new Date(alert.created_at).toLocaleDateString('fr-FR')}<br/>
                <strong>Priorité:</strong> {alert.priority}
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Annuler
            </Button>
            <Button type="submit" disabled={!notes.trim() || !reason.trim()}>
              Marquer comme résolue
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Modal pour ignorer une alerte
interface DismissAlertModalProps {
  alert: ExtendedAdminAlert;
  onClose: () => void;
  onDismiss: (reason: string) => void;
}

const DismissAlertModal: React.FC<DismissAlertModalProps> = ({ alert, onClose, onDismiss }) => {
  const [reason, setReason] = useState('');
  const [selectedReason, setSelectedReason] = useState('');

  const commonReasons = [
    'Fausse alerte',
    'Problème déjà résolu',
    'Alerte en double',
    'Non pertinent pour notre système',
    'Maintenance programmée',
    'Comportement normal',
    'Alerte obsolète',
    'Autre (préciser ci-dessous)'
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const finalReason = selectedReason === 'Autre (préciser ci-dessous)' ? reason : selectedReason;
    if (finalReason.trim()) {
      onDismiss(finalReason);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Ignorer l'alerte</h3>
          <Button variant="outline" size="sm" onClick={onClose}>
            <XCircle size={16} />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Raison de l'ignorance *
            </label>
            <div className="space-y-2">
              {commonReasons.map((reasonOption) => (
                <label key={reasonOption} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="dismissReason"
                    value={reasonOption}
                    checked={selectedReason === reasonOption}
                    onChange={(e) => setSelectedReason(e.target.value)}
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{reasonOption}</span>
                </label>
              ))}
            </div>
          </div>

          {selectedReason === 'Autre (préciser ci-dessous)' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Précisez la raison
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                rows={3}
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Expliquez pourquoi cette alerte doit être ignorée..."
                required
              />
            </div>
          )}

          <div className="bg-yellow-50 p-3 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertTriangle size={16} className="text-yellow-500 mt-0.5" />
              <div className="text-sm text-yellow-700">
                <strong>Attention:</strong> Ignorer une alerte la marquera comme non pertinente.
                Cette action peut être annulée plus tard si nécessaire.
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm text-gray-700">
              <strong>Alerte à ignorer:</strong> {alert.title}<br/>
              <strong>Type:</strong> {alert.alert_type}<br/>
              <strong>Priorité:</strong> {alert.priority}
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={!selectedReason || (selectedReason === 'Autre (préciser ci-dessous)' && !reason.trim())}
              className="bg-gray-600 hover:bg-gray-700"
            >
              Ignorer l'alerte
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Modal pour escalader une alerte
interface EscalateAlertModalProps {
  alert: ExtendedAdminAlert;
  onClose: () => void;
  onEscalate: (newPriority: AlertPriority, reason: string) => void;
}

const EscalateAlertModal: React.FC<EscalateAlertModalProps> = ({ alert, onClose, onEscalate }) => {
  const [newPriority, setNewPriority] = useState<AlertPriority>(AlertPriority.HIGH);
  const [reason, setReason] = useState('');
  const [impact, setImpact] = useState('');
  const [urgency, setUrgency] = useState('');

  const priorities = [
    { value: AlertPriority.MEDIUM, label: 'Moyenne', color: 'text-yellow-600', disabled: alert.priority === AlertPriority.CRITICAL || alert.priority === AlertPriority.HIGH },
    { value: AlertPriority.HIGH, label: 'Haute', color: 'text-orange-600', disabled: alert.priority === AlertPriority.CRITICAL },
    { value: AlertPriority.CRITICAL, label: 'Critique', color: 'text-red-600', disabled: false }
  ];

  const impactOptions = [
    'Affecte plusieurs utilisateurs',
    'Impact sur les performances',
    'Risque de sécurité',
    'Perte de données potentielle',
    'Service indisponible',
    'Impact financier',
    'Réputation de l\'entreprise'
  ];

  const urgencyOptions = [
    'Nécessite une action immédiate',
    'Problème s\'aggrave',
    'Deadline critique',
    'Escalade client',
    'Médias impliqués',
    'Conformité réglementaire'
  ];

  useEffect(() => {
    // Définir la priorité par défaut basée sur la priorité actuelle
    if (alert.priority === AlertPriority.LOW) {
      setNewPriority(AlertPriority.MEDIUM);
    } else if (alert.priority === AlertPriority.MEDIUM) {
      setNewPriority(AlertPriority.HIGH);
    } else if (alert.priority === AlertPriority.HIGH) {
      setNewPriority(AlertPriority.CRITICAL);
    }
  }, [alert.priority]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (reason.trim()) {
      const fullReason = `${reason}\nImpact: ${impact}\nUrgence: ${urgency}`;
      onEscalate(newPriority, fullReason);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div className="bg-white rounded-lg p-6 w-full max-w-lg">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Escalader la priorité</h3>
          <Button variant="outline" size="sm" onClick={onClose}>
            <XCircle size={16} />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Priorité actuelle vs nouvelle */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-1">Priorité actuelle</p>
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full border ${getPriorityColor(alert.priority)}`}>
                  {alert.priority}
                </span>
              </div>
              <TrendingUp size={24} className="text-orange-500" />
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-1">Nouvelle priorité</p>
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full border ${getPriorityColor(newPriority)}`}>
                  {newPriority}
                </span>
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nouvelle priorité *
            </label>
            <div className="space-y-2">
              {priorities.map((priority) => (
                <label key={priority.value} className={`flex items-center space-x-2 ${priority.disabled ? 'opacity-50' : ''}`}>
                  <input
                    type="radio"
                    name="newPriority"
                    value={priority.value}
                    checked={newPriority === priority.value}
                    onChange={(e) => setNewPriority(e.target.value as AlertPriority)}
                    disabled={priority.disabled}
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <span className={`text-sm font-medium ${priority.color}`}>{priority.label}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Impact (optionnel)
            </label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              value={impact}
              onChange={(e) => setImpact(e.target.value)}
            >
              <option value="">Sélectionner l'impact...</option>
              {impactOptions.map((option) => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Urgence (optionnel)
            </label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              value={urgency}
              onChange={(e) => setUrgency(e.target.value)}
            >
              <option value="">Sélectionner l'urgence...</option>
              {urgencyOptions.map((option) => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Justification de l'escalade *
            </label>
            <textarea
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              rows={3}
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Expliquez pourquoi cette alerte nécessite une priorité plus élevée..."
              required
            />
          </div>

          <div className="bg-orange-50 p-3 rounded-lg">
            <div className="flex items-start space-x-2">
              <TrendingUp size={16} className="text-orange-500 mt-0.5" />
              <div className="text-sm text-orange-700">
                <strong>Note:</strong> L'escalade d'une alerte notifiera automatiquement
                les administrateurs de niveau supérieur et déclenchera des procédures d'urgence si applicable.
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={!reason.trim()}
              className="bg-orange-600 hover:bg-orange-700"
            >
              Escalader
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SystemAlerts;
