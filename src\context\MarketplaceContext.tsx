import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { Database } from '../lib/database.types';
import { IProduct, INegotiation, NegotiationStatus, IOrder, OrderStatus } from '../types';
import { OrderTrackingService } from '../services/orderTrackingService';

type Product = Database['public']['Tables']['products']['Row'];
type Negotiation = Database['public']['Tables']['negotiations']['Row'];
type NegotiationMessage = Database['public']['Tables']['negotiation_messages']['Row'];
type Order = Database['public']['Tables']['orders']['Row'];

interface MarketplaceContextType {
  products: Product[];
  negotiations: Negotiation[];
  orders: Order[];
  loading: boolean;
  error: string | null;
  addProduct: (product: Omit<Product, 'id' | 'created_at' | 'updated_at'>) => Promise<void>;
  updateProduct: (productId: string, updates: Partial<Product>) => Promise<void>;
  deleteProduct: (productId: string) => Promise<void>;
  startNegotiation: (productId: string, initialOffer: number, message?: string) => Promise<void>;
  respondToNegotiation: (negotiationId: string, accepted: boolean, counterOffer?: number, message?: string) => Promise<void>;
  createOrder: (productId: string, quantity: number, negotiationId?: string) => Promise<void>;
  updateOrderStatus: (orderId: string, status: OrderStatus) => Promise<void>;
}

const MarketplaceContext = createContext<MarketplaceContextType>({
  products: [],
  negotiations: [],
  orders: [],
  loading: true,
  error: null,
  addProduct: async () => {},
  updateProduct: async () => {},
  deleteProduct: async () => {},
  startNegotiation: async () => {},
  respondToNegotiation: async () => {},
  createOrder: async () => {},
  updateOrderStatus: async () => {},
});

export const useMarketplace = () => useContext(MarketplaceContext);

export const MarketplaceProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [negotiations, setNegotiations] = useState<Negotiation[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Load initial data and set up real-time subscriptions
    const loadInitialData = async () => {
      try {
        setLoading(true);

        // Fetch products
        const { data: productsData, error: productsError } = await supabase
          .from('products')
          .select('*')
          .order('created_at', { ascending: false });

        if (productsError) throw productsError;
        setProducts(productsData);

        // Fetch user's negotiations
        const { data: negotiationsData, error: negotiationsError } = await supabase
          .from('negotiations')
          .select('*')
          .order('updated_at', { ascending: false });

        if (negotiationsError) throw negotiationsError;
        setNegotiations(negotiationsData);

        // Fetch user's orders
        const { data: ordersData, error: ordersError } = await supabase
          .from('orders')
          .select('*')
          .order('created_at', { ascending: false });

        if (ordersError) throw ordersError;
        setOrders(ordersData);

      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    // Set up real-time subscriptions
    const subscribeToChanges = () => {
      // Products subscription
      const productsSubscription = supabase
        .channel('products_changes')
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'products'
        }, (payload) => {
          if (payload.eventType === 'INSERT') {
            setProducts(prev => [payload.new as Product, ...prev]);
          } else if (payload.eventType === 'UPDATE') {
            setProducts(prev => prev.map(product =>
              product.id === payload.new.id ? payload.new as Product : product
            ));
          } else if (payload.eventType === 'DELETE') {
            setProducts(prev => prev.filter(product =>
              product.id !== payload.old.id
            ));
          }
        })
        .subscribe();

      // Negotiations subscription
      const negotiationsSubscription = supabase
        .channel('negotiations_changes')
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'negotiations'
        }, (payload) => {
          if (payload.eventType === 'INSERT') {
            setNegotiations(prev => [payload.new as Negotiation, ...prev]);
          } else if (payload.eventType === 'UPDATE') {
            setNegotiations(prev => prev.map(negotiation =>
              negotiation.id === payload.new.id ? payload.new as Negotiation : negotiation
            ));
          }
        })
        .subscribe();

      // Orders subscription
      const ordersSubscription = supabase
        .channel('orders_changes')
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'orders'
        }, (payload) => {
          if (payload.eventType === 'INSERT') {
            setOrders(prev => [payload.new as Order, ...prev]);
          } else if (payload.eventType === 'UPDATE') {
            setOrders(prev => prev.map(order =>
              order.id === payload.new.id ? payload.new as Order : order
            ));
          }
        })
        .subscribe();

      return () => {
        productsSubscription.unsubscribe();
        negotiationsSubscription.unsubscribe();
        ordersSubscription.unsubscribe();
      };
    };

    loadInitialData();
    const unsubscribe = subscribeToChanges();

    return () => {
      unsubscribe();
    };
  }, []);

  const addProduct = async (product: Omit<Product, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { error } = await supabase
        .from('products')
        .insert([product]);

      if (error) throw error;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add product');
      throw err;
    }
  };

  const updateProduct = async (productId: string, updates: Partial<Product>) => {
    try {
      const { error } = await supabase
        .from('products')
        .update(updates)
        .eq('id', productId);

      if (error) throw error;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update product');
      throw err;
    }
  };

  const deleteProduct = async (productId: string) => {
    try {
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', productId);

      if (error) throw error;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete product');
      throw err;
    }
  };

  const startNegotiation = async (productId: string, initialOffer: number, message?: string) => {
    try {
      const { data: product, error: productError } = await supabase
        .from('products')
        .select('business_id, price')
        .eq('id', productId)
        .single();

      if (productError) throw productError;

      const { data: negotiation, error: negotiationError } = await supabase
        .from('negotiations')
        .insert([{
          product_id: productId,
          buyer_id: (await supabase.auth.getUser()).data.user?.id,
          seller_id: product.business_id,
          initial_price: product.price,
          current_offer: initialOffer,
          status: NegotiationStatus.PENDING
        }])
        .select()
        .single();

      if (negotiationError) throw negotiationError;

      if (message) {
        const { error: messageError } = await supabase
          .from('negotiation_messages')
          .insert([{
            negotiation_id: negotiation.id,
            sender_id: (await supabase.auth.getUser()).data.user?.id,
            content: message,
            offer: initialOffer
          }]);

        if (messageError) throw messageError;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start negotiation');
      throw err;
    }
  };

  const respondToNegotiation = async (negotiationId: string, accepted: boolean, counterOffer?: number, message?: string) => {
    try {
      const updates: Partial<Negotiation> = {
        status: accepted ? NegotiationStatus.ACCEPTED :
                counterOffer ? NegotiationStatus.COUNTER_OFFER :
                NegotiationStatus.REJECTED
      };

      if (counterOffer) {
        updates.current_offer = counterOffer;
      }

      const { error: negotiationError } = await supabase
        .from('negotiations')
        .update(updates)
        .eq('id', negotiationId);

      if (negotiationError) throw negotiationError;

      if (message || counterOffer) {
        const { error: messageError } = await supabase
          .from('negotiation_messages')
          .insert([{
            negotiation_id: negotiationId,
            sender_id: (await supabase.auth.getUser()).data.user?.id,
            content: message || '',
            offer: counterOffer
          }]);

        if (messageError) throw messageError;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to respond to negotiation');
      throw err;
    }
  };

  const createOrder = async (productId: string, quantity: number, negotiationId?: string) => {
    try {
      const { data: product, error: productError } = await supabase
        .from('products')
        .select('business_id, price, stock')
        .eq('id', productId)
        .single();

      if (productError) throw productError;

      let finalPrice = product.price * quantity;

      if (negotiationId) {
        const { data: negotiation, error: negotiationError } = await supabase
          .from('negotiations')
          .select('current_offer')
          .eq('id', negotiationId)
          .single();

        if (negotiationError) throw negotiationError;
        finalPrice = negotiation.current_offer * quantity;
      }

      const buyerId = (await supabase.auth.getUser()).data.user?.id;
      if (!buyerId) throw new Error('User not authenticated');

      // Créer la commande
      const { data: newOrder, error: orderError } = await supabase
        .from('orders')
        .insert([{
          product_id: productId,
          buyer_id: buyerId,
          seller_id: product.business_id,
          quantity,
          total_price: finalPrice,
          status: OrderStatus.PENDING,
          negotiation_id: negotiationId
        }])
        .select()
        .single();

      if (orderError) throw orderError;

      // Initialiser le système de suivi automatiquement
      try {
        await OrderTrackingService.initializeTracking(
          newOrder.id,
          buyerId,
          product.business_id
        );

        // Démarrer la simulation de progression (pour les tests)
        // Dans un environnement de production, ceci serait géré par des webhooks ou des processus métier
        setTimeout(() => {
          OrderTrackingService.simulateOrderProgression(newOrder.id);
        }, 1000);

      } catch (trackingError) {
        console.error('Erreur lors de l\'initialisation du suivi:', trackingError);
        // Ne pas faire échouer la commande si le suivi échoue
      }

      // Mettre à jour le stock du produit
      const { error: stockError } = await supabase
        .from('products')
        .update({ stock: product.stock - quantity })
        .eq('id', productId);

      if (stockError) throw stockError;

      // Marquer la négociation comme complétée si applicable
      if (negotiationId) {
        await supabase
          .from('negotiations')
          .update({ status: NegotiationStatus.COMPLETED })
          .eq('id', negotiationId);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create order');
      throw err;
    }
  };

  const updateOrderStatus = async (orderId: string, status: OrderStatus) => {
    try {
      const { error } = await supabase
        .from('orders')
        .update({ status })
        .eq('id', orderId);

      if (error) throw error;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update order status');
      throw err;
    }
  };

  return (
    <MarketplaceContext.Provider value={{
      products,
      negotiations,
      orders,
      loading,
      error,
      addProduct,
      updateProduct,
      deleteProduct,
      startNegotiation,
      respondToNegotiation,
      createOrder,
      updateOrderStatus,
    }}>
      {children}
    </MarketplaceContext.Provider>
  );
};