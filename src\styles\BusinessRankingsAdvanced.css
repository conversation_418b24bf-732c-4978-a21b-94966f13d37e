/* ===== SECTION RECOMMANDATIONS ===== */
.recommendations-section {
  margin-bottom: 40px;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-radius: 20px;
  padding: 32px;
  border: 2px solid #22c55e;
  box-shadow: 0 8px 32px rgba(34, 197, 94, 0.15);
}

.recommendations-header {
  text-align: center;
  margin-bottom: 32px;
}

.recommendations-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 1.75rem;
  font-weight: 700;
  color: #14532d;
  margin: 0 0 8px 0;
}

.recommendations-subtitle {
  font-size: 1rem;
  color: #16a34a;
  font-weight: 500;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
}

.recommendation-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.recommendation-card.priority {
  border: 2px solid #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.priority-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #ef4444;
  color: white;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendation-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.recommendation-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.recommendation-icon.success {
  background: #dcfce7;
  color: #16a34a;
}

.recommendation-icon.warning {
  background: #fef3c7;
  color: #ca8a04;
}

.recommendation-content strong {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 4px;
}

.recommendation-content p {
  font-size: 0.8rem;
  color: #64748b;
  line-height: 1.4;
  margin: 0 0 8px 0;
}

.recommendation-impact {
  font-size: 0.75rem;
  font-weight: 500;
}

.recommendation-impact .positive {
  color: #16a34a;
}

.recommendation-impact .negative {
  color: #dc2626;
}

/* Styles pour les graphiques sans données */
.chart-no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #64748b;
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.chart-no-data p:first-child {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.chart-no-data p:last-child {
  font-size: 0.875rem;
  margin: 0;
}

/* Opportunités */
.opportunity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.opportunity-item {
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.opportunity-category {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.opportunity-stats {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.stat {
  font-size: 0.75rem;
  padding: 2px 6px;
  background: #e0f2fe;
  color: #0369a1;
  border-radius: 4px;
}

.opportunity-btn {
  padding: 6px 12px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.opportunity-btn:hover {
  background: #2563eb;
}

/* Plan d'action */
.action-timeline {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.timeline-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  border-left: 4px solid;
}

.timeline-item.completed {
  background: #f0fdf4;
  border-left-color: #22c55e;
}

.timeline-item.current {
  background: #fef3c7;
  border-left-color: #f59e0b;
}

.timeline-item.upcoming {
  background: #f8fafc;
  border-left-color: #e5e7eb;
}

.timeline-date {
  font-size: 0.75rem;
  font-weight: 600;
  color: #64748b;
  min-width: 70px;
}

.timeline-content strong {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 4px;
}

.timeline-content p {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0 0 8px 0;
}

.timeline-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.75rem;
  color: #64748b;
}

.timeline-progress .progress-bar {
  width: 60px;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.timeline-progress .progress-bar div {
  height: 100%;
  background: #f59e0b;
  border-radius: 2px;
}

/* ===== SECTION GAMIFICATION ===== */
.gamification-section {
  margin-bottom: 40px;
  background: linear-gradient(135deg, #fef7cd 0%, #fef3c7 100%);
  border-radius: 20px;
  padding: 32px;
  border: 2px solid #f59e0b;
  box-shadow: 0 8px 32px rgba(245, 158, 11, 0.15);
}

.gamification-header {
  text-align: center;
  margin-bottom: 32px;
}

.gamification-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 1.75rem;
  font-weight: 700;
  color: #92400e;
  margin: 0 0 8px 0;
}

.gamification-subtitle {
  font-size: 1rem;
  color: #d97706;
  font-weight: 500;
}

.gamification-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
}

/* Score de performance */
.performance-score {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.score-header {
  text-align: center;
  margin-bottom: 20px;
}

.score-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.score-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.score-number {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.score-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.score-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.score-category {
  font-size: 0.875rem;
  color: #64748b;
}

.score-points {
  font-size: 0.875rem;
  font-weight: 600;
  color: #16a34a;
}

.next-level {
  text-align: center;
}

.level-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
}

.level-progress .progress-bar {
  flex: 1;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
  border-radius: 4px;
  transition: width 0.8s ease;
}

.next-level p {
  font-size: 0.75rem;
  color: #64748b;
  margin: 0;
}

/* Badges */
.badges-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.badges-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.badges-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.badges-count {
  font-size: 0.875rem;
  color: #64748b;
  background: #f3f4f6;
  padding: 2px 8px;
  border-radius: 12px;
}

.badges-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.badge-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.badge-item.earned {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
}

.badge-item.locked {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  opacity: 0.7;
}

.badge-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.badge-info strong {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 2px;
}

.badge-info p {
  font-size: 0.75rem;
  color: #64748b;
  margin: 0;
}

.badge-progress {
  font-size: 0.75rem;
  color: #f59e0b;
  font-weight: 600;
  margin-top: 4px;
}

/* Objectifs */
.objectives-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.objectives-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.objectives-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.objectives-period {
  font-size: 0.875rem;
  color: #64748b;
  background: #f3f4f6;
  padding: 2px 8px;
  border-radius: 12px;
}

.objectives-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.objective-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  border-left: 4px solid;
}

.objective-item.completed {
  background: #f0fdf4;
  border-left-color: #22c55e;
}

.objective-item.in-progress {
  background: #fef3c7;
  border-left-color: #f59e0b;
}

.objective-item.pending {
  background: #f8fafc;
  border-left-color: #e5e7eb;
}

.objective-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.objective-item.completed .objective-icon {
  background: #dcfce7;
  color: #16a34a;
}

.objective-item.in-progress .objective-icon {
  background: #fef3c7;
  color: #ca8a04;
}

.objective-item.pending .objective-icon {
  background: #f3f4f6;
  color: #6b7280;
}

.objective-content {
  flex: 1;
}

.objective-content strong {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 8px;
}

.objective-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.objective-progress .progress-bar {
  flex: 1;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.objective-progress span {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
  min-width: 40px;
}

.objective-reward {
  font-size: 0.75rem;
  font-weight: 600;
  color: #f59e0b;
  background: #fef3c7;
  padding: 4px 8px;
  border-radius: 6px;
  align-self: flex-start;
}

/* ===== SECTION NOTIFICATIONS ===== */
.notifications-section {
  margin-bottom: 40px;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-radius: 20px;
  padding: 32px;
  border: 2px solid #ef4444;
  box-shadow: 0 8px 32px rgba(239, 68, 68, 0.15);
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.notifications-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.75rem;
  font-weight: 700;
  color: #991b1b;
  margin: 0;
}

.notification-settings-btn {
  padding: 8px 16px;
  background: white;
  border: 1px solid #ef4444;
  border-radius: 8px;
  color: #ef4444;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-settings-btn:hover {
  background: #ef4444;
  color: white;
}

.notifications-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
}

.notifications-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

/* Alertes récentes */
.alerts-count {
  font-size: 0.875rem;
  color: white;
  background: #ef4444;
  padding: 2px 8px;
  border-radius: 12px;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.alert-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  border-left: 4px solid;
}

.alert-item.success {
  background: #f0fdf4;
  border-left-color: #22c55e;
}

.alert-item.warning {
  background: #fefce8;
  border-left-color: #eab308;
}

.alert-item.info {
  background: #eff6ff;
  border-left-color: #3b82f6;
}

.alert-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.alert-item.success .alert-icon {
  background: #dcfce7;
  color: #16a34a;
}

.alert-item.warning .alert-icon {
  background: #fef3c7;
  color: #ca8a04;
}

.alert-item.info .alert-icon {
  background: #dbeafe;
  color: #2563eb;
}

.alert-content strong {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 4px;
}

.alert-content p {
  font-size: 0.8rem;
  color: #64748b;
  line-height: 1.4;
  margin: 0 0 8px 0;
}

.alert-time {
  font-size: 0.75rem;
  color: #9ca3af;
  font-style: italic;
}

/* Accomplissements */
.achievements-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.achievement-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 12px;
  border: 1px solid #f59e0b;
}

.achievement-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.achievement-content strong {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 4px;
}

.achievement-content p {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0 0 8px 0;
}

.achievement-reward {
  font-size: 0.75rem;
  color: #f59e0b;
  font-weight: 600;
}

/* Résumé hebdomadaire */
.summary-period {
  font-size: 0.875rem;
  color: #64748b;
  background: #f3f4f6;
  padding: 2px 8px;
  border-radius: 12px;
}

.summary-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.summary-stat {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon.positive {
  background: #dcfce7;
  color: #16a34a;
}

.stat-icon.neutral {
  background: #f3f4f6;
  color: #6b7280;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
}

.stat-label {
  font-size: 0.75rem;
  color: #64748b;
}

.summary-actions {
  display: flex;
  gap: 8px;
}

.summary-btn {
  flex: 1;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.summary-btn:not(.secondary) {
  background: #3b82f6;
  color: white;
  border: 1px solid #3b82f6;
}

.summary-btn.secondary {
  background: white;
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

.summary-btn:hover {
  transform: translateY(-1px);
}

/* ===== MODALS ===== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease;
}

.modal-content.large {
  max-width: 800px;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 0 24px 24px 24px;
}

.modal-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 24px;
  border-top: 1px solid #e5e7eb;
}

/* Styles pour les paramètres de notification */
.settings-section {
  margin-bottom: 24px;
}

.settings-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.setting-item {
  margin-bottom: 12px;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
}

.setting-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.setting-item select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
}

/* Styles pour le rapport */
.report-summary {
  margin-bottom: 24px;
}

.report-summary h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.report-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.report-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.report-stat .stat-label {
  font-size: 0.75rem;
  color: #64748b;
  margin-bottom: 4px;
}

.report-stat .stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.report-section {
  margin-bottom: 24px;
}

.report-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.report-products {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.report-product {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  gap: 12px;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.product-rank {
  font-weight: 700;
  color: #3b82f6;
  font-size: 0.875rem;
}

.product-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.product-category {
  font-size: 0.75rem;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.product-rating {
  font-size: 0.875rem;
  color: #f59e0b;
  font-weight: 600;
}

.report-recommendations {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.report-recommendation {
  padding: 12px;
  background: #f0fdf4;
  border-radius: 8px;
  border-left: 4px solid #22c55e;
}

.report-recommendation strong {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 4px;
}

.report-recommendation p {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0;
}

/* Styles pour le formulaire email */
.email-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.form-group input,
.form-group select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  font-weight: 400;
  color: #374151;
  cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

/* Boutons des modals */
.btn-primary {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn-secondary {
  padding: 10px 20px;
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* Responsive */
@media (max-width: 1200px) {
  .analytics-grid,
  .recommendations-grid,
  .gamification-grid,
  .notifications-grid {
    grid-template-columns: 1fr;
  }

  .analytics-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .analytics-grid,
  .recommendations-grid,
  .gamification-grid,
  .notifications-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 16px;
  }

  .report-stats {
    grid-template-columns: 1fr 1fr;
  }

  .report-product {
    grid-template-columns: 1fr;
    gap: 8px;
    text-align: center;
  }
}
