-- DÉPLOIEMENT COMPLET DU SYSTÈME D'ALERTES
-- Exécutez ce script dans Supabase SQL Editor

-- =====================================================
-- 1. VÉRIFIER ET CRÉER LA TABLE ADMIN_ALERTS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 Vérification de la table admin_alerts...';
    
    -- Créer la table admin_alerts si elle n'existe pas
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_alerts') THEN
        CREATE TABLE admin_alerts (
            id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
            alert_type text NOT NULL,
            priority text NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'critical')),
            title text NOT NULL,
            message text NOT NULL,
            details jsonb DEFAULT '{}'::jsonb,
            assigned_to uuid REFERENCES admin_profiles(id),
            created_by uuid REFERENCES admin_profiles(id),
            status text NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'dismissed')),
            resolution_notes text,
            created_at timestamp with time zone DEFAULT now(),
            updated_at timestamp with time zone DEFAULT now(),
            resolved_at timestamp with time zone
        );
        
        -- Créer les index pour les performances
        CREATE INDEX idx_admin_alerts_status ON admin_alerts(status);
        CREATE INDEX idx_admin_alerts_priority ON admin_alerts(priority);
        CREATE INDEX idx_admin_alerts_type ON admin_alerts(alert_type);
        CREATE INDEX idx_admin_alerts_assigned_to ON admin_alerts(assigned_to);
        CREATE INDEX idx_admin_alerts_created_at ON admin_alerts(created_at);
        
        RAISE NOTICE '✅ Table admin_alerts créée avec succès';
    ELSE
        RAISE NOTICE '✅ Table admin_alerts existe déjà';
    END IF;
    
    -- Vérifier et ajouter les colonnes manquantes si nécessaire
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'admin_alerts' AND column_name = 'details'
    ) THEN
        ALTER TABLE admin_alerts ADD COLUMN details jsonb DEFAULT '{}'::jsonb;
        RAISE NOTICE '✅ Colonne details ajoutée';
    END IF;
    
END $$;

-- =====================================================
-- 2. CRÉER DES ALERTES DE TEST VARIÉES
-- =====================================================

DO $$
DECLARE
    admin_profile_id uuid;
    alert_count integer;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🧪 Création d''alertes de test...';
    
    -- Récupérer un profil admin existant
    SELECT id INTO admin_profile_id FROM admin_profiles LIMIT 1;
    
    -- Compter les alertes existantes
    SELECT COUNT(*) INTO alert_count FROM admin_alerts;
    
    IF alert_count = 0 THEN
        -- Créer des alertes de test avec différents types et priorités
        INSERT INTO admin_alerts (
            alert_type, priority, title, message, details, created_by, status, created_at
        ) VALUES 
        (
            'security',
            'critical',
            'Tentative d''intrusion détectée',
            'Plusieurs tentatives de connexion échouées détectées depuis l''adresse IP *************. Action immédiate requise.',
            '{"ip_address": "*************", "attempts": 15, "last_attempt": "2024-01-15T10:30:00Z", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"}'::jsonb,
            admin_profile_id,
            'open',
            NOW() - INTERVAL '2 hours'
        ),
        (
            'system',
            'high',
            'Espace disque faible sur le serveur',
            'L''espace disque disponible sur le serveur principal est inférieur à 10%. Nettoyage ou extension requise.',
            '{"server": "prod-server-01", "disk_usage": "92%", "available_space": "8.2GB", "threshold": "10%"}'::jsonb,
            admin_profile_id,
            'open',
            NOW() - INTERVAL '4 hours'
        ),
        (
            'database',
            'medium',
            'Performance de base de données dégradée',
            'Les requêtes de base de données prennent plus de temps que d''habitude. Optimisation recommandée.',
            '{"avg_query_time": "2.5s", "normal_time": "0.8s", "affected_tables": ["posts", "users", "business_profiles"]}'::jsonb,
            admin_profile_id,
            'in_progress',
            NOW() - INTERVAL '6 hours'
        ),
        (
            'user_action',
            'medium',
            'Pic d''inscriptions d''utilisateurs',
            'Augmentation inhabituelle du nombre d''inscriptions (+300% par rapport à la normale). Vérification recommandée.',
            '{"registrations_today": 450, "normal_average": 150, "increase_percentage": 300}'::jsonb,
            admin_profile_id,
            'open',
            NOW() - INTERVAL '1 hour'
        ),
        (
            'business',
            'low',
            'Nouvelle demande de vérification entreprise',
            'Une nouvelle entreprise a soumis une demande de vérification avec tous les documents requis.',
            '{"business_name": "TechCorp Solutions", "business_id": "b123e4567-e89b-12d3-a456-426614174000", "documents_count": 3}'::jsonb,
            admin_profile_id,
            'open',
            NOW() - INTERVAL '30 minutes'
        ),
        (
            'content',
            'high',
            'Contenu signalé en masse',
            'Un post a été signalé par plus de 10 utilisateurs pour contenu inapproprié. Modération urgente requise.',
            '{"post_id": "p123e4567-e89b-12d3-a456-426614174000", "reports_count": 12, "report_reasons": ["spam", "inappropriate", "harassment"]}'::jsonb,
            admin_profile_id,
            'open',
            NOW() - INTERVAL '15 minutes'
        ),
        (
            'performance',
            'medium',
            'Temps de réponse API élevé',
            'Les API de l''application répondent plus lentement que d''habitude. Impact sur l''expérience utilisateur.',
            '{"avg_response_time": "3.2s", "normal_time": "1.1s", "affected_endpoints": ["/api/posts", "/api/users"]}'::jsonb,
            admin_profile_id,
            'open',
            NOW() - INTERVAL '45 minutes'
        ),
        (
            'deployment',
            'low',
            'Mise à jour système programmée',
            'Une mise à jour de maintenance est programmée pour ce soir à 23h00. Durée estimée: 2 heures.',
            '{"scheduled_time": "2024-01-15T23:00:00Z", "estimated_duration": "2 hours", "affected_services": ["web", "api", "database"]}'::jsonb,
            admin_profile_id,
            'open',
            NOW() - INTERVAL '3 hours'
        ),
        (
            'security',
            'critical',
            'Activité suspecte détectée',
            'Tentative d''accès non autorisé aux données sensibles détectée. Investigation immédiate requise.',
            '{"target": "user_data", "source_ip": "************", "timestamp": "2024-01-15T14:22:00Z", "blocked": true}'::jsonb,
            admin_profile_id,
            'resolved',
            NOW() - INTERVAL '1 day'
        ),
        (
            'system',
            'low',
            'Sauvegarde automatique réussie',
            'La sauvegarde automatique quotidienne s''est terminée avec succès. Toutes les données sont sécurisées.',
            '{"backup_size": "2.4GB", "duration": "45 minutes", "status": "success", "next_backup": "2024-01-16T02:00:00Z"}'::jsonb,
            admin_profile_id,
            'resolved',
            NOW() - INTERVAL '12 hours'
        );
        
        RAISE NOTICE '✅ 10 alertes de test créées avec différents types et priorités';
    ELSE
        RAISE NOTICE '✅ Des alertes existent déjà (%), pas de création de test', alert_count;
    END IF;
    
END $$;

-- =====================================================
-- 3. CRÉER DES STATISTIQUES ET MÉTRIQUES
-- =====================================================

DO $$
DECLARE
    total_alerts integer;
    open_alerts integer;
    critical_alerts integer;
    resolved_today integer;
    avg_resolution_time interval;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📊 Calcul des statistiques d''alertes...';
    
    -- Statistiques générales
    SELECT COUNT(*) INTO total_alerts FROM admin_alerts;
    SELECT COUNT(*) INTO open_alerts FROM admin_alerts WHERE status = 'open';
    SELECT COUNT(*) INTO critical_alerts FROM admin_alerts WHERE priority = 'critical' AND status != 'resolved';
    
    -- Alertes résolues aujourd'hui
    SELECT COUNT(*) INTO resolved_today 
    FROM admin_alerts 
    WHERE status = 'resolved' 
    AND DATE(resolved_at) = CURRENT_DATE;
    
    -- Temps moyen de résolution
    SELECT AVG(resolved_at - created_at) INTO avg_resolution_time
    FROM admin_alerts 
    WHERE status = 'resolved' 
    AND resolved_at IS NOT NULL;
    
    RAISE NOTICE '📈 Statistiques calculées:';
    RAISE NOTICE '   • Total alertes: %', total_alerts;
    RAISE NOTICE '   • Alertes ouvertes: %', open_alerts;
    RAISE NOTICE '   • Alertes critiques: %', critical_alerts;
    RAISE NOTICE '   • Résolues aujourd''hui: %', resolved_today;
    RAISE NOTICE '   • Temps moyen résolution: %', COALESCE(avg_resolution_time::text, 'N/A');
    
END $$;

-- =====================================================
-- 4. CRÉER UNE ALERTE DE DÉPLOIEMENT
-- =====================================================

DO $$
DECLARE
    admin_profile_id uuid;
    total_alerts integer;
    open_alerts integer;
    critical_alerts integer;
BEGIN
    SELECT id INTO admin_profile_id FROM admin_profiles LIMIT 1;
    SELECT COUNT(*) INTO total_alerts FROM admin_alerts;
    SELECT COUNT(*) INTO open_alerts FROM admin_alerts WHERE status = 'open';
    SELECT COUNT(*) INTO critical_alerts FROM admin_alerts WHERE priority = 'critical' AND status != 'resolved';
    
    IF admin_profile_id IS NOT NULL THEN
        INSERT INTO admin_alerts (
            alert_type, 
            priority, 
            title, 
            message, 
            details, 
            created_by, 
            status
        ) VALUES (
            'deployment', 
            'medium', 
            'Système d''Alertes Complètement Déployé', 
            FORMAT('Le système complet de gestion des alertes est maintenant opérationnel. %s alertes au total, %s ouvertes, %s critiques.', total_alerts, open_alerts, critical_alerts), 
            FORMAT('{"deployment": "system_alerts", "stats": {"total": %s, "open": %s, "critical": %s}, "features": ["alert_management", "priority_system", "assignment", "resolution_tracking", "bulk_actions", "detailed_view", "export", "real_time_stats"]}', total_alerts, open_alerts, critical_alerts)::jsonb, 
            admin_profile_id, 
            'open'
        );
        
        RAISE NOTICE '✅ Alerte de déploiement créée';
    END IF;
END $$;

-- =====================================================
-- 5. RÉSUMÉ FINAL ET GUIDE D'UTILISATION
-- =====================================================

DO $$
DECLARE
    total_alerts integer;
    open_alerts integer;
    in_progress_alerts integer;
    resolved_alerts integer;
    critical_alerts integer;
    high_alerts integer;
    medium_alerts integer;
    low_alerts integer;
BEGIN
    -- Statistiques finales
    SELECT COUNT(*) INTO total_alerts FROM admin_alerts;
    SELECT COUNT(*) INTO open_alerts FROM admin_alerts WHERE status = 'open';
    SELECT COUNT(*) INTO in_progress_alerts FROM admin_alerts WHERE status = 'in_progress';
    SELECT COUNT(*) INTO resolved_alerts FROM admin_alerts WHERE status = 'resolved';
    SELECT COUNT(*) INTO critical_alerts FROM admin_alerts WHERE priority = 'critical';
    SELECT COUNT(*) INTO high_alerts FROM admin_alerts WHERE priority = 'high';
    SELECT COUNT(*) INTO medium_alerts FROM admin_alerts WHERE priority = 'medium';
    SELECT COUNT(*) INTO low_alerts FROM admin_alerts WHERE priority = 'low';
    
    RAISE NOTICE '';
    RAISE NOTICE '🎊 ===============================================';
    RAISE NOTICE '🎊 SYSTÈME D''ALERTES COMPLÈTEMENT DÉPLOYÉ !';
    RAISE NOTICE '🎊 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '📊 STATISTIQUES FINALES:';
    RAISE NOTICE '   🔔 Total alertes: %', total_alerts;
    RAISE NOTICE '   🔴 Ouvertes: %', open_alerts;
    RAISE NOTICE '   🟡 En cours: %', in_progress_alerts;
    RAISE NOTICE '   🟢 Résolues: %', resolved_alerts;
    RAISE NOTICE '';
    RAISE NOTICE '⚡ PAR PRIORITÉ:';
    RAISE NOTICE '   🔥 Critiques: %', critical_alerts;
    RAISE NOTICE '   🟠 Hautes: %', high_alerts;
    RAISE NOTICE '   🟡 Moyennes: %', medium_alerts;
    RAISE NOTICE '   🔵 Basses: %', low_alerts;
    RAISE NOTICE '';
    RAISE NOTICE '🚀 FONCTIONNALITÉS DISPONIBLES:';
    RAISE NOTICE '';
    RAISE NOTICE '📋 TYPES D''ALERTES SUPPORTÉS:';
    RAISE NOTICE '   ✅ Sécurité (tentatives intrusion, activités suspectes)';
    RAISE NOTICE '   ✅ Système (espace disque, performance serveur)';
    RAISE NOTICE '   ✅ Base de données (performance, erreurs)';
    RAISE NOTICE '   ✅ Actions utilisateur (pics d''activité, comportements)';
    RAISE NOTICE '   ✅ Entreprises (demandes vérification, problèmes)';
    RAISE NOTICE '   ✅ Contenu (signalements, modération)';
    RAISE NOTICE '   ✅ Performance (API, temps de réponse)';
    RAISE NOTICE '   ✅ Déploiement (mises à jour, maintenance)';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 ACTIONS DISPONIBLES:';
    RAISE NOTICE '   ✅ Assigner à un administrateur';
    RAISE NOTICE '   ✅ Marquer comme résolue';
    RAISE NOTICE '   ✅ Ignorer l''alerte';
    RAISE NOTICE '   ✅ Escalader la priorité';
    RAISE NOTICE '   ✅ Actions en lot';
    RAISE NOTICE '';
    RAISE NOTICE '📊 INTERFACE AVANCÉE:';
    RAISE NOTICE '   ✅ Statistiques en temps réel (5 cartes)';
    RAISE NOTICE '   ✅ Filtres multiples (type, statut, priorité)';
    RAISE NOTICE '   ✅ Recherche globale';
    RAISE NOTICE '   ✅ Vue détaillée avec historique';
    RAISE NOTICE '   ✅ Création d''alertes personnalisées';
    RAISE NOTICE '   ✅ Export des données';
    RAISE NOTICE '   ✅ Pagination intelligente';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 TESTEZ MAINTENANT:';
    RAISE NOTICE '1. Allez dans "Alertes Système"';
    RAISE NOTICE '2. Explorez les 5 cartes de statistiques';
    RAISE NOTICE '3. Testez les filtres par type/statut/priorité';
    RAISE NOTICE '4. Cliquez sur 👁️ pour voir les détails';
    RAISE NOTICE '5. Testez les actions avec ⋮';
    RAISE NOTICE '6. Créez une nouvelle alerte avec +';
    RAISE NOTICE '7. Utilisez les actions en lot';
    RAISE NOTICE '8. Exportez les données';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 SYSTÈME PRÊT POUR LA PRODUCTION !';
    RAISE NOTICE '';
END $$;
