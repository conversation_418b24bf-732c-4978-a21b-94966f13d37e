import { FollowRequestService } from '../services/followRequestService';

/**
 * Utilitaire de test pour le service de demandes de suivi
 */
export class FollowRequestServiceTester {
  
  /**
   * Tester la récupération des demandes reçues
   */
  static async testGetReceivedRequests(userId: string): Promise<void> {
    console.log('🧪 Test: Récupération des demandes reçues');
    console.log('👤 User ID:', userId);
    
    try {
      const startTime = Date.now();
      const requests = await FollowRequestService.getReceivedFollowRequests(userId);
      const endTime = Date.now();
      
      console.log('✅ Test réussi');
      console.log('📊 Résultats:');
      console.log(`   - Demandes trouvées: ${requests.length}`);
      console.log(`   - Temps d'exécution: ${endTime - startTime}ms`);
      
      if (requests.length > 0) {
        console.log('📋 Première demande:');
        console.log('   - ID:', requests[0].id);
        console.log('   - Statut:', requests[0].status);
        console.log('   - Demandeur:', requests[0].requesterProfile?.username || 'Inconnu');
        console.log('   - Type de compte:', requests[0].requesterProfile?.role || 'Inconnu');
        console.log('   - Nom business:', requests[0].requesterProfile?.businessName || 'N/A');
      }
      
    } catch (error) {
      console.error('❌ Test échoué:', error);
    }
  }
  
  /**
   * Tester la récupération des demandes envoyées
   */
  static async testGetSentRequests(userId: string): Promise<void> {
    console.log('🧪 Test: Récupération des demandes envoyées');
    console.log('👤 User ID:', userId);
    
    try {
      const startTime = Date.now();
      const requests = await FollowRequestService.getSentFollowRequests(userId);
      const endTime = Date.now();
      
      console.log('✅ Test réussi');
      console.log('📊 Résultats:');
      console.log(`   - Demandes trouvées: ${requests.length}`);
      console.log(`   - Temps d'exécution: ${endTime - startTime}ms`);
      
      if (requests.length > 0) {
        console.log('📋 Première demande:');
        console.log('   - ID:', requests[0].id);
        console.log('   - Statut:', requests[0].status);
        console.log('   - Cible:', requests[0].targetProfile?.username || 'Inconnu');
        console.log('   - Type de compte:', requests[0].targetProfile?.role || 'Inconnu');
        console.log('   - Nom business:', requests[0].targetProfile?.businessName || 'N/A');
      }
      
    } catch (error) {
      console.error('❌ Test échoué:', error);
    }
  }
  
  /**
   * Tester avec des IDs invalides
   */
  static async testWithInvalidIds(): Promise<void> {
    console.log('🧪 Test: Gestion des IDs invalides');
    
    const invalidIds = [
      '',
      'invalid-id',
      '123',
      'not-a-uuid',
      null as any,
      undefined as any
    ];
    
    for (const invalidId of invalidIds) {
      console.log(`🔍 Test avec ID invalide: "${invalidId}"`);
      
      try {
        const receivedRequests = await FollowRequestService.getReceivedFollowRequests(invalidId);
        const sentRequests = await FollowRequestService.getSentFollowRequests(invalidId);
        
        console.log(`✅ Gestion gracieuse - Reçues: ${receivedRequests.length}, Envoyées: ${sentRequests.length}`);
        
      } catch (error) {
        console.log(`⚠️ Erreur capturée (normal):`, error.message);
      }
    }
  }
  
  /**
   * Test de performance avec plusieurs appels simultanés
   */
  static async testPerformance(userId: string, iterations: number = 5): Promise<void> {
    console.log('🧪 Test: Performance avec appels simultanés');
    console.log(`🔄 Nombre d'itérations: ${iterations}`);
    
    const startTime = Date.now();
    
    try {
      const promises = Array.from({ length: iterations }, () => 
        Promise.all([
          FollowRequestService.getReceivedFollowRequests(userId),
          FollowRequestService.getSentFollowRequests(userId)
        ])
      );
      
      const results = await Promise.all(promises);
      const endTime = Date.now();
      
      console.log('✅ Test de performance réussi');
      console.log(`⏱️ Temps total: ${endTime - startTime}ms`);
      console.log(`📊 Moyenne par appel: ${(endTime - startTime) / (iterations * 2)}ms`);
      
      // Vérifier la cohérence des résultats
      const firstResult = results[0];
      const allConsistent = results.every(result => 
        result[0].length === firstResult[0].length && 
        result[1].length === firstResult[1].length
      );
      
      console.log(`🔍 Cohérence des résultats: ${allConsistent ? '✅' : '❌'}`);
      
    } catch (error) {
      console.error('❌ Test de performance échoué:', error);
    }
  }
  
  /**
   * Tester la récupération des business_name
   */
  static async testBusinessNameRetrieval(userId: string): Promise<void> {
    console.log('🧪 Test: Récupération des business_name');

    try {
      const [receivedRequests, sentRequests] = await Promise.all([
        FollowRequestService.getReceivedFollowRequests(userId),
        FollowRequestService.getSentFollowRequests(userId)
      ]);

      console.log('📊 Analyse des business_name:');

      // Analyser les demandes reçues
      const receivedBusinessCount = receivedRequests.filter(r =>
        r.requesterProfile?.role === 'business' && r.requesterProfile?.businessName
      ).length;

      console.log(`   - Demandes reçues de business: ${receivedBusinessCount}/${receivedRequests.length}`);

      // Analyser les demandes envoyées
      const sentBusinessCount = sentRequests.filter(r =>
        r.targetProfile?.role === 'business' && r.targetProfile?.businessName
      ).length;

      console.log(`   - Demandes envoyées vers business: ${sentBusinessCount}/${sentRequests.length}`);

      // Afficher quelques exemples
      const businessExamples = [
        ...receivedRequests.filter(r => r.requesterProfile?.businessName),
        ...sentRequests.filter(r => r.targetProfile?.businessName)
      ].slice(0, 3);

      if (businessExamples.length > 0) {
        console.log('📋 Exemples de business_name récupérés:');
        businessExamples.forEach((req, index) => {
          const businessName = req.requesterProfile?.businessName || req.targetProfile?.businessName;
          const username = req.requesterProfile?.username || req.targetProfile?.username;
          console.log(`   ${index + 1}. ${username} → ${businessName}`);
        });
      }

      console.log('✅ Test business_name réussi');

    } catch (error) {
      console.error('❌ Test business_name échoué:', error);
    }
  }

  /**
   * Exécuter tous les tests
   */
  static async runAllTests(userId: string): Promise<void> {
    console.log('🚀 Démarrage des tests du FollowRequestService');
    console.log('=' .repeat(50));

    await this.testGetReceivedRequests(userId);
    console.log('');

    await this.testGetSentRequests(userId);
    console.log('');

    await this.testBusinessNameRetrieval(userId);
    console.log('');

    await this.testWithInvalidIds();
    console.log('');

    await this.testPerformance(userId, 3);
    console.log('');

    console.log('🎉 Tests terminés');
    console.log('=' .repeat(50));
  }
}

// Fonction utilitaire pour exécuter les tests depuis la console
export const testFollowRequestService = (userId: string) => {
  FollowRequestServiceTester.runAllTests(userId);
};

// Export pour utilisation dans la console du navigateur
(window as any).testFollowRequestService = testFollowRequestService;
