-- =====================================================
-- SCRIPT DE TEST DU SYSTÈME DE PAIEMENT
-- Version: 1.0.0
-- Date: 2024-12-19
-- Description: Tests complets pour valider le fonctionnement
--              du système de paiement après déploiement
-- =====================================================

-- Commencer une transaction de test
BEGIN;

-- =====================================================
-- VARIABLES DE TEST
-- =====================================================

-- C<PERSON>er des UUIDs de test
\set test_business_id '\'12345678-1234-1234-1234-123456789012\''
\set test_business_id2 '\'*************-4321-4321-************\''

RAISE NOTICE '🧪 DÉBUT DES TESTS DU SYSTÈME DE PAIEMENT';
RAISE NOTICE '================================================';

-- =====================================================
-- TEST 1: CRÉATION D'ESSAI GRATUIT
-- =====================================================

RAISE NOTICE '📋 Test 1: Création d''essai gratuit';

DO $$
DECLARE
    result RECORD;
    subscription_count INTEGER;
BEGIN
    -- Créer un essai gratuit
    SELECT * INTO result FROM create_free_trial(:test_business_id);
    
    IF NOT result.success THEN
        RAISE EXCEPTION 'Échec de création d''essai gratuit: %', result.message;
    END IF;
    
    -- Vérifier que l'abonnement a été créé
    SELECT COUNT(*) INTO subscription_count
    FROM business_subscriptions
    WHERE business_id = :test_business_id AND is_active = true;
    
    IF subscription_count != 1 THEN
        RAISE EXCEPTION 'Abonnement d''essai non créé correctement';
    END IF;
    
    RAISE NOTICE '✅ Essai gratuit créé avec succès: %', result.subscription_id;
END $$;

-- =====================================================
-- TEST 2: VÉRIFICATION DU STATUT D'ABONNEMENT
-- =====================================================

RAISE NOTICE '📋 Test 2: Vérification du statut d''abonnement';

DO $$
DECLARE
    status RECORD;
BEGIN
    -- Obtenir le statut d'abonnement
    SELECT * INTO status FROM get_subscription_status(:test_business_id);
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Statut d''abonnement non trouvé';
    END IF;
    
    IF NOT status.is_active THEN
        RAISE EXCEPTION 'L''abonnement devrait être actif';
    END IF;
    
    IF status.plan_id != 'free-trial' THEN
        RAISE EXCEPTION 'Le plan devrait être free-trial, trouvé: %', status.plan_id;
    END IF;
    
    IF NOT status.is_trial THEN
        RAISE EXCEPTION 'L''abonnement devrait être marqué comme essai';
    END IF;
    
    RAISE NOTICE '✅ Statut d''abonnement correct: Plan %, % jours restants', 
                 status.plan_name, status.days_remaining;
END $$;

-- =====================================================
-- TEST 3: CRÉATION D'INTENTION DE PAIEMENT
-- =====================================================

RAISE NOTICE '📋 Test 3: Création d''intention de paiement';

DO $$
DECLARE
    payment_intent_id TEXT;
    method_id TEXT;
BEGIN
    -- Créer une méthode de paiement de test
    method_id := 'pm_test_' || EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::BIGINT;
    
    INSERT INTO payment_methods (
        id, business_id, type, provider, card_last4, card_brand,
        is_default, is_verified
    ) VALUES (
        method_id, :test_business_id, 'card', 'Visa', '4242', 'visa',
        true, true
    );
    
    -- Créer une intention de paiement
    payment_intent_id := 'pi_test_' || EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::BIGINT;
    
    INSERT INTO payment_intents (
        id, business_id, plan_id, payment_method_id,
        amount, currency, status
    ) VALUES (
        payment_intent_id, :test_business_id, 'professional', method_id,
        35000.00, 'XOF', 'pending'
    );
    
    RAISE NOTICE '✅ Intention de paiement créée: %', payment_intent_id;
    
    -- Stocker l'ID pour les tests suivants
    PERFORM set_config('test.payment_intent_id', payment_intent_id, true);
    PERFORM set_config('test.payment_method_id', method_id, true);
END $$;

-- =====================================================
-- TEST 4: APPLICATION D'UN COUPON
-- =====================================================

RAISE NOTICE '📋 Test 4: Application d''un coupon';

DO $$
DECLARE
    result RECORD;
    payment_intent_id TEXT;
    original_amount DECIMAL;
    expected_discount DECIMAL;
BEGIN
    payment_intent_id := current_setting('test.payment_intent_id');
    
    -- Récupérer le montant original
    SELECT amount INTO original_amount
    FROM payment_intents
    WHERE id = payment_intent_id;
    
    -- Appliquer le coupon WELCOME10 (10% de réduction)
    SELECT * INTO result FROM apply_coupon(payment_intent_id, 'WELCOME10');
    
    IF NOT result.success THEN
        RAISE EXCEPTION 'Échec d''application du coupon: %', result.message;
    END IF;
    
    expected_discount := original_amount * 0.10;
    
    IF ABS(result.discount_amount - expected_discount) > 0.01 THEN
        RAISE EXCEPTION 'Montant de réduction incorrect. Attendu: %, Reçu: %', 
                        expected_discount, result.discount_amount;
    END IF;
    
    RAISE NOTICE '✅ Coupon appliqué: Réduction de % F CFA sur % F CFA', 
                 result.discount_amount, original_amount;
END $$;

-- =====================================================
-- TEST 5: TRAITEMENT D'UN PAIEMENT RÉUSSI
-- =====================================================

RAISE NOTICE '📋 Test 5: Traitement d''un paiement réussi';

DO $$
DECLARE
    result RECORD;
    payment_intent_id TEXT;
    receipt_exists BOOLEAN;
    subscription_exists BOOLEAN;
BEGIN
    payment_intent_id := current_setting('test.payment_intent_id');
    
    -- Traiter le paiement
    SELECT * INTO result FROM process_payment(
        payment_intent_id, 
        'txn_test_' || EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::BIGINT,
        '{"status": "succeeded", "test": true}'::jsonb
    );
    
    IF NOT result.success THEN
        RAISE EXCEPTION 'Échec du traitement du paiement: %', result.message;
    END IF;
    
    -- Vérifier que le reçu a été créé
    SELECT EXISTS(
        SELECT 1 FROM payment_receipts WHERE id = result.receipt_id
    ) INTO receipt_exists;
    
    IF NOT receipt_exists THEN
        RAISE EXCEPTION 'Reçu de paiement non créé';
    END IF;
    
    -- Vérifier qu'un nouvel abonnement a été créé
    SELECT EXISTS(
        SELECT 1 FROM business_subscriptions 
        WHERE business_id = :test_business_id 
        AND plan_id = 'professional' 
        AND is_active = true
    ) INTO subscription_exists;
    
    IF NOT subscription_exists THEN
        RAISE EXCEPTION 'Nouvel abonnement non créé';
    END IF;
    
    RAISE NOTICE '✅ Paiement traité avec succès: Reçu %', result.receipt_id;
END $$;

-- =====================================================
-- TEST 6: GÉNÉRATION DE NUMÉRO DE FACTURE
-- =====================================================

RAISE NOTICE '📋 Test 6: Génération de numéro de facture';

DO $$
DECLARE
    invoice1 TEXT;
    invoice2 TEXT;
BEGIN
    -- Générer deux numéros de facture
    SELECT generate_invoice_number() INTO invoice1;
    SELECT generate_invoice_number() INTO invoice2;
    
    -- Vérifier le format
    IF NOT invoice1 ~ '^INV-\d{4}-\d{6}$' THEN
        RAISE EXCEPTION 'Format de facture invalide: %', invoice1;
    END IF;
    
    -- Vérifier l'unicité
    IF invoice1 = invoice2 THEN
        RAISE EXCEPTION 'Les numéros de facture ne sont pas uniques: %', invoice1;
    END IF;
    
    RAISE NOTICE '✅ Numéros de facture générés: % et %', invoice1, invoice2;
END $$;

-- =====================================================
-- TEST 7: ANNULATION D'ABONNEMENT
-- =====================================================

RAISE NOTICE '📋 Test 7: Annulation d''abonnement';

DO $$
DECLARE
    result RECORD;
    subscription_active BOOLEAN;
BEGIN
    -- Annuler l'abonnement (pas immédiatement)
    SELECT * INTO result FROM cancel_subscription(
        :test_business_id2, 
        'Test d''annulation', 
        false
    );
    
    -- Pour ce test, créons d'abord un abonnement pour le business 2
    INSERT INTO business_subscriptions (
        business_id, plan_id, start_date, end_date,
        is_active, is_trial, auto_renew,
        payment_status, payment_method, stripe_subscription_id
    ) VALUES (
        :test_business_id2, 'starter', CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP + INTERVAL '30 days',
        true, false, true,
        'paid', NULL, NULL
    );
    
    -- Maintenant annuler
    SELECT * INTO result FROM cancel_subscription(
        :test_business_id2, 
        'Test d''annulation', 
        false
    );
    
    IF NOT result.success THEN
        RAISE EXCEPTION 'Échec d''annulation: %', result.message;
    END IF;
    
    -- Vérifier que auto_renew est désactivé
    SELECT is_active INTO subscription_active
    FROM business_subscriptions
    WHERE business_id = :test_business_id2 AND is_active = true;
    
    RAISE NOTICE '✅ Abonnement annulé pour la fin de période: %', result.cancelled_at;
END $$;

-- =====================================================
-- TEST 8: DÉTECTION D'ANOMALIES
-- =====================================================

RAISE NOTICE '📋 Test 8: Détection d''anomalies';

DO $$
DECLARE
    anomaly_count INTEGER;
BEGIN
    -- Exécuter la détection d'anomalies
    SELECT COUNT(*) INTO anomaly_count FROM detect_payment_anomalies();
    
    RAISE NOTICE '✅ Détection d''anomalies exécutée: % anomalies détectées', anomaly_count;
END $$;

-- =====================================================
-- TEST 9: STATISTIQUES ET VUES
-- =====================================================

RAISE NOTICE '📋 Test 9: Statistiques et vues';

DO $$
DECLARE
    stats_count INTEGER;
    revenue_stats RECORD;
BEGIN
    -- Tester les vues matérialisées
    SELECT COUNT(*) INTO stats_count FROM subscription_metrics;
    
    IF stats_count = 0 THEN
        RAISE EXCEPTION 'Aucune métrique d''abonnement trouvée';
    END IF;
    
    -- Tester le calcul de statistiques de revenus
    SELECT * INTO revenue_stats FROM calculate_revenue_stats(
        CURRENT_DATE - INTERVAL '30 days',
        CURRENT_DATE
    );
    
    RAISE NOTICE '✅ Statistiques calculées: % F CFA de revenus, % transactions', 
                 revenue_stats.total_revenue, revenue_stats.total_transactions;
END $$;

-- =====================================================
-- TEST 10: NETTOYAGE ET MAINTENANCE
-- =====================================================

RAISE NOTICE '📋 Test 10: Nettoyage et maintenance';

DO $$
DECLARE
    maintenance_result TEXT;
    cleanup_result RECORD;
BEGIN
    -- Tester la maintenance quotidienne
    SELECT daily_maintenance() INTO maintenance_result;
    
    -- Tester le nettoyage des anciennes données
    SELECT * INTO cleanup_result FROM cleanup_old_data(1); -- Garder seulement 1 jour
    
    RAISE NOTICE '✅ Maintenance exécutée: %', maintenance_result;
    RAISE NOTICE '✅ Nettoyage: % événements, % webhooks supprimés', 
                 cleanup_result.events_deleted, cleanup_result.webhooks_deleted;
END $$;

-- =====================================================
-- NETTOYAGE DES DONNÉES DE TEST
-- =====================================================

RAISE NOTICE '🧹 Nettoyage des données de test...';

-- Supprimer les données de test créées
DELETE FROM coupon_usages WHERE business_id IN (:test_business_id, :test_business_id2);
DELETE FROM payment_receipts WHERE business_id IN (:test_business_id, :test_business_id2);
DELETE FROM payment_events WHERE event_data->>'business_id' IN (:test_business_id, :test_business_id2);
DELETE FROM payment_intents WHERE business_id IN (:test_business_id, :test_business_id2);
DELETE FROM payment_methods WHERE business_id IN (:test_business_id, :test_business_id2);
DELETE FROM business_subscriptions WHERE business_id IN (:test_business_id, :test_business_id2);

-- Rollback pour ne pas affecter la base de données
ROLLBACK;

-- =====================================================
-- RAPPORT FINAL
-- =====================================================

RAISE NOTICE '';
RAISE NOTICE '🎉 TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS!';
RAISE NOTICE '==========================================';
RAISE NOTICE '';
RAISE NOTICE '✅ Tests réussis:';
RAISE NOTICE '   1. Création d''essai gratuit';
RAISE NOTICE '   2. Vérification du statut d''abonnement';
RAISE NOTICE '   3. Création d''intention de paiement';
RAISE NOTICE '   4. Application d''un coupon';
RAISE NOTICE '   5. Traitement d''un paiement réussi';
RAISE NOTICE '   6. Génération de numéro de facture';
RAISE NOTICE '   7. Annulation d''abonnement';
RAISE NOTICE '   8. Détection d''anomalies';
RAISE NOTICE '   9. Statistiques et vues';
RAISE NOTICE '   10. Nettoyage et maintenance';
RAISE NOTICE '';
RAISE NOTICE '🚀 Le système de paiement est prêt pour la production!';
RAISE NOTICE '';
RAISE NOTICE '📋 Prochaines étapes recommandées:';
RAISE NOTICE '   - Configurer les processeurs de paiement réels';
RAISE NOTICE '   - Mettre en place les webhooks';
RAISE NOTICE '   - Planifier les tâches de maintenance';
RAISE NOTICE '   - Configurer la surveillance';
RAISE NOTICE '   - Tester avec de vraies transactions';
RAISE NOTICE '';
