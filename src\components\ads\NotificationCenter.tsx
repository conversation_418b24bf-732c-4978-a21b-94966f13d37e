import React, { useState, useEffect } from 'react';
import { 
  Bell, X, AlertTriangle, CheckCircle, Info, TrendingUp, 
  DollarSign, Target, Users, Calendar, Settings, BellRing
} from 'lucide-react';
import { formatAmount, formatPercentage } from '../../utils/formatUtils';

interface Notification {
  id: string;
  type: 'success' | 'warning' | 'error' | 'info' | 'performance' | 'budget' | 'optimization';
  title: string;
  message: string;
  timestamp: Date;
  priority: 'high' | 'medium' | 'low';
  actionable?: boolean;
  action?: {
    label: string;
    callback: () => void;
  };
  data?: any;
}

interface NotificationCenterProps {
  isOpen: boolean;
  onClose: () => void;
  campaigns: any[];
  analytics: any;
  onNotificationAction?: (notification: Notification) => void;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  isOpen,
  onClose,
  campaigns,
  analytics,
  onNotificationAction
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filter, setFilter] = useState<'all' | 'unread' | 'actionable'>('all');
  const [readNotifications, setReadNotifications] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (isOpen) {
      generateIntelligentNotifications();
    }
  }, [isOpen, campaigns, analytics]);

  const generateIntelligentNotifications = () => {
    const newNotifications: Notification[] = [];

    // Notifications de performance
    campaigns.forEach(campaign => {
      if (campaign.ctr > 8) {
        newNotifications.push({
          id: `perf-${campaign.id}-high-ctr`,
          type: 'performance',
          title: 'Performance exceptionnelle',
          message: `La campagne "${campaign.title}" a un CTR de ${campaign.ctr.toFixed(2)}% ! Considérez augmenter le budget.`,
          timestamp: new Date(),
          priority: 'high',
          actionable: true,
          action: {
            label: 'Augmenter le budget',
            callback: () => console.log('Augmenter budget', campaign.id)
          },
          data: { campaignId: campaign.id, metric: 'ctr', value: campaign.ctr }
        });
      }

      if (campaign.spent / campaign.budget > 0.8) {
        newNotifications.push({
          id: `budget-${campaign.id}-warning`,
          type: 'warning',
          title: 'Budget bientôt épuisé',
          message: `La campagne "${campaign.title}" a utilisé ${((campaign.spent / campaign.budget) * 100).toFixed(1)}% de son budget.`,
          timestamp: new Date(),
          priority: 'medium',
          actionable: true,
          action: {
            label: 'Ajuster le budget',
            callback: () => console.log('Ajuster budget', campaign.id)
          },
          data: { campaignId: campaign.id, spent: campaign.spent, budget: campaign.budget }
        });
      }

      if (campaign.ctr < 2) {
        newNotifications.push({
          id: `perf-${campaign.id}-low-ctr`,
          type: 'optimization',
          title: 'Optimisation recommandée',
          message: `La campagne "${campaign.title}" a un CTR faible (${campaign.ctr.toFixed(2)}%). Optimisez le ciblage ou les créations.`,
          timestamp: new Date(),
          priority: 'medium',
          actionable: true,
          action: {
            label: 'Optimiser',
            callback: () => console.log('Optimiser campagne', campaign.id)
          },
          data: { campaignId: campaign.id, issue: 'low_ctr', value: campaign.ctr }
        });
      }
    });

    // Notifications d'analytics
    if (analytics.roas > 5) {
      newNotifications.push({
        id: 'analytics-high-roas',
        type: 'success',
        title: 'ROAS exceptionnel',
        message: `Votre ROAS de ${analytics.roas}x est excellent ! Vos campagnes sont très rentables.`,
        timestamp: new Date(),
        priority: 'high',
        actionable: false,
        data: { metric: 'roas', value: analytics.roas }
      });
    }

    if (analytics.conversionRate < 2) {
      newNotifications.push({
        id: 'analytics-low-conversion',
        type: 'warning',
        title: 'Taux de conversion faible',
        message: `Votre taux de conversion de ${analytics.conversionRate}% pourrait être amélioré. Optimisez vos pages de destination.`,
        timestamp: new Date(),
        priority: 'medium',
        actionable: true,
        action: {
          label: 'Voir recommandations',
          callback: () => console.log('Voir recommandations conversion')
        },
        data: { metric: 'conversion_rate', value: analytics.conversionRate }
      });
    }

    // Notifications temporelles
    const now = new Date();
    const isWeekend = now.getDay() === 0 || now.getDay() === 6;
    
    if (isWeekend) {
      newNotifications.push({
        id: 'timing-weekend',
        type: 'info',
        title: 'Performance weekend',
        message: 'Les weekends montrent généralement +40% de conversions. Vos campagnes sont-elles optimisées ?',
        timestamp: new Date(),
        priority: 'low',
        actionable: true,
        action: {
          label: 'Ajuster la planification',
          callback: () => console.log('Ajuster planification weekend')
        },
        data: { timing: 'weekend' }
      });
    }

    // Notifications de budget global
    const totalSpent = campaigns.reduce((sum, c) => sum + c.spent, 0);
    const totalBudget = campaigns.reduce((sum, c) => sum + c.budget, 0);
    
    if (totalSpent / totalBudget > 0.9) {
      newNotifications.push({
        id: 'budget-global-warning',
        type: 'error',
        title: 'Budget global critique',
        message: `Vous avez utilisé ${((totalSpent / totalBudget) * 100).toFixed(1)}% de votre budget total (${formatAmount(totalSpent)} / ${formatAmount(totalBudget)}).`,
        timestamp: new Date(),
        priority: 'high',
        actionable: true,
        action: {
          label: 'Gérer le budget',
          callback: () => console.log('Gérer budget global')
        },
        data: { totalSpent, totalBudget }
      });
    }

    // Notifications d'opportunités
    newNotifications.push({
      id: 'opportunity-audience-expansion',
      type: 'optimization',
      title: 'Opportunité d\'expansion',
      message: 'L\'audience 35-44 ans montre un potentiel élevé avec un CPC 15% plus bas. Testez cette expansion.',
      timestamp: new Date(),
      priority: 'medium',
      actionable: true,
      action: {
        label: 'Tester l\'expansion',
        callback: () => console.log('Tester expansion audience')
      },
      data: { opportunity: 'audience_expansion', segment: '35-44' }
    });

    setNotifications(newNotifications);
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return CheckCircle;
      case 'warning': return AlertTriangle;
      case 'error': return AlertTriangle;
      case 'performance': return TrendingUp;
      case 'budget': return DollarSign;
      case 'optimization': return Target;
      default: return Info;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success': return '#10b981';
      case 'warning': return '#f59e0b';
      case 'error': return '#ef4444';
      case 'performance': return '#8b5cf6';
      case 'budget': return '#3b82f6';
      case 'optimization': return '#06b6d4';
      default: return '#6b7280';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#6b7280';
    }
  };

  const markAsRead = (notificationId: string) => {
    setReadNotifications(prev => new Set([...prev, notificationId]));
  };

  const handleNotificationAction = (notification: Notification) => {
    if (notification.action) {
      notification.action.callback();
      markAsRead(notification.id);
      if (onNotificationAction) {
        onNotificationAction(notification);
      }
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread':
        return !readNotifications.has(notification.id);
      case 'actionable':
        return notification.actionable;
      default:
        return true;
    }
  });

  const unreadCount = notifications.filter(n => !readNotifications.has(n.id)).length;

  if (!isOpen) return null;

  return (
    <div className="notification-center-modal">
      <div className="notification-center-content">
        <div className="notification-center-header">
          <div className="notification-title">
            <BellRing size={24} />
            <div>
              <h2>Centre de notifications</h2>
              <p>{unreadCount} notification{unreadCount !== 1 ? 's' : ''} non lue{unreadCount !== 1 ? 's' : ''}</p>
            </div>
          </div>
          <button className="close-btn" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="notification-filters">
          <button 
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            Toutes ({notifications.length})
          </button>
          <button 
            className={`filter-btn ${filter === 'unread' ? 'active' : ''}`}
            onClick={() => setFilter('unread')}
          >
            Non lues ({unreadCount})
          </button>
          <button 
            className={`filter-btn ${filter === 'actionable' ? 'active' : ''}`}
            onClick={() => setFilter('actionable')}
          >
            Actions requises ({notifications.filter(n => n.actionable).length})
          </button>
        </div>

        <div className="notifications-list">
          {filteredNotifications.length === 0 ? (
            <div className="no-notifications">
              <Bell size={48} color="#9ca3af" />
              <h3>Aucune notification</h3>
              <p>Vous êtes à jour !</p>
            </div>
          ) : (
            filteredNotifications.map(notification => {
              const IconComponent = getNotificationIcon(notification.type);
              const isRead = readNotifications.has(notification.id);
              
              return (
                <div 
                  key={notification.id} 
                  className={`notification-item ${isRead ? 'read' : 'unread'}`}
                  onClick={() => !isRead && markAsRead(notification.id)}
                >
                  <div className="notification-icon" style={{ color: getNotificationColor(notification.type) }}>
                    <IconComponent size={20} />
                  </div>
                  
                  <div className="notification-content">
                    <div className="notification-header">
                      <h4>{notification.title}</h4>
                      <div className="notification-meta">
                        <span 
                          className="priority-badge" 
                          style={{ backgroundColor: getPriorityColor(notification.priority) }}
                        >
                          {notification.priority === 'high' ? 'Haute' : 
                           notification.priority === 'medium' ? 'Moyenne' : 'Basse'}
                        </span>
                        <span className="notification-time">
                          {notification.timestamp.toLocaleTimeString('fr-FR', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </span>
                      </div>
                    </div>
                    
                    <p className="notification-message">{notification.message}</p>
                    
                    {notification.actionable && notification.action && (
                      <button 
                        className="notification-action"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleNotificationAction(notification);
                        }}
                      >
                        {notification.action.label}
                      </button>
                    )}
                  </div>
                  
                  {!isRead && <div className="unread-indicator"></div>}
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationCenter;
