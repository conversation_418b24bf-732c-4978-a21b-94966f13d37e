import React, { useState, useRef, useEffect } from 'react';
import { 
  ShoppingCart, 
  Search, 
  Tag, 
  TrendingUp, 
  Star, 
  Package, 
  Handshake, 
  MessageCircle, 
  Clock, 
  CheckCircle, 
  XCircle,
  DollarSign,
  Filter,
  Grid3X3,
  List,
  ChevronDown,
  Zap,
  Heart,
  Eye
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { UserRole } from '../../types';

interface MarketplaceDropdownMenuProps {
  onMarketplaceClick: () => void;
  onCategoriesClick?: () => void;
  onTopProductsClick?: () => void;
  onTrendingClick?: () => void;
  onOffersClick?: () => void;
  onNegotiationsClick?: () => void;
  onMyOrdersClick?: () => void;
  onBusinessProductsClick?: () => void;
  onBusinessSalesClick?: () => void;
  className?: string;
}

const MarketplaceDropdownMenu: React.FC<MarketplaceDropdownMenuProps> = ({
  onMarketplaceClick,
  onCategoriesClick,
  onTopProductsClick,
  onTrendingClick,
  onOffersClick,
  onNegotiationsClick,
  onMyOrdersClick,
  onBusinessProductsClick,
  onBusinessSalesClick,
  className = ''
}) => {
  const { currentUser } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Fermer le menu quand on clique à l'extérieur
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Fermer le menu quand on appuie sur Escape
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);

  const handleMenuClick = (action: () => void) => {
    action();
    setIsOpen(false);
  };

  const isBusinessUser = currentUser?.role === UserRole.BUSINESS;

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Marketplace Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-100 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <ShoppingCart size={20} />
        <span className="font-medium">Marketplace</span>
        <ChevronDown 
          size={16} 
          className={`transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute left-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          {/* Header */}
          <div className="px-4 py-3 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Marketplace</h3>
              <div className="flex items-center space-x-2">
                <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full font-medium">
                  Gratuit
                </span>
                <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full font-medium">
                  0% Commission
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Découvrez, achetez et négociez les meilleurs produits
            </p>
          </div>

          {/* Navigation Sections */}
          <div className="py-1">
            {/* Explorer */}
            <div className="px-4 py-2">
              <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
                Explorer
              </h4>
              <div className="space-y-1">
                <button
                  onClick={() => handleMenuClick(onMarketplaceClick)}
                  className="w-full flex items-center px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                >
                  <Grid3X3 size={16} className="mr-3 text-gray-500" />
                  <div className="flex-1 text-left">
                    <div className="font-medium">Tous les produits</div>
                    <div className="text-xs text-gray-500">Parcourir le catalogue complet</div>
                  </div>
                </button>

                {onCategoriesClick && (
                  <button
                    onClick={() => handleMenuClick(onCategoriesClick)}
                    className="w-full flex items-center px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                  >
                    <Filter size={16} className="mr-3 text-gray-500" />
                    <div className="flex-1 text-left">
                      <div className="font-medium">Catégories</div>
                      <div className="text-xs text-gray-500">Filtrer par catégorie</div>
                    </div>
                  </button>
                )}

                {onTopProductsClick && (
                  <button
                    onClick={() => handleMenuClick(onTopProductsClick)}
                    className="w-full flex items-center px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                  >
                    <Star size={16} className="mr-3 text-yellow-500" />
                    <div className="flex-1 text-left">
                      <div className="font-medium">Top produits</div>
                      <div className="text-xs text-gray-500">Les mieux notés</div>
                    </div>
                  </button>
                )}

                {onTrendingClick && (
                  <button
                    onClick={() => handleMenuClick(onTrendingClick)}
                    className="w-full flex items-center px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                  >
                    <TrendingUp size={16} className="mr-3 text-green-500" />
                    <div className="flex-1 text-left">
                      <div className="font-medium">Tendances</div>
                      <div className="text-xs text-gray-500">Produits populaires</div>
                    </div>
                  </button>
                )}

                {onOffersClick && (
                  <button
                    onClick={() => handleMenuClick(onOffersClick)}
                    className="w-full flex items-center px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                  >
                    <Tag size={16} className="mr-3 text-red-500" />
                    <div className="flex-1 text-left">
                      <div className="font-medium">Offres & Promotions</div>
                      <div className="text-xs text-gray-500">Réductions spéciales</div>
                    </div>
                    <span className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                      Nouveau
                    </span>
                  </button>
                )}
              </div>
            </div>

            {/* Négociations */}
            <div className="border-t border-gray-100 px-4 py-2">
              <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
                Négociations
              </h4>
              <div className="space-y-1">
                {onNegotiationsClick && (
                  <button
                    onClick={() => handleMenuClick(onNegotiationsClick)}
                    className="w-full flex items-center px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                  >
                    <Handshake size={16} className="mr-3 text-blue-500" />
                    <div className="flex-1 text-left">
                      <div className="font-medium">Mes négociations</div>
                      <div className="text-xs text-gray-500">Gérer vos négociations</div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                        2
                      </span>
                    </div>
                  </button>
                )}

                <button
                  className="w-full flex items-center px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                >
                  <MessageCircle size={16} className="mr-3 text-purple-500" />
                  <div className="flex-1 text-left">
                    <div className="font-medium">Négocier un prix</div>
                    <div className="text-xs text-gray-500">Proposer une offre</div>
                  </div>
                  <Zap size={12} className="text-yellow-500" />
                </button>

                <div className="grid grid-cols-3 gap-1 mt-2">
                  <div className="text-center p-2 bg-yellow-50 rounded-md">
                    <Clock size={14} className="mx-auto text-yellow-600 mb-1" />
                    <div className="text-xs text-yellow-700 font-medium">En cours</div>
                    <div className="text-xs text-yellow-600">3</div>
                  </div>
                  <div className="text-center p-2 bg-green-50 rounded-md">
                    <CheckCircle size={14} className="mx-auto text-green-600 mb-1" />
                    <div className="text-xs text-green-700 font-medium">Acceptées</div>
                    <div className="text-xs text-green-600">12</div>
                  </div>
                  <div className="text-center p-2 bg-red-50 rounded-md">
                    <XCircle size={14} className="mx-auto text-red-600 mb-1" />
                    <div className="text-xs text-red-700 font-medium">Refusées</div>
                    <div className="text-xs text-red-600">5</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Mes Achats */}
            <div className="border-t border-gray-100 px-4 py-2">
              <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
                Mes Achats
              </h4>
              <div className="space-y-1">
                {onMyOrdersClick && (
                  <button
                    onClick={() => handleMenuClick(onMyOrdersClick)}
                    className="w-full flex items-center px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                  >
                    <Package size={16} className="mr-3 text-green-500" />
                    <div className="flex-1 text-left">
                      <div className="font-medium">Mes commandes</div>
                      <div className="text-xs text-gray-500">Historique et suivi</div>
                    </div>
                  </button>
                )}

                <button
                  className="w-full flex items-center px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                >
                  <Heart size={16} className="mr-3 text-pink-500" />
                  <div className="flex-1 text-left">
                    <div className="font-medium">Liste de souhaits</div>
                    <div className="text-xs text-gray-500">Produits favoris</div>
                  </div>
                  <span className="text-xs text-gray-500">8</span>
                </button>

                <button
                  className="w-full flex items-center px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                >
                  <Eye size={16} className="mr-3 text-gray-500" />
                  <div className="flex-1 text-left">
                    <div className="font-medium">Récemment vus</div>
                    <div className="text-xs text-gray-500">Historique de navigation</div>
                  </div>
                </button>
              </div>
            </div>

            {/* Section Business (si utilisateur business) */}
            {isBusinessUser && (
              <div className="border-t border-gray-100 px-4 py-2">
                <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
                  Mon Business
                </h4>
                <div className="space-y-1">
                  {onBusinessProductsClick && (
                    <button
                      onClick={() => handleMenuClick(onBusinessProductsClick)}
                      className="w-full flex items-center px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                    >
                      <Package size={16} className="mr-3 text-blue-500" />
                      <div className="flex-1 text-left">
                        <div className="font-medium">Mes produits</div>
                        <div className="text-xs text-gray-500">Gérer mon catalogue</div>
                      </div>
                    </button>
                  )}

                  {onBusinessSalesClick && (
                    <button
                      onClick={() => handleMenuClick(onBusinessSalesClick)}
                      className="w-full flex items-center px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                    >
                      <DollarSign size={16} className="mr-3 text-green-500" />
                      <div className="flex-1 text-left">
                        <div className="font-medium">Mes ventes</div>
                        <div className="text-xs text-gray-500">Statistiques et revenus</div>
                      </div>
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-100 px-4 py-2">
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Marketplace gratuit</span>
              <div className="flex items-center space-x-2">
                <span className="flex items-center">
                  <Handshake size={12} className="mr-1" />
                  Négociation activée
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MarketplaceDropdownMenu;
