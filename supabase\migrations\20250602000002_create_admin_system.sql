-- Migration pour créer le système d'administration complet
-- Niveaux d'accès, permissions, audit et gestion des administrateurs

-- =====================================================
-- 1. ÉNUMÉRATION DES NIVEAUX D'ADMINISTRATION
-- =====================================================

-- Créer un type enum pour les niveaux d'administration
DO $$ BEGIN
    CREATE TYPE admin_level AS ENUM (
        'super_admin',      -- Accès complet à tout
        'platform_admin',   -- Gestion de la plateforme
        'content_moderator', -- Modération de contenu
        'support_admin',    -- Support et assistance
        'business_admin',   -- Gestion des entreprises
        'user_admin',       -- Gestion des utilisateurs
        'analytics_admin'   -- Accès aux analytics
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =====================================================
-- 2. TABLE DES ADMINISTRATEURS
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_profiles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  admin_level admin_level NOT NULL DEFAULT 'support_admin',
  
  -- Informations administrateur
  admin_code text UNIQUE NOT NULL, -- Code unique d'identification admin
  department text,
  supervisor_id uuid REFERENCES admin_profiles(id),
  
  -- Permissions spécifiques
  permissions jsonb DEFAULT '{}'::jsonb,
  restrictions jsonb DEFAULT '{}'::jsonb,
  
  -- Statut et activité
  is_active boolean DEFAULT true,
  last_login timestamptz,
  login_count integer DEFAULT 0,
  
  -- Métadonnées
  notes text,
  created_by uuid REFERENCES admin_profiles(id),
  
  -- Timestamps
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  activated_at timestamptz DEFAULT now(),
  deactivated_at timestamptz,
  
  -- Contraintes
  UNIQUE(user_id),
  CHECK(admin_code ~ '^ADM[0-9]{6}$') -- Format: ADM123456
);

-- =====================================================
-- 3. TABLE DES PERMISSIONS SYSTÈME
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_permissions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  permission_code text UNIQUE NOT NULL,
  permission_name text NOT NULL,
  description text,
  category text NOT NULL,
  
  -- Niveaux requis
  required_level admin_level NOT NULL,
  is_sensitive boolean DEFAULT false,
  
  -- Métadonnées
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- =====================================================
-- 4. TABLE DES SESSIONS ADMINISTRATEUR
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  admin_id uuid NOT NULL REFERENCES admin_profiles(id) ON DELETE CASCADE,
  
  -- Informations de session
  session_token text UNIQUE NOT NULL,
  ip_address inet,
  user_agent text,
  location jsonb,
  
  -- Statut de session
  is_active boolean DEFAULT true,
  expires_at timestamptz NOT NULL,
  
  -- Timestamps
  created_at timestamptz DEFAULT now(),
  ended_at timestamptz
);

-- =====================================================
-- 5. TABLE D'AUDIT DES ACTIONS ADMINISTRATEUR
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_audit_log (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  admin_id uuid NOT NULL REFERENCES admin_profiles(id) ON DELETE CASCADE,
  
  -- Action effectuée
  action_type text NOT NULL,
  action_description text NOT NULL,
  target_type text, -- 'user', 'business', 'post', 'ad', etc.
  target_id text,
  
  -- Détails de l'action
  old_values jsonb,
  new_values jsonb,
  metadata jsonb DEFAULT '{}'::jsonb,
  
  -- Contexte
  ip_address inet,
  user_agent text,
  session_id uuid REFERENCES admin_sessions(id),
  
  -- Résultat
  success boolean DEFAULT true,
  error_message text,
  
  -- Timestamps
  created_at timestamptz DEFAULT now()
);

-- =====================================================
-- 6. TABLE DES ALERTES SYSTÈME
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_alerts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Type et priorité
  alert_type text NOT NULL,
  priority text NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  
  -- Contenu de l'alerte
  title text NOT NULL,
  message text NOT NULL,
  details jsonb DEFAULT '{}'::jsonb,
  
  -- Assignation
  assigned_to uuid REFERENCES admin_profiles(id),
  created_by uuid REFERENCES admin_profiles(id),
  
  -- Statut
  status text DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'dismissed')),
  resolution_notes text,
  
  -- Timestamps
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  resolved_at timestamptz
);

-- =====================================================
-- 7. TABLE DES RAPPORTS ADMINISTRATEUR
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_reports (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Informations du rapport
  report_type text NOT NULL,
  title text NOT NULL,
  description text,
  
  -- Données du rapport
  data jsonb NOT NULL,
  filters jsonb DEFAULT '{}'::jsonb,
  
  -- Métadonnées
  generated_by uuid NOT NULL REFERENCES admin_profiles(id),
  is_scheduled boolean DEFAULT false,
  schedule_config jsonb,
  
  -- Partage
  shared_with uuid[] DEFAULT '{}',
  is_public boolean DEFAULT false,
  
  -- Timestamps
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- =====================================================
-- 8. CRÉATION DES INDEX POUR OPTIMISER LES PERFORMANCES
-- =====================================================

-- Index pour admin_profiles
CREATE INDEX IF NOT EXISTS idx_admin_profiles_user_id ON admin_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_profiles_admin_level ON admin_profiles(admin_level);
CREATE INDEX IF NOT EXISTS idx_admin_profiles_is_active ON admin_profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_admin_profiles_supervisor_id ON admin_profiles(supervisor_id);

-- Index pour admin_sessions
CREATE INDEX IF NOT EXISTS idx_admin_sessions_admin_id ON admin_sessions(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_is_active ON admin_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_expires_at ON admin_sessions(expires_at);

-- Index pour admin_audit_log
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_admin_id ON admin_audit_log(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_action_type ON admin_audit_log(action_type);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_target_type ON admin_audit_log(target_type);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_created_at ON admin_audit_log(created_at);

-- Index pour admin_alerts
CREATE INDEX IF NOT EXISTS idx_admin_alerts_assigned_to ON admin_alerts(assigned_to);
CREATE INDEX IF NOT EXISTS idx_admin_alerts_status ON admin_alerts(status);
CREATE INDEX IF NOT EXISTS idx_admin_alerts_priority ON admin_alerts(priority);
CREATE INDEX IF NOT EXISTS idx_admin_alerts_created_at ON admin_alerts(created_at);

-- =====================================================
-- 9. ACTIVATION DE LA SÉCURITÉ RLS
-- =====================================================

ALTER TABLE admin_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_reports ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 10. POLITIQUES DE SÉCURITÉ
-- =====================================================

-- Politiques pour admin_profiles
CREATE POLICY "Admins can view admin profiles based on level"
  ON admin_profiles
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM admin_profiles ap
      WHERE ap.user_id = auth.uid()
      AND ap.is_active = true
      AND (
        ap.admin_level = 'super_admin' OR
        ap.admin_level = 'platform_admin' OR
        ap.id = admin_profiles.id
      )
    )
  );

CREATE POLICY "Super admins can manage admin profiles"
  ON admin_profiles
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM admin_profiles ap
      WHERE ap.user_id = auth.uid()
      AND ap.is_active = true
      AND ap.admin_level = 'super_admin'
    )
  );

-- Politiques pour admin_audit_log
CREATE POLICY "Admins can view audit logs based on level"
  ON admin_audit_log
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM admin_profiles ap
      WHERE ap.user_id = auth.uid()
      AND ap.is_active = true
      AND (
        ap.admin_level IN ('super_admin', 'platform_admin') OR
        ap.id = admin_audit_log.admin_id
      )
    )
  );

-- Politiques pour admin_alerts
CREATE POLICY "Admins can view relevant alerts"
  ON admin_alerts
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM admin_profiles ap
      WHERE ap.user_id = auth.uid()
      AND ap.is_active = true
      AND (
        ap.admin_level IN ('super_admin', 'platform_admin') OR
        ap.id = admin_alerts.assigned_to OR
        ap.id = admin_alerts.created_by
      )
    )
  );

-- =====================================================
-- 11. FONCTIONS UTILITAIRES
-- =====================================================

-- Fonction pour vérifier les permissions d'un administrateur
CREATE OR REPLACE FUNCTION check_admin_permission(
  p_user_id uuid,
  p_permission_code text
) RETURNS boolean AS $$
DECLARE
  admin_record record;
  permission_record record;
BEGIN
  -- Récupérer le profil admin
  SELECT * INTO admin_record
  FROM admin_profiles
  WHERE user_id = p_user_id AND is_active = true;

  IF admin_record IS NULL THEN
    RETURN false;
  END IF;

  -- Super admin a tous les droits
  IF admin_record.admin_level = 'super_admin' THEN
    RETURN true;
  END IF;

  -- Vérifier la permission spécifique
  SELECT * INTO permission_record
  FROM admin_permissions
  WHERE permission_code = p_permission_code;

  IF permission_record IS NULL THEN
    RETURN false;
  END IF;

  -- Vérifier le niveau requis
  CASE permission_record.required_level
    WHEN 'super_admin' THEN
      RETURN admin_record.admin_level = 'super_admin';
    WHEN 'platform_admin' THEN
      RETURN admin_record.admin_level IN ('super_admin', 'platform_admin');
    ELSE
      RETURN admin_record.admin_level = permission_record.required_level OR
             admin_record.admin_level IN ('super_admin', 'platform_admin');
  END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour enregistrer une action d'audit
CREATE OR REPLACE FUNCTION log_admin_action(
  p_admin_id uuid,
  p_action_type text,
  p_action_description text,
  p_target_type text DEFAULT NULL,
  p_target_id text DEFAULT NULL,
  p_old_values jsonb DEFAULT NULL,
  p_new_values jsonb DEFAULT NULL,
  p_metadata jsonb DEFAULT '{}'::jsonb
) RETURNS uuid AS $$
DECLARE
  log_id uuid;
BEGIN
  INSERT INTO admin_audit_log (
    admin_id,
    action_type,
    action_description,
    target_type,
    target_id,
    old_values,
    new_values,
    metadata
  ) VALUES (
    p_admin_id,
    p_action_type,
    p_action_description,
    p_target_type,
    p_target_id,
    p_old_values,
    p_new_values,
    p_metadata
  ) RETURNING id INTO log_id;

  RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 12. INSERTION DES PERMISSIONS SYSTÈME
-- =====================================================

INSERT INTO admin_permissions (permission_code, permission_name, description, category, required_level, is_sensitive) VALUES
-- Permissions Super Admin
('SUPER_ADMIN_ALL', 'Accès complet système', 'Accès total à toutes les fonctionnalités', 'system', 'super_admin', true),
('MANAGE_ADMINS', 'Gestion des administrateurs', 'Créer, modifier, supprimer des comptes admin', 'admin', 'super_admin', true),
('SYSTEM_CONFIG', 'Configuration système', 'Modifier les paramètres système', 'system', 'super_admin', true),

-- Permissions Platform Admin
('PLATFORM_OVERVIEW', 'Vue d''ensemble plateforme', 'Accès au tableau de bord principal', 'platform', 'platform_admin', false),
('MANAGE_USERS', 'Gestion des utilisateurs', 'Gérer les comptes utilisateurs', 'users', 'platform_admin', false),
('MANAGE_BUSINESSES', 'Gestion des entreprises', 'Gérer les comptes entreprises', 'business', 'platform_admin', false),
('VIEW_ANALYTICS', 'Voir les analytics', 'Accès aux statistiques globales', 'analytics', 'platform_admin', false),
('MANAGE_PAYMENTS', 'Gestion des paiements', 'Gérer les transactions et abonnements', 'finance', 'platform_admin', true),

-- Permissions Content Moderator
('MODERATE_POSTS', 'Modération des posts', 'Modérer les publications', 'content', 'content_moderator', false),
('MODERATE_COMMENTS', 'Modération des commentaires', 'Modérer les commentaires', 'content', 'content_moderator', false),
('MANAGE_REPORTS', 'Gestion des signalements', 'Traiter les signalements', 'content', 'content_moderator', false),
('BAN_USERS', 'Bannir des utilisateurs', 'Suspendre ou bannir des comptes', 'moderation', 'content_moderator', true),

-- Permissions Support Admin
('MANAGE_TICKETS', 'Gestion des tickets', 'Gérer les tickets de support', 'support', 'support_admin', false),
('LIVE_CHAT', 'Chat en direct', 'Accès au chat support', 'support', 'support_admin', false),
('VIEW_USER_DATA', 'Voir données utilisateur', 'Accès aux données utilisateur pour support', 'support', 'support_admin', true),

-- Permissions Business Admin
('MANAGE_BUSINESS_ACCOUNTS', 'Gestion comptes entreprise', 'Gérer spécifiquement les entreprises', 'business', 'business_admin', false),
('VERIFY_BUSINESSES', 'Vérifier entreprises', 'Valider les comptes entreprise', 'business', 'business_admin', false),
('MANAGE_SUBSCRIPTIONS', 'Gestion abonnements', 'Gérer les abonnements business', 'business', 'business_admin', false),

-- Permissions User Admin
('MANAGE_USER_ACCOUNTS', 'Gestion comptes utilisateur', 'Gérer spécifiquement les utilisateurs', 'users', 'user_admin', false),
('MANAGE_USER_STATUS', 'Gestion statuts utilisateur', 'Modifier les statuts utilisateur', 'users', 'user_admin', false),
('VIEW_USER_ACTIVITY', 'Voir activité utilisateur', 'Consulter l''activité des utilisateurs', 'users', 'user_admin', false),

-- Permissions Analytics Admin
('VIEW_DETAILED_ANALYTICS', 'Analytics détaillées', 'Accès aux analytics avancées', 'analytics', 'analytics_admin', false),
('EXPORT_DATA', 'Export de données', 'Exporter les données analytics', 'analytics', 'analytics_admin', false),
('GENERATE_REPORTS', 'Génération de rapports', 'Créer des rapports personnalisés', 'analytics', 'analytics_admin', false);

-- =====================================================
-- 13. CRÉATION D'UN SUPER ADMINISTRATEUR PAR DÉFAUT
-- =====================================================

-- Fonction pour créer le premier super admin
CREATE OR REPLACE FUNCTION create_super_admin(
  p_email text,
  p_username text,
  p_admin_code text DEFAULT NULL
) RETURNS uuid AS $$
DECLARE
  user_id uuid;
  admin_id uuid;
  generated_code text;
BEGIN
  -- Générer un code admin si non fourni
  IF p_admin_code IS NULL THEN
    generated_code := 'ADM' || lpad(floor(random() * 1000000)::text, 6, '0');
  ELSE
    generated_code := p_admin_code;
  END IF;

  -- Vérifier si l'utilisateur existe
  SELECT id INTO user_id
  FROM profiles
  WHERE email = p_email OR username = p_username;

  IF user_id IS NULL THEN
    RAISE EXCEPTION 'Utilisateur non trouvé avec email % ou username %', p_email, p_username;
  END IF;

  -- Créer le profil admin
  INSERT INTO admin_profiles (
    user_id,
    admin_level,
    admin_code,
    department,
    is_active,
    notes
  ) VALUES (
    user_id,
    'super_admin',
    generated_code,
    'Administration Système',
    true,
    'Super administrateur créé automatiquement'
  ) RETURNING id INTO admin_id;

  -- Mettre à jour le rôle dans profiles
  UPDATE profiles
  SET role = 'admin'
  WHERE id = user_id;

  -- Log de création
  PERFORM log_admin_action(
    admin_id,
    'CREATE_SUPER_ADMIN',
    'Création du super administrateur',
    'admin',
    admin_id::text,
    NULL,
    jsonb_build_object('admin_code', generated_code, 'user_id', user_id),
    jsonb_build_object('auto_created', true)
  );

  RETURN admin_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 14. TRIGGERS POUR AUDIT AUTOMATIQUE
-- =====================================================

-- Fonction trigger pour audit automatique des modifications admin
CREATE OR REPLACE FUNCTION audit_admin_changes() RETURNS TRIGGER AS $$
DECLARE
  admin_id uuid;
BEGIN
  -- Récupérer l'ID admin de l'utilisateur actuel
  SELECT id INTO admin_id
  FROM admin_profiles
  WHERE user_id = auth.uid() AND is_active = true;

  IF admin_id IS NOT NULL THEN
    IF TG_OP = 'UPDATE' THEN
      PERFORM log_admin_action(
        admin_id,
        'UPDATE_' || TG_TABLE_NAME,
        'Modification de ' || TG_TABLE_NAME,
        TG_TABLE_NAME,
        COALESCE(NEW.id::text, OLD.id::text),
        to_jsonb(OLD),
        to_jsonb(NEW),
        jsonb_build_object('trigger', true)
      );
      RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
      PERFORM log_admin_action(
        admin_id,
        'DELETE_' || TG_TABLE_NAME,
        'Suppression de ' || TG_TABLE_NAME,
        TG_TABLE_NAME,
        OLD.id::text,
        to_jsonb(OLD),
        NULL,
        jsonb_build_object('trigger', true)
      );
      RETURN OLD;
    END IF;
  END IF;

  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Créer les triggers d'audit pour les tables importantes
CREATE TRIGGER audit_profiles_changes
  AFTER UPDATE OR DELETE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION audit_admin_changes();

CREATE TRIGGER audit_business_profiles_changes
  AFTER UPDATE OR DELETE ON business_profiles
  FOR EACH ROW
  EXECUTE FUNCTION audit_admin_changes();

CREATE TRIGGER audit_posts_changes
  AFTER UPDATE OR DELETE ON posts
  FOR EACH ROW
  EXECUTE FUNCTION audit_admin_changes();
