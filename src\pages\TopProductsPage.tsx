import React, { useMemo } from 'react';
import { usePosts } from '../context/PostsContext';
import { IPost } from '../types';
import ProductCard from '../components/marketplace/ProductCard'; // Assuming IPost can be adapted for ProductCard
import { Star } from 'lucide-react';

// Helper to calculate average rating for a post (can be moved to a utility file)
const getAverageRating = (post: IPost): { average: number; count: number } => {
  const commentRatings = post.comments
    ?.filter(comment => comment.hasUsedProduct && typeof comment.rating === 'number' && comment.rating > 0)
    .map(comment => comment.rating!);
  
  const allRatings = [post.rating, ...(commentRatings || [])].filter(r => typeof r === 'number' && r > 0 && r <= 5);

  if (allRatings.length === 0) {
    return { average: 0, count: 0 };
  }
  const sum = allRatings.reduce((acc, curr) => acc + curr, 0);
  return { average: sum / allRatings.length, count: allRatings.length };
};

interface TopRatedPost extends IPost {
  calculatedAverageRating: number;
  ratingCount: number;
}

const TopProductsPage: React.FC = () => {
  const { posts, loading } = usePosts();

  const topProductPerCategory = useMemo(() => {
    if (loading || posts.length === 0) {
      return [];
    }

    const postsWithAvgRating: TopRatedPost[] = posts.map(post => {
      const ratingInfo = getAverageRating(post);
      return {
        ...post,
        calculatedAverageRating: ratingInfo.average,
        ratingCount: ratingInfo.count,
      };
    });

    const categories: { [key: string]: TopRatedPost[] } = {};
    postsWithAvgRating.forEach(post => {
      if (!post.category) return;
      if (!categories[post.category]) {
        categories[post.category] = [];
      }
      categories[post.category].push(post);
    });

    const topProducts: TopRatedPost[] = [];
    for (const category in categories) {
      categories[category].sort((a, b) => b.calculatedAverageRating - a.calculatedAverageRating || b.ratingCount - a.ratingCount); // Sort by rating, then by count
      if (categories[category].length > 0) {
        topProducts.push(categories[category][0]); // Get the top one
      }
    }
    // Sort the top products from each category by their rating as well for overall display order
    topProducts.sort((a,b) => b.calculatedAverageRating - a.calculatedAverageRating || b.ratingCount - a.ratingCount);
    return topProducts;
  }, [posts, loading]);

  if (loading) {
    return <div className="text-center py-10">Chargement des top produits...</div>;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8 flex items-center">
        <Star size={32} className="mr-3 text-yellow-500 fill-yellow-400" />
        <h1 className="text-3xl font-bold text-gray-900">Top Produits par Catégorie</h1>
      </div>
      <p className="mt-2 mb-6 text-gray-600">Découvrez les produits les mieux notés dans chaque catégorie, basés sur les avis de la communauté.</p>

      {topProductPerCategory.length === 0 && !loading && (
         <div className="text-center py-10 bg-white p-6 rounded-lg shadow">
          <Star size={48} className="mx-auto text-gray-400 mb-4" />
          <p className="text-xl text-gray-700 font-semibold">Aucun produit à afficher pour le moment.</p>
          <p className="text-gray-500 mt-2">Les évaluations et posts aideront à déterminer les top produits.</p>
        </div>
      )}

      {topProductPerCategory.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {topProductPerCategory.map(post => (
            // ProductCard expects IProduct and onNegotiateClick.
            // We need to adapt IPost to IProduct or create a specific card.
            // For now, a simplified display:
            <div key={post.id} className="border p-4 rounded-lg shadow-lg bg-white flex flex-col">
              <img src={post.images[0] || 'https://via.placeholder.com/300x200?text=No+Image'} alt={post.productName} className="w-full h-48 object-cover rounded-md mb-3" />
              <h3 className="text-lg font-semibold text-gray-800">{post.productName}</h3>
              <p className="text-sm text-gray-600">{post.businessName}</p>
              <p className="text-xs text-gray-500 mb-1">Catégorie: {post.category}</p>
              <div className="flex items-center mt-1">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star key={i} size={16} className={`mr-0.5 ${i < post.calculatedAverageRating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`} />
                ))}
                <span className="ml-1 text-xs text-gray-600">({post.calculatedAverageRating.toFixed(1)}/5 sur {post.ratingCount} avis)</span>
              </div>
              {/* Add more details or a link to the product/post page if needed */}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TopProductsPage;
