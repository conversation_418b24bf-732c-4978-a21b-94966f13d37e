-- Créer la table des plans d'abonnement
CREATE TABLE IF NOT EXISTS public.subscription_plans (
  id text PRIMARY KEY,
  name text NOT NULL,
  description text,
  price integer NOT NULL, -- Prix en centimes
  duration integer NOT NULL, -- Durée en jours
  features text[] DEFAULT '{}',
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Insérer les plans par défaut
INSERT INTO public.subscription_plans (id, name, description, price, duration, features, is_active) VALUES
  ('trial', 'Essai Gratuit', 'Essai gratuit de 7 jours', 0, 7, ARRAY['basic-ai', 'basic-dashboard', 'basic-reviews'], true),
  ('monthly', 'Plan Mensuel', 'Abonnement mensuel', 2500, 30, ARRAY['basic-ai', 'basic-dashboard', 'basic-reviews', 'pdf-export'], true),
  ('yearly', 'Plan Annuel', 'Abonnement annuel avec réduction', 25000, 365, ARRAY['advanced-ai', 'custom-dashboard', 'unlimited-reviews', 'api-access'], true)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  price = EXCLUDED.price,
  duration = EXCLUDED.duration,
  features = EXCLUDED.features,
  is_active = EXCLUDED.is_active,
  updated_at = now();

-- Créer des index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_subscription_plans_is_active ON public.subscription_plans(is_active);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_price ON public.subscription_plans(price);

-- Activer RLS
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre à tous les utilisateurs authentifiés de voir les plans actifs
CREATE POLICY "Authenticated users can view active plans" ON public.subscription_plans
  FOR SELECT USING (is_active = true);

-- Politique pour permettre aux admins de gérer tous les plans
CREATE POLICY "Admins can manage all plans" ON public.subscription_plans
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.admin_profiles 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_subscription_plans_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour automatiquement updated_at
CREATE TRIGGER update_subscription_plans_updated_at
  BEFORE UPDATE ON public.subscription_plans
  FOR EACH ROW
  EXECUTE FUNCTION update_subscription_plans_updated_at();

-- Donner les permissions nécessaires
GRANT SELECT ON public.subscription_plans TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.subscription_plans TO service_role;
