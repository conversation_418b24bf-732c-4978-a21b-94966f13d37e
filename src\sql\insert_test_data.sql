-- =====================================================
-- SCRIPT D'INSERTION DE DONNÉES DE TEST
-- À exécuter dans Supabase SQL Editor après avoir créé les tables
-- =====================================================

-- Note: Ce script utilise des UUIDs génériques pour les tests
-- Dans un environnement réel, utilisez les vrais IDs d'utilisateurs

-- 1. INSERTION DE COMMENTAIRES DE TEST
-- =====================================================

-- Générer des UUIDs de test pour les utilisateurs
DO $$
DECLARE
    test_user_1 UUID := '11111111-1111-1111-1111-111111111111';
    test_user_2 UUID := '*************-2222-2222-************';
    test_user_3 UUID := '*************-3333-3333-************';
    test_user_4 UUID := '*************-4444-4444-************';
BEGIN
    -- Commentaires pour le produit 1 (Smartphone)
    INSERT INTO marketcomen (id, product_id, user_id, comment, is_verified_purchase, created_at) VALUES
    (gen_random_uuid(), '1', test_user_1, 'Excellent smartphone ! L''écran est magnifique et la batterie tient toute la journée. Je recommande vivement ce produit.', true, NOW() - INTERVAL '2 days'),
    (gen_random_uuid(), '1', test_user_2, 'Bon produit mais un peu cher. La qualité est au rendez-vous cependant. Livraison rapide et emballage soigné.', false, NOW() - INTERVAL '3 days'),
    (gen_random_uuid(), '1', test_user_3, 'Très satisfait de mon achat ! Le smartphone fonctionne parfaitement et l''interface est intuitive.', true, NOW() - INTERVAL '1 day');

    -- Commentaires pour le produit 2 (Olgane - Huile de beauté)
    INSERT INTO marketcomen (id, product_id, user_id, comment, is_verified_purchase, created_at) VALUES
    (gen_random_uuid(), '2', test_user_1, 'Cette huile Olgane est fantastique ! Ma peau n''a jamais été aussi douce. Produit 100% naturel comme promis.', true, NOW() - INTERVAL '4 days'),
    (gen_random_uuid(), '2', test_user_4, 'Produit naturel de qualité, livraison rapide. Très content de cet achat local. Je recommande !', true, NOW() - INTERVAL '5 days'),
    (gen_random_uuid(), '2', test_user_2, 'Bonne huile, texture agréable et odeur naturelle. Résultats visibles après quelques utilisations.', true, NOW() - INTERVAL '6 days');

    -- Commentaires pour le produit 3 (Crème hydratante)
    INSERT INTO marketcomen (id, product_id, user_id, comment, is_verified_purchase, created_at) VALUES
    (gen_random_uuid(), '3', test_user_2, 'Crème hydratante correcte, texture agréable mais l''effet n''est pas extraordinaire. Rapport qualité-prix moyen.', false, NOW() - INTERVAL '7 days'),
    (gen_random_uuid(), '3', test_user_3, 'Pas mal pour le prix. La crème hydrate bien mais ne fait pas de miracle. Emballage soigné.', false, NOW() - INTERVAL '8 days');

    -- Commentaires pour le produit 5 (Savon au Karité)
    INSERT INTO marketcomen (id, product_id, user_id, comment, is_verified_purchase, created_at) VALUES
    (gen_random_uuid(), '5', test_user_4, 'Savon au karité excellent ! Parfait pour les peaux sensibles comme la mienne. Très doux et hydratant.', true, NOW() - INTERVAL '9 days'),
    (gen_random_uuid(), '5', test_user_1, 'Très bon savon naturel. Mousse bien et laisse la peau douce. Je rachèterai certainement.', true, NOW() - INTERVAL '10 days');

    -- Commentaires pour le produit 6 (Ordinateur Portable)
    INSERT INTO marketcomen (id, product_id, user_id, comment, is_verified_purchase, created_at) VALUES
    (gen_random_uuid(), '6', test_user_1, 'Ordinateur portable très performant, parfait pour le travail. Livraison soignée et rapide. Très satisfaite !', true, NOW() - INTERVAL '11 days'),
    (gen_random_uuid(), '6', test_user_3, 'Excellent laptop ! Rapide, silencieux et avec un bel écran. Parfait pour la programmation et le design.', true, NOW() - INTERVAL '12 days');

END $$;

-- 2. INSERTION DE NOTES DE TEST
-- =====================================================

DO $$
DECLARE
    test_user_1 UUID := '11111111-1111-1111-1111-111111111111';
    test_user_2 UUID := '*************-2222-2222-************';
    test_user_3 UUID := '*************-3333-3333-************';
    test_user_4 UUID := '*************-4444-4444-************';
BEGIN
    -- Notes pour le produit 1 (Smartphone)
    INSERT INTO marketrating (id, product_id, user_id, rating, is_verified_purchase, created_at) VALUES
    (gen_random_uuid(), '1', test_user_1, 5, true, NOW() - INTERVAL '2 days'),
    (gen_random_uuid(), '1', test_user_2, 4, false, NOW() - INTERVAL '3 days'),
    (gen_random_uuid(), '1', test_user_3, 5, true, NOW() - INTERVAL '1 day');

    -- Notes pour le produit 2 (Olgane - Huile de beauté)
    INSERT INTO marketrating (id, product_id, user_id, rating, is_verified_purchase, created_at) VALUES
    (gen_random_uuid(), '2', test_user_1, 5, true, NOW() - INTERVAL '4 days'),
    (gen_random_uuid(), '2', test_user_4, 5, true, NOW() - INTERVAL '5 days'),
    (gen_random_uuid(), '2', test_user_2, 4, true, NOW() - INTERVAL '6 days');

    -- Notes pour le produit 3 (Crème hydratante)
    INSERT INTO marketrating (id, product_id, user_id, rating, is_verified_purchase, created_at) VALUES
    (gen_random_uuid(), '3', test_user_2, 3, false, NOW() - INTERVAL '7 days'),
    (gen_random_uuid(), '3', test_user_3, 3, false, NOW() - INTERVAL '8 days');

    -- Notes pour le produit 5 (Savon au Karité)
    INSERT INTO marketrating (id, product_id, user_id, rating, is_verified_purchase, created_at) VALUES
    (gen_random_uuid(), '5', test_user_4, 5, true, NOW() - INTERVAL '9 days'),
    (gen_random_uuid(), '5', test_user_1, 5, true, NOW() - INTERVAL '10 days');

    -- Notes pour le produit 6 (Ordinateur Portable)
    INSERT INTO marketrating (id, product_id, user_id, rating, is_verified_purchase, created_at) VALUES
    (gen_random_uuid(), '6', test_user_1, 5, true, NOW() - INTERVAL '11 days'),
    (gen_random_uuid(), '6', test_user_3, 5, true, NOW() - INTERVAL '12 days');

END $$;

-- 3. INSERTION DE QUELQUES RÉPONSES AUX COMMENTAIRES
-- =====================================================

DO $$
DECLARE
    test_user_1 UUID := '11111111-1111-1111-1111-111111111111';
    test_user_2 UUID := '*************-2222-2222-************';
    test_user_3 UUID := '*************-3333-3333-************';
    parent_comment_id UUID;
BEGIN
    -- Récupérer l'ID d'un commentaire parent pour le produit 1
    SELECT id INTO parent_comment_id 
    FROM marketcomen 
    WHERE product_id = '1' AND parent_comment_id IS NULL 
    LIMIT 1;

    -- Ajouter une réponse si un commentaire parent existe
    IF parent_comment_id IS NOT NULL THEN
        INSERT INTO marketcomen (id, product_id, user_id, comment, parent_comment_id, is_verified_purchase, created_at) VALUES
        (gen_random_uuid(), '1', test_user_3, 'Je suis d''accord, excellent rapport qualité-prix ! Je l''ai acheté aussi et je ne regrette pas.', parent_comment_id, false, NOW() - INTERVAL '1 day');
    END IF;

    -- Récupérer l'ID d'un commentaire parent pour le produit 2
    SELECT id INTO parent_comment_id 
    FROM marketcomen 
    WHERE product_id = '2' AND parent_comment_id IS NULL 
    LIMIT 1;

    -- Ajouter une réponse si un commentaire parent existe
    IF parent_comment_id IS NOT NULL THEN
        INSERT INTO marketcomen (id, product_id, user_id, comment, parent_comment_id, is_verified_purchase, created_at) VALUES
        (gen_random_uuid(), '2', test_user_2, 'Merci pour ce retour ! J''hésite à l''acheter, votre avis m''aide beaucoup.', parent_comment_id, false, NOW() - INTERVAL '3 days');
    END IF;

END $$;

-- 4. VÉRIFICATION DES DONNÉES INSÉRÉES
-- =====================================================

-- Compter les commentaires par produit
SELECT 
    product_id,
    COUNT(*) as total_comments,
    COUNT(CASE WHEN parent_comment_id IS NULL THEN 1 END) as main_comments,
    COUNT(CASE WHEN parent_comment_id IS NOT NULL THEN 1 END) as replies
FROM marketcomen 
GROUP BY product_id 
ORDER BY product_id;

-- Compter les notes par produit avec moyenne
SELECT 
    product_id,
    COUNT(*) as total_ratings,
    ROUND(AVG(rating), 2) as average_rating,
    COUNT(CASE WHEN is_verified_purchase = true THEN 1 END) as verified_ratings
FROM marketrating 
GROUP BY product_id 
ORDER BY product_id;

-- Tester la vue product_stats
SELECT * FROM product_stats ORDER BY product_id;

-- Tester la vue comments_with_user_info
SELECT 
    product_id,
    comment,
    username,
    is_verified_purchase,
    user_rating,
    created_at
FROM comments_with_user_info 
ORDER BY product_id, created_at DESC;

-- Message de confirmation
SELECT 'Données de test insérées avec succès!' as status;
SELECT 
    (SELECT COUNT(*) FROM marketcomen) as total_comments,
    (SELECT COUNT(*) FROM marketrating) as total_ratings,
    (SELECT COUNT(DISTINCT product_id) FROM marketcomen) as products_with_comments,
    (SELECT COUNT(DISTINCT product_id) FROM marketrating) as products_with_ratings;
