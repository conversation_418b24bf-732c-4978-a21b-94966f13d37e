import { supabase } from '../lib/supabase';
import { UserRole } from '../types';

// Types pour les interactions publicitaires
export interface AdLike {
  id: string;
  campaign_id: string;
  user_id: string;
  created_at: string;
}

export interface AdComment {
  id: string;
  campaign_id: string;
  user_id: string;
  parent_comment_id?: string;
  content: string;
  is_approved: boolean;
  is_flagged: boolean;
  flagged_reason?: string;
  created_at: string;
  updated_at: string;
  // Données jointes
  username?: string;
  profile_picture?: string;
  user_role?: string;
}

export interface AdShare {
  id: string;
  campaign_id: string;
  user_id: string;
  share_type: 'facebook' | 'twitter' | 'whatsapp' | 'email' | 'copy_link' | 'internal';
  shared_at: string;
}

export interface BusinessResponse {
  id: string;
  commentId: string;
  businessId: string;
  content: string;
  createdAt: Date;
  businessName: string;
  businessProfilePicture?: string;
}

export interface AdCommentWithResponse extends AdComment {
  businessResponse?: BusinessResponse;
}

export interface AdEngagementMetrics {
  campaign_id: string;
  total_likes: number;
  total_comments: number;
  total_shares: number;
  total_saves: number;
  engagement_rate: number;
  last_updated: string;
}

class AdInteractionService {
  // =====================================================
  // GESTION DES LIKES
  // =====================================================

  /**
   * Ajouter un like à une publicité
   */
  async likeAd(campaignId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('ad_likes')
        .insert([{
          campaign_id: campaignId,
          user_id: userId
        }]);

      if (error) {
        // Si c'est une erreur de contrainte unique, l'utilisateur a déjà liké
        if (error.code === '23505') {
          console.log('Utilisateur a déjà liké cette publicité');
          return false;
        }
        // Gestion des erreurs RLS
        if (error.code === '42501') {
          console.error('Erreur RLS lors du like:', error);
          throw new Error('Permissions insuffisantes pour liker cette publicité. Vérifiez votre rôle utilisateur.');
        }
        throw error;
      }

      console.log(`👍 Like ajouté pour la campagne ${campaignId} par l'utilisateur ${userId}`);
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'ajout du like:', error);
      return false;
    }
  }

  /**
   * Retirer un like d'une publicité
   */
  async unlikeAd(campaignId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('ad_likes')
        .delete()
        .eq('campaign_id', campaignId)
        .eq('user_id', userId);

      if (error) throw error;

      console.log(`👎 Like retiré pour la campagne ${campaignId} par l'utilisateur ${userId}`);
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression du like:', error);
      return false;
    }
  }

  /**
   * Vérifier si un utilisateur a liké une publicité
   */
  async hasUserLikedAd(campaignId: string, userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('ad_likes')
        .select('id')
        .eq('campaign_id', campaignId)
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return !!data;
    } catch (error) {
      console.error('Erreur lors de la vérification du like:', error);
      return false;
    }
  }

  /**
   * Récupérer le nombre de likes d'une publicité
   */
  async getAdLikesCount(campaignId: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('ad_likes')
        .select('*', { count: 'exact', head: true })
        .eq('campaign_id', campaignId);

      if (error) throw error;
      return count || 0;
    } catch (error) {
      console.error('Erreur lors du comptage des likes:', error);
      return 0;
    }
  }

  // =====================================================
  // GESTION DES COMMENTAIRES
  // =====================================================

  /**
   * Ajouter un commentaire à une publicité
   * Restriction: Seuls les utilisateurs réguliers peuvent commenter les publicités
   * Exception: Les entreprises peuvent répondre aux commentaires sur leurs propres publicités
   */
  async addComment(campaignId: string, userId: string, content: string, parentCommentId?: string): Promise<AdComment | null> {
    try {
      // Vérifier le rôle de l'utilisateur avant d'autoriser le commentaire
      const { data: userProfile, error: userError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', userId)
        .single();

      if (userError) {
        console.error('Erreur lors de la récupération du profil utilisateur:', userError);
        throw new Error('Impossible de vérifier les permissions utilisateur');
      }

      // Vérifier si c'est une entreprise
      if (userProfile.role === UserRole.BUSINESS) {
        // Exception: Permettre aux entreprises de répondre aux commentaires sur leurs propres publicités
        if (parentCommentId) {
          // Vérifier que la campagne appartient à cette entreprise
          const { data: campaign, error: campaignError } = await supabase
            .from('ad_campaigns')
            .select('business_id')
            .eq('id', campaignId)
            .single();

          if (campaignError || !campaign) {
            throw new Error('Impossible de vérifier la propriété de la campagne');
          }

          if (campaign.business_id === userId) {
            console.log(`✅ Entreprise autorisée à répondre sur sa propre publicité: ${campaignId}`);
          } else {
            console.log(`🚫 Tentative de commentaire bloquée: l'entreprise ${userId} ne peut pas commenter la publicité d'une autre entreprise`);
            throw new Error('Les comptes entreprise ne peuvent commenter que leurs propres publicités en réponse aux clients.');
          }
        } else {
          // Empêcher les entreprises de créer des commentaires initiaux sur les publicités
          console.log(`🚫 Tentative de commentaire bloquée: l'entreprise ${userId} ne peut pas créer de commentaire initial`);
          throw new Error('Les comptes entreprise ne peuvent pas commenter les publicités. Seuls les utilisateurs réguliers peuvent interagir avec les publicités.');
        }
      }

      const { data, error } = await supabase
        .from('ad_comments')
        .insert([{
          campaign_id: campaignId,
          user_id: userId,
          content: content.trim(),
          parent_comment_id: parentCommentId || null
        }])
        .select()
        .single();

      if (error) {
        console.error('Erreur Supabase lors de l\'ajout du commentaire:', error);

        // Gestion spécifique des erreurs RLS
        if (error.code === '42501') {
          throw new Error('Permissions insuffisantes pour commenter cette publicité. Les politiques de sécurité bloquent cette action.');
        }

        throw error;
      }

      console.log(`💬 Commentaire ajouté pour la campagne ${campaignId} par l'utilisateur ${userId}`);

      // Note: Les métriques d'engagement sont mises à jour automatiquement par les triggers SQL

      return data;
    } catch (error) {
      console.error('Erreur lors de l\'ajout du commentaire:', error);
      throw error; // Re-throw pour que l'interface puisse afficher le message d'erreur
    }
  }

  /**
   * Récupérer les commentaires d'une publicité
   */
  async getAdComments(campaignId: string, limit: number = 50): Promise<AdComment[]> {
    try {
      const { data, error } = await supabase
        .from('ad_comments_with_user')
        .select('*')
        .eq('campaign_id', campaignId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des commentaires:', error);
      return [];
    }
  }

  /**
   * Supprimer un commentaire
   */
  async deleteComment(commentId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('ad_comments')
        .delete()
        .eq('id', commentId)
        .eq('user_id', userId); // Seulement l'auteur peut supprimer

      if (error) throw error;

      console.log(`🗑️ Commentaire ${commentId} supprimé par l'utilisateur ${userId}`);
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression du commentaire:', error);
      return false;
    }
  }

  /**
   * Récupérer les réponses des entreprises pour des commentaires
   */
  async getBusinessResponses(commentIds: string[]): Promise<BusinessResponse[]> {
    try {
      const { data, error } = await supabase
        .from('ad_business_responses')
        .select(`
          *,
          profiles:business_id (
            username,
            full_name,
            profile_picture
          )
        `)
        .in('comment_id', commentIds)
        .eq('is_approved', true)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data?.map(response => ({
        id: response.id,
        commentId: response.comment_id,
        businessId: response.business_id,
        content: response.content,
        createdAt: new Date(response.created_at),
        businessName: response.profiles?.full_name || response.profiles?.username || 'Entreprise',
        businessProfilePicture: response.profiles?.profile_picture
      })) || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des réponses business:', error);
      return [];
    }
  }

  /**
   * Récupérer les commentaires avec les réponses des entreprises
   */
  async getAdCommentsWithResponses(campaignId: string, limit: number = 50): Promise<AdCommentWithResponse[]> {
    try {
      // Récupérer les commentaires
      const comments = await this.getAdComments(campaignId, limit);

      if (comments.length === 0) return [];

      // Récupérer les réponses des entreprises
      const commentIds = comments.map(c => c.id);
      const responses = await this.getBusinessResponses(commentIds);

      // Associer les réponses aux commentaires
      return comments.map(comment => ({
        ...comment,
        businessResponse: responses.find(r => r.commentId === comment.id)
      }));
    } catch (error) {
      console.error('Erreur lors de la récupération des commentaires avec réponses:', error);
      return [];
    }
  }

  // =====================================================
  // GESTION DES PARTAGES
  // =====================================================

  /**
   * Enregistrer un partage de publicité
   */
  async shareAd(campaignId: string, userId: string, shareType: AdShare['share_type']): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('ad_shares')
        .insert([{
          campaign_id: campaignId,
          user_id: userId,
          share_type: shareType,
          ip_address: null, // Pourrait être ajouté côté serveur
          user_agent: navigator.userAgent
        }]);

      if (error) throw error;

      console.log(`📤 Partage ${shareType} enregistré pour la campagne ${campaignId} par l'utilisateur ${userId}`);
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du partage:', error);
      return false;
    }
  }

  /**
   * Récupérer le nombre de partages d'une publicité
   */
  async getAdSharesCount(campaignId: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('ad_shares')
        .select('*', { count: 'exact', head: true })
        .eq('campaign_id', campaignId);

      if (error) throw error;
      return count || 0;
    } catch (error) {
      console.error('Erreur lors du comptage des partages:', error);
      return 0;
    }
  }

  // =====================================================
  // MÉTRIQUES D'ENGAGEMENT
  // =====================================================

  /**
   * Récupérer les métriques d'engagement d'une publicité
   */
  async getEngagementMetrics(campaignId: string): Promise<AdEngagementMetrics | null> {
    try {
      const { data, error } = await supabase
        .from('ad_engagement_metrics')
        .select('*')
        .eq('campaign_id', campaignId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      console.error('Erreur lors de la récupération des métriques:', error);
      return null;
    }
  }

  /**
   * Récupérer les métriques complètes d'une campagne
   */
  async getCampaignWithEngagement(campaignId: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('ad_campaigns_with_engagement')
        .select('*')
        .eq('id', campaignId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Erreur lors de la récupération de la campagne avec engagement:', error);
      return null;
    }
  }

  // =====================================================
  // UTILITAIRES
  // =====================================================

  /**
   * Partager via les réseaux sociaux avec messages personnalisés
   */
  shareToSocialMedia(campaignId: string, userId: string, platform: string, adTitle: string, adUrl: string): void {
    const encodedTitle = encodeURIComponent(adTitle);
    const encodedUrl = encodeURIComponent(adUrl);

    // Messages personnalisés pour chaque plateforme
    const messages = {
      facebook: `Découvrez cette offre intéressante sur Customeroom !`,
      twitter: `🔥 ${adTitle} - Découvrez cette offre sur @Customeroom`,
      whatsapp: `Salut ! Je viens de voir cette offre intéressante sur Customeroom : ${adTitle}`,
      email: `Offre intéressante sur Customeroom`
    };

    let shareUrl = '';

    switch (platform) {
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodeURIComponent(messages.facebook)}`;
        break;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(messages.twitter)}&url=${encodedUrl}&hashtags=Customeroom,Offre,Business`;
        break;
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodeURIComponent(messages.whatsapp + ' ' + adUrl)}`;
        break;
      case 'email':
        const emailBody = `Bonjour,\n\nJe voulais partager avec vous cette offre intéressante que j'ai trouvée sur Customeroom :\n\n${adTitle}\n\n${adUrl}\n\nCordialement`;
        shareUrl = `mailto:?subject=${encodeURIComponent(messages.email)}&body=${encodeURIComponent(emailBody)}`;
        break;
      default:
        console.error('Plateforme de partage non supportée:', platform);
        return;
    }

    // Enregistrer le partage
    this.shareAd(campaignId, userId, platform as AdShare['share_type']);

    // Ouvrir la fenêtre de partage avec des dimensions optimisées
    const windowFeatures = platform === 'email' ? '' : 'width=600,height=500,scrollbars=yes,resizable=yes';
    window.open(shareUrl, '_blank', windowFeatures);
  }

  /**
   * Copier le lien dans le presse-papiers
   */
  async copyLinkToClipboard(campaignId: string, userId: string, adUrl: string): Promise<boolean> {
    try {
      await navigator.clipboard.writeText(adUrl);

      // Enregistrer le partage
      await this.shareAd(campaignId, userId, 'copy_link');

      console.log('📋 Lien copié dans le presse-papiers');
      return true;
    } catch (error) {
      console.error('Erreur lors de la copie du lien:', error);
      return false;
    }
  }

  /**
   * Sauvegarder une publicité dans les favoris
   */
  async saveAdToFavorites(campaignId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_saved_ads')
        .insert([{
          campaign_id: campaignId,
          user_id: userId,
          saved_at: new Date().toISOString()
        }]);

      if (error) {
        // Si c'est une erreur de contrainte unique, la publicité est déjà sauvegardée
        if (error.code === '23505') {
          console.log('Publicité déjà sauvegardée');
          return true;
        }
        throw error;
      }

      console.log(`💾 Publicité ${campaignId} sauvegardée pour l'utilisateur ${userId}`);
      return true;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      return false;
    }
  }

  /**
   * Vérifier si une publicité est sauvegardée par l'utilisateur
   */
  async isAdSavedByUser(campaignId: string, userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('user_saved_ads')
        .select('id')
        .eq('campaign_id', campaignId)
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return !!data;
    } catch (error) {
      console.error('Erreur lors de la vérification de sauvegarde:', error);
      return false;
    }
  }

  /**
   * Supprimer une publicité des favoris
   */
  async removeAdFromFavorites(campaignId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_saved_ads')
        .delete()
        .eq('campaign_id', campaignId)
        .eq('user_id', userId);

      if (error) throw error;

      console.log(`🗑️ Publicité ${campaignId} supprimée des favoris de l'utilisateur ${userId}`);
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression des favoris:', error);
      return false;
    }
  }
}

export const adInteractionService = new AdInteractionService();
