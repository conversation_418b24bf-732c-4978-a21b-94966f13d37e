-- SCRIPT DE TEST POUR LES BOUTONS D'ALERTES DÉVELOPPÉS
-- Exécutez ce script pour créer des alertes de test avec différents statuts

-- =====================================================
-- 1. CRÉER DES ALERTES POUR TESTER TOUS LES BOUTONS
-- =====================================================

DO $$
DECLARE
    admin_profile_id uuid;
    admin_profile_id_2 uuid;
BEGIN
    RAISE NOTICE '🧪 CRÉATION D''ALERTES POUR TESTER LES BOUTONS';
    RAISE NOTICE '============================================';
    
    -- R<PERSON><PERSON><PERSON>rer des profils admin existants
    SELECT id INTO admin_profile_id FROM admin_profiles LIMIT 1;
    SELECT id INTO admin_profile_id_2 FROM admin_profiles OFFSET 1 LIMIT 1;
    
    IF admin_profile_id IS NULL THEN
        RAISE NOTICE '❌ Aucun profil admin trouvé. Créez d''abord des administrateurs.';
        RETURN;
    END IF;
    
    -- Supprimer les anciennes alertes de test
    DELETE FROM admin_alerts WHERE title LIKE '%Test Bouton%';
    
    -- 1. Alerte OUVERTE pour tester l'assignation
    INSERT INTO admin_alerts (
        alert_type, priority, title, message, details, created_by, status, created_at
    ) VALUES (
        'security',
        'high',
        'Test Bouton - Alerte Ouverte',
        'Cette alerte est ouverte et peut être assignée à un administrateur. Testez le bouton "Prendre en charge".',
        '{"test_type": "assignation", "button_test": "assign", "expected_action": "assign_to_admin"}'::jsonb,
        admin_profile_id,
        'open',
        NOW() - INTERVAL '30 minutes'
    );
    
    -- 2. Alerte EN COURS pour tester la résolution
    INSERT INTO admin_alerts (
        alert_type, priority, title, message, details, assigned_to, created_by, status, created_at
    ) VALUES (
        'system',
        'medium',
        'Test Bouton - Alerte En Cours',
        'Cette alerte est en cours de traitement. Testez le bouton "Marquer comme résolue".',
        '{"test_type": "resolution", "button_test": "resolve", "expected_action": "mark_resolved"}'::jsonb,
        admin_profile_id,
        admin_profile_id,
        'in_progress',
        NOW() - INTERVAL '1 hour'
    );
    
    -- 3. Alerte BASSE PRIORITÉ pour tester l'escalade
    INSERT INTO admin_alerts (
        alert_type, priority, title, message, details, created_by, status, created_at
    ) VALUES (
        'user_action',
        'low',
        'Test Bouton - Escalade Priorité',
        'Cette alerte a une priorité basse. Testez le bouton "Escalader la priorité".',
        '{"test_type": "escalation", "button_test": "escalate", "current_priority": "low", "expected_priority": "medium"}'::jsonb,
        admin_profile_id,
        'open',
        NOW() - INTERVAL '2 hours'
    );
    
    -- 4. Alerte MOYENNE PRIORITÉ pour tester l'escalade vers HAUTE
    INSERT INTO admin_alerts (
        alert_type, priority, title, message, details, created_by, status, created_at
    ) VALUES (
        'database',
        'medium',
        'Test Bouton - Escalade vers Haute',
        'Cette alerte a une priorité moyenne. Testez l\'escalade vers priorité haute.',
        '{"test_type": "escalation", "button_test": "escalate", "current_priority": "medium", "expected_priority": "high"}'::jsonb,
        admin_profile_id,
        'open',
        NOW() - INTERVAL '45 minutes'
    );
    
    -- 5. Alerte HAUTE PRIORITÉ pour tester l'escalade vers CRITIQUE
    INSERT INTO admin_alerts (
        alert_type, priority, title, message, details, created_by, status, created_at
    ) VALUES (
        'performance',
        'high',
        'Test Bouton - Escalade vers Critique',
        'Cette alerte a une priorité haute. Testez l\'escalade vers priorité critique.',
        '{"test_type": "escalation", "button_test": "escalate", "current_priority": "high", "expected_priority": "critical"}'::jsonb,
        admin_profile_id,
        'open',
        NOW() - INTERVAL '15 minutes'
    );
    
    -- 6. Alerte CRITIQUE pour tester que l'escalade n'est pas possible
    INSERT INTO admin_alerts (
        alert_type, priority, title, message, details, created_by, status, created_at
    ) VALUES (
        'security',
        'critical',
        'Test Bouton - Priorité Maximale',
        'Cette alerte a déjà la priorité critique. Le bouton d\'escalade ne devrait pas être disponible.',
        '{"test_type": "escalation", "button_test": "no_escalate", "current_priority": "critical", "note": "escalation_not_possible"}'::jsonb,
        admin_profile_id,
        'open',
        NOW() - INTERVAL '5 minutes'
    );
    
    -- 7. Alerte pour tester l'ignorance
    INSERT INTO admin_alerts (
        alert_type, priority, title, message, details, created_by, status, created_at
    ) VALUES (
        'content',
        'low',
        'Test Bouton - Ignorer Alerte',
        'Cette alerte peut être ignorée. Testez le bouton "Ignorer l\'alerte" avec différentes raisons.',
        '{"test_type": "dismissal", "button_test": "dismiss", "expected_action": "mark_dismissed"}'::jsonb,
        admin_profile_id,
        'open',
        NOW() - INTERVAL '3 hours'
    );
    
    -- 8. Alerte RÉSOLUE pour tester les boutons désactivés
    INSERT INTO admin_alerts (
        alert_type, priority, title, message, details, assigned_to, created_by, status, resolved_at, resolution_notes, created_at
    ) VALUES (
        'deployment',
        'medium',
        'Test Bouton - Alerte Résolue',
        'Cette alerte est déjà résolue. Seuls certains boutons devraient être disponibles.',
        '{"test_type": "resolved", "button_test": "limited_actions", "note": "already_resolved"}'::jsonb,
        admin_profile_id,
        admin_profile_id,
        'resolved',
        NOW() - INTERVAL '30 minutes',
        'Alerte résolue pour test des boutons',
        NOW() - INTERVAL '4 hours'
    );
    
    -- 9. Alerte IGNORÉE pour tester les boutons désactivés
    INSERT INTO admin_alerts (
        alert_type, priority, title, message, details, created_by, status, created_at
    ) VALUES (
        'business',
        'low',
        'Test Bouton - Alerte Ignorée',
        'Cette alerte a été ignorée. Testez quels boutons sont encore disponibles.',
        '{"test_type": "dismissed", "button_test": "limited_actions", "note": "already_dismissed"}'::jsonb,
        admin_profile_id,
        'dismissed',
        NOW() - INTERVAL '6 hours'
    );
    
    -- 10. Alerte avec assignation pour tester la réassignation
    INSERT INTO admin_alerts (
        alert_type, priority, title, message, details, assigned_to, created_by, status, created_at
    ) VALUES (
        'system',
        'high',
        'Test Bouton - Réassignation',
        'Cette alerte est assignée. Testez le bouton "Réassigner" pour changer l\'administrateur.',
        '{"test_type": "reassignment", "button_test": "reassign", "current_assignee": "admin_1", "expected_action": "change_assignee"}'::jsonb,
        admin_profile_id,
        admin_profile_id_2,
        'in_progress',
        NOW() - INTERVAL '2 hours'
    );
    
    RAISE NOTICE '✅ 10 alertes de test créées pour tester tous les boutons';
    
END $$;

-- =====================================================
-- 2. AFFICHER LES ALERTES DE TEST CRÉÉES
-- =====================================================

SELECT 
    '🎯 ALERTES DE TEST CRÉÉES' as info,
    title,
    priority,
    status,
    CASE 
        WHEN assigned_to IS NOT NULL THEN 'Assignée'
        ELSE 'Non assignée'
    END as assignation,
    created_at
FROM admin_alerts 
WHERE title LIKE '%Test Bouton%'
ORDER BY created_at DESC;

-- =====================================================
-- 3. GUIDE DE TEST DES BOUTONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎯 GUIDE DE TEST DES BOUTONS DÉVELOPPÉS';
    RAISE NOTICE '=====================================';
    RAISE NOTICE '';
    RAISE NOTICE '📋 TESTS À EFFECTUER:';
    RAISE NOTICE '';
    RAISE NOTICE '1️⃣ BOUTON "PRENDRE EN CHARGE" / "ASSIGNER":';
    RAISE NOTICE '   • Alerte: "Test Bouton - Alerte Ouverte"';
    RAISE NOTICE '   • Action: Cliquez sur ⋮ puis "Prendre en charge"';
    RAISE NOTICE '   • Test: Modal d''assignation avec liste des admins';
    RAISE NOTICE '   • Vérifiez: Sélection admin + raison obligatoire';
    RAISE NOTICE '';
    RAISE NOTICE '2️⃣ BOUTON "MARQUER COMME RÉSOLUE":';
    RAISE NOTICE '   • Alerte: "Test Bouton - Alerte En Cours"';
    RAISE NOTICE '   • Action: Cliquez sur ⋮ puis "Marquer comme résolue"';
    RAISE NOTICE '   • Test: Modal de résolution avec actions prises';
    RAISE NOTICE '   • Vérifiez: Notes obligatoires + actions optionnelles';
    RAISE NOTICE '';
    RAISE NOTICE '3️⃣ BOUTON "ESCALADER LA PRIORITÉ":';
    RAISE NOTICE '   • Alertes: "Test Bouton - Escalade Priorité" (low→medium)';
    RAISE NOTICE '   •         "Test Bouton - Escalade vers Haute" (medium→high)';
    RAISE NOTICE '   •         "Test Bouton - Escalade vers Critique" (high→critical)';
    RAISE NOTICE '   • Action: Cliquez sur ⋮ puis "Escalader"';
    RAISE NOTICE '   • Test: Modal d''escalade avec impact/urgence';
    RAISE NOTICE '   • Vérifiez: Justification obligatoire';
    RAISE NOTICE '';
    RAISE NOTICE '4️⃣ BOUTON "IGNORER L''ALERTE":';
    RAISE NOTICE '   • Alerte: "Test Bouton - Ignorer Alerte"';
    RAISE NOTICE '   • Action: Cliquez sur ⋮ puis "Ignorer"';
    RAISE NOTICE '   • Test: Modal avec raisons prédéfinies';
    RAISE NOTICE '   • Vérifiez: Option "Autre" avec champ texte';
    RAISE NOTICE '';
    RAISE NOTICE '5️⃣ BOUTON "RÉASSIGNER":';
    RAISE NOTICE '   • Alerte: "Test Bouton - Réassignation"';
    RAISE NOTICE '   • Action: Cliquez sur ⋮ puis "Réassigner"';
    RAISE NOTICE '   • Test: Modal de réassignation';
    RAISE NOTICE '   • Vérifiez: Changement d''administrateur';
    RAISE NOTICE '';
    RAISE NOTICE '6️⃣ BOUTONS DÉSACTIVÉS:';
    RAISE NOTICE '   • Alerte: "Test Bouton - Priorité Maximale"';
    RAISE NOTICE '   • Vérifiez: Bouton escalade non disponible';
    RAISE NOTICE '   • Alerte: "Test Bouton - Alerte Résolue"';
    RAISE NOTICE '   • Vérifiez: Boutons limités pour alertes résolues';
    RAISE NOTICE '';
    RAISE NOTICE '🔍 POINTS À VÉRIFIER:';
    RAISE NOTICE '   ✅ Interface des modals responsive et claire';
    RAISE NOTICE '   ✅ Validation des champs obligatoires';
    RAISE NOTICE '   ✅ Messages d''information contextuels';
    RAISE NOTICE '   ✅ Boutons désactivés selon le statut';
    RAISE NOTICE '   ✅ Actions enregistrées correctement';
    RAISE NOTICE '   ✅ Notifications de succès/erreur';
    RAISE NOTICE '';
    RAISE NOTICE '🎊 TESTEZ MAINTENANT DANS L''INTERFACE !';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- 4. STATISTIQUES DES ALERTES DE TEST
-- =====================================================

SELECT 
    '📊 RÉPARTITION DES ALERTES DE TEST' as info,
    priority,
    status,
    COUNT(*) as count
FROM admin_alerts 
WHERE title LIKE '%Test Bouton%'
GROUP BY priority, status
ORDER BY 
    CASE priority 
        WHEN 'critical' THEN 1 
        WHEN 'high' THEN 2 
        WHEN 'medium' THEN 3 
        WHEN 'low' THEN 4 
    END,
    status;
