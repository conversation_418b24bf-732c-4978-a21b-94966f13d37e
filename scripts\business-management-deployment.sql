-- DÉPLOIEMENT COMPLET DE LA GESTION DES ENTREPRISES
-- Exécutez ce script dans Supabase SQL Editor

-- =====================================================
-- 1. VÉRIFIER ET CRÉER LES COLONNES NÉCESSAIRES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 Vérification de la structure des tables business...';
    
    -- Ajouter les colonnes manquantes si nécessaire
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'business_profiles' AND column_name = 'total_products'
    ) THEN
        ALTER TABLE business_profiles ADD COLUMN total_products INTEGER DEFAULT 0;
        RAISE NOTICE '✅ Colonne total_products ajoutée';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'business_profiles' AND column_name = 'total_sales'
    ) THEN
        ALTER TABLE business_profiles ADD COLUMN total_sales INTEGER DEFAULT 0;
        RAISE NOTICE '✅ Colonne total_sales ajoutée';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'business_profiles' AND column_name = 'total_revenue'
    ) THEN
        ALTER TABLE business_profiles ADD COLUMN total_revenue DECIMAL(15,2) DEFAULT 0;
        RAISE NOTICE '✅ Colonne total_revenue ajoutée';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'business_profiles' AND column_name = 'average_rating'
    ) THEN
        ALTER TABLE business_profiles ADD COLUMN average_rating DECIMAL(3,2) DEFAULT NULL;
        RAISE NOTICE '✅ Colonne average_rating ajoutée';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'business_profiles' AND column_name = 'total_reviews'
    ) THEN
        ALTER TABLE business_profiles ADD COLUMN total_reviews INTEGER DEFAULT 0;
        RAISE NOTICE '✅ Colonne total_reviews ajoutée';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'business_profiles' AND column_name = 'last_activity'
    ) THEN
        ALTER TABLE business_profiles ADD COLUMN last_activity TIMESTAMP DEFAULT NOW();
        RAISE NOTICE '✅ Colonne last_activity ajoutée';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'business_profiles' AND column_name = 'suspension_reason'
    ) THEN
        ALTER TABLE business_profiles ADD COLUMN suspension_reason TEXT DEFAULT NULL;
        RAISE NOTICE '✅ Colonne suspension_reason ajoutée';
    END IF;
END $$;

-- =====================================================
-- 2. CRÉER DES ENTREPRISES DE DÉMONSTRATION
-- =====================================================

DO $$
DECLARE
    business_ids uuid[] := ARRAY[
        gen_random_uuid(),
        gen_random_uuid(),
        gen_random_uuid(),
        gen_random_uuid(),
        gen_random_uuid(),
        gen_random_uuid(),
        gen_random_uuid(),
        gen_random_uuid()
    ];
    i INTEGER;
BEGIN
    RAISE NOTICE '🏢 Création d''entreprises de démonstration...';
    
    -- Entreprise 1: TechCorp Solutions (Vérifiée Premium)
    INSERT INTO profiles (
        id, username, email, role, status, is_verified, 
        profile_picture, country, city, phone, website, bio, created_at, updated_at
    ) VALUES (
        business_ids[1],
        'techcorp_solutions',
        '<EMAIL>',
        'business',
        'contributor',
        true,
        'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=150',
        'France',
        'Paris',
        '+33 1 23 45 67 89',
        'https://techcorp-solutions.demo',
        'Solutions technologiques innovantes pour entreprises',
        NOW() - INTERVAL '6 months',
        NOW()
    ) ON CONFLICT (email) DO UPDATE SET
        is_verified = true,
        updated_at = NOW();

    INSERT INTO business_profiles (
        id, business_name, business_description, business_category,
        business_status, business_verified, total_products, total_sales, 
        total_revenue, average_rating, total_reviews, last_activity,
        created_at, updated_at
    ) VALUES (
        business_ids[1],
        'TechCorp Solutions',
        'Leader en solutions technologiques B2B avec plus de 10 ans d''expérience',
        'Technology',
        'premium',
        true,
        45,
        1250,
        2500000.00,
        4.8,
        156,
        NOW() - INTERVAL '2 hours',
        NOW() - INTERVAL '6 months',
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET
        business_verified = true,
        business_status = 'premium',
        updated_at = NOW();

    -- Entreprise 2: Green Energy Co (Vérifiée)
    INSERT INTO profiles (
        id, username, email, role, status, is_verified, 
        profile_picture, country, city, phone, website, bio, created_at, updated_at
    ) VALUES (
        business_ids[2],
        'green_energy_co',
        '<EMAIL>',
        'business',
        'contributor',
        true,
        'https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=150',
        'France',
        'Lyon',
        '+33 4 56 78 90 12',
        'https://green-energy.demo',
        'Spécialiste en énergies renouvelables et solutions écologiques',
        NOW() - INTERVAL '4 months',
        NOW()
    ) ON CONFLICT (email) DO UPDATE SET
        is_verified = true,
        updated_at = NOW();

    INSERT INTO business_profiles (
        id, business_name, business_description, business_category,
        business_status, business_verified, total_products, total_sales, 
        total_revenue, average_rating, total_reviews, last_activity,
        created_at, updated_at
    ) VALUES (
        business_ids[2],
        'Green Energy Co',
        'Solutions d''énergie renouvelable pour particuliers et entreprises',
        'Energy',
        'verified',
        true,
        28,
        890,
        1800000.00,
        4.6,
        98,
        NOW() - INTERVAL '1 day',
        NOW() - INTERVAL '4 months',
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET
        business_verified = true,
        business_status = 'verified',
        updated_at = NOW();

    -- Entreprise 3: Fashion Boutique (Nouvelle)
    INSERT INTO profiles (
        id, username, email, role, status, is_verified, 
        profile_picture, country, city, phone, bio, created_at, updated_at
    ) VALUES (
        business_ids[3],
        'fashion_boutique_paris',
        '<EMAIL>',
        'business',
        'member',
        false,
        'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=150',
        'France',
        'Paris',
        '+33 1 98 76 54 32',
        'Boutique de mode tendance au cœur de Paris',
        NOW() - INTERVAL '2 weeks',
        NOW()
    ) ON CONFLICT (email) DO UPDATE SET
        updated_at = NOW();

    INSERT INTO business_profiles (
        id, business_name, business_description, business_category,
        business_status, business_verified, total_products, total_sales, 
        total_revenue, average_rating, total_reviews, last_activity,
        created_at, updated_at
    ) VALUES (
        business_ids[3],
        'Fashion Boutique Paris',
        'Mode féminine et masculine, collections exclusives',
        'Fashion',
        'new',
        false,
        12,
        45,
        25000.00,
        4.2,
        8,
        NOW() - INTERVAL '3 hours',
        NOW() - INTERVAL '2 weeks',
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET
        updated_at = NOW();

    -- Entreprise 4: RestaurantDelice (Partenaire)
    INSERT INTO profiles (
        id, username, email, role, status, is_verified, 
        profile_picture, country, city, phone, website, bio, created_at, updated_at
    ) VALUES (
        business_ids[4],
        'restaurant_delice',
        '<EMAIL>',
        'business',
        'leader',
        true,
        'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=150',
        'France',
        'Marseille',
        '+33 4 91 23 45 67',
        'https://restaurant-delice.demo',
        'Restaurant gastronomique, cuisine française traditionnelle',
        NOW() - INTERVAL '1 year',
        NOW()
    ) ON CONFLICT (email) DO UPDATE SET
        is_verified = true,
        updated_at = NOW();

    INSERT INTO business_profiles (
        id, business_name, business_description, business_category,
        business_status, business_verified, total_products, total_sales, 
        total_revenue, average_rating, total_reviews, last_activity,
        created_at, updated_at
    ) VALUES (
        business_ids[4],
        'Restaurant Délice',
        'Restaurant gastronomique proposant une cuisine française raffinée',
        'Restaurant',
        'partner',
        true,
        8,
        2100,
        850000.00,
        4.9,
        245,
        NOW() - INTERVAL '30 minutes',
        NOW() - INTERVAL '1 year',
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET
        business_verified = true,
        business_status = 'partner',
        updated_at = NOW();

    -- Entreprise 5: StartupInnovation (En attente)
    INSERT INTO profiles (
        id, username, email, role, status, is_verified, 
        profile_picture, country, city, bio, created_at, updated_at
    ) VALUES (
        business_ids[5],
        'startup_innovation',
        '<EMAIL>',
        'business',
        'newbie',
        false,
        'https://images.unsplash.com/photo-1549923746-c502d488b3ea?w=150',
        'France',
        'Toulouse',
        'Startup technologique spécialisée en IA et machine learning',
        NOW() - INTERVAL '1 week',
        NOW()
    ) ON CONFLICT (email) DO UPDATE SET
        updated_at = NOW();

    INSERT INTO business_profiles (
        id, business_name, business_description, business_category,
        business_status, business_verified, total_products, total_sales, 
        total_revenue, average_rating, total_reviews, last_activity,
        created_at, updated_at
    ) VALUES (
        business_ids[5],
        'Startup Innovation',
        'Solutions d''intelligence artificielle pour entreprises',
        'Technology',
        'new',
        false,
        3,
        0,
        0.00,
        NULL,
        0,
        NOW() - INTERVAL '2 days',
        NOW() - INTERVAL '1 week',
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET
        updated_at = NOW();

    -- Entreprise 6: Fitness Center (Mise en avant)
    INSERT INTO profiles (
        id, username, email, role, status, is_verified, 
        profile_picture, country, city, phone, bio, created_at, updated_at
    ) VALUES (
        business_ids[6],
        'fitness_center_elite',
        '<EMAIL>',
        'business',
        'contributor',
        true,
        'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=150',
        'France',
        'Nice',
        '+33 4 93 12 34 56',
        'Salle de sport premium avec équipements dernière génération',
        NOW() - INTERVAL '8 months',
        NOW()
    ) ON CONFLICT (email) DO UPDATE SET
        is_verified = true,
        updated_at = NOW();

    INSERT INTO business_profiles (
        id, business_name, business_description, business_category,
        business_status, business_verified, total_products, total_sales, 
        total_revenue, average_rating, total_reviews, last_activity,
        created_at, updated_at
    ) VALUES (
        business_ids[6],
        'Fitness Center Elite',
        'Salle de sport haut de gamme avec coaching personnalisé',
        'Sports & Fitness',
        'featured',
        true,
        15,
        650,
        320000.00,
        4.7,
        89,
        NOW() - INTERVAL '4 hours',
        NOW() - INTERVAL '8 months',
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET
        business_verified = true,
        business_status = 'featured',
        updated_at = NOW();

    RAISE NOTICE '✅ Entreprises de démonstration créées avec succès';
END $$;

-- =====================================================
-- 3. CRÉER UNE ALERTE DE DÉPLOIEMENT
-- =====================================================

DO $$
DECLARE
    admin_profile_id uuid;
    total_businesses integer;
    verified_businesses integer;
    premium_businesses integer;
BEGIN
    SELECT id INTO admin_profile_id FROM admin_profiles LIMIT 1;
    
    -- Compter les statistiques
    SELECT COUNT(*) INTO total_businesses FROM business_profiles;
    SELECT COUNT(*) INTO verified_businesses FROM business_profiles WHERE business_verified = true;
    SELECT COUNT(*) INTO premium_businesses FROM business_profiles WHERE business_status = 'premium';
    
    IF admin_profile_id IS NOT NULL THEN
        INSERT INTO admin_alerts (
            alert_type, 
            priority, 
            title, 
            message, 
            details, 
            created_by, 
            status
        ) VALUES (
            'deployment', 
            'high', 
            'Gestion des Entreprises Complètement Déployée', 
            FORMAT('Le système complet de gestion des entreprises est maintenant opérationnel. %s entreprises au total, %s vérifiées, %s premium.', total_businesses, verified_businesses, premium_businesses), 
            FORMAT('{"deployment": "business_management", "stats": {"total": %s, "verified": %s, "premium": %s}, "features": ["advanced_search", "bulk_actions", "business_details", "verification", "status_management", "performance_metrics", "export", "pagination"]}', total_businesses, verified_businesses, premium_businesses)::jsonb, 
            admin_profile_id, 
            'open'
        );
        
        RAISE NOTICE '✅ Alerte de déploiement créée';
    END IF;
END $$;

-- =====================================================
-- 4. RÉSUMÉ FINAL
-- =====================================================

DO $$
DECLARE
    total_businesses integer;
    verified_businesses integer;
    premium_businesses integer;
    new_businesses integer;
    partner_businesses integer;
    featured_businesses integer;
BEGIN
    -- Statistiques finales
    SELECT COUNT(*) INTO total_businesses FROM business_profiles;
    SELECT COUNT(*) INTO verified_businesses FROM business_profiles WHERE business_verified = true;
    SELECT COUNT(*) INTO premium_businesses FROM business_profiles WHERE business_status = 'premium';
    SELECT COUNT(*) INTO new_businesses FROM business_profiles WHERE business_status = 'new';
    SELECT COUNT(*) INTO partner_businesses FROM business_profiles WHERE business_status = 'partner';
    SELECT COUNT(*) INTO featured_businesses FROM business_profiles WHERE business_status = 'featured';
    
    RAISE NOTICE '';
    RAISE NOTICE '🎊 ===============================================';
    RAISE NOTICE '🎊 GESTION DES ENTREPRISES COMPLÈTEMENT DÉPLOYÉE !';
    RAISE NOTICE '🎊 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '📊 STATISTIQUES FINALES:';
    RAISE NOTICE '   🏢 Total entreprises: %', total_businesses;
    RAISE NOTICE '   ✅ Vérifiées: % (%.1f%%)', verified_businesses, (verified_businesses::float / GREATEST(total_businesses, 1) * 100);
    RAISE NOTICE '   💎 Premium: %', premium_businesses;
    RAISE NOTICE '   🆕 Nouvelles: %', new_businesses;
    RAISE NOTICE '   🤝 Partenaires: %', partner_businesses;
    RAISE NOTICE '   ⭐ Mises en avant: %', featured_businesses;
    RAISE NOTICE '';
    RAISE NOTICE '🚀 FONCTIONNALITÉS DISPONIBLES:';
    RAISE NOTICE '';
    RAISE NOTICE '🔍 RECHERCHE ET FILTRES:';
    RAISE NOTICE '   ✅ Recherche globale (nom, email, utilisateur)';
    RAISE NOTICE '   ✅ Filtres par statut (Nouveau → Partenaire)';
    RAISE NOTICE '   ✅ Filtres par vérification';
    RAISE NOTICE '   ✅ Filtres par catégorie';
    RAISE NOTICE '   ✅ Tri dynamique et pagination';
    RAISE NOTICE '';
    RAISE NOTICE '🏢 GESTION DES ENTREPRISES:';
    RAISE NOTICE '   ✅ Vue détaillée complète';
    RAISE NOTICE '   ✅ Vérification d''entreprises';
    RAISE NOTICE '   ✅ Changement de statut';
    RAISE NOTICE '   ✅ Suspension/Activation';
    RAISE NOTICE '   ✅ Métriques de performance';
    RAISE NOTICE '   ✅ Accès au profil public';
    RAISE NOTICE '';
    RAISE NOTICE '📊 MÉTRIQUES AVANCÉES:';
    RAISE NOTICE '   ✅ Chiffre d''affaires total';
    RAISE NOTICE '   ✅ Nombre de produits';
    RAISE NOTICE '   ✅ Notes et avis clients';
    RAISE NOTICE '   ✅ Statistiques de vente';
    RAISE NOTICE '';
    RAISE NOTICE '⚡ ACTIONS EN LOT:';
    RAISE NOTICE '   ✅ Sélection multiple';
    RAISE NOTICE '   ✅ Vérification en masse';
    RAISE NOTICE '   ✅ Activation en masse';
    RAISE NOTICE '   ✅ Suspension en masse';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 TESTEZ MAINTENANT:';
    RAISE NOTICE '1. Allez dans "Gestion Entreprises"';
    RAISE NOTICE '2. Explorez les filtres et la recherche';
    RAISE NOTICE '3. Cliquez sur 👁️ pour voir les détails';
    RAISE NOTICE '4. Testez les actions avec ⋮';
    RAISE NOTICE '5. Utilisez les actions en lot';
    RAISE NOTICE '6. Exportez les données';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 SYSTÈME PRÊT POUR LA PRODUCTION !';
    RAISE NOTICE '';
END $$;
