# 🔧 Guide de Correction du Système de Likes

## 🎯 **Problème Identifié**

Le bouton "J'aime" ne fonctionne pas car **la table `posts` n'a pas de colonne `likes`**.

### ❌ **Erreur Actuelle**
```sql
-- Cette requête échoue car la colonne 'likes' n'existe pas
UPDATE posts SET likes = [...] WHERE id = 'post-id';
```

## 🛠️ **Solution : Ajouter la Colonne Likes**

### **Étape 1 : Appliquer la Migration**

1. **Ouvrez Supabase Dashboard**
   - Allez sur [supabase.com](https://supabase.com)
   - Connectez-vous à votre projet

2. **Accédez à l'Éditeur SQL**
   - Cliquez sur "SQL Editor" dans le menu de gauche
   - Créez une nouvelle requête

3. **Exécutez le Script de Migration (Version Minimale)** ⭐
   - Copiez le contenu du fichier `scripts/add-likes-minimal.sql`
   - Collez-le dans l'éditeur SQL
   - Cliquez sur "Run" pour exécuter

### **Étape 2 : Vérifier la Migration**

1. **Exécutez le Script de Test**
   - Copiez le contenu du fichier `scripts/test-likes-minimal.sql`
   - Exécutez-le dans l'éditeur SQL
   - Vérifiez que tous les tests passent ✅

2. **Vérifiez la Structure**
   ```sql
   -- Vérifier que la colonne existe
   SELECT column_name, data_type 
   FROM information_schema.columns 
   WHERE table_name = 'posts' 
   AND column_name IN ('likes', 'shares');
   ```

## 📋 **Changements Apportés**

### **1. 🗄️ Nouvelles Colonnes**
```sql
-- Colonne pour les likes
ALTER TABLE posts ADD COLUMN likes UUID[] DEFAULT ARRAY[]::UUID[];

-- Colonne pour les partages
ALTER TABLE posts ADD COLUMN shares UUID[] DEFAULT ARRAY[]::UUID[];
```

### **2. 🚀 Index pour les Performances**
```sql
-- Index GIN pour les recherches rapides
CREATE INDEX posts_likes_idx ON posts USING GIN (likes);
CREATE INDEX posts_recommendations_idx ON posts USING GIN (recommendations);
CREATE INDEX posts_shares_idx ON posts USING GIN (shares);
```

### **3. 📊 Vue Mise à Jour**
```sql
-- La vue posts_with_author_details inclut maintenant les likes et shares
CREATE OR REPLACE VIEW posts_with_author_details AS
SELECT p.*, p.likes, p.shares, p.recommendations, ...
```

### **4. 🔧 Fonctions Utilitaires**
```sql
-- Fonctions pour ajouter/retirer des likes
add_like_to_post(post_id UUID, user_id UUID)
remove_like_from_post(post_id UUID, user_id UUID)
```

## 🧪 **Test du Système**

### **Après la Migration :**

1. **Redémarrez l'Application**
   ```bash
   # Arrêtez le serveur (Ctrl+C)
   npm run dev
   ```

2. **Testez le Bouton "J'aime"**
   - Connectez-vous avec un compte utilisateur standard
   - Cliquez sur "J'aime" sur un post
   - Vérifiez que le compteur change : "J'aime (1)"

3. **Consultez les Logs**
   - Ouvrez la console du navigateur (F12)
   - Recherchez les logs commençant par 🖱️, 🔄, ✅

### **Logs Attendus :**
```
🖱️ Clic sur le bouton J'aime détecté
👤 Utilisateur actuel: {id: "...", username: "..."}
📝 Post ID: "..."
✅ Appel de likePost avec: {postId: "...", userId: "..."}
🔄 Début de likePost: {postId: "...", userId: "..."}
📝 Post trouvé: "Titre du post"
👍 Likes actuels: []
👍 Likes mis à jour: ["user-id"]
✅ État local mis à jour
💾 Mise à jour en base de données...
✅ Mise à jour en base réussie
```

## 🎯 **Fonctionnalités Activées**

### **✅ Après la Migration :**
- ✅ Bouton "J'aime" fonctionnel
- ✅ Compteur de likes en temps réel
- ✅ Notifications de likes
- ✅ Mise à jour de la base de données
- ✅ Restrictions pour les entreprises
- ✅ Logs de debug détaillés

### **🔄 Comportement Attendu :**
1. **Clic sur "J'aime"** → Compteur passe de 0 à 1
2. **Re-clic** → Compteur repasse à 0 (unlike)
3. **Entreprises** → Bouton grisé avec tooltip
4. **Non-connectés** → Bouton désactivé

## 🚨 **Dépannage**

### **Si le bouton ne fonctionne toujours pas :**

1. **Vérifiez la Console**
   - Erreurs JavaScript ?
   - Erreurs Supabase ?

2. **Vérifiez la Base de Données**
   ```sql
   -- Vérifier qu'un post a bien la colonne likes
   SELECT id, likes, shares FROM posts LIMIT 1;
   ```

3. **Vérifiez les Permissions RLS**
   ```sql
   -- Vérifier les politiques RLS
   SELECT * FROM pg_policies WHERE tablename = 'posts';
   ```

4. **Testez Manuellement**
   ```sql
   -- Test manuel d'ajout de like
   UPDATE posts 
   SET likes = ARRAY['user-id-here']::UUID[] 
   WHERE id = 'post-id-here';
   ```

## 🎉 **Résultat Final**

Après avoir appliqué cette migration, le système de likes sera **entièrement fonctionnel** avec :

- 👍 **Likes** sur les posts
- 🔄 **Partages** (préparé pour le futur)
- 🎯 **Recommandations** (déjà existant)
- 📊 **Métriques** en temps réel
- 🔔 **Notifications** automatiques

**Le bouton "J'aime" fonctionnera parfaitement !** 🎯

## 📁 **Fichiers Créés**

- `scripts/add-likes-minimal.sql` - Script d'installation minimal ⭐ **RECOMMANDÉ**
- `scripts/test-likes-minimal.sql` - Script de test minimal ⭐ **RECOMMANDÉ**
- `scripts/add-likes-simple.sql` - Script d'installation simplifié
- `scripts/test-likes-simple.sql` - Script de test simplifié
- `scripts/add-likes-to-posts.sql` - Script d'installation complet (avec vue)
- `scripts/test-likes-system.sql` - Script de test complet
- `supabase/migrations/20250131000000_add_likes_to_posts.sql` - Migration principale
- `GUIDE_CORRECTION_LIKES.md` - Guide détaillé

## ⚠️ **Erreurs SQL Corrigées**

**Erreur 1:** `column "c.created_at" must appear in the GROUP BY clause`
**Erreur 2:** `cannot change name of view column "author_username" to "likes"`

**Solution:** Création d'une version minimale qui ajoute UNIQUEMENT les colonnes nécessaires sans toucher aux vues existantes.

**Utilisez les scripts `*-minimal.sql` pour une installation garantie sans erreur.**
