import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { UserRole } from '../types';
import { getAdPermissions } from '../utils/adPermissions';
import FeedAdCard, { FeedAd } from '../components/ads/FeedAdCard';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import Button from '../components/ui/Button';
import { User, Building2, Shield, TestTube } from 'lucide-react';
import { DEFAULT_IMAGES_SIZED } from '../constants/defaultImages';

// Publicité de test avec UUID valide
const testAd: FeedAd = {
  id: '00000000-0000-0000-0000-000000000001',
  title: 'Test Restriction Entreprises',
  description: 'Cette publicité sert à tester les restrictions de commentaires pour les comptes entreprise.',
  imageUrl: DEFAULT_IMAGES_SIZED.PROMO_BANNER,
  targetUrl: '#',
  businessName: 'Test Business',
  businessLogo: DEFAULT_IMAGES_SIZED.BUSINESS_LOGO
};

const AdRestrictionsTestPage: React.FC = () => {
  const { currentUser, simulateBusinessUser } = useAuth();
  const [testRole, setTestRole] = useState<UserRole | null>(null);

  // Obtenir les permissions pour le rôle actuel ou de test
  const roleToTest = testRole || currentUser?.role;
  const permissions = getAdPermissions(roleToTest);

  const handleRoleTest = (role: UserRole) => {
    setTestRole(role);
  };

  const resetTest = () => {
    setTestRole(null);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* En-tête */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <TestTube className="text-blue-600" size={24} />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Test des Restrictions - Publicités
              </h1>
              <p className="text-gray-600">
                Testez les restrictions de commentaires pour les différents types de comptes
              </p>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Informations utilisateur actuel */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">Utilisateur Actuel</h2>
        </CardHeader>
        <CardBody>
          {currentUser ? (
            <div className="space-y-2">
              <p><strong>Nom :</strong> {currentUser.username}</p>
              <p><strong>Rôle :</strong> 
                <span className={`ml-2 px-2 py-1 rounded text-sm ${
                  currentUser.role === UserRole.BUSINESS 
                    ? 'bg-blue-100 text-blue-800' 
                    : currentUser.role === UserRole.ADMIN
                    ? 'bg-purple-100 text-purple-800'
                    : 'bg-green-100 text-green-800'
                }`}>
                  {currentUser.role}
                </span>
              </p>
            </div>
          ) : (
            <p className="text-gray-500">Aucun utilisateur connecté</p>
          )}
        </CardBody>
      </Card>

      {/* Simulateur de rôles */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">Simulateur de Rôles</h2>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            <p className="text-gray-600">
              Testez les restrictions avec différents rôles utilisateur :
            </p>
            
            <div className="flex flex-wrap gap-3">
              <Button
                variant={testRole === UserRole.STANDARD ? 'primary' : 'outline'}
                leftIcon={<User size={16} />}
                onClick={() => handleRoleTest(UserRole.STANDARD)}
              >
                Utilisateur Standard
              </Button>
              
              <Button
                variant={testRole === UserRole.BUSINESS ? 'primary' : 'outline'}
                leftIcon={<Building2 size={16} />}
                onClick={() => handleRoleTest(UserRole.BUSINESS)}
              >
                Compte Entreprise
              </Button>
              
              <Button
                variant={testRole === UserRole.ADMIN ? 'primary' : 'outline'}
                leftIcon={<Shield size={16} />}
                onClick={() => handleRoleTest(UserRole.ADMIN)}
              >
                Administrateur
              </Button>
              
              <Button
                variant="outline"
                onClick={resetTest}
              >
                Réinitialiser
              </Button>
            </div>

            {testRole && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Mode Test :</strong> Simulation du rôle "{testRole}"
                </p>
              </div>
            )}
          </div>
        </CardBody>
      </Card>

      {/* Permissions actuelles */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">
            Permissions pour : {roleToTest || 'Non défini'}
          </h2>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className={`p-3 rounded-lg border ${
              permissions.canComment 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <h3 className="font-medium">Commentaires</h3>
              <p className={`text-sm ${
                permissions.canComment ? 'text-green-700' : 'text-red-700'
              }`}>
                {permissions.canComment ? '✅ Autorisé' : '🚫 Bloqué'}
              </p>
              {permissions.restrictions.comment && (
                <p className="text-xs text-gray-600 mt-1">
                  {permissions.restrictions.comment}
                </p>
              )}
            </div>

            <div className={`p-3 rounded-lg border ${
              permissions.canLike 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <h3 className="font-medium">Likes</h3>
              <p className={`text-sm ${
                permissions.canLike ? 'text-green-700' : 'text-red-700'
              }`}>
                {permissions.canLike ? '✅ Autorisé' : '🚫 Bloqué'}
              </p>
            </div>

            <div className={`p-3 rounded-lg border ${
              permissions.canShare 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <h3 className="font-medium">Partages</h3>
              <p className={`text-sm ${
                permissions.canShare ? 'text-green-700' : 'text-red-700'
              }`}>
                {permissions.canShare ? '✅ Autorisé' : '🚫 Bloqué'}
              </p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Publicité de test */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">Publicité de Test</h2>
        </CardHeader>
        <CardBody>
          <p className="text-gray-600 mb-4">
            Testez les interactions avec cette publicité selon le rôle sélectionné :
          </p>
          
          <div className="max-w-md mx-auto">
            <FeedAdCard ad={testAd} />
          </div>
        </CardBody>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold">Instructions de Test</h2>
        </CardHeader>
        <CardBody>
          <div className="space-y-3 text-sm">
            <div>
              <h3 className="font-medium text-green-700">✅ Utilisateur Standard</h3>
              <p>Peut commenter, liker et partager les publicités</p>
            </div>
            
            <div>
              <h3 className="font-medium text-red-700">🚫 Compte Entreprise</h3>
              <p>Ne peut PAS commenter, mais peut liker et partager</p>
              <p className="text-xs text-gray-600">
                Le bouton commentaire doit être désactivé avec un message explicatif
              </p>
            </div>
            
            <div>
              <h3 className="font-medium text-purple-700">👑 Administrateur</h3>
              <p>Peut tout faire sans restriction</p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default AdRestrictionsTestPage;
