/* Modern Business Dashboard - Styles Ultra-Professionnels */

/* Variables CSS pour la cohérence */
:root {
  --primary-blue: #3b82f6;
  --primary-blue-dark: #2563eb;
  --success-green: #10b981;
  --success-green-dark: #059669;
  --warning-amber: #f59e0b;
  --warning-amber-dark: #d97706;
  --danger-red: #ef4444;
  --danger-red-dark: #dc2626;
  --purple: #8b5cf6;
  --purple-dark: #7c3aed;
  --indigo: #6366f1;
  --indigo-dark: #4f46e5;
  --teal: #14b8a6;
  --teal-dark: #0d9488;
  
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  --border-radius: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Animations personnalisées */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* Styles globaux pour le dashboard */
.modern-business-dashboard {
  min-height: 100vh;
  background: var(--gray-50);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header avec gradient */
.dashboard-header-gradient {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
  position: relative;
  overflow: hidden;
}

.dashboard-header-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

/* Cards avec effets modernes */
.modern-card {
  background: white;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-200);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.modern-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-blue), var(--success-green), var(--warning-amber));
  opacity: 0;
  transition: var(--transition);
}

.modern-card:hover::before {
  opacity: 1;
}

/* Métriques avec gradients */
.metric-card-gradient {
  position: relative;
  overflow: hidden;
  border: none;
  color: white;
  transition: var(--transition);
}

.metric-card-gradient:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.metric-card-gradient::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  transition: var(--transition);
  opacity: 0;
}

.metric-card-gradient:hover::after {
  opacity: 1;
  animation: pulse 2s infinite;
}

/* Couleurs des métriques */
.metric-revenue {
  background: linear-gradient(135deg, var(--success-green) 0%, var(--success-green-dark) 100%);
}

.metric-orders {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
}

.metric-products {
  background: linear-gradient(135deg, var(--purple) 0%, var(--purple-dark) 100%);
}

.metric-rating {
  background: linear-gradient(135deg, var(--warning-amber) 0%, var(--warning-amber-dark) 100%);
}

.metric-reviews {
  background: linear-gradient(135deg, var(--indigo) 0%, var(--indigo-dark) 100%);
}

.metric-conversion {
  background: linear-gradient(135deg, var(--teal) 0%, var(--teal-dark) 100%);
}

/* Boutons d'action rapide */
.quick-action-modern {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  padding: 1rem;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.quick-action-modern:hover {
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.quick-action-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s;
}

.quick-action-modern:hover::before {
  left: 100%;
}

/* Alertes modernes */
.alert-modern {
  border-radius: var(--border-radius-lg);
  border-left: 4px solid;
  background: white;
  box-shadow: var(--shadow);
  transition: var(--transition);
  animation: fadeInUp 0.5s ease-out;
}

.alert-modern:hover {
  box-shadow: var(--shadow-md);
  transform: translateX(4px);
}

.alert-urgent {
  border-left-color: var(--danger-red);
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.alert-warning {
  border-left-color: var(--warning-amber);
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.alert-info {
  border-left-color: var(--primary-blue);
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.alert-success {
  border-left-color: var(--success-green);
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

/* Activité récente */
.activity-item-modern {
  padding: 1rem;
  border-radius: var(--border-radius);
  background: var(--gray-50);
  transition: var(--transition);
  border: 1px solid transparent;
}

.activity-item-modern:hover {
  background: white;
  border-color: var(--gray-200);
  box-shadow: var(--shadow-sm);
  transform: translateX(4px);
}

/* Badges de statut */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-completed {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: var(--success-green-dark);
}

.status-pending {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: var(--warning-amber-dark);
}

.status-processing {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: var(--primary-blue-dark);
}

/* Responsive design */
@media (max-width: 768px) {
  .modern-business-dashboard {
    padding: 1rem;
  }
  
  .metric-card-gradient {
    margin-bottom: 1rem;
  }
  
  .dashboard-header-gradient {
    padding: 2rem 1rem;
  }
}

/* Animations d'entrée */
.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

/* Effets de focus pour l'accessibilité */
.modern-card:focus-within,
.quick-action-modern:focus,
.alert-modern:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Scrollbar personnalisée */
.modern-business-dashboard ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.modern-business-dashboard ::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 3px;
}

.modern-business-dashboard ::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 3px;
}

.modern-business-dashboard ::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}
