import React from 'react';
import ToastTester from '../components/ui/ToastTester';

/**
 * Page de test pour le système de notifications Toast
 */
const ToastTestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🍞 Test du Système Toast
          </h1>
          <p className="text-gray-600">
            Testez le fonctionnement des notifications toast dans Customeroom
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Composant de test */}
          <div>
            <ToastTester />
          </div>

          {/* Documentation */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold mb-4 text-gray-800">
              📚 Documentation
            </h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-gray-700 mb-2">Types de Toast</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center gap-2">
                    <span className="w-4 h-4 bg-green-500 rounded"></span>
                    <strong>Succès :</strong> Actions réussies, confirmations
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-4 h-4 bg-red-500 rounded"></span>
                    <strong>Erreur :</strong> Erreurs, échecs d'opération
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-4 h-4 bg-blue-500 rounded"></span>
                    <strong>Info :</strong> Informations générales
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-4 h-4 bg-yellow-500 rounded"></span>
                    <strong>Warning :</strong> Avertissements, attention
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-gray-700 mb-2">Utilisation</h3>
                <div className="bg-gray-100 p-3 rounded text-xs font-mono">
                  <div className="text-gray-800">
                    <div>import &#123; useToast &#125; from '../ui/ToastContainer';</div>
                    <br />
                    <div>const &#123; showSuccess &#125; = useToast();</div>
                    <div>showSuccess('Titre', 'Message');</div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-700 mb-2">Fonctionnalités</h3>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>✅ Auto-fermeture après 4 secondes</li>
                  <li>✅ Fermeture manuelle avec bouton X</li>
                  <li>✅ Animations fluides d'entrée/sortie</li>
                  <li>✅ Pile de notifications multiples</li>
                  <li>✅ Responsive design</li>
                  <li>✅ Fallback avec alert() si pas de provider</li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-gray-700 mb-2">Exemples d'Usage</h3>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• Partage de publicité réussi</li>
                  <li>• Like ajouté/retiré</li>
                  <li>• Commentaire publié</li>
                  <li>• Erreur de connexion</li>
                  <li>• Sauvegarde en favoris</li>
                  <li>• Téléchargement d'image</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Statut du ToastProvider */}
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4 text-gray-800">
            🔧 Statut du ToastProvider
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="font-semibold text-green-800">Configuré</span>
              </div>
              <p className="text-sm text-green-700">
                ToastProvider ajouté dans App.tsx
              </p>
            </div>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="font-semibold text-blue-800">Fallback</span>
              </div>
              <p className="text-sm text-blue-700">
                Alert() en cas d'erreur de contexte
              </p>
            </div>
            
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                <span className="font-semibold text-purple-800">Intégré</span>
              </div>
              <p className="text-sm text-purple-700">
                Utilisé dans FeedAdCard et autres
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToastTestPage;
