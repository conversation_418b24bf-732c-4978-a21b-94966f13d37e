-- Script pour vérifier la structure de la table business_subscriptions
-- Exécuter dans l'interface SQL de Supabase

-- 1. Vérifier si la table existe
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public'
   AND table_name = 'business_subscriptions'
);

-- 2. Lister toutes les colonnes de la table business_subscriptions
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'business_subscriptions' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. Vérifier les contraintes
SELECT 
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
WHERE tc.table_name = 'business_subscriptions'
AND tc.table_schema = 'public';

-- 4. Vérifier s'il y a des données dans la table
SELECT COUNT(*) as total_rows FROM business_subscriptions;

-- 5. Voir un exemple de structure (si des données existent)
SELECT * FROM business_subscriptions LIMIT 1;
