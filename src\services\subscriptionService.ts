import { supabase } from '../lib/supabase';

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  duration: number; // en jours
  features: string[];
  description: string;
  isActive: boolean;
}

export interface BusinessSubscription {
  id: string;
  business_id: string;
  plan_id: string;
  start_date: string;
  end_date: string;
  is_active: boolean;
  is_trial: boolean;
  auto_renew: boolean;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionStatus {
  isActive: boolean;
  plan: string;
  planName: string;
  startDate: string;
  endDate: string;
  daysRemaining: number;
  isTrialPeriod: boolean;
  autoRenew: boolean;
}

export class SubscriptionService {
  
  /**
   * Récupère le statut d'abonnement d'une entreprise
   */
  static async getBusinessSubscriptionStatus(businessId: string): Promise<SubscriptionStatus | null> {
    try {
      console.log('🔍 Récupération du statut d\'abonnement pour:', businessId);

      // Récupérer l'abonnement actuel
      const { data: subscription, error } = await supabase
        .from('business_subscriptions')
        .select(`
          *,
          subscription_plans (
            id,
            name,
            price,
            duration,
            description
          )
        `)
        .eq('business_id', businessId)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('❌ Erreur lors de la récupération de l\'abonnement:', error);
        throw error;
      }

      // Si aucun abonnement actif, créer un essai gratuit
      if (!subscription) {
        console.log('📝 Aucun abonnement trouvé, création d\'un essai gratuit...');
        return await this.createFreeTrial(businessId);
      }

      // Calculer les jours restants
      const endDate = new Date(subscription.end_date);
      const now = new Date();
      const daysRemaining = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));

      const status: SubscriptionStatus = {
        isActive: subscription.is_active && daysRemaining > 0,
        plan: subscription.plan_id,
        planName: subscription.subscription_plans?.name || 'Plan inconnu',
        startDate: subscription.start_date,
        endDate: subscription.end_date,
        daysRemaining,
        isTrialPeriod: subscription.is_trial,
        autoRenew: subscription.auto_renew
      };

      // Si l'abonnement a expiré, le désactiver
      if (daysRemaining <= 0 && subscription.is_active) {
        await this.deactivateSubscription(subscription.id);
        status.isActive = false;
      }

      console.log('✅ Statut d\'abonnement récupéré:', status);
      return status;

    } catch (error) {
      console.error('❌ Erreur dans getBusinessSubscriptionStatus:', error);
      return null;
    }
  }

  /**
   * Crée un essai gratuit de 7 jours pour une nouvelle entreprise
   */
  static async createFreeTrial(businessId: string): Promise<SubscriptionStatus> {
    try {
      console.log('🎁 Création d\'un essai gratuit pour:', businessId);

      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(startDate.getDate() + 7); // 7 jours d'essai

      // Insérer l'abonnement d'essai
      const { data: subscription, error } = await supabase
        .from('business_subscriptions')
        .insert({
          business_id: businessId,
          plan_id: 'free-trial',
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          is_active: true,
          is_trial: true,
          auto_renew: false
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Erreur lors de la création de l\'essai gratuit:', error);
        throw error;
      }

      const status: SubscriptionStatus = {
        isActive: true,
        plan: 'free-trial',
        planName: 'Essai Gratuit',
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        daysRemaining: 7,
        isTrialPeriod: true,
        autoRenew: false
      };

      console.log('✅ Essai gratuit créé avec succès');
      return status;

    } catch (error) {
      console.error('❌ Erreur dans createFreeTrial:', error);
      throw error;
    }
  }

  /**
   * Souscrit à un plan payant
   */
  static async subscribeToPlan(businessId: string, planId: string): Promise<boolean> {
    try {
      console.log('💳 Souscription au plan:', planId, 'pour:', businessId);

      // Récupérer les détails du plan
      const { data: plan, error: planError } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('id', planId)
        .single();

      if (planError) {
        console.error('❌ Plan non trouvé:', planError);
        throw planError;
      }

      // Désactiver l'abonnement actuel
      await supabase
        .from('business_subscriptions')
        .update({ is_active: false })
        .eq('business_id', businessId)
        .eq('is_active', true);

      // Créer le nouvel abonnement
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(startDate.getDate() + plan.duration);

      const { error: subscriptionError } = await supabase
        .from('business_subscriptions')
        .insert({
          business_id: businessId,
          plan_id: planId,
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          is_active: true,
          is_trial: false,
          auto_renew: true
        });

      if (subscriptionError) {
        console.error('❌ Erreur lors de la création de l\'abonnement:', subscriptionError);
        throw subscriptionError;
      }

      console.log('✅ Souscription réussie au plan:', planId);
      return true;

    } catch (error) {
      console.error('❌ Erreur dans subscribeToPlan:', error);
      return false;
    }
  }

  /**
   * Annule un abonnement
   */
  static async cancelSubscription(businessId: string): Promise<boolean> {
    try {
      console.log('❌ Annulation de l\'abonnement pour:', businessId);

      const { error } = await supabase
        .from('business_subscriptions')
        .update({ 
          auto_renew: false,
          is_active: false 
        })
        .eq('business_id', businessId)
        .eq('is_active', true);

      if (error) {
        console.error('❌ Erreur lors de l\'annulation:', error);
        throw error;
      }

      console.log('✅ Abonnement annulé avec succès');
      return true;

    } catch (error) {
      console.error('❌ Erreur dans cancelSubscription:', error);
      return false;
    }
  }

  /**
   * Désactive un abonnement expiré
   */
  static async deactivateSubscription(subscriptionId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('business_subscriptions')
        .update({ is_active: false })
        .eq('id', subscriptionId);

      if (error) {
        console.error('❌ Erreur lors de la désactivation:', error);
        throw error;
      }

      console.log('✅ Abonnement désactivé:', subscriptionId);
    } catch (error) {
      console.error('❌ Erreur dans deactivateSubscription:', error);
    }
  }

  /**
   * Récupère tous les plans d'abonnement disponibles
   */
  static async getAvailablePlans(): Promise<SubscriptionPlan[]> {
    try {
      const { data: plans, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('price', { ascending: true });

      if (error) {
        console.error('❌ Erreur lors de la récupération des plans:', error);
        throw error;
      }

      return plans || [];
    } catch (error) {
      console.error('❌ Erreur dans getAvailablePlans:', error);
      return [];
    }
  }

  /**
   * Vérifie si une entreprise a accès à une fonctionnalité
   */
  static async hasFeatureAccess(businessId: string, feature: string): Promise<boolean> {
    try {
      const status = await this.getBusinessSubscriptionStatus(businessId);
      
      if (!status || !status.isActive) {
        return false;
      }

      // Logique pour vérifier l'accès aux fonctionnalités selon le plan
      const featureAccess: { [key: string]: string[] } = {
        'free-trial': ['basic-ai', 'basic-dashboard', 'basic-reviews'],
        'starter': ['basic-ai', 'basic-dashboard', 'basic-reviews', 'pdf-export'],
        'professional': ['advanced-ai', 'custom-dashboard', 'unlimited-reviews', 'api-access'],
        'enterprise': ['premium-ai', 'custom-dashboard', 'unlimited-reviews', 'api-access', 'dedicated-support']
      };

      const planFeatures = featureAccess[status.plan] || [];
      return planFeatures.includes(feature);

    } catch (error) {
      console.error('❌ Erreur dans hasFeatureAccess:', error);
      return false;
    }
  }

  /**
   * Récupère l'historique des abonnements d'une entreprise
   */
  static async getSubscriptionHistory(businessId: string): Promise<BusinessSubscription[]> {
    try {
      const { data: subscriptions, error } = await supabase
        .from('business_subscriptions')
        .select(`
          *,
          subscription_plans (
            name,
            price,
            description
          )
        `)
        .eq('business_id', businessId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Erreur lors de la récupération de l\'historique:', error);
        throw error;
      }

      return subscriptions || [];
    } catch (error) {
      console.error('❌ Erreur dans getSubscriptionHistory:', error);
      return [];
    }
  }
}
