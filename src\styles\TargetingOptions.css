.targeting-options {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-top: 1.5rem;
  border: 1px solid #e5e7eb;
}

.targeting-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.5rem;
  overflow-x: auto;
}

.targeting-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: none;
  border: none;
  border-radius: 0.25rem;
  color: #6b7280;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
}

.targeting-tab:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.targeting-tab.active {
  background-color: #e0f2fe;
  color: #0ea5e9;
}

.targeting-section {
  margin-bottom: 1.5rem;
}

.targeting-section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 1rem 0 0.75rem 0;
}

.targeting-options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 0.75rem;
}

.targeting-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.targeting-option:hover {
  background-color: #f3f4f6;
}

.targeting-option input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  border-radius: 0.25rem;
  border: 1px solid #d1d5db;
  cursor: pointer;
}

.targeting-summary {
  background-color: #f0f9ff;
  border-radius: 0.5rem;
  padding: 1.25rem;
  margin-top: 1.5rem;
  border-left: 3px solid #0ea5e9;
}

.targeting-summary-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #0ea5e9;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.targeting-summary-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.targeting-summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.targeting-summary-label {
  font-weight: 500;
  color: #4b5563;
}

.targeting-summary-value {
  color: #6b7280;
}

/* Animation pour les transitions entre les onglets */
.targeting-content {
  animation: fade-in 0.3s ease-in-out;
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Styles pour les écrans plus petits */
@media (max-width: 768px) {
  .targeting-options-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .targeting-tab {
    padding: 0.5rem;
  }
  
  .targeting-tab span {
    display: none;
  }
}
