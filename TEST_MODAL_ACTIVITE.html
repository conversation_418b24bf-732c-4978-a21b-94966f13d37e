<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal Activité</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f3f4f6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .activity-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            background: #f9fafb;
            transition: all 0.2s;
        }
        .activity-card:hover {
            background: #f3f4f6;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
        }
        .activity-content {
            flex: 1;
        }
        .activity-title {
            font-weight: 600;
            color: #111827;
            margin-bottom: 4px;
        }
        .activity-description {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 8px;
        }
        .activity-time {
            color: #9ca3af;
            font-size: 12px;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-success { background: #dcfce7; color: #166534; }
        .status-warning { background: #fef3c7; color: #92400e; }
        .status-info { background: #dbeafe; color: #1e40af; }
        .status-error { background: #fee2e2; color: #dc2626; }
        .filters {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .filter-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        .btn-primary:hover {
            background: #2563eb;
        }
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Activité récente - Test Modal</h1>
            <p>Simulation du modal d'activité développé</p>
        </div>

        <div class="filters">
            <input type="text" class="filter-input" placeholder="🔍 Rechercher..." id="searchInput">
            <select class="filter-input" id="typeFilter">
                <option value="all">Tous les types</option>
                <option value="order">Commandes</option>
                <option value="review">Avis</option>
                <option value="product">Produits</option>
                <option value="message">Messages</option>
            </select>
            <select class="filter-input" id="statusFilter">
                <option value="all">Tous les statuts</option>
                <option value="success">Succès</option>
                <option value="warning">Attention</option>
                <option value="info">Info</option>
                <option value="error">Erreur</option>
            </select>
            <button class="btn btn-primary" onclick="exportData()">📥 Export CSV</button>
            <button class="btn btn-primary" onclick="refreshData()">🔄 Actualiser</button>
        </div>

        <div id="activitiesList">
            <!-- Les activités seront générées par JavaScript -->
        </div>

        <div id="emptyState" class="empty-state" style="display: none;">
            <div class="empty-icon">📭</div>
            <h3>Aucune activité trouvée</h3>
            <p>Essayez de modifier vos filtres ou d'actualiser les données.</p>
        </div>
    </div>

    <script>
        // Données simulées d'activités
        const activities = [
            {
                id: 'order-1',
                type: 'order',
                title: 'Commande #12345',
                description: 'Jean Dupont - iPhone 15 Pro (Qté: 1)',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
                status: 'success',
                amount: 450000
            },
            {
                id: 'review-1',
                type: 'review',
                title: 'Avis client - 5/5 ⭐',
                description: 'Marie Martin: "Excellent service, livraison rapide et produit conforme"',
                timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
                status: 'success'
            },
            {
                id: 'message-1',
                type: 'message',
                title: 'Message client reçu',
                description: 'Un client a envoyé une question sur les produits électroniques',
                timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
                status: 'info'
            },
            {
                id: 'product-1',
                type: 'product',
                title: 'Alerte stock faible',
                description: 'MacBook Air M3 - Il reste 3 unités',
                timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000),
                status: 'warning'
            },
            {
                id: 'order-2',
                type: 'order',
                title: 'Commande #12346',
                description: 'Paul Durand - Samsung Galaxy S24 (Qté: 2)',
                timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000),
                status: 'warning',
                amount: 380000
            },
            {
                id: 'review-2',
                type: 'review',
                title: 'Avis client - 2/5 ⭐',
                description: 'Client mécontent: "Livraison trop lente, produit abîmé"',
                timestamp: new Date(Date.now() - 18 * 60 * 60 * 1000),
                status: 'error'
            }
        ];

        let filteredActivities = [...activities];

        function getActivityIcon(type) {
            const icons = {
                order: { icon: '🛍️', bg: '#dcfce7' },
                review: { icon: '⭐', bg: '#fef3c7' },
                product: { icon: '📦', bg: '#dbeafe' },
                message: { icon: '💬', bg: '#f3e8ff' }
            };
            return icons[type] || { icon: '📋', bg: '#f3f4f6' };
        }

        function formatTimeAgo(date) {
            const now = new Date();
            const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
            
            if (diffInHours < 1) return 'Il y a moins d\'une heure';
            if (diffInHours < 24) return `Il y a ${diffInHours}h`;
            const diffInDays = Math.floor(diffInHours / 24);
            return `Il y a ${diffInDays}j`;
        }

        function formatAmount(amount) {
            return new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: 'XOF',
                minimumFractionDigits: 0
            }).format(amount);
        }

        function renderActivities() {
            const container = document.getElementById('activitiesList');
            const emptyState = document.getElementById('emptyState');

            if (filteredActivities.length === 0) {
                container.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            container.style.display = 'block';
            emptyState.style.display = 'none';

            container.innerHTML = filteredActivities.map(activity => {
                const iconData = getActivityIcon(activity.type);
                return `
                    <div class="activity-card" style="display: flex; align-items: flex-start;">
                        <div class="activity-icon" style="background: ${iconData.bg};">
                            ${iconData.icon}
                        </div>
                        <div class="activity-content">
                            <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                                <div style="flex: 1;">
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                                        <div class="activity-title">${activity.title}</div>
                                        <span class="status-badge status-${activity.status}">
                                            ${activity.status === 'success' ? '✅ Succès' :
                                              activity.status === 'warning' ? '⚠️ Attention' :
                                              activity.status === 'error' ? '❌ Erreur' : 'ℹ️ Info'}
                                        </span>
                                    </div>
                                    <div class="activity-description">${activity.description}</div>
                                    <div class="activity-time">🕒 ${formatTimeAgo(activity.timestamp)}</div>
                                </div>
                                ${activity.amount ? `
                                    <div style="text-align: right; color: #059669; font-weight: 600;">
                                        💰 ${formatAmount(activity.amount)}
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function filterActivities() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const typeFilter = document.getElementById('typeFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            filteredActivities = activities.filter(activity => {
                const matchesSearch = !searchTerm || 
                    activity.title.toLowerCase().includes(searchTerm) ||
                    activity.description.toLowerCase().includes(searchTerm);
                
                const matchesType = typeFilter === 'all' || activity.type === typeFilter;
                const matchesStatus = statusFilter === 'all' || activity.status === statusFilter;

                return matchesSearch && matchesType && matchesStatus;
            });

            renderActivities();
        }

        function exportData() {
            const csvContent = [
                ['Date', 'Type', 'Titre', 'Description', 'Statut', 'Montant'],
                ...filteredActivities.map(activity => [
                    activity.timestamp.toLocaleDateString('fr-FR'),
                    activity.type,
                    activity.title,
                    activity.description,
                    activity.status,
                    activity.amount ? formatAmount(activity.amount) : ''
                ])
            ].map(row => row.join(',')).join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `activite-test-${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            window.URL.revokeObjectURL(url);
            
            alert('✅ Export CSV réussi !');
        }

        function refreshData() {
            // Simulation d'actualisation
            const loader = document.createElement('div');
            loader.innerHTML = '🔄 Actualisation...';
            loader.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #3b82f6; color: white; padding: 12px 20px; border-radius: 8px; z-index: 1000;';
            document.body.appendChild(loader);
            
            setTimeout(() => {
                document.body.removeChild(loader);
                alert('✅ Données actualisées !');
            }, 1000);
        }

        // Event listeners
        document.getElementById('searchInput').addEventListener('input', filterActivities);
        document.getElementById('typeFilter').addEventListener('change', filterActivities);
        document.getElementById('statusFilter').addEventListener('change', filterActivities);

        // Initialisation
        renderActivities();
    </script>
</body>
</html>
