import React, { useState, useEffect } from 'react';
import {
  X,
  Star,
  ShoppingCart,
  MessageCircle,
  Heart,
  Share2,
  ChevronLeft,
  ChevronRight,
  Zap,
  Package,
  Truck,
  Shield,
  Award,
  Crown,
  TrendingUp,
  MapPin,
  Clock,
  Users,
  ThumbsUp,
  Eye,
  Minus,
  Plus
} from 'lucide-react';
import { IProduct, QualityBadge } from '../../types';
import Button from '../ui/Button';
import ProductReviews from './ProductReviews';
import { DEFAULT_IMAGES_SIZED } from '../../constants/defaultImages';

interface ProductPreviewModalProps {
  product: IProduct | null;
  isOpen: boolean;
  onClose: () => void;
  onBuyClick: (product: IProduct) => void;
  onNegotiateClick: (product: IProduct) => void;
}

const QualityBadgeConfig: Record<QualityBadge, {
  colors: string;
  icon: React.ReactNode;
  label: string;
}> = {
  [QualityBadge.STANDARD]: {
    colors: 'bg-gray-100 text-gray-700 border-gray-200',
    icon: <Package size={14} />,
    label: 'Standard'
  },
  [QualityBadge.VERIFIED]: {
    colors: 'bg-blue-100 text-blue-700 border-blue-200',
    icon: <Shield size={14} />,
    label: 'Vérifié'
  },
  [QualityBadge.PREMIUM]: {
    colors: 'bg-purple-100 text-purple-700 border-purple-200',
    icon: <Crown size={14} />,
    label: 'Premium'
  },
  [QualityBadge.EXCELLENT]: {
    colors: 'bg-green-100 text-green-700 border-green-200',
    icon: <Award size={14} />,
    label: 'Excellent'
  },
  [QualityBadge.TOP_QUALITY]: {
    colors: 'bg-indigo-100 text-indigo-700 border-indigo-200',
    icon: <TrendingUp size={14} />,
    label: 'Top Qualité'
  },
  [QualityBadge.GOLD]: {
    colors: 'bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900 border-yellow-300',
    icon: <Crown size={14} />,
    label: 'Gold'
  },
  [QualityBadge.SILVER]: {
    colors: 'bg-gradient-to-r from-gray-300 to-gray-400 text-gray-800 border-gray-300',
    icon: <Award size={14} />,
    label: 'Silver'
  },
  [QualityBadge.BRONZE]: {
    colors: 'bg-gradient-to-r from-orange-400 to-orange-500 text-orange-900 border-orange-300',
    icon: <Award size={14} />,
    label: 'Bronze'
  },
  [QualityBadge.NONE]: {
    colors: 'hidden',
    icon: null,
    label: ''
  }
};

const ProductPreviewModal: React.FC<ProductPreviewModalProps> = ({
  product,
  isOpen,
  onClose,
  onBuyClick,
  onNegotiateClick
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'description' | 'reviews' | 'shipping' | 'comments'>('description');

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      setCurrentImageIndex(0);
      setQuantity(1);
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen || !product) return null;

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price);
  };

  const getRatingStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} size={16} className="text-yellow-400 fill-current" />);
    }

    if (hasHalfStar) {
      stars.push(<Star key="half" size={16} className="text-yellow-400 fill-current opacity-50" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<Star key={`empty-${i}`} size={16} className="text-gray-300" />);
    }

    return stars;
  };

  const badgeConfig = QualityBadgeConfig[product.qualityBadge || QualityBadge.NONE];

  // Mock data pour l'aperçu
  const mockImages = [
    product.images[0] || DEFAULT_IMAGES_SIZED.PRODUCT_MEDIUM,
    'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500&h=500&fit=crop',
    'https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f?w=500&h=500&fit=crop'
  ];



  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-75 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
          >
            <X size={20} className="text-gray-600" />
          </button>

          <div className="flex flex-col lg:flex-row h-full max-h-[90vh]">
            {/* Images Section */}
            <div className="lg:w-1/2 relative">
              {/* Main Image */}
              <div className="relative h-96 lg:h-full bg-gray-100">
                <img
                  src={mockImages[currentImageIndex]}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />

                {/* Image Navigation */}
                {mockImages.length > 1 && (
                  <>
                    <button
                      onClick={() => setCurrentImageIndex(prev => prev === 0 ? mockImages.length - 1 : prev - 1)}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                    >
                      <ChevronLeft size={20} />
                    </button>
                    <button
                      onClick={() => setCurrentImageIndex(prev => prev === mockImages.length - 1 ? 0 : prev + 1)}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                    >
                      <ChevronRight size={20} />
                    </button>
                  </>
                )}

                {/* Image Indicators */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {mockImages.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                      }`}
                    />
                  ))}
                </div>

                {/* Badges on Image */}
                <div className="absolute top-4 left-4 flex flex-col space-y-2">
                  {product.negotiable && (
                    <div className="flex items-center bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs font-semibold px-3 py-1.5 rounded-full shadow-lg">
                      <Zap size={12} className="mr-1" />
                      Négociable
                    </div>
                  )}

                  {product.qualityBadge && product.qualityBadge !== QualityBadge.NONE && (
                    <div className={`flex items-center text-xs font-semibold px-3 py-1.5 rounded-full border shadow-lg ${badgeConfig.colors}`}>
                      {badgeConfig.icon}
                      <span className="ml-1">{badgeConfig.label}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Thumbnail Images */}
              <div className="p-4 flex space-x-2 overflow-x-auto">
                {mockImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-colors ${
                      index === currentImageIndex ? 'border-blue-500' : 'border-gray-200'
                    }`}
                  >
                    <img
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            </div>

            {/* Content Section */}
            <div className="lg:w-1/2 flex flex-col">
              <div className="p-6 flex-1 overflow-y-auto">
                {/* Header */}
                <div className="mb-6">
                  <div className="flex items-start justify-between mb-2">
                    <span className="text-sm font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                      {product.category}
                    </span>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setIsFavorite(!isFavorite)}
                        className={`p-2 rounded-full transition-colors ${
                          isFavorite ? 'bg-red-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-red-50 hover:text-red-500'
                        }`}
                      >
                        <Heart size={18} className={isFavorite ? 'fill-current' : ''} />
                      </button>
                      <button className="p-2 rounded-full bg-gray-100 text-gray-600 hover:bg-blue-50 hover:text-blue-500 transition-colors">
                        <Share2 size={18} />
                      </button>
                    </div>
                  </div>

                  <h1 className="text-2xl font-bold text-gray-900 mb-3">
                    {product.name}
                  </h1>

                  {/* Rating */}
                  <div className="flex items-center mb-4">
                    <div className="flex items-center mr-3">
                      {getRatingStars(product.averageRating)}
                    </div>
                    <span className="text-lg font-semibold text-gray-700">
                      {product.averageRating.toFixed(1)}
                    </span>
                    <span className="text-sm text-gray-500 ml-2">
                      (127 avis)
                    </span>
                  </div>

                  {/* Price */}
                  <div className="mb-6">
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {formatPrice(product.price)}
                      <span className="text-xl font-semibold text-gray-600 ml-2">F CFA</span>
                    </div>
                    {product.negotiable && (
                      <div className="text-sm text-green-600 font-medium">
                        💬 Prix négociable - Faites votre offre !
                      </div>
                    )}
                  </div>

                  {/* Stock Status */}
                  <div className="mb-6">
                    {product.stock !== undefined && product.stock > 0 && (
                      <div className="flex items-center text-green-600">
                        <Package size={16} className="mr-2" />
                        <span className="font-medium">
                          {product.stock > 10 ? 'En stock' : `Plus que ${product.stock} en stock`}
                        </span>
                      </div>
                    )}
                    {product.stock === 0 && (
                      <div className="flex items-center text-red-600">
                        <Package size={16} className="mr-2" />
                        <span className="font-medium">Rupture de stock</span>
                      </div>
                    )}
                  </div>

                  {/* Quantity Selector */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Quantité
                    </label>
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => setQuantity(Math.max(1, quantity - 1))}
                        className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                        disabled={quantity <= 1}
                      >
                        <Minus size={16} />
                      </button>
                      <span className="text-lg font-semibold w-12 text-center">
                        {quantity}
                      </span>
                      <button
                        onClick={() => setQuantity(quantity + 1)}
                        className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                        disabled={product.stock !== undefined && quantity >= product.stock}
                      >
                        <Plus size={16} />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Tabs */}
                <div className="border-b border-gray-200 mb-6">
                  <nav className="flex space-x-8">
                    {[
                      { id: 'description', label: 'Description', icon: <Eye size={16} /> },
                      { id: 'comments', label: 'Avis', icon: <MessageCircle size={16} /> },
                      { id: 'shipping', label: 'Livraison', icon: <Truck size={16} /> }
                    ].map(tab => (
                      <button
                        key={tab.id}
                        onClick={() => setSelectedTab(tab.id as any)}
                        className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                          selectedTab === tab.id
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        {tab.icon}
                        <span>{tab.label}</span>
                      </button>
                    ))}
                  </nav>
                </div>

                {/* Tab Content */}
                <div className="mb-6">
                  {selectedTab === 'description' && (
                    <div>
                      <p className="text-gray-700 leading-relaxed mb-4">
                        {product.description}
                      </p>
                      <div className="space-y-3">
                        <div className="flex items-center text-sm text-gray-600">
                          <MapPin size={16} className="mr-2 text-gray-400" />
                          <span>Expédié depuis Abidjan, Côte d'Ivoire</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <Clock size={16} className="mr-2 text-gray-400" />
                          <span>Livraison sous 2-5 jours ouvrés</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <Shield size={16} className="mr-2 text-gray-400" />
                          <span>Garantie 30 jours satisfait ou remboursé</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {selectedTab === 'comments' && (
                    <div className="-mx-6 -mb-6">
                      <ProductReviews
                        productId={product.id}
                        productName={product.name}
                      />
                    </div>
                  )}

                  {selectedTab === 'shipping' && (
                    <div className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <Truck size={20} className="text-blue-500 mt-1" />
                        <div>
                          <h4 className="font-medium text-gray-900 mb-1">Livraison standard</h4>
                          <p className="text-sm text-gray-600">2-5 jours ouvrés - Gratuite dès 25 000 F CFA</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <Zap size={20} className="text-yellow-500 mt-1" />
                        <div>
                          <h4 className="font-medium text-gray-900 mb-1">Livraison express</h4>
                          <p className="text-sm text-gray-600">24-48h - 2 500 F CFA</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <MapPin size={20} className="text-green-500 mt-1" />
                        <div>
                          <h4 className="font-medium text-gray-900 mb-1">Retrait en magasin</h4>
                          <p className="text-sm text-gray-600">Gratuit - Disponible sous 2h</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="p-6 border-t border-gray-200 bg-gray-50">
                <div className="flex space-x-4">
                  <Button
                    variant="primary"
                    fullWidth
                    leftIcon={<ShoppingCart size={20} />}
                    disabled={product.stock === 0}
                    onClick={() => {
                      onBuyClick(product);
                      onClose();
                    }}
                    className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-4 rounded-xl transition-all duration-300"
                  >
                    Ajouter au panier
                  </Button>

                  {product.negotiable && (
                    <Button
                      variant="outline"
                      leftIcon={<MessageCircle size={20} />}
                      onClick={() => {
                        onNegotiateClick(product);
                        onClose();
                      }}
                      className="px-6 py-4 border-2 border-gray-300 text-gray-700 hover:border-blue-500 hover:text-blue-600 rounded-xl transition-all duration-300"
                    >
                      Négocier
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductPreviewModal;
