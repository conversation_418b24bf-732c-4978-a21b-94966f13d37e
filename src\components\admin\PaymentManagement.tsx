import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  DollarSign,
  Calendar,
  Users,
  Building2,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock,
  Filter,
  Download,
  Search,
  Eye,
  RefreshCw,
  Ban,
  CheckSquare,
  Shield
} from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import { formatAmount } from '../../utils/formatUtils';
import {
  AdminPaymentService,
  AdminPaymentStats,
  AdminSubscription,
  AdminAdPayment,
  AdminTransaction
} from '../../services/adminPaymentService';
import SubscriptionDetailsModal from './SubscriptionDetailsModal';
import SuspendSubscriptionModal from './SuspendSubscriptionModal';
import AdPaymentDetailsModal from './AdPaymentDetailsModal';
import StopAdModal from './StopAdModal';
import SubscriptionCodeModal from './SubscriptionCodeModal';
import { SubscriptionCode, SubscriptionCodeService } from '../../services/subscriptionCodeService';

const PaymentManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'subscriptions' | 'ads' | 'transactions'>('overview');
  const [stats, setStats] = useState<AdminPaymentStats | null>(null);
  const [subscriptions, setSubscriptions] = useState<AdminSubscription[]>([]);
  const [adPayments, setAdPayments] = useState<AdminAdPayment[]>([]);
  const [transactions, setTransactions] = useState<AdminTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');

  // États pour les modals
  const [selectedSubscription, setSelectedSubscription] = useState<AdminSubscription | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isSuspendModalOpen, setIsSuspendModalOpen] = useState(false);

  // États pour les modals des publicités
  const [selectedAdPayment, setSelectedAdPayment] = useState<AdminAdPayment | null>(null);
  const [isAdDetailsModalOpen, setIsAdDetailsModalOpen] = useState(false);
  const [isStopAdModalOpen, setIsStopAdModalOpen] = useState(false);

  // États pour les codes d'abonnement
  const [subscriptionCodes, setSubscriptionCodes] = useState<SubscriptionCode[]>([]);
  const [selectedCode, setSelectedCode] = useState<SubscriptionCode | null>(null);
  const [isCodeModalOpen, setIsCodeModalOpen] = useState(false);
  const [codeStatusFilter, setCodeStatusFilter] = useState('all');
  const [codeSearchTerm, setCodeSearchTerm] = useState('');

  useEffect(() => {
    loadPaymentData();
  }, []);

  // Recharger les données quand on change d'onglet vers les codes
  useEffect(() => {
    if (activeTab === 'codes') {
      console.log('🔄 ADMIN - Onglet codes activé, rechargement des données...');
      loadPaymentData();
    }
  }, [activeTab]);

  const loadPaymentData = async () => {
    setLoading(true);
    try {
      // Charger les statistiques
      const paymentStats = await AdminPaymentService.getAdminPaymentStats();
      setStats(paymentStats);

      // Charger les abonnements
      const subscriptionsData = await AdminPaymentService.getAdminSubscriptions({
        status: statusFilter,
        searchTerm: searchTerm || undefined,
        dateFilter: dateFilter
      });
      setSubscriptions(subscriptionsData);

      // Charger les paiements publicitaires
      const adPaymentsData = await AdminPaymentService.getAdminAdPayments({
        status: statusFilter,
        searchTerm: searchTerm || undefined
      });
      setAdPayments(adPaymentsData);

      // Charger les transactions
      const transactionsData = await AdminPaymentService.getAdminTransactions({
        status: statusFilter,
        searchTerm: searchTerm || undefined
      });
      setTransactions(transactionsData);

      // Charger les codes d'abonnement
      console.log('🔄 ADMIN - Chargement des codes d\'abonnement...');
      const codesData = await SubscriptionCodeService.getAllCodes({
        status: codeStatusFilter !== 'all' ? codeStatusFilter : undefined,
        businessName: codeSearchTerm || undefined
      });
      console.log('📋 ADMIN - Codes reçus:', codesData.length, codesData);
      setSubscriptionCodes(codesData);

    } catch (error) {
      console.error('Erreur lors du chargement des données de paiement:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fonctions de gestion des modals
  const handleViewSubscription = (subscription: AdminSubscription) => {
    setSelectedSubscription(subscription);
    setIsDetailsModalOpen(true);
  };

  const handleSuspendSubscription = (subscription: AdminSubscription) => {
    setSelectedSubscription(subscription);
    setIsSuspendModalOpen(true);
  };

  const handleConfirmSuspension = async (
    subscriptionId: string,
    reason: string,
    suspensionType: 'temporary' | 'permanent',
    duration?: number
  ) => {
    try {
      const success = await AdminPaymentService.updateSubscriptionStatus(
        subscriptionId,
        suspensionType === 'permanent' ? 'cancelled' : 'pending',
        reason
      );

      if (success) {
        // Recharger les données
        await loadPaymentData();

        // Notification de succès
        alert(`Abonnement ${suspensionType === 'permanent' ? 'suspendu définitivement' : 'suspendu temporairement'} avec succès.`);
      } else {
        alert('Erreur lors de la suspension de l\'abonnement.');
      }
    } catch (error) {
      console.error('Erreur lors de la suspension:', error);
      alert('Erreur lors de la suspension de l\'abonnement.');
    }
  };

  const handleReactivateSubscription = async (subscriptionId: string) => {
    try {
      const success = await AdminPaymentService.updateSubscriptionStatus(
        subscriptionId,
        'active',
        'Réactivation manuelle par administrateur'
      );

      if (success) {
        // Recharger les données
        await loadPaymentData();

        // Notification de succès
        alert('Abonnement réactivé avec succès.');
      } else {
        alert('Erreur lors de la réactivation de l\'abonnement.');
      }
    } catch (error) {
      console.error('Erreur lors de la réactivation:', error);
      alert('Erreur lors de la réactivation de l\'abonnement.');
    }
  };

  // Fonctions de gestion des modals pour les publicités
  const handleViewAdPayment = (adPayment: AdminAdPayment) => {
    setSelectedAdPayment(adPayment);
    setIsAdDetailsModalOpen(true);
  };

  const handleStopAdPayment = (adPayment: AdminAdPayment) => {
    setSelectedAdPayment(adPayment);
    setIsStopAdModalOpen(true);
  };

  const handleConfirmStopAd = async (
    adPaymentId: string,
    reason: string,
    refundType: 'none' | 'partial' | 'full'
  ) => {
    try {
      const success = await AdminPaymentService.updateAdPaymentStatus(
        adPaymentId,
        'cancelled',
        reason
      );

      if (success) {
        // Recharger les données
        await loadPaymentData();

        // Notification de succès
        alert(`Publicité arrêtée avec succès. ${refundType !== 'none' ? 'Remboursement en cours de traitement.' : ''}`);
      } else {
        alert('Erreur lors de l\'arrêt de la publicité.');
      }
    } catch (error) {
      console.error('Erreur lors de l\'arrêt de la publicité:', error);
      alert('Erreur lors de l\'arrêt de la publicité.');
    }
  };

  const handlePauseAdPayment = async (adPaymentId: string) => {
    try {
      const success = await AdminPaymentService.updateAdPaymentStatus(
        adPaymentId,
        'pending',
        'Suspension temporaire par administrateur'
      );

      if (success) {
        // Recharger les données
        await loadPaymentData();

        // Notification de succès
        alert('Publicité suspendue avec succès.');
      } else {
        alert('Erreur lors de la suspension de la publicité.');
      }
    } catch (error) {
      console.error('Erreur lors de la suspension de la publicité:', error);
      alert('Erreur lors de la suspension de la publicité.');
    }
  };

  const handleResumeAdPayment = async (adPaymentId: string) => {
    try {
      const success = await AdminPaymentService.updateAdPaymentStatus(
        adPaymentId,
        'active',
        'Reprise par administrateur'
      );

      if (success) {
        // Recharger les données
        await loadPaymentData();

        // Notification de succès
        alert('Publicité reprise avec succès.');
      } else {
        alert('Erreur lors de la reprise de la publicité.');
      }
    } catch (error) {
      console.error('Erreur lors de la reprise de la publicité:', error);
      alert('Erreur lors de la reprise de la publicité.');
    }
  };

  // Fonctions de gestion des codes d'abonnement
  const handleViewCode = (code: SubscriptionCode) => {
    setSelectedCode(code);
    setIsCodeModalOpen(true);
  };

  const handleValidateCode = async (codeId: string, reason?: string) => {
    try {
      console.log('🔄 ADMIN - Début validation code:', codeId);

      // Récupérer l'ID de l'admin connecté
      const { AdminService } = await import('../../services/adminService');
      const adminProfile = await AdminService.getCurrentAdminProfile();
      const adminId = adminProfile?.id || 'admin_fallback';

      console.log('👤 ADMIN - ID admin récupéré:', adminId);

      const success = await SubscriptionCodeService.validateCode({
        codeId,
        action: 'validate',
        reason,
        adminId
      });

      console.log('✅ ADMIN - Résultat validation:', success);

      if (success) {
        console.log('🔄 ADMIN - Attente de 500ms pour synchronisation...');
        // Petit délai pour laisser le temps à la synchronisation de se faire
        await new Promise(resolve => setTimeout(resolve, 500));

        console.log('🔄 ADMIN - Forcer la synchronisation...');
        SubscriptionCodeService.forceSyncAfterValidation();

        console.log('🔄 ADMIN - Rechargement des données après validation...');
        await loadPaymentData();
        console.log('✅ ADMIN - Données rechargées');

        // Notification de succès
        alert('Code validé avec succès. L\'abonnement a été activé.');
      } else {
        console.error('❌ ADMIN - Échec validation');
        alert('Erreur lors de la validation du code.');
      }
    } catch (error) {
      console.error('❌ ADMIN - Erreur lors de la validation du code:', error);
      alert('Erreur lors de la validation du code: ' + error.message);
    }
  };

  const handleRejectCode = async (codeId: string, reason: string) => {
    try {
      // Récupérer l'ID de l'admin connecté
      const { AdminService } = await import('../../services/adminService');
      const adminProfile = await AdminService.getCurrentAdminProfile();
      const adminId = adminProfile?.id || 'admin_fallback';

      console.log('👤 ADMIN - ID admin récupéré pour rejet:', adminId);

      const success = await SubscriptionCodeService.validateCode({
        codeId,
        action: 'reject',
        reason,
        adminId
      });

      if (success) {
        console.log('🔄 ADMIN - Attente de 500ms pour synchronisation...');
        // Petit délai pour laisser le temps à la synchronisation de se faire
        await new Promise(resolve => setTimeout(resolve, 500));

        console.log('🔄 ADMIN - Forcer la synchronisation...');
        SubscriptionCodeService.forceSyncAfterValidation();

        // Recharger les données
        console.log('🔄 ADMIN - Rechargement des données après rejet...');
        await loadPaymentData();
        console.log('✅ ADMIN - Données rechargées');

        // Notification de succès
        alert('Code rejeté avec succès. L\'entreprise a été notifiée.');
      } else {
        alert('Erreur lors du rejet du code.');
      }
    } catch (error) {
      console.error('Erreur lors du rejet du code:', error);
      alert('Erreur lors du rejet du code.');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'expired':
      case 'cancelled':
      case 'failed':
        return 'text-red-600 bg-red-100';
      case 'refunded':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
        return <CheckCircle size={16} />;
      case 'pending':
        return <Clock size={16} />;
      case 'expired':
      case 'cancelled':
      case 'failed':
        return <XCircle size={16} />;
      default:
        return <AlertCircle size={16} />;
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
    trend?: { value: number; isPositive: boolean };
  }> = ({ title, value, icon, color, trend }) => (
    <Card className="hover:shadow-lg transition-shadow">
      <CardBody className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900 mt-1">
              {typeof value === 'number' && title.includes('Revenue') || title.includes('Revenus') 
                ? formatAmount(value) 
                : value}
            </p>
            {trend && (
              <div className={`flex items-center mt-2 text-sm ${
                trend.isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                <TrendingUp size={16} className="mr-1" />
                {trend.value}% ce mois
              </div>
            )}
          </div>
          <div className={`p-3 rounded-full ${color}`}>
            {icon}
          </div>
        </div>
      </CardBody>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Paiements</h1>
          <p className="text-gray-600 mt-2">Abonnements, publicités et transactions</p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            leftIcon={<Download size={16} />}
          >
            Exporter
          </Button>
          <Button
            onClick={loadPaymentData}
            leftIcon={<RefreshCw size={16} />}
          >
            Actualiser
          </Button>
        </div>
      </div>

      {/* Onglets */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Vue d\'ensemble', icon: <DollarSign size={16} /> },
            { id: 'subscriptions', label: 'Abonnements', icon: <Calendar size={16} /> },
            { id: 'codes', label: 'Codes d\'abonnement', icon: <Shield size={16} /> },
            { id: 'ads', label: 'Publicités', icon: <TrendingUp size={16} /> },
            { id: 'transactions', label: 'Transactions', icon: <CreditCard size={16} /> }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Contenu des onglets */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Statistiques principales */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="Revenus Totaux"
              value={stats?.totalRevenue || 0}
              icon={<DollarSign size={24} className="text-white" />}
              color="bg-green-500"
              trend={{ value: 12.5, isPositive: true }}
            />
            <StatCard
              title="Revenus Mensuels"
              value={stats?.monthlyRevenue || 0}
              icon={<Calendar size={24} className="text-white" />}
              color="bg-blue-500"
              trend={{ value: 8.3, isPositive: true }}
            />
            <StatCard
              title="Abonnements Actifs"
              value={stats?.activeSubscriptions || 0}
              icon={<Users size={24} className="text-white" />}
              color="bg-purple-500"
              trend={{ value: 15.2, isPositive: true }}
            />
            <StatCard
              title="Paiements en Attente"
              value={stats?.pendingPayments || 0}
              icon={<Clock size={24} className="text-white" />}
              color="bg-orange-500"
            />
          </div>

          {/* Répartition des revenus */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardBody className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition des Revenus</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-4 h-4 bg-blue-500 rounded"></div>
                      <span className="text-gray-700">Abonnements</span>
                    </div>
                    <span className="font-semibold">{formatAmount(stats?.subscriptionRevenue || 0)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-4 h-4 bg-green-500 rounded"></div>
                      <span className="text-gray-700">Publicités</span>
                    </div>
                    <span className="font-semibold">{formatAmount(stats?.adRevenue || 0)}</span>
                  </div>
                </div>
              </CardBody>
            </Card>

            <Card>
              <CardBody className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Statut des Abonnements</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <CheckCircle size={16} className="text-green-500" />
                      <span className="text-gray-700">Actifs</span>
                    </div>
                    <span className="font-semibold">{stats?.activeSubscriptions || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <XCircle size={16} className="text-red-500" />
                      <span className="text-gray-700">Expirés</span>
                    </div>
                    <span className="font-semibold">{stats?.expiredSubscriptions || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <AlertCircle size={16} className="text-orange-500" />
                      <span className="text-gray-700">Demandes de remboursement</span>
                    </div>
                    <span className="font-semibold">{stats?.refundRequests || 0}</span>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        </div>
      )}

      {/* Onglet Abonnements */}
      {activeTab === 'subscriptions' && (
        <div className="space-y-6">
          {/* Filtres */}
          <Card>
            <CardBody className="p-4">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-64">
                  <div className="relative">
                    <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Rechercher une entreprise..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Tous les statuts</option>
                  <option value="active">Actifs</option>
                  <option value="expired">Expirés</option>
                  <option value="cancelled">Annulés</option>
                  <option value="pending">En attente</option>
                </select>
                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Toutes les dates</option>
                  <option value="today">Aujourd'hui</option>
                  <option value="week">Cette semaine</option>
                  <option value="month">Ce mois</option>
                  <option value="year">Cette année</option>
                </select>
              </div>
            </CardBody>
          </Card>

          {/* Liste des abonnements */}
          <Card>
            <CardBody className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Entreprise
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Plan
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Montant
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Période
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Prochain paiement
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {subscriptions.map((subscription) => (
                      <tr key={subscription.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <Building2 size={16} className="text-gray-400 mr-3" />
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {subscription.businessName}
                              </div>
                              <div className="text-sm text-gray-500">
                                ID: {subscription.businessId}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            subscription.planType === 'yearly' ? 'bg-purple-100 text-purple-800' :
                            subscription.planType === 'monthly' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {subscription.planType === 'yearly' ? 'Annuel' :
                             subscription.planType === 'monthly' ? 'Mensuel' : 'Essai'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(subscription.status)}`}>
                            {getStatusIcon(subscription.status)}
                            <span className="ml-1">
                              {subscription.status === 'active' ? 'Actif' :
                               subscription.status === 'expired' ? 'Expiré' :
                               subscription.status === 'cancelled' ? 'Annulé' : 'En attente'}
                            </span>
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatAmount(subscription.amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div>
                            <div>Du {new Date(subscription.startDate).toLocaleDateString('fr-FR')}</div>
                            <div>Au {new Date(subscription.endDate).toLocaleDateString('fr-FR')}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {subscription.nextPayment ?
                            new Date(subscription.nextPayment).toLocaleDateString('fr-FR') :
                            'N/A'
                          }
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              leftIcon={<Eye size={14} />}
                              onClick={() => handleViewSubscription(subscription)}
                            >
                              Voir
                            </Button>
                            {subscription.status === 'active' && (
                              <Button
                                variant="outline"
                                size="sm"
                                leftIcon={<Ban size={14} />}
                                className="text-red-600 border-red-300 hover:bg-red-50"
                                onClick={() => handleSuspendSubscription(subscription)}
                              >
                                Suspendre
                              </Button>
                            )}
                            {(subscription.status === 'expired' || subscription.status === 'cancelled') && (
                              <Button
                                variant="outline"
                                size="sm"
                                leftIcon={<CheckSquare size={14} />}
                                className="text-green-600 border-green-300 hover:bg-green-50"
                                onClick={() => handleReactivateSubscription(subscription.id)}
                              >
                                Réactiver
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Onglet Codes d'abonnement */}
      {activeTab === 'codes' && (
        <div className="space-y-6">
          {/* Statistiques des codes */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <Card>
              <CardBody className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-900">{subscriptionCodes.length}</div>
                <div className="text-sm text-gray-600">Total codes</div>
              </CardBody>
            </Card>
            <Card>
              <CardBody className="p-4 text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {subscriptionCodes.filter(code => code.status === 'pending').length}
                </div>
                <div className="text-sm text-gray-600">En attente</div>
              </CardBody>
            </Card>
            <Card>
              <CardBody className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">
                  {subscriptionCodes.filter(code => code.status === 'validated').length}
                </div>
                <div className="text-sm text-gray-600">Validés</div>
              </CardBody>
            </Card>
            <Card>
              <CardBody className="p-4 text-center">
                <div className="text-2xl font-bold text-red-600">
                  {subscriptionCodes.filter(code => code.status === 'rejected').length}
                </div>
                <div className="text-sm text-gray-600">Rejetés</div>
              </CardBody>
            </Card>
            <Card>
              <CardBody className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-600">
                  {subscriptionCodes.filter(code => code.status === 'expired').length}
                </div>
                <div className="text-sm text-gray-600">Expirés</div>
              </CardBody>
            </Card>
          </div>

          {/* Filtres */}
          <Card>
            <CardBody className="p-4">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-64">
                  <div className="relative">
                    <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Rechercher une entreprise..."
                      value={codeSearchTerm}
                      onChange={(e) => setCodeSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
                <select
                  value={codeStatusFilter}
                  onChange={(e) => setCodeStatusFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Tous les statuts</option>
                  <option value="pending">En attente</option>
                  <option value="validated">Validés</option>
                  <option value="rejected">Rejetés</option>
                  <option value="expired">Expirés</option>
                </select>
                <select
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Tous les plans</option>
                  <option value="trial">Essai gratuit</option>
                  <option value="monthly">Mensuel</option>
                  <option value="yearly">Annuel</option>
                </select>
                <Button
                  variant="outline"
                  leftIcon={<RefreshCw size={16} />}
                  onClick={loadPaymentData}
                  disabled={loading}
                >
                  {loading ? 'Chargement...' : 'Actualiser'}
                </Button>
                <Button
                  variant="outline"
                  onClick={async () => {
                    console.log('🔍 DEBUG - localStorage:', localStorage.getItem('customeroom_generated_codes'));
                    console.log('🔍 DEBUG - sessionStorage:', sessionStorage.getItem('customeroom_session_codes'));
                    console.log('🔍 DEBUG - subscriptionCodes state:', subscriptionCodes);

                    // Test direct du service avec synchronisation forcée
                    const { SubscriptionCodeService } = await import('../../services/subscriptionCodeService');
                    SubscriptionCodeService.forceSync();
                    const allCodes = await SubscriptionCodeService.getAllCodes();
                    console.log('🔍 DEBUG - Service getAllCodes après sync:', allCodes);

                    loadPaymentData();
                  }}
                >
                  Debug
                </Button>
                <Button
                  variant="outline"
                  onClick={async () => {
                    // Test: créer un code manuellement
                    const { SubscriptionCodeService } = await import('../../services/subscriptionCodeService');
                    const testCode = await SubscriptionCodeService.createSubscriptionCode(
                      'test-business-id',
                      'Test Business',
                      'monthly',
                      25000
                    );
                    console.log('🧪 Code test créé:', testCode);
                    loadPaymentData();
                  }}
                >
                  Test Code
                </Button>
              </div>
            </CardBody>
          </Card>

          {/* Liste des codes */}
          <Card>
            <CardBody className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Code
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Entreprise
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Plan
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Montant
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Généré le
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Expiration
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {subscriptionCodes
                      .filter(code => {
                        const matchesStatus = codeStatusFilter === 'all' || code.status === codeStatusFilter;
                        const matchesSearch = !codeSearchTerm ||
                          code.business_name.toLowerCase().includes(codeSearchTerm.toLowerCase()) ||
                          code.code.includes(codeSearchTerm);
                        return matchesStatus && matchesSearch;
                      })
                      .map((code) => (
                      <tr key={code.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            <Shield size={16} className="text-blue-500" />
                            <div>
                              <div className="text-sm font-mono font-bold text-blue-600">
                                {SubscriptionCodeService.formatCode(code.code)}
                              </div>
                              <div className="text-xs text-gray-500">
                                ID: {code.id}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <Building2 size={16} className="text-gray-400 mr-3" />
                            <div className="text-sm text-gray-900">
                              {code.business_name}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            code.plan_type === 'yearly' ? 'bg-purple-100 text-purple-800' :
                            code.plan_type === 'monthly' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {code.plan_type === 'yearly' ? 'Annuel' :
                             code.plan_type === 'monthly' ? 'Mensuel' : 'Essai'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatAmount(code.amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${
                            code.status === 'pending' ? 'text-yellow-600 bg-yellow-100' :
                            code.status === 'validated' ? 'text-green-600 bg-green-100' :
                            code.status === 'rejected' ? 'text-red-600 bg-red-100' :
                            'text-gray-600 bg-gray-100'
                          }`}>
                            {code.status === 'pending' && <Clock size={12} className="mr-1" />}
                            {code.status === 'validated' && <CheckCircle size={12} className="mr-1" />}
                            {code.status === 'rejected' && <XCircle size={12} className="mr-1" />}
                            {code.status === 'expired' && <AlertCircle size={12} className="mr-1" />}
                            <span>
                              {code.status === 'pending' ? 'En attente' :
                               code.status === 'validated' ? 'Validé' :
                               code.status === 'rejected' ? 'Rejeté' : 'Expiré'}
                            </span>
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div>
                            <div>{new Date(code.generated_at).toLocaleDateString('fr-FR')}</div>
                            <div className="text-xs">
                              {new Date(code.generated_at).toLocaleTimeString('fr-FR', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div>
                            <div className={SubscriptionCodeService.isCodeExpired(code) ? 'text-red-600 font-medium' : ''}>
                              {SubscriptionCodeService.isCodeExpired(code) ? 'Expiré' :
                               SubscriptionCodeService.getTimeUntilExpiration(code)}
                            </div>
                            <div className="text-xs">
                              {new Date(code.expires_at).toLocaleDateString('fr-FR')}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              leftIcon={<Eye size={14} />}
                              onClick={() => handleViewCode(code)}
                            >
                              Voir
                            </Button>
                            {code.status === 'pending' && !SubscriptionCodeService.isCodeExpired(code) && (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  leftIcon={<CheckCircle size={14} />}
                                  className="text-green-600 border-green-300 hover:bg-green-50"
                                  onClick={() => handleValidateCode(code.id)}
                                >
                                  Valider
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  leftIcon={<XCircle size={14} />}
                                  className="text-red-600 border-red-300 hover:bg-red-50"
                                  onClick={() => handleViewCode(code)}
                                >
                                  Rejeter
                                </Button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Onglet Publicités */}
      {activeTab === 'ads' && (
        <div className="space-y-6">
          {/* Filtres */}
          <Card>
            <CardBody className="p-4">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-64">
                  <div className="relative">
                    <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Rechercher une publicité..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Tous les statuts</option>
                  <option value="active">Actives</option>
                  <option value="completed">Terminées</option>
                  <option value="cancelled">Annulées</option>
                  <option value="pending">En attente</option>
                </select>
              </div>
            </CardBody>
          </Card>

          {/* Liste des publicités */}
          <Card>
            <CardBody className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Publicité
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Entreprise
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Montant
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Durée
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Performance
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {adPayments.map((ad) => (
                      <tr key={ad.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {ad.adTitle}
                            </div>
                            <div className="text-sm text-gray-500">
                              ID: {ad.adId}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <Building2 size={16} className="text-gray-400 mr-3" />
                            <div className="text-sm text-gray-900">
                              {ad.businessName}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(ad.status)}`}>
                            {getStatusIcon(ad.status)}
                            <span className="ml-1">
                              {ad.status === 'active' ? 'Active' :
                               ad.status === 'completed' ? 'Terminée' :
                               ad.status === 'cancelled' ? 'Annulée' : 'En attente'}
                            </span>
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatAmount(ad.amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div>
                            <div>{ad.duration} jours</div>
                            <div className="text-xs">
                              {new Date(ad.startDate).toLocaleDateString('fr-FR')} - {new Date(ad.endDate).toLocaleDateString('fr-FR')}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div>
                            <div>{ad.impressions.toLocaleString()} vues</div>
                            <div>{ad.clicks} clics</div>
                            <div className="text-xs text-gray-400">
                              CTR: {((ad.clicks / ad.impressions) * 100).toFixed(2)}%
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              leftIcon={<Eye size={14} />}
                              onClick={() => handleViewAdPayment(ad)}
                            >
                              Voir
                            </Button>
                            {ad.status === 'active' && (
                              <Button
                                variant="outline"
                                size="sm"
                                leftIcon={<Ban size={14} />}
                                className="text-red-600 border-red-300 hover:bg-red-50"
                                onClick={() => handleStopAdPayment(ad)}
                              >
                                Arrêter
                              </Button>
                            )}
                            {ad.status === 'pending' && (
                              <Button
                                variant="outline"
                                size="sm"
                                leftIcon={<CheckSquare size={14} />}
                                className="text-green-600 border-green-300 hover:bg-green-50"
                                onClick={() => handleResumeAdPayment(ad.id)}
                              >
                                Reprendre
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Onglet Transactions */}
      {activeTab === 'transactions' && (
        <div className="space-y-6">
          {/* Filtres */}
          <Card>
            <CardBody className="p-4">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-64">
                  <div className="relative">
                    <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Rechercher une transaction..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Tous les statuts</option>
                  <option value="completed">Complétées</option>
                  <option value="pending">En attente</option>
                  <option value="failed">Échouées</option>
                  <option value="refunded">Remboursées</option>
                </select>
                <select
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Tous les types</option>
                  <option value="subscription">Abonnements</option>
                  <option value="advertisement">Publicités</option>
                </select>
              </div>
            </CardBody>
          </Card>

          {/* Liste des transactions */}
          <Card>
            <CardBody className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Transaction
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Entreprise
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Montant
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {transactions.map((transaction) => (
                      <tr key={transaction.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {transaction.reference}
                            </div>
                            <div className="text-sm text-gray-500">
                              {transaction.description}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <Building2 size={16} className="text-gray-400 mr-3" />
                            <div className="text-sm text-gray-900">
                              {transaction.businessName}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            transaction.type === 'subscription' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                          }`}>
                            {transaction.type === 'subscription' ? 'Abonnement' : 'Publicité'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatAmount(transaction.amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(transaction.status)}`}>
                            {getStatusIcon(transaction.status)}
                            <span className="ml-1">
                              {transaction.status === 'completed' ? 'Complétée' :
                               transaction.status === 'pending' ? 'En attente' :
                               transaction.status === 'failed' ? 'Échouée' : 'Remboursée'}
                            </span>
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div>
                            <div>{new Date(transaction.transactionDate).toLocaleDateString('fr-FR')}</div>
                            <div className="text-xs">{transaction.paymentMethod}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              leftIcon={<Eye size={14} />}
                            >
                              Détails
                            </Button>
                            {transaction.status === 'completed' && (
                              <Button
                                variant="outline"
                                size="sm"
                                leftIcon={<RefreshCw size={14} />}
                                className="text-orange-600 border-orange-300 hover:bg-orange-50"
                              >
                                Rembourser
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Modals */}
      <SubscriptionDetailsModal
        subscription={selectedSubscription}
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedSubscription(null);
        }}
        onSuspend={handleSuspendSubscription}
        onReactivate={handleReactivateSubscription}
      />

      <SuspendSubscriptionModal
        subscription={selectedSubscription}
        isOpen={isSuspendModalOpen}
        onClose={() => {
          setIsSuspendModalOpen(false);
          setSelectedSubscription(null);
        }}
        onConfirm={handleConfirmSuspension}
      />

      {/* Modals pour les publicités */}
      <AdPaymentDetailsModal
        adPayment={selectedAdPayment}
        isOpen={isAdDetailsModalOpen}
        onClose={() => {
          setIsAdDetailsModalOpen(false);
          setSelectedAdPayment(null);
        }}
        onStop={handleStopAdPayment}
        onPause={handlePauseAdPayment}
        onResume={handleResumeAdPayment}
      />

      <StopAdModal
        adPayment={selectedAdPayment}
        isOpen={isStopAdModalOpen}
        onClose={() => {
          setIsStopAdModalOpen(false);
          setSelectedAdPayment(null);
        }}
        onConfirm={handleConfirmStopAd}
      />

      {/* Modal pour les codes d'abonnement */}
      <SubscriptionCodeModal
        code={selectedCode}
        isOpen={isCodeModalOpen}
        onClose={() => {
          setIsCodeModalOpen(false);
          setSelectedCode(null);
        }}
        onValidate={handleValidateCode}
        onReject={handleRejectCode}
      />
    </div>
  );
};

export default PaymentManagement;
