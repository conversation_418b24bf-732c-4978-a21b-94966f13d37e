import React, { useState } from 'react';
import { Package, Clock, CheckCircle, XCircle, Eye, Truck, Calendar, User, Phone, MapPin, CreditCard } from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';

interface Order {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  customerAddress: string;
  products: {
    id: string;
    name: string;
    quantity: number;
    price: number;
    image: string;
  }[];
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed';
  orderDate: string;
  deliveryDate?: string;
  notes?: string;
}

interface BusinessOrdersManagerProps {
  businessId: string;
}

const BusinessOrdersManager: React.FC<BusinessOrdersManagerProps> = ({ businessId }) => {
  const [activeFilter, setActiveFilter] = useState<'all' | 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled'>('all');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  // Données fictives pour la démonstration
  const orders: Order[] = [
    {
      id: '1',
      orderNumber: 'CMD-2025-001',
      customerName: 'Aminata Traoré',
      customerPhone: '+225 07 12 34 56 78',
      customerAddress: 'Cocody, Abidjan, Côte d\'Ivoire',
      products: [
        {
          id: '1',
          name: 'Crème hydratante visage',
          quantity: 2,
          price: 15000,
          image: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=100&h=100&fit=crop'
        },
        {
          id: '2',
          name: 'Sérum anti-âge',
          quantity: 1,
          price: 25000,
          image: 'https://images.unsplash.com/photo-1571781926291-c477ebfd024b?w=100&h=100&fit=crop'
        }
      ],
      totalAmount: 55000,
      status: 'pending',
      paymentStatus: 'paid',
      orderDate: '2025-01-15T10:30:00Z',
      notes: 'Livraison urgente demandée'
    },
    {
      id: '2',
      orderNumber: 'CMD-2025-002',
      customerName: 'Kouassi Jean',
      customerPhone: '+225 05 98 76 54 32',
      customerAddress: 'Plateau, Abidjan, Côte d\'Ivoire',
      products: [
        {
          id: '3',
          name: 'Parfum femme 50ml',
          quantity: 1,
          price: 35000,
          image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=100&h=100&fit=crop'
        }
      ],
      totalAmount: 35000,
      status: 'confirmed',
      paymentStatus: 'paid',
      orderDate: '2025-01-14T14:20:00Z',
      deliveryDate: '2025-01-16T00:00:00Z'
    },
    {
      id: '3',
      orderNumber: 'CMD-2025-003',
      customerName: 'Fatou Diallo',
      customerPhone: '+225 01 23 45 67 89',
      customerAddress: 'Yopougon, Abidjan, Côte d\'Ivoire',
      products: [
        {
          id: '4',
          name: 'Kit de maquillage complet',
          quantity: 1,
          price: 45000,
          image: 'https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=100&h=100&fit=crop'
        }
      ],
      totalAmount: 45000,
      status: 'delivered',
      paymentStatus: 'paid',
      orderDate: '2025-01-10T09:15:00Z',
      deliveryDate: '2025-01-12T16:30:00Z'
    }
  ];

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'confirmed':
        return 'Confirmée';
      case 'shipped':
        return 'Expédiée';
      case 'delivered':
        return 'Livrée';
      case 'cancelled':
        return 'Annulée';
      default:
        return 'Inconnu';
    }
  };

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return <Clock size={16} />;
      case 'confirmed':
        return <CheckCircle size={16} />;
      case 'shipped':
        return <Truck size={16} />;
      case 'delivered':
        return <CheckCircle size={16} />;
      case 'cancelled':
        return <XCircle size={16} />;
      default:
        return <Package size={16} />;
    }
  };

  const filteredOrders = activeFilter === 'all' 
    ? orders 
    : orders.filter(order => order.status === activeFilter);

  const updateOrderStatus = (orderId: string, newStatus: Order['status']) => {
    // Ici, vous implémenteriez la logique de mise à jour du statut
    console.log(`Mise à jour du statut de la commande ${orderId} vers ${newStatus}`);
  };

  const filters = [
    { key: 'all', label: 'Toutes', count: orders.length },
    { key: 'pending', label: 'En attente', count: orders.filter(o => o.status === 'pending').length },
    { key: 'confirmed', label: 'Confirmées', count: orders.filter(o => o.status === 'confirmed').length },
    { key: 'shipped', label: 'Expédiées', count: orders.filter(o => o.status === 'shipped').length },
    { key: 'delivered', label: 'Livrées', count: orders.filter(o => o.status === 'delivered').length },
    { key: 'cancelled', label: 'Annulées', count: orders.filter(o => o.status === 'cancelled').length },
  ];

  return (
    <div className="space-y-6">
      {/* En-tête avec statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardBody>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <Package size={20} />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">{orders.length}</h3>
                <p className="text-sm text-gray-500">Total commandes</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
                <Clock size={20} />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {orders.filter(o => o.status === 'pending').length}
                </h3>
                <p className="text-sm text-gray-500">En attente</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <CheckCircle size={20} />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {orders.filter(o => o.status === 'delivered').length}
                </h3>
                <p className="text-sm text-gray-500">Livrées</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                <CreditCard size={20} />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {orders.reduce((sum, order) => sum + order.totalAmount, 0).toLocaleString()} F CFA
                </h3>
                <p className="text-sm text-gray-500">Chiffre d'affaires</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Filtres */}
      <Card>
        <CardBody>
          <div className="flex flex-wrap gap-2">
            {filters.map((filter) => (
              <button
                key={filter.key}
                onClick={() => setActiveFilter(filter.key as any)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeFilter === filter.key
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {filter.label} ({filter.count})
              </button>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Liste des commandes */}
      <div className="space-y-4">
        {filteredOrders.map((order) => (
          <Card key={order.id}>
            <CardBody>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-4 mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      {order.orderNumber}
                    </h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {getStatusIcon(order.status)}
                      <span className="ml-1">{getStatusLabel(order.status)}</span>
                    </span>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      order.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {order.paymentStatus === 'paid' ? 'Payé' : 'Non payé'}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <div className="flex items-center text-sm text-gray-600 mb-2">
                        <User size={16} className="mr-2" />
                        <span>{order.customerName}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600 mb-2">
                        <Phone size={16} className="mr-2" />
                        <span>{order.customerPhone}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin size={16} className="mr-2" />
                        <span>{order.customerAddress}</span>
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center text-sm text-gray-600 mb-2">
                        <Calendar size={16} className="mr-2" />
                        <span>Commandé le {new Date(order.orderDate).toLocaleDateString('fr-FR')}</span>
                      </div>
                      {order.deliveryDate && (
                        <div className="flex items-center text-sm text-gray-600 mb-2">
                          <Truck size={16} className="mr-2" />
                          <span>Livré le {new Date(order.deliveryDate).toLocaleDateString('fr-FR')}</span>
                        </div>
                      )}
                      <div className="text-lg font-bold text-gray-900">
                        {order.totalAmount.toLocaleString()} F CFA
                      </div>
                    </div>
                  </div>

                  {/* Produits */}
                  <div className="border-t pt-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Produits commandés :</h4>
                    <div className="space-y-2">
                      {order.products.map((product) => (
                        <div key={product.id} className="flex items-center space-x-3">
                          <img
                            src={product.image}
                            alt={product.name}
                            className="w-12 h-12 rounded-lg object-cover"
                          />
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">{product.name}</p>
                            <p className="text-sm text-gray-500">
                              Quantité: {product.quantity} × {product.price.toLocaleString()} F CFA
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {order.notes && (
                    <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                      <p className="text-sm text-yellow-800">
                        <strong>Note :</strong> {order.notes}
                      </p>
                    </div>
                  )}
                </div>

                <div className="flex flex-col space-y-2 ml-4">
                  <Button
                    variant="outline"
                    size="sm"
                    leftIcon={<Eye size={16} />}
                    onClick={() => setSelectedOrder(order)}
                  >
                    Détails
                  </Button>
                  
                  {order.status === 'pending' && (
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => updateOrderStatus(order.id, 'confirmed')}
                    >
                      Confirmer
                    </Button>
                  )}
                  
                  {order.status === 'confirmed' && (
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => updateOrderStatus(order.id, 'shipped')}
                    >
                      Expédier
                    </Button>
                  )}
                  
                  {order.status === 'shipped' && (
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => updateOrderStatus(order.id, 'delivered')}
                    >
                      Marquer livré
                    </Button>
                  )}
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      {filteredOrders.length === 0 && (
        <Card>
          <CardBody>
            <div className="text-center py-8">
              <Package size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune commande</h3>
              <p className="text-gray-500">
                {activeFilter === 'all' 
                  ? 'Vous n\'avez encore reçu aucune commande.'
                  : `Aucune commande avec le statut "${getStatusLabel(activeFilter as Order['status'])}".`
                }
              </p>
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default BusinessOrdersManager;
