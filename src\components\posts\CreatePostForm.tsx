import React, { useState } from 'react';
import { Image, X, Star, AlertCircle, Info } from 'lucide-react';
import { PostType, UserRole } from '../../types/index';
import { useAuth } from '../../context/AuthContext';
import { usePosts } from '../../context/PostsContext';
import Button from '../ui/Button';
import Card, { CardBody, CardFooter } from '../ui/Card';
import Avatar from '../ui/Avatar';


const CreatePostForm: React.FC = () => {
  const { currentUser } = useAuth();
  const { createPost } = usePosts();

  const [isOpen, setIsOpen] = useState(false);
  const [postType, setPostType] = useState<PostType>(PostType.FAVORITE);
  // const [title, setTitle] = useState(''); // Title removed
  const [businessName, setBusinessName] = useState(currentUser?.role === UserRole.BUSINESS ? currentUser.username : '');
  const [productName, setProductName] = useState('');
  const [category, setCategory] = useState('');
  const [description, setDescription] = useState('');
  const [imageFiles, setImageFiles] = useState<File[]>([]); // Changed to File[]
  const [imagePreviews, setImagePreviews] = useState<string[]>([]); // For displaying previews
  const [rating, setRating] = useState(0);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');

  if (!currentUser) return null;

  const handleAddTag = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && tagInput.trim()) {
      e.preventDefault();
      setTags([...tags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (index: number) => {
    setTags(tags.filter((_, i) => i !== index));
  };

  const handleImageFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files);
      const newImageFiles = [...imageFiles, ...filesArray].slice(0, 3); // Limit to 3 images
      setImageFiles(newImageFiles);

      const newImagePreviews = newImageFiles.map(file => URL.createObjectURL(file));
      // Clean up old object URLs before setting new ones to prevent memory leaks
      imagePreviews.forEach(url => URL.revokeObjectURL(url));
      setImagePreviews(newImagePreviews);
    }
  };

  const handleRemoveImage = (index: number) => {
    const newImageFiles = imageFiles.filter((_, i) => i !== index);
    setImageFiles(newImageFiles);

    const newImagePreviews = newImageFiles.map(file => URL.createObjectURL(file));
    imagePreviews.forEach(url => URL.revokeObjectURL(url)); // Clean up old
    setImagePreviews(newImagePreviews);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation différente selon le type de post et le rôle
    if (currentUser.role === UserRole.BUSINESS) {
      // Debug pour les entreprises
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Validation entreprise:', {
          businessName: businessName,
          productName: productName,
          category: category,
          description: description,
          businessNameValid: !!businessName,
          productNameValid: !!productName,
          categoryValid: !!category,
          descriptionValid: !!description
        });
      }

      // Pour les entreprises (demande d'avis) : pas de rating requis
      if (!businessName || !productName || !category || !description) {
        alert('Veuillez remplir tous les champs obligatoires.');
        return;
      }
    } else {
      // Pour les utilisateurs standards (coup de coeur/gueule) : rating requis
      if (!businessName || !productName || !category || !description || rating === 0) {
        alert('Veuillez remplir tous les champs obligatoires et donner une note.');
        return;
      }
    }

    // Debug des images avant envoi
    if (process.env.NODE_ENV === 'development') {
      console.log('📤 Envoi du post avec images:', {
        imageFilesCount: imageFiles.length,
        imageFiles: imageFiles.map(file => ({
          name: file.name,
          size: file.size,
          type: file.type
        })),
        postType: postType,
        businessName: businessName,
        userId: currentUser.id
      });
    }

    await createPost({
      // title, // Title removed
      type: postType,
      businessName,
      productName,
      category,
      description,
      images: imageFiles, // Pass File[] - will be handled in PostsContext
      rating: currentUser.role === UserRole.BUSINESS ? undefined : rating, // Pas de rating pour les entreprises
      tags,
      userId: currentUser.id,
      username: currentUser.username, // Pass username
      userProfilePicture: currentUser.profilePicture, // Pass profile pic
      authorRole: currentUser.role // Pass user role
    });

    // Reset form
    // setTitle(''); // Title removed
    setBusinessName('');
    setProductName('');
    setCategory('');
    setDescription('');
    setImageFiles([]); // Reset file state
    imagePreviews.forEach(url => URL.revokeObjectURL(url)); // Clean up previews
    setImagePreviews([]);
    setRating(0);
    setTags([]);
    setIsOpen(false);
  };

  const categories = [
    'Électronique', 'Maison & Jardin', 'Mode', 'Beauté & Santé',
    'Sports & Loisirs', 'Alimentation', 'Automobile', 'Services'
  ];

  // Si l'utilisateur est une entreprise, afficher le formulaire pour "Demande d'avis"
  if (currentUser.role === UserRole.BUSINESS) {
    // Initial post creation box pour les entreprises
    if (!isOpen) {
      return (
        <Card className="mb-4">
          <CardBody className="p-3">
            <div className="flex items-center">
              <Avatar
                src={currentUser.profilePicture}
                alt={currentUser.username}
                size="md"
              />
              <button
                className="ml-2 flex-1 text-left px-4 py-2 bg-blue-100 hover:bg-blue-200 rounded-full text-blue-700"
                onClick={() => {
                  setPostType(PostType.REVIEW);
                  setIsOpen(true);
                }}
              >
                Demander l'avis des utilisateurs sur vos produits...
              </button>
            </div>
          </CardBody>
          <CardFooter className="flex justify-center">
            <Button
              variant="primary"
              size="sm"
              leftIcon={<Info className="text-white" size={18} />}
              className="flex-1 bg-blue-600 hover:bg-blue-700"
              onClick={() => {
                setPostType(PostType.REVIEW);
                setIsOpen(true);
              }}
            >
              Demande d'avis
            </Button>
          </CardFooter>
        </Card>
      );
    }

    // Formulaire étendu pour les entreprises (Demande d'avis uniquement)
    return (
      <Card className="mb-4">
        <div className="p-4 border-b border-gray-200 bg-blue-50">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-medium text-blue-900">
              Demander l'avis des utilisateurs
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
            >
              <X size={18} />
            </Button>
          </div>
          <p className="text-sm text-blue-700 mt-2">
            Présentez votre produit ou service et recueillez les avis de la communauté
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <CardBody className="p-4">
            {/* Pas de sélection de type pour les entreprises - toujours REVIEW */}
            <input type="hidden" value={PostType.REVIEW} />

            {/* Nom de l'entreprise */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nom de l'entreprise *
                {process.env.NODE_ENV === 'development' && (
                  <span className={`ml-2 text-xs ${businessName ? 'text-green-600' : 'text-red-600'}`}>
                    {businessName ? '✓' : '✗'}
                  </span>
                )}
              </label>
              <input
                type="text"
                value={businessName}
                onChange={(e) => setBusinessName(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  businessName ? 'border-green-300' : 'border-red-300'
                }`}
                placeholder="Nom de votre entreprise"
                required
              />
            </div>

            {/* Nom du produit/service */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Produit ou Service *
                {process.env.NODE_ENV === 'development' && (
                  <span className={`ml-2 text-xs ${productName ? 'text-green-600' : 'text-red-600'}`}>
                    {productName ? '✓' : '✗'}
                  </span>
                )}
              </label>
              <input
                type="text"
                value={productName}
                onChange={(e) => setProductName(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  productName ? 'border-green-300' : 'border-red-300'
                }`}
                placeholder="Nom du produit ou service"
                required
              />
            </div>

            {/* Catégorie */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Catégorie *
                {process.env.NODE_ENV === 'development' && (
                  <span className={`ml-2 text-xs ${category ? 'text-green-600' : 'text-red-600'}`}>
                    {category ? '✓' : '✗'}
                  </span>
                )}
              </label>
              <select
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  category ? 'border-green-300' : 'border-red-300'
                }`}
                required
              >
                <option value="">Sélectionnez une catégorie</option>
                <option value="Technologie">Technologie</option>
                <option value="Mode">Mode</option>
                <option value="Alimentation">Alimentation</option>
                <option value="Services">Services</option>
                <option value="Santé">Santé</option>
                <option value="Éducation">Éducation</option>
                <option value="Autre">Autre</option>
              </select>
            </div>

            {/* Description */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
                {process.env.NODE_ENV === 'development' && (
                  <span className={`ml-2 text-xs ${description ? 'text-green-600' : 'text-red-600'}`}>
                    {description ? '✓' : '✗'}
                  </span>
                )}
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={4}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  description ? 'border-green-300' : 'border-red-300'
                }`}
                placeholder="Décrivez votre produit ou service et ce sur quoi vous aimeriez avoir des avis..."
                required
              />
            </div>

            {/* Images */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Images du produit/service
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageFileChange}
                  className="hidden"
                  id="image-upload"
                />
                <label
                  htmlFor="image-upload"
                  className="cursor-pointer flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  <Image size={18} className="mr-2" />
                  Ajouter des images
                </label>
              </div>

              {/* Image previews */}
              {imagePreviews.length > 0 && (
                <div className="mt-3 grid grid-cols-3 gap-2">
                  {imagePreviews.map((preview, index) => (
                    <div key={index} className="relative">
                      <img
                        src={preview}
                        alt={`Preview ${index + 1}`}
                        className="w-full h-24 object-cover rounded-md"
                      />
                      <button
                        type="button"
                        onClick={() => handleRemoveImage(index)}
                        className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                      >
                        <X size={12} />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardBody>

          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              variant="primary"
              className="bg-blue-600 hover:bg-blue-700"
            >
              Publier la demande d'avis
            </Button>
          </CardFooter>
        </form>
      </Card>
    );
  }

  // Initial post creation box pour les utilisateurs standards
  if (!isOpen) {
    return (
      <Card className="mb-4">
        <CardBody className="p-3">
          <div className="flex items-center">
            <Avatar
              src={currentUser.profilePicture}
              alt={currentUser.username}
              size="md"
            />
            <button
              className="ml-2 flex-1 text-left px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-full text-gray-600"
              onClick={() => setIsOpen(true)}
            >
              Partagez votre expérience...
            </button>
          </div>
        </CardBody>
        <CardFooter className="flex justify-between">
          <Button
            variant="ghost"
            size="sm"
            leftIcon={<Star className="text-amber-500" size={18} />}
            className="flex-1 text-gray-700"
            onClick={() => {
              setPostType(PostType.FAVORITE);
              setIsOpen(true);
            }}
          >
            Coup de Coeur
          </Button>
          <Button
            variant="ghost"
            size="sm"
            leftIcon={<AlertCircle className="text-red-500" size={18} />}
            className="flex-1 text-gray-700"
            onClick={() => {
              setPostType(PostType.COMPLAINT);
              setIsOpen(true);
            }}
          >
            Coup de Gueule
          </Button>
        </CardFooter>
      </Card>
    );
  }

  // Expanded post creation form
  return (
    <Card className="mb-4">
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-xl font-medium text-gray-900">
            {postType === PostType.FAVORITE ? 'Partagez un Coup de Coeur' : 'Partagez un Coup de Gueule'}
          </h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(false)}
          >
            <X size={18} />
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <CardBody className="p-4">
          {/* Post Type Selection */}
          <div className="mb-4">
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant={postType === PostType.FAVORITE ? "primary" : "outline"}
                onClick={() => setPostType(PostType.FAVORITE)}
                type="button"
                leftIcon={<Star size={16} />}
              >
                Coup de Coeur
              </Button>
              <Button
                size="sm"
                variant={postType === PostType.COMPLAINT ? "primary" : "outline"}
                onClick={() => setPostType(PostType.COMPLAINT)}
                type="button"
                leftIcon={<AlertCircle size={16} />}
              >
                Coup de Gueule
              </Button>
            </div>
          </div>

          {/* Title field removed */}

          {/* Business and Product */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="business" className="block text-sm font-medium text-gray-700 mb-1">
                Entreprise concernée
              </label>
              <input
                type="text"
                id="business"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Nom de l'entreprise"
                value={businessName}
                onChange={(e) => setBusinessName(e.target.value)}
                required
              />
            </div>
            <div>
              <label htmlFor="product" className="block text-sm font-medium text-gray-700 mb-1">
                Produit ou service
              </label>
              <input
                type="text"
                id="product"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Nom du produit ou service"
                value={productName}
                onChange={(e) => setProductName(e.target.value)}
                required
              />
            </div>
          </div>

          {/* Category */}
          <div className="mb-4">
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
              Catégorie
            </label>
            <select
              id="category"
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              required
            >
              <option value="">Sélectionnez une catégorie</option>
              {categories.map((cat) => (
                <option key={cat} value={cat}>{cat}</option>
              ))}
            </select>
          </div>

          {/* Description */}
          <div className="mb-4">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              rows={4}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Partagez votre expérience en détail..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              required
            />
          </div>

          {/* Rating */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Votre note
            </label>
            <div className="flex space-x-1">
              {Array.from({ length: 5 }).map((_, i) => (
                <button
                  key={i}
                  type="button"
                  className="text-2xl focus:outline-none"
                  onClick={() => setRating(i + 1)}
                >
                  <span className={i < rating ? 'text-amber-400' : 'text-gray-300'}>★</span>
                </button>
              ))}
            </div>
          </div>

          {/* Images */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Images (max 3)
            </label>
            <input
              type="file"
              id="images"
              multiple
              accept="image/*"
              onChange={handleImageFileChange}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 mb-2"
              disabled={imageFiles.length >= 3}
            />
            <div className="flex flex-wrap gap-2 mb-2">
              {imagePreviews.map((previewUrl, index) => (
                <div key={index} className="relative">
                  <img
                    src={previewUrl}
                    alt={`Preview ${index + 1}`}
                    className="w-20 h-20 object-cover rounded"
                  />
                  <button
                    type="button"
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-0.5 leading-none" // Adjusted padding
                    onClick={() => handleRemoveImage(index)}
                  >
                    <X size={12} />
                  </button>
                </div>
              ))}
            </div>
            {imageFiles.length < 3 && <p className="text-xs text-gray-500">Vous pouvez ajouter jusqu'à {3 - imageFiles.length} image(s) supplémentaire(s).</p>}
          </div>

          {/* Tags */}
          <div className="mb-4">
            <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-1">
              Tags (optionnels)
            </label>
            <div className="flex flex-wrap gap-2 mb-2">
              {tags.map((tag, index) => (
                <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  #{tag}
                  <button
                    type="button"
                    className="ml-1 text-blue-500 hover:text-blue-700"
                    onClick={() => handleRemoveTag(index)}
                  >
                    <X size={12} />
                  </button>
                </span>
              ))}
            </div>
            <input
              type="text"
              id="tags"
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Ajouter des tags (appuyez sur Entrée pour ajouter)"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyDown={handleAddTag}
            />
          </div>
        </CardBody>

        <CardFooter className="flex justify-end space-x-3">
          <Button
            variant="outline"
            type="button"
            onClick={() => setIsOpen(false)}
          >
            Annuler
          </Button>
          <Button
            variant="primary"
            type="submit"
          >
            Publier
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
};

export default CreatePostForm;
