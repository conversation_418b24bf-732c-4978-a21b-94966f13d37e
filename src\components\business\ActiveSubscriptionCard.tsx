import React from 'react';
import { Crown, Calendar, Clock, CheckCircle, AlertTriangle, RefreshCw } from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import { SubscriptionStatus } from '../../services/subscriptionService';

interface ActiveSubscriptionCardProps {
  status: SubscriptionStatus;
  onRefresh: () => void;
  isRefreshing?: boolean;
}

const ActiveSubscriptionCard: React.FC<ActiveSubscriptionCardProps> = ({
  status,
  onRefresh,
  isRefreshing = false
}) => {
  // Déterminer la couleur et l'icône selon le statut
  const getStatusConfig = () => {
    if (!status.isActive) {
      return {
        bgColor: 'border-red-200 bg-red-50',
        badgeColor: 'bg-red-100 text-red-700',
        textColor: 'text-red-600',
        icon: <AlertTriangle size={20} className="text-red-500" />,
        label: 'Expiré',
        description: 'Votre abonnement a expiré'
      };
    }

    if (status.daysRemaining <= 3) {
      return {
        bgColor: 'border-orange-200 bg-orange-50',
        badgeColor: 'bg-orange-100 text-orange-700',
        textColor: 'text-orange-600',
        icon: <Clock size={20} className="text-orange-500" />,
        label: 'Expire bientôt',
        description: 'Votre abonnement expire dans quelques jours'
      };
    }

    if (status.daysRemaining <= 7) {
      return {
        bgColor: 'border-yellow-200 bg-yellow-50',
        badgeColor: 'bg-yellow-100 text-yellow-700',
        textColor: 'text-yellow-600',
        icon: <Clock size={20} className="text-yellow-500" />,
        label: 'Expire bientôt',
        description: 'Pensez à renouveler votre abonnement'
      };
    }

    return {
      bgColor: 'border-green-200 bg-green-50',
      badgeColor: 'bg-green-100 text-green-700',
      textColor: 'text-green-600',
      icon: <CheckCircle size={20} className="text-green-500" />,
      label: 'Actif',
      description: 'Votre abonnement est actif'
    };
  };

  const config = getStatusConfig();

  // Affichage du compteur
  const CountdownDisplay = () => (
    <div className="text-center">
      <div className={`text-4xl font-bold ${config.textColor} mb-1 transition-all duration-300`}>
        {status.daysRemaining}
      </div>
      <div className="text-sm text-gray-600">
        jour{status.daysRemaining > 1 ? 's' : ''} restant{status.daysRemaining > 1 ? 's' : ''}
      </div>
    </div>
  );

  return (
    <Card className={`${config.bgColor} shadow-lg hover:shadow-xl transition-all duration-300`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-white rounded-lg shadow-sm">
              <Crown className="text-yellow-500" size={24} />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Abonnement Actuel</h3>
              <p className="text-sm text-gray-600">{config.description}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium ${config.badgeColor}`}>
              {config.icon}
              <span>{config.label}</span>
            </span>
          </div>
        </div>
      </CardHeader>

      <CardBody className="pt-0">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Informations de la formule */}
          <div className="md:col-span-2">
            <div className="bg-white rounded-xl p-4 shadow-sm">
              <h4 className="font-bold text-xl text-gray-900 mb-2">{status.planName}</h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Type d'abonnement</span>
                  <span className="font-medium text-gray-900">
                    {status.isTrialPeriod ? 'Période d\'essai' : 'Abonnement payant'}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Date de début</span>
                  <span className="font-medium text-gray-900">
                    {new Date(status.startDate).toLocaleDateString('fr-FR')}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Date d'expiration</span>
                  <span className="font-medium text-gray-900">
                    {new Date(status.endDate).toLocaleDateString('fr-FR')}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Renouvellement automatique</span>
                  <span className={`font-medium ${status.autoRenew ? 'text-green-600' : 'text-gray-600'}`}>
                    {status.autoRenew ? 'Activé' : 'Désactivé'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Compteur de jours */}
          <div className="flex flex-col items-center justify-center">
            <div className="bg-white rounded-xl p-6 shadow-sm w-full text-center">
              <div className="mb-4">
                <Calendar className="mx-auto text-gray-400" size={32} />
              </div>
              
              {status.isActive ? (
                <CountdownDisplay />
              ) : (
                <div className="text-center">
                  <div className="text-4xl font-bold text-red-500 mb-1">0</div>
                  <div className="text-sm text-gray-600">Expiré</div>
                </div>
              )}
              
              <div className="mt-4 pt-4 border-t border-gray-200">
                <Button
                  onClick={onRefresh}
                  variant="outline"
                  size="sm"
                  disabled={isRefreshing}
                  className="w-full"
                >
                  {isRefreshing ? (
                    <>
                      <RefreshCw size={16} className="animate-spin mr-2" />
                      Actualisation...
                    </>
                  ) : (
                    <>
                      <RefreshCw size={16} className="mr-2" />
                      Actualiser
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Barre de progression */}
        {status.isActive && (
          <div className="mt-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progression de l'abonnement</span>
              <span>{Math.round((1 - status.daysRemaining / 30) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-1000 ease-out ${
                  status.daysRemaining <= 3 ? 'bg-red-500' :
                  status.daysRemaining <= 7 ? 'bg-yellow-500' :
                  'bg-green-500'
                }`}
                style={{ width: `${Math.round((1 - status.daysRemaining / 30) * 100)}%` }}
              />
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default ActiveSubscriptionCard;
