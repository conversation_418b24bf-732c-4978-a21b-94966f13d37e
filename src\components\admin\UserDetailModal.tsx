import React from 'react';
import { 
  X, User, Mail, MapPin, Calendar, Building, Shield, 
  CheckCircle, XCircle, Crown, Award, Globe, Phone,
  MessageSquare, Heart, TrendingUp, Clock
} from 'lucide-react';
import Button from '../ui/Button';
import VerificationBadge from '../ui/VerificationBadge';
import { UserRole, UserStatus } from '../../types';

interface User {
  id: string;
  username: string;
  email: string;
  role: UserRole;
  status: UserStatus;
  profile_picture?: string;
  created_at: string;
  last_login?: string;
  is_verified?: boolean;
  post_count?: number;
  followers_count?: number;
  following_count?: number;
  comment_count?: number;
  recommendations_count?: number;
  country?: string;
  city?: string;
  age?: number;
  gender?: string;
  bio?: string;
  website?: string;
  phone?: string;
  profession?: string;
  interests?: string;
  profile_completed_at?: string;
  suspended_until?: string;
  suspension_reason?: string;
  business_name?: string;
  business_status?: string;
  business_category?: string;
  business_description?: string;
  business_verified?: boolean;
}

interface UserDetailModalProps {
  user: User;
  onClose: () => void;
}

const UserDetailModal: React.FC<UserDetailModalProps> = ({ user, onClose }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getVerificationStatus = () => {
    if (user.role === UserRole.ADMIN) {
      return {
        status: 'Administrateur Vérifié',
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        icon: Crown
      };
    }

    if (user.role === UserRole.BUSINESS) {
      if (user.business_verified && user.is_verified) {
        return {
          status: 'Entreprise Complètement Vérifiée',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          icon: Shield
        };
      } else if (user.is_verified) {
        return {
          status: 'Compte Business Vérifié',
          color: 'text-blue-500',
          bgColor: 'bg-blue-50',
          icon: CheckCircle
        };
      } else {
        return {
          status: 'Entreprise Non Vérifiée',
          color: 'text-gray-500',
          bgColor: 'bg-gray-50',
          icon: XCircle
        };
      }
    }

    if (user.is_verified) {
      return {
        status: 'Utilisateur Vérifié',
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        icon: CheckCircle
      };
    }

    return {
      status: 'Non Vérifié',
      color: 'text-gray-500',
      bgColor: 'bg-gray-50',
      icon: XCircle
    };
  };

  const verificationInfo = getVerificationStatus();
  const IconComponent = verificationInfo.icon;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <img
              src={user.profile_picture || '/default-avatar.png'}
              alt={user.username}
              className="w-16 h-16 rounded-full object-cover"
            />
            <div>
              <div className="flex items-center space-x-3">
                <h2 className="text-2xl font-bold text-gray-900">{user.username}</h2>
                <VerificationBadge
                  isVerified={user.is_verified || false}
                  userRole={user.role}
                  businessVerified={user.role === UserRole.BUSINESS ? user.business_verified || false : false}
                  size="md"
                  variant="default"
                  showTooltip={true}
                />
              </div>
              <p className="text-gray-600">{user.email}</p>
            </div>
          </div>
          <Button variant="outline" onClick={onClose}>
            <X size={20} />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Informations de base */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations Personnelles</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <User className="w-5 h-5 text-gray-400" />
                  <span className="text-sm text-gray-600">Nom d'utilisateur:</span>
                  <span className="text-sm font-medium text-gray-900">{user.username}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="w-5 h-5 text-gray-400" />
                  <span className="text-sm text-gray-600">Email:</span>
                  <span className="text-sm font-medium text-gray-900">{user.email}</span>
                </div>
                {user.age && (
                  <div className="flex items-center space-x-3">
                    <Calendar className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-600">Âge:</span>
                    <span className="text-sm font-medium text-gray-900">{user.age} ans</span>
                  </div>
                )}
                {user.gender && (
                  <div className="flex items-center space-x-3">
                    <User className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-600">Genre:</span>
                    <span className="text-sm font-medium text-gray-900">{user.gender}</span>
                  </div>
                )}
                {user.profession && (
                  <div className="flex items-center space-x-3">
                    <Building className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-600">Profession:</span>
                    <span className="text-sm font-medium text-gray-900">{user.profession}</span>
                  </div>
                )}
                {(user.city || user.country) && (
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-600">Localisation:</span>
                    <span className="text-sm font-medium text-gray-900">
                      {user.city && user.country ? `${user.city}, ${user.country}` : user.country || user.city}
                    </span>
                  </div>
                )}
                {user.phone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-600">Téléphone:</span>
                    <span className="text-sm font-medium text-gray-900">{user.phone}</span>
                  </div>
                )}
                {user.website && (
                  <div className="flex items-center space-x-3">
                    <Globe className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-600">Site web:</span>
                    <a 
                      href={user.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-sm font-medium text-blue-600 hover:underline"
                    >
                      {user.website}
                    </a>
                  </div>
                )}
              </div>
            </div>

            {/* Statut de vérification */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Statut de Vérification</h3>
              <div className={`p-4 rounded-lg ${verificationInfo.bgColor} border border-gray-200`}>
                <div className="flex items-center space-x-3">
                  <IconComponent className={`w-6 h-6 ${verificationInfo.color}`} />
                  <div>
                    <p className={`font-medium ${verificationInfo.color}`}>{verificationInfo.status}</p>
                    <p className="text-sm text-gray-600 mt-1">
                      {user.is_verified 
                        ? `Compte vérifié le ${user.profile_completed_at ? formatDate(user.profile_completed_at) : 'Date inconnue'}`
                        : 'Ce compte n\'a pas encore été vérifié'
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Statistiques et activité */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Statistiques d'Activité</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <MessageSquare className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">Publications</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-600 mt-1">{user.post_count || 0}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Heart className="w-5 h-5 text-green-600" />
                    <span className="text-sm font-medium text-green-900">Followers</span>
                  </div>
                  <p className="text-2xl font-bold text-green-600 mt-1">{user.followers_count || 0}</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-5 h-5 text-purple-600" />
                    <span className="text-sm font-medium text-purple-900">Following</span>
                  </div>
                  <p className="text-2xl font-bold text-purple-600 mt-1">{user.following_count || 0}</p>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Award className="w-5 h-5 text-orange-600" />
                    <span className="text-sm font-medium text-orange-900">Recommandations</span>
                  </div>
                  <p className="text-2xl font-bold text-orange-600 mt-1">{user.recommendations_count || 0}</p>
                </div>
              </div>
            </div>

            {/* Informations business si applicable */}
            {user.role === UserRole.BUSINESS && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations Entreprise</h3>
                <div className="space-y-3">
                  {user.business_name && (
                    <div className="flex items-center space-x-3">
                      <Building className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-600">Nom:</span>
                      <span className="text-sm font-medium text-gray-900">{user.business_name}</span>
                    </div>
                  )}
                  {user.business_category && (
                    <div className="flex items-center space-x-3">
                      <Award className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-600">Catégorie:</span>
                      <span className="text-sm font-medium text-gray-900">{user.business_category}</span>
                    </div>
                  )}
                  {user.business_status && (
                    <div className="flex items-center space-x-3">
                      <Shield className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-600">Statut:</span>
                      <span className="text-sm font-medium text-gray-900">{user.business_status}</span>
                    </div>
                  )}
                  {user.business_description && (
                    <div className="mt-3">
                      <span className="text-sm text-gray-600">Description:</span>
                      <p className="text-sm text-gray-900 mt-1">{user.business_description}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Dates importantes */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Historique</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Calendar className="w-5 h-5 text-gray-400" />
                  <span className="text-sm text-gray-600">Inscription:</span>
                  <span className="text-sm font-medium text-gray-900">{formatDate(user.created_at)}</span>
                </div>
                {user.last_login && (
                  <div className="flex items-center space-x-3">
                    <Clock className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-600">Dernière connexion:</span>
                    <span className="text-sm font-medium text-gray-900">{formatDate(user.last_login)}</span>
                  </div>
                )}
                {user.suspended_until && new Date(user.suspended_until) > new Date() && (
                  <div className="flex items-center space-x-3">
                    <XCircle className="w-5 h-5 text-red-500" />
                    <span className="text-sm text-gray-600">Suspendu jusqu'au:</span>
                    <span className="text-sm font-medium text-red-600">{formatDate(user.suspended_until)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Bio */}
        {user.bio && (
          <div className="px-6 pb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Biographie</h3>
            <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">{user.bio}</p>
          </div>
        )}

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <Button onClick={onClose}>
            Fermer
          </Button>
        </div>
      </div>
    </div>
  );
};

export default UserDetailModal;
