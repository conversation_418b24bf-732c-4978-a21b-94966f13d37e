import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import Button from '../ui/Button';
import Card, { CardBody, CardHeader } from '../ui/Card';
import { UserRole, IBusinessUser, BusinessStatus } from '../../types';

const QuickLogin: React.FC = () => {
  const { currentUser, login, logout, simulateBusinessUser } = useAuth();
  const [loading, setLoading] = useState(false);

  const handleSimulateBusinessLogin = () => {
    setLoading(true);
    simulateBusinessUser();
    setTimeout(() => setLoading(false), 500);
  };

  const handleBusinessLogin = async () => {
    try {
      // Utiliser les identifiants de test pour une entreprise
      await login('<EMAIL>', 'password123');
    } catch (error) {
      console.error('Erreur de connexion business:', error);
    }
  };

  const handleUserLogin = async () => {
    try {
      // Utiliser les identifiants de test pour un utilisateur normal
      await login('<EMAIL>', 'password123');
    } catch (error) {
      console.error('Erreur de connexion utilisateur:', error);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Erreur de déconnexion:', error);
    }
  };

  return (
    <Card className="mb-4 border-blue-200 bg-blue-50">
      <CardHeader>
        <h3 className="text-lg font-semibold text-blue-800">🚀 Connexion Rapide (Test)</h3>
      </CardHeader>
      <CardBody>
        <div className="space-y-3">
          {!currentUser ? (
            <div className="space-y-2">
              <div className="flex space-x-2">
                <Button
                  onClick={handleSimulateBusinessLogin}
                  variant="primary"
                  size="sm"
                  disabled={loading}
                >
                  {loading ? 'Connexion...' : 'Simuler Entreprise (Dexima)'}
                </Button>
                <Button
                  onClick={handleUserLogin}
                  variant="outline"
                  size="sm"
                >
                  Se connecter comme Utilisateur
                </Button>
              </div>
              <div className="text-xs text-blue-600">
                💡 Utilisez "Simuler Entreprise" pour tester l'IA Conseiller
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-700">
                  Connecté en tant que: <strong>{currentUser.username}</strong>
                </p>
                <p className="text-xs text-blue-600">
                  Rôle: {currentUser.role === UserRole.BUSINESS ? 'Entreprise' : 'Utilisateur'}
                </p>
              </div>
              <Button
                onClick={handleLogout}
                variant="outline"
                size="sm"
              >
                Se déconnecter
              </Button>
            </div>
          )}
        </div>
      </CardBody>
    </Card>
  );
};

export default QuickLogin;
