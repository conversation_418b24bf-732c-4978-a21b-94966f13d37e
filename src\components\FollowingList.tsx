import React, { useState, useEffect } from 'react';
import { useFollow } from '../context/FollowContext'; 

interface FollowingListProps {
  userId: string;
}

interface Following {
  id: string;
  username: string;
  profile_picture?: string;
  role?: string;
  businessName?: string;
}

const FollowingList: React.FC<FollowingListProps> = ({ userId }) => { 
  const { getFollowingDetails } = useFollow(); 
  const [following, setFollowing] = useState<Following[]>([]); 
  const [loading, setLoading] = useState(true); 

  useEffect(() => { 
    const loadFollowing = async () => { 
      setLoading(true); 
      const data = await getFollowingDetails(userId); 
      setFollowing(data); 
      setLoading(false); 
    }; 

    loadFollowing(); 
  }, [userId, getFollowingDetails]); 

  if (loading) return <p>Chargement des abonnements...</p>; 

  return ( 
    <div className="following-list"> 
      <h3>Abonnements ({following.length})</h3> 
      {following.length > 0 ? ( 
        following.map(follow => (
          <div key={follow.id} className="following-item"> 
            <img 
              src={follow.profile_picture || '/default-avatar.png'} 
              alt={follow.role === 'business' && follow.businessName ? follow.businessName : follow.username} 
              className="following-avatar" 
            /> 
            <span className="following-username">{follow.role === 'business' && follow.businessName ? follow.businessName : follow.username}</span> 
          </div> 
        )) 
      ) : ( 
        <p>Aucun abonnement pour le moment</p> 
      )} 
    </div> 
  ); 
};

export default FollowingList;