import React from 'react';
import { useAuth } from '../../context/AuthContext';
import { UserRole } from '../../types';
import Card, { CardBody, CardHeader } from '../ui/Card';
import { useNavigate } from 'react-router-dom';

const SubscriptionDebug: React.FC = () => {
  const { currentUser, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  return (
    <Card className="mb-4 border-2 border-yellow-400 bg-yellow-50">
      <CardHeader>
        <h3 className="text-lg font-semibold text-yellow-800">
          🔍 Debug - État de l'Abonnement
        </h3>
      </CardHeader>
      <CardBody>
        <div className="space-y-3 text-sm">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <strong>Authentifié :</strong>
              <span className={`ml-2 px-2 py-1 rounded text-xs ${isAuthenticated ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {isAuthenticated ? '✅ Oui' : '❌ Non'}
              </span>
            </div>
            
            <div>
              <strong>Utilisateur :</strong>
              <span className="ml-2 text-gray-700">
                {currentUser ? currentUser.username || 'Utilisateur connecté' : 'Aucun'}
              </span>
            </div>
            
            <div>
              <strong>Rôle :</strong>
              <span className={`ml-2 px-2 py-1 rounded text-xs ${
                currentUser?.role === UserRole.BUSINESS ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {currentUser?.role || 'Non défini'}
              </span>
            </div>
            
            <div>
              <strong>Est Entreprise :</strong>
              <span className={`ml-2 px-2 py-1 rounded text-xs ${
                currentUser?.role === UserRole.BUSINESS ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {currentUser?.role === UserRole.BUSINESS ? '✅ Oui' : '❌ Non'}
              </span>
            </div>
          </div>
          
          {currentUser?.role === UserRole.BUSINESS && (
            <div className="mt-4 p-3 bg-green-100 rounded-lg">
              <strong className="text-green-800">✅ Compte Entreprise Détecté</strong>
              <div className="mt-2 text-green-700">
                <div><strong>Nom d'entreprise :</strong> {(currentUser as any).businessName || 'Non défini'}</div>
                <div><strong>ID :</strong> {currentUser.id}</div>
              </div>
            </div>
          )}
          
          {currentUser?.role !== UserRole.BUSINESS && (
            <div className="mt-4 p-3 bg-red-100 rounded-lg">
              <strong className="text-red-800">❌ Compte Non-Entreprise</strong>
              <div className="mt-2 text-red-700">
                L'onglet "Mon abonnement" n'est visible que pour les comptes entreprise.
                <br />
                <strong>Rôle actuel :</strong> {currentUser?.role || 'Non défini'}
              </div>
            </div>
          )}
          
          <div className="mt-4 p-3 bg-blue-100 rounded-lg">
            <strong className="text-blue-800">🔧 Actions de Debug</strong>
            <div className="mt-2 space-y-2">
              <button
                onClick={() => console.log('Current User:', currentUser)}
                className="px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
              >
                Log User dans Console
              </button>
              <button
                onClick={() => {
                  console.log('Auth State:', { isAuthenticated, currentUser });
                  console.log('User Role:', currentUser?.role);
                  console.log('Is Business:', currentUser?.role === UserRole.BUSINESS);
                }}
                className="ml-2 px-3 py-1 bg-purple-500 text-white rounded text-xs hover:bg-purple-600"
              >
                Log État Complet
              </button>
              <button
                onClick={() => {
                  console.log('🚀 Navigation vers abonnements...');
                  navigate('/business-subscription');
                }}
                className="ml-2 px-4 py-2 bg-green-500 text-white rounded font-bold hover:bg-green-600 text-sm"
              >
                🚀 ALLER AUX ABONNEMENTS
              </button>
              <button
                onClick={() => {
                  console.log('🏢 Navigation vers profil entreprise...');
                  navigate('/profile');
                }}
                className="ml-2 px-4 py-2 bg-blue-500 text-white rounded font-bold hover:bg-blue-600 text-sm"
              >
                🏢 PROFIL ENTREPRISE
              </button>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-gray-100 rounded-lg">
            <strong className="text-gray-800">📋 Instructions</strong>
            <div className="mt-2 text-gray-700 text-xs">
              <ol className="list-decimal list-inside space-y-1">
                <li>Vérifiez que vous êtes connecté avec un compte entreprise</li>
                <li>Le rôle doit être "business" (pas "user")</li>
                <li>Si le rôle est incorrect, utilisez QuickLogin pour changer</li>
                <li>L'onglet "Mon abonnement" apparaîtra automatiquement</li>
              </ol>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default SubscriptionDebug;
