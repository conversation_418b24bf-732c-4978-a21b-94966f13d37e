import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, BarChart3, PieChart, Activity } from 'lucide-react';
import { formatAmount, formatPercentage } from '../../utils/formatUtils';

interface ChartData {
  performance: {
    labels: string[];
    impressions: number[];
    clicks: number[];
    conversions: number[];
    spend: number[];
  };
  audience: {
    demographics: {
      age: Array<{ range: string; percentage: number }>;
      gender: Array<{ type: string; percentage: number }>;
      interests: Array<{ category: string; percentage: number }>;
    };
    geography: Array<{ city: string; percentage: number }>;
  };
  budget: {
    allocation: Array<{ campaign: string; spent: number; budget: number }>;
    timeline: Array<{ month: string; budget: number; spent: number }>;
  };
}

interface InteractiveChartsProps {
  data: ChartData;
  timeRange: string;
  onDataUpdate?: (newData: ChartData) => void;
}

const InteractiveCharts: React.FC<InteractiveChartsProps> = ({ 
  data, 
  timeRange, 
  onDataUpdate 
}) => {
  const [activeChart, setActiveChart] = useState<'performance' | 'audience' | 'budget'>('performance');
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [animationProgress, setAnimationProgress] = useState(0);

  useEffect(() => {
    // Animation d'entrée
    const timer = setTimeout(() => {
      setAnimationProgress(100);
    }, 100);
    return () => clearTimeout(timer);
  }, [activeChart, timeRange]);

  // Générer des données dynamiques basées sur la période
  const generateTimeBasedData = (baseData: ChartData, range: string): ChartData => {
    const multipliers = {
      '7d': { impressions: 1, clicks: 1, conversions: 1 },
      '30d': { impressions: 4.2, clicks: 4.1, conversions: 3.8 },
      '90d': { impressions: 12.5, clicks: 11.8, conversions: 10.2 },
      '1y': { impressions: 52, clicks: 48, conversions: 42 }
    };

    const mult = multipliers[range as keyof typeof multipliers] || multipliers['7d'];
    
    return {
      ...baseData,
      performance: {
        ...baseData.performance,
        impressions: baseData.performance.impressions.map(val => Math.round(val * mult.impressions)),
        clicks: baseData.performance.clicks.map(val => Math.round(val * mult.clicks)),
        conversions: baseData.performance.conversions.map(val => Math.round(val * mult.conversions)),
        spend: baseData.performance.spend.map(val => Math.round(val * mult.impressions))
      }
    };
  };

  const dynamicData = generateTimeBasedData(data, timeRange);

  const PerformanceChart = () => {
    const maxValue = Math.max(...dynamicData.performance.impressions);
    
    return (
      <div className="interactive-chart performance-chart">
        <div className="chart-header">
          <h3>Performance des campagnes</h3>
          <div className="chart-controls">
            <div className="metric-selector">
              <button className="metric-btn active">Impressions</button>
              <button className="metric-btn">Clics</button>
              <button className="metric-btn">Conversions</button>
            </div>
          </div>
        </div>
        
        <div className="chart-container">
          <div className="chart-y-axis">
            {[100, 75, 50, 25, 0].map(percent => (
              <div key={percent} className="y-axis-label">
                {Math.round((maxValue * percent) / 100).toLocaleString()}
              </div>
            ))}
          </div>
          
          <div className="chart-area">
            <div className="chart-grid">
              {[0, 25, 50, 75, 100].map(line => (
                <div key={line} className="grid-line" style={{ bottom: `${line}%` }}></div>
              ))}
            </div>
            
            <div className="chart-bars">
              {dynamicData.performance.labels.map((label, index) => {
                const impressionHeight = (dynamicData.performance.impressions[index] / maxValue) * 100;
                const clickHeight = (dynamicData.performance.clicks[index] / Math.max(...dynamicData.performance.clicks)) * 100;
                const conversionHeight = (dynamicData.performance.conversions[index] / Math.max(...dynamicData.performance.conversions)) * 100;
                
                return (
                  <div 
                    key={label} 
                    className="bar-group"
                    onMouseEnter={() => setHoveredIndex(index)}
                    onMouseLeave={() => setHoveredIndex(null)}
                  >
                    <div className="bars-container">
                      <div 
                        className="bar impressions"
                        style={{ 
                          height: `${(impressionHeight * animationProgress) / 100}%`,
                          backgroundColor: '#4f46e5'
                        }}
                      ></div>
                      <div 
                        className="bar clicks"
                        style={{ 
                          height: `${(clickHeight * animationProgress) / 100}%`,
                          backgroundColor: '#10b981'
                        }}
                      ></div>
                      <div 
                        className="bar conversions"
                        style={{ 
                          height: `${(conversionHeight * animationProgress) / 100}%`,
                          backgroundColor: '#f59e0b'
                        }}
                      ></div>
                    </div>
                    
                    <div className="bar-label">{label}</div>
                    
                    {hoveredIndex === index && (
                      <div className="chart-tooltip">
                        <div className="tooltip-content">
                          <div className="tooltip-item">
                            <span className="tooltip-color impressions"></span>
                            <span>Impressions: {dynamicData.performance.impressions[index].toLocaleString()}</span>
                          </div>
                          <div className="tooltip-item">
                            <span className="tooltip-color clicks"></span>
                            <span>Clics: {dynamicData.performance.clicks[index].toLocaleString()}</span>
                          </div>
                          <div className="tooltip-item">
                            <span className="tooltip-color conversions"></span>
                            <span>Conversions: {dynamicData.performance.conversions[index].toLocaleString()}</span>
                          </div>
                          <div className="tooltip-item">
                            <span className="tooltip-color spend"></span>
                            <span>Dépense: {formatAmount(dynamicData.performance.spend[index])}</span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        
        <div className="chart-legend">
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#4f46e5' }}></div>
            <span>Impressions</span>
          </div>
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#10b981' }}></div>
            <span>Clics</span>
          </div>
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: '#f59e0b' }}></div>
            <span>Conversions</span>
          </div>
        </div>
      </div>
    );
  };

  const AudienceChart = () => (
    <div className="interactive-chart audience-chart">
      <div className="chart-header">
        <h3>Analyse d'audience</h3>
      </div>
      
      <div className="audience-grid">
        <div className="audience-section">
          <h4>Répartition par âge</h4>
          <div className="pie-chart-container">
            <div className="pie-chart">
              {dynamicData.audience.demographics.age.map((item, index) => {
                const angle = (item.percentage / 100) * 360;
                const rotation = dynamicData.audience.demographics.age
                  .slice(0, index)
                  .reduce((sum, prev) => sum + (prev.percentage / 100) * 360, 0);
                
                return (
                  <div
                    key={item.range}
                    className="pie-slice"
                    style={{
                      '--angle': `${angle}deg`,
                      '--rotation': `${rotation}deg`,
                      '--color': `hsl(${index * 60}, 70%, 60%)`
                    } as React.CSSProperties}
                  ></div>
                );
              })}
            </div>
            <div className="pie-legend">
              {dynamicData.audience.demographics.age.map((item, index) => (
                <div key={item.range} className="pie-legend-item">
                  <div 
                    className="legend-color" 
                    style={{ backgroundColor: `hsl(${index * 60}, 70%, 60%)` }}
                  ></div>
                  <span>{item.range}: {item.percentage}%</span>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="audience-section">
          <h4>Centres d'intérêt</h4>
          <div className="interests-bars">
            {dynamicData.audience.demographics.interests.map((item, index) => (
              <div key={item.category} className="interest-bar">
                <div className="interest-label">{item.category}</div>
                <div className="interest-progress">
                  <div 
                    className="interest-fill"
                    style={{ 
                      width: `${(item.percentage * animationProgress) / 100}%`,
                      backgroundColor: `hsl(${index * 45}, 65%, 55%)`
                    }}
                  ></div>
                </div>
                <div className="interest-value">{item.percentage}%</div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="audience-section">
          <h4>Répartition géographique</h4>
          <div className="geography-chart">
            {dynamicData.audience.geography.map((item, index) => (
              <div key={item.city} className="geo-item">
                <div className="geo-label">{item.city}</div>
                <div className="geo-bar">
                  <div 
                    className="geo-fill"
                    style={{ 
                      width: `${(item.percentage * animationProgress) / 100}%`,
                      backgroundColor: '#10b981'
                    }}
                  ></div>
                </div>
                <div className="geo-value">{item.percentage}%</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const BudgetChart = () => (
    <div className="interactive-chart budget-chart">
      <div className="chart-header">
        <h3>Gestion du budget</h3>
      </div>
      
      <div className="budget-grid">
        <div className="budget-section">
          <h4>Allocation par campagne</h4>
          <div className="budget-bars">
            {dynamicData.budget.allocation.map((item, index) => {
              const usagePercent = (item.spent / item.budget) * 100;
              return (
                <div key={item.campaign} className="budget-bar">
                  <div className="budget-info">
                    <span className="campaign-name">{item.campaign}</span>
                    <span className="budget-amounts">
                      {formatAmount(item.spent)} / {formatAmount(item.budget)}
                    </span>
                  </div>
                  <div className="budget-progress">
                    <div 
                      className="budget-used"
                      style={{ 
                        width: `${(usagePercent * animationProgress) / 100}%`,
                        backgroundColor: usagePercent > 80 ? '#ef4444' : usagePercent > 60 ? '#f59e0b' : '#10b981'
                      }}
                    ></div>
                  </div>
                  <div className="budget-percentage">{usagePercent.toFixed(1)}%</div>
                </div>
              );
            })}
          </div>
        </div>
        
        <div className="budget-section">
          <h4>Évolution mensuelle</h4>
          <div className="timeline-chart">
            {dynamicData.budget.timeline.map((item, index) => {
              const maxBudget = Math.max(...dynamicData.budget.timeline.map(t => t.budget));
              const budgetHeight = (item.budget / maxBudget) * 100;
              const spentHeight = (item.spent / maxBudget) * 100;
              
              return (
                <div key={item.month} className="timeline-item">
                  <div className="timeline-bars">
                    <div 
                      className="timeline-bar budget"
                      style={{ height: `${(budgetHeight * animationProgress) / 100}%` }}
                    ></div>
                    <div 
                      className="timeline-bar spent"
                      style={{ height: `${(spentHeight * animationProgress) / 100}%` }}
                    ></div>
                  </div>
                  <div className="timeline-label">{item.month}</div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="interactive-charts-container">
      <div className="charts-navigation">
        <button 
          className={`nav-btn ${activeChart === 'performance' ? 'active' : ''}`}
          onClick={() => setActiveChart('performance')}
        >
          <BarChart3 size={16} />
          Performance
        </button>
        <button 
          className={`nav-btn ${activeChart === 'audience' ? 'active' : ''}`}
          onClick={() => setActiveChart('audience')}
        >
          <PieChart size={16} />
          Audience
        </button>
        <button 
          className={`nav-btn ${activeChart === 'budget' ? 'active' : ''}`}
          onClick={() => setActiveChart('budget')}
        >
          <Activity size={16} />
          Budget
        </button>
      </div>
      
      <div className="charts-content">
        {activeChart === 'performance' && <PerformanceChart />}
        {activeChart === 'audience' && <AudienceChart />}
        {activeChart === 'budget' && <BudgetChart />}
      </div>
    </div>
  );
};

export default InteractiveCharts;
