import React, { useState } from 'react';
import { useBusinessSubscriptionStatus } from './hooks/useBusinessSubscriptionStatus';
import SubscriptionActivationNotification from './components/business/SubscriptionActivationNotification';
import ActiveSubscriptionCard from './components/business/ActiveSubscriptionCard';
import Button from './components/ui/Button';

/**
 * Composant de test pour vérifier le système d'activation d'abonnement
 */
const TestSubscriptionActivation: React.FC = () => {
  const { status, loading, error, justActivated, refetch } = useBusinessSubscriptionStatus();
  const [showTestNotification, setShowTestNotification] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleTestNotification = () => {
    setShowTestNotification(true);
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refetch();
    } finally {
      setIsRefreshing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement du statut d'abonnement...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">❌</div>
          <p className="text-red-600">{error}</p>
          <Button onClick={handleRefresh} className="mt-4">
            Réessayer
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Test - Activation d'Abonnement
        </h1>
        <p className="text-gray-600">
          Cette page permet de tester le système d'activation d'abonnement et les notifications.
        </p>
      </div>

      {/* Indicateurs de statut */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div className="bg-white p-4 rounded-lg shadow border">
          <h3 className="font-semibold text-gray-900 mb-2">Statut de chargement</h3>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            loading ? 'bg-yellow-100 text-yellow-700' : 'bg-green-100 text-green-700'
          }`}>
            {loading ? 'En cours...' : 'Chargé'}
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <h3 className="font-semibold text-gray-900 mb-2">Activation détectée</h3>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            justActivated ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
          }`}>
            {justActivated ? 'Oui' : 'Non'}
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <h3 className="font-semibold text-gray-900 mb-2">Abonnement actif</h3>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            status?.isActive ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
          }`}>
            {status?.isActive ? 'Actif' : 'Inactif'}
          </div>
        </div>
      </div>

      {/* Boutons de test */}
      <div className="bg-white p-6 rounded-lg shadow border mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Actions de test</h2>
        <div className="flex flex-wrap gap-4">
          <Button onClick={handleTestNotification}>
            Tester la notification d'activation
          </Button>
          <Button onClick={handleRefresh} disabled={isRefreshing} variant="outline">
            {isRefreshing ? 'Actualisation...' : 'Actualiser le statut'}
          </Button>
        </div>
      </div>

      {/* Affichage du statut d'abonnement */}
      {status && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Statut d'abonnement actuel</h2>
          <ActiveSubscriptionCard 
            status={status} 
            onRefresh={handleRefresh}
            isRefreshing={isRefreshing}
          />
        </div>
      )}

      {/* Détails techniques */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Détails techniques</h2>
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-gray-700 mb-2">Données du statut :</h3>
            <pre className="bg-white p-4 rounded border text-sm overflow-auto">
              {JSON.stringify(status, null, 2)}
            </pre>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-700 mb-2">États :</h3>
            <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
              <li>Loading: {loading ? 'true' : 'false'}</li>
              <li>Error: {error || 'null'}</li>
              <li>Just Activated: {justActivated ? 'true' : 'false'}</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Notifications de test */}
      <SubscriptionActivationNotification
        isVisible={showTestNotification}
        planName={status?.planName || 'Plan Test'}
        daysRemaining={status?.daysRemaining || 30}
        onClose={() => setShowTestNotification(false)}
      />

      {/* Notification automatique d'activation */}
      {status && justActivated && (
        <SubscriptionActivationNotification
          isVisible={true}
          planName={status.planName}
          daysRemaining={status.daysRemaining}
          onClose={() => {}}
        />
      )}
    </div>
  );
};

export default TestSubscriptionActivation;
