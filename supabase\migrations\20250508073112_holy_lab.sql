/*
  # Add indexes and constraints to auth tables

  1. New Indexes
    - `profiles_email_idx` on `profiles(email)`
    - `profiles_username_idx` on `profiles(username)`
    - `business_profiles_name_idx` on `business_profiles(business_name)`
    - `follows_follower_idx` on `follows(follower_id)`
    - `follows_following_idx` on `follows(following_id)`

  2. Additional Constraints
    - Email format validation
    - Username length and format
    - Business name length
*/

-- Add email format check
ALTER TABLE profiles
ADD CONSTRAINT email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Add username format and length constraints
ALTER TABLE profiles
ADD CONSTRAINT username_length CHECK (char_length(username) BETWEEN 3 AND 30),
ADD CONSTRAINT username_format CHECK (username ~* '^[a-zA-Z0-9_-]+$');

-- Add business name length constraint
ALTER TABLE business_profiles
ADD CONSTRAINT business_name_length CHECK (char_length(business_name) BETWEEN 2 AND 100);

-- <PERSON>reate indexes for better query performance
CREATE INDEX IF NOT EXISTS profiles_email_idx ON profiles(email);
CREATE INDEX IF NOT EXISTS profiles_username_idx ON profiles(username);
CREATE INDEX IF NOT EXISTS business_profiles_name_idx ON business_profiles(business_name);
CREATE INDEX IF NOT EXISTS follows_follower_idx ON follows(follower_id);
CREATE INDEX IF NOT EXISTS follows_following_idx ON follows(following_id);

-- Add updated_at trigger to business_profiles
CREATE TRIGGER update_business_profiles_updated_at
  BEFORE UPDATE ON business_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();