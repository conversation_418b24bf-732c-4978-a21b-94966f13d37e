-- Script de diagnostic pour vérifier l'état des tables d'abonnement
-- Exécuter ce script dans l'éditeur SQL de Supabase pour diagnostiquer les problèmes

-- 1. Vérifier quelles tables existent
SELECT 
  table_name,
  table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('subscription_plans', 'business_subscriptions', 'subscription_codes', 'subscription_notifications')
ORDER BY table_name;

-- 2. Vérifier la structure de la table subscription_plans
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'subscription_plans' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. Vérifier la structure de la table business_subscriptions
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'business_subscriptions' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 4. Vérifier la structure de la table subscription_codes
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'subscription_codes' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 5. Vérifier la structure de la table subscription_notifications
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'subscription_notifications' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 6. Vérifier si la procédure stockée existe
SELECT 
  routine_name,
  routine_type,
  data_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name = 'create_subscription_from_code';

-- 7. Compter les données existantes
SELECT 'subscription_plans' as table_name, COUNT(*) as row_count FROM public.subscription_plans
UNION ALL
SELECT 'business_subscriptions' as table_name, COUNT(*) as row_count FROM public.business_subscriptions
UNION ALL
SELECT 'subscription_codes' as table_name, COUNT(*) as row_count FROM public.subscription_codes
UNION ALL
SELECT 'subscription_notifications' as table_name, COUNT(*) as row_count FROM public.subscription_notifications;

-- 8. Afficher les plans existants
SELECT * FROM public.subscription_plans ORDER BY id;

-- 9. Vérifier les politiques RLS
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('subscription_plans', 'business_subscriptions', 'subscription_codes', 'subscription_notifications')
ORDER BY tablename, policyname;
