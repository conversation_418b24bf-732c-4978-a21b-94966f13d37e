-- Script minimal pour ajouter UNIQUEMENT les colonnes likes et shares
-- Version ultra-simplifiée qui évite toute modification de vue

-- =====================================================
-- 1. AJOUTER LES COLONNES LIKES ET SHARES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Ajout des colonnes likes et shares à la table posts...';
    
    -- Vérifier si la colonne likes existe déjà
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' 
        AND column_name = 'likes'
        AND table_schema = 'public'
    ) THEN
        -- Ajouter la colonne likes
        ALTER TABLE posts ADD COLUMN likes UUID[] DEFAULT ARRAY[]::UUID[];
        RAISE NOTICE '✅ Colonne "likes" ajoutée';
    ELSE
        RAISE NOTICE '⚠️ Colonne "likes" existe déjà';
    END IF;
    
    -- Vérifier si la colonne shares existe déjà
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' 
        AND column_name = 'shares'
        AND table_schema = 'public'
    ) THEN
        -- Ajouter la colonne shares
        ALTER TABLE posts ADD COLUMN shares UUID[] DEFAULT ARRAY[]::UUID[];
        RAISE NOTICE '✅ Colonne "shares" ajoutée';
    ELSE
        RAISE NOTICE '⚠️ Colonne "shares" existe déjà';
    END IF;
END $$;

-- =====================================================
-- 2. AJOUTER LES COMMENTAIRES EXPLICATIFS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📝 Ajout des commentaires explicatifs...';
    
    -- Commentaires sur les colonnes
    COMMENT ON COLUMN posts.likes IS 'Liste des UUIDs des utilisateurs qui ont aimé ce post';
    COMMENT ON COLUMN posts.shares IS 'Liste des UUIDs des utilisateurs qui ont partagé ce post';
    
    RAISE NOTICE '✅ Commentaires ajoutés';
END $$;

-- =====================================================
-- 3. CRÉER LES INDEX POUR LES PERFORMANCES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🚀 Création des index pour optimiser les performances...';
    
    -- Index GIN pour les likes (recherches rapides dans les tableaux)
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'posts' 
        AND indexname = 'posts_likes_idx'
    ) THEN
        CREATE INDEX posts_likes_idx ON posts USING GIN (likes);
        RAISE NOTICE '✅ Index "posts_likes_idx" créé';
    ELSE
        RAISE NOTICE '⚠️ Index "posts_likes_idx" existe déjà';
    END IF;
    
    -- Index GIN pour les shares
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'posts' 
        AND indexname = 'posts_shares_idx'
    ) THEN
        CREATE INDEX posts_shares_idx ON posts USING GIN (shares);
        RAISE NOTICE '✅ Index "posts_shares_idx" créé';
    ELSE
        RAISE NOTICE '⚠️ Index "posts_shares_idx" existe déjà';
    END IF;
    
    -- Index GIN pour les recommendations (au cas où il n'existerait pas)
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'posts' 
        AND indexname = 'posts_recommendations_idx'
    ) THEN
        CREATE INDEX posts_recommendations_idx ON posts USING GIN (recommendations);
        RAISE NOTICE '✅ Index "posts_recommendations_idx" créé';
    ELSE
        RAISE NOTICE '⚠️ Index "posts_recommendations_idx" existe déjà';
    END IF;
END $$;

-- =====================================================
-- 4. INITIALISER LES DONNÉES EXISTANTES
-- =====================================================

DO $$
DECLARE
    posts_updated INTEGER;
BEGIN
    RAISE NOTICE '🔄 Initialisation des données existantes...';
    
    -- Initialiser les colonnes pour tous les posts existants
    UPDATE posts 
    SET 
        likes = COALESCE(likes, ARRAY[]::UUID[]),
        shares = COALESCE(shares, ARRAY[]::UUID[])
    WHERE likes IS NULL OR shares IS NULL;
    
    GET DIAGNOSTICS posts_updated = ROW_COUNT;
    
    RAISE NOTICE '✅ % posts mis à jour avec les nouvelles colonnes', posts_updated;
END $$;

-- =====================================================
-- 5. VÉRIFICATION FINALE
-- =====================================================

DO $$
DECLARE
    total_posts INTEGER;
    posts_with_likes INTEGER;
    posts_with_shares INTEGER;
    sample_post RECORD;
    likes_column_exists BOOLEAN;
    shares_column_exists BOOLEAN;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 VÉRIFICATION FINALE';
    RAISE NOTICE '====================';
    
    -- Vérifier l'existence des colonnes
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'likes'
    ) INTO likes_column_exists;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'shares'
    ) INTO shares_column_exists;
    
    RAISE NOTICE 'Colonne likes existe: %', CASE WHEN likes_column_exists THEN 'OUI' ELSE 'NON' END;
    RAISE NOTICE 'Colonne shares existe: %', CASE WHEN shares_column_exists THEN 'OUI' ELSE 'NON' END;
    
    -- Compter les posts
    SELECT COUNT(*) INTO total_posts FROM posts;
    RAISE NOTICE 'Total posts: %', total_posts;
    
    IF likes_column_exists THEN
        -- Compter les posts avec likes initialisés
        SELECT COUNT(*) INTO posts_with_likes 
        FROM posts 
        WHERE likes IS NOT NULL;
        RAISE NOTICE 'Posts avec likes: %', posts_with_likes;
        
        -- Prendre un échantillon
        SELECT id, likes, shares 
        INTO sample_post
        FROM posts 
        LIMIT 1;
        
        IF sample_post.id IS NOT NULL THEN
            RAISE NOTICE '';
            RAISE NOTICE '📋 ÉCHANTILLON:';
            RAISE NOTICE 'Post ID: %', sample_post.id;
            RAISE NOTICE 'Likes: %', sample_post.likes;
            RAISE NOTICE 'Shares: %', sample_post.shares;
        END IF;
    END IF;
    
    -- Résultat final
    IF likes_column_exists AND shares_column_exists THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 MIGRATION RÉUSSIE !';
        RAISE NOTICE '=====================';
        RAISE NOTICE '✅ Colonnes likes et shares ajoutées';
        RAISE NOTICE '✅ Index créés pour les performances';
        RAISE NOTICE '✅ Données existantes initialisées';
        RAISE NOTICE '';
        RAISE NOTICE '🚀 Le système de likes est maintenant opérationnel !';
        RAISE NOTICE '';
        RAISE NOTICE '📱 PROCHAINES ÉTAPES:';
        RAISE NOTICE '1. Redémarrez votre application React';
        RAISE NOTICE '2. Connectez-vous avec un compte utilisateur';
        RAISE NOTICE '3. Testez le bouton "J''aime" sur un post';
        RAISE NOTICE '4. Vérifiez que le compteur se met à jour';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '❌ ÉCHEC DE LA MIGRATION';
        RAISE NOTICE '========================';
        RAISE NOTICE 'Les colonnes n''ont pas pu être ajoutées correctement.';
    END IF;
END $$;
