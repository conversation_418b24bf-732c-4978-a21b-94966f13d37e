# Système de Statut d'Entreprise - Customeroom

## 📋 Vue d'ensemble

Le système de statut d'entreprise de Customeroom permet de classer et récompenser les entreprises selon leurs performances, leur engagement et leur qualité de service. Ce système encourage l'excellence et offre des avantages progressifs.

## 🏆 Niveaux de Statut

### 1. **Nouveau** (NEW)
- **Description** : Entreprise nouvellement inscrite
- **Critères** : Aucun critère spécifique
- **Avantages** :
  - Accès aux fonctionnalités de base
  - Création de profil entreprise gratuite
  - Possibilité d'ajouter des produits

### 2. **Actif** (ACTIVE)
- **Description** : Entreprise active avec profil complet
- **Critères** :
  - Profil d'entreprise complet
  - Au moins 1 produit
  - Compte actif depuis 7 jours
- **Avantages** :
  - Badge entreprise active
  - Visibilité dans les recherches
  - Accès au marketplace
  - Possibilité de recevoir des avis
  - Statistiques de base

### 3. **Vérifié** (VERIFIED)
- **Description** : Entreprise vérifiée avec documents officiels
- **Critères** :
  - Profil complet
  - Au moins 5 produits
  - Au moins 10 ventes
  - Au moins 5 avis
  - Note moyenne ≥ 3.5/5
  - Compte actif depuis 30 jours
  - Vérification officielle
  - Document d'entreprise fourni
- **Avantages** :
  - Badge vérifié bleu
  - Confiance client renforcée
  - Priorité dans les résultats de recherche
  - Accès aux outils marketing avancés
  - Support client prioritaire
  - Statistiques détaillées

### 4. **Premium** (PREMIUM)
- **Description** : Entreprise premium avec excellent service
- **Critères** :
  - Tous les critères "Vérifié"
  - Au moins 20 produits
  - Au moins 100 ventes
  - Au moins 50 avis
  - Note moyenne ≥ 4.0/5
  - Compte actif depuis 90 jours
  - Chiffre d'affaires ≥ 500 000 F CFA
- **Avantages** :
  - Badge premium doré
  - Mise en avant dans les résultats
  - Outils d'analyse avancés
  - Campagnes publicitaires privilégiées
  - Support dédié
  - Accès aux événements exclusifs
  - Commission réduite sur les ventes

### 5. **Mis en Avant** (FEATURED)
- **Description** : Entreprise d'excellence mise en avant
- **Critères** :
  - Tous les critères "Premium"
  - Au moins 50 produits
  - Au moins 500 ventes
  - Au moins 200 avis
  - Note moyenne ≥ 4.5/5
  - Compte actif depuis 180 jours
  - Chiffre d'affaires ≥ 2 000 000 F CFA
  - Exigences spéciales (excellence service, innovation, contribution communauté)
- **Avantages** :
  - Badge featured violet
  - Mise en avant sur la page d'accueil
  - Partenariats exclusifs
  - Accès aux données de marché
  - Programme d'ambassadeur
  - Événements VIP
  - Commission minimale

### 6. **Partenaire Officiel** (PARTNER)
- **Description** : Partenaire stratégique de Customeroom
- **Critères** :
  - Tous les critères "Featured"
  - Au moins 100 produits
  - Au moins 2000 ventes
  - Au moins 1000 avis
  - Note moyenne ≥ 4.8/5
  - Compte actif depuis 365 jours
  - Chiffre d'affaires ≥ 10 000 000 F CFA
  - Partenariat stratégique signé
- **Avantages** :
  - Badge partenaire rouge couronne
  - Statut de partenaire officiel
  - Co-marketing avec Customeroom
  - Accès aux nouvelles fonctionnalités en avant-première
  - Participation aux décisions stratégiques
  - Revenus de parrainage
  - Aucune commission sur les ventes

## 🗄️ Structure de la Base de Données

### Tables Principales

#### `business_status_criteria`
Stocke les critères pour chaque niveau de statut.

#### `business_status_history`
Historique des changements de statut des entreprises.

#### `business_verification_documents`
Documents de vérification uploadés par les entreprises.

#### `business_profiles` (mise à jour)
Table existante enrichie avec de nouvelles colonnes :
- `total_revenue` : Chiffre d'affaires total
- `verification_status` : Statut de vérification
- `has_business_document` : Document d'entreprise fourni
- `status_updated_at` : Date de dernière mise à jour du statut
- `auto_status_check_enabled` : Vérification automatique activée

## 🔧 Installation

### 1. Exécuter la Migration
```sql
-- Dans l'éditeur SQL de Supabase
\i supabase/migrations/20250131000002_create_business_status_system.sql
```

### 2. Configurer le Système
```sql
-- Dans l'éditeur SQL de Supabase
\i scripts/setup-business-status-system.sql
```

## 🚀 Utilisation

### Frontend (TypeScript)

```typescript
import { BusinessStatusService } from '../services/businessStatusService';

// Récupérer les métriques d'une entreprise
const metrics = await BusinessStatusService.getBusinessMetricsFromDB(businessId);

// Vérifier l'éligibilité pour un statut
const eligibility = await BusinessStatusService.checkEligibilityFromDB(businessId, 'verified');

// Mettre à jour le statut si éligible
const result = await BusinessStatusService.updateBusinessStatusIfEligible(businessId);

// Récupérer l'historique des statuts
const history = await BusinessStatusService.getStatusHistory(businessId);
```

### Backend (SQL)

```sql
-- Calculer les métriques d'une entreprise
SELECT calculate_business_metrics('uuid-entreprise');

-- Vérifier l'éligibilité
SELECT check_business_status_eligibility('uuid-entreprise', 'verified');

-- Mettre à jour le statut
SELECT update_business_status_if_eligible('uuid-entreprise');
```

## ⚙️ Configuration

### Mise à Jour Automatique
Le système peut automatiquement vérifier et mettre à jour les statuts :

```sql
-- Activer la mise à jour automatique pour une entreprise
UPDATE business_profiles 
SET auto_status_check_enabled = true 
WHERE id = 'uuid-entreprise';
```

### Triggers
Des triggers automatiques vérifient le statut après :
- Mise à jour du nombre de ventes
- Changement du chiffre d'affaires
- Modification du statut de vérification
- Ajout de documents d'entreprise

## 📊 Monitoring

### Requêtes Utiles

```sql
-- Voir la distribution des statuts
SELECT business_status, COUNT(*) 
FROM business_profiles 
GROUP BY business_status;

-- Entreprises éligibles pour une promotion
SELECT bp.business_name, bp.business_status
FROM business_profiles bp
WHERE check_business_status_eligibility(bp.id, 'verified')->>'eligible' = 'true';

-- Historique récent des changements
SELECT bsh.*, bp.business_name 
FROM business_status_history bsh 
JOIN business_profiles bp ON bsh.business_id = bp.id 
ORDER BY bsh.created_at DESC 
LIMIT 10;
```

## 🔒 Sécurité

- **RLS activé** sur toutes les tables
- **Politiques appropriées** pour chaque type d'utilisateur
- **Validation des données** avec contraintes CHECK
- **Audit trail** complet dans l'historique

## 🎯 Bonnes Pratiques

1. **Vérification régulière** : Exécutez périodiquement la mise à jour des statuts
2. **Monitoring** : Surveillez les changements de statut suspects
3. **Documentation** : Documentez les changements manuels de statut
4. **Backup** : Sauvegardez régulièrement l'historique des statuts

## 🐛 Dépannage

### Problèmes Courants

1. **Statut non mis à jour** : Vérifiez que `auto_status_check_enabled = true`
2. **Métriques incorrectes** : Vérifiez l'intégrité des données dans les tables liées
3. **Erreurs de permissions** : Vérifiez les politiques RLS

### Logs

```sql
-- Voir les erreurs récentes
SELECT * FROM pg_stat_activity WHERE state = 'active';
```

## 📞 Support

Pour toute question ou problème :
- Consultez les logs de la base de données
- Vérifiez les contraintes et politiques RLS
- Contactez l'équipe de développement
