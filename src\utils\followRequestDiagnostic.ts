import { supabase } from '../lib/supabase';

export class FollowRequestDiagnostic {
  
  /**
   * Vérifier si les tables existent
   */
  static async checkTablesExist() {
    console.log('🔍 Vérification de l\'existence des tables...');
    
    try {
      // Vérifier follow_requests
      const { data: followRequests, error: frError } = await supabase
        .from('follow_requests')
        .select('count')
        .limit(1);
      
      console.log('✅ Table follow_requests:', frError ? '❌ N\'existe pas' : '✅ Existe');
      
      // Vérifier privacy_settings
      const { data: privacySettings, error: psError } = await supabase
        .from('privacy_settings')
        .select('count')
        .limit(1);
      
      console.log('✅ Table privacy_settings:', psError ? '❌ N\'existe pas' : '✅ Existe');
      
      // Vérifier user_blocks
      const { data: userBlocks, error: ubError } = await supabase
        .from('user_blocks')
        .select('count')
        .limit(1);
      
      console.log('✅ Table user_blocks:', ubError ? '❌ N\'existe pas' : '✅ Existe');
      
      return {
        followRequests: !frError,
        privacySettings: !psError,
        userBlocks: !ubError
      };
      
    } catch (error) {
      console.error('❌ Erreur lors de la vérification des tables:', error);
      return {
        followRequests: false,
        privacySettings: false,
        userBlocks: false
      };
    }
  }

  /**
   * Vérifier les paramètres de confidentialité des utilisateurs
   */
  static async checkPrivacySettings(userId: string) {
    console.log(`🔍 Vérification des paramètres de confidentialité pour l'utilisateur: ${userId}`);
    
    try {
      const { data, error } = await supabase
        .from('privacy_settings')
        .select('*')
        .eq('user_id', userId)
        .single();
      
      if (error) {
        console.log('❌ Paramètres de confidentialité non trouvés:', error.message);
        return null;
      }
      
      console.log('✅ Paramètres trouvés:', {
        requireFollowApproval: data.require_follow_approval,
        allowFollowRequests: data.allow_follow_requests,
        notifyFollowRequests: data.notify_follow_requests
      });
      
      return data;
      
    } catch (error) {
      console.error('❌ Erreur lors de la vérification des paramètres:', error);
      return null;
    }
  }

  /**
   * Tester la création d'une demande de suivi
   */
  static async testFollowRequest(requesterId: string, targetId: string) {
    console.log(`🧪 Test de création de demande de suivi: ${requesterId} -> ${targetId}`);
    
    try {
      // Vérifier les paramètres de la cible
      const targetSettings = await this.checkPrivacySettings(targetId);
      
      if (!targetSettings) {
        console.log('⚠️ Création de paramètres par défaut pour la cible...');
        const { data: newSettings, error: createError } = await supabase
          .from('privacy_settings')
          .insert([{ user_id: targetId }])
          .select()
          .single();
        
        if (createError) {
          console.error('❌ Erreur lors de la création des paramètres:', createError);
          return false;
        }
        
        console.log('✅ Paramètres créés:', newSettings);
      }
      
      // Tenter de créer une demande
      const { data: request, error: requestError } = await supabase
        .from('follow_requests')
        .insert([{
          requester_id: requesterId,
          target_id: targetId,
          message: 'Test de demande de suivi',
          status: 'pending',
          request_type: 'follow'
        }])
        .select()
        .single();
      
      if (requestError) {
        console.error('❌ Erreur lors de la création de la demande:', requestError);
        return false;
      }
      
      console.log('✅ Demande créée avec succès:', request);
      return true;
      
    } catch (error) {
      console.error('❌ Erreur lors du test:', error);
      return false;
    }
  }

  /**
   * Vérifier les notifications
   */
  static async checkNotifications(userId: string) {
    console.log(`🔍 Vérification des notifications pour l'utilisateur: ${userId}`);
    
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (error) {
        console.error('❌ Erreur lors de la récupération des notifications:', error);
        return [];
      }
      
      console.log(`✅ ${data.length} notifications trouvées:`);
      data.forEach((notif, index) => {
        console.log(`  ${index + 1}. ${notif.type}: ${notif.message} (${notif.created_at})`);
      });
      
      return data;
      
    } catch (error) {
      console.error('❌ Erreur lors de la vérification des notifications:', error);
      return [];
    }
  }

  /**
   * Vérifier les utilisateurs existants
   */
  static async checkUsers() {
    console.log('🔍 Vérification des utilisateurs...');
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, username, role')
        .in('username', ['Kouassi Ange', 'Soro Chantal', 'Dexima']);
      
      if (error) {
        console.error('❌ Erreur lors de la récupération des utilisateurs:', error);
        return [];
      }
      
      console.log('✅ Utilisateurs trouvés:');
      data.forEach(user => {
        console.log(`  - ${user.username} (${user.id}) - ${user.role}`);
      });
      
      return data;
      
    } catch (error) {
      console.error('❌ Erreur lors de la vérification des utilisateurs:', error);
      return [];
    }
  }

  /**
   * Diagnostic complet
   */
  static async runFullDiagnostic() {
    console.log('🚀 Démarrage du diagnostic complet du système de demandes de suivi...');
    console.log('='.repeat(80));
    
    // 1. Vérifier les tables
    const tablesExist = await this.checkTablesExist();
    console.log('');
    
    // 2. Vérifier les utilisateurs
    const users = await this.checkUsers();
    console.log('');
    
    if (users.length > 0) {
      // 3. Vérifier les paramètres de confidentialité
      for (const user of users) {
        await this.checkPrivacySettings(user.id);
        console.log('');
      }
      
      // 4. Vérifier les notifications récentes
      for (const user of users) {
        await this.checkNotifications(user.id);
        console.log('');
      }
    }
    
    console.log('='.repeat(80));
    console.log('✅ Diagnostic terminé');
    
    return {
      tablesExist,
      users,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Nettoyer les données de test
   */
  static async cleanupTestData() {
    console.log('🧹 Nettoyage des données de test...');
    
    try {
      // Supprimer les demandes de test
      const { error: deleteError } = await supabase
        .from('follow_requests')
        .delete()
        .eq('message', 'Test de demande de suivi');
      
      if (deleteError) {
        console.error('❌ Erreur lors du nettoyage:', deleteError);
      } else {
        console.log('✅ Données de test supprimées');
      }
      
    } catch (error) {
      console.error('❌ Erreur lors du nettoyage:', error);
    }
  }
}

// Fonction utilitaire pour exécuter le diagnostic depuis la console
(window as any).followDiagnostic = FollowRequestDiagnostic;

// Fonction de diagnostic rapide pour la console
(window as any).quickFollowDiagnostic = async () => {
  console.log('🚀 Diagnostic Rapide du Système de Suivi');
  console.log('='.repeat(50));

  try {
    // Test des tables
    const tablesExist = await FollowRequestDiagnostic.checkTablesExist();
    console.log('📊 Tables:', tablesExist);

    // Test des utilisateurs
    const users = await FollowRequestDiagnostic.checkUsers();
    console.log('👥 Utilisateurs:', users);

    if (users.length > 0) {
      // Test des paramètres de confidentialité
      for (const user of users.slice(0, 2)) { // Limiter à 2 utilisateurs
        await FollowRequestDiagnostic.checkPrivacySettings(user.id);
      }
    }

    console.log('✅ Diagnostic terminé');
    console.log('💡 Pour un diagnostic complet, utilisez: followDiagnostic.runFullDiagnostic()');

  } catch (error) {
    console.error('❌ Erreur lors du diagnostic:', error);
  }
};
