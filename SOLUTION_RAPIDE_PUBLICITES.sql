-- ✅ SOLUTION RAPIDE : Créer des campagnes avec des UUIDs valides
-- Exécutez ce script dans Supabase pour corriger le problème immédiatement

-- =====================================================
-- 1. SUPPRIMER LES ANCIENNES DONNÉES DE TEST (SI ELLES EXISTENT)
-- =====================================================
DELETE FROM ad_engagement_metrics WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid
);

DELETE FROM ad_likes WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid
);

DELETE FROM ad_comments WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid
);

DELETE FROM ad_shares WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid
);

DELETE FROM ad_creatives WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid
);

DELETE FROM ad_campaigns WHERE id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid
);

-- =====================================================
-- 2. CRÉER LES CAMPAGNES AVEC DES UUIDs VALIDES
-- =====================================================

-- Récupérer un business_id valide ou créer un business de test
DO $$
DECLARE
  business_user_id uuid;
BEGIN
  -- Essayer de récupérer un utilisateur business existant
  SELECT id INTO business_user_id FROM profiles WHERE role = 'business' LIMIT 1;
  
  -- Si aucun business n'existe, créer un utilisateur de test
  IF business_user_id IS NULL THEN
    -- Créer un utilisateur business de test
    INSERT INTO profiles (id, email, username, role, created_at)
    VALUES (
      '10000000-0000-0000-0000-000000000001'::uuid,
      '<EMAIL>',
      'Olgane Cosmetics',
      'business',
      now()
    ) ON CONFLICT (id) DO NOTHING;
    
    business_user_id := '10000000-0000-0000-0000-000000000001'::uuid;
  END IF;
  
  -- Créer les campagnes publicitaires
  INSERT INTO ad_campaigns (
    id,
    business_id,
    title,
    description,
    image_url,
    target_url,
    bid_amount,
    daily_budget,
    total_budget,
    placements,
    status,
    start_date,
    impressions,
    clicks
  ) VALUES 
  (
    '00000000-0000-0000-0000-000000000001'::uuid,
    business_user_id,
    'Promotion Été - Huile de Beauté',
    'Profitez de notre offre spéciale été avec 20% de réduction sur notre huile de beauté Olgane.',
    'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
    'https://example.com/promo-ete',
    250.00,
    5000.00,
    50000.00,
    '["newsfeed", "sidebar"]'::jsonb,
    'active',
    now(),
    100,
    5
  ),
  (
    '00000000-0000-0000-0000-000000000002'::uuid,
    business_user_id,
    'Lancement Crème Hydratante',
    'Découvrez notre nouvelle crème hydratante visage pour une peau éclatante et revitalisée.',
    'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
    'https://example.com/creme-hydratante',
    200.00,
    3000.00,
    30000.00,
    '["newsfeed"]'::jsonb,
    'active',
    now(),
    80,
    3
  ),
  (
    '00000000-0000-0000-0000-000000000003'::uuid,
    business_user_id,
    'Promo Flash Sérum Anti-âge',
    'Offre flash de 48h : -30% sur notre sérum anti-âge révolutionnaire.',
    'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
    'https://example.com/serum-anti-age',
    300.00,
    7000.00,
    70000.00,
    '["newsfeed", "sidebar", "marketplace"]'::jsonb,
    'active',
    now(),
    150,
    8
  );
  
END $$;

-- =====================================================
-- 3. CRÉER LES MÉTRIQUES D'ENGAGEMENT INITIALES
-- =====================================================

INSERT INTO ad_engagement_metrics (
  campaign_id,
  total_likes,
  total_comments,
  total_shares,
  engagement_rate,
  last_updated
) VALUES 
(
  '00000000-0000-0000-0000-000000000001'::uuid,
  0,
  0,
  0,
  0.0,
  now()
),
(
  '00000000-0000-0000-0000-000000000002'::uuid,
  0,
  0,
  0,
  0.0,
  now()
),
(
  '00000000-0000-0000-0000-000000000003'::uuid,
  0,
  0,
  0,
  0.0,
  now()
);

-- =====================================================
-- 4. VÉRIFICATION
-- =====================================================

-- Afficher les campagnes créées
SELECT 
  id,
  title,
  status,
  impressions,
  clicks,
  'Campagne créée avec succès ✅' as message
FROM ad_campaigns 
WHERE id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid
);

-- Afficher les métriques
SELECT 
  campaign_id,
  total_likes,
  total_comments,
  total_shares,
  'Métriques initialisées ✅' as message
FROM ad_engagement_metrics 
WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid
);

-- =====================================================
-- 5. INSTRUCTIONS POUR VOTRE CODE
-- =====================================================

/*
🎯 MAINTENANT, DANS VOTRE CODE, REMPLACEZ LES IDs MOCKÉS :

❌ AVANT :
const mockFeedAds: FeedAd[] = [
  {
    id: 'MOCK-001', // ❌ Format invalide
    // ...
  }
];

✅ APRÈS :
const mockFeedAds: FeedAd[] = [
  {
    id: '00000000-0000-0000-0000-000000000001', // ✅ UUID valide
    title: 'Promotion Été - Huile de Beauté',
    description: 'Profitez de notre offre spéciale été avec 20% de réduction...',
    imageUrl: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
    targetUrl: 'https://example.com/promo-ete',
    businessName: 'Olgane Cosmetics',
    businessLogo: '/images/default-business.jpg',
    discount: 20
  },
  {
    id: '00000000-0000-0000-0000-000000000002', // ✅ UUID valide
    title: 'Lancement Crème Hydratante',
    description: 'Découvrez notre nouvelle crème hydratante visage...',
    imageUrl: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
    targetUrl: 'https://example.com/creme-hydratante',
    businessName: 'Olgane Cosmetics',
    businessLogo: '/images/default-business.jpg'
  },
  {
    id: '00000000-0000-0000-0000-000000000003', // ✅ UUID valide
    title: 'Promo Flash Sérum Anti-âge',
    description: 'Offre flash de 48h : -30% sur notre sérum anti-âge...',
    imageUrl: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
    targetUrl: 'https://example.com/serum-anti-age',
    businessName: 'Olgane Cosmetics',
    businessLogo: '/images/default-business.jpg',
    discount: 30
  }
];

🚀 APRÈS AVOIR EXÉCUTÉ CE SCRIPT ET MODIFIÉ VOTRE CODE :
- Les boutons d'interaction fonctionneront ✅
- Les likes seront enregistrés en base ✅
- Les commentaires seront sauvegardés ✅
- Les partages seront trackés ✅
- Les métriques seront calculées automatiquement ✅
*/
