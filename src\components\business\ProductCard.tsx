// ... existing code ...

return (
  <div className="product-card">
    {product.isOnMarketplace && (
      <div className="marketplace-badge">
        <span>En vente sur Marketplace</span>
      </div>
    )}
    
    <img 
      src={product.images[0] || getImageWithFallback(null, 'product')} 
      alt={product.name} 
      className="product-image"
    />
    
    {/* ... reste du code ... */}
  </div>
);

// ... existing code ...