-- Script de test pour vérifier le système d'administration
-- À exécuter après l'installation pour valider le fonctionnement

-- =====================================================
-- 1. VÉRIFICATION DES TABLES
-- =====================================================

-- Lister toutes les tables admin créées
SELECT 
    'Tables Admin Créées' as section,
    table_name,
    CASE 
        WHEN table_name IN (
            'admin_profiles', 'admin_permissions', 'admin_sessions',
            'admin_audit_log', 'admin_alerts', 'admin_reports'
        ) THEN '✅ OK'
        ELSE '❌ Manquant'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE 'admin_%'
ORDER BY table_name;

-- =====================================================
-- 2. VÉRIFICATION DES PERMISSIONS
-- =====================================================

-- Compter les permissions par catégorie
SELECT 
    'Permissions par Catégorie' as section,
    category,
    count(*) as count,
    CASE 
        WHEN count(*) > 0 THEN '✅ OK'
        ELSE '❌ Vide'
    END as status
FROM admin_permissions 
GROUP BY category
ORDER BY category;

-- Total des permissions
SELECT 
    'Total Permissions' as section,
    count(*) as total_permissions,
    CASE 
        WHEN count(*) >= 20 THEN '✅ OK'
        ELSE '❌ Insuffisant'
    END as status
FROM admin_permissions;

-- =====================================================
-- 3. VÉRIFICATION DES FONCTIONS
-- =====================================================

-- Lister les fonctions admin créées
SELECT 
    'Fonctions Admin' as section,
    routine_name,
    routine_type,
    CASE 
        WHEN routine_name IN (
            'check_admin_permission', 'log_admin_action', 
            'create_super_admin', 'audit_admin_changes'
        ) THEN '✅ OK'
        ELSE '❓ Autre'
    END as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND (routine_name LIKE '%admin%' OR routine_name LIKE '%audit%')
ORDER BY routine_name;

-- =====================================================
-- 4. VÉRIFICATION DES TRIGGERS
-- =====================================================

-- Lister les triggers d'audit
SELECT 
    'Triggers d\'Audit' as section,
    trigger_name,
    event_object_table,
    CASE 
        WHEN trigger_name LIKE 'audit_%' THEN '✅ OK'
        ELSE '❓ Autre'
    END as status
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
AND trigger_name LIKE '%audit%'
ORDER BY trigger_name;

-- =====================================================
-- 5. VÉRIFICATION DES POLITIQUES RLS
-- =====================================================

-- Vérifier que RLS est activé sur les tables admin
SELECT 
    'RLS Status' as section,
    schemaname,
    tablename,
    rowsecurity,
    CASE 
        WHEN rowsecurity = true THEN '✅ Activé'
        ELSE '❌ Désactivé'
    END as status
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename LIKE 'admin_%'
ORDER BY tablename;

-- =====================================================
-- 6. VÉRIFICATION DES ADMINISTRATEURS
-- =====================================================

-- Lister les administrateurs existants
SELECT 
    'Administrateurs Existants' as section,
    p.username,
    p.email,
    ap.admin_level,
    ap.admin_code,
    ap.is_active,
    CASE 
        WHEN ap.is_active = true THEN '✅ Actif'
        ELSE '❌ Inactif'
    END as status
FROM admin_profiles ap
JOIN profiles p ON ap.user_id = p.id
ORDER BY ap.admin_level, p.username;

-- Compter par niveau d'admin
SELECT 
    'Admins par Niveau' as section,
    admin_level,
    count(*) as count,
    CASE 
        WHEN admin_level = 'super_admin' AND count(*) > 0 THEN '✅ OK'
        WHEN admin_level != 'super_admin' THEN '✅ OK'
        ELSE '❌ Aucun Super Admin'
    END as status
FROM admin_profiles 
WHERE is_active = true
GROUP BY admin_level
ORDER BY admin_level;

-- =====================================================
-- 7. TEST DES FONCTIONS
-- =====================================================

-- Tester la fonction de vérification des permissions
-- (Nécessite un utilisateur admin existant)
DO $$
DECLARE
    test_user_id uuid;
    permission_result boolean;
BEGIN
    -- Récupérer un admin pour le test
    SELECT user_id INTO test_user_id
    FROM admin_profiles 
    WHERE is_active = true 
    LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        -- Tester la fonction check_admin_permission
        SELECT check_admin_permission(test_user_id, 'SUPER_ADMIN_ALL') INTO permission_result;
        
        RAISE NOTICE 'Test fonction check_admin_permission: %', 
            CASE WHEN permission_result THEN '✅ OK' ELSE '❌ Échec' END;
    ELSE
        RAISE NOTICE 'Test fonction check_admin_permission: ❌ Aucun admin pour tester';
    END IF;
END $$;

-- =====================================================
-- 8. VÉRIFICATION DES INDEX
-- =====================================================

-- Lister les index sur les tables admin
SELECT 
    'Index Admin' as section,
    schemaname,
    tablename,
    indexname,
    CASE 
        WHEN indexname LIKE 'idx_admin_%' THEN '✅ OK'
        WHEN indexname LIKE '%_pkey' THEN '✅ Primary Key'
        ELSE '❓ Autre'
    END as status
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename LIKE 'admin_%'
ORDER BY tablename, indexname;

-- =====================================================
-- 9. RÉSUMÉ DE L'INSTALLATION
-- =====================================================

-- Résumé final
SELECT 
    '=== RÉSUMÉ DE L''INSTALLATION ===' as section,
    '' as details,
    '' as status
UNION ALL
SELECT 
    'Tables Admin:',
    count(*)::text,
    CASE WHEN count(*) >= 6 THEN '✅' ELSE '❌' END
FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name LIKE 'admin_%'
UNION ALL
SELECT 
    'Permissions Système:',
    count(*)::text,
    CASE WHEN count(*) >= 20 THEN '✅' ELSE '❌' END
FROM admin_permissions
UNION ALL
SELECT 
    'Super Admins:',
    count(*)::text,
    CASE WHEN count(*) > 0 THEN '✅' ELSE '❌' END
FROM admin_profiles 
WHERE admin_level = 'super_admin' AND is_active = true
UNION ALL
SELECT 
    'Fonctions Admin:',
    count(*)::text,
    CASE WHEN count(*) >= 3 THEN '✅' ELSE '❌' END
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('check_admin_permission', 'log_admin_action', 'create_super_admin')
UNION ALL
SELECT 
    'RLS Activé:',
    CASE WHEN count(*) = (SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE 'admin_%') 
         THEN 'Toutes les tables' 
         ELSE 'Partiel' END,
    CASE WHEN count(*) = (SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE 'admin_%') 
         THEN '✅' 
         ELSE '❌' END
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename LIKE 'admin_%' 
AND rowsecurity = true;

-- =====================================================
-- 10. RECOMMANDATIONS
-- =====================================================

-- Afficher les recommandations
SELECT 
    '=== RECOMMANDATIONS ===' as message
UNION ALL
SELECT 
    CASE 
        WHEN (SELECT count(*) FROM admin_profiles WHERE admin_level = 'super_admin' AND is_active = true) = 0
        THEN '❌ Créer au moins un Super Admin avec le script create-super-admin.sql'
        ELSE '✅ Super Admin configuré'
    END
UNION ALL
SELECT 
    CASE 
        WHEN (SELECT count(*) FROM admin_permissions) < 20
        THEN '❌ Permissions système incomplètes - Relancer la migration'
        ELSE '✅ Permissions système complètes'
    END
UNION ALL
SELECT 
    '📝 Consulter la documentation dans docs/ADMIN_SYSTEM.md'
UNION ALL
SELECT 
    '🔗 Accéder au tableau de bord sur /admin'
UNION ALL
SELECT 
    '🔐 Tester les permissions avec différents niveaux d''admin';

-- Message final
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TEST DU SYSTÈME D''ADMINISTRATION TERMINÉ ===';
    RAISE NOTICE 'Consultez les résultats ci-dessus pour valider l''installation.';
    RAISE NOTICE 'En cas de problème, consultez scripts/apply-admin-migration.md';
    RAISE NOTICE '';
END $$;
