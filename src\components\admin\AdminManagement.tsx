import React, { useState, useEffect } from 'react';
import {
  Shield,
  Search,
  Filter,
  Plus,
  MoreVertical,
  UserCheck,
  UserX,
  Edit,
  Trash2,
  Eye,
  Crown,
  Settings,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import { AdminService } from '../../services/adminService';
import { AdminProfile, AdminLevel, AdminFilters } from '../../types/admin';
import { supabase } from '../../lib/supabase';

// Fonction simple pour formater les dates
const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'il y a quelques secondes';
  if (diffInSeconds < 3600) return `il y a ${Math.floor(diffInSeconds / 60)} minutes`;
  if (diffInSeconds < 86400) return `il y a ${Math.floor(diffInSeconds / 3600)} heures`;
  if (diffInSeconds < 2592000) return `il y a ${Math.floor(diffInSeconds / 86400)} jours`;
  if (diffInSeconds < 31536000) return `il y a ${Math.floor(diffInSeconds / 2592000)} mois`;
  return `il y a ${Math.floor(diffInSeconds / 31536000)} ans`;
};

interface AdminManagementProps {
  onNavigateToSection?: (section: string) => void;
}

const AdminManagement: React.FC<AdminManagementProps> = ({ onNavigateToSection }) => {
  const [admins, setAdmins] = useState<AdminProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedAdmin, setSelectedAdmin] = useState<AdminProfile | null>(null);

  useEffect(() => {
    loadAdmins();
  }, []);

  const loadAdmins = async () => {
    try {
      setLoading(true);
      const filters: AdminFilters = {
        searchTerm: searchTerm || undefined,
        adminLevel: selectedLevel !== 'all' ? [selectedLevel as AdminLevel] : undefined
      };
      
      const adminList = await AdminService.getAllAdmins(filters);
      setAdmins(adminList);
    } catch (error) {
      console.error('Erreur lors du chargement des admins:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredAdmins = admins.filter(admin => {
    const matchesSearch = admin.admin_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         admin.user?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         admin.user?.email?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLevel = selectedLevel === 'all' || admin.admin_level === selectedLevel;
    const matchesStatus = selectedStatus === 'all' || 
                         (selectedStatus === 'active' && admin.is_active) ||
                         (selectedStatus === 'inactive' && !admin.is_active);
    
    return matchesSearch && matchesLevel && matchesStatus;
  });

  const getAdminLevelLabel = (level: AdminLevel): string => {
    const labels: Record<AdminLevel, string> = {
      [AdminLevel.SUPER_ADMIN]: 'Super Admin',
      [AdminLevel.PLATFORM_ADMIN]: 'Admin Plateforme',
      [AdminLevel.CONTENT_MODERATOR]: 'Modérateur',
      [AdminLevel.SUPPORT_ADMIN]: 'Support',
      [AdminLevel.BUSINESS_ADMIN]: 'Admin Business',
      [AdminLevel.USER_ADMIN]: 'Admin Utilisateurs',
      [AdminLevel.ANALYTICS_ADMIN]: 'Admin Analytics'
    };
    return labels[level] || level;
  };

  const getAdminLevelColor = (level: AdminLevel): string => {
    const colors: Record<AdminLevel, string> = {
      [AdminLevel.SUPER_ADMIN]: 'bg-purple-100 text-purple-800',
      [AdminLevel.PLATFORM_ADMIN]: 'bg-blue-100 text-blue-800',
      [AdminLevel.CONTENT_MODERATOR]: 'bg-green-100 text-green-800',
      [AdminLevel.SUPPORT_ADMIN]: 'bg-yellow-100 text-yellow-800',
      [AdminLevel.BUSINESS_ADMIN]: 'bg-indigo-100 text-indigo-800',
      [AdminLevel.USER_ADMIN]: 'bg-pink-100 text-pink-800',
      [AdminLevel.ANALYTICS_ADMIN]: 'bg-gray-100 text-gray-800'
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  const handleToggleStatus = async (admin: AdminProfile) => {
    try {
      const newStatus = !admin.is_active;
      const success = await AdminService.toggleAdminStatus(admin.id, newStatus);
      if (success) {
        await loadAdmins();
      } else {
        alert('Erreur lors du changement de statut');
      }
    } catch (error) {
      console.error('Erreur lors du changement de statut:', error);
      alert('Erreur lors du changement de statut');
    }
  };

  const handleDeleteAdmin = async (admin: AdminProfile) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer l'administrateur ${admin.user?.username} ?\n\nCette action est irréversible et l'utilisateur perdra tous ses privilèges d'administration.`)) {
      try {
        const success = await AdminService.deleteAdmin(admin.id);
        if (success) {
          await loadAdmins();
        } else {
          alert('Erreur lors de la suppression');
        }
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        alert('Erreur lors de la suppression');
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gestion des Administrateurs</h2>
          <p className="text-gray-600 mt-1">Gérer les comptes administrateurs et leurs permissions</p>
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={() => setShowCreateModal(true)}
            leftIcon={<Plus size={16} />}
          >
            Nouvel Admin
          </Button>
          <Button
            variant="outline"
            onClick={() => loadAdmins()}
            leftIcon={<Shield size={16} />}
          >
            Actualiser
          </Button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Crown size={20} className="text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Super Admins</p>
                <p className="text-lg font-semibold text-gray-900">
                  {admins.filter(a => a.admin_level === AdminLevel.SUPER_ADMIN).length}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle size={20} className="text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Actifs</p>
                <p className="text-lg font-semibold text-gray-900">
                  {admins.filter(a => a.is_active).length}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <XCircle size={20} className="text-red-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Inactifs</p>
                <p className="text-lg font-semibold text-gray-900">
                  {admins.filter(a => !a.is_active).length}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Shield size={20} className="text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-lg font-semibold text-gray-900">{admins.length}</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Filtres et recherche */}
      <Card>
        <CardBody className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Rechercher par nom, email ou code admin..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <select
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Tous les niveaux</option>
                <option value="super_admin">Super Admin</option>
                <option value="platform_admin">Admin Plateforme</option>
                <option value="content_moderator">Modérateur</option>
                <option value="support_admin">Support</option>
                <option value="business_admin">Admin Business</option>
                <option value="user_admin">Admin Utilisateurs</option>
                <option value="analytics_admin">Admin Analytics</option>
              </select>

              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Tous les statuts</option>
                <option value="active">Actifs</option>
                <option value="inactive">Inactifs</option>
              </select>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Liste des administrateurs */}
      <Card>
        <CardBody className="p-0">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : filteredAdmins.length === 0 ? (
            <div className="text-center py-12">
              <Shield size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun administrateur trouvé</h3>
              <p className="text-gray-600">Aucun administrateur ne correspond à vos critères de recherche.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Administrateur
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Niveau
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Département
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Dernière connexion
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredAdmins.map((admin) => (
                    <tr key={admin.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            {admin.user?.profile_picture ? (
                              <img
                                className="h-10 w-10 rounded-full"
                                src={admin.user.profile_picture}
                                alt=""
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                <Shield size={20} className="text-gray-600" />
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {admin.user?.username || 'N/A'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {admin.user?.email || 'N/A'}
                            </div>
                            <div className="text-xs text-gray-400">
                              {admin.admin_code}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getAdminLevelColor(admin.admin_level)}`}>
                          {getAdminLevelLabel(admin.admin_level)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {admin.department || 'Non défini'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          admin.is_active 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {admin.is_active ? 'Actif' : 'Inactif'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {admin.last_login
                          ? formatTimeAgo(admin.last_login)
                          : 'Jamais connecté'
                        }
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedAdmin(admin);
                              setShowEditModal(true);
                            }}
                          >
                            <Edit size={14} />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleToggleStatus(admin)}
                            className={admin.is_active ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'}
                          >
                            {admin.is_active ? <UserX size={14} /> : <UserCheck size={14} />}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteAdmin(admin)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 size={14} />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Modal de création d'admin */}
      {showCreateModal && (
        <CreateAdminModal
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false);
            loadAdmins();
          }}
        />
      )}

      {/* Modal d'édition d'admin */}
      {showEditModal && selectedAdmin && (
        <EditAdminModal
          admin={selectedAdmin}
          onClose={() => {
            setShowEditModal(false);
            setSelectedAdmin(null);
          }}
          onSuccess={() => {
            setShowEditModal(false);
            setSelectedAdmin(null);
            loadAdmins();
          }}
        />
      )}
    </div>
  );
};

// Modal de création d'admin
interface CreateAdminModalProps {
  onClose: () => void;
  onSuccess: () => void;
}

const CreateAdminModal: React.FC<CreateAdminModalProps> = ({ onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    adminLevel: AdminLevel.SUPPORT_ADMIN,
    department: '',
    supervisorId: ''
  });
  const [loading, setLoading] = useState(false);
  const [availableUsers, setAvailableUsers] = useState<any[]>([]);
  const [availableSupervisors, setAvailableSupervisors] = useState<AdminProfile[]>([]);

  useEffect(() => {
    loadAvailableUsers();
    loadAvailableSupervisors();
  }, []);

  const loadAvailableUsers = async () => {
    try {
      // Charger les utilisateurs non-admin
      const { data, error } = await supabase
        .from('profiles')
        .select('id, username, email')
        .neq('role', 'admin')
        .order('username');

      if (!error && data) {
        setAvailableUsers(data);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des utilisateurs:', error);
    }
  };

  const loadAvailableSupervisors = async () => {
    try {
      const supervisors = await AdminService.getAllAdmins();
      setAvailableSupervisors(supervisors.filter(admin => admin.is_active));
    } catch (error) {
      console.error('Erreur lors du chargement des superviseurs:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Trouver l'utilisateur sélectionné
      const selectedUser = availableUsers.find(user => user.email === formData.email);
      if (!selectedUser) {
        alert('Utilisateur non trouvé');
        return;
      }

      const newAdmin = await AdminService.createAdmin(
        selectedUser.id,
        formData.adminLevel,
        formData.department || undefined,
        formData.supervisorId || undefined
      );

      if (newAdmin) {
        onSuccess();
      } else {
        alert('Erreur lors de la création de l\'administrateur');
      }
    } catch (error) {
      console.error('Erreur lors de la création:', error);
      alert('Erreur lors de la création de l\'administrateur');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Créer un Administrateur</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Utilisateur
            </label>
            <select
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">Sélectionner un utilisateur</option>
              {availableUsers.map(user => (
                <option key={user.id} value={user.email}>
                  {user.username} ({user.email})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Niveau d'administration
            </label>
            <select
              value={formData.adminLevel}
              onChange={(e) => setFormData({ ...formData, adminLevel: e.target.value as AdminLevel })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value={AdminLevel.SUPPORT_ADMIN}>Support</option>
              <option value={AdminLevel.USER_ADMIN}>Admin Utilisateurs</option>
              <option value={AdminLevel.BUSINESS_ADMIN}>Admin Business</option>
              <option value={AdminLevel.CONTENT_MODERATOR}>Modérateur</option>
              <option value={AdminLevel.ANALYTICS_ADMIN}>Admin Analytics</option>
              <option value={AdminLevel.PLATFORM_ADMIN}>Admin Plateforme</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Département (optionnel)
            </label>
            <input
              type="text"
              value={formData.department}
              onChange={(e) => setFormData({ ...formData, department: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Ex: Support Client, Modération..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Superviseur (optionnel)
            </label>
            <select
              value={formData.supervisorId}
              onChange={(e) => setFormData({ ...formData, supervisorId: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Aucun superviseur</option>
              {availableSupervisors.map(supervisor => (
                <option key={supervisor.id} value={supervisor.id}>
                  {supervisor.user?.username} ({supervisor.admin_code})
                </option>
              ))}
            </select>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={loading}
            >
              {loading ? 'Création...' : 'Créer'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Modal d'édition d'admin
interface EditAdminModalProps {
  admin: AdminProfile;
  onClose: () => void;
  onSuccess: () => void;
}

const EditAdminModal: React.FC<EditAdminModalProps> = ({ admin, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    adminLevel: admin.admin_level,
    department: admin.department || '',
    supervisorId: admin.supervisor_id || '',
    notes: admin.notes || ''
  });
  const [loading, setLoading] = useState(false);
  const [availableSupervisors, setAvailableSupervisors] = useState<AdminProfile[]>([]);

  useEffect(() => {
    loadAvailableSupervisors();
  }, []);

  const loadAvailableSupervisors = async () => {
    try {
      const supervisors = await AdminService.getAllAdmins();
      setAvailableSupervisors(supervisors.filter(s => s.is_active && s.id !== admin.id));
    } catch (error) {
      console.error('Erreur lors du chargement des superviseurs:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const success = await AdminService.updateAdmin(admin.id, {
        admin_level: formData.adminLevel,
        department: formData.department || undefined,
        supervisor_id: formData.supervisorId || undefined,
        notes: formData.notes || undefined
      });

      if (success) {
        onSuccess();
      } else {
        alert('Erreur lors de la mise à jour de l\'administrateur');
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      alert('Erreur lors de la mise à jour de l\'administrateur');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Modifier l'Administrateur
        </h3>

        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center">
            <div className="flex-shrink-0 h-8 w-8">
              {admin.user?.profile_picture ? (
                <img
                  className="h-8 w-8 rounded-full"
                  src={admin.user.profile_picture}
                  alt=""
                />
              ) : (
                <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                  <Shield size={16} className="text-gray-600" />
                </div>
              )}
            </div>
            <div className="ml-3">
              <div className="text-sm font-medium text-gray-900">
                {admin.user?.username}
              </div>
              <div className="text-xs text-gray-500">
                {admin.admin_code}
              </div>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Niveau d'administration
            </label>
            <select
              value={formData.adminLevel}
              onChange={(e) => setFormData({ ...formData, adminLevel: e.target.value as AdminLevel })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value={AdminLevel.SUPPORT_ADMIN}>Support</option>
              <option value={AdminLevel.USER_ADMIN}>Admin Utilisateurs</option>
              <option value={AdminLevel.BUSINESS_ADMIN}>Admin Business</option>
              <option value={AdminLevel.CONTENT_MODERATOR}>Modérateur</option>
              <option value={AdminLevel.ANALYTICS_ADMIN}>Admin Analytics</option>
              <option value={AdminLevel.PLATFORM_ADMIN}>Admin Plateforme</option>
              <option value={AdminLevel.SUPER_ADMIN}>Super Admin</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Département
            </label>
            <input
              type="text"
              value={formData.department}
              onChange={(e) => setFormData({ ...formData, department: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Ex: Support Client, Modération..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Superviseur
            </label>
            <select
              value={formData.supervisorId}
              onChange={(e) => setFormData({ ...formData, supervisorId: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Aucun superviseur</option>
              {availableSupervisors.map(supervisor => (
                <option key={supervisor.id} value={supervisor.id}>
                  {supervisor.user?.username} ({supervisor.admin_code})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
              placeholder="Notes sur cet administrateur..."
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={loading}
            >
              {loading ? 'Mise à jour...' : 'Mettre à jour'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AdminManagement;
