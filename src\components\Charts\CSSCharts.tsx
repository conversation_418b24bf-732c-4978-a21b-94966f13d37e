import React from 'react';
import './CSSCharts.css';

interface SalesEvolutionChartProps {
  salesData: Array<{
    date: string;
    revenue: number;
    count: number;
  }>;
}

interface CategoryDistributionChartProps {
  categoryData: Array<{
    category: string;
    value: number;
  }>;
}

interface MonthlyComparisonChartProps {
  monthlyData: Array<{
    month: string;
    current: number;
    previous: number;
  }>;
}

// Graphique d'évolution des ventes en CSS pur
export const SalesEvolutionChart: React.FC<SalesEvolutionChartProps> = ({ salesData }) => {
  const maxRevenue = Math.max(...salesData.map(d => d.revenue));
  const maxCount = Math.max(...salesData.map(d => d.count));

  return (
    <div className="css-chart-container">
      <div className="chart-header">
        <h3>📈 Évolution des ventes</h3>
        <div className="chart-legend">
          <div className="legend-item">
            <span className="legend-color revenue"></span>
            <span>Chiffre d'affaires</span>
          </div>
          <div className="legend-item">
            <span className="legend-color count"></span>
            <span>Nombre de ventes</span>
          </div>
        </div>
      </div>
      
      <div className="line-chart">
        <div className="chart-grid">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="grid-line" style={{ bottom: `${i * 25}%` }}></div>
          ))}
        </div>
        
        <div className="chart-data">
          {salesData.map((data, index) => (
            <div key={index} className="data-point" style={{ left: `${(index / (salesData.length - 1)) * 100}%` }}>
              <div 
                className="revenue-bar" 
                style={{ height: `${(data.revenue / maxRevenue) * 100}%` }}
                title={`${data.date}: ${data.revenue.toLocaleString()} F CFA`}
              ></div>
              <div 
                className="count-point" 
                style={{ bottom: `${(data.count / maxCount) * 100}%` }}
                title={`${data.date}: ${data.count} ventes`}
              ></div>
              <span className="date-label">{data.date}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Graphique de répartition par catégorie en CSS pur
export const CategoryDistributionChart: React.FC<CategoryDistributionChartProps> = ({ categoryData }) => {
  const total = categoryData.reduce((sum, item) => sum + item.value, 0);
  const colors = ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899'];
  
  let currentAngle = 0;
  const segments = categoryData.map((item, index) => {
    const percentage = (item.value / total) * 100;
    const angle = (item.value / total) * 360;
    const segment = {
      ...item,
      percentage: percentage.toFixed(1),
      startAngle: currentAngle,
      angle,
      color: colors[index % colors.length]
    };
    currentAngle += angle;
    return segment;
  });

  return (
    <div className="css-chart-container">
      <div className="chart-header">
        <h3>🥧 Répartition par catégorie</h3>
      </div>
      
      <div className="pie-chart-container">
        <div className="pie-chart">
          <svg viewBox="0 0 200 200" className="pie-svg">
            {segments.map((segment, index) => {
              const radius = 80;
              const centerX = 100;
              const centerY = 100;
              
              const startAngleRad = (segment.startAngle * Math.PI) / 180;
              const endAngleRad = ((segment.startAngle + segment.angle) * Math.PI) / 180;
              
              const x1 = centerX + radius * Math.cos(startAngleRad);
              const y1 = centerY + radius * Math.sin(startAngleRad);
              const x2 = centerX + radius * Math.cos(endAngleRad);
              const y2 = centerY + radius * Math.sin(endAngleRad);
              
              const largeArcFlag = segment.angle > 180 ? 1 : 0;
              
              const pathData = [
                `M ${centerX} ${centerY}`,
                `L ${x1} ${y1}`,
                `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                'Z'
              ].join(' ');
              
              return (
                <path
                  key={index}
                  d={pathData}
                  fill={segment.color}
                  stroke="white"
                  strokeWidth="2"
                  className="pie-segment"
                  title={`${segment.category}: ${segment.percentage}%`}
                />
              );
            })}
          </svg>
        </div>
        
        <div className="pie-legend">
          {segments.map((segment, index) => (
            <div key={index} className="legend-item">
              <span 
                className="legend-color" 
                style={{ backgroundColor: segment.color }}
              ></span>
              <span className="legend-text">
                {segment.category} ({segment.percentage}%)
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Graphique de comparaison mensuelle en CSS pur
export const MonthlyComparisonChart: React.FC<MonthlyComparisonChartProps> = ({ monthlyData }) => {
  const maxValue = Math.max(...monthlyData.flatMap(d => [d.current, d.previous]));

  return (
    <div className="css-chart-container">
      <div className="chart-header">
        <h3>📊 Comparaison mensuelle</h3>
        <div className="chart-legend">
          <div className="legend-item">
            <span className="legend-color current"></span>
            <span>Mois actuel</span>
          </div>
          <div className="legend-item">
            <span className="legend-color previous"></span>
            <span>Mois précédent</span>
          </div>
        </div>
      </div>
      
      <div className="bar-chart">
        <div className="chart-grid">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="grid-line" style={{ bottom: `${i * 25}%` }}></div>
          ))}
        </div>
        
        <div className="bars-container">
          {monthlyData.map((data, index) => {
            const change = ((data.current - data.previous) / data.previous * 100).toFixed(1);
            const isPositive = data.current > data.previous;
            
            return (
              <div key={index} className="bar-group">
                <div className="bars">
                  <div 
                    className="bar current" 
                    style={{ height: `${(data.current / maxValue) * 100}%` }}
                    title={`${data.month} actuel: ${data.current.toLocaleString()} F CFA`}
                  ></div>
                  <div 
                    className="bar previous" 
                    style={{ height: `${(data.previous / maxValue) * 100}%` }}
                    title={`${data.month} précédent: ${data.previous.toLocaleString()} F CFA`}
                  ></div>
                </div>
                <div className="bar-label">
                  <span className="month-name">{data.month}</span>
                  <span className={`change-indicator ${isPositive ? 'positive' : 'negative'}`}>
                    {isPositive ? '+' : ''}{change}%
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default {
  SalesEvolutionChart,
  CategoryDistributionChart,
  MonthlyComparisonChart
};
