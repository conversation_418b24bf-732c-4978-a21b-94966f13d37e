import { useState, useEffect } from 'react';
import { AdminService } from '../services/adminService';
import { AdminProfile, AdminLevel } from '../types/admin';
import { useAuth } from '../context/AuthContext';

interface AdminPermissions {
  adminProfile: AdminProfile | null;
  loading: boolean;
  error: string | null;
  hasPermission: (permissionCode: string) => boolean;
  hasLevel: (requiredLevel: AdminLevel) => boolean;
  canAccess: (section: string) => boolean;
  refreshProfile: () => Promise<void>;
}

export const useAdminPermissions = (): AdminPermissions => {
  const { currentUser } = useAuth();
  const [adminProfile, setAdminProfile] = useState<AdminProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (currentUser?.role === 'admin') {
      loadAdminProfile();
    } else {
      setLoading(false);
    }
  }, [currentUser]);

  const loadAdminProfile = async () => {
    try {
      setLoading(true);
      setError(null);
      const profile = await AdminService.getCurrentAdminProfile();
      setAdminProfile(profile);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement du profil admin');
    } finally {
      setLoading(false);
    }
  };

  const hasPermission = async (permissionCode: string): Promise<boolean> => {
    try {
      return await AdminService.hasPermission(permissionCode);
    } catch (error) {
      console.error('Erreur lors de la vérification des permissions:', error);
      return false;
    }
  };

  const hasLevel = (requiredLevel: AdminLevel): boolean => {
    if (!adminProfile) return false;
    
    // Super admin a tous les droits
    if (adminProfile.admin_level === AdminLevel.SUPER_ADMIN) return true;
    
    // Platform admin a accès à presque tout sauf super admin
    if (adminProfile.admin_level === AdminLevel.PLATFORM_ADMIN && 
        requiredLevel !== AdminLevel.SUPER_ADMIN) return true;
    
    // Vérifier le niveau exact
    return adminProfile.admin_level === requiredLevel;
  };

  const canAccess = (section: string): boolean => {
    if (!adminProfile) return false;

    const sectionPermissions: Record<string, AdminLevel> = {
      'dashboard': AdminLevel.SUPPORT_ADMIN,
      'users': AdminLevel.USER_ADMIN,
      'businesses': AdminLevel.BUSINESS_ADMIN,
      'content': AdminLevel.CONTENT_MODERATOR,
      'alerts': AdminLevel.SUPPORT_ADMIN,
      'admins': AdminLevel.PLATFORM_ADMIN,
      'analytics': AdminLevel.ANALYTICS_ADMIN,
      'settings': AdminLevel.PLATFORM_ADMIN,
      'system': AdminLevel.SUPER_ADMIN
    };

    const requiredLevel = sectionPermissions[section];
    if (!requiredLevel) return false;

    return hasLevel(requiredLevel);
  };

  const refreshProfile = async () => {
    await loadAdminProfile();
  };

  return {
    adminProfile,
    loading,
    error,
    hasPermission: (permissionCode: string) => {
      // Version synchrone pour l'interface, utiliser la version async pour les vérifications importantes
      if (!adminProfile) return false;
      if (adminProfile.admin_level === AdminLevel.SUPER_ADMIN) return true;
      
      // Ici on pourrait implémenter une cache des permissions
      return false;
    },
    hasLevel,
    canAccess,
    refreshProfile
  };
};

// Hook pour vérifier une permission spécifique de manière asynchrone
export const usePermissionCheck = (permissionCode: string) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkPermission = async () => {
      try {
        setLoading(true);
        const result = await AdminService.hasPermission(permissionCode);
        setHasPermission(result);
      } catch (error) {
        console.error('Erreur lors de la vérification de permission:', error);
        setHasPermission(false);
      } finally {
        setLoading(false);
      }
    };

    checkPermission();
  }, [permissionCode]);

  return { hasPermission, loading };
};

// Hook pour les niveaux d'accès par section
export const useSectionAccess = () => {
  const { adminProfile } = useAdminPermissions();

  const getSectionAccess = () => {
    if (!adminProfile) {
      return {
        dashboard: false,
        users: false,
        businesses: false,
        content: false,
        alerts: false,
        admins: false,
        analytics: false,
        settings: false
      };
    }

    const level = adminProfile.admin_level;
    const isSuperAdmin = level === AdminLevel.SUPER_ADMIN;
    const isPlatformAdmin = level === AdminLevel.PLATFORM_ADMIN;

    return {
      dashboard: isSuperAdmin || isPlatformAdmin || level === AdminLevel.SUPPORT_ADMIN,
      users: isSuperAdmin || isPlatformAdmin || level === AdminLevel.USER_ADMIN,
      businesses: isSuperAdmin || isPlatformAdmin || level === AdminLevel.BUSINESS_ADMIN,
      content: isSuperAdmin || isPlatformAdmin || level === AdminLevel.CONTENT_MODERATOR,
      alerts: isSuperAdmin || isPlatformAdmin || level === AdminLevel.SUPPORT_ADMIN,
      admins: isSuperAdmin || isPlatformAdmin,
      analytics: isSuperAdmin || isPlatformAdmin || level === AdminLevel.ANALYTICS_ADMIN,
      settings: isSuperAdmin || isPlatformAdmin
    };
  };

  return getSectionAccess();
};

// Composant HOC pour protéger les routes admin
export const withAdminProtection = <P extends object>(
  Component: React.ComponentType<P>,
  requiredLevel?: AdminLevel
) => {
  return (props: P) => {
    const { adminProfile, loading, hasLevel } = useAdminPermissions();

    if (loading) {
      return (
        <div className="flex justify-center items-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (!adminProfile) {
      return (
        <div className="flex justify-center items-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-2">Accès Refusé</h1>
            <p className="text-gray-600">Vous n'avez pas les permissions d'administrateur.</p>
          </div>
        </div>
      );
    }

    if (requiredLevel && !hasLevel(requiredLevel)) {
      return (
        <div className="flex justify-center items-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-orange-600 mb-2">Niveau Insuffisant</h1>
            <p className="text-gray-600">
              Votre niveau d'administration ne permet pas d'accéder à cette section.
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Niveau requis: {requiredLevel} | Votre niveau: {adminProfile.admin_level}
            </p>
          </div>
        </div>
      );
    }

    return <Component {...props} />;
  };
};

// Utilitaires pour les permissions
export const AdminPermissionUtils = {
  // Vérifier si un utilisateur peut effectuer une action
  canPerformAction: (adminProfile: AdminProfile | null, action: string): boolean => {
    if (!adminProfile || !adminProfile.is_active) return false;
    
    // Super admin peut tout faire
    if (adminProfile.admin_level === AdminLevel.SUPER_ADMIN) return true;
    
    // Définir les actions par niveau
    const actionPermissions: Record<string, AdminLevel[]> = {
      'create_user': [AdminLevel.SUPER_ADMIN, AdminLevel.PLATFORM_ADMIN, AdminLevel.USER_ADMIN],
      'delete_user': [AdminLevel.SUPER_ADMIN, AdminLevel.PLATFORM_ADMIN],
      'ban_user': [AdminLevel.SUPER_ADMIN, AdminLevel.PLATFORM_ADMIN, AdminLevel.CONTENT_MODERATOR],
      'verify_business': [AdminLevel.SUPER_ADMIN, AdminLevel.PLATFORM_ADMIN, AdminLevel.BUSINESS_ADMIN],
      'moderate_content': [AdminLevel.SUPER_ADMIN, AdminLevel.PLATFORM_ADMIN, AdminLevel.CONTENT_MODERATOR],
      'view_analytics': [AdminLevel.SUPER_ADMIN, AdminLevel.PLATFORM_ADMIN, AdminLevel.ANALYTICS_ADMIN],
      'manage_admins': [AdminLevel.SUPER_ADMIN, AdminLevel.PLATFORM_ADMIN],
      'system_config': [AdminLevel.SUPER_ADMIN]
    };
    
    const allowedLevels = actionPermissions[action];
    return allowedLevels ? allowedLevels.includes(adminProfile.admin_level) : false;
  },

  // Obtenir la liste des actions disponibles pour un admin
  getAvailableActions: (adminProfile: AdminProfile | null): string[] => {
    if (!adminProfile) return [];
    
    const allActions = [
      'create_user', 'delete_user', 'ban_user', 'verify_business',
      'moderate_content', 'view_analytics', 'manage_admins', 'system_config'
    ];
    
    return allActions.filter(action => 
      AdminPermissionUtils.canPerformAction(adminProfile, action)
    );
  },

  // Formater le niveau d'admin pour l'affichage
  formatAdminLevel: (level: AdminLevel): string => {
    const levelNames: Record<AdminLevel, string> = {
      [AdminLevel.SUPER_ADMIN]: 'Super Administrateur',
      [AdminLevel.PLATFORM_ADMIN]: 'Administrateur Plateforme',
      [AdminLevel.CONTENT_MODERATOR]: 'Modérateur de Contenu',
      [AdminLevel.SUPPORT_ADMIN]: 'Administrateur Support',
      [AdminLevel.BUSINESS_ADMIN]: 'Administrateur Business',
      [AdminLevel.USER_ADMIN]: 'Administrateur Utilisateurs',
      [AdminLevel.ANALYTICS_ADMIN]: 'Administrateur Analytics'
    };
    
    return levelNames[level] || level;
  }
};
