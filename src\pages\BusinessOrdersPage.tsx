import React from 'react';
import { useAuth } from '../context/AuthContext';
import { UserRole } from '../types';
import { Navigate } from 'react-router-dom';
import BusinessOrdersManager from '../components/orders/BusinessOrdersManager';

const BusinessOrdersPage: React.FC = () => {
  const { currentUser } = useAuth();

  // Rediriger si l'utilisateur n'est pas une entreprise
  if (currentUser && currentUser.role !== UserRole.BUSINESS) {
    return <Navigate to="/profile" replace />;
  }

  return (
    <div>
      <BusinessOrdersManager businessId={currentUser?.id || ''} />
    </div>
  );
};

export default BusinessOrdersPage;
