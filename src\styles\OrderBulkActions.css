/* Actions en lot pour les commandes */
.bulk-actions-card {
  margin-bottom: 1rem;
  border: 2px solid #3b82f6;
  background: #eff6ff;
}

.bulk-actions-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

/* Informations de sélection */
.selection-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.selection-checkbox input[type="checkbox"] {
  width: 1.125rem;
  height: 1.125rem;
  accent-color: #3b82f6;
}

.selection-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.selected-count {
  font-weight: 600;
  color: #1e40af;
  font-size: 0.875rem;
}

.select-all-btn {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 0.75rem;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
}

.select-all-btn:hover {
  color: #1e40af;
}

/* Actions en lot */
.bulk-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Dropdowns d'actions */
.action-dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 50;
  min-width: 160px;
  padding: 0.5rem 0;
  margin-top: 0.25rem;
}

.dropdown-menu button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: none;
  background: none;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  text-align: left;
  transition: background-color 0.2s;
}

.dropdown-menu button:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.dropdown-menu button:first-child {
  border-radius: 8px 8px 0 0;
}

.dropdown-menu button:last-child {
  border-radius: 0 0 8px 8px;
}

/* Dialog de confirmation */
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.confirmation-dialog {
  background: white;
  border-radius: 12px;
  max-width: 400px;
  width: 100%;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.confirmation-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.warning-icon {
  color: #f59e0b;
}

.confirmation-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
}

.confirmation-body {
  padding: 1rem 1.5rem;
}

.confirmation-body p {
  margin: 0 0 0.75rem 0;
  color: #374151;
  line-height: 1.5;
}

.confirmation-warning {
  color: #92400e !important;
  font-size: 0.875rem;
  background: #fef3c7;
  padding: 0.75rem;
  border-radius: 6px;
  border-left: 4px solid #f59e0b;
}

.confirmation-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  background: #f9fafb;
}

/* Responsive */
@media (max-width: 768px) {
  .bulk-actions-container {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .selection-info {
    justify-content: center;
  }

  .bulk-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .dropdown-menu {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    min-width: 200px;
  }

  .confirmation-dialog {
    margin: 1rem;
    max-width: none;
  }

  .confirmation-actions {
    flex-direction: column;
  }
}

/* États des boutons */
.bulk-actions button {
  white-space: nowrap;
}

.bulk-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Animation d'apparition */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bulk-actions-card {
  animation: slideDown 0.3s ease-out;
}

.dropdown-menu {
  animation: slideDown 0.2s ease-out;
}

/* Indicateur de sélection partielle */
input[type="checkbox"]:indeterminate {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

input[type="checkbox"]:indeterminate::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 2px;
  background: white;
  border-radius: 1px;
}

/* Styles pour les icônes dans les boutons */
.bulk-actions button svg {
  flex-shrink: 0;
}

/* Hover effects */
.action-dropdown:hover .dropdown-menu {
  display: block;
}

/* Focus states */
.bulk-actions button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.dropdown-menu button:focus {
  background: #e5e7eb;
  outline: none;
}

/* Loading state */
.bulk-actions button.loading {
  position: relative;
  color: transparent;
}

.bulk-actions button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
