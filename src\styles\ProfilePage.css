/* Page de profil principale */
.profile-page-container {
  width: 100%;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* En-tête du profil */
.profile-header-container {
  width: 100%;
  position: relative;
  margin-bottom: 20px;
}

.profile-cover-photo {
  width: 100%;
  height: 200px;
  background-size: cover;
  background-position: center;
  position: relative;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.profile-status {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 10px;
}

.status-badge, .location-badge {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.profile-info-section {
  display: flex;
  align-items: flex-end;
  padding: 0 20px;
  margin-top: -50px;
  position: relative;
}

.profile-avatar-container {
  margin-right: 20px;
}

.profile-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.profile-details {
  flex-grow: 1;
  padding-bottom: 10px;
}

.profile-username {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  color: #333;
}

.profile-website {
  font-size: 14px;
  color: #1da1f2;
  text-decoration: none;
  display: block;
  margin-top: 4px;
}

.profile-meta {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.location-info {
  display: inline-flex;
  align-items: center;
  color: #1da1f2;
  font-weight: 500;
  position: relative;
  padding-left: 15px;
}

.location-info:before {
  content: '•';
  position: absolute;
  left: 5px;
  color: #ccc;
}

.profile-actions {
  display: flex;
  align-items: center;
}

.edit-profile-button, .follow-button {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.edit-profile-button {
  background-color: transparent;
  border: 1px solid #1da1f2;
  color: #1da1f2;
}

.follow-button {
  background-color: #1da1f2;
  border: none;
  color: white;
}

.follow-button.following {
  background-color: #e0245e;
}

.edit-profile-button:hover {
  background-color: rgba(29, 161, 242, 0.1);
}

.follow-button:hover {
  opacity: 0.9;
}

/* Contenu principal */
.profile-content-container {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 20px;
  padding: 0 20px 20px;
}

/* Sidebar gauche */
.profile-sidebar-left {
  grid-column: 1;
}

/* Contenu principal */
.profile-main-content {
  grid-column: 2;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Sidebar droite */
.profile-sidebar-right {
  grid-column: 3;
}

/* Onglets */
.profile-tabs-container {
  border-bottom: 1px solid #e1e8ed;
}

/* Contenu des onglets */
.profile-tab-content {
  padding: 20px;
}

/* Posts */
.posts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.post-card {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 15px;
  background-color: white;
}

.post-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.post-type-badge {
  background-color: #f0f7ff;
  color: #1da1f2;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.post-date {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #657786;
  font-size: 12px;
}

.post-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #14171a;
}

.post-business {
  margin-bottom: 8px;
  font-size: 14px;
}

.business-label {
  color: #657786;
  margin-right: 4px;
}

.business-name {
  font-weight: 500;
  color: #14171a;
}

.post-description {
  font-size: 14px;
  line-height: 1.4;
  color: #14171a;
  margin-bottom: 12px;
}

.post-images {
  margin-bottom: 12px;
}

.post-images img {
  width: 100%;
  border-radius: 8px;
  max-height: 300px;
  object-fit: cover;
}

.post-footer {
  border-top: 1px solid #e1e8ed;
  padding-top: 12px;
}

.post-stats {
  display: flex;
  gap: 16px;
}

.post-stat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #657786;
  font-size: 14px;
}

.no-posts-message {
  text-align: center;
  padding: 30px 0;
  color: #657786;
}

.create-post-button {
  background-color: #1da1f2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  margin-top: 10px;
  cursor: pointer;
}

/* Produits mieux notés */
.top-rated-products {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 15px;
}

.sidebar-title {
  font-size: 16px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 15px;
  color: #14171a;
}

.product-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-image {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  object-fit: cover;
}

.product-info {
  flex-grow: 1;
}

.product-name {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 2px 0;
  color: #14171a;
}

.product-category {
  font-size: 12px;
  color: #657786;
  margin: 0 0 2px 0;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stars {
  color: #ffac33;
  font-size: 12px;
}

.rating-value {
  font-size: 12px;
  font-weight: 600;
  color: #14171a;
}

.view-all-link {
  display: block;
  text-align: center;
  margin-top: 15px;
  color: #1da1f2;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

/* États vides */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-state-icon {
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-state h3 {
  margin-bottom: 8px;
  color: #374151;
}

.empty-state p {
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 0;
}

/* Fil d'actualité */
.newsfeed-container {
  padding: 0;
}

.newsfeed-header {
  padding: 20px 20px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 0 !important;
}

.newsfeed-header h2 {
  margin: 0;
}

.newsfeed-header p {
  margin: 8px 0 20px 0;
}

/* Améliorations des onglets */
.profile-tabs-container .flex {
  width: 100%;
}

.profile-tabs-container .flex button {
  border-radius: 0;
  border-bottom: 2px solid transparent;
  position: relative;
  min-height: 70px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.2s ease;
  letter-spacing: 0.025em;
}

.profile-tabs-container .flex button:hover {
  background-color: #f9fafb;
  transform: translateY(-1px);
}

.profile-tabs-container .flex button.active {
  background-color: #eff6ff;
  border-bottom-color: #3b82f6;
  color: #2563eb;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

/* Style pour un seul onglet */
.profile-tabs-container .single-tab button {
  border-bottom: 2px solid #3b82f6;
  background-color: #eff6ff;
  color: #2563eb;
  cursor: default;
}

.profile-tabs-container .single-tab button:hover {
  transform: none;
  background-color: #eff6ff;
}

/* Responsive pour les onglets */
@media (max-width: 640px) {
  .profile-tabs-container .flex button {
    min-height: 50px;
    font-size: 14px;
    padding: 12px 16px;
  }

  .profile-tabs-container .flex button span {
    display: none;
  }
}

/* Utilitaires */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Styles pour les cartes modernes - Utilisation de Tailwind CSS */

/* Styles obsolètes supprimés - Utilisation de Tailwind CSS pour le nouveau design */

/* Responsive */
@media (max-width: 1200px) {
  .profile-content-container {
    grid-template-columns: 200px 1fr;
  }
}

@media (max-width: 992px) {
  .profile-content-container {
    grid-template-columns: 1fr;
  }

  .profile-sidebar-left {
    order: 2;
  }

  .profile-main-content {
    order: 1;
  }
}

@media (max-width: 768px) {
  .profile-content-container {
    grid-template-columns: 1fr;
  }

  .profile-sidebar-left {
    order: 2;
  }

  .profile-main-content {
    order: 1;
  }

  .profile-info-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .profile-avatar-container {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .profile-actions {
    margin-top: 10px;
  }
}