-- SCRIPT DE DEBUG POUR LA MODÉRATION DE CONTENU
-- Exécutez ce script pour diagnostiquer pourquoi aucun contenu n'apparaît

-- =====================================================
-- 1. VÉRIFIER LES TABLES ET DONNÉES EXISTANTES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 DIAGNOSTIC DE LA MODÉRATION DE CONTENU';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '';
END $$;

-- Vérifier les tables principales
SELECT 
    'Table posts' as table_name,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'posts') 
         THEN '✅ Existe' ELSE '❌ N''existe pas' END as status,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'posts') 
         THEN (SELECT COUNT(*)::text FROM posts) ELSE '0' END as count;

SELECT 
    'Table ad_comments' as table_name,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'ad_comments') 
         THEN '✅ Existe' ELSE '❌ N''existe pas' END as status,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'ad_comments') 
         THEN (SELECT COUNT(*)::text FROM ad_comments) ELSE '0' END as count;

SELECT 
    'Table marketcomen' as table_name,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'marketcomen') 
         THEN '✅ Existe' ELSE '❌ N''existe pas' END as status,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'marketcomen') 
         THEN (SELECT COUNT(*)::text FROM marketcomen) ELSE '0' END as count;

-- =====================================================
-- 2. LISTER TOUT LE CONTENU DISPONIBLE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📋 CONTENU DISPONIBLE PAR TABLE:';
    RAISE NOTICE '===============================';
END $$;

-- Lister les posts existants
SELECT 
    'POSTS' as type,
    id,
    COALESCE(description, 'Pas de description') as content,
    business_name,
    type as post_type,
    created_at
FROM posts 
ORDER BY created_at DESC 
LIMIT 10;

-- Lister les commentaires de publicités
SELECT 
    'AD_COMMENTS' as type,
    id,
    content,
    campaign_id,
    is_approved,
    is_flagged,
    created_at
FROM ad_comments 
ORDER BY created_at DESC 
LIMIT 10;

-- Lister les commentaires marketplace
SELECT 
    'MARKETPLACE_COMMENTS' as type,
    id,
    comment as content,
    product_id,
    is_approved,
    created_at
FROM marketcomen 
ORDER BY created_at DESC 
LIMIT 10;

-- =====================================================
-- 3. CRÉER DU CONTENU DE TEST SI AUCUN N'EXISTE
-- =====================================================

DO $$
DECLARE
    posts_count integer;
    comments_count integer;
    market_count integer;
    test_user_id uuid;
    test_business_id uuid;
    test_campaign_id uuid;
BEGIN
    -- Compter le contenu existant
    SELECT COUNT(*) INTO posts_count FROM posts;
    SELECT COUNT(*) INTO comments_count FROM ad_comments;
    SELECT COUNT(*) INTO market_count FROM marketcomen;
    
    RAISE NOTICE '';
    RAISE NOTICE '📊 CONTENU ACTUEL:';
    RAISE NOTICE 'Posts: %, Commentaires pub: %, Avis marketplace: %', posts_count, comments_count, market_count;
    
    IF posts_count = 0 AND comments_count = 0 AND market_count = 0 THEN
        RAISE NOTICE '';
        RAISE NOTICE '🏗️ CRÉATION DE CONTENU DE TEST...';
        
        -- Récupérer ou créer un utilisateur de test
        SELECT id INTO test_user_id FROM profiles WHERE role = 'standard' LIMIT 1;
        
        IF test_user_id IS NULL THEN
            test_user_id := gen_random_uuid();
            INSERT INTO profiles (
                id, username, email, role, status, created_at, updated_at
            ) VALUES (
                test_user_id,
                'test_content_user',
                '<EMAIL>',
                'standard',
                'member',
                NOW(),
                NOW()
            );
            RAISE NOTICE '✅ Utilisateur de test créé: %', test_user_id;
        ELSE
            RAISE NOTICE '✅ Utilisateur existant utilisé: %', test_user_id;
        END IF;
        
        -- Créer des posts de test
        INSERT INTO posts (
            id, user_id, type, business_name, product_name, category,
            description, rating, created_at, updated_at
        ) VALUES 
        (
            gen_random_uuid(),
            test_user_id,
            'coup_de_coeur',
            'Entreprise Fantastique',
            'Produit Excellent',
            'Technology',
            'Je recommande vivement cette entreprise ! Service client exceptionnel et produits de qualité.',
            5,
            NOW() - INTERVAL '2 days',
            NOW()
        ),
        (
            gen_random_uuid(),
            test_user_id,
            'coup_de_gueule',
            'Entreprise Décevante',
            'Produit Défaillant',
            'Services',
            'Très déçu de mon achat. Le produit ne fonctionne pas comme annoncé et le SAV est inexistant.',
            1,
            NOW() - INTERVAL '1 day',
            NOW()
        ),
        (
            gen_random_uuid(),
            test_user_id,
            'coup_de_coeur',
            'Restaurant Délicieux',
            'Menu Gastronomique',
            'Restaurant',
            'Excellent restaurant ! Cuisine raffinée, service impeccable, je reviendrai sans hésiter.',
            5,
            NOW() - INTERVAL '3 hours',
            NOW()
        );
        
        RAISE NOTICE '✅ 3 posts de test créés';
        
        -- Récupérer ou créer une entreprise
        SELECT bp.id INTO test_business_id 
        FROM business_profiles bp 
        JOIN profiles p ON bp.id = p.id 
        WHERE p.role = 'business' 
        LIMIT 1;
        
        IF test_business_id IS NULL THEN
            test_business_id := gen_random_uuid();
            INSERT INTO profiles (
                id, username, email, role, status, created_at, updated_at
            ) VALUES (
                test_business_id,
                'test_content_business',
                '<EMAIL>',
                'business',
                'contributor',
                NOW(),
                NOW()
            );
            
            INSERT INTO business_profiles (
                id, business_name, business_description, business_category,
                business_status, business_verified, created_at, updated_at
            ) VALUES (
                test_business_id,
                'Test Content Business',
                'Entreprise de test pour la modération de contenu',
                'Technology',
                'verified',
                true,
                NOW(),
                NOW()
            );
            RAISE NOTICE '✅ Entreprise de test créée: %', test_business_id;
        END IF;
        
        -- Créer une campagne publicitaire de test
        INSERT INTO ad_campaigns (
            id, business_id, title, description, budget, status,
            start_date, end_date, created_at, updated_at
        ) VALUES (
            gen_random_uuid(),
            test_business_id,
            'Campagne Test Modération',
            'Campagne publicitaire pour tester la modération des commentaires',
            1000.00,
            'active',
            NOW(),
            NOW() + INTERVAL '30 days',
            NOW(),
            NOW()
        ) RETURNING id INTO test_campaign_id;
        
        RAISE NOTICE '✅ Campagne publicitaire créée: %', test_campaign_id;
        
        -- Créer des commentaires de publicité de test
        INSERT INTO ad_comments (
            id, campaign_id, user_id, content, is_approved, is_flagged,
            created_at, updated_at
        ) VALUES 
        (
            gen_random_uuid(),
            test_campaign_id,
            test_user_id,
            'Super publicité ! Cette entreprise a l''air très professionnelle et fiable.',
            true,
            false,
            NOW() - INTERVAL '1 day',
            NOW()
        ),
        (
            gen_random_uuid(),
            test_campaign_id,
            test_user_id,
            'Publicité intéressante, j''aimerais en savoir plus sur vos services.',
            true,
            false,
            NOW() - INTERVAL '3 hours',
            NOW()
        ),
        (
            gen_random_uuid(),
            test_campaign_id,
            test_user_id,
            'Attention, cette publicité semble suspecte. Méfiez-vous !',
            false,
            true,
            NOW() - INTERVAL '1 hour',
            NOW()
        );
        
        RAISE NOTICE '✅ 3 commentaires de publicité créés';
        
        -- Créer des commentaires marketplace de test
        INSERT INTO marketcomen (
            id, product_id, user_id, comment, is_approved, is_verified_purchase,
            created_at, updated_at
        ) VALUES 
        (
            gen_random_uuid(),
            'PROD_TEST_001',
            test_user_id,
            'Excellent produit ! Livraison rapide et emballage soigné. Je recommande vivement.',
            true,
            true,
            NOW() - INTERVAL '2 days',
            NOW()
        ),
        (
            gen_random_uuid(),
            'PROD_TEST_002',
            test_user_id,
            'Produit correct mais sans plus. Le rapport qualité-prix pourrait être amélioré.',
            true,
            false,
            NOW() - INTERVAL '1 day',
            NOW()
        ),
        (
            gen_random_uuid(),
            'PROD_TEST_003',
            test_user_id,
            'Produit défectueux reçu. Service client non réactif. Je déconseille cet achat.',
            false,
            true,
            NOW() - INTERVAL '2 hours',
            NOW()
        );
        
        RAISE NOTICE '✅ 3 commentaires marketplace créés';
        
    ELSE
        RAISE NOTICE '✅ Du contenu existe déjà, pas besoin de créer du contenu de test';
    END IF;
END $$;

-- =====================================================
-- 4. VÉRIFIER LES JOINTURES ET RELATIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔗 VÉRIFICATION DES RELATIONS:';
    RAISE NOTICE '=============================';
END $$;

-- Vérifier les posts avec leurs auteurs
SELECT 
    'Posts avec auteurs' as verification,
    COUNT(*) as total,
    COUNT(p.username) as avec_auteur,
    COUNT(*) - COUNT(p.username) as sans_auteur
FROM posts po
LEFT JOIN profiles p ON po.user_id = p.id;

-- Vérifier les commentaires avec leurs auteurs
SELECT 
    'Commentaires pub avec auteurs' as verification,
    COUNT(*) as total,
    COUNT(p.username) as avec_auteur,
    COUNT(*) - COUNT(p.username) as sans_auteur
FROM ad_comments ac
LEFT JOIN profiles p ON ac.user_id = p.id;

-- Vérifier les avis marketplace avec leurs auteurs
SELECT 
    'Avis marketplace avec auteurs' as verification,
    COUNT(*) as total,
    COUNT(p.username) as avec_auteur,
    COUNT(*) - COUNT(p.username) as sans_auteur
FROM marketcomen mc
LEFT JOIN profiles p ON mc.user_id = p.id;

-- =====================================================
-- 5. RÉSUMÉ ET INSTRUCTIONS
-- =====================================================

DO $$
DECLARE
    total_posts integer;
    total_ad_comments integer;
    total_market_comments integer;
    total_content integer;
BEGIN
    SELECT COUNT(*) INTO total_posts FROM posts;
    SELECT COUNT(*) INTO total_ad_comments FROM ad_comments;
    SELECT COUNT(*) INTO total_market_comments FROM marketcomen;
    total_content := total_posts + total_ad_comments + total_market_comments;
    
    RAISE NOTICE '';
    RAISE NOTICE '📊 RÉSUMÉ FINAL:';
    RAISE NOTICE '===============';
    RAISE NOTICE 'Posts: %', total_posts;
    RAISE NOTICE 'Commentaires publicités: %', total_ad_comments;
    RAISE NOTICE 'Avis marketplace: %', total_market_comments;
    RAISE NOTICE 'TOTAL CONTENU: %', total_content;
    RAISE NOTICE '';
    
    IF total_content > 0 THEN
        RAISE NOTICE '✅ Du contenu est disponible pour la modération !';
        RAISE NOTICE '';
        RAISE NOTICE '🎯 PROCHAINES ÉTAPES:';
        RAISE NOTICE '1. Rechargez la page de modération de contenu';
        RAISE NOTICE '2. Vérifiez la console du navigateur pour les erreurs';
        RAISE NOTICE '3. Si le problème persiste, vérifiez les permissions RLS';
        RAISE NOTICE '4. Testez avec les filtres "Tous les types" et "Tous les statuts"';
    ELSE
        RAISE NOTICE '❌ Aucun contenu trouvé !';
        RAISE NOTICE 'Exécutez ce script à nouveau pour créer du contenu de test';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '🔧 SI LE PROBLÈME PERSISTE:';
    RAISE NOTICE '1. Vérifiez que RLS n''est pas activé sur les tables';
    RAISE NOTICE '2. Consultez les logs de la console navigateur';
    RAISE NOTICE '3. Vérifiez les permissions de l''utilisateur admin';
    RAISE NOTICE '';
END $$;
