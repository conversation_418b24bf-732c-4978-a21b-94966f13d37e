import React, { useState, useEffect } from 'react';
import { adCampaignService, FeedAd } from '../../services/adCampaignService';
import FeedAdCard from './FeedAdCard';
import { DEFAULT_IMAGES_SIZED } from '../../constants/defaultImages';

interface DatabaseAdManagerProps {
  placementId: string;
  maxAds?: number;
  rotationIntervalMs?: number;
  fallbackToMockData?: boolean;
}

// Données mockées de fallback
const mockFeedAds: FeedAd[] = [
  {
    id: '00000000-0000-0000-0000-000000000001', // ✅ UUID valide
    title: 'Promotion Été - Huile de Beauté',
    description: 'Profitez de notre offre spéciale été avec 20% de réduction sur notre huile de beauté bio. Livraison gratuite pour toute commande.',
    imageUrl: '/images/placeholders/cosmetic-ad-1.jpg',
    targetUrl: 'https://example.com/promo-ete',
    businessName: 'Olgane Cosmetics',
    businessLogo: '/images/placeholders/business-logo-1.png',
    discount: 20
  },
  {
    id: '00000000-0000-0000-0000-000000000002', // ✅ UUID valide
    title: 'Lancement Crème Hydratante',
    description: 'Découvrez notre nouvelle crème hydratante visage aux extraits naturels. Idéale pour tous types de peau.',
    imageUrl: '/images/placeholders/cosmetic-ad-2.jpg',
    targetUrl: 'https://example.com/creme-hydratante',
    businessName: 'Olgane Cosmetics',
    businessLogo: '/images/placeholders/business-logo-1.png'
  },
  {
    id: '00000000-0000-0000-0000-000000000003', // ✅ UUID valide
    title: 'Promo Flash Sérum Anti-âge',
    description: 'Offre flash de 48h : -30% sur notre sérum anti-âge aux peptides. Résultats visibles dès 2 semaines.',
    imageUrl: '/images/placeholders/cosmetic-ad-3.jpg',
    targetUrl: 'https://example.com/serum-anti-age',
    businessName: 'Olgane Cosmetics',
    businessLogo: '/images/placeholders/business-logo-1.png',
    discount: 30
  }
];

const DatabaseAdManager: React.FC<DatabaseAdManagerProps> = ({
  placementId,
  maxAds = 5,
  rotationIntervalMs = 60000, // 1 minute par défaut
  fallbackToMockData = true
}) => {
  const [ads, setAds] = useState<FeedAd[]>([]);
  const [currentAdIndex, setCurrentAdIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [usingMockData, setUsingMockData] = useState(false);

  // Charger les publicités depuis la base de données
  const loadAdsFromDatabase = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const databaseAds = await adCampaignService.getActiveAdsForPlacement(placementId, maxAds);
      
      if (databaseAds.length > 0) {
        setAds(databaseAds);
        setUsingMockData(false);
        console.log(`✅ ${databaseAds.length} publicités chargées depuis la base de données pour l'emplacement ${placementId}`);
      } else if (fallbackToMockData) {
        setAds(mockFeedAds);
        setUsingMockData(true);
        console.log(`⚠️ Aucune publicité en base, utilisation des données mockées pour l'emplacement ${placementId}`);
      } else {
        setAds([]);
        setUsingMockData(false);
        console.log(`❌ Aucune publicité disponible pour l'emplacement ${placementId}`);
      }
    } catch (err) {
      console.error('Erreur lors du chargement des publicités:', err);
      setError('Erreur lors du chargement des publicités');
      
      if (fallbackToMockData) {
        setAds(mockFeedAds);
        setUsingMockData(true);
      }
    } finally {
      setLoading(false);
    }
  };

  // Charger les publicités au montage du composant
  useEffect(() => {
    loadAdsFromDatabase();
  }, [placementId, maxAds]);

  // Rotation automatique des publicités
  useEffect(() => {
    if (ads.length <= 1) return;

    const rotationInterval = setInterval(() => {
      setCurrentAdIndex(prevIndex => (prevIndex + 1) % ads.length);
    }, rotationIntervalMs);

    return () => clearInterval(rotationInterval);
  }, [ads.length, rotationIntervalMs]);

  // Enregistrer une impression lorsqu'une publicité est affichée
  const recordAdImpression = async (ad: FeedAd) => {
    try {
      // Pour les données mockées, on ne peut pas enregistrer d'impression
      if (usingMockData) {
        console.log(`📊 Impression simulée pour la publicité mockée: ${ad.id}`);
        return;
      }

      // Enregistrer l'impression en base de données
      await adCampaignService.recordImpression(
        ad.id, // campaign_id
        ad.id, // creative_id (simplifié pour cet exemple)
        placementId
      );
      
      console.log(`📊 Impression enregistrée pour la publicité: ${ad.id}`);
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de l\'impression:', error);
    }
  };

  // Enregistrer l'impression de la publicité actuelle
  useEffect(() => {
    if (ads.length > 0 && !loading) {
      const currentAd = ads[currentAdIndex];
      recordAdImpression(currentAd);
    }
  }, [currentAdIndex, ads, loading]);

  // Gestionnaire de clic sur une publicité
  const handleAdClick = async (ad: FeedAd) => {
    try {
      if (usingMockData) {
        console.log(`🖱️ Clic simulé sur la publicité mockée: ${ad.id}`);
        window.open(ad.targetUrl, '_blank');
        return;
      }

      // Enregistrer le clic en base de données
      // Note: Pour un vrai système, il faudrait récupérer l'impression_id
      const cost = 250; // Coût par clic par défaut
      await adCampaignService.recordClick('dummy-impression-id', ad.id, cost);
      
      console.log(`🖱️ Clic enregistré pour la publicité: ${ad.id}, coût: ${cost} F CFA`);
      
      // Ouvrir le lien cible
      window.open(ad.targetUrl, '_blank');
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du clic:', error);
      // Ouvrir le lien même en cas d'erreur
      window.open(ad.targetUrl, '_blank');
    }
  };

  // Affichage pendant le chargement
  if (loading) {
    return (
      <div className="flex justify-center items-center py-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-sm text-gray-600">Chargement des publicités...</span>
      </div>
    );
  }

  // Affichage en cas d'erreur sans fallback
  if (error && !fallbackToMockData) {
    return (
      <div className="text-center py-4">
        <p className="text-red-600 text-sm">{error}</p>
        <button 
          onClick={loadAdsFromDatabase}
          className="mt-2 text-blue-600 text-sm hover:underline"
        >
          Réessayer
        </button>
      </div>
    );
  }

  // Aucune publicité disponible
  if (ads.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-gray-500 text-sm">Aucune publicité disponible</p>
      </div>
    );
  }

  const currentAd = ads[currentAdIndex];

  return (
    <div className="relative">
      {/* Indicateur de source des données en mode développement */}
      {process.env.NODE_ENV === 'development' && (
        <div className={`absolute top-2 right-2 z-10 px-2 py-1 rounded text-xs font-medium ${
          usingMockData 
            ? 'bg-yellow-100 text-yellow-800' 
            : 'bg-green-100 text-green-800'
        }`}>
          {usingMockData ? '🧪 Mock' : '💾 DB'}
        </div>
      )}

      {/* Affichage de la publicité */}
      <FeedAdCard 
        ad={currentAd}
        onClick={() => handleAdClick(currentAd)}
      />

      {/* Indicateurs de pagination si plusieurs publicités */}
      {ads.length > 1 && (
        <div className="flex justify-center mt-2 space-x-1">
          {ads.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentAdIndex(index)}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentAdIndex 
                  ? 'bg-blue-600' 
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
              aria-label={`Publicité ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Informations de debug en mode développement */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-2 p-2 bg-gray-100 rounded text-xs">
          <div><strong>Emplacement:</strong> {placementId}</div>
          <div><strong>Publicités:</strong> {ads.length}</div>
          <div><strong>Source:</strong> {usingMockData ? 'Données mockées' : 'Base de données'}</div>
          <div><strong>Rotation:</strong> {rotationIntervalMs / 1000}s</div>
        </div>
      )}
    </div>
  );
};

export default DatabaseAdManager;
