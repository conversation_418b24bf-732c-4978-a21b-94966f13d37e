import React, { useState, useEffect } from 'react';
import { IUser } from '../../types';
import { ProfileService } from '../../services/profileService';
import Button from '../ui/Button';
import { X, Upload } from 'lucide-react';
import { DEFAULT_IMAGES } from '../../constants/defaultImages';

interface EditProfileModalProps {
  user: IUser;
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: any) => Promise<void>;
}

const EditProfileModal: React.FC<EditProfileModalProps> = ({
  user,
  isOpen,
  onClose,
  onSave,
}) => {
  const [formData, setFormData] = useState({
    username: user.username,
    city: user.city || '',
    country: user.country || '',
    age: (user as any).age || '',
    gender: (user as any).gender || '',
    bio: (user as any).bio || '',
    website: (user as any).website || '',
    phone: (user as any).phone || '',
    profession: (user as any).profession || '',
    interests: (user as any).interests || '',
    profilePictureFile: null as File | null,
    coverPhotoFile: null as File | null,
  });
  const [loading, setLoading] = useState(false);

  // Effet pour scroll vers le haut quand la modal s'ouvre
  useEffect(() => {
    if (isOpen) {
      // Scroll vers le haut de la page
      window.scrollTo({ top: 0, behavior: 'smooth' });
      // Empêcher le scroll du body quand la modal est ouverte
      document.body.style.overflow = 'hidden';
    } else {
      // Restaurer le scroll du body quand la modal se ferme
      document.body.style.overflow = 'unset';
    }

    // Cleanup function pour restaurer le scroll si le composant se démonte
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Fonctions pour gérer les images par défaut
  const getProfileImageSrc = () => {
    if (formData.profilePictureFile) {
      return URL.createObjectURL(formData.profilePictureFile);
    }
    return user.profilePicture || DEFAULT_IMAGES.AVATAR;
  };

  const getCoverImageSrc = () => {
    if (formData.coverPhotoFile) {
      return URL.createObjectURL(formData.coverPhotoFile);
    }
    return user.coverPhotoUrl || DEFAULT_IMAGES.COVER;
  };

  if (!isOpen) return null;

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, type: 'profile' | 'cover') => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({
        ...prev,
        [type === 'profile' ? 'profilePictureFile' : 'coverPhotoFile']: file,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Valider les données du formulaire
      const validation = ProfileService.validateFormData(formData);
      if (!validation.isValid) {
        alert('Erreurs de validation:\n' + validation.errors.join('\n'));
        setLoading(false);
        return;
      }

      // Nettoyer les centres d'intérêt
      const cleanedFormData = {
        ...formData,
        interests: ProfileService.cleanInterests(formData.interests)
      };

      // Mettre à jour le profil via le service
      const result = await ProfileService.updateProfile(user.id, cleanedFormData);

      if (result.error) {
        alert('Erreur lors de la mise à jour: ' + result.error);
        return;
      }

      // Appeler la fonction de sauvegarde du parent si elle existe
      if (onSave) {
        await onSave(cleanedFormData);
      }

      alert('Profil mis à jour avec succès !');
      onClose();
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil:', error);
      alert('Une erreur inattendue s\'est produite');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4 pt-8 overflow-y-auto">
      <div className="bg-white rounded-xl w-full max-w-4xl max-h-[85vh] overflow-y-auto shadow-2xl my-8">
        {/* Header avec gradient */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 rounded-t-xl">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-3xl font-bold text-white">Éditer le profil</h2>
              <p className="text-blue-100 mt-1">Personnalisez votre profil et vos informations</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-blue-200 transition-colors p-2 hover:bg-white/10 rounded-full"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="p-6">

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Section Images avec design moderne */}
            <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-5 border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                <Upload className="w-6 h-6 mr-3 text-blue-600" />
                Photos du profil
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Photo de profil */}
                <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                  <label className="block text-sm font-semibold text-gray-700 mb-4">
                    Photo de profil
                  </label>
                  <div className="flex flex-col items-center space-y-4">
                    <div className="relative">
                      <img
                        src={getProfileImageSrc()}
                        alt="Profile"
                        className="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg ring-4 ring-blue-100"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = DEFAULT_IMAGES.AVATAR;
                        }}
                      />
                      <div className="absolute bottom-2 right-2 w-8 h-8 bg-green-500 border-3 border-white rounded-full"></div>
                    </div>
                    <label className="cursor-pointer bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-300 shadow-md">
                      <Upload className="w-5 h-5 inline mr-2" />
                      Changer la photo
                      <input
                        type="file"
                        className="hidden"
                        accept="image/*"
                        onChange={(e) => handleFileChange(e, 'profile')}
                      />
                    </label>
                    <p className="text-xs text-gray-500 text-center">
                      JPG, PNG ou GIF. Taille max : 5MB
                    </p>
                  </div>
                </div>

                {/* Photo de couverture */}
                <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                  <label className="block text-sm font-semibold text-gray-700 mb-4">
                    Photo de couverture
                  </label>
                  <div className="flex flex-col items-center space-y-4">
                    <div className="relative">
                      <img
                        src={getCoverImageSrc()}
                        alt="Cover"
                        className="w-48 h-28 rounded-lg object-cover border-2 border-gray-200 shadow-md"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = DEFAULT_IMAGES.COVER;
                        }}
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
                    </div>
                    <label className="cursor-pointer bg-gradient-to-r from-green-500 to-emerald-500 text-white px-6 py-3 rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-300 shadow-md">
                      <Upload className="w-5 h-5 inline mr-2" />
                      Changer la couverture
                      <input
                        type="file"
                        className="hidden"
                        accept="image/*"
                        onChange={(e) => handleFileChange(e, 'cover')}
                      />
                    </label>
                    <p className="text-xs text-gray-500 text-center">
                      Recommandé : 1200x300px. Max : 10MB
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Section Informations personnelles */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200">
              <h3 className="text-xl font-semibold text-gray-800 mb-5 flex items-center">
                <svg className="w-6 h-6 mr-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                </svg>
                Informations personnelles
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Nom d'utilisateur */}
                <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Nom d'utilisateur *
                  </label>
                  <input
                    type="text"
                    value={formData.username}
                    onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    required
                    placeholder="Votre nom d'utilisateur"
                  />
                </div>

                {/* Ville */}
                <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Ville
                  </label>
                  <input
                    type="text"
                    value={formData.city}
                    onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="Votre ville"
                  />
                </div>

                {/* Pays */}
                <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Pays
                  </label>
                  <input
                    type="text"
                    value={formData.country}
                    onChange={(e) => setFormData(prev => ({ ...prev, country: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="Votre pays"
                  />
                </div>

                {/* Âge */}
                <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Âge
                  </label>
                  <input
                    type="number"
                    min="13"
                    max="120"
                    value={formData.age}
                    onChange={(e) => setFormData(prev => ({ ...prev, age: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="Votre âge"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    Information privée pour le ciblage publicitaire
                  </p>
                </div>

                {/* Genre */}
                <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Genre
                  </label>
                  <select
                    value={formData.gender}
                    onChange={(e) => setFormData(prev => ({ ...prev, gender: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  >
                    <option value="">Sélectionnez votre genre</option>
                    <option value="Homme">Homme</option>
                    <option value="Femme">Femme</option>
                    <option value="Autre">Autre</option>
                    <option value="Préfère ne pas dire">Préfère ne pas dire</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-2">
                    Information privée pour le ciblage publicitaire
                  </p>
                </div>

                {/* Email (lecture seule) */}
                <div className="bg-gray-50 rounded-lg p-4 shadow-sm border border-gray-200">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Email
                  </label>
                  <input
                    type="email"
                    value={user.email}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 text-gray-600 cursor-not-allowed"
                    disabled
                    placeholder="<EMAIL>"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    L'email ne peut pas être modifié
                  </p>
                </div>
              </div>
            </div>

            {/* Section Informations supplémentaires */}
            <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl p-5 border border-orange-200">
              <h3 className="text-xl font-semibold text-gray-800 mb-5 flex items-center">
                <svg className="w-6 h-6 mr-3 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                Informations supplémentaires
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Bio */}
                <div className="md:col-span-2 bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Biographie
                  </label>
                  <textarea
                    value={formData.bio}
                    onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none"
                    rows={4}
                    placeholder="Parlez-nous de vous..."
                    maxLength={500}
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    {formData.bio.length}/500 caractères
                  </p>
                </div>

                {/* Site web */}
                <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Site web
                  </label>
                  <input
                    type="url"
                    value={formData.website}
                    onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="https://votre-site.com"
                  />
                </div>

                {/* Téléphone */}
                <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Téléphone
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="+33 1 23 45 67 89"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    Information privée, non visible publiquement
                  </p>
                </div>

                {/* Profession */}
                <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Profession
                  </label>
                  <input
                    type="text"
                    value={formData.profession}
                    onChange={(e) => setFormData(prev => ({ ...prev, profession: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="Votre métier ou profession"
                  />
                </div>

                {/* Centres d'intérêt */}
                <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Centres d'intérêt
                  </label>
                  <input
                    type="text"
                    value={formData.interests}
                    onChange={(e) => setFormData(prev => ({ ...prev, interests: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="Sport, musique, voyage..."
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    Séparez vos intérêts par des virgules
                  </p>
                </div>
              </div>
            </div>

            {/* Section Boutons d'action */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200">
              <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0 sm:space-x-6">
                <div className="text-center sm:text-left">
                  <h4 className="text-lg font-semibold text-gray-800">Prêt à sauvegarder ?</h4>
                  <p className="text-sm text-gray-600">Vos modifications seront appliquées immédiatement</p>
                </div>
                <div className="flex space-x-4">
                  <Button
                    onClick={onClose}
                    variant="outline"
                    size="lg"
                    type="button"
                    className="px-8 py-3 border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-300"
                  >
                    Annuler
                  </Button>
                  <Button
                    variant="primary"
                    size="lg"
                    type="submit"
                    disabled={loading}
                    className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-lg shadow-lg transition-all duration-300 disabled:opacity-50"
                  >
                    {loading ? (
                      <div className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Enregistrement...
                      </div>
                    ) : (
                      'Enregistrer les modifications'
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default EditProfileModal;
