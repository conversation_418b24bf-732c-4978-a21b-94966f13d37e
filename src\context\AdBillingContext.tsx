import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { supabase } from '../lib/supabase';

// Types pour le système de facturation publicitaire
export interface AdWallet {
  id: string;
  businessId: string;
  balance: number; // Solde en F CFA
  totalSpent: number; // Total dépensé
  totalRecharged: number; // Total rechargé
  currency: string;
  status: 'active' | 'suspended' | 'blocked';
  createdAt: Date;
  updatedAt: Date;
}

export interface AdTransaction {
  id: string;
  walletId: string;
  campaignId?: string;
  type: 'recharge' | 'spend' | 'refund' | 'bonus';
  amount: number;
  description: string;
  paymentMethod?: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaymentMethod {
  id: string;
  type: 'mobile_money' | 'bank_card' | 'bank_transfer' | 'crypto';
  provider: string; // Orange Money, MTN Money, Visa, etc.
  accountNumber: string;
  accountName: string;
  isDefault: boolean;
  isActive: boolean;
}

export interface BillingAlert {
  id: string;
  businessId: string;
  type: 'low_balance' | 'campaign_paused' | 'payment_failed' | 'budget_exceeded';
  message: string;
  threshold?: number;
  isRead: boolean;
  createdAt: Date;
}

interface AdBillingContextType {
  // État
  wallet: AdWallet | null;
  transactions: AdTransaction[];
  paymentMethods: PaymentMethod[];
  billingAlerts: BillingAlert[];
  loading: boolean;
  error: string | null;

  // Actions Wallet
  getWallet: () => Promise<AdWallet | null>;
  createWallet: () => Promise<AdWallet>;
  updateWalletBalance: (amount: number, type: 'add' | 'subtract') => Promise<void>;

  // Actions Recharge
  rechargeWallet: (amount: number, paymentMethodId: string) => Promise<void>;
  processPayment: (amount: number, paymentMethod: PaymentMethod) => Promise<boolean>;

  // Actions Méthodes de paiement
  addPaymentMethod: (method: Omit<PaymentMethod, 'id'>) => Promise<void>;
  removePaymentMethod: (methodId: string) => Promise<void>;
  setDefaultPaymentMethod: (methodId: string) => Promise<void>;

  // Actions Facturation
  chargeCampaign: (campaignId: string, amount: number, description: string) => Promise<boolean>;
  refundCampaign: (campaignId: string, amount: number, reason: string) => Promise<void>;

  // Actions Alertes
  createBillingAlert: (type: BillingAlert['type'], message: string, threshold?: number) => Promise<void>;
  markAlertAsRead: (alertId: string) => Promise<void>;
  clearAllAlerts: () => Promise<void>;

  // Utilitaires
  canAffordCampaign: (budgetAmount: number) => boolean;
  getSpendingByPeriod: (startDate: Date, endDate: Date) => Promise<AdTransaction[]>;
  generateInvoice: (campaignId: string) => Promise<any>;
}

const AdBillingContext = createContext<AdBillingContextType | undefined>(undefined);

export const useAdBilling = () => {
  const context = useContext(AdBillingContext);
  if (!context) {
    throw new Error('useAdBilling must be used within an AdBillingProvider');
  }
  return context;
};

export const AdBillingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { currentUser } = useAuth();
  const [wallet, setWallet] = useState<AdWallet | null>(null);
  const [transactions, setTransactions] = useState<AdTransaction[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [billingAlerts, setBillingAlerts] = useState<BillingAlert[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Charger les données au montage
  useEffect(() => {
    if (currentUser && currentUser.role === 'business') {
      loadWalletData();
      loadPaymentMethods();
      loadBillingAlerts();
    }
  }, [currentUser]);

  // Charger les données du wallet
  const loadWalletData = async () => {
    if (!currentUser) return;

    try {
      setLoading(true);
      const wallet = await getWallet();
      if (wallet) {
        await loadTransactions(wallet.id);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur de chargement');
    } finally {
      setLoading(false);
    }
  };

  // Obtenir ou créer le wallet
  const getWallet = async (): Promise<AdWallet | null> => {
    if (!currentUser) return null;

    try {
      // Vérifier si le wallet existe déjà
      const { data: existingWallet, error: fetchError } = await supabase
        .from('ad_wallets')
        .select('*')
        .eq('business_id', currentUser.id)
        .single();

      // Si les tables n'existent pas encore (404), créer un wallet de démonstration
      if (fetchError && fetchError.message.includes('404')) {
        console.warn('Tables de facturation non créées. Utilisation du mode démonstration.');
        const demoWallet: AdWallet = {
          id: `demo-wallet-${currentUser.id}`,
          businessId: currentUser.id,
          balance: 50000, // 50 000 F CFA de démonstration
          totalSpent: 0,
          totalRecharged: 50000,
          currency: 'XOF',
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date()
        };
        setWallet(demoWallet);
        return demoWallet;
      }

      if (existingWallet) {
        const walletData: AdWallet = {
          id: existingWallet.id,
          businessId: existingWallet.business_id,
          balance: existingWallet.balance,
          totalSpent: existingWallet.total_spent,
          totalRecharged: existingWallet.total_recharged,
          currency: existingWallet.currency,
          status: existingWallet.status,
          createdAt: new Date(existingWallet.created_at),
          updatedAt: new Date(existingWallet.updated_at)
        };
        setWallet(walletData);
        return walletData;
      }

      // Créer un nouveau wallet si il n'existe pas
      return await createWallet();
    } catch (err) {
      console.error('Erreur lors de la récupération du wallet:', err);
      return null;
    }
  };

  // Créer un nouveau wallet
  const createWallet = async (): Promise<AdWallet> => {
    if (!currentUser) throw new Error('Utilisateur non connecté');

    try {
      const { data: newWallet, error } = await supabase
        .from('ad_wallets')
        .insert([{
          business_id: currentUser.id,
          balance: 0,
          total_spent: 0,
          total_recharged: 0,
          currency: 'XOF', // Franc CFA
          status: 'active'
        }])
        .select()
        .single();

      if (error) throw error;

      const walletData: AdWallet = {
        id: newWallet.id,
        businessId: newWallet.business_id,
        balance: newWallet.balance,
        totalSpent: newWallet.total_spent,
        totalRecharged: newWallet.total_recharged,
        currency: newWallet.currency,
        status: newWallet.status,
        createdAt: new Date(newWallet.created_at),
        updatedAt: new Date(newWallet.updated_at)
      };

      setWallet(walletData);
      return walletData;
    } catch (err) {
      throw new Error('Erreur lors de la création du wallet');
    }
  };

  // Charger les transactions
  const loadTransactions = async (walletId: string) => {
    try {
      const { data: transactionsData, error } = await supabase
        .from('ad_transactions')
        .select('*')
        .eq('wallet_id', walletId)
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;

      const formattedTransactions: AdTransaction[] = transactionsData?.map(t => ({
        id: t.id,
        walletId: t.wallet_id,
        campaignId: t.campaign_id,
        type: t.type,
        amount: t.amount,
        description: t.description,
        paymentMethod: t.payment_method,
        status: t.status,
        metadata: t.metadata,
        createdAt: new Date(t.created_at),
        updatedAt: new Date(t.updated_at)
      })) || [];

      setTransactions(formattedTransactions);
    } catch (err) {
      console.error('Erreur lors du chargement des transactions:', err);
    }
  };

  // Charger les méthodes de paiement
  const loadPaymentMethods = async () => {
    if (!currentUser) return;

    try {
      const { data: methodsData, error } = await supabase
        .from('payment_methods')
        .select('*')
        .eq('business_id', currentUser.id)
        .eq('is_active', true);

      // Si les tables n'existent pas encore (404), créer des méthodes de démonstration
      if (error && error.message.includes('404')) {
        console.warn('Tables de facturation non créées. Utilisation de méthodes de démonstration.');
        const demoMethods: PaymentMethod[] = [
          {
            id: `demo-method-1-${currentUser.id}`,
            type: 'mobile_money',
            provider: 'Orange Money',
            accountNumber: '+225 07 XX XX XX XX',
            accountName: 'Compte de démonstration',
            isDefault: true,
            isActive: true
          }
        ];
        setPaymentMethods(demoMethods);
        return;
      }

      if (error) throw error;

      const formattedMethods: PaymentMethod[] = methodsData?.map(m => ({
        id: m.id,
        type: m.type,
        provider: m.provider,
        accountNumber: m.account_number,
        accountName: m.account_name,
        isDefault: m.is_default,
        isActive: m.is_active
      })) || [];

      setPaymentMethods(formattedMethods);
    } catch (err) {
      console.error('Erreur lors du chargement des méthodes de paiement:', err);
    }
  };

  // Charger les alertes de facturation
  const loadBillingAlerts = async () => {
    if (!currentUser) return;

    try {
      const { data: alertsData, error } = await supabase
        .from('billing_alerts')
        .select('*')
        .eq('business_id', currentUser.id)
        .eq('is_read', false)
        .order('created_at', { ascending: false });

      // Si les tables n'existent pas encore (404), ignorer silencieusement
      if (error && error.message.includes('404')) {
        console.warn('Tables de facturation non créées. Aucune alerte disponible.');
        setBillingAlerts([]);
        return;
      }

      if (error) throw error;

      const formattedAlerts: BillingAlert[] = alertsData?.map(a => ({
        id: a.id,
        businessId: a.business_id,
        type: a.type,
        message: a.message,
        threshold: a.threshold,
        isRead: a.is_read,
        createdAt: new Date(a.created_at)
      })) || [];

      setBillingAlerts(formattedAlerts);
    } catch (err) {
      console.error('Erreur lors du chargement des alertes:', err);
    }
  };

  // Mettre à jour le solde du wallet
  const updateWalletBalance = async (amount: number, type: 'add' | 'subtract') => {
    if (!wallet) throw new Error('Wallet non trouvé');

    try {
      const newBalance = type === 'add' ? wallet.balance + amount : wallet.balance - amount;

      if (newBalance < 0) {
        throw new Error('Solde insuffisant');
      }

      const { error } = await supabase
        .from('ad_wallets')
        .update({
          balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', wallet.id);

      if (error) throw error;

      setWallet(prev => prev ? { ...prev, balance: newBalance } : null);

      // Vérifier les seuils d'alerte
      await checkBalanceThresholds(newBalance);
    } catch (err) {
      throw new Error('Erreur lors de la mise à jour du solde');
    }
  };

  // Vérifier les seuils d'alerte de solde
  const checkBalanceThresholds = async (currentBalance: number) => {
    const lowBalanceThreshold = 10000; // 10 000 F CFA
    const criticalBalanceThreshold = 5000; // 5 000 F CFA

    if (currentBalance <= criticalBalanceThreshold) {
      await createBillingAlert(
        'low_balance',
        `Solde critique : ${currentBalance.toLocaleString()} F CFA. Rechargez votre compte pour continuer vos campagnes.`,
        criticalBalanceThreshold
      );
    } else if (currentBalance <= lowBalanceThreshold) {
      await createBillingAlert(
        'low_balance',
        `Solde faible : ${currentBalance.toLocaleString()} F CFA. Pensez à recharger votre compte.`,
        lowBalanceThreshold
      );
    }
  };

  // Recharger le wallet
  const rechargeWallet = async (amount: number, paymentMethodId: string) => {
    if (!wallet) throw new Error('Wallet non trouvé');

    const paymentMethod = paymentMethods.find(m => m.id === paymentMethodId);
    if (!paymentMethod) throw new Error('Méthode de paiement non trouvée');

    try {
      setLoading(true);

      // Traiter le paiement
      const paymentSuccess = await processPayment(amount, paymentMethod);
      if (!paymentSuccess) {
        throw new Error('Échec du paiement');
      }

      // Mode démonstration si les tables n'existent pas
      if (wallet.id.startsWith('demo-wallet-')) {
        console.log('Mode démonstration : Simulation de recharge');

        // Créer une transaction de démonstration
        const demoTransaction: AdTransaction = {
          id: `demo-transaction-${Date.now()}`,
          walletId: wallet.id,
          type: 'recharge',
          amount: amount,
          description: `Recharge via ${paymentMethod.provider} (Démonstration)`,
          paymentMethod: paymentMethod.provider,
          status: 'completed',
          metadata: { payment_method_id: paymentMethodId, demo: true },
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Mettre à jour le wallet en mémoire
        const updatedWallet: AdWallet = {
          ...wallet,
          balance: wallet.balance + amount,
          totalRecharged: wallet.totalRecharged + amount,
          updatedAt: new Date()
        };

        setWallet(updatedWallet);
        setTransactions(prev => [demoTransaction, ...prev]);
        return;
      }

      // Mode normal avec base de données
      const { data: transaction, error: transactionError } = await supabase
        .from('ad_transactions')
        .insert([{
          wallet_id: wallet.id,
          type: 'recharge',
          amount: amount,
          description: `Recharge via ${paymentMethod.provider}`,
          payment_method: paymentMethod.provider,
          status: 'completed',
          metadata: { payment_method_id: paymentMethodId }
        }])
        .select()
        .single();

      if (transactionError) throw transactionError;

      // Mettre à jour le solde
      await updateWalletBalance(amount, 'add');

      // Mettre à jour le total rechargé
      const { error: updateError } = await supabase
        .from('ad_wallets')
        .update({
          total_recharged: wallet.totalRecharged + amount
        })
        .eq('id', wallet.id);

      if (updateError) throw updateError;

      // Recharger les données
      await loadWalletData();

    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Erreur lors de la recharge');
    } finally {
      setLoading(false);
    }
  };

  // Traiter un paiement (simulation)
  const processPayment = async (amount: number, paymentMethod: PaymentMethod): Promise<boolean> => {
    // Simulation du traitement de paiement
    // Dans une vraie application, ceci ferait appel à une API de paiement

    try {
      // Simuler un délai de traitement
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simuler un taux de succès de 95%
      const success = Math.random() > 0.05;

      if (!success) {
        await createBillingAlert(
          'payment_failed',
          `Échec du paiement de ${amount.toLocaleString()} F CFA via ${paymentMethod.provider}. Veuillez réessayer.`
        );
        return false;
      }

      return true;
    } catch (err) {
      return false;
    }
  };

  // Facturer une campagne
  const chargeCampaign = async (campaignId: string, amount: number, description: string): Promise<boolean> => {
    if (!wallet) throw new Error('Wallet non trouvé');

    if (wallet.balance < amount) {
      await createBillingAlert(
        'low_balance',
        `Solde insuffisant pour la campagne. Solde actuel : ${wallet.balance.toLocaleString()} F CFA, montant requis : ${amount.toLocaleString()} F CFA.`
      );
      return false;
    }

    try {
      // Mode démonstration si les tables n'existent pas
      if (wallet.id.startsWith('demo-wallet-')) {
        console.log('Mode démonstration : Simulation de facturation');

        // Créer une transaction de démonstration
        const demoTransaction: AdTransaction = {
          id: `demo-transaction-${Date.now()}`,
          walletId: wallet.id,
          campaignId: campaignId,
          type: 'spend',
          amount: amount,
          description: `${description} (Démonstration)`,
          status: 'completed',
          metadata: { demo: true },
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Mettre à jour le wallet en mémoire
        const updatedWallet: AdWallet = {
          ...wallet,
          balance: wallet.balance - amount,
          totalSpent: wallet.totalSpent + amount,
          updatedAt: new Date()
        };

        setWallet(updatedWallet);
        setTransactions(prev => [demoTransaction, ...prev]);
        return true;
      }

      // Mode normal avec base de données
      const { data: transaction, error: transactionError } = await supabase
        .from('ad_transactions')
        .insert([{
          wallet_id: wallet.id,
          campaign_id: campaignId,
          type: 'spend',
          amount: amount,
          description: description,
          status: 'completed'
        }])
        .select()
        .single();

      if (transactionError) throw transactionError;

      // Mettre à jour le solde
      await updateWalletBalance(amount, 'subtract');

      // Mettre à jour le total dépensé
      const { error: updateError } = await supabase
        .from('ad_wallets')
        .update({
          total_spent: wallet.totalSpent + amount
        })
        .eq('id', wallet.id);

      if (updateError) throw updateError;

      return true;
    } catch (err) {
      throw new Error('Erreur lors de la facturation de la campagne');
    }
  };

  // Rembourser une campagne
  const refundCampaign = async (campaignId: string, amount: number, reason: string) => {
    if (!wallet) throw new Error('Wallet non trouvé');

    try {
      // Créer la transaction de remboursement
      const { data: transaction, error: transactionError } = await supabase
        .from('ad_transactions')
        .insert([{
          wallet_id: wallet.id,
          campaign_id: campaignId,
          type: 'refund',
          amount: amount,
          description: `Remboursement: ${reason}`,
          status: 'completed'
        }])
        .select()
        .single();

      if (transactionError) throw transactionError;

      // Mettre à jour le solde
      await updateWalletBalance(amount, 'add');

      // Recharger les données
      await loadWalletData();
    } catch (err) {
      throw new Error('Erreur lors du remboursement');
    }
  };

  // Ajouter une méthode de paiement
  const addPaymentMethod = async (method: Omit<PaymentMethod, 'id'>) => {
    if (!currentUser) throw new Error('Utilisateur non connecté');

    try {
      const { data: newMethod, error } = await supabase
        .from('payment_methods')
        .insert([{
          business_id: currentUser.id,
          type: method.type,
          provider: method.provider,
          account_number: method.accountNumber,
          account_name: method.accountName,
          is_default: method.isDefault,
          is_active: method.isActive
        }])
        .select()
        .single();

      if (error) throw error;

      await loadPaymentMethods();
    } catch (err) {
      throw new Error('Erreur lors de l\'ajout de la méthode de paiement');
    }
  };

  // Supprimer une méthode de paiement
  const removePaymentMethod = async (methodId: string) => {
    try {
      const { error } = await supabase
        .from('payment_methods')
        .update({ is_active: false })
        .eq('id', methodId);

      if (error) throw error;

      await loadPaymentMethods();
    } catch (err) {
      throw new Error('Erreur lors de la suppression de la méthode de paiement');
    }
  };

  // Définir la méthode de paiement par défaut
  const setDefaultPaymentMethod = async (methodId: string) => {
    if (!currentUser) throw new Error('Utilisateur non connecté');

    try {
      // Désactiver toutes les méthodes par défaut
      await supabase
        .from('payment_methods')
        .update({ is_default: false })
        .eq('business_id', currentUser.id);

      // Activer la nouvelle méthode par défaut
      const { error } = await supabase
        .from('payment_methods')
        .update({ is_default: true })
        .eq('id', methodId);

      if (error) throw error;

      await loadPaymentMethods();
    } catch (err) {
      throw new Error('Erreur lors de la définition de la méthode par défaut');
    }
  };

  // Créer une alerte de facturation
  const createBillingAlert = async (type: BillingAlert['type'], message: string, threshold?: number) => {
    if (!currentUser) return;

    try {
      // Mode démonstration si les tables n'existent pas
      if (wallet && wallet.id.startsWith('demo-wallet-')) {
        console.log('Mode démonstration : Création d\'alerte');

        const demoAlert: BillingAlert = {
          id: `demo-alert-${Date.now()}`,
          businessId: currentUser.id,
          type: type,
          message: `${message} (Démonstration)`,
          threshold: threshold,
          isRead: false,
          createdAt: new Date()
        };

        setBillingAlerts(prev => [demoAlert, ...prev]);
        return;
      }

      // Mode normal avec base de données
      const { data: alert, error } = await supabase
        .from('billing_alerts')
        .insert([{
          business_id: currentUser.id,
          type: type,
          message: message,
          threshold: threshold,
          is_read: false
        }])
        .select()
        .single();

      if (error) throw error;

      await loadBillingAlerts();
    } catch (err) {
      console.error('Erreur lors de la création de l\'alerte:', err);
    }
  };

  // Marquer une alerte comme lue
  const markAlertAsRead = async (alertId: string) => {
    try {
      const { error } = await supabase
        .from('billing_alerts')
        .update({ is_read: true })
        .eq('id', alertId);

      if (error) throw error;

      setBillingAlerts(prev => prev.filter(alert => alert.id !== alertId));
    } catch (err) {
      console.error('Erreur lors de la mise à jour de l\'alerte:', err);
    }
  };

  // Effacer toutes les alertes
  const clearAllAlerts = async () => {
    if (!currentUser) return;

    try {
      const { error } = await supabase
        .from('billing_alerts')
        .update({ is_read: true })
        .eq('business_id', currentUser.id);

      if (error) throw error;

      setBillingAlerts([]);
    } catch (err) {
      console.error('Erreur lors de l\'effacement des alertes:', err);
    }
  };

  // Vérifier si l'entreprise peut se permettre une campagne
  const canAffordCampaign = (budgetAmount: number): boolean => {
    return wallet ? wallet.balance >= budgetAmount : false;
  };

  // Obtenir les dépenses par période
  const getSpendingByPeriod = async (startDate: Date, endDate: Date): Promise<AdTransaction[]> => {
    if (!wallet) return [];

    try {
      const { data: transactionsData, error } = await supabase
        .from('ad_transactions')
        .select('*')
        .eq('wallet_id', wallet.id)
        .eq('type', 'spend')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;

      return transactionsData?.map(t => ({
        id: t.id,
        walletId: t.wallet_id,
        campaignId: t.campaign_id,
        type: t.type,
        amount: t.amount,
        description: t.description,
        paymentMethod: t.payment_method,
        status: t.status,
        metadata: t.metadata,
        createdAt: new Date(t.created_at),
        updatedAt: new Date(t.updated_at)
      })) || [];
    } catch (err) {
      console.error('Erreur lors de la récupération des dépenses:', err);
      return [];
    }
  };

  // Générer une facture
  const generateInvoice = async (campaignId: string) => {
    if (!wallet) throw new Error('Wallet non trouvé');

    try {
      const campaignTransactions = transactions.filter(t => t.campaignId === campaignId);

      const invoice = {
        id: `INV-${Date.now()}`,
        campaignId,
        businessId: currentUser?.id,
        transactions: campaignTransactions,
        totalAmount: campaignTransactions.reduce((sum, t) => sum + t.amount, 0),
        currency: wallet.currency,
        generatedAt: new Date(),
        status: 'generated'
      };

      return invoice;
    } catch (err) {
      throw new Error('Erreur lors de la génération de la facture');
    }
  };

  const value: AdBillingContextType = {
    // État
    wallet,
    transactions,
    paymentMethods,
    billingAlerts,
    loading,
    error,

    // Actions Wallet
    getWallet,
    createWallet,
    updateWalletBalance,

    // Actions Recharge
    rechargeWallet,
    processPayment,

    // Actions Méthodes de paiement
    addPaymentMethod,
    removePaymentMethod,
    setDefaultPaymentMethod,

    // Actions Facturation
    chargeCampaign,
    refundCampaign,

    // Actions Alertes
    createBillingAlert,
    markAlertAsRead,
    clearAllAlerts,

    // Utilitaires
    canAffordCampaign,
    getSpendingByPeriod,
    generateInvoice
  };

  return (
    <AdBillingContext.Provider value={value}>
      {children}
    </AdBillingContext.Provider>
  );
};
