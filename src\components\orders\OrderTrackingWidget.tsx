import React, { useState, useEffect } from 'react';
import { 
  Package, 
  Truck, 
  MapPin, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  Bell,
  Copy,
  ExternalLink
} from 'lucide-react';
import { OrderTrackingService, OrderTracking, OrderTrackingStatus } from '../../services/orderTrackingService';
import Button from '../ui/Button';

interface OrderTrackingWidgetProps {
  orderId: string;
  className?: string;
  showNotifications?: boolean;
}

const OrderTrackingWidget: React.FC<OrderTrackingWidgetProps> = ({
  orderId,
  className = '',
  showNotifications = true
}) => {
  const [tracking, setTracking] = useState<OrderTracking | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Charger les données de suivi
  const loadTracking = async () => {
    try {
      setLoading(true);
      setError(null);
      const trackingData = await OrderTrackingService.getOrderTracking(orderId);
      setTracking(trackingData);
    } catch (err) {
      setError('Impossible de charger les informations de suivi');
      console.error('Erreur de chargement du suivi:', err);
    } finally {
      setLoading(false);
    }
  };

  // Actualiser les données
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadTracking();
    setRefreshing(false);
  };

  // Copier le numéro de suivi
  const copyTrackingNumber = () => {
    if (tracking?.tracking_number) {
      navigator.clipboard.writeText(tracking.tracking_number);
      // Ici vous pourriez ajouter une notification toast
    }
  };

  useEffect(() => {
    loadTracking();

    // Actualisation automatique toutes les 30 secondes
    const interval = setInterval(loadTracking, 30000);
    return () => clearInterval(interval);
  }, [orderId]);

  // Fonction pour obtenir l'icône du statut
  const getStatusIcon = (status: OrderTrackingStatus) => {
    switch (status) {
      case OrderTrackingStatus.ORDER_CONFIRMED:
      case OrderTrackingStatus.PAYMENT_CONFIRMED:
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case OrderTrackingStatus.PREPARING:
      case OrderTrackingStatus.READY_FOR_PICKUP:
        return <Package className="w-5 h-5 text-blue-500" />;
      case OrderTrackingStatus.PICKED_UP:
      case OrderTrackingStatus.IN_TRANSIT:
      case OrderTrackingStatus.OUT_FOR_DELIVERY:
        return <Truck className="w-5 h-5 text-orange-500" />;
      case OrderTrackingStatus.DELIVERED:
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case OrderTrackingStatus.DELIVERY_FAILED:
      case OrderTrackingStatus.CANCELLED:
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  // Fonction pour obtenir la couleur du statut
  const getStatusColor = (status: OrderTrackingStatus) => {
    switch (status) {
      case OrderTrackingStatus.DELIVERED:
        return 'text-green-600 bg-green-50 border-green-200';
      case OrderTrackingStatus.IN_TRANSIT:
      case OrderTrackingStatus.OUT_FOR_DELIVERY:
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case OrderTrackingStatus.DELIVERY_FAILED:
      case OrderTrackingStatus.CANCELLED:
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  // Formater la date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !tracking) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="text-center">
          <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
          <p className="text-red-600 mb-4">{error || 'Aucune information de suivi disponible'}</p>
          <Button variant="outline" onClick={handleRefresh} size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Réessayer
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Package className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Suivi de commande</h3>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <span>#{tracking.tracking_number}</span>
                <button
                  onClick={copyTrackingNumber}
                  className="p-1 hover:bg-gray-100 rounded"
                  title="Copier le numéro de suivi"
                >
                  <Copy className="w-3 h-3" />
                </button>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
            {showNotifications && (
              <Button variant="ghost" size="sm">
                <Bell className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Statut actuel */}
      <div className="p-4 border-b border-gray-100">
        <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(tracking.current_status)}`}>
          {getStatusIcon(tracking.current_status)}
          <span className="ml-2">{tracking.events[0]?.title || 'En cours'}</span>
        </div>
        {tracking.estimated_delivery && (
          <div className="mt-2 flex items-center text-sm text-gray-600">
            <Clock className="w-4 h-4 mr-1" />
            <span>Livraison estimée: {formatDate(tracking.estimated_delivery)}</span>
          </div>
        )}
      </div>

      {/* Timeline des événements */}
      <div className="p-4">
        <h4 className="font-medium text-gray-900 mb-3">Historique</h4>
        <div className="space-y-4">
          {tracking.events.map((event, index) => (
            <div key={event.id} className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                {getStatusIcon(event.status as OrderTrackingStatus)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">{event.title}</p>
                  <span className="text-xs text-gray-500">{formatDate(event.created_at)}</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">{event.description}</p>
                {event.location && (
                  <div className="flex items-center mt-1 text-xs text-gray-500">
                    <MapPin className="w-3 h-3 mr-1" />
                    <span>{event.location}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Footer avec actions */}
      <div className="p-4 border-t border-gray-100 bg-gray-50">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Dernière mise à jour: {formatDate(tracking.updated_at)}</span>
          <div className="flex items-center space-x-2">
            <span>Transporteur: {tracking.carrier}</span>
            <ExternalLink className="w-3 h-3" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderTrackingWidget;
