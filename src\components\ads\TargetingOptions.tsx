import React, { useState } from 'react';
import { Target, MapPin, Users, Calendar, Clock, Tag } from 'lucide-react';

interface TargetingOptionsProps {
  onTargetingChange: (targeting: TargetingSettings) => void;
  initialTargeting?: TargetingSettings;
}

export interface TargetingSettings {
  demographics: {
    ageRanges: string[];
    genders: string[];
    interests: string[];
  };
  location: {
    countries: string[];
    cities: string[];
    radius?: number;
  };
  timing: {
    daysOfWeek: string[];
    timeOfDay: string[];
  };
  devices: string[];
  languages: string[];
}

const defaultTargeting: TargetingSettings = {
  demographics: {
    ageRanges: [],
    genders: [],
    interests: []
  },
  location: {
    countries: ['Sénégal'],
    cities: [],
    radius: 0
  },
  timing: {
    daysOfWeek: ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'],
    timeOfDay: ['morning', 'afternoon', 'evening', 'night']
  },
  devices: ['Mobile', 'Desktop', 'Tablet'],
  languages: ['Français']
};

// Options disponibles
const ageRangeOptions = ['13-17', '18-24', '25-34', '35-44', '45-54', '55-64', '65+'];
const genderOptions = ['Hommes', 'Femmes', 'Tous genres'];
const interestOptions = [
  'Mode', 'Beauté', 'Santé', 'Fitness', 'Alimentation', 'Voyages', 'Technologie',
  'Jeux vidéo', 'Musique', 'Cinéma', 'Littérature', 'Art', 'Décoration', 'Bricolage',
  'Jardinage', 'Automobile', 'Finance', 'Éducation', 'Parentalité', 'Sports', 'Cuisine'
];
const countryOptions = ['Sénégal', 'Mali', 'Côte d\'Ivoire', 'Burkina Faso', 'Guinée', 'Mauritanie', 'Gambie'];
const cityOptions = {
  'Sénégal': ['Dakar', 'Thiès', 'Saint-Louis', 'Ziguinchor', 'Kaolack', 'Touba', 'Rufisque', 'Mbour', 'Diourbel'],
  'Mali': ['Bamako', 'Sikasso', 'Mopti', 'Ségou', 'Kayes'],
  'Côte d\'Ivoire': ['Abidjan', 'Bouaké', 'Daloa', 'Yamoussoukro', 'Korhogo'],
  'Burkina Faso': ['Ouagadougou', 'Bobo-Dioulasso', 'Koudougou', 'Banfora'],
  'Guinée': ['Conakry', 'Nzérékoré', 'Kankan', 'Kindia'],
  'Mauritanie': ['Nouakchott', 'Nouadhibou', 'Kiffa', 'Rosso'],
  'Gambie': ['Banjul', 'Serekunda', 'Brikama', 'Bakau']
};
const daysOfWeekOptions = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'];
const timeOfDayOptions = [
  { label: 'Matin (6h-12h)', value: 'morning' },
  { label: 'Après-midi (12h-18h)', value: 'afternoon' },
  { label: 'Soirée (18h-24h)', value: 'evening' },
  { label: 'Nuit (0h-6h)', value: 'night' }
];
const deviceOptions = ['Mobile', 'Desktop', 'Tablet'];
const languageOptions = ['Français', 'Wolof', 'Pulaar', 'Sérère', 'Diola', 'Mandingue', 'Soninké', 'Anglais'];

const TargetingOptions: React.FC<TargetingOptionsProps> = ({
  onTargetingChange,
  initialTargeting = defaultTargeting
}) => {
  const [targeting, setTargeting] = useState<TargetingSettings>(initialTargeting);
  const [activeTab, setActiveTab] = useState<string>('demographics');

  // Gérer les changements dans les options de ciblage démographique
  const handleDemographicChange = (type: 'ageRanges' | 'genders' | 'interests', value: string) => {
    const currentValues = targeting.demographics[type];
    let newValues: string[];

    if (currentValues.includes(value)) {
      newValues = currentValues.filter(v => v !== value);
    } else {
      newValues = [...currentValues, value];
    }

    const newTargeting = {
      ...targeting,
      demographics: {
        ...targeting.demographics,
        [type]: newValues
      }
    };

    setTargeting(newTargeting);
    onTargetingChange(newTargeting);
  };

  // Gérer les changements dans les options de ciblage géographique
  const handleLocationChange = (type: 'countries' | 'cities', value: string) => {
    const currentValues = targeting.location[type];
    let newValues: string[];

    if (currentValues.includes(value)) {
      newValues = currentValues.filter(v => v !== value);
    } else {
      newValues = [...currentValues, value];
    }

    // Si nous changeons de pays, réinitialiser les villes
    const newTargeting = {
      ...targeting,
      location: {
        ...targeting.location,
        [type]: newValues,
        ...(type === 'countries' ? { cities: [] } : {})
      }
    };

    setTargeting(newTargeting);
    onTargetingChange(newTargeting);
  };

  // Gérer les changements dans les options de ciblage temporel
  const handleTimingChange = (type: 'daysOfWeek' | 'timeOfDay', value: string) => {
    const currentValues = targeting.timing[type];
    let newValues: string[];

    if (currentValues.includes(value)) {
      newValues = currentValues.filter(v => v !== value);
    } else {
      newValues = [...currentValues, value];
    }

    const newTargeting = {
      ...targeting,
      timing: {
        ...targeting.timing,
        [type]: newValues
      }
    };

    setTargeting(newTargeting);
    onTargetingChange(newTargeting);
  };

  // Gérer les changements dans les options de ciblage des appareils
  const handleDeviceChange = (value: string) => {
    const currentValues = targeting.devices;
    let newValues: string[];

    if (currentValues.includes(value)) {
      newValues = currentValues.filter(v => v !== value);
    } else {
      newValues = [...currentValues, value];
    }

    const newTargeting = {
      ...targeting,
      devices: newValues
    };

    setTargeting(newTargeting);
    onTargetingChange(newTargeting);
  };

  // Gérer les changements dans les options de ciblage linguistique
  const handleLanguageChange = (value: string) => {
    const currentValues = targeting.languages;
    let newValues: string[];

    if (currentValues.includes(value)) {
      newValues = currentValues.filter(v => v !== value);
    } else {
      newValues = [...currentValues, value];
    }

    const newTargeting = {
      ...targeting,
      languages: newValues
    };

    setTargeting(newTargeting);
    onTargetingChange(newTargeting);
  };

  return (
    <div className="targeting-options">
      <div className="targeting-tabs">
        <button
          className={`targeting-tab ${activeTab === 'demographics' ? 'active' : ''}`}
          onClick={() => setActiveTab('demographics')}
        >
          <Users size={16} />
          <span>Démographie</span>
        </button>
        <button
          className={`targeting-tab ${activeTab === 'location' ? 'active' : ''}`}
          onClick={() => setActiveTab('location')}
        >
          <MapPin size={16} />
          <span>Localisation</span>
        </button>
        <button
          className={`targeting-tab ${activeTab === 'timing' ? 'active' : ''}`}
          onClick={() => setActiveTab('timing')}
        >
          <Clock size={16} />
          <span>Horaires</span>
        </button>
        <button
          className={`targeting-tab ${activeTab === 'other' ? 'active' : ''}`}
          onClick={() => setActiveTab('other')}
        >
          <Target size={16} />
          <span>Autres</span>
        </button>
      </div>

      <div className="targeting-content">
        {activeTab === 'demographics' && (
          <div className="targeting-section">
            <h3 className="targeting-section-title">Âge</h3>
            <div className="targeting-options-grid">
              {ageRangeOptions.map(age => (
                <label key={age} className="targeting-option">
                  <input
                    type="checkbox"
                    checked={targeting.demographics.ageRanges.includes(age)}
                    onChange={() => handleDemographicChange('ageRanges', age)}
                  />
                  <span>{age} ans</span>
                </label>
              ))}
            </div>

            <h3 className="targeting-section-title">Genre</h3>
            <div className="targeting-options-grid">
              {genderOptions.map(gender => (
                <label key={gender} className="targeting-option">
                  <input
                    type="checkbox"
                    checked={targeting.demographics.genders.includes(gender)}
                    onChange={() => handleDemographicChange('genders', gender)}
                  />
                  <span>{gender}</span>
                </label>
              ))}
            </div>

            <h3 className="targeting-section-title">Centres d'intérêt</h3>
            <div className="targeting-options-grid">
              {interestOptions.map(interest => (
                <label key={interest} className="targeting-option">
                  <input
                    type="checkbox"
                    checked={targeting.demographics.interests.includes(interest)}
                    onChange={() => handleDemographicChange('interests', interest)}
                  />
                  <span>{interest}</span>
                </label>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'location' && (
          <div className="targeting-section">
            <h3 className="targeting-section-title">Pays</h3>
            <div className="targeting-options-grid">
              {countryOptions.map(country => (
                <label key={country} className="targeting-option">
                  <input
                    type="checkbox"
                    checked={targeting.location.countries.includes(country)}
                    onChange={() => handleLocationChange('countries', country)}
                  />
                  <span>{country}</span>
                </label>
              ))}
            </div>

            <h3 className="targeting-section-title">Villes</h3>
            <div className="targeting-options-grid">
              {targeting.location.countries.map(country => (
                <React.Fragment key={country}>
                  {cityOptions[country as keyof typeof cityOptions]?.map(city => (
                    <label key={city} className="targeting-option">
                      <input
                        type="checkbox"
                        checked={targeting.location.cities.includes(city)}
                        onChange={() => handleLocationChange('cities', city)}
                      />
                      <span>{city}</span>
                    </label>
                  ))}
                </React.Fragment>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'timing' && (
          <div className="targeting-section">
            <h3 className="targeting-section-title">Jours de la semaine</h3>
            <div className="targeting-options-grid">
              {daysOfWeekOptions.map(day => (
                <label key={day} className="targeting-option">
                  <input
                    type="checkbox"
                    checked={targeting.timing.daysOfWeek.includes(day)}
                    onChange={() => handleTimingChange('daysOfWeek', day)}
                  />
                  <span>{day}</span>
                </label>
              ))}
            </div>

            <h3 className="targeting-section-title">Moment de la journée</h3>
            <div className="targeting-options-grid">
              {timeOfDayOptions.map(time => (
                <label key={time.value} className="targeting-option">
                  <input
                    type="checkbox"
                    checked={targeting.timing.timeOfDay.includes(time.value)}
                    onChange={() => handleTimingChange('timeOfDay', time.value)}
                  />
                  <span>{time.label}</span>
                </label>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'other' && (
          <div className="targeting-section">
            <h3 className="targeting-section-title">Appareils</h3>
            <div className="targeting-options-grid">
              {deviceOptions.map(device => (
                <label key={device} className="targeting-option">
                  <input
                    type="checkbox"
                    checked={targeting.devices.includes(device)}
                    onChange={() => handleDeviceChange(device)}
                  />
                  <span>{device}</span>
                </label>
              ))}
            </div>

            <h3 className="targeting-section-title">Langues</h3>
            <div className="targeting-options-grid">
              {languageOptions.map(language => (
                <label key={language} className="targeting-option">
                  <input
                    type="checkbox"
                    checked={targeting.languages.includes(language)}
                    onChange={() => handleLanguageChange(language)}
                  />
                  <span>{language}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="targeting-summary">
        <h3 className="targeting-summary-title">
          <Target size={16} />
          <span>Résumé du ciblage</span>
        </h3>
        <div className="targeting-summary-content">
          <div className="targeting-summary-item">
            <span className="targeting-summary-label">Démographie:</span>
            <span className="targeting-summary-value">
              {targeting.demographics.ageRanges.length > 0 ? targeting.demographics.ageRanges.join(', ') : 'Tous les âges'} |
              {targeting.demographics.genders.length > 0 ? targeting.demographics.genders.join(', ') : 'Tous genres'}
            </span>
          </div>
          <div className="targeting-summary-item">
            <span className="targeting-summary-label">Centres d'intérêt:</span>
            <span className="targeting-summary-value">
              {targeting.demographics.interests.length > 0 ? targeting.demographics.interests.join(', ') : 'Tous'}
            </span>
          </div>
          <div className="targeting-summary-item">
            <span className="targeting-summary-label">Localisation:</span>
            <span className="targeting-summary-value">
              {targeting.location.countries.join(', ')}
              {targeting.location.cities.length > 0 ? ` (${targeting.location.cities.join(', ')})` : ''}
            </span>
          </div>
          <div className="targeting-summary-item">
            <span className="targeting-summary-label">Horaires:</span>
            <span className="targeting-summary-value">
              {targeting.timing.daysOfWeek.length === 7 ? 'Tous les jours' : targeting.timing.daysOfWeek.join(', ')} |
              {targeting.timing.timeOfDay.length === 4 ? 'Toute la journée' :
                targeting.timing.timeOfDay.map(time =>
                  timeOfDayOptions.find(option => option.value === time)?.label || time
                ).join(', ')
              }
            </span>
          </div>
          <div className="targeting-summary-item">
            <span className="targeting-summary-label">Appareils:</span>
            <span className="targeting-summary-value">
              {targeting.devices.join(', ')}
            </span>
          </div>
          <div className="targeting-summary-item">
            <span className="targeting-summary-label">Langues:</span>
            <span className="targeting-summary-value">
              {targeting.languages.join(', ')}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TargetingOptions;
