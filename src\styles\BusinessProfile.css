/* Styles spécifiques pour le profil entreprise */
.business-header-container {
  width: 100%;
  position: relative;
  margin-bottom: 20px;
}

.business-avatar {
  border: 4px solid #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.business-category {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.business-icon {
  margin-right: 6px;
  color: #1da1f2;
}

.business-founded,
.business-website {
  display: flex;
  align-items: center;
  gap: 5px;
}

.business-contact-info {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 15px;
  margin-top: 20px;
}

.contact-title {
  font-size: 16px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 15px;
  color: #14171a;
}

.contact-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  color: #1da1f2;
  margin-right: 10px;
  flex-shrink: 0;
}

.contact-link {
  color: #1da1f2;
  text-decoration: none;
}

.contact-link:hover {
  text-decoration: underline;
}

/* Styles pour le catalogue de produits */
.product-catalog {
  padding: 10px 0;
}

.product-card {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  background-color: white;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-image-container {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.quality-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.badge-gold {
  background-color: rgba(255, 215, 0, 0.9);
  color: #333;
}

.badge-silver {
  background-color: rgba(192, 192, 192, 0.9);
  color: #333;
}

.badge-bronze {
  background-color: rgba(205, 127, 50, 0.9);
  color: #fff;
}

.product-details {
  padding: 15px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 5px 0;
  color: #14171a;
}

.product-category {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #657786;
  margin-bottom: 10px;
}

.product-description {
  font-size: 14px;
  color: #14171a;
  margin-bottom: 15px;
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 600;
}

.product-price {
  font-weight: 700;
  color: #14171a;
}

.negotiable-tag,
.low-stock-tag {
  display: inline-block;
  margin-top: 8px;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.negotiable-tag {
  background-color: #e8f5fd;
  color: #1da1f2;
}

.low-stock-tag {
  background-color: #ffebee;
  color: #e53935;
  margin-left: 5px;
}

.add-product-button {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #1da1f2;
  color: white;
  border: none;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-product-button:hover {
  background-color: #1991db;
}

.empty-catalog {
  text-align: center;
  padding: 40px 0;
}

/* Styles pour les avis */
.business-reviews {
  padding: 10px 0;
}

.reviews-summary {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  text-align: center;
  border: 1px solid #e1e8ed;
}

.average-rating h3 {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 10px;
  color: #14171a;
}

.rating-value {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stars-container {
  display: flex;
  gap: 2px;
}

.review-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e1e8ed;
}

.review-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.reviewer-info {
  display: flex;
  align-items: center;
}

.reviewer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
  object-fit: cover;
}

.reviewer-name {
  font-weight: 600;
  color: #14171a;
}

.review-date {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #657786;
  margin-top: 2px;
}

.review-type {
  display: flex;
  align-items: flex-start;
}

.review-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.review-badge.positive {
  background-color: #e8f5fd;
  color: #1da1f2;
}

.review-badge.negative {
  background-color: #ffebee;
  color: #e53935;
}

.review-content {
  margin-bottom: 15px;
}

.review-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #14171a;
}

.review-rating {
  display: flex;
  gap: 2px;
  margin-bottom: 10px;
}

.review-description {
  font-size: 14px;
  line-height: 1.5;
  color: #14171a;
  margin-bottom: 15px;
}

.review-images {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  overflow-x: auto;
  padding-bottom: 5px;
}

.review-image {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  object-fit: cover;
}

.review-footer {
  border-top: 1px solid #e1e8ed;
  padding-top: 15px;
  margin-bottom: 15px;
}

.review-stats {
  display: flex;
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #657786;
}

.review-comments {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.comments-title {
  font-size: 14px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 10px;
  color: #14171a;
}

.comment-item {
  padding: 10px 0;
  border-bottom: 1px solid #e1e8ed;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.commenter-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
  object-fit: cover;
}

.commenter-name {
  font-weight: 600;
  font-size: 14px;
  color: #14171a;
}

.comment-date {
  font-size: 12px;
  color: #657786;
  margin-left: 10px;
}

.comment-content {
  font-size: 14px;
  color: #14171a;
  margin: 5px 0;
}

.comment-rating {
  display: flex;
  gap: 2px;
}

.view-more-comments {
  display: block;
  width: 100%;
  padding: 8px;
  background-color: transparent;
  border: 1px solid #e1e8ed;
  border-radius: 4px;
  color: #1da1f2;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.view-more-comments:hover {
  background-color: #f5f8fa;
}

/* Styles pour la section À propos */
.business-about-container {
  padding: 10px 0;
}

.business-description {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e1e8ed;
}

.about-section-title {
  font-size: 18px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 15px;
  color: #14171a;
}

.about-text {
  font-size: 14px;
  line-height: 1.6;
  color: #14171a;
}

.business-category-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e1e8ed;
}

.category-tag {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background-color: #f5f8fa;
  border-radius: 20px;
  font-size: 14px;
  color: #14171a;
}

.category-icon {
  margin-right: 8px;
  color: #1da1f2;
}

.business-stats-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e1e8ed;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  text-align: center;
}

.stat-item {
  padding: 15px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #14171a;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #657786;
}

/* Styles pour les publications liées à l'entreprise */
.business-posts-container {
  padding: 10px 0;
}

.business-posts-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.business-post-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e1e8ed;
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.post-author {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
  object-fit: cover;
}

.author-name {
  font-weight: 600;
  color: #14171a;
}

.post-date {
  font-size: 14px;
  color: #657786;
}

.post-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #14171a;
}

.post-content {
  font-size: 14px;
  line-height: 1.5;
  color: #14171a;
  margin-bottom: 15px;
}

.post-images {
  margin-bottom: 15px;
}

.post-image {
  width: 100%;
  max-height: 300px;
  border-radius: 8px;
  object-fit: cover;
}

.no-posts-message {
  text-align: center;
  padding: 40px 0;
  color: #657786;
}

/* Responsive */
@media (max-width: 992px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .review-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .review-type {
    margin-top: 10px;
  }
}

@media (max-width: 576px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
