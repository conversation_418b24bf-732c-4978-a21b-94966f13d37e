import React, { useMemo, useState, useEffect } from 'react';
import { IPost } from '../../types';
import { Star, ThumbsUp, MessageCircle, Calendar, Bell, AlertCircle, Eye } from 'lucide-react';
import { NotificationType, useNotifications, INotification } from '../../context/NotificationsContext';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import { Link } from 'react-router-dom';

interface BusinessReviewsProps {
  businessName: string;
  posts: IPost[];
}

const BusinessReviews: React.FC<BusinessReviewsProps> = ({ businessName, posts }) => {
  const { notifications, getBusinessNotifications, markAsRead, markAllAsRead } = useNotifications();
  const [activeTab, setActiveTab] = useState<'all' | 'mentions'>('all');
  const [businessNotifications, setBusinessNotifications] = useState<any[]>([]);
  
  // Filtrer les posts concernant cette entreprise
  const businessPosts = useMemo(() => {
    return posts.filter(post => post.businessName === businessName);
  }, [posts, businessName]);
  
  // Récupérer les notifications concernant cette entreprise
  useEffect(() => {
    const notifs = getBusinessNotifications(businessName);
    setBusinessNotifications(notifs);
    
    // Mettre à jour le compteur de notifications non lues
    const unreadCount = notifs.filter(notif => !notif.read).length;
    if (unreadCount > 0) {
      // Mettre en évidence l'onglet des mentions
      const mentionsTab = document.querySelector('[data-tab="mentions"]');
      if (mentionsTab) {
        mentionsTab.classList.add('has-notifications');
      }
    }
  }, [businessName, notifications, getBusinessNotifications]);

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Star 
          key={i} 
          size={16} 
          className={i <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}
        />
      );
    }
    return stars;
  };

  if (businessPosts.length === 0) {
    return (
      <div className="empty-reviews">
        <p className="text-center text-gray-500 my-8">Aucun avis pour le moment.</p>
      </div>
    );
  }

  return (
    <div className="business-reviews">
      {/* Onglets pour naviguer entre tous les avis et les mentions */}
      <div className="flex border-b mb-4">
        <button
          className={`py-2 px-4 font-medium ${activeTab === 'all' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
          onClick={() => setActiveTab('all')}
          data-tab="all"
        >
          Tous les avis
        </button>
        <button
          className={`py-2 px-4 font-medium flex items-center ${activeTab === 'mentions' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
          onClick={() => setActiveTab('mentions')}
          data-tab="mentions"
        >
          Mentions
          {businessNotifications.filter(n => !n.read).length > 0 && (
            <span className="ml-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {businessNotifications.filter(n => !n.read).length}
            </span>
          )}
        </button>
      </div>
      
      {activeTab === 'all' && (
        <>
          <div className="reviews-summary">
            <div className="average-rating">
              <h3>Note moyenne</h3>
              <div className="rating-value">
                {(() => {
                  let totalRating = 0;
                  let ratingCount = 0;
                  
                  businessPosts.forEach(post => {
                    if (post.rating) {
                      totalRating += post.rating;
                      ratingCount++;
                    }
                    
                    post.comments.forEach(comment => {
                      if (comment.hasUsedProduct && comment.rating) {
                        totalRating += comment.rating;
                        ratingCount++;
                      }
                    });
                  });
                  
                  const averageRating = ratingCount > 0 ? (totalRating / ratingCount).toFixed(1) : '0.0';
                  
                  return (
                    <div className="flex items-center">
                      <span className="text-3xl font-bold mr-2">{averageRating}</span>
                      <div className="flex">
                        {renderStars(parseFloat(averageRating))}
                      </div>
                      <span className="text-sm text-gray-500 ml-2">({ratingCount} avis)</span>
                    </div>
                  );
                })()} 
              </div>
            </div>
          </div>
          
          <div className="reviews-list mt-6">
            <h3 className="text-xl font-semibold mb-4">Avis récents</h3>
            {businessPosts.length === 0 ? (
              <div className="empty-reviews bg-gray-50 p-6 text-center rounded-lg">
                <AlertCircle size={40} className="mx-auto mb-3 text-gray-400" />
                <p className="text-gray-500">Aucun avis pour le moment.</p>
                <p className="text-gray-500 text-sm mt-1">Les avis des clients apparaîtront ici lorsqu'ils seront publiés.</p>
              </div>
            ) : (
              businessPosts.map(post => (
                <div key={post.id} className="review-item bg-white p-4 rounded-lg shadow-sm mb-4">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center">
                      <img 
                        src={post.userProfilePicture || 'https://via.placeholder.com/40'} 
                        alt={post.username} 
                        className="w-10 h-10 rounded-full mr-3" 
                      />
                      <div>
                        <h4 className="font-medium">{post.username}</h4>
                        <div className="flex items-center text-sm text-gray-500">
                          <Calendar size={14} className="mr-1" />
                          <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex">
                      {post.rating && renderStars(post.rating)}
                    </div>
                  </div>
                  
                  <div className="mt-3">
                    <p className="text-gray-700">{post.description}</p>
                    
                    {post.images && post.images.length > 0 && (
                      <div className="flex mt-3 space-x-2 overflow-x-auto">
                        {post.images.map((image, index) => (
                          <img 
                            key={index} 
                            src={image} 
                            alt={`Image ${index + 1}`} 
                            className="w-20 h-20 object-cover rounded" 
                          />
                        ))}
                      </div>
                    )}
                    
                    <div className="flex items-center mt-3 text-sm text-gray-500">
                      <div className="flex items-center mr-4">
                        <ThumbsUp size={14} className="mr-1" />
                        <span>{post.likes?.length || 0}</span>
                      </div>
                      <div className="flex items-center">
                        <MessageCircle size={14} className="mr-1" />
                        <span>{post.comments?.length || 0}</span>
                      </div>
                    </div>
                  </div>
                  
                  {post.comments && post.comments.length > 0 && (
                    <div className="mt-4 pl-4 border-l-2 border-gray-200">
                      <h5 className="text-sm font-medium mb-2">Commentaires</h5>
                      {post.comments.map(comment => (
                        <div key={comment.id} className="comment-item mb-3">
                          <div className="flex items-start">
                            <img 
                              src={comment.profilePicture || 'https://via.placeholder.com/30'} 
                              alt={comment.username} 
                              className="w-6 h-6 rounded-full mr-2" 
                            />
                            <div>
                              <div className="flex items-center">
                                <h6 className="text-sm font-medium">{comment.username}</h6>
                                {comment.hasUsedProduct && (
                                  <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Vérifié</span>
                                )}
                              </div>
                              
                              {comment.hasUsedProduct && comment.rating && (
                                <div className="flex mt-1">
                                  {renderStars(comment.rating)}
                                </div>
                              )}
                              
                              <p className="text-sm text-gray-700 mt-1">{comment.content}</p>
                              <span className="text-xs text-gray-500">{new Date(comment.createdAt).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </>
      )}
      
      {activeTab === 'mentions' && (
        <div className="mentions-list">
          <h3 className="text-xl font-semibold mb-4">Mentions de votre entreprise</h3>
          
          {businessNotifications.length === 0 ? (
            <div className="empty-mentions bg-gray-50 p-6 text-center rounded-lg">
              <Bell size={40} className="mx-auto mb-3 text-gray-400" />
              <p className="text-gray-500">Aucune mention pour le moment.</p>
              <p className="text-gray-500 text-sm mt-1">Vous serez notifié ici lorsque des utilisateurs mentionneront votre entreprise.</p>
            </div>
          ) : (
            <div>
              {businessNotifications.filter(n => !n.read).length > 0 && (
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md mb-3">
                  <p className="text-sm text-gray-700">
                    <span className="font-medium">{businessNotifications.filter(n => !n.read).length}</span> {businessNotifications.filter(n => !n.read).length > 1 ? 'nouvelles notifications' : 'nouvelle notification'}
                  </p>
                  <Button 
                    variant="link" 
                    size="sm" 
                    className="text-blue-600"
                    onClick={() => markAllAsRead()}
                  >
                    Tout marquer comme lu
                  </Button>
                </div>
              )}
              
              {businessNotifications.map((notification: INotification) => (
                <Card key={notification.id} className={`mb-3 transition-all ${!notification.read ? 'border-l-4 border-blue-500' : ''}`}>
                  <CardBody>
                    <div className="flex items-start">
                      <div className="mr-3">
                        {notification.type === NotificationType.MENTION ? (
                          <AlertCircle size={20} className="text-blue-500" />
                        ) : notification.type === NotificationType.COMMENT ? (
                          <MessageCircle size={20} className="text-green-500" />
                        ) : notification.type === NotificationType.REVIEW ? (
                          <Star size={20} className="text-purple-500" />
                        ) : (
                          <Bell size={20} className="text-gray-500" />
                        )}
                      </div>
                      <div className="flex-1">
                        {notification.senderProfilePicture && (
                          <div className="flex items-center mb-2">
                            <img 
                              src={notification.senderProfilePicture} 
                              alt={notification.senderName || 'Utilisateur'} 
                              className="w-8 h-8 rounded-full mr-2" 
                            />
                            <span className="font-medium">{notification.senderName}</span>
                            <span className="text-xs text-gray-500 ml-2">
                              {new Date(notification.createdAt).toLocaleDateString()} à {new Date(notification.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                            </span>
                          </div>
                        )}
                        <p className="text-gray-700">{notification.message}</p>
                        <div className="flex justify-end items-center mt-3">
                          {notification.relatedItemId && (
                            <Link 
                              to={`/posts/${notification.relatedItemId}`} 
                              className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                              onClick={() => markAsRead(notification.id)}
                            >
                              <Eye size={16} className="mr-1" />
                              Voir le post
                            </Link>
                          )}
                        </div>
                      </div>
                      {!notification.read && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full ml-2"></div>
                      )}
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BusinessReviews;
