import React from 'react';
import SidebarAdsCarousel from '../components/ads/SidebarAdsCarousel';
import ImageDiagnostic from '../components/ads/ImageDiagnostic';
import Card, { CardBody, CardHeader } from '../components/ui/Card';

const AdsTestPage: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Contenu principal */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <h1 className="text-2xl font-bold text-gray-900">Test des Publicités</h1>
            </CardHeader>
            <CardBody>
              <div className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-3">
                    🎯 Test d'Affichage des Publicités
                  </h2>
                  <p className="text-gray-600 mb-4">
                    Cette page permet de tester l'affichage des publicités dans la barre latérale droite.
                    Les améliorations incluent :
                  </p>
                  <ul className="list-disc list-inside space-y-2 text-gray-600">
                    <li>✅ <strong>Images réelles</strong> depuis Unsplash au lieu de placeholders</li>
                    <li>✅ <strong>Gestion d'erreur</strong> avec images de fallback colorées</li>
                    <li>✅ <strong>Indicateurs de chargement</strong> avec animations</li>
                    <li>✅ <strong>Animations fluides</strong> pour les transitions</li>
                    <li>✅ <strong>Design amélioré</strong> avec gradients et effets hover</li>
                    <li>✅ <strong>Responsive design</strong> optimisé</li>
                  </ul>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-900 mb-2">🔧 Fonctionnalités Testées</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <h4 className="font-medium text-blue-800 mb-1">Images</h4>
                      <ul className="text-blue-700 space-y-1">
                        <li>• Chargement depuis Unsplash</li>
                        <li>• Fallback avec gradients colorés</li>
                        <li>• Lazy loading optimisé</li>
                        <li>• Gestion des erreurs</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-blue-800 mb-1">Interactions</h4>
                      <ul className="text-blue-700 space-y-1">
                        <li>• Navigation avec flèches</li>
                        <li>• Rotation automatique</li>
                        <li>• Effets hover</li>
                        <li>• Animations d'entrée</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="font-semibold text-green-900 mb-2">✨ Améliorations Apportées</h3>
                  <div className="text-sm text-green-700 space-y-2">
                    <p><strong>1. Images réelles :</strong> Remplacement des placeholders par des images Unsplash</p>
                    <p><strong>2. Fallback intelligent :</strong> Gradients colorés basés sur le titre en cas d'erreur</p>
                    <p><strong>3. Indicateurs de chargement :</strong> Spinners animés pendant le chargement</p>
                    <p><strong>4. Animations CSS :</strong> Transitions fluides et effets visuels</p>
                    <p><strong>5. Design moderne :</strong> Ombres, bordures arrondies, et typographie améliorée</p>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h3 className="font-semibold text-yellow-900 mb-2">🧪 Instructions de Test</h3>
                  <ol className="list-decimal list-inside space-y-2 text-yellow-800 text-sm">
                    <li>Regardez la barre latérale droite pour voir les publicités</li>
                    <li>Cliquez sur les flèches pour naviguer entre les annonces</li>
                    <li>Observez les animations de transition</li>
                    <li>Testez les effets hover sur les cartes</li>
                    <li>Vérifiez que les images se chargent correctement</li>
                    <li>Si une image ne se charge pas, un fallback coloré apparaît</li>
                  </ol>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">📊 Métriques</h4>
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex justify-between">
                        <span>Annonces totales :</span>
                        <span className="font-medium">5</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Affichées simultanément :</span>
                        <span className="font-medium">3</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Rotation automatique :</span>
                        <span className="font-medium">5s</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Sources d'images :</span>
                        <span className="font-medium">Unsplash</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">🎨 Styles CSS</h4>
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex justify-between">
                        <span>Line-clamp :</span>
                        <span className="font-medium">✅ Ajouté</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Animations :</span>
                        <span className="font-medium">✅ Fluides</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Gradients :</span>
                        <span className="font-medium">✅ Dynamiques</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Responsive :</span>
                        <span className="font-medium">✅ Optimisé</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-center py-8">
                  <div className="inline-flex items-center space-x-2 bg-green-100 text-green-800 px-4 py-2 rounded-full">
                    <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                    <span className="font-medium">Système publicitaire opérationnel</span>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Barre latérale droite avec publicités */}
        <div className="lg:col-span-1">
          <div className="sticky top-4">
            <SidebarAdsCarousel />
            
            {/* Informations techniques */}
            <Card className="mt-6">
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">🔧 Infos Techniques</h3>
              </CardHeader>
              <CardBody>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Composant :</span>
                    <span className="text-gray-600 ml-2">SidebarAdsCarousel</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Framework :</span>
                    <span className="text-gray-600 ml-2">React + TypeScript</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Styles :</span>
                    <span className="text-gray-600 ml-2">Tailwind CSS</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Images :</span>
                    <span className="text-gray-600 ml-2">Unsplash API</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">État :</span>
                    <span className="text-green-600 ml-2 font-medium">✅ Fonctionnel</span>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Diagnostic des images */}
            <div className="mt-6">
              <ImageDiagnostic />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdsTestPage;
