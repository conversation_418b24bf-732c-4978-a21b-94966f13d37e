.profile-container {
  padding: 20px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.follow-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #1da1f2;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.follow-button.following {
  background-color: #e0245e;
}

.follow-button:hover {
  opacity: 0.9;
}

.followers-section,
.following-section {
  margin-top: 20px;
}

.followers-list,
.following-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.follower-item,
.following-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  background-color: #f7f9fa;
}

.follower-avatar,
.following-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 10px;
}

.follower-username,
.following-username {
  font-weight: 500;
}

/* Styles suggérés pour ProfilePage.tsx */ 
.profile-header { 
  display: flex; 
  align-items: center; 
  margin-bottom: 20px; 
  padding-bottom: 20px; 
  border-bottom: 1px solid #eee; 
} 

.profile-avatar-large { 
  width: 120px; /* Ajustez la taille selon vos besoins */ 
  height: 120px; 
  border-radius: 50%; 
  object-fit: cover; 
  margin-right: 20px; 
  border: 3px solid #fff; 
  box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
} 

.profile-info { 
  flex-grow: 1; 
} 

.profile-info h1 { 
  margin-top: 0; 
  margin-bottom: 5px; 
  font-size: 24px; 
} 

.profile-bio { 
  font-size: 14px; 
  color: #555; 
  margin-bottom: 15px; 
} 

/* Assurez-vous que le bouton de suivi s'intègre bien */ 
.profile-info .follow-button { 
  /* Peut-être ajuster les marges ou la taille ici si nécessaire */ 
}