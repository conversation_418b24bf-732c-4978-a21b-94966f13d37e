-- Migration pour enrichir la table profiles avec les nouveaux champs du formulaire "Éditer le profil"
-- Cette migration ajoute tous les champs nécessaires pour le formulaire développé

-- Ajouter les nouveaux champs à la table profiles
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS bio TEXT,
ADD COLUMN IF NOT EXISTS website TEXT,
ADD COLUMN IF NOT EXISTS phone VARCHAR(20),
ADD COLUMN IF NOT EXISTS profession VARCHAR(100),
ADD COLUMN IF NOT EXISTS interests TEXT;

-- Ajouter des contraintes pour valider les données
-- Contrainte pour la biographie (max 500 caractères)
ALTER TABLE profiles
ADD CONSTRAINT bio_length_check CHECK (bio IS NULL OR char_length(bio) <= 500);

-- Contrainte pour le site web (format URL basique)
ALTER TABLE profiles
ADD CONSTRAINT website_format_check CHECK (
  website IS NULL OR 
  website = '' OR 
  website ~* '^https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(/.*)?$'
);

-- Contrainte pour le téléphone (format international basique)
ALTER TABLE profiles
ADD CONSTRAINT phone_format_check CHECK (
  phone IS NULL OR 
  phone = '' OR 
  phone ~* '^\+?[1-9]\d{1,14}$'
);

-- Contrainte pour la profession (longueur raisonnable)
ALTER TABLE profiles
ADD CONSTRAINT profession_length_check CHECK (
  profession IS NULL OR 
  char_length(profession) <= 100
);

-- Contrainte pour les centres d'intérêt (max 500 caractères)
ALTER TABLE profiles
ADD CONSTRAINT interests_length_check CHECK (
  interests IS NULL OR 
  char_length(interests) <= 500
);

-- Mettre à jour la contrainte gender pour inclure les nouvelles options
ALTER TABLE profiles
DROP CONSTRAINT IF EXISTS gender_check;

ALTER TABLE profiles
ADD CONSTRAINT gender_check CHECK (
  gender IS NULL OR 
  gender IN ('Homme', 'Femme', 'Autre', 'Préfère ne pas dire')
);

-- Ajouter des commentaires pour documenter les nouvelles colonnes
COMMENT ON COLUMN profiles.bio IS 'Biographie de l''utilisateur (max 500 caractères)';
COMMENT ON COLUMN profiles.website IS 'Site web personnel de l''utilisateur (format URL)';
COMMENT ON COLUMN profiles.phone IS 'Numéro de téléphone de l''utilisateur (privé, non visible publiquement)';
COMMENT ON COLUMN profiles.profession IS 'Profession ou métier de l''utilisateur';
COMMENT ON COLUMN profiles.interests IS 'Centres d''intérêt de l''utilisateur séparés par des virgules';

-- Créer des index pour améliorer les performances de recherche
CREATE INDEX IF NOT EXISTS profiles_profession_idx ON profiles(profession) WHERE profession IS NOT NULL;
CREATE INDEX IF NOT EXISTS profiles_city_country_idx ON profiles(city, country) WHERE city IS NOT NULL AND country IS NOT NULL;

-- Créer une fonction pour valider et nettoyer les centres d'intérêt
CREATE OR REPLACE FUNCTION clean_interests(input_interests TEXT)
RETURNS TEXT AS $$
BEGIN
  -- Si l'input est NULL ou vide, retourner NULL
  IF input_interests IS NULL OR trim(input_interests) = '' THEN
    RETURN NULL;
  END IF;
  
  -- Nettoyer les espaces en trop et normaliser les virgules
  RETURN trim(regexp_replace(input_interests, '\s*,\s*', ', ', 'g'));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Créer un trigger pour nettoyer automatiquement les centres d'intérêt
CREATE OR REPLACE FUNCTION trigger_clean_interests()
RETURNS TRIGGER AS $$
BEGIN
  NEW.interests = clean_interests(NEW.interests);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Appliquer le trigger sur les insertions et mises à jour
DROP TRIGGER IF EXISTS clean_interests_trigger ON profiles;
CREATE TRIGGER clean_interests_trigger
  BEFORE INSERT OR UPDATE OF interests ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION trigger_clean_interests();

-- Créer une fonction pour obtenir les statistiques du profil
CREATE OR REPLACE FUNCTION get_profile_completion_stats(profile_id UUID)
RETURNS JSON AS $$
DECLARE
  profile_record RECORD;
  completion_percentage INTEGER;
  missing_fields TEXT[];
BEGIN
  -- Récupérer le profil
  SELECT * INTO profile_record FROM profiles WHERE id = profile_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object('error', 'Profile not found');
  END IF;
  
  -- Initialiser le tableau des champs manquants
  missing_fields := ARRAY[]::TEXT[];
  
  -- Vérifier chaque champ optionnel
  IF profile_record.profile_picture IS NULL OR profile_record.profile_picture = '' THEN
    missing_fields := array_append(missing_fields, 'photo_profil');
  END IF;
  
  IF profile_record.cover_photo_url IS NULL OR profile_record.cover_photo_url = '' THEN
    missing_fields := array_append(missing_fields, 'photo_couverture');
  END IF;
  
  IF profile_record.city IS NULL OR profile_record.city = '' THEN
    missing_fields := array_append(missing_fields, 'ville');
  END IF;
  
  IF profile_record.country IS NULL OR profile_record.country = '' THEN
    missing_fields := array_append(missing_fields, 'pays');
  END IF;
  
  IF profile_record.bio IS NULL OR profile_record.bio = '' THEN
    missing_fields := array_append(missing_fields, 'biographie');
  END IF;
  
  IF profile_record.profession IS NULL OR profile_record.profession = '' THEN
    missing_fields := array_append(missing_fields, 'profession');
  END IF;
  
  IF profile_record.interests IS NULL OR profile_record.interests = '' THEN
    missing_fields := array_append(missing_fields, 'centres_interet');
  END IF;
  
  -- Calculer le pourcentage de complétion (sur 9 champs : username, email + 7 optionnels)
  completion_percentage := ROUND(((9 - array_length(missing_fields, 1)) * 100.0) / 9);
  
  RETURN json_build_object(
    'completion_percentage', completion_percentage,
    'missing_fields', missing_fields,
    'total_fields', 9,
    'completed_fields', 9 - array_length(missing_fields, 1)
  );
END;
$$ LANGUAGE plpgsql STABLE;

-- Créer une vue pour les profils enrichis avec statistiques
CREATE OR REPLACE VIEW profiles_with_stats AS
SELECT 
  p.*,
  get_profile_completion_stats(p.id) as completion_stats,
  CASE 
    WHEN p.bio IS NOT NULL AND p.profession IS NOT NULL AND p.city IS NOT NULL 
    THEN true 
    ELSE false 
  END as is_profile_complete,
  CASE 
    WHEN p.interests IS NOT NULL 
    THEN string_to_array(p.interests, ',') 
    ELSE ARRAY[]::TEXT[] 
  END as interests_array
FROM profiles p;

-- Ajouter des politiques RLS pour les nouveaux champs
-- Les informations personnelles restent visibles publiquement sauf le téléphone
CREATE POLICY "Phone numbers are private" ON profiles
  FOR SELECT
  USING (
    CASE 
      WHEN auth.uid() = id THEN true  -- L'utilisateur peut voir son propre téléphone
      ELSE false  -- Les autres ne peuvent pas voir le téléphone
    END
  );

-- Mettre à jour la politique de mise à jour pour inclure les nouveaux champs
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
CREATE POLICY "Users can update their own profile"
  ON profiles
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Créer une fonction pour mettre à jour le profil avec validation
CREATE OR REPLACE FUNCTION update_user_profile(
  profile_id UUID,
  new_username TEXT DEFAULT NULL,
  new_city TEXT DEFAULT NULL,
  new_country TEXT DEFAULT NULL,
  new_age INTEGER DEFAULT NULL,
  new_gender TEXT DEFAULT NULL,
  new_bio TEXT DEFAULT NULL,
  new_website TEXT DEFAULT NULL,
  new_phone TEXT DEFAULT NULL,
  new_profession TEXT DEFAULT NULL,
  new_interests TEXT DEFAULT NULL,
  new_profile_picture TEXT DEFAULT NULL,
  new_cover_photo_url TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  update_result RECORD;
BEGIN
  -- Vérifier que l'utilisateur peut modifier ce profil
  IF auth.uid() != profile_id THEN
    RETURN json_build_object('error', 'Unauthorized');
  END IF;
  
  -- Effectuer la mise à jour
  UPDATE profiles SET
    username = COALESCE(new_username, username),
    city = COALESCE(new_city, city),
    country = COALESCE(new_country, country),
    age = COALESCE(new_age, age),
    gender = COALESCE(new_gender, gender),
    bio = COALESCE(new_bio, bio),
    website = COALESCE(new_website, website),
    phone = COALESCE(new_phone, phone),
    profession = COALESCE(new_profession, profession),
    interests = COALESCE(new_interests, interests),
    profile_picture = COALESCE(new_profile_picture, profile_picture),
    cover_photo_url = COALESCE(new_cover_photo_url, cover_photo_url),
    updated_at = NOW()
  WHERE id = profile_id
  RETURNING *;
  
  GET DIAGNOSTICS update_result = ROW_COUNT;
  
  IF update_result > 0 THEN
    RETURN json_build_object('success', true, 'message', 'Profile updated successfully');
  ELSE
    RETURN json_build_object('error', 'Profile not found or no changes made');
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Accorder les permissions nécessaires
GRANT EXECUTE ON FUNCTION update_user_profile TO authenticated;
GRANT EXECUTE ON FUNCTION get_profile_completion_stats TO authenticated;
GRANT SELECT ON profiles_with_stats TO authenticated;

-- Nettoyer les données existantes (appliquer la fonction de nettoyage aux intérêts existants)
UPDATE profiles 
SET interests = clean_interests(interests) 
WHERE interests IS NOT NULL AND interests != '';

-- Ajouter un commentaire général sur la migration
COMMENT ON TABLE profiles IS 'Table des profils utilisateurs enrichie avec bio, site web, téléphone, profession et centres d''intérêt';
