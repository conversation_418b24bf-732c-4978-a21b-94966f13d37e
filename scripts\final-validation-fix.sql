-- SCRIPT FINAL DE CORRECTION DE LA VALIDATION
-- Ce script corrige le problème de clé étrangère

-- =====================================================
-- 1. DÉCOUVRIR LES PLANS DISPONIBLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 DÉCOUVERTE DES PLANS DISPONIBLES';
    RAISE NOTICE '==================================';
    RAISE NOTICE '';
END $$;

-- Voir tous les plans disponibles
SELECT 
    '📋 PLANS DISPONIBLES' as section,
    *
FROM subscription_plans
ORDER BY created_at;

-- Voir la structure de subscription_plans
SELECT 
    '🔧 STRUCTURE SUBSCRIPTION_PLANS' as section,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'subscription_plans' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- =====================================================
-- 2. CRÉER LES PLANS MANQUANTS SI NÉCESSAIRE
-- =====================================================

-- Vérifier si le plan 'monthly' existe
DO $$
DECLARE
    plan_exists boolean;
    plan_count integer;
BEGIN
    SELECT EXISTS(
        SELECT 1 FROM subscription_plans WHERE id = 'monthly'
    ) INTO plan_exists;
    
    SELECT COUNT(*) INTO plan_count FROM subscription_plans;
    
    RAISE NOTICE '📊 ÉTAT DES PLANS:';
    RAISE NOTICE '   Total plans: %', plan_count;
    RAISE NOTICE '   Plan monthly existe: %', plan_exists;
    RAISE NOTICE '';
    
    -- Créer le plan monthly s'il n'existe pas
    IF NOT plan_exists THEN
        RAISE NOTICE '🔧 Création du plan monthly...';
        
        INSERT INTO subscription_plans (
            id, name, description, price, is_active, created_at, updated_at
        ) VALUES (
            'monthly',
            'Plan Mensuel',
            'Abonnement mensuel standard',
            25000,
            true,
            NOW(),
            NOW()
        );
        
        RAISE NOTICE '✅ Plan monthly créé avec succès';
    ELSE
        RAISE NOTICE '✅ Plan monthly existe déjà';
    END IF;
    RAISE NOTICE '';
END $$;

-- =====================================================
-- 3. VALIDATION DU CODE
-- =====================================================

-- Valider le code
UPDATE subscription_codes 
SET 
    status = 'validated',
    validated_at = NOW(),
    validated_by = NULL,
    admin_notes = 'Validation manuelle - correction technique',
    updated_at = NOW()
WHERE code = '02500076';

-- Vérifier la validation
SELECT 
    '✅ CODE VALIDÉ' as section,
    code,
    business_name,
    status,
    validated_at,
    admin_notes
FROM subscription_codes 
WHERE code = '02500076';

-- =====================================================
-- 4. CRÉATION D'ABONNEMENT AVEC PLAN VALIDE
-- =====================================================

-- Désactiver les abonnements existants
UPDATE business_subscriptions 
SET is_active = false
WHERE business_id = 'f22bf671-e198-4890-9cd7-029ab3596180' 
AND is_active = true;

-- Créer l'abonnement avec un plan_id valide
DO $$
DECLARE
    valid_plan_id text;
    plan_found boolean := false;
BEGIN
    -- Chercher un plan valide (dans l'ordre de préférence)
    SELECT id INTO valid_plan_id 
    FROM subscription_plans 
    WHERE id IN ('monthly', 'mensuel', 'standard', 'basic') 
    AND is_active = true
    LIMIT 1;
    
    IF valid_plan_id IS NOT NULL THEN
        plan_found := true;
    ELSE
        -- Prendre le premier plan actif disponible
        SELECT id INTO valid_plan_id 
        FROM subscription_plans 
        WHERE is_active = true
        LIMIT 1;
        
        IF valid_plan_id IS NOT NULL THEN
            plan_found := true;
        END IF;
    END IF;
    
    RAISE NOTICE '🎯 PLAN SÉLECTIONNÉ: %', COALESCE(valid_plan_id, 'AUCUN');
    
    IF plan_found THEN
        -- Créer l'abonnement
        INSERT INTO business_subscriptions (
            business_id,
            plan_id,
            is_active,
            start_date,
            end_date,
            created_at,
            updated_at,
            payment_status,
            payment_method,
            stripe_subscription_id
        ) VALUES (
            'f22bf671-e198-4890-9cd7-029ab3596180',
            valid_plan_id,
            true,
            NOW(),
            NOW() + INTERVAL '1 month',
            NOW(),
            NOW(),
            'paid',
            NULL,
            NULL
        );
        
        RAISE NOTICE '✅ Abonnement créé avec le plan: %', valid_plan_id;
    ELSE
        RAISE NOTICE '❌ ERREUR: Aucun plan valide trouvé';
    END IF;
END $$;

-- =====================================================
-- 5. VÉRIFICATION FINALE
-- =====================================================

-- Vérifier le code
SELECT 
    '🔐 CODE FINAL' as section,
    code, business_name, status, validated_at
FROM subscription_codes 
WHERE code = '02500076';

-- Vérifier l'abonnement
SELECT 
    '🏢 ABONNEMENT FINAL' as section,
    bs.*,
    sp.name as plan_name,
    sp.price as plan_price
FROM business_subscriptions bs
LEFT JOIN subscription_plans sp ON bs.plan_id = sp.id
WHERE bs.business_id = 'f22bf671-e198-4890-9cd7-029ab3596180'
AND bs.is_active = true;

-- Statistiques finales
DO $$
DECLARE
    code_validated boolean;
    has_subscription boolean;
    plan_name text;
BEGIN
    SELECT status = 'validated' INTO code_validated
    FROM subscription_codes WHERE code = '02500076';
    
    SELECT EXISTS(
        SELECT 1 FROM business_subscriptions 
        WHERE business_id = 'f22bf671-e198-4890-9cd7-029ab3596180' 
        AND is_active = true
    ) INTO has_subscription;
    
    SELECT sp.name INTO plan_name
    FROM business_subscriptions bs
    JOIN subscription_plans sp ON bs.plan_id = sp.id
    WHERE bs.business_id = 'f22bf671-e198-4890-9cd7-029ab3596180'
    AND bs.is_active = true;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎯 RÉSULTAT FINAL:';
    RAISE NOTICE '==================';
    RAISE NOTICE '✅ Code validé: %', code_validated;
    RAISE NOTICE '🏢 Abonnement actif: %', has_subscription;
    RAISE NOTICE '📋 Plan activé: %', COALESCE(plan_name, 'AUCUN');
    
    IF code_validated AND has_subscription THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 SUCCÈS COMPLET !';
        RAISE NOTICE 'Code validé et abonnement créé avec succès.';
        RAISE NOTICE '';
        RAISE NOTICE '🔄 ACTIONS SUIVANTES:';
        RAISE NOTICE '1. Rechargez l''interface admin';
        RAISE NOTICE '2. Vérifiez que le code apparaît "Validé"';
        RAISE NOTICE '3. Testez l''abonnement côté entreprise';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '⚠️ PROBLÈME DÉTECTÉ';
        RAISE NOTICE 'Vérifiez les erreurs ci-dessus.';
    END IF;
    RAISE NOTICE '';
END $$;
