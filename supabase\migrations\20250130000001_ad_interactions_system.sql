-- Migration pour ajouter le système d'interactions avec les publicités
-- Cette migration ajoute les tables pour tracker les likes, commentaires et partages sur les publicités

-- =====================================================
-- 1. TABLE DES LIKES SUR LES PUBLICITÉS
-- =====================================================
CREATE TABLE IF NOT EXISTS ad_likes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id uuid NOT NULL REFERENCES ad_campaigns(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  
  -- Métadonnées
  created_at TIMESTAMPTZ DEFAULT now(),
  
  -- Contraintes
  UNIQUE(campaign_id, user_id) -- Un utilisateur ne peut liker qu'une fois par campagne
);

-- =====================================================
-- 2. TABLE DES COMMENTAIRES SUR LES PUBLICITÉS
-- =====================================================
CREATE TABLE IF NOT EXISTS ad_comments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id uuid NOT NULL REFERENCES ad_campaigns(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  parent_comment_id uuid REFERENCES ad_comments(id) ON DELETE CASCADE, -- Pour les réponses
  
  -- Contenu
  content TEXT NOT NULL CHECK (length(content) > 0 AND length(content) <= 1000),
  
  -- Modération
  is_approved BOOLEAN DEFAULT true,
  is_flagged BOOLEAN DEFAULT false,
  flagged_reason TEXT,
  
  -- Métadonnées
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- 3. TABLE DES PARTAGES DE PUBLICITÉS
-- =====================================================
CREATE TABLE IF NOT EXISTS ad_shares (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id uuid NOT NULL REFERENCES ad_campaigns(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  
  -- Type de partage
  share_type VARCHAR(20) NOT NULL CHECK (share_type IN ('facebook', 'twitter', 'whatsapp', 'email', 'copy_link', 'internal')),
  
  -- Métadonnées
  shared_at TIMESTAMPTZ DEFAULT now(),
  ip_address INET,
  user_agent TEXT
);

-- =====================================================
-- 4. TABLE DES MÉTRIQUES D'ENGAGEMENT AGRÉGÉES
-- =====================================================
CREATE TABLE IF NOT EXISTS ad_engagement_metrics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id uuid NOT NULL REFERENCES ad_campaigns(id) ON DELETE CASCADE,
  
  -- Compteurs d'engagement
  total_likes INTEGER DEFAULT 0,
  total_comments INTEGER DEFAULT 0,
  total_shares INTEGER DEFAULT 0,
  total_saves INTEGER DEFAULT 0, -- Pour une future fonctionnalité "sauvegarder"
  
  -- Métriques calculées
  engagement_rate DECIMAL(5,4) DEFAULT 0, -- (likes + comments + shares) / impressions * 100
  
  -- Timestamps
  last_updated TIMESTAMPTZ DEFAULT now(),
  
  -- Contrainte
  UNIQUE(campaign_id)
);

-- =====================================================
-- 5. INDEX POUR OPTIMISER LES PERFORMANCES
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_ad_likes_campaign_id ON ad_likes(campaign_id);
CREATE INDEX IF NOT EXISTS idx_ad_likes_user_id ON ad_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_likes_created_at ON ad_likes(created_at);

CREATE INDEX IF NOT EXISTS idx_ad_comments_campaign_id ON ad_comments(campaign_id);
CREATE INDEX IF NOT EXISTS idx_ad_comments_user_id ON ad_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_comments_parent_id ON ad_comments(parent_comment_id);
CREATE INDEX IF NOT EXISTS idx_ad_comments_created_at ON ad_comments(created_at);

CREATE INDEX IF NOT EXISTS idx_ad_shares_campaign_id ON ad_shares(campaign_id);
CREATE INDEX IF NOT EXISTS idx_ad_shares_user_id ON ad_shares(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_shares_type ON ad_shares(share_type);
CREATE INDEX IF NOT EXISTS idx_ad_shares_shared_at ON ad_shares(shared_at);

-- =====================================================
-- 6. POLITIQUES RLS (ROW LEVEL SECURITY)
-- =====================================================

-- Activer RLS sur toutes les nouvelles tables
ALTER TABLE ad_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_engagement_metrics ENABLE ROW LEVEL SECURITY;

-- Politiques pour ad_likes
CREATE POLICY "Users can view all ad likes" ON ad_likes
  FOR SELECT USING (true);

CREATE POLICY "Users can like ads" ON ad_likes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can unlike their own likes" ON ad_likes
  FOR DELETE USING (auth.uid() = user_id);

-- Politiques pour ad_comments
CREATE POLICY "Users can view approved ad comments" ON ad_comments
  FOR SELECT USING (is_approved = true);

CREATE POLICY "Users can add comments on ads" ON ad_comments
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own comments" ON ad_comments
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own comments" ON ad_comments
  FOR DELETE USING (auth.uid() = user_id);

-- Politiques pour ad_shares
CREATE POLICY "Users can view ad shares" ON ad_shares
  FOR SELECT USING (true);

CREATE POLICY "Users can share ads" ON ad_shares
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Politiques pour ad_engagement_metrics (lecture seule pour tous)
CREATE POLICY "Everyone can view ad engagement metrics" ON ad_engagement_metrics
  FOR SELECT USING (true);

-- =====================================================
-- 7. FONCTIONS POUR CALCULER LES MÉTRIQUES D'ENGAGEMENT
-- =====================================================

-- Fonction pour calculer le taux d'engagement
CREATE OR REPLACE FUNCTION calculate_engagement_rate(campaign_id_param uuid)
RETURNS DECIMAL(5,4) AS $$
DECLARE
  total_impressions INTEGER;
  total_engagement INTEGER;
  engagement_rate_result DECIMAL(5,4);
BEGIN
  -- Récupérer le nombre d'impressions
  SELECT impressions INTO total_impressions
  FROM ad_campaigns WHERE id = campaign_id_param;
  
  -- Calculer l'engagement total (likes + commentaires + partages)
  SELECT 
    COALESCE(
      (SELECT COUNT(*) FROM ad_likes WHERE campaign_id = campaign_id_param) +
      (SELECT COUNT(*) FROM ad_comments WHERE campaign_id = campaign_id_param) +
      (SELECT COUNT(*) FROM ad_shares WHERE campaign_id = campaign_id_param),
      0
    ) INTO total_engagement;
  
  -- Calculer le taux d'engagement
  IF total_impressions > 0 THEN
    engagement_rate_result := (total_engagement::DECIMAL / total_impressions::DECIMAL) * 100;
  ELSE
    engagement_rate_result := 0;
  END IF;
  
  RETURN engagement_rate_result;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour mettre à jour les métriques d'engagement
CREATE OR REPLACE FUNCTION update_engagement_metrics(campaign_id_param uuid)
RETURNS void AS $$
DECLARE
  likes_count INTEGER;
  comments_count INTEGER;
  shares_count INTEGER;
  engagement_rate_value DECIMAL(5,4);
BEGIN
  -- Compter les interactions
  SELECT COUNT(*) INTO likes_count FROM ad_likes WHERE campaign_id = campaign_id_param;
  SELECT COUNT(*) INTO comments_count FROM ad_comments WHERE campaign_id = campaign_id_param AND is_approved = true;
  SELECT COUNT(*) INTO shares_count FROM ad_shares WHERE campaign_id = campaign_id_param;
  
  -- Calculer le taux d'engagement
  engagement_rate_value := calculate_engagement_rate(campaign_id_param);
  
  -- Insérer ou mettre à jour les métriques
  INSERT INTO ad_engagement_metrics (
    campaign_id, 
    total_likes, 
    total_comments, 
    total_shares, 
    engagement_rate,
    last_updated
  ) VALUES (
    campaign_id_param,
    likes_count,
    comments_count,
    shares_count,
    engagement_rate_value,
    now()
  )
  ON CONFLICT (campaign_id) 
  DO UPDATE SET
    total_likes = EXCLUDED.total_likes,
    total_comments = EXCLUDED.total_comments,
    total_shares = EXCLUDED.total_shares,
    engagement_rate = EXCLUDED.engagement_rate,
    last_updated = EXCLUDED.last_updated;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 8. TRIGGERS POUR METTRE À JOUR AUTOMATIQUEMENT LES MÉTRIQUES
-- =====================================================

-- Trigger pour mettre à jour les métriques après un like
CREATE OR REPLACE FUNCTION trigger_update_engagement_on_like()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM update_engagement_metrics(NEW.campaign_id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_engagement_on_like_insert
  AFTER INSERT ON ad_likes
  FOR EACH ROW EXECUTE FUNCTION trigger_update_engagement_on_like();

CREATE TRIGGER update_engagement_on_like_delete
  AFTER DELETE ON ad_likes
  FOR EACH ROW EXECUTE FUNCTION trigger_update_engagement_on_like();

-- Trigger pour mettre à jour les métriques après un commentaire
CREATE OR REPLACE FUNCTION trigger_update_engagement_on_comment()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    PERFORM update_engagement_metrics(NEW.campaign_id);
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    PERFORM update_engagement_metrics(OLD.campaign_id);
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_engagement_on_comment_insert
  AFTER INSERT ON ad_comments
  FOR EACH ROW EXECUTE FUNCTION trigger_update_engagement_on_comment();

CREATE TRIGGER update_engagement_on_comment_update
  AFTER UPDATE ON ad_comments
  FOR EACH ROW EXECUTE FUNCTION trigger_update_engagement_on_comment();

CREATE TRIGGER update_engagement_on_comment_delete
  AFTER DELETE ON ad_comments
  FOR EACH ROW EXECUTE FUNCTION trigger_update_engagement_on_comment();

-- Trigger pour mettre à jour les métriques après un partage
CREATE OR REPLACE FUNCTION trigger_update_engagement_on_share()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM update_engagement_metrics(NEW.campaign_id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_engagement_on_share_insert
  AFTER INSERT ON ad_shares
  FOR EACH ROW EXECUTE FUNCTION trigger_update_engagement_on_share();

-- =====================================================
-- 9. VUES POUR FACILITER LES REQUÊTES
-- =====================================================

-- Vue pour les statistiques complètes des campagnes avec engagement
CREATE OR REPLACE VIEW ad_campaigns_with_engagement AS
SELECT
  c.*,
  COALESCE(em.total_likes, 0) as total_likes,
  COALESCE(em.total_comments, 0) as total_comments,
  COALESCE(em.total_shares, 0) as total_shares,
  COALESCE(em.engagement_rate, 0) as engagement_rate,
  (COALESCE(em.total_likes, 0) + COALESCE(em.total_comments, 0) + COALESCE(em.total_shares, 0)) as total_engagement
FROM ad_campaigns c
LEFT JOIN ad_engagement_metrics em ON c.id = em.campaign_id;

-- Vue pour les commentaires avec informations utilisateur
CREATE OR REPLACE VIEW ad_comments_with_user AS
SELECT
  ac.*,
  p.username,
  p.profile_picture,
  p.role as user_role
FROM ad_comments ac
JOIN profiles p ON ac.user_id = p.id
WHERE ac.is_approved = true
ORDER BY ac.created_at DESC;

-- =====================================================
-- 10. COMMENTAIRES POUR DOCUMENTATION
-- =====================================================
COMMENT ON TABLE ad_likes IS 'Likes des utilisateurs sur les publicités';
COMMENT ON TABLE ad_comments IS 'Commentaires des utilisateurs sur les publicités';
COMMENT ON TABLE ad_shares IS 'Partages des publicités par les utilisateurs';
COMMENT ON TABLE ad_engagement_metrics IS 'Métriques d''engagement agrégées par campagne';

COMMENT ON COLUMN ad_comments.content IS 'Contenu du commentaire (max 1000 caractères)';
COMMENT ON COLUMN ad_comments.is_approved IS 'Commentaire approuvé par la modération';
COMMENT ON COLUMN ad_shares.share_type IS 'Type de partage (facebook, twitter, whatsapp, etc.)';
COMMENT ON COLUMN ad_engagement_metrics.engagement_rate IS 'Taux d''engagement en pourcentage';
