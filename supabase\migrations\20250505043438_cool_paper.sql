/*
  # Marketplace Schema

  1. New Tables
    - `products`
      - `id` (uuid, primary key)
      - `name` (text)
      - `description` (text)
      - `price` (numeric)
      - `business_id` (uuid, references users)
      - `category` (text)
      - `images` (text[])
      - `average_rating` (numeric)
      - `quality_badge` (text)
      - `negotiable` (boolean)
      - `stock` (integer)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

    - `negotiations`
      - `id` (uuid, primary key)
      - `product_id` (uuid, references products)
      - `buyer_id` (uuid, references users)
      - `seller_id` (uuid, references users)
      - `initial_price` (numeric)
      - `current_offer` (numeric)
      - `status` (text)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

    - `negotiation_messages`
      - `id` (uuid, primary key)
      - `negotiation_id` (uuid, references negotiations)
      - `sender_id` (uuid, references users)
      - `content` (text)
      - `offer` (numeric)
      - `created_at` (timestamptz)

    - `orders`
      - `id` (uuid, primary key)
      - `buyer_id` (uuid, references users)
      - `seller_id` (uuid, references users)
      - `product_id` (uuid, references products)
      - `quantity` (integer)
      - `total_price` (numeric)
      - `status` (text)
      - `negotiation_id` (uuid, references negotiations)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
*/

-- Products table
CREATE TABLE IF NOT EXISTS products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  price numeric NOT NULL CHECK (price >= 0),
  business_id uuid REFERENCES auth.users(id) NOT NULL,
  category text NOT NULL,
  images text[] DEFAULT ARRAY[]::text[],
  average_rating numeric DEFAULT 0 CHECK (average_rating >= 0 AND average_rating <= 5),
  quality_badge text DEFAULT 'none',
  negotiable boolean DEFAULT false,
  stock integer DEFAULT 0 CHECK (stock >= 0),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Products policies
CREATE POLICY "Anyone can view products"
  ON products
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Businesses can create their own products"
  ON products
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = business_id);

CREATE POLICY "Businesses can update their own products"
  ON products
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = business_id)
  WITH CHECK (auth.uid() = business_id);

-- Negotiations table
CREATE TABLE IF NOT EXISTS negotiations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid REFERENCES products(id) NOT NULL,
  buyer_id uuid REFERENCES auth.users(id) NOT NULL,
  seller_id uuid REFERENCES auth.users(id) NOT NULL,
  initial_price numeric NOT NULL CHECK (initial_price >= 0),
  current_offer numeric NOT NULL CHECK (current_offer >= 0),
  status text NOT NULL DEFAULT 'pending',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE negotiations ENABLE ROW LEVEL SECURITY;

-- Negotiations policies
CREATE POLICY "Users can view their own negotiations"
  ON negotiations
  FOR SELECT
  TO authenticated
  USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

CREATE POLICY "Buyers can create negotiations"
  ON negotiations
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = buyer_id);

CREATE POLICY "Involved parties can update negotiations"
  ON negotiations
  FOR UPDATE
  TO authenticated
  USING (auth.uid() IN (buyer_id, seller_id))
  WITH CHECK (auth.uid() IN (buyer_id, seller_id));

-- Negotiation messages table
CREATE TABLE IF NOT EXISTS negotiation_messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  negotiation_id uuid REFERENCES negotiations(id) NOT NULL,
  sender_id uuid REFERENCES auth.users(id) NOT NULL,
  content text NOT NULL,
  offer numeric,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE negotiation_messages ENABLE ROW LEVEL SECURITY;

-- Negotiation messages policies
CREATE POLICY "Users can view messages from their negotiations"
  ON negotiation_messages
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM negotiations n
      WHERE n.id = negotiation_id
      AND (auth.uid() = n.buyer_id OR auth.uid() = n.seller_id)
    )
  );

CREATE POLICY "Users can create messages for their negotiations"
  ON negotiation_messages
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM negotiations n
      WHERE n.id = negotiation_id
      AND (auth.uid() = n.buyer_id OR auth.uid() = n.seller_id)
    )
  );

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  buyer_id uuid REFERENCES auth.users(id) NOT NULL,
  seller_id uuid REFERENCES auth.users(id) NOT NULL,
  product_id uuid REFERENCES products(id) NOT NULL,
  quantity integer NOT NULL CHECK (quantity > 0),
  total_price numeric NOT NULL CHECK (total_price >= 0),
  status text NOT NULL DEFAULT 'pending',
  negotiation_id uuid REFERENCES negotiations(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE orders ENABLE ROW LEVEL SECURITY;

-- Orders policies
CREATE POLICY "Users can view their own orders"
  ON orders
  FOR SELECT
  TO authenticated
  USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

CREATE POLICY "Buyers can create orders"
  ON orders
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = buyer_id);

CREATE POLICY "Involved parties can update orders"
  ON orders
  FOR UPDATE
  TO authenticated
  USING (auth.uid() IN (buyer_id, seller_id))
  WITH CHECK (auth.uid() IN (buyer_id, seller_id));

-- Triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_products_updated_at
  BEFORE UPDATE ON products
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_negotiations_updated_at
  BEFORE UPDATE ON negotiations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at
  BEFORE UPDATE ON orders
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();