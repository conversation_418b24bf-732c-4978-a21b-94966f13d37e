import React, { useState } from 'react';
import { Home, ThumbsUp, HelpCircle, Bell, MessageSquare, Search, Menu, X, Per<PERSON>, Brain, CreditCard, UserPlus } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useFollowRequests } from '../../context/FollowRequestContext';
import Button from '../ui/Button';
import Avatar from '../ui/Avatar';
import UserDropdownMenu from '../ui/UserDropdownMenu'; // Import du nouveau menu déroulant
import MarketplaceDropdownMenu from '../ui/MarketplaceDropdownMenu'; // Import du menu marketplace
import { UserRole } from '../../types'; // Ajout de l'import pour UserRole
import NotificationsDropdown from '../notifications/NotificationsDropdown';
import MessagesDropdown from '../messages/MessagesDropdown';
import FollowRequestsModal from '../follow/FollowRequestsModal';

interface NavbarProps {
  toggleSidebar: () => void;
  sidebarOpen: boolean;
  onProfileClick: () => void;
  onHomeClick: () => void;
  onMarketplaceClick: () => void;
  onRecommendationsClick: () => void;
  onSupportClick: () => void;
  onSearchSubmit: (query: string) => void; // Added
  onOffersAndPromotionsClick?: () => void; // Ajout du lien vers les offres et promotions
  onSettingsClick?: () => void; // Ajout pour les paramètres
  onOrdersClick?: () => void; // Ajout pour les commandes
  onMyRecommendationsClick?: () => void; // Ajout pour les recommandations
  onBusinessProductsClick?: () => void; // Ajout pour les produits business
  onBusinessSalesClick?: () => void; // Ajout pour les ventes business
  onNegotiationsClick?: () => void; // Ajout pour les négociations
  onTopProductsClick?: () => void; // Ajout pour les top produits
  onTrendingClick?: () => void; // Ajout pour les tendances
  onAIRecommendationsClick?: () => void; // Ajout pour les recommandations IA
  onSubscriptionClick?: () => void; // Ajout pour l'abonnement (entreprises uniquement)
  onAdminClick?: () => void; // Ajout pour l'administration
  currentPage: 'home' | 'profile' | 'marketplace' | 'recommendations' | 'support' | 'my-recommendations' | 'top-products' | 'trending' | 'businesses' | 'search-results' | 'ai-recommendations' | 'business-orders' | 'business-sales' | 'business-rankings' | 'business-reviews' | 'business-ads' | 'offers-and-promotions' | 'negotiations' | 'subscription'; // Ajout de toutes les pages
}

const Navbar: React.FC<NavbarProps> = ({
  toggleSidebar,
  sidebarOpen,
  onProfileClick,
  onHomeClick,
  onMarketplaceClick,
  onRecommendationsClick,
  onSupportClick,
  onSearchSubmit,
  onOffersAndPromotionsClick,
  onSettingsClick,
  onOrdersClick,
  onMyRecommendationsClick,
  onBusinessProductsClick,
  onBusinessSalesClick,
  onNegotiationsClick,
  onTopProductsClick,
  onTrendingClick,
  onAIRecommendationsClick,
  onSubscriptionClick,
  onAdminClick,
  currentPage
}) => {
  const { currentUser, logout, isAuthenticated } = useAuth();
  const { pendingRequestsCount } = useFollowRequests();
  const [searchQuery, setSearchQuery] = React.useState(''); // This state is local to Navbar for input control
  const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false);
  const [followRequestsModalOpen, setFollowRequestsModalOpen] = useState(false);

  // Vérifier si l'utilisateur est une entreprise
  const isBusinessUser = currentUser?.role === UserRole.BUSINESS;

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">

          {/* Logo and Mobile Menu Button */}
          <div className="flex items-center">
            <button
              className="md:hidden p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
              onClick={toggleSidebar}
            >
              {sidebarOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
            <div className="flex-shrink-0 flex items-center">
              <span className="text-2xl font-bold text-blue-600" onClick={onHomeClick} style={{ cursor: 'pointer' }}>
                Customeroom
              </span>
            </div>
          </div>

          {/* Search Bar - Hidden on mobile */}
          <div className="hidden md:block flex-1 max-w-md mx-4">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-full
                          bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500
                          focus:border-blue-500 sm:text-sm"
                placeholder="Rechercher produits, entreprises, catégories, tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && searchQuery.trim()) {
                    onSearchSubmit(searchQuery.trim());
                  }
                }}
              />
            </div>
          </div>

          {/* Navigation Icons */}
          <div className="hidden md:flex items-center space-x-2">
            {isAuthenticated ? (
              <>
                <NotificationsDropdown />

                {/* Bouton Demandes de Suivi */}
                <div className="relative">
                  <button
                    className="relative p-2 rounded-full text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                    onClick={() => setFollowRequestsModalOpen(true)}
                    aria-label="Demandes de suivi"
                  >
                    <UserPlus size={24} className={pendingRequestsCount > 0 ? 'text-blue-600' : 'text-gray-600'} />

                    {pendingRequestsCount > 0 && (
                      <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                        {pendingRequestsCount > 99 ? '99+' : pendingRequestsCount}
                      </span>
                    )}
                  </button>
                </div>

                <MessagesDropdown />
                <div className="ml-3 relative">
                  <UserDropdownMenu
                    onProfileClick={onProfileClick}
                    onSettingsClick={onSettingsClick || (() => {})}
                    onOrdersClick={onOrdersClick}
                    onMyRecommendationsClick={onMyRecommendationsClick}
                    onBusinessProductsClick={onBusinessProductsClick}
                    onAdminClick={onAdminClick}
                  />
                </div>
              </>
            ) : (
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">Se connecter</Button>
                <Button size="sm">S'inscrire</Button>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center">
            {isAuthenticated ? (
              <button onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
                <Avatar
                  src={currentUser?.profilePicture || "https://images.pexels.com/photos/2726111/pexels-photo-2726111.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"}
                  alt={currentUser?.role === UserRole.BUSINESS ? (currentUser as any).businessName : currentUser?.username || "User"}
                  size="sm"
                />
              </button>
            ) : (
              <Button size="sm">Se connecter</Button>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-white border-t border-gray-200 py-2">
          <div className="px-4 pt-2 pb-3 space-y-1">
            <div className="flex items-center p-2 rounded-md">
              <Search className="h-5 w-5 text-gray-400 mr-3" />
              <input
                type="text"
                className="block w-full border-none focus:outline-none focus:ring-0 text-sm"
                placeholder="Rechercher..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <button className="flex items-center p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 w-full">
              <Bell className="h-5 w-5 mr-3" />
              <span>Notifications</span>
            </button>
            <button className="flex items-center p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 w-full">
              <MessageSquare className="h-5 w-5 mr-3" />
              <span>Messages</span>
            </button>
            {isAuthenticated && (
              <>
                <button
                  className="flex items-center p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 w-full"
                  onClick={() => {
                    onProfileClick();
                    setMobileMenuOpen(false); // Close menu after click
                  }}
                >
                  {/* You might want to add a User icon here */}
                  <span>Profil</span>
                </button>
                <button
                  className="flex items-center p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 w-full"
                  onClick={() => {
                    logout();
                    setMobileMenuOpen(false); // Close menu after click
                  }}
                >
                  <span>Se déconnecter</span>
                </button>
              </>
            )}
          </div>
        </div>
      )}

      {/* Main Navigation */}
      <div className="hidden md:block border-t border-gray-200">
        <div className="container mx-auto px-4">
          <nav className="flex space-x-8">
            <button
              onClick={onHomeClick}
              className={`px-3 py-2 text-sm font-medium border-b-2 ${currentPage === 'home' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
            >
              <Home className="h-5 w-5 inline-block mr-1" />
              Accueil
            </button>
            <button
              onClick={onRecommendationsClick}
              className={`px-3 py-2 text-sm font-medium border-b-2 ${currentPage === 'recommendations' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
            >
              <ThumbsUp className="h-5 w-5 inline-block mr-1" />
              Recommandations
            </button>
            {onAIRecommendationsClick && !isBusinessUser && (
              <button
                onClick={onAIRecommendationsClick}
                className={`px-3 py-2 text-sm font-medium border-b-2 ${currentPage === 'ai-recommendations' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
              >
                <Brain className="h-5 w-5 inline-block mr-1" />
                Recommandation IA
              </button>
            )}
            <div className="relative">
              <MarketplaceDropdownMenu
                onMarketplaceClick={onMarketplaceClick}
                onTopProductsClick={onTopProductsClick}
                onTrendingClick={onTrendingClick}
                onOffersClick={onOffersAndPromotionsClick}
                onNegotiationsClick={onNegotiationsClick}
                onMyOrdersClick={onOrdersClick}
                onBusinessProductsClick={onBusinessProductsClick}
                onBusinessSalesClick={onBusinessSalesClick}
              />
            </div>
            {onOffersAndPromotionsClick && (
              <button
                onClick={onOffersAndPromotionsClick}
                className={`px-3 py-2 text-sm font-medium border-b-2 ${currentPage === 'offers-and-promotions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
              >
                <Percent className="h-5 w-5 inline-block mr-1" />
                Offres et promotions
              </button>
            )}

            <button
              onClick={onSupportClick}
              className={`px-3 py-2 text-sm font-medium border-b-2 ${currentPage === 'support' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
            >
              <HelpCircle className="h-5 w-5 inline-block mr-1" />
              Support & Aide
            </button>
          </nav>
        </div>
      </div>

      {/* Modal des demandes de suivi */}
      <FollowRequestsModal
        isOpen={followRequestsModalOpen}
        onClose={() => setFollowRequestsModalOpen(false)}
      />


    </header>
  );
};

export default Navbar;
