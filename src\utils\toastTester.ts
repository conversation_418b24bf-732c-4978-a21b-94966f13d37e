/**
 * Utilitaire pour tester le système Toast depuis la console du navigateur
 */

// Fonction pour tester tous les types de toast
export const testAllToasts = () => {
  console.log('🧪 Test de tous les types de toast...');
  
  // Simuler l'utilisation du hook useToast
  try {
    // Note: Cette fonction doit être appelée depuis un composant React
    // ou après que l'application soit montée
    console.log('✅ Pour tester les toasts, naviguez vers /toast-test');
    console.log('🔗 Ou utilisez les fonctions testToastSuccess(), testToastError(), etc.');
  } catch (error) {
    console.error('❌ Erreur lors du test des toasts:', error);
  }
};

// Fonctions individuelles pour tester chaque type
export const testToastSuccess = () => {
  console.log('🟢 Test toast succès - Naviguez vers /toast-test pour voir le résultat');
};

export const testToastError = () => {
  console.log('🔴 Test toast erreur - Naviguez vers /toast-test pour voir le résultat');
};

export const testToastInfo = () => {
  console.log('🔵 Test toast info - Naviguez vers /toast-test pour voir le résultat');
};

export const testToastWarning = () => {
  console.log('🟡 Test toast warning - Naviguez vers /toast-test pour voir le résultat');
};

// Fonction pour vérifier le statut du ToastProvider
export const checkToastProviderStatus = () => {
  console.log('🔍 Vérification du statut du ToastProvider...');
  
  // Vérifier si React est disponible
  if (typeof window !== 'undefined' && (window as any).React) {
    console.log('✅ React détecté');
  } else {
    console.log('⚠️ React non détecté dans window');
  }
  
  // Instructions pour l'utilisateur
  console.log('📋 Instructions de test:');
  console.log('1. Naviguez vers /toast-test dans l\'application');
  console.log('2. Utilisez les boutons de test sur la page');
  console.log('3. Vérifiez que les toasts apparaissent en haut à droite');
  console.log('4. Testez la fermeture automatique et manuelle');
};

// Fonction pour diagnostiquer les problèmes de toast
export const diagnoseToastIssues = () => {
  console.log('🔧 Diagnostic des problèmes de toast...');
  
  const issues = [];
  const solutions = [];
  
  // Vérifier la présence de React
  if (typeof window === 'undefined' || !(window as any).React) {
    issues.push('React non disponible dans le contexte global');
    solutions.push('Assurez-vous que React est chargé');
  }
  
  // Vérifier la structure DOM
  const rootElement = document.getElementById('root');
  if (!rootElement) {
    issues.push('Élément root non trouvé');
    solutions.push('Vérifiez que l\'application React est montée');
  }
  
  // Afficher les résultats
  if (issues.length === 0) {
    console.log('✅ Aucun problème détecté');
    console.log('💡 Si les toasts ne fonctionnent pas:');
    console.log('   1. Vérifiez que ToastProvider est dans App.tsx');
    console.log('   2. Vérifiez l\'ordre des providers');
    console.log('   3. Testez sur /toast-test');
  } else {
    console.log('❌ Problèmes détectés:');
    issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue}`);
    });
    console.log('🔧 Solutions suggérées:');
    solutions.forEach((solution, index) => {
      console.log(`   ${index + 1}. ${solution}`);
    });
  }
};

// Fonction pour afficher l'aide
export const toastHelp = () => {
  console.log('🍞 Aide du système Toast');
  console.log('========================');
  console.log('');
  console.log('📋 Fonctions disponibles:');
  console.log('• testAllToasts() - Tester tous les types');
  console.log('• testToastSuccess() - Tester toast succès');
  console.log('• testToastError() - Tester toast erreur');
  console.log('• testToastInfo() - Tester toast info');
  console.log('• testToastWarning() - Tester toast warning');
  console.log('• checkToastProviderStatus() - Vérifier le statut');
  console.log('• diagnoseToastIssues() - Diagnostiquer les problèmes');
  console.log('• toastHelp() - Afficher cette aide');
  console.log('');
  console.log('🔗 Page de test: /toast-test');
  console.log('📚 Documentation: CORRECTION_TOAST_PROVIDER.md');
};

// Exporter toutes les fonctions vers window pour utilisation en console
if (typeof window !== 'undefined') {
  (window as any).testAllToasts = testAllToasts;
  (window as any).testToastSuccess = testToastSuccess;
  (window as any).testToastError = testToastError;
  (window as any).testToastInfo = testToastInfo;
  (window as any).testToastWarning = testToastWarning;
  (window as any).checkToastProviderStatus = checkToastProviderStatus;
  (window as any).diagnoseToastIssues = diagnoseToastIssues;
  (window as any).toastHelp = toastHelp;
  
  // Message d'information au chargement
  console.log('🍞 Utilitaires Toast chargés ! Tapez toastHelp() pour l\'aide.');
}
