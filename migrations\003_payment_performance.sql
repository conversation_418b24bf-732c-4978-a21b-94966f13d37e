-- =====================================================
-- MIGRATION: Optimisations de Performance et Contraintes
-- Version: 1.0.0
-- Date: 2024-12-19
-- Description: Index de performance, contraintes avancées,
--              et optimisations pour le système de paiement
-- =====================================================

-- =====================================================
-- 1. INDEX COMPOSITES POUR PERFORMANCE
-- =====================================================

-- Index pour les requêtes fréquentes sur les abonnements
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_business_subscriptions_business_active_end 
ON business_subscriptions(business_id, is_active, end_date);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_business_subscriptions_plan_active 
ON business_subscriptions(plan_id, is_active) 
WHERE is_active = true;

-- Index pour les intentions de paiement par business et statut
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_intents_business_status_created 
ON payment_intents(business_id, status, created_at);

-- Index pour les reçus par business et date
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_receipts_business_paid_at 
ON payment_receipts(business_id, paid_at DESC);

-- Index pour les événements de paiement par date
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_events_created_at_desc 
ON payment_events(created_at DESC);

-- Index pour les webhooks non traités
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_webhooks_unprocessed 
ON payment_webhooks(provider, processed, created_at) 
WHERE processed = false;

-- Index pour les méthodes de paiement par défaut
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_methods_business_default 
ON payment_methods(business_id) 
WHERE is_default = true;

-- Index pour les coupons actifs par code
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_coupons_code_active 
ON subscription_coupons(code) 
WHERE is_active = true;

-- =====================================================
-- 2. CONTRAINTES AVANCÉES
-- =====================================================

-- Contrainte : Un seul abonnement actif par business
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_unique_active_subscription 
ON business_subscriptions(business_id) 
WHERE is_active = true;

-- Contrainte : Une seule méthode de paiement par défaut par business
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_unique_default_payment_method 
ON payment_methods(business_id) 
WHERE is_default = true;

-- Contrainte : Code de coupon unique
ALTER TABLE subscription_coupons 
ADD CONSTRAINT IF NOT EXISTS uk_subscription_coupons_code 
UNIQUE (code);

-- Contrainte : Numéro de facture unique
ALTER TABLE payment_receipts 
ADD CONSTRAINT IF NOT EXISTS uk_payment_receipts_invoice_number 
UNIQUE (invoice_number);

-- =====================================================
-- 3. CONTRAINTES DE VALIDATION
-- =====================================================

-- Validation des montants positifs
ALTER TABLE subscription_plans 
ADD CONSTRAINT IF NOT EXISTS chk_subscription_plans_price_positive 
CHECK (price >= 0);

ALTER TABLE payment_intents 
ADD CONSTRAINT IF NOT EXISTS chk_payment_intents_amount_positive 
CHECK (amount > 0);

ALTER TABLE payment_receipts 
ADD CONSTRAINT IF NOT EXISTS chk_payment_receipts_amount_positive 
CHECK (amount > 0);

-- Validation des dates d'abonnement
ALTER TABLE business_subscriptions 
ADD CONSTRAINT IF NOT EXISTS chk_business_subscriptions_dates 
CHECK (end_date > start_date);

-- Validation des coupons
ALTER TABLE subscription_coupons 
ADD CONSTRAINT IF NOT EXISTS chk_subscription_coupons_discount_positive 
CHECK (discount_value > 0);

ALTER TABLE subscription_coupons 
ADD CONSTRAINT IF NOT EXISTS chk_subscription_coupons_usage_limits 
CHECK (usage_limit IS NULL OR usage_limit > 0);

ALTER TABLE subscription_coupons 
ADD CONSTRAINT IF NOT EXISTS chk_subscription_coupons_dates 
CHECK (valid_until IS NULL OR valid_from IS NULL OR valid_until > valid_from);

-- =====================================================
-- 4. FONCTIONS D'OPTIMISATION
-- =====================================================

-- Fonction pour calculer les statistiques de revenus
CREATE OR REPLACE FUNCTION calculate_revenue_stats(
    p_start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    p_end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE(
    total_revenue DECIMAL,
    total_transactions INTEGER,
    average_transaction DECIMAL,
    revenue_by_plan JSONB,
    daily_revenue JSONB
) AS $$
DECLARE
    v_revenue_by_plan JSONB;
    v_daily_revenue JSONB;
BEGIN
    -- Calculer les revenus par plan
    SELECT jsonb_object_agg(plan_name, plan_revenue)
    INTO v_revenue_by_plan
    FROM (
        SELECT 
            pr.plan_name,
            SUM(pr.amount) as plan_revenue
        FROM payment_receipts pr
        WHERE pr.paid_at::DATE BETWEEN p_start_date AND p_end_date
        AND pr.status = 'paid'
        GROUP BY pr.plan_name
    ) plan_stats;
    
    -- Calculer les revenus quotidiens
    SELECT jsonb_object_agg(revenue_date, daily_amount)
    INTO v_daily_revenue
    FROM (
        SELECT 
            pr.paid_at::DATE as revenue_date,
            SUM(pr.amount) as daily_amount
        FROM payment_receipts pr
        WHERE pr.paid_at::DATE BETWEEN p_start_date AND p_end_date
        AND pr.status = 'paid'
        GROUP BY pr.paid_at::DATE
        ORDER BY pr.paid_at::DATE
    ) daily_stats;
    
    -- Retourner les statistiques globales
    RETURN QUERY
    SELECT 
        COALESCE(SUM(pr.amount), 0) as total_revenue,
        COUNT(*)::INTEGER as total_transactions,
        COALESCE(AVG(pr.amount), 0) as average_transaction,
        COALESCE(v_revenue_by_plan, '{}'::jsonb) as revenue_by_plan,
        COALESCE(v_daily_revenue, '{}'::jsonb) as daily_revenue
    FROM payment_receipts pr
    WHERE pr.paid_at::DATE BETWEEN p_start_date AND p_end_date
    AND pr.status = 'paid';
END;
$$ LANGUAGE plpgsql;

-- Fonction pour nettoyer les anciennes données
CREATE OR REPLACE FUNCTION cleanup_old_data(
    p_days_to_keep INTEGER DEFAULT 365
)
RETURNS TABLE(
    events_deleted INTEGER,
    webhooks_deleted INTEGER,
    message TEXT
) AS $$
DECLARE
    v_events_deleted INTEGER;
    v_webhooks_deleted INTEGER;
    v_cutoff_date TIMESTAMP WITH TIME ZONE;
BEGIN
    v_cutoff_date := CURRENT_TIMESTAMP - (p_days_to_keep || ' days')::INTERVAL;
    
    -- Supprimer les anciens événements (garder ceux liés aux paiements récents)
    DELETE FROM payment_events 
    WHERE created_at < v_cutoff_date
    AND payment_intent_id NOT IN (
        SELECT id FROM payment_intents 
        WHERE created_at >= v_cutoff_date
    );
    
    GET DIAGNOSTICS v_events_deleted = ROW_COUNT;
    
    -- Supprimer les anciens webhooks traités
    DELETE FROM payment_webhooks 
    WHERE created_at < v_cutoff_date
    AND processed = true;
    
    GET DIAGNOSTICS v_webhooks_deleted = ROW_COUNT;
    
    RETURN QUERY SELECT 
        v_events_deleted,
        v_webhooks_deleted,
        format('Nettoyage terminé: %s événements et %s webhooks supprimés', 
               v_events_deleted, v_webhooks_deleted);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. VUES MATÉRIALISÉES POUR PERFORMANCE
-- =====================================================

-- Vue matérialisée pour les statistiques de revenus mensuels
CREATE MATERIALIZED VIEW IF NOT EXISTS monthly_revenue_stats AS
SELECT 
    DATE_TRUNC('month', pr.paid_at) as month,
    COUNT(*) as transaction_count,
    SUM(pr.amount) as total_revenue,
    AVG(pr.amount) as average_transaction,
    COUNT(DISTINCT pr.business_id) as unique_customers,
    jsonb_object_agg(pr.plan_name, plan_stats.plan_revenue) as revenue_by_plan
FROM payment_receipts pr
JOIN (
    SELECT 
        DATE_TRUNC('month', paid_at) as month,
        plan_name,
        SUM(amount) as plan_revenue
    FROM payment_receipts
    WHERE status = 'paid'
    GROUP BY DATE_TRUNC('month', paid_at), plan_name
) plan_stats ON DATE_TRUNC('month', pr.paid_at) = plan_stats.month
WHERE pr.status = 'paid'
GROUP BY DATE_TRUNC('month', pr.paid_at)
ORDER BY month DESC;

-- Index sur la vue matérialisée
CREATE UNIQUE INDEX IF NOT EXISTS idx_monthly_revenue_stats_month 
ON monthly_revenue_stats(month);

-- Vue matérialisée pour les métriques d'abonnement
CREATE MATERIALIZED VIEW IF NOT EXISTS subscription_metrics AS
SELECT 
    sp.id as plan_id,
    sp.name as plan_name,
    sp.price,
    COUNT(bs.id) as total_subscriptions,
    COUNT(CASE WHEN bs.is_active THEN 1 END) as active_subscriptions,
    COUNT(CASE WHEN bs.is_trial THEN 1 END) as trial_subscriptions,
    AVG(EXTRACT(DAYS FROM (bs.end_date - bs.start_date))) as average_duration_days,
    COUNT(CASE WHEN bs.cancelled_at IS NOT NULL THEN 1 END) as cancelled_subscriptions,
    ROUND(
        COUNT(CASE WHEN bs.cancelled_at IS NOT NULL THEN 1 END)::DECIMAL / 
        NULLIF(COUNT(bs.id), 0) * 100, 2
    ) as churn_rate_percent
FROM subscription_plans sp
LEFT JOIN business_subscriptions bs ON sp.id = bs.plan_id
GROUP BY sp.id, sp.name, sp.price
ORDER BY sp.price;

-- Index sur la vue matérialisée
CREATE UNIQUE INDEX IF NOT EXISTS idx_subscription_metrics_plan_id 
ON subscription_metrics(plan_id);

-- =====================================================
-- 6. FONCTIONS DE RAFRAÎCHISSEMENT DES VUES
-- =====================================================

-- Fonction pour rafraîchir toutes les vues matérialisées
CREATE OR REPLACE FUNCTION refresh_payment_stats()
RETURNS TEXT AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY monthly_revenue_stats;
    REFRESH MATERIALIZED VIEW CONCURRENTLY subscription_metrics;
    
    RETURN 'Vues matérialisées rafraîchies avec succès';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. TÂCHES DE MAINTENANCE AUTOMATIQUE
-- =====================================================

-- Fonction pour les tâches de maintenance quotidiennes
CREATE OR REPLACE FUNCTION daily_maintenance()
RETURNS TEXT AS $$
DECLARE
    v_expired_count INTEGER;
    v_cleanup_result TEXT;
BEGIN
    -- Nettoyer les abonnements expirés
    SELECT expired_count INTO v_expired_count
    FROM cleanup_expired_subscriptions();
    
    -- Rafraîchir les statistiques
    PERFORM refresh_payment_stats();
    
    -- Nettoyer les anciennes données (garder 2 ans)
    SELECT message INTO v_cleanup_result
    FROM cleanup_old_data(730);
    
    RETURN format('Maintenance terminée: %s abonnements expirés, %s', 
                  v_expired_count, v_cleanup_result);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 8. POLITIQUES DE SÉCURITÉ (RLS)
-- =====================================================

-- Activer RLS sur les tables sensibles
ALTER TABLE business_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_intents ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_receipts ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;

-- Politique pour les abonnements (les entreprises ne voient que leurs données)
CREATE POLICY business_subscriptions_policy ON business_subscriptions
    FOR ALL TO authenticated
    USING (business_id = auth.uid());

-- Politique pour les intentions de paiement
CREATE POLICY payment_intents_policy ON payment_intents
    FOR ALL TO authenticated
    USING (business_id = auth.uid());

-- Politique pour les reçus de paiement
CREATE POLICY payment_receipts_policy ON payment_receipts
    FOR ALL TO authenticated
    USING (business_id = auth.uid());

-- Politique pour les méthodes de paiement
CREATE POLICY payment_methods_policy ON payment_methods
    FOR ALL TO authenticated
    USING (business_id = auth.uid());

-- =====================================================
-- 9. MONITORING ET ALERTES
-- =====================================================

-- Fonction pour détecter les anomalies de paiement
CREATE OR REPLACE FUNCTION detect_payment_anomalies()
RETURNS TABLE(
    anomaly_type TEXT,
    description TEXT,
    count INTEGER,
    severity TEXT
) AS $$
BEGIN
    -- Échecs de paiement élevés (> 10% dans les dernières 24h)
    RETURN QUERY
    SELECT 
        'high_failure_rate'::TEXT,
        'Taux d''échec de paiement élevé dans les dernières 24h'::TEXT,
        COUNT(*)::INTEGER,
        'high'::TEXT
    FROM payment_intents
    WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '24 hours'
    AND status = 'failed'
    HAVING COUNT(*) > (
        SELECT COUNT(*) * 0.1 
        FROM payment_intents 
        WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '24 hours'
    );
    
    -- Webhooks non traités anciens (> 1 heure)
    RETURN QUERY
    SELECT 
        'old_unprocessed_webhooks'::TEXT,
        'Webhooks non traités depuis plus d''1 heure'::TEXT,
        COUNT(*)::INTEGER,
        'medium'::TEXT
    FROM payment_webhooks
    WHERE processed = false
    AND created_at < CURRENT_TIMESTAMP - INTERVAL '1 hour'
    HAVING COUNT(*) > 0;
    
    -- Abonnements expirés non nettoyés
    RETURN QUERY
    SELECT 
        'expired_active_subscriptions'::TEXT,
        'Abonnements expirés encore marqués comme actifs'::TEXT,
        COUNT(*)::INTEGER,
        'medium'::TEXT
    FROM business_subscriptions
    WHERE is_active = true
    AND end_date < CURRENT_TIMESTAMP - INTERVAL '1 hour'
    HAVING COUNT(*) > 0;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMMENTAIRES ET DOCUMENTATION
-- =====================================================

COMMENT ON FUNCTION calculate_revenue_stats IS 'Calcule les statistiques de revenus pour une période donnée';
COMMENT ON FUNCTION cleanup_old_data IS 'Nettoie les anciennes données pour optimiser les performances';
COMMENT ON FUNCTION refresh_payment_stats IS 'Rafraîchit toutes les vues matérialisées de statistiques';
COMMENT ON FUNCTION daily_maintenance IS 'Effectue les tâches de maintenance quotidiennes automatiques';
COMMENT ON FUNCTION detect_payment_anomalies IS 'Détecte les anomalies dans le système de paiement';

COMMENT ON MATERIALIZED VIEW monthly_revenue_stats IS 'Statistiques de revenus mensuels agrégées';
COMMENT ON MATERIALIZED VIEW subscription_metrics IS 'Métriques d''abonnement par plan';

-- =====================================================
-- FIN DE LA MIGRATION
-- =====================================================
