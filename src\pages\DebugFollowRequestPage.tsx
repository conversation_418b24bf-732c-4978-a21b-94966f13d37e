import React from 'react';
import FollowRequestDebugPanel from '../components/debug/FollowRequestDebugPanel';

const DebugFollowRequestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Debug - Système de Demandes de Suivi
          </h1>
          <p className="text-gray-600">
            Panel de diagnostic pour identifier et résoudre les problèmes du système de suivi
          </p>
        </div>
        
        <FollowRequestDebugPanel />
        
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>
            Pour accéder à cette page en développement, ajoutez <code>/debug-follow-requests</code> à l'URL
          </p>
        </div>
      </div>
    </div>
  );
};

export default DebugFollowRequestPage;
