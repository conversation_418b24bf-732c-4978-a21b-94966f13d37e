-- DÉPLOIEMENT FINAL DU SYSTÈME DE VÉRIFICATION COMPLET
-- Exécutez ce script dans Supabase SQL Editor

-- =====================================================
-- 1. VÉRIFIER LA STRUCTURE DES TABLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 Vérification de la structure des tables...';
    
    -- Vérifier que les colonnes de vérification existent
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'is_verified'
    ) THEN
        RAISE NOTICE '✅ Colonne is_verified trouvée dans profiles';
    ELSE
        RAISE NOTICE '❌ Colonne is_verified manquante dans profiles';
    END IF;
    
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'business_profiles' AND column_name = 'business_verified'
    ) THEN
        RAISE NOTICE '✅ Colonne business_verified trouvée dans business_profiles';
    ELSE
        RAISE NOTICE '❌ Colonne business_verified manquante dans business_profiles';
    END IF;
END $$;

-- =====================================================
-- 2. AJOUTER LES COLONNES SI ELLES N'EXISTENT PAS
-- =====================================================

-- Ajouter is_verified à profiles si elle n'existe pas
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'is_verified'
    ) THEN
        ALTER TABLE profiles ADD COLUMN is_verified BOOLEAN DEFAULT false;
        RAISE NOTICE '✅ Colonne is_verified ajoutée à profiles';
    END IF;
END $$;

-- Ajouter business_verified à business_profiles si elle n'existe pas
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'business_profiles' AND column_name = 'business_verified'
    ) THEN
        ALTER TABLE business_profiles ADD COLUMN business_verified BOOLEAN DEFAULT false;
        RAISE NOTICE '✅ Colonne business_verified ajoutée à business_profiles';
    END IF;
END $$;

-- =====================================================
-- 3. METTRE À JOUR LES UTILISATEURS EXISTANTS
-- =====================================================

-- Vérifier automatiquement les admins
UPDATE profiles 
SET is_verified = true 
WHERE role = 'admin' AND is_verified = false;

-- Vérifier les utilisateurs avec profil complet
UPDATE profiles 
SET is_verified = true 
WHERE role = 'standard' 
  AND is_verified = false
  AND profile_picture IS NOT NULL 
  AND bio IS NOT NULL 
  AND country IS NOT NULL
  AND created_at < NOW() - INTERVAL '7 days';

-- Vérifier quelques entreprises établies
UPDATE business_profiles 
SET business_verified = true 
WHERE business_name IS NOT NULL 
  AND business_description IS NOT NULL 
  AND business_category IS NOT NULL
  AND created_at < NOW() - INTERVAL '30 days';

-- =====================================================
-- 4. CRÉER DES UTILISATEURS DE DÉMONSTRATION
-- =====================================================

-- Utilisateur standard vérifié
INSERT INTO profiles (
    id, username, email, role, status, is_verified, 
    profile_picture, country, city, bio, created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'demo_verified_user',
    '<EMAIL>',
    'standard',
    'contributor',
    true,
    'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
    'France',
    'Paris',
    'Utilisateur de démonstration vérifié',
    NOW(),
    NOW()
) ON CONFLICT (email) DO UPDATE SET
    is_verified = true,
    updated_at = NOW();

-- Entreprise complètement vérifiée
DO $$
DECLARE
    demo_business_id uuid;
BEGIN
    -- Insérer ou récupérer l'ID
    INSERT INTO profiles (
        id, username, email, role, status, is_verified, 
        profile_picture, country, city, bio, created_at, updated_at
    ) VALUES (
        gen_random_uuid(),
        'demo_verified_business',
        '<EMAIL>',
        'business',
        'contributor',
        true,
        'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=150',
        'France',
        'Lyon',
        'Entreprise de démonstration vérifiée',
        NOW(),
        NOW()
    ) ON CONFLICT (email) DO UPDATE SET
        is_verified = true,
        updated_at = NOW()
    RETURNING id INTO demo_business_id;
    
    -- Si pas de RETURNING (conflit), récupérer l'ID
    IF demo_business_id IS NULL THEN
        SELECT id INTO demo_business_id FROM profiles WHERE email = '<EMAIL>';
    END IF;
    
    -- Créer ou mettre à jour le profil business
    INSERT INTO business_profiles (
        id, business_name, business_description, business_category,
        business_status, business_verified, created_at, updated_at
    ) VALUES (
        demo_business_id,
        'Demo Corp Verified',
        'Entreprise de démonstration avec vérification complète',
        'Technology',
        'verified',
        true,
        NOW(),
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET
        business_verified = true,
        business_status = 'verified',
        updated_at = NOW();
END $$;

-- =====================================================
-- 5. CRÉER UNE ALERTE DE DÉPLOIEMENT FINAL
-- =====================================================

DO $$
DECLARE
    admin_profile_id uuid;
    total_users integer;
    verified_users integer;
    verified_businesses integer;
BEGIN
    SELECT id INTO admin_profile_id FROM admin_profiles LIMIT 1;
    
    -- Compter les statistiques
    SELECT COUNT(*) INTO total_users FROM profiles;
    SELECT COUNT(*) INTO verified_users FROM profiles WHERE is_verified = true;
    SELECT COUNT(*) INTO verified_businesses FROM business_profiles WHERE business_verified = true;
    
    IF admin_profile_id IS NOT NULL THEN
        INSERT INTO admin_alerts (
            alert_type, 
            priority, 
            title, 
            message, 
            details, 
            created_by, 
            status
        ) VALUES (
            'deployment', 
            'high', 
            'Système de Vérification Complètement Déployé', 
            FORMAT('Le système de badges de vérification est maintenant actif sur toute la plateforme. %s utilisateurs vérifiés sur %s total, %s entreprises vérifiées.', verified_users, total_users, verified_businesses), 
            FORMAT('{"deployment": "verification_system", "stats": {"total_users": %s, "verified_users": %s, "verified_businesses": %s}, "components": ["ProfileBanner", "UserCard", "UserDropdownMenu", "UserManagement", "VerificationBadge"], "features": ["auto_verification", "manual_verification", "business_verification", "admin_verification"]}', total_users, verified_users, verified_businesses)::jsonb, 
            admin_profile_id, 
            'open'
        );
        
        RAISE NOTICE '✅ Alerte de déploiement final créée';
    END IF;
END $$;

-- =====================================================
-- 6. FONCTION DE TEST COMPLÈTE
-- =====================================================

CREATE OR REPLACE FUNCTION test_verification_system()
RETURNS text AS $$
DECLARE
    result text := '';
    test_count integer;
BEGIN
    -- Test 1: Vérifier les badges standards
    SELECT COUNT(*) INTO test_count FROM profiles WHERE is_verified = true;
    result := result || FORMAT('✅ %s utilisateurs vérifiés trouvés', test_count) || E'\n';
    
    -- Test 2: Vérifier les badges business
    SELECT COUNT(*) INTO test_count FROM business_profiles WHERE business_verified = true;
    result := result || FORMAT('✅ %s entreprises vérifiées trouvées', test_count) || E'\n';
    
    -- Test 3: Vérifier les admins
    SELECT COUNT(*) INTO test_count FROM profiles WHERE role = 'admin' AND is_verified = true;
    result := result || FORMAT('✅ %s administrateurs vérifiés trouvés', test_count) || E'\n';
    
    -- Test 4: Vérifier la structure
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'is_verified') THEN
        result := result || '✅ Structure de vérification profiles OK' || E'\n';
    ELSE
        result := result || '❌ Structure de vérification profiles manquante' || E'\n';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'business_profiles' AND column_name = 'business_verified') THEN
        result := result || '✅ Structure de vérification business_profiles OK' || E'\n';
    ELSE
        result := result || '❌ Structure de vérification business_profiles manquante' || E'\n';
    END IF;
    
    RETURN result || E'\n🎉 Tests du système de vérification terminés !';
END;
$$ LANGUAGE plpgsql;

-- Exécuter les tests
SELECT test_verification_system();

-- =====================================================
-- 7. RÉSUMÉ FINAL
-- =====================================================

DO $$
DECLARE
    total_users integer;
    verified_users integer;
    total_businesses integer;
    verified_businesses integer;
    admin_count integer;
BEGIN
    -- Statistiques finales
    SELECT COUNT(*) INTO total_users FROM profiles;
    SELECT COUNT(*) INTO verified_users FROM profiles WHERE is_verified = true;
    SELECT COUNT(*) INTO total_businesses FROM business_profiles;
    SELECT COUNT(*) INTO verified_businesses FROM business_profiles WHERE business_verified = true;
    SELECT COUNT(*) INTO admin_count FROM profiles WHERE role = 'admin';
    
    RAISE NOTICE '';
    RAISE NOTICE '🎊 ===============================================';
    RAISE NOTICE '🎊 SYSTÈME DE VÉRIFICATION COMPLÈTEMENT DÉPLOYÉ !';
    RAISE NOTICE '🎊 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '📊 STATISTIQUES FINALES:';
    RAISE NOTICE '   👥 Total utilisateurs: %', total_users;
    RAISE NOTICE '   ✅ Utilisateurs vérifiés: % (%.1f%%)', verified_users, (verified_users::float / GREATEST(total_users, 1) * 100);
    RAISE NOTICE '   🏢 Total entreprises: %', total_businesses;
    RAISE NOTICE '   🛡️ Entreprises vérifiées: % (%.1f%%)', verified_businesses, (verified_businesses::float / GREATEST(total_businesses, 1) * 100);
    RAISE NOTICE '   👑 Administrateurs: %', admin_count;
    RAISE NOTICE '';
    RAISE NOTICE '🎨 BADGES DISPONIBLES:';
    RAISE NOTICE '   🔵 Badge bleu: Utilisateur vérifié';
    RAISE NOTICE '   🛡️ Badge bleu foncé: Entreprise vérifiée';
    RAISE NOTICE '   👑 Badge rouge: Administrateur';
    RAISE NOTICE '';
    RAISE NOTICE '📱 COMPOSANTS INTÉGRÉS:';
    RAISE NOTICE '   ✅ ProfileBanner (bannières de profil)';
    RAISE NOTICE '   ✅ UserCard (cartes utilisateur)';
    RAISE NOTICE '   ✅ UserDropdownMenu (menu utilisateur)';
    RAISE NOTICE '   ✅ UserManagement (gestion admin)';
    RAISE NOTICE '   ✅ VerificationBadge (composant badge)';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 FONCTIONNALITÉS ACTIVES:';
    RAISE NOTICE '   ✅ Vérification automatique';
    RAISE NOTICE '   ✅ Vérification manuelle admin';
    RAISE NOTICE '   ✅ Vérification business';
    RAISE NOTICE '   ✅ Badges contextuels';
    RAISE NOTICE '   ✅ Tooltips informatifs';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 TESTEZ MAINTENANT:';
    RAISE NOTICE '1. Rechargez votre application (F5)';
    RAISE NOTICE '2. Consultez les profils utilisateurs';
    RAISE NOTICE '3. Vérifiez le menu utilisateur';
    RAISE NOTICE '4. Testez la gestion admin';
    RAISE NOTICE '5. Vérifiez manuellement un compte';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 SYSTÈME PRÊT POUR LA PRODUCTION !';
    RAISE NOTICE '';
END $$;
