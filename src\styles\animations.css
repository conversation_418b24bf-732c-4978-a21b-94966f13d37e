/* Animations pour les notifications d'abonnement */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8) translateY(50px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes confetti {
  0% {
    transform: translateY(-10px) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100px) rotate(360deg);
    opacity: 0;
  }
}

/* Classes d'animation */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.animate-bounce-in {
  animation: bounceIn 0.6s ease-out 0.3s both;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out 0.4s both;
}

.animate-slide-up-delay {
  animation: slideUp 0.5s ease-out 0.5s both;
}

.animate-confetti {
  animation: confetti 2s ease-out forwards;
}

/* Transitions pour les composants */
.transition-all-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale {
  transition: transform 0.2s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-scale:active {
  transform: scale(0.95);
}

/* Animation de pulsation pour les éléments importants */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Animation de rotation pour les icônes de chargement */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin 2s linear infinite;
}

/* Animation de glissement pour les notifications */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-slide-out-right {
  animation: slideOutRight 0.3s ease-in;
}

/* Animation de compteur */
@keyframes countUp {
  from {
    transform: scale(1.2);
  }
  to {
    transform: scale(1);
  }
}

.animate-count-up {
  animation: countUp 0.3s ease-out;
}

/* Animation de barre de progression */
@keyframes progressFill {
  from {
    width: 0%;
  }
}

.animate-progress {
  animation: progressFill 1s ease-out;
}

/* Effet de brillance pour les éléments premium */
@keyframes shine {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

.shine-effect {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shine 2s infinite;
}

/* Animation de secousse pour attirer l'attention */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

/* Animation de zoom pour les éléments interactifs */
.zoom-on-hover {
  transition: transform 0.2s ease-in-out;
}

.zoom-on-hover:hover {
  transform: scale(1.1);
}

/* Animation de fondu pour les transitions */
.fade-transition {
  transition: opacity 0.3s ease-in-out;
}

.fade-transition.fade-out {
  opacity: 0;
}

.fade-transition.fade-in {
  opacity: 1;
}
