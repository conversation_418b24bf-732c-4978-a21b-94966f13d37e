import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Filter, Search, CheckCheck, Trash2, Star, MessageSquare, ShoppingCart, Heart, TrendingUp } from 'lucide-react';
import { useNotifications } from '../../context/NotificationsContext';
import NotificationItem from './NotificationItem';
import Button from '../ui/Button';

interface NotificationsDropdownProps {
  className?: string;
}

const NotificationsDropdown: React.FC<NotificationsDropdownProps> = ({ className }) => {
  const { notifications, unreadCount, markAllAsRead } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);
  const [filterType, setFilterType] = useState<'all' | 'unread' | 'important'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [hasNewNotification, setHasNewNotification] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Simuler l'arrivée de nouvelles notifications
  useEffect(() => {
    if (unreadCount > 0) {
      setHasNewNotification(true);
      const timer = setTimeout(() => setHasNewNotification(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [unreadCount]);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setHasNewNotification(false);
    }
  };

  const handleMarkAllAsRead = () => {
    markAllAsRead();
  };

  // Filtrer les notifications
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = (notification.title || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (notification.message || '').toLowerCase().includes(searchQuery.toLowerCase());

    switch (filterType) {
      case 'unread':
        return !notification.isRead && matchesSearch;
      case 'important':
        return notification.priority === 'high' && matchesSearch;
      default:
        return matchesSearch;
    }
  });

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'message': return MessageSquare;
      case 'order': return ShoppingCart;
      case 'like': return Heart;
      case 'trending': return TrendingUp;
      case 'important': return Star;
      default: return Bell;
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'À l\'instant';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}j`;
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        className="relative p-2 rounded-full text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
        onClick={toggleDropdown}
        aria-label="Notifications"
      >
        {hasNewNotification ? (
          <BellRing size={24} className="text-blue-600 animate-bounce" />
        ) : (
          <Bell size={24} className={unreadCount > 0 ? 'text-blue-600' : 'text-gray-600'} />
        )}

        {unreadCount > 0 && (
          <span className={`absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center ${
            hasNewNotification ? 'animate-pulse' : ''
          }`}>
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}

        {hasNewNotification && (
          <span className="absolute -top-1 -right-1 bg-red-500 rounded-full h-3 w-3 animate-ping"></span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
              <div className="flex items-center space-x-2">
                {unreadCount > 0 && (
                  <Button
                    variant="link"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    className="text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                  >
                    <CheckCheck size={14} />
                    <span>Tout lire</span>
                  </Button>
                )}
                <button className="p-1 rounded-full hover:bg-gray-100">
                  <Settings size={16} className="text-gray-500" />
                </button>
              </div>
            </div>

            {/* Search */}
            <div className="relative mb-3">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Rechercher..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-9 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>

            {/* Filters */}
            <div className="flex space-x-2">
              {[
                { key: 'all', label: 'Toutes', count: notifications.length },
                { key: 'unread', label: 'Non lues', count: unreadCount },
                { key: 'important', label: 'Importantes', count: notifications.filter(n => n.priority === 'high').length }
              ].map(filter => (
                <button
                  key={filter.key}
                  onClick={() => setFilterType(filter.key as any)}
                  className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${
                    filterType === filter.key
                      ? 'bg-blue-100 text-blue-700'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {filter.label} ({filter.count})
                </button>
              ))}
            </div>
          </div>

          {/* Notifications List */}
          <div className="max-h-80 overflow-y-auto">
            {filteredNotifications.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                <Bell size={48} className="mx-auto mb-2 text-gray-300" />
                <p className="font-medium">Aucune notification</p>
                <p className="text-sm">Vous êtes à jour !</p>
              </div>
            ) : (
              filteredNotifications.map(notification => {
                const IconComponent = getNotificationIcon(notification.type);
                return (
                  <div
                    key={notification.id}
                    className={`p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors ${
                      !notification.isRead ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-full ${
                        notification.priority === 'high' ? 'bg-red-100 text-red-600' :
                        notification.type === 'message' ? 'bg-blue-100 text-blue-600' :
                        notification.type === 'order' ? 'bg-green-100 text-green-600' :
                        'bg-gray-100 text-gray-600'
                      }`}>
                        <IconComponent size={16} />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <h4 className={`text-sm font-medium ${
                            !notification.isRead ? 'text-gray-900' : 'text-gray-700'
                          }`}>
                            {notification.title}
                          </h4>
                          <span className="text-xs text-gray-500 ml-2">
                            {formatTime(notification.createdAt)}
                          </span>
                        </div>

                        <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                          {notification.message}
                        </p>

                        {notification.priority === 'high' && (
                          <div className="flex items-center mt-2">
                            <Star size={12} className="text-yellow-500 mr-1" />
                            <span className="text-xs text-yellow-600 font-medium">Important</span>
                          </div>
                        )}
                      </div>

                      {!notification.isRead && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      )}
                    </div>
                  </div>
                );
              })
            )}
          </div>

          {/* Footer */}
          <div className="p-3 border-t border-gray-200 bg-gray-50">
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => console.log('Voir toutes les notifications')}
            >
              Voir toutes les notifications
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationsDropdown;
