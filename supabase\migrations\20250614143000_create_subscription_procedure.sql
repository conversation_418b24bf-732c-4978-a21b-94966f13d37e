-- Désactiver la sécurité au niveau des lignes pour le propriétaire de la table
ALTER TABLE public.business_subscriptions OWNER TO postgres;
ALTER TABLE public.business_subscriptions DISABLE ROW LEVEL SECURITY;

-- Créer la fonction avec les privilèges du définisseur
CREATE OR REPLACE FUNCTION public.create_subscription_from_code(p_code_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_code_record record;
  v_subscription_record record;
  v_start_date timestamptz;
  v_end_date timestamptz;
BEGIN
  -- 1. R<PERSON><PERSON><PERSON>rer les détails du code d'abonnement
  SELECT * INTO v_code_record
  FROM public.subscription_codes
  WHERE id = p_code_id AND status = 'validated';

  IF NOT FOUND THEN
    RAISE NOTICE 'Code non trouvé ou non validé: %', p_code_id;
    RETURN FALSE;
  END IF;

  RAISE NOTICE 'Code trouvé: business_id=%, plan_type=%', v_code_record.business_id, v_code_record.plan_type;

  -- 2. Désactiver tout abonnement actif existant pour cette entreprise
  UPDATE public.business_subscriptions
  SET is_active = FALSE, updated_at = now()
  WHERE business_id = v_code_record.business_id AND is_active = TRUE;

  RAISE NOTICE 'Abonnements précédents désactivés pour business_id=%', v_code_record.business_id;

  -- 3. Calculer les dates de début et de fin
  v_start_date := now();
  IF v_code_record.plan_type = 'monthly' THEN
    v_end_date := v_start_date + interval '1 month';
  ELSIF v_code_record.plan_type = 'yearly' THEN
    v_end_date := v_start_date + interval '1 year';
  ELSIF v_code_record.plan_type = 'trial' THEN
    v_end_date := v_start_date + interval '7 days';
  ELSE
    v_end_date := v_start_date + interval '1 month'; -- Par défaut
  END IF;

  RAISE NOTICE 'Dates calculées: start=%, end=%', v_start_date, v_end_date;

  -- 4. Insérer le nouvel abonnement
  INSERT INTO public.business_subscriptions (
    business_id,
    plan_id,
    start_date,
    end_date,
    is_active,
    is_trial,
    auto_renew,
    payment_status
  ) VALUES (
    v_code_record.business_id,
    v_code_record.plan_type,
    v_start_date,
    v_end_date,
    TRUE,
    v_code_record.plan_type = 'trial',
    FALSE,
    'paid'
  );

  RAISE NOTICE 'Nouvel abonnement créé pour business_id=%', v_code_record.business_id;

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    -- Log l'erreur si nécessaire
    RAISE NOTICE 'Erreur dans create_subscription_from_code: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- Réactiver la sécurité au niveau des lignes
ALTER TABLE public.business_subscriptions ENABLE ROW LEVEL SECURITY;
-- Forcer la politique de sécurité pour toutes les lignes
ALTER TABLE public.business_subscriptions FORCE ROW LEVEL SECURITY;

-- Donner les permissions d'exécution à 'authenticated' et 'service_role'
GRANT EXECUTE ON FUNCTION public.create_subscription_from_code(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_subscription_from_code(uuid) TO service_role;