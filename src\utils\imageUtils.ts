// Utility function to handle image fallbacks
import defaultAvatar from '../assets/default-avatar.svg';
import defaultProduct from '../assets/default-product.svg';
import defaultPost from '../assets/default-cover.svg';

export const getImageWithFallback = (src: string | null, type: 'avatar' | 'product' | 'post' = 'product') => {
  if (src) return src;

  // Local fallback images based on type
  switch (type) {
    case 'avatar':
      return defaultAvatar;
    case 'post':
      return defaultPost;
    case 'product':
    default:
      return defaultProduct;
  }
};

// Improved function to fix avatar image paths and handle errors
export const fixAvatarImagePath = (url: string | null): string => {
  if (!url) return defaultAvatar;

  try {
    // Rewrite legacy 'dixiste-avatars' URLs to 'profile-images'
    if (url.includes('dixiste-avatars/')) {
      return url.replace('dixiste-avatars/', 'profile-images/');
    }

    // Check if the URL is for an avatar but missing the 'avatars/' segment
    if (url.includes('profile-images/') && !url.includes('profile-images/avatars/')) {
      return url.replace('profile-images/', 'profile-images/avatars/');
    }

    // Check if the URL is a Supabase URL (to handle potential 404 errors)
    if (url.includes('supabase.co')) {
      // We'll still return the URL but rely on the onError handler in the component
      return url;
    }

    return url;
  } catch (error) {
    console.error('Error processing image URL:', error);
    return defaultAvatar;
  }
};

/**
 * NOUVELLES FONCTIONS ROBUSTES POUR ÉVITER LES PROBLÈMES RÉCURRENTS D'IMAGES
 */

export type ImageInput = string | string[] | null | undefined;

/**
 * Normalise les images en tableau de chaînes, peu importe le format d'entrée
 * @param images - Images sous n'importe quel format (JSON string, array, null, undefined)
 * @returns Tableau de chaînes d'images valides
 */
export const normalizeImages = (images: ImageInput): string[] => {
  // Cas null/undefined
  if (!images) {
    return [];
  }

  // Cas déjà un tableau
  if (Array.isArray(images)) {
    return images.filter(img => img && typeof img === 'string' && img.trim() !== '');
  }

  // Cas chaîne (potentiellement JSON)
  if (typeof images === 'string') {
    const trimmed = images.trim();

    // Chaîne vide
    if (!trimmed) {
      return [];
    }

    // Tenter de parser comme JSON
    try {
      const parsed = JSON.parse(trimmed);
      if (Array.isArray(parsed)) {
        return parsed.filter(img => img && typeof img === 'string' && img.trim() !== '');
      }
      // Si ce n'est pas un tableau après parsing, traiter comme une seule image
      if (typeof parsed === 'string' && parsed.trim() !== '') {
        return [parsed.trim()];
      }
    } catch (error) {
      // Si le parsing JSON échoue, traiter comme une URL d'image simple
      if (trimmed.startsWith('http') || trimmed.startsWith('/')) {
        return [trimmed];
      }
    }
  }

  // Cas par défaut
  return [];
};

// Images par défaut par catégorie pour les produits
const DEFAULT_PRODUCT_IMAGES: Record<string, string[]> = {
  'Beauté': [
    'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1571781926291-c477ebfd024b?w=400&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop&crop=center'
  ],
  'Cosmétiques': [
    'https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=400&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=400&h=400&fit=crop&crop=center'
  ],
  'Hygiène': [
    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1586495777744-4413f21062fa?w=400&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=400&h=400&fit=crop&crop=center'
  ],
  'Parfums': [
    'https://images.unsplash.com/photo-1541643600914-78b084683601?w=400&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1588405748880-12d1d2a59d32?w=400&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1594035910387-fea47794261f?w=400&h=400&fit=crop&crop=center'
  ],
  'Alimentation': [
    'https://images.unsplash.com/photo-1567306301408-9b74779a11af?w=400&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=400&fit=crop&crop=center'
  ],
  'Boissons': [
    'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400&h=400&fit=crop&crop=center'
  ]
};

// Images par défaut génériques
const FALLBACK_IMAGES = [
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400&h=400&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1571781926291-c477ebfd024b?w=400&h=400&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=400&h=400&fit=crop&crop=center'
];

/**
 * Obtient une image par défaut pour une catégorie donnée
 * @param category - Catégorie du produit
 * @param index - Index de l'image (pour la variété)
 * @returns URL de l'image par défaut
 */
export const getDefaultImageForCategory = (category: string = 'Beauté', index: number = 0): string => {
  const categoryImages = DEFAULT_PRODUCT_IMAGES[category] || FALLBACK_IMAGES;
  const imageIndex = index % categoryImages.length;
  return categoryImages[imageIndex];
};

/**
 * Fonction principale pour traiter les images de manière robuste
 * @param images - Images sous n'importe quel format
 * @param category - Catégorie pour les images par défaut
 * @param productName - Nom du produit pour le debug
 * @returns Tableau d'images valides et normalisées
 */
export const processImages = (
  images: ImageInput,
  category: string = 'Beauté',
  productName: string = 'Produit'
): string[] => {
  const normalized = normalizeImages(images);

  // Si aucune image valide, utiliser les images par défaut
  if (normalized.length === 0) {
    const defaultImages = DEFAULT_PRODUCT_IMAGES[category] || FALLBACK_IMAGES;
    return [defaultImages[0]]; // Retourner la première image par défaut
  }

  // Log de debug en mode développement
  if (process.env.NODE_ENV === 'development' && images && normalized.length !== (Array.isArray(images) ? images.length : 1)) {
    console.log('processImages - conversion:', {
      productName,
      category,
      input: images,
      inputType: typeof images,
      output: normalized,
      validCount: normalized.length
    });
  }

  return normalized;
};

/**
 * Obtient la première image d'un produit avec fallback
 * @param images - Images du produit
 * @param category - Catégorie du produit
 * @param productName - Nom du produit
 * @returns URL de la première image
 */
export const getFirstProductImage = (
  images: ImageInput,
  category: string = 'Beauté',
  productName: string = 'Produit'
): string => {
  const processedImages = processImages(images, category, productName);
  return processedImages[0] || getDefaultImageForCategory(category, 0);
};

/**
 * Crée les props pour un élément img avec gestion d'erreur
 * @param src - URL de l'image
 * @param alt - Texte alternatif
 * @param category - Catégorie pour le fallback
 * @returns Props pour l'élément img
 */
export const createImageProps = (
  src: string,
  alt: string,
  category: string = 'Beauté'
) => {
  return {
    src,
    alt,
    onError: (e: React.SyntheticEvent<HTMLImageElement>) => {
      const target = e.target as HTMLImageElement;
      const fallbackImage = getDefaultImageForCategory(category, 0);
      if (target.src !== fallbackImage) {
        target.src = fallbackImage;
      }
    },
    loading: 'lazy' as const,
    style: {
      objectFit: 'cover' as const,
      width: '100%',
      height: '100%'
    }
  };
};

/**
 * FONCTIONS SPÉCIFIQUES POUR LES PUBLICITÉS
 */

// Images par défaut pour les publicités
export const DEFAULT_AD_IMAGES = {
  PROMO: 'https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?w=400&h=300&fit=crop&crop=center',
  BUSINESS: 'https://images.unsplash.com/photo-1556761175-b413da4baf72?w=400&h=300&fit=crop&crop=center',
  PRODUCT: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400&h=300&fit=crop&crop=center',
  SERVICE: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=400&h=300&fit=crop&crop=center'
};

// Types d'images supportés pour les publicités
export const SUPPORTED_AD_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp'
];

// Taille maximum des images de publicité (5MB)
export const MAX_AD_IMAGE_SIZE = 5 * 1024 * 1024;

/**
 * Valide un fichier image pour les publicités
 */
export const validateAdImageFile = (file: File): { isValid: boolean; error?: string } => {
  // Vérifier le type
  if (!SUPPORTED_AD_IMAGE_TYPES.includes(file.type)) {
    return {
      isValid: false,
      error: 'Format de fichier non supporté. Utilisez JPG, PNG ou WebP.'
    };
  }

  // Vérifier la taille
  if (file.size > MAX_AD_IMAGE_SIZE) {
    return {
      isValid: false,
      error: 'Le fichier est trop volumineux. Taille maximum : 5MB.'
    };
  }

  return { isValid: true };
};

/**
 * Crée une URL blob pour un fichier image de publicité avec gestion d'erreur
 */
export const createAdImageURL = (file: File): string | null => {
  try {
    return URL.createObjectURL(file);
  } catch (error) {
    console.error('Erreur lors de la création de l\'URL image de publicité:', error);
    return null;
  }
};

/**
 * Révoque une URL blob de manière sécurisée
 */
export const revokeAdImageURL = (url: string): void => {
  try {
    if (url && url.startsWith('blob:')) {
      URL.revokeObjectURL(url);
    }
  } catch (error) {
    console.error('Erreur lors de la révocation de l\'URL image de publicité:', error);
  }
};

/**
 * Gestionnaire d'erreur d'image pour les publicités
 */
export const handleAdImageError = (
  event: React.SyntheticEvent<HTMLImageElement>,
  adType: 'promo' | 'business' | 'product' | 'service' = 'promo',
  placeholderClass: string = 'image-error-placeholder'
): void => {
  const target = event.target as HTMLImageElement;
  const parent = target.parentElement;

  if (!parent) return;

  // Utiliser l'image par défaut selon le type de publicité
  const fallbackSrc = DEFAULT_AD_IMAGES[adType.toUpperCase() as keyof typeof DEFAULT_AD_IMAGES];

  // Si on n'a pas encore essayé l'image de fallback, l'utiliser
  if (target.src !== fallbackSrc) {
    target.src = fallbackSrc;
    return;
  }

  // Sinon, créer un placeholder d'erreur
  target.style.display = 'none';

  // Éviter de créer plusieurs placeholders
  if (parent.querySelector(`.${placeholderClass}`)) return;

  const placeholder = document.createElement('div');
  placeholder.className = placeholderClass;
  placeholder.innerHTML = `
    <div class="error-icon">📷</div>
    <p>Image non disponible</p>
  `;

  parent.appendChild(placeholder);
};

/**
 * Obtient une image par défaut pour une publicité
 */
export const getDefaultAdImage = (adType: 'promo' | 'business' | 'product' | 'service' = 'promo'): string => {
  return DEFAULT_AD_IMAGES[adType.toUpperCase() as keyof typeof DEFAULT_AD_IMAGES];
};

/**
 * Crée les props pour une image de publicité avec gestion d'erreur
 */
export const createAdImageProps = (
  src: string | undefined,
  alt: string,
  adType: 'promo' | 'business' | 'product' | 'service' = 'promo'
) => {
  return {
    src: src || getDefaultAdImage(adType),
    alt,
    onError: (e: React.SyntheticEvent<HTMLImageElement>) => {
      handleAdImageError(e, adType);
    },
    loading: 'lazy' as const,
    style: {
      objectFit: 'cover' as const,
      width: '100%',
      height: '100%',
      transition: 'opacity 0.3s ease'
    }
  };
};

/**
 * Gestionnaire d'upload d'image pour les publicités avec validation
 */
export const handleAdImageUpload = (
  file: File,
  onSuccess: (url: string, file: File) => void,
  onError: (error: string) => void
): void => {
  // Valider le fichier
  const validation = validateAdImageFile(file);
  if (!validation.isValid) {
    onError(validation.error!);
    return;
  }

  // Créer l'URL
  const url = createAdImageURL(file);
  if (!url) {
    onError('Erreur lors du traitement de l\'image. Veuillez réessayer.');
    return;
  }

  onSuccess(url, file);
};

/**
 * Formate la taille d'un fichier en format lisible
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Vérifie si une URL est une URL blob
 */
export const isBlobURL = (url: string): boolean => {
  return url && url.startsWith('blob:');
};

/**
 * Nettoie toutes les URLs blob d'un tableau
 */
export const cleanupBlobURLs = (urls: string[]): void => {
  urls.forEach(url => {
    if (isBlobURL(url)) {
      revokeAdImageURL(url);
    }
  });
};