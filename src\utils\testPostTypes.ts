import { PostType } from '../types/index';

/**
 * Utilitaire pour tester et valider les types de posts
 */

export const validatePostType = (type: any): PostType => {
  const validTypes = Object.values(PostType);
  
  if (!validTypes.includes(type)) {
    console.error('Type de post invalide:', type);
    console.log('Types valides:', validTypes);
    return PostType.FAVORITE; // fallback
  }
  
  return type as PostType;
};

export const getPostTypeDisplayName = (type: PostType): string => {
  switch (type) {
    case PostType.FAVORITE:
      return 'Coup de Coeur';
    case PostType.COMPLAINT:
      return 'Coup de Gueule';
    case PostType.REVIEW:
      return 'Demande d\'avis';
    default:
      return 'Type inconnu';
  }
};

export const getPostTypeColor = (type: PostType): { bg: string; text: string; bgLight: string } => {
  switch (type) {
    case PostType.FAVORITE:
      return { bg: 'bg-green-600', text: 'text-white', bgLight: 'bg-green-50' };
    case PostType.COMPLAINT:
      return { bg: 'bg-red-600', text: 'text-white', bgLight: 'bg-red-50' };
    case PostType.REVIEW:
      return { bg: 'bg-blue-600', text: 'text-white', bgLight: 'bg-blue-50' };
    default:
      return { bg: 'bg-gray-600', text: 'text-white', bgLight: 'bg-gray-50' };
  }
};

// Données de test pour créer des posts de chaque type
export const testPostsData = [
  {
    type: PostType.FAVORITE,
    businessName: 'Test Business 1',
    productName: 'Produit Excellent',
    category: 'Technologie',
    description: 'Ce produit est vraiment fantastique! Je le recommande vivement.',
    rating: 5
  },
  {
    type: PostType.COMPLAINT,
    businessName: 'Test Business 2', 
    productName: 'Produit Décevant',
    category: 'Services',
    description: 'Service client très décevant, je ne recommande pas du tout.',
    rating: 1
  },
  {
    type: PostType.REVIEW,
    businessName: 'Test Business 3',
    productName: 'Nouveau Produit',
    category: 'Mode',
    description: 'Nous aimerions avoir vos avis sur ce nouveau produit.',
    rating: 3
  }
];

/**
 * Fonction pour vérifier la cohérence des types de posts
 */
export const auditPostTypes = (posts: any[]) => {
  console.log('=== AUDIT DES TYPES DE POSTS ===');
  
  const typeCount = posts.reduce((acc, post) => {
    const type = post.type;
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});
  
  console.log('Répartition des types:', typeCount);
  
  const invalidPosts = posts.filter(post => !Object.values(PostType).includes(post.type));
  if (invalidPosts.length > 0) {
    console.error('Posts avec types invalides:', invalidPosts);
  }
  
  console.log('================================');
  
  return {
    totalPosts: posts.length,
    typeDistribution: typeCount,
    invalidPosts: invalidPosts.length
  };
};
