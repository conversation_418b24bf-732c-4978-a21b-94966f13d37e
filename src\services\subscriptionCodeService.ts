import { supabase } from '../lib/supabase';

import { AdminService } from './adminService';

// Types pour les codes d'abonnement
export interface SubscriptionCode {
  id: string;
  code: string;                    // Code à 8 chiffres
  business_id: string;
  business_name: string;
  plan_type: 'trial' | 'monthly' | 'yearly';
  amount: number;                  // Montant équivalent au code
  status: 'pending' | 'validated' | 'expired' | 'rejected';
  generated_at: string;
  expires_at: string;             // Expiration après 24h
  validated_at?: string;
  validated_by?: string;          // ID de l'admin qui a validé
  rejection_reason?: string;
  created_at: string;
  updated_at: string;
}

export interface CodeValidationRequest {
  codeId: string;
  action: 'validate' | 'reject';
  reason?: string;
  adminId: string;
}

export class SubscriptionCodeService {

  // Clés pour le stockage
  private static readonly STORAGE_KEY = 'customeroom_generated_codes';
  private static readonly SESSION_KEY = 'customeroom_session_codes';

  // Stockage global en mémoire (fallback si localStorage ne fonctionne pas)
  private static globalCodes: SubscriptionCode[] = [];

  // Stockage des codes générés dynamiquement (simulation DB)
  private static dynamicMockCodes: SubscriptionCode[] = [];

  // =====================================================
  // STOCKAGE LOCAL
  // =====================================================

  // Méthode pour sauvegarder les codes dans le stockage local
  private static saveToStorage(codes: SubscriptionCode[]): void {
    try {
      console.log('💾 Sauvegarde de', codes.length, 'codes dans le stockage local');
      
      // Sauvegarder dans localStorage
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(codes));
      console.log('✅ Codes sauvegardés dans localStorage:', codes.length);
      console.log('💾 localStorage après sauvegarde:', localStorage.getItem(this.STORAGE_KEY));

      // Sauvegarder dans sessionStorage (plus fiable)
      sessionStorage.setItem(this.SESSION_KEY, JSON.stringify(codes));
      console.log('✅ Codes sauvegardés dans sessionStorage:', codes.length);
      console.log('💾 sessionStorage après sauvegarde:', sessionStorage.getItem(this.SESSION_KEY));
      
      // Mettre à jour la variable globale
      this.globalCodes = [...codes];
      console.log('✅ Variable globale mise à jour:', this.globalCodes.length);
      
      // Vérification de la sauvegarde
      const savedLocalStorage = localStorage.getItem(this.STORAGE_KEY);
      const savedSessionStorage = sessionStorage.getItem(this.SESSION_KEY);
      
      if (savedLocalStorage && savedSessionStorage) {
        const localCount = JSON.parse(savedLocalStorage).length;
        const sessionCount = JSON.parse(savedSessionStorage).length;
        console.log(`✅ Vérification: ${localCount} codes dans localStorage, ${sessionCount} codes dans sessionStorage`);
      } else {
        console.warn('⚠️ Vérification: Problème de sauvegarde dans le stockage');
      }
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde dans le stockage:', error);
      // Fallback: sauvegarder seulement en mémoire
      this.globalCodes = [...codes];
      console.log('⚠️ Fallback: codes sauvegardés uniquement en mémoire globale');
    }
  }

  private static loadFromStorage(): SubscriptionCode[] {
    try {
      // Essayer sessionStorage d'abord (plus fiable)
      const sessionStored = sessionStorage.getItem(this.SESSION_KEY);
      console.log('🔍 sessionStorage raw data:', sessionStored);
      if (sessionStored) {
        const codes = JSON.parse(sessionStored);
        console.log('📂 Codes chargés depuis sessionStorage:', codes.length, codes);
        // Synchroniser avec la mémoire globale
        this.globalCodes = [...codes];
        return codes;
      }

      // Essayer localStorage ensuite
      const stored = localStorage.getItem(this.STORAGE_KEY);
      console.log('🔍 localStorage raw data:', stored);
      if (stored) {
        const codes = JSON.parse(stored);
        console.log('📂 Codes chargés depuis localStorage:', codes.length, codes);
        // Synchroniser avec la mémoire globale
        this.globalCodes = [...codes];
        return codes;
      } else {
        console.log('📂 Aucun code trouvé dans les storages');
      }
    } catch (error) {
      console.error('Erreur lors du chargement depuis les storages:', error);
    }

    // Fallback: utiliser la mémoire globale
    console.log('📂 Fallback: utilisation mémoire globale:', this.globalCodes.length);
    return [...this.globalCodes];
  }

  // Méthode pour vider le cache (utile pour les tests)
  static clearStorage(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      this.globalCodes = [];
      console.log('🗑️ Cache des codes vidé');
    } catch (error) {
      console.error('Erreur lors du vidage du cache:', error);
    }
  }

  // Méthode pour forcer la synchronisation
  static forceSync(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.globalCodes = JSON.parse(stored);
        console.log('🔄 Synchronisation forcée:', this.globalCodes.length);
      }
    } catch (error) {
      console.error('Erreur lors de la synchronisation:', error);
    }
  }

  // Nouvelle méthode pour forcer la synchronisation après validation
  static forceSyncAfterValidation(): void {
    try {
      console.log('🔄 Synchronisation forcée après validation...');

      // Vérifier l'état avant synchronisation
      console.log('📋 État avant sync - dynamicMockCodes:', this.dynamicMockCodes.length);
      console.log('📋 État avant sync - globalCodes:', this.globalCodes.length);

      // Recharger depuis tous les storages
      this.initializeDynamicCodes();

      // Si les codes dynamiques sont vides après init, essayer de les restaurer
      if (this.dynamicMockCodes.length === 0) {
        console.log('⚠️ Codes dynamiques vides après init, tentative de restauration...');

        // Essayer de restaurer depuis sessionStorage
        const sessionData = sessionStorage.getItem(this.SESSION_KEY);
        if (sessionData) {
          try {
            const sessionCodes = JSON.parse(sessionData);
            this.dynamicMockCodes = [...sessionCodes];
            console.log('✅ Codes restaurés depuis sessionStorage:', this.dynamicMockCodes.length);
          } catch (e) {
            console.error('❌ Erreur parsing sessionStorage:', e);
          }
        }

        // Si toujours vide, essayer localStorage
        if (this.dynamicMockCodes.length === 0) {
          const localData = localStorage.getItem(this.STORAGE_KEY);
          if (localData) {
            try {
              const localCodes = JSON.parse(localData);
              this.dynamicMockCodes = [...localCodes];
              console.log('✅ Codes restaurés depuis localStorage:', this.dynamicMockCodes.length);
            } catch (e) {
              console.error('❌ Erreur parsing localStorage:', e);
            }
          }
        }
      }

      // Forcer la synchronisation globale
      this.forceSync();

      console.log('✅ Synchronisation après validation terminée');
      console.log('📋 Codes dynamiques après sync:', this.dynamicMockCodes.length);
      console.log('📋 Détail après sync:', this.dynamicMockCodes.map(c => `${c.code} - ${c.status}`));
    } catch (error) {
      console.error('❌ Erreur lors de la synchronisation après validation:', error);
    }
  }

  // Ajouter un code aux données mockées (simulation DB)
  private static addToMockData(code: SubscriptionCode): void {
    this.dynamicMockCodes.push(code);
    console.log('📝 Code ajouté aux données mockées, total:', this.dynamicMockCodes.length);
  }

  // Initialiser les codes dynamiques depuis le stockage
  private static initializeDynamicCodes(): void {
    // Toujours recharger depuis le storage pour avoir les derniers codes
    const storedCodes = this.loadFromStorage();
    this.dynamicMockCodes = [...storedCodes];
    console.log('🔄 Codes dynamiques initialisés/rechargés:', this.dynamicMockCodes.length);
    console.log('🔄 Détail codes dynamiques:', this.dynamicMockCodes.map(c => c.code));
  }

  // =====================================================
  // GÉNÉRATION DE CODES
  // =====================================================
  
  static generateSubscriptionCode(amount: number): string {
    // Générer un code de 8 chiffres basé sur le montant
    const baseCode = amount.toString().padStart(6, '0');
    const randomSuffix = Math.floor(Math.random() * 100).toString().padStart(2, '0');
    return baseCode + randomSuffix;
  }

  static async createSubscriptionCode(
    businessId: string,
    businessName: string,
    planType: 'trial' | 'monthly' | 'yearly',
    amount: number
  ): Promise<SubscriptionCode | null> {
    try {
      console.log('🔧 SERVICE - Début createSubscriptionCode avec:', {
        businessId,
        businessName,
        planType,
        amount
      });

      // Générer le code
      const code = this.generateSubscriptionCode(amount);
      console.log('🎲 Code généré:', code);

      // Calculer la date d'expiration (24h)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);
      console.log('📅 Date expiration:', expiresAt);
      
      const subscriptionCode: Omit<SubscriptionCode, 'id' | 'created_at' | 'updated_at'> = {
        code,
        business_id: businessId,
        business_name: businessName,
        plan_type: planType,
        amount,
        status: 'pending',
        generated_at: new Date().toISOString(),
        expires_at: expiresAt.toISOString()
      };

      // Insérer en base de données Supabase
      const { data, error } = await supabase
        .from('subscription_codes')
        .insert(subscriptionCode)
        .select()
        .single();

      if (error) {
        console.error('❌ Erreur Supabase lors de l\'insertion:', error);

        // Fallback en cas d'erreur Supabase - utiliser le stockage local
        const mockCode: SubscriptionCode = {
          id: `code_${Date.now()}`,
          ...subscriptionCode,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Sauvegarder en local comme fallback
        this.saveCodeToLocalStorage(mockCode);
        console.log('💾 Code sauvé en local comme fallback');
        return mockCode;
      }

      // Code créé avec succès en base
      const createdCode = data as SubscriptionCode;

      console.log('✅ SERVICE - Code d\'abonnement généré avec succès:', createdCode);
      console.log('🔄 SERVICE - Retour du code au composant...');

      // SOLUTION : Ajouter directement aux codes dynamiques (simulation DB)
      console.log('💾 SERVICE - Ajout du code aux données dynamiques pour simulation DB');

      // Ajouter aux codes mockés pour qu'il apparaisse immédiatement
      this.addToMockData(createdCode);

      // Sauvegarder aussi dans le storage pour persistance
      const existingCodes = this.loadFromStorage();
      // Vérifier si le code n'existe pas déjà pour éviter les doublons
      const codeExists = existingCodes.some(c => c.id === createdCode.id);
      if (!codeExists) {
        existingCodes.push(createdCode);
        this.saveToStorage(existingCodes);
        console.log('💾 SERVICE - Code sauvé dans storage, total:', existingCodes.length);
      } else {
        console.log('⚠️ SERVICE - Code déjà existant, pas de sauvegarde');
      }

      return createdCode;

    } catch (error) {
      console.error('Erreur lors de la génération du code:', error);
      return null;
    }
  }

  // =====================================================
  // RÉCUPÉRATION DES CODES
  // =====================================================
  
  static async getPendingCodes(): Promise<SubscriptionCode[]> {
    try {
      // En production, récupérer depuis la base de données
      // const { data, error } = await supabase
      //   .from('subscription_codes')
      //   .select('*')
      //   .eq('status', 'pending')
      //   .order('created_at', { ascending: false });

      // if (error) throw error;

      // Données mockées pour la démonstration
      const mockCodes: SubscriptionCode[] = [
        {
          id: 'code_001',
          code: '02500001',
          business_id: 'bus_001',
          business_name: 'Dexima Cosmétiques',
          plan_type: 'monthly',
          amount: 25000,
          status: 'pending',
          generated_at: '2024-11-28T10:30:00Z',
          expires_at: '2024-11-29T10:30:00Z',
          created_at: '2024-11-28T10:30:00Z',
          updated_at: '2024-11-28T10:30:00Z'
        },
        {
          id: 'code_002',
          code: '24000002',
          business_id: 'bus_002',
          business_name: 'Pharmacie Centrale',
          plan_type: 'yearly',
          amount: 240000,
          status: 'pending',
          generated_at: '2024-11-28T14:15:00Z',
          expires_at: '2024-11-29T14:15:00Z',
          created_at: '2024-11-28T14:15:00Z',
          updated_at: '2024-11-28T14:15:00Z'
        },
        {
          id: 'code_003',
          code: '02500003',
          business_id: 'bus_003',
          business_name: 'Restaurant Le Palais',
          plan_type: 'monthly',
          amount: 25000,
          status: 'pending',
          generated_at: '2024-11-28T16:45:00Z',
          expires_at: '2024-11-29T16:45:00Z',
          created_at: '2024-11-28T16:45:00Z',
          updated_at: '2024-11-28T16:45:00Z'
        }
      ];

      return mockCodes;

    } catch (error) {
      console.error('Erreur lors de la récupération des codes:', error);
      return [];
    }
  }

  static async getAllCodes(filters?: {
    status?: string;
    businessName?: string;
    planType?: string;
  }): Promise<SubscriptionCode[]> {
    try {
      console.log('📋 SERVICE - getAllCodes appelé avec filtres:', filters);

      // CORRECTION: Toujours initialiser les codes dynamiques en premier pour avoir les derniers codes
      this.initializeDynamicCodes();
      console.log('📋 SERVICE - Codes dynamiques après init:', this.dynamicMockCodes.length);
      console.log('📋 SERVICE - Détail codes dynamiques:', this.dynamicMockCodes.map(c => `${c.code} (${c.business_name}) - ${c.status}`));

      // Essayer de récupérer depuis Supabase d'abord
      let supabaseCodes: SubscriptionCode[] = [];
      let useSupabaseData = false;

      try {
        let query = supabase
          .from('subscription_codes')
          .select('*')
          .order('generated_at', { ascending: false });

        // Appliquer les filtres
        if (filters?.status && filters.status !== 'all') {
          query = query.eq('status', filters.status);
        }
        if (filters?.businessName) {
          query = query.ilike('business_name', `%${filters.businessName}%`);
        }
        if (filters?.planType && filters.planType !== 'all') {
          query = query.eq('plan_type', filters.planType);
        }

        const { data, error } = await query;

        if (!error && data) {
          supabaseCodes = data as SubscriptionCode[];
          console.log('📋 SERVICE - Codes récupérés depuis Supabase:', supabaseCodes.length);
          useSupabaseData = true;
        } else {
          console.log('⚠️ SERVICE - Erreur Supabase ou pas de données:', error);
        }
      } catch (supabaseError) {
        console.log('⚠️ SERVICE - Erreur connexion Supabase:', supabaseError);
      }

      // Si on a des données Supabase, les combiner avec les données locales mises à jour
      if (useSupabaseData) {
        // Créer un Map pour éviter les doublons et prioriser les données les plus récentes
        const codeMap = new Map<string, SubscriptionCode>();

        // D'abord ajouter les codes Supabase
        supabaseCodes.forEach(code => {
          codeMap.set(code.id, code);
        });

        // Puis ajouter/remplacer avec les codes du stockage local (plus récents)
        this.dynamicMockCodes.forEach(code => {
          const existingCode = codeMap.get(code.id);
          if (!existingCode || new Date(code.updated_at) > new Date(existingCode.updated_at)) {
            codeMap.set(code.id, code);
          }
        });

        const combinedCodes = Array.from(codeMap.values());
        console.log('📋 SERVICE - Codes combinés (Supabase + Local):', combinedCodes.length);

        // Si on a des codes combinés, les retourner
        if (combinedCodes.length > 0) {
          return combinedCodes.sort((a, b) =>
            new Date(b.generated_at).getTime() - new Date(a.generated_at).getTime()
          );
        }
      }

      // Fallback : utiliser les données locales/mockées
      console.log('📋 SERVICE - Utilisation du fallback local/mocké');

      // Récupérer tous les codes (dynamiques + mockés statiques)
      // CORRECTION: Créer un Map pour éviter les doublons entre codes dynamiques et mockés
      const allCodesMap = new Map<string, SubscriptionCode>();

      // D'abord ajouter les codes générés dynamiquement (priorité car plus récents)
      this.dynamicMockCodes.forEach(code => {
        allCodesMap.set(code.id, code);
      });

      // Puis ajouter les codes mockés statiques seulement s'ils n'existent pas déjà
      const staticMockCodes: SubscriptionCode[] = [
        {
          id: 'code_001',
          code: '02500001',
          business_id: 'bus_001',
          business_name: 'Mon Entreprise',
          plan_type: 'monthly',
          amount: 25000,
          status: 'pending',
          generated_at: '2024-06-14T10:30:00Z',
          expires_at: '2024-06-15T10:30:00Z',
          created_at: '2024-06-14T10:30:00Z',
          updated_at: '2024-06-14T10:30:00Z'
        },
        {
          id: 'code_002',
          code: '24000002',
          business_id: 'bus_002',
          business_name: 'Pharmacie Centrale',
          plan_type: 'yearly',
          amount: 240000,
          status: 'pending',
          generated_at: '2024-06-14T14:15:00Z',
          expires_at: '2024-06-15T14:15:00Z',
          created_at: '2024-06-14T14:15:00Z',
          updated_at: '2024-06-14T14:15:00Z'
        },
        {
          id: 'code_004',
          code: '02500004',
          business_id: 'bus_004',
          business_name: 'Boutique Mode Africaine',
          plan_type: 'monthly',
          amount: 25000,
          status: 'validated',
          generated_at: '2024-06-13T09:20:00Z',
          expires_at: '2024-06-14T09:20:00Z',
          validated_at: '2024-06-13T11:45:00Z',
          validated_by: 'admin_001',
          created_at: '2024-06-13T09:20:00Z',
          updated_at: '2024-06-13T11:45:00Z'
        },
        {
          id: 'code_005',
          code: '24000005',
          business_id: 'bus_005',
          business_name: 'Garage Auto Plus',
          plan_type: 'yearly',
          amount: 240000,
          status: 'rejected',
          generated_at: '2024-06-12T15:30:00Z',
          expires_at: '2024-06-13T15:30:00Z',
          rejection_reason: 'Informations d\'entreprise incomplètes',
          created_at: '2024-06-12T15:30:00Z',
          updated_at: '2024-06-12T16:15:00Z'
        },
        {
          id: 'code_006',
          code: '02500006',
          business_id: 'bus_006',
          business_name: 'Salon de Beauté Élégance',
          plan_type: 'monthly',
          amount: 25000,
          status: 'expired',
          generated_at: '2024-06-11T12:00:00Z',
          expires_at: '2024-06-12T12:00:00Z',
          created_at: '2024-06-11T12:00:00Z',
          updated_at: '2024-06-12T12:00:00Z'
        }
      ];

      // Ajouter les codes mockés seulement s'ils n'existent pas déjà dans les codes dynamiques
      staticMockCodes.forEach(staticCode => {
        const existingDynamicCode = this.dynamicMockCodes.find(dc => dc.id === staticCode.id);
        if (!existingDynamicCode) {
          allCodesMap.set(staticCode.id, staticCode);
        }
        // Si le code existe déjà dans les codes dynamiques, on garde la version dynamique
      });

      const allCodes = Array.from(allCodesMap.values());

      // Appliquer les filtres
      let filteredCodes = allCodes;

      if (filters?.status && filters.status !== 'all') {
        filteredCodes = filteredCodes.filter(code => code.status === filters.status);
      }

      if (filters?.businessName) {
        const searchTerm = filters.businessName.toLowerCase();
        filteredCodes = filteredCodes.filter(code =>
          code.business_name.toLowerCase().includes(searchTerm)
        );
      }

      if (filters?.planType && filters.planType !== 'all') {
        filteredCodes = filteredCodes.filter(code => code.plan_type === filters.planType);
      }

      // Trier par date de génération (plus récent en premier)
      filteredCodes.sort((a, b) =>
        new Date(b.generated_at).getTime() - new Date(a.generated_at).getTime()
      );

      console.log('📋 SERVICE - Codes filtrés retournés:', filteredCodes.length);
      console.log('📋 SERVICE - Détail codes retournés:', filteredCodes.map(c => `${c.code} (${c.business_name}) - ${c.status}`));
      return filteredCodes;

    } catch (error) {
      console.error('Erreur lors de la récupération des codes:', error);
      return [];
    }
  }

  // =====================================================
  // VALIDATION DES CODES
  // =====================================================
  
  static async validateCode(request: CodeValidationRequest): Promise<boolean> {
    try {
      const { codeId, action, reason, adminId } = request;

      console.log(`🔍 ${action === 'validate' ? 'Validation' : 'Rejet'} du code ${codeId} par ${adminId}`);

      // Préparer les données de mise à jour
      const updateData: any = {
        status: action === 'validate' ? 'validated' : 'rejected',
        updated_at: new Date().toISOString()
      };

      if (action === 'validate') {
        updateData.validated_at = new Date().toISOString();
        updateData.validated_by = adminId;
        updateData.admin_notes = reason || 'Code validé par l\'administrateur';
      } else {
        updateData.rejection_reason = reason;
        updateData.admin_notes = reason;
      }

      console.log('📝 Données de mise à jour préparées:', updateData);

      let updatedCode: SubscriptionCode | null = null;

      // Essayer de mettre à jour en base de données Supabase
      try {
        const { data, error } = await supabase
          .from('subscription_codes')
          .update(updateData)
          .eq('id', codeId)
          .select()
          .single();

        if (error) {
          console.error('❌ Erreur Supabase lors de la validation:', error);
        } else {
          console.log('✅ Code mis à jour avec succès en base Supabase');
          updatedCode = data as SubscriptionCode;
        }
      } catch (supabaseError) {
        console.error('❌ Erreur connexion Supabase:', supabaseError);
      }

      // TOUJOURS mettre à jour le stockage local, même en cas d'erreur Supabase
      console.log('🔄 Mise à jour du stockage local...');

      if (updatedCode) {
        // Si on a les données de Supabase, les utiliser
        console.log('💾 Mise à jour avec les données Supabase:', updatedCode);
        this.updateLocalCode(updatedCode);
      } else {
        // Sinon, mettre à jour manuellement le code local
        console.log('💾 Fallback : mise à jour manuelle du stockage local avec:', updateData);
        this.updateLocalCodeManually(codeId, updateData);
        
        // CORRECTION: Vérifier que la mise à jour a bien été effectuée
        const storedCodes = this.loadFromStorage();
        const updatedStoredCode = storedCodes.find(c => c.id === codeId);
        console.log('🔍 Vérification après mise à jour manuelle:', updatedStoredCode);
      }

      // Si validation, créer l'abonnement
      if (action === 'validate') {
        try {
          await this.createSubscriptionFromCode(codeId);
          console.log('✅ Abonnement créé avec succès');
        } catch (subscriptionError) {
          console.error('❌ Erreur lors de la création de l\'abonnement:', subscriptionError);
          // Ne pas faire échouer la validation pour autant
        }
      }

      // Enregistrer l'action dans les logs d'audit
      try {
        await this.logCodeAction({
          action: action === 'validate' ? 'code_validated' : 'code_rejected',
          codeId,
          adminId,
          reason
        });
      } catch (logError) {
        console.error('❌ Erreur lors de l\'enregistrement du log:', logError);
        // Ne pas faire échouer la validation pour autant
      }

      console.log('✅ Validation terminée avec succès');
      return true;

    } catch (error) {
      console.error('❌ Erreur lors de la validation du code:', error);
      return false;
    }
  }

  // =====================================================
  // CRÉATION D'ABONNEMENT
  // =====================================================
  
  private static async createSubscriptionFromCode(codeId: string): Promise<boolean> {
    try {
      console.log('📝 Appel de la procédure stockée pour créer l\'abonnement pour le code:', codeId);

      const { data, error } = await supabase.rpc('create_subscription_from_code', {
        p_code_id: codeId
      });

      if (error) {
        console.error('❌ Erreur lors de l\'appel RPC create_subscription_from_code:', error);
        throw error;
      }

      if (data === false) {
        console.error('❌ La procédure stockée a retourné FALSE.');
        throw new Error('La procédure stockée a échoué à créer l\'abonnement.');
      }

      console.log('✅ Abonnement créé avec succès via la procédure stockée pour le code:', codeId);
      return true;

    } catch (error) {
      console.error('❌ Erreur lors de la création de l\'abonnement via RPC:', error);
      return false;
    }
  }

  // =====================================================
  // VÉRIFICATION D'EXPIRATION
  // =====================================================
  
  static async checkExpiredCodes(): Promise<void> {
    try {
      // En production, mettre à jour les codes expirés
      // const now = new Date().toISOString();
      // const { error } = await supabase
      //   .from('subscription_codes')
      //   .update({
      //     status: 'expired',
      //     updated_at: now
      //   })
      //   .eq('status', 'pending')
      //   .lt('expires_at', now);

      // if (error) throw error;

      console.log('🕒 Vérification des codes expirés effectuée');

    } catch (error) {
      console.error('Erreur lors de la vérification des codes expirés:', error);
    }
  }

  // =====================================================
  // STATISTIQUES
  // =====================================================
  
  static async getCodeStatistics(): Promise<{
    total: number;
    pending: number;
    validated: number;
    rejected: number;
    expired: number;
  }> {
    try {
      // En production, compter depuis la base de données
      // const { data, error } = await supabase
      //   .from('subscription_codes')
      //   .select('status');

      // if (error) throw error;

      // Simulation pour la démonstration
      return {
        total: 6,
        pending: 3,
        validated: 1,
        rejected: 1,
        expired: 1
      };

    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      return {
        total: 0,
        pending: 0,
        validated: 0,
        rejected: 0,
        expired: 0
      };
    }
  }

  // =====================================================
  // LOGS D'AUDIT
  // =====================================================
  
  private static async logCodeAction(action: {
    action: string;
    codeId: string;
    adminId: string;
    reason?: string;
  }): Promise<void> {
    try {
      console.log('📝 Enregistrement de l\'action:', action);

      // Vérifier que l'administrateur existe
      if (!action.adminId) {
        console.warn('⚠️ ID administrateur manquant, skip du log');
        return;
      }

      // Vérifier que le code existe
      if (!action.codeId) {
        console.warn('⚠️ ID du code manquant, skip du log');
        return;
      }

      // Si l'adminId est un fallback, ne pas essayer de logger
      if (action.adminId === 'admin_fallback') {
        console.warn('⚠️ Admin fallback utilisé, skip du log en base');
        return;
      }

      // Vérifier que l'administrateur existe dans la base de données
      const { data: adminExists, error: adminError } = await supabase
        .from('admin_profiles')
        .select('id')
        .eq('id', action.adminId)
        .single();

      if (adminError || !adminExists) {
        console.warn(`⚠️ Administrateur avec ID ${action.adminId} non trouvé, skip du log:`, adminError);
        return;
      }

      // Utiliser le service AdminService pour centraliser l'enregistrement des logs
      await AdminService.logAction(
        action.action,
        `Code d'abonnement ${action.action === 'code_validated' ? 'validé' : 'rejeté'}`,
        'subscription_code',
        action.codeId,
        null,
        { reason: action.reason }
      );

      console.log('✅ Log d\'action enregistré avec succès');

    } catch (error) {
      console.warn('⚠️ Erreur lors de l\'enregistrement du log (non bloquant):', error);
      // Ne pas faire échouer la validation pour un problème de log
    }
  }

  // =====================================================
  // MISE À JOUR LOCALE DES CODES
  // =====================================================
  
  // CORRECTION: Nouvelle méthode pour mettre à jour un code dans le stockage local
  private static updateLocalCode(updatedCode: SubscriptionCode): void {
    try {
      console.log('🔄 Début mise à jour locale du code:', updatedCode.id);

      // Charger les codes existants
      const existingCodes = this.loadFromStorage();

      // Trouver l'index du code à mettre à jour
      const codeIndex = existingCodes.findIndex(code => code.id === updatedCode.id);

      if (codeIndex !== -1) {
        // Remplacer le code existant par le code mis à jour
        existingCodes[codeIndex] = updatedCode;
        console.log('✅ Code trouvé et mis à jour localement');
      } else {
        // Si le code n'existe pas, l'ajouter
        existingCodes.push(updatedCode);
        console.log('✅ Code ajouté au stockage local');
      }

      // Sauvegarder les codes mis à jour
      this.saveToStorage(existingCodes);

      // Mettre à jour également les codes dynamiques
      this.dynamicMockCodes = existingCodes;

      console.log('✅ Stockage local mis à jour avec succès');
    } catch (error) {
      console.error('❌ Erreur lors de la mise à jour locale du code:', error);
    }
  }

  // Méthode pour mettre à jour manuellement un code local quand Supabase échoue
  private static updateLocalCodeManually(codeId: string, updateData: any): void {
    try {
      console.log('🔄 Début mise à jour manuelle du code:', codeId);
      console.log('🔄 Données de mise à jour:', updateData);

      // Charger les codes existants
      const existingCodes = this.loadFromStorage();
      console.log('📋 Codes existants chargés:', existingCodes.length);

      // Trouver l'index du code à mettre à jour
      const codeIndex = existingCodes.findIndex(code => code.id === codeId);
      console.log('🔍 Index du code trouvé:', codeIndex);

      if (codeIndex !== -1) {
        // CORRECTION: Sauvegarder l'ancien état pour comparaison
        const oldCode = { ...existingCodes[codeIndex] };
        
        // Mettre à jour le code existant avec les nouvelles données
        existingCodes[codeIndex] = {
          ...existingCodes[codeIndex],
          ...updateData
        };
        
        console.log('✅ Code trouvé et mis à jour manuellement');
        console.log('📋 Ancien état:', oldCode);
        console.log('📋 Nouvel état:', existingCodes[codeIndex]);

        // Sauvegarder les codes mis à jour
        this.saveToStorage(existingCodes);
        console.log('💾 Codes sauvegardés dans le stockage');

        // Mettre à jour également les codes dynamiques
        const dynamicIndex = this.dynamicMockCodes.findIndex(code => code.id === codeId);
        if (dynamicIndex !== -1) {
          this.dynamicMockCodes[dynamicIndex] = {
            ...this.dynamicMockCodes[dynamicIndex],
            ...updateData
          };
          console.log('✅ Code également mis à jour dans les codes dynamiques');
        } else {
          // Si le code n'existe pas dans les codes dynamiques, l'ajouter
          const codeToAdd = existingCodes[codeIndex];
          this.dynamicMockCodes.push(codeToAdd);
          console.log('✅ Code ajouté aux codes dynamiques');
        }

        console.log('✅ Mise à jour manuelle terminée avec succès');
      } else {
        console.log('⚠️ Code non trouvé pour mise à jour manuelle:', codeId);

        // Essayer de trouver dans les codes dynamiques
        const dynamicIndex = this.dynamicMockCodes.findIndex(code => code.id === codeId);
        if (dynamicIndex !== -1) {
          // Sauvegarder l'ancien état pour comparaison
          const oldDynamicCode = { ...this.dynamicMockCodes[dynamicIndex] };
          
          this.dynamicMockCodes[dynamicIndex] = {
            ...this.dynamicMockCodes[dynamicIndex],
            ...updateData
          };
          
          console.log('✅ Code trouvé et mis à jour dans les codes dynamiques');
          console.log('📋 Ancien état (dynamique):', oldDynamicCode);
          console.log('📋 Nouvel état (dynamique):', this.dynamicMockCodes[dynamicIndex]);
          
          // Ajouter également aux codes du stockage
          existingCodes.push(this.dynamicMockCodes[dynamicIndex]);
          this.saveToStorage(existingCodes);
          console.log('💾 Code dynamique ajouté au stockage local');
        } else {
          console.log('❌ Code introuvable dans les codes dynamiques également');
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors de la mise à jour manuelle du code:', error);
    }
  }

  // Méthode pour sauvegarder un code unique dans le stockage local
  private static saveCodeToLocalStorage(code: SubscriptionCode): void {
    try {
      console.log('💾 Sauvegarde du code en local:', code.id);

      // Charger les codes existants
      const existingCodes = this.loadFromStorage();

      // Vérifier si le code n'existe pas déjà
      const codeExists = existingCodes.some(c => c.id === code.id);
      if (!codeExists) {
        existingCodes.push(code);
        this.saveToStorage(existingCodes);
        console.log('✅ Code sauvé en local avec succès');
      } else {
        console.log('⚠️ Code déjà existant, pas de sauvegarde');
      }
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde du code en local:', error);
    }
  }
  
  // =====================================================
  // UTILITAIRES
  // =====================================================
  
  static isCodeExpired(code: SubscriptionCode): boolean {
    return new Date(code.expires_at) < new Date();
  }

  static getTimeUntilExpiration(code: SubscriptionCode): string {
    const now = new Date();
    const expiration = new Date(code.expires_at);
    const diffMs = expiration.getTime() - now.getTime();
    
    if (diffMs <= 0) return 'Expiré';
    
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m`;
    } else {
      return `${diffMinutes}m`;
    }
  }

  static formatCode(code: string): string {
    // Formater le code pour l'affichage (ex: 02500001 -> 0250-0001)
    return code.replace(/(\d{4})(\d{4})/, '$1-$2');
  }
}
