import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Star,
  TrendingUp,
  Filter,
  Search,
  Grid,
  List,
  ArrowLeft,
  Trophy,
  Medal,
  Award,
  Crown
} from 'lucide-react';
import { DEFAULT_IMAGES } from '../constants/defaultImages';
import Card, { CardBody } from '../components/ui/Card';
import { TopRatedProductsService, TopRatedProduct, ProductCategory } from '../services/topRatedProductsService';

const TopRatedProductsPage: React.FC = () => {
  const [products, setProducts] = useState<TopRatedProduct[]>([]);
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'rating' | 'reviews' | 'name'>('rating');
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalReviews: 0,
    averageRating: 0,
    topRatedCount: 0
  });

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        // Charger les produits, catégories et statistiques depuis la base de données
        const [productsData, categoriesData, statsData] = await Promise.all([
          TopRatedProductsService.getTopRatedProducts(),
          TopRatedProductsService.getProductCategories(),
          TopRatedProductsService.getTopRatedProductsStats()
        ]);

        setProducts(productsData);
        setCategories(categoriesData);
        setStats(statsData);
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        // En cas d'erreur, utiliser des données par défaut
        setProducts([]);
        setCategories([{ id: 'all', name: 'Toutes catégories', count: 0 }]);
        setStats({ totalProducts: 0, totalReviews: 0, averageRating: 0, topRatedCount: 0 });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Effet pour filtrer par catégorie
  useEffect(() => {
    if (selectedCategory === 'all') return;

    const loadCategoryProducts = async () => {
      setIsLoading(true);
      try {
        const categoryProducts = await TopRatedProductsService.getProductsByCategory(selectedCategory);
        setProducts(categoryProducts);
      } catch (error) {
        console.error('Erreur lors du filtrage par catégorie:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCategoryProducts();
  }, [selectedCategory]);

  // Effet pour la recherche
  useEffect(() => {
    if (!searchQuery.trim()) return;

    const searchProducts = async () => {
      setIsLoading(true);
      try {
        const searchResults = await TopRatedProductsService.searchProducts(searchQuery);
        setProducts(searchResults);
      } catch (error) {
        console.error('Erreur lors de la recherche:', error);
      } finally {
        setIsLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchProducts, 500);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  // Filtrer et trier les produits
  const filteredProducts = products
    .filter(product => {
      const matchesCategory = selectedCategory === 'all' || 
        product.category.toLowerCase() === selectedCategory;
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.businessName.toLowerCase().includes(searchQuery.toLowerCase());
      return matchesCategory && matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'reviews':
          return b.reviewCount - a.reviewCount;
        case 'name':
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-5 h-5 text-yellow-500" />;
      case 2:
        return <Trophy className="w-5 h-5 text-gray-400" />;
      case 3:
        return <Medal className="w-5 h-5 text-amber-600" />;
      default:
        return <Award className="w-5 h-5 text-blue-500" />;
    }
  };

  const getRankBadge = (rank: number) => {
    if (rank <= 3) {
      const colors = {
        1: 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white',
        2: 'bg-gradient-to-r from-gray-300 to-gray-500 text-white',
        3: 'bg-gradient-to-r from-amber-400 to-amber-600 text-white'
      };
      return colors[rank as keyof typeof colors];
    }
    return 'bg-blue-100 text-blue-800';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement des produits...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link 
                to="/" 
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Retour
              </Link>
              <div className="flex items-center space-x-2">
                <div className="bg-gradient-to-r from-orange-500 to-red-600 rounded-lg p-2">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Produits Mieux Notés</h1>
                  <p className="text-sm text-gray-600">Découvrez les produits les plus appréciés</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Section */}
        {!isLoading && stats.totalProducts > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <Card>
              <CardBody className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.totalProducts}</div>
                <div className="text-sm text-gray-600">Produits Notés</div>
              </CardBody>
            </Card>
            <Card>
              <CardBody className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{stats.totalReviews}</div>
                <div className="text-sm text-gray-600">Avis Total</div>
              </CardBody>
            </Card>
            <Card>
              <CardBody className="p-4 text-center">
                <div className="flex items-center justify-center space-x-1">
                  <Star className="w-5 h-5 text-yellow-400 fill-current" />
                  <span className="text-2xl font-bold text-yellow-600">{stats.averageRating.toFixed(1)}</span>
                </div>
                <div className="text-sm text-gray-600">Note Moyenne</div>
              </CardBody>
            </Card>
            <Card>
              <CardBody className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.topRatedCount}</div>
                <div className="text-sm text-gray-600">Excellents (4.5+)</div>
              </CardBody>
            </Card>
          </div>
        )}

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardBody className="p-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Rechercher un produit..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Filters */}
              <div className="flex items-center space-x-4">
                {/* Category Filter */}
                <div className="flex items-center space-x-2">
                  <Filter className="w-5 h-5 text-gray-500" />
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name} ({category.count})
                      </option>
                    ))}
                  </select>
                </div>

                {/* Sort */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'rating' | 'reviews' | 'name')}
                  className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="rating">Mieux notés</option>
                  <option value="reviews">Plus d'avis</option>
                  <option value="name">Nom A-Z</option>
                </select>

                {/* View Mode */}
                <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
                  >
                    <Grid className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
                  >
                    <List className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600">
            {filteredProducts.length} produit{filteredProducts.length > 1 ? 's' : ''} trouvé{filteredProducts.length > 1 ? 's' : ''}
          </p>
        </div>

        {/* Products Grid/List */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardBody className="p-0">
                  <div className="aspect-square bg-gray-200"></div>
                  <div className="p-4 space-y-2">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        ) : filteredProducts.length > 0 ? (
          <div className={viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' 
            : 'space-y-4'
          }>
            {filteredProducts.map((product) => (
              <Card key={product.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardBody className={viewMode === 'grid' ? 'p-0' : 'p-4'}>
                  {viewMode === 'grid' ? (
                    // Grid View
                    <div>
                      {/* Rank Badge */}
                      <div className="relative">
                        <div className="aspect-square bg-gray-100 overflow-hidden">
                          <img
                            src={product.image}
                            alt={product.name}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = DEFAULT_IMAGES.PRODUCT;
                            }}
                          />
                        </div>
                        <div className={`absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-bold ${getRankBadge(product.rank)}`}>
                          #{product.rank}
                        </div>
                        <div className="absolute top-2 right-2">
                          {getRankIcon(product.rank)}
                        </div>
                      </div>
                      
                      <div className="p-4">
                        <h3 className="font-semibold text-gray-900 mb-1 truncate text-lg">{product.name}</h3>
                        <p className="text-sm text-blue-600 hover:text-blue-800 mb-2 font-medium">{product.businessName}</p>
                        
                        <div className="flex items-center mb-2">
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                size={14}
                                className={`${
                                  i < Math.floor(product.rating)
                                    ? 'text-yellow-400 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-sm font-bold text-gray-900 ml-1">
                            {product.rating.toFixed(1)}
                          </span>
                          <span className="text-xs text-gray-500 ml-1">
                            ({product.reviewCount} avis)
                          </span>
                        </div>
                        
                        {product.price && (
                          <p className="text-lg font-bold text-blue-600">
                            {product.price.toLocaleString()} F CFA
                          </p>
                        )}
                      </div>
                    </div>
                  ) : (
                    // List View
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        <div className="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                          <img
                            src={product.image}
                            alt={product.name}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = DEFAULT_IMAGES.PRODUCT;
                            }}
                          />
                        </div>
                        <div className={`absolute -top-2 -left-2 px-2 py-1 rounded-full text-xs font-bold ${getRankBadge(product.rank)}`}>
                          #{product.rank}
                        </div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 mb-1 text-lg">{product.name}</h3>
                            <p className="text-sm text-blue-600 hover:text-blue-800 mb-2 font-medium">{product.businessName}</p>
                            <p className="text-sm text-gray-500 mb-2 line-clamp-2">{product.description}</p>
                            
                            <div className="flex items-center">
                              <div className="flex items-center">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    size={14}
                                    className={`${
                                      i < Math.floor(product.rating)
                                        ? 'text-yellow-400 fill-current'
                                        : 'text-gray-300'
                                    }`}
                                  />
                                ))}
                              </div>
                              <span className="text-sm font-bold text-gray-900 ml-1">
                                {product.rating.toFixed(1)}
                              </span>
                              <span className="text-xs text-gray-500 ml-1">
                                ({product.reviewCount} avis)
                              </span>
                            </div>
                          </div>
                          
                          <div className="text-right ml-4">
                            {getRankIcon(product.rank)}
                            {product.price && (
                              <p className="text-lg font-bold text-blue-600 mt-2">
                                {product.price.toLocaleString()} F CFA
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </CardBody>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <TrendingUp className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Aucun produit trouvé</h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || selectedCategory !== 'all'
                ? 'Essayez de modifier vos critères de recherche.'
                : 'Aucun produit mieux noté disponible pour le moment.'
              }
            </p>
            {!searchQuery && selectedCategory === 'all' && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                <p className="text-sm text-blue-800 mb-2">
                  <strong>Comment ça marche :</strong>
                </p>
                <p className="text-xs text-blue-700">
                  Les produits sont classés selon les notes données dans les posts et commentaires des utilisateurs.
                  Créez des posts avec des notes pour voir apparaître les produits ici !
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TopRatedProductsPage;
