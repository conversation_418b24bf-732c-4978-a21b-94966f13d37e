import React from 'react';

interface NetworkStatsProps {
  abonnements: number;
  abonnes: number;
}

const NetworkStats: React.FC<NetworkStatsProps> = ({ abonnements, abonnes }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
      <h3 className="font-semibold mb-3"><PERSON><PERSON><PERSON></h3>
      <div className="flex justify-between">
        <div className="text-center w-1/2">
          <div className="text-xl font-bold">{abonnements}</div>
          <div className="text-xs text-gray-500">Abonnements</div>
        </div>
        <div className="text-center w-1/2">
          <div className="text-xl font-bold">{abonnes}</div>
          <div className="text-xs text-gray-500">Abonnés</div>
        </div>
      </div>
    </div>
  );
};

export default NetworkStats;
