/* Analytics des commandes */
.order-analytics {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* En-tête */
.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.header-left h2 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.date-range-selector {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 0.25rem;
}

.range-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: none;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.range-btn:hover {
  color: #374151;
  background: #e5e7eb;
}

.range-btn.active {
  background: #3b82f6;
  color: white;
}

/* Métriques principales */
.main-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.metric-card {
  border: 1px solid #e5e7eb;
  transition: all 0.2s;
}

.metric-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  color: white;
}

.metric-icon.revenue { background: #ef4444; }
.metric-icon.orders { background: #3b82f6; }
.metric-icon.aov { background: #10b981; }
.metric-icon.conversion { background: #8b5cf6; }

.metric-details {
  flex: 1;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.metric-growth {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.growth-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
}

.growth-indicator.positive {
  background: #d1fae5;
  color: #065f46;
}

.growth-indicator.negative {
  background: #fee2e2;
  color: #991b1b;
}

/* Onglets */
.analytics-tabs {
  display: flex;
  background: #f9fafb;
  border-radius: 8px;
  padding: 0.25rem;
  gap: 0.25rem;
}

.analytics-tabs .tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
  justify-content: center;
}

.analytics-tabs .tab:hover {
  color: #374151;
  background: #f3f4f6;
}

.analytics-tabs .tab.active {
  background: white;
  color: #3b82f6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Contenu des onglets */
.analytics-content {
  min-height: 400px;
}

.overview-tab,
.products-tab,
.customers-tab,
.trends-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Répartition par statut */
.status-distribution {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 8px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.status-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-label {
  font-weight: 500;
  color: #374151;
}

.status-count {
  font-weight: 600;
  color: #1f2937;
  margin-right: 1rem;
}

.status-percentage {
  font-size: 0.875rem;
  color: #6b7280;
  min-width: 50px;
  text-align: right;
}

/* Graphique de revenus */
.revenue-chart {
  display: flex;
  align-items: end;
  gap: 1rem;
  height: 200px;
  padding: 1rem 0;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;
}

.bar {
  width: 100%;
  max-width: 40px;
  background: linear-gradient(to top, #3b82f6, #60a5fa);
  border-radius: 4px 4px 0 0;
  margin-bottom: 0.5rem;
  min-height: 20px;
}

.bar-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.bar-value {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
}

/* Top produits */
.top-products {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.product-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.875rem;
}

.product-info {
  flex: 1;
}

.product-name {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.product-stats {
  font-size: 0.875rem;
  color: #6b7280;
}

.product-badge {
  color: #f59e0b;
}

/* Métriques clients */
.customer-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.customer-metric {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.customer-metric svg {
  color: #3b82f6;
}

/* Tendances de croissance */
.growth-trends {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.trend-item {
  text-align: center;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 8px;
}

.trend-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.75rem;
}

.trend-value {
  display: flex;
  justify-content: center;
}

/* États de chargement et d'erreur */
.analytics-loading,
.analytics-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.analytics-loading svg,
.analytics-error svg {
  color: #6b7280;
  margin-bottom: 1rem;
}

.analytics-loading p,
.analytics-error p {
  color: #6b7280;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .analytics-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .date-range-selector {
    overflow-x: auto;
  }

  .range-btn {
    white-space: nowrap;
    min-width: fit-content;
  }

  .main-metrics {
    grid-template-columns: 1fr;
  }

  .analytics-tabs {
    overflow-x: auto;
  }

  .analytics-tabs .tab {
    white-space: nowrap;
    min-width: fit-content;
    flex: none;
  }

  .revenue-chart {
    overflow-x: auto;
    padding-bottom: 1rem;
  }

  .chart-bar {
    min-width: 60px;
  }

  .growth-trends {
    grid-template-columns: 1fr;
  }

  .customer-metrics {
    grid-template-columns: 1fr;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.metric-card,
.analytics-content > * {
  animation: fadeIn 0.3s ease-out;
}

/* Hover effects */
.product-item:hover {
  background: #f3f4f6;
  transform: translateX(4px);
  transition: all 0.2s;
}

.trend-item:hover {
  background: #f3f4f6;
  transform: translateY(-2px);
  transition: all 0.2s;
}
