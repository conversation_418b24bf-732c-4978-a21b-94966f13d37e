-- Migration pour ajouter le statut business à la vue posts_with_author_details
-- Cette migration permet d'afficher le vrai statut des entreprises mentionnées dans les posts

-- =====================================================
-- 1. METTRE À JOUR LA VUE posts_with_author_details
-- =====================================================

-- Supprimer la vue existante pour éviter les conflits de colonnes
DROP VIEW IF EXISTS posts_with_author_details;

-- Recréer la vue pour inclure le statut business des entreprises mentionnées
CREATE VIEW posts_with_author_details AS
SELECT
  p.id,
  p.user_id,
  p.type,
  p.business_name,
  p.product_name,
  p.category,
  p.description,
  p.rating,
  p.images,
  p.tags,
  p.recommendations,
  p.likes,
  p.shares,
  p.created_at,
  p.updated_at,
  u.username as author_username,
  u.profile_picture as author_profile_picture,
  u.status as author_status,
  u.role as author_role,
  -- Récupérer le statut business de l'entreprise mentionnée dans le post
  CASE
    WHEN p.business_name IS NOT NULL THEN (
      SELECT bp.business_status
      FROM business_profiles bp
      JOIN profiles pr ON bp.id = pr.id
      WHERE pr.username ILIKE p.business_name
        AND pr.role = 'business'
      LIMIT 1
    )
    ELSE NULL
  END as mentioned_business_status,
  COALESCE(
    (
      SELECT json_agg(
        json_build_object(
          'id', c.id,
          'userId', c.user_id,
          'username', cu.username,
          'profilePicture', cu.profile_picture,
          'content', c.content,
          'hasUsedProduct', c.has_used_product,
          'rating', c.rating,
          'createdAt', c.created_at,
          'authorRole', cu.role,
          'businessName', CASE WHEN cu.role = 'business' THEN cu.username ELSE NULL END
        ) ORDER BY c.created_at ASC
      )
      FROM comments c
      LEFT JOIN profiles cu ON c.user_id = cu.id
      WHERE c.post_id = p.id
    ),
    '[]'::json
  ) as comments
FROM posts p
LEFT JOIN profiles u ON p.user_id = u.id;

-- Accorder l'accès à la vue
GRANT SELECT ON posts_with_author_details TO authenticated;

-- =====================================================
-- 2. COMMENTAIRES POUR DOCUMENTATION
-- =====================================================

COMMENT ON VIEW posts_with_author_details IS 'Vue enrichie des posts avec informations auteur et statut business des entreprises mentionnées';
