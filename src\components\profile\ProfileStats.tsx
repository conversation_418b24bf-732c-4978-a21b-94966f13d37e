import React, { useMemo } from 'react';
import { Award, ThumbsUp, MessageSquare, Star } from 'lucide-react';
import { IUser, IBusinessUser, UserStatus } from '../../types';
import Card, { CardBody } from '../ui/Card';
import UserStatusBadge from '../ui/UserStatusBadge';
import { usePosts } from '../../context/PostsContext';

interface ProfileStatsProps {
  user?: IUser | IBusinessUser;
}

const LoadingCard = () => (
  <Card>
    <CardBody className="p-4">
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="h-8 bg-gray-200 rounded w-1/2"></div>
      </div>
    </CardBody>
  </Card>
);

const ProfileStats: React.FC<ProfileStatsProps> = ({ user }) => {
  const { posts: allPosts } = usePosts();

  const userRecommendationsCount = useMemo(() => {
    if (!allPosts || !user) return 0;
    return allPosts.filter(post => post.recommendations?.includes(user.id)).length;
  }, [allPosts, user?.id]);

  const userReviewsPublishedCount = useMemo(() => {
    if (!allPosts || !user) return 0;
    let count = 0;
    allPosts.forEach(post => {
      post.comments?.forEach(comment => {
        if (comment.userId === user.id && comment.hasUsedProduct) {
          count++;
        }
      });
    });
    return count;
  }, [allPosts, user?.id]);

  // Si l'utilisateur n'est pas défini, on affiche un état de chargement
  if (!user) {
    return (
      <div className="space-y-4">
        <LoadingCard />
        <LoadingCard />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* User Status */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div className="bg-gradient-to-r from-emerald-500 to-blue-600 px-4 py-3">
          <div className="flex items-center text-white">
            <div className="bg-white/20 rounded-lg p-1.5 mr-2">
              <Award size={16} />
            </div>
            <h3 className="font-semibold text-sm">Statut</h3>
          </div>
        </div>
        <div className="p-4">
          <UserStatusBadge
            status={user?.status || UserStatus.NEWBIE}
            className="text-sm px-3 py-1"
          />
        </div>
      </div>

      {/* Statistics */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div className="bg-gradient-to-r from-purple-500 to-pink-600 px-4 py-3">
          <div className="flex items-center text-white">
            <div className="bg-white/20 rounded-lg p-1.5 mr-2">
              <Star size={16} />
            </div>
            <h3 className="font-semibold text-sm">Statistiques</h3>
          </div>
        </div>
        <div className="p-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-100">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                  <Star size={14} className="text-yellow-600" />
                </div>
                <span className="text-sm text-gray-700 font-medium">Recommandations</span>
              </div>
              <span className="font-bold text-gray-900 text-lg">{userRecommendationsCount}</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg border border-blue-100">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <MessageSquare size={14} className="text-blue-600" />
                </div>
                <span className="text-sm text-gray-700 font-medium">Posts créés</span>
              </div>
              <span className="font-bold text-gray-900 text-lg">{user.post_count || 0}</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <ThumbsUp size={14} className="text-green-600" />
                </div>
                <span className="text-sm text-gray-700 font-medium">Avis publiés</span>
              </div>
              <span className="font-bold text-gray-900 text-lg">{userReviewsPublishedCount}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileStats;
