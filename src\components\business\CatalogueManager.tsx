import React, { useState } from 'react';
import { Plus, ShoppingCart, MessageCircle, Edit, Trash2, Eye } from 'lucide-react';
import { IProduct, QualityBadge, IBusinessUser, PostType } from '../../types/index';
import EditProductModal from './EditProductModal';
import '../../styles/CatalogueManager.css';
import * as productService from '../../services/productService';
import { supabase } from '../../lib/supabase';
import { toast } from 'react-toastify';
import { usePosts } from '../../context/PostsContext';

interface CatalogueManagerProps {
  businessData: IBusinessUser;
  products: IProduct[];
  isOwnBusiness: boolean;
  onProductsUpdate: (products: IProduct[]) => void;
}

const CatalogueManager: React.FC<CatalogueManagerProps> = ({
  businessData,
  products,
  isOwnBusiness,
  onProductsUpdate
}) => {
  const { createPost } = usePosts(); // <-- Fix: get createPost from context
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<IProduct | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    price: '',
    negotiable: false,
    stock: '1',
    images: [] as File[]
  });
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      category: '',
      price: '',
      negotiable: false,
      stock: '1',
      images: []
    });
    setPreviewUrls([]);
    setError(null);
    setSuccessMessage(null);
  };

  const handleEditProduct = (product: IProduct) => {
    setEditingProduct(product);
    setIsEditModalOpen(true);
  };
  
  const handleSaveEditedProduct = (productData: Partial<IProduct>, isCreating: boolean) => {
    if (isCreating) {
      productService.addProduct(productData, businessData.id).then(newProduct => {
        if (newProduct) {
          onProductsUpdate([...products, newProduct]);
          setSuccessMessage('Produit ajouté avec succès');
        }
      });
    } else if (productData.id) {
      productService.updateProduct(productData.id, productData).then((updatedProduct) => {
        if (updatedProduct) {
          const updatedProducts = products.map(p => p.id === updatedProduct.id ? updatedProduct : p);
          onProductsUpdate(updatedProducts);
          setSuccessMessage('Produit mis à jour avec succès');
        }
      });
    }
  };

  const handleDeleteProduct = async (productId: string) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {
      return;
    }
  
    try {
      // Supprimer le produit de la base de données
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', productId);
        
      if (error) throw error;
      
      // Mettre à jour l'état local
      const updatedProducts = products.filter(p => p.id !== productId);
      onProductsUpdate(updatedProducts);
      
      setSuccessMessage('Produit supprimé avec succès');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Erreur lors de la suppression du produit:', error);
      setError('Une erreur est survenue lors de la suppression du produit');
    }
  };

  const handlePublishToMarketplace = async (productId: string) => {
    try {
      // Mettre à jour le statut du produit plutôt que de le déplacer
      const { error } = await supabase
        .from('products')
        .update({ isOnMarketplace: true })
        .eq('id', productId);
        
      if (error) throw error;
      
      // Mettre à jour l'état local pour refléter le changement
      onProductsUpdate(products.map(product => 
        product.id === productId 
          ? { ...product, isOnMarketplace: true } 
          : product
      ));
      
      toast.success('Produit publié sur la marketplace avec succès');
    } catch (error) {
      console.error('Erreur lors de la publication sur la marketplace:', error);
      toast.error('Erreur lors de la publication sur la marketplace');
    }
  };

  const handleRequestReviews = async (productId: string) => {
    try {
      const product = products.find(p => p.id === productId);
      if (!product) {
        setError("Produit introuvable pour la demande d'avis");
        return;
      }
      
      // S'assurer que product.images est un tableau valide
      const productImages = Array.isArray(product.images) ? product.images : [];
      
      // Ensure rating is a valid number between 1-5 to satisfy the database constraint
      const validRating = 1; // Explicitly set to 1 for review requests
      
      await createPost({
        userId: businessData.id,
        username: businessData.username, // Utiliser le nom du contact (username)
        type: PostType.REVIEW, // Make sure PostType is imported from '../../types/index'
        businessName: businessData.businessName, // Utiliser le nom de l'entreprise (businessName)
        productName: product.name,
        category: product.category,
        description: `Nous sollicitons vos avis sur notre produit/service : ${product.name}. Merci de partager votre expérience !`,
        images: productImages as unknown as File[], // Cast to satisfy File[] type, context handles both
        rating: validRating, // Using validated rating to respect DB constraint (1-5)
        tags: ['avis', 'demande', product.category]
      });
      toast.success(`Demande d'avis publiée pour le produit ${product.name}`);
    } catch (error: any) {
      console.error("Erreur lors de la demande d'avis:", error);
      setError("Une erreur est survenue lors de la demande d'avis: " + (error?.message || error));
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    const newImages = Array.from(files);
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...newImages]
    }));

    // Créer des URLs pour la prévisualisation
    const newPreviewUrls = newImages.map(file => URL.createObjectURL(file));
    setPreviewUrls(prev => [...prev, ...newPreviewUrls]);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Validation
      if (!formData.name.trim()) {
        throw new Error('Le nom du produit est requis');
      }

      if (!formData.price.trim() || isNaN(parseFloat(formData.price))) {
        throw new Error('Veuillez entrer un prix valide');
      }

      // Préparer les données du produit en camelCase (le service fait la conversion snake_case)
      const productData = {
        name: formData.name,
        description: formData.description,
        category: formData.category,
        price: parseFloat(formData.price),
        businessId: businessData.id,
        averageRating: editingProduct ? editingProduct.averageRating : 0,
        qualityBadge: editingProduct ? editingProduct.qualityBadge : QualityBadge.NONE,
        images: previewUrls,
        negotiable: formData.negotiable,
        stock: parseInt(formData.stock)
      };

      let updatedProduct: IProduct | null;
      if (editingProduct) {
        // Mettre à jour le produit existant
        updatedProduct = await productService.updateProduct(editingProduct.id, productData);
        if (updatedProduct) {
          const updatedProducts = products.map(p => p.id === editingProduct.id ? updatedProduct! : p);
          onProductsUpdate(updatedProducts);
          setSuccessMessage('Produit mis à jour avec succès');
        }
      } else {
        // Ajouter un nouveau produit
        updatedProduct = await productService.addProduct(productData, businessData.id);
        if (updatedProduct) {
          onProductsUpdate([...products, updatedProduct]);
          setSuccessMessage('Produit ajouté avec succès');
        }
      }

      // Réinitialiser le formulaire
      resetForm();
      setShowAddForm(false);
      setEditingProduct(null);
    } catch (error) {
      console.error('Erreur lors de l\'ajout/modification du produit:', error);
      setError(error instanceof Error ? error.message : 'Une erreur est survenue');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="catalogue-manager">
      <div className="catalogue-header">
        <h2>Catalogue de produits et services</h2>
        {isOwnBusiness && (
          <button 
            className="add-product-button"
            onClick={() => {
              resetForm();
              setEditingProduct(null);
              setShowAddForm(!showAddForm);
            }}
          >
            <Plus size={18} />
            {showAddForm ? 'Annuler' : 'Ajouter un produit/service'}
          </button>
        )}
      </div>

      {error && <div className="error-message">{error}</div>}
      {successMessage && <div className="success-message">{successMessage}</div>}

      {showAddForm && (
        <div className="product-form-container">
          <h3>{editingProduct ? 'Modifier le produit/service' : 'Ajouter un nouveau produit/service'}</h3>
          <form onSubmit={handleSubmit} className="product-form">
            <div className="form-group">
              <label htmlFor="name">Nom du produit/service *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="description">Description</label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows={4}
              />
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="category">Catégorie</label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                >
                  <option value="">Sélectionner une catégorie</option>
                  <option value="Beauté">Beauté</option>
                  <option value="Santé">Santé</option>
                  <option value="Mode">Mode</option>
                  <option value="Alimentation">Alimentation</option>
                  <option value="Technologie">Technologie</option>
                  <option value="Services">Services</option>
                  <option value="Autre">Autre</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="price">Prix (F CFA) *</label>
                <input
                  type="number"
                  id="price"
                  name="price"
                  value={formData.price}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  required
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="stock">Stock disponible</label>
                <input
                  type="number"
                  id="stock"
                  name="stock"
                  value={formData.stock}
                  onChange={handleChange}
                  min="0"
                />
              </div>

              <div className="form-group checkbox-group">
                <label>
                  <input
                    type="checkbox"
                    name="negotiable"
                    checked={formData.negotiable}
                    onChange={handleChange}
                  />
                  Prix négociable
                </label>
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="images">Images du produit</label>
              <input
                type="file"
                id="images"
                name="images"
                onChange={handleImageChange}
                multiple
                accept="image/*"
              />
              <div className="image-preview-container">
                {previewUrls.map((url, index) => (
                  <div key={index} className="image-preview">
                    <img src={url} alt={`Aperçu ${index + 1}`} />
                  </div>
                ))}
              </div>
            </div>

            <div className="form-actions">
              <button
                type="button"
                className="cancel-button"
                onClick={() => {
                  resetForm();
                  setShowAddForm(false);
                  setEditingProduct(null);
                }}
              >
                Annuler
              </button>
              <button
                type="submit"
                className="submit-button"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Enregistrement...' : editingProduct ? 'Mettre à jour' : 'Ajouter'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Modal d'édition de produit */}
      <EditProductModal 
        product={editingProduct}
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setEditingProduct(null);
        }}
        onSave={handleSaveEditedProduct}
        businessId={businessData.id} // <-- Ajouté
      />

      <div className="products-list">
        {products.length === 0 ? (
          <p className="no-products-message">
            {isOwnBusiness 
              ? 'Vous n\'avez pas encore ajouté de produits ou services à votre catalogue. Cliquez sur "Ajouter un produit/service" pour commencer.'
              : 'Cette entreprise n\'a pas encore ajouté de produits ou services à son catalogue.'}
          </p>
        ) : (
          products.map(product => (
            <div key={product.id} className="product-card">
              <div className="product-image">
                {product.images && product.images.length > 0 ? (
                  <img 
                    src={product.images[0]} 
                    alt={product.name} 
                    onError={e => { e.currentTarget.src = require('../../assets/default-product.svg'); }}
                  />
                ) : (
                  <div className="no-image">
                    <img src={require('../../assets/default-product.svg')} alt="Produit par défaut" style={{width: '60%', opacity: 0.5}} />
                    <span>Pas d'image</span>
                  </div>
                )}
                {product.qualityBadge && product.qualityBadge !== QualityBadge.NONE && (
                  <div className={`quality-badge ${typeof product.qualityBadge === 'string' ? product.qualityBadge.toLowerCase() : ''}`}>
                    {product.qualityBadge}
                  </div>
                )}
              </div>
              <div className="product-info">
                <h3 className="product-name">{product.name}</h3>
                <p className="product-description">{product.description}</p>
                <div className="product-details">
                  <span className="product-price">{product.price.toLocaleString()} F CFA</span>
                  {product.negotiable && <span className="negociable-tag">Négociable</span>}
                  <span className="product-category">{product.category}</span>
                </div>
                {product.averageRating > 0 && (
                  <div className="product-rating">
                    <span className="rating-value">{product.averageRating.toFixed(1)}</span>
                    <span className="rating-stars">
                      {'★'.repeat(Math.round(product.averageRating))}
                      {'☆'.repeat(5 - Math.round(product.averageRating))}
                    </span>
                  </div>
                )}
              </div>
              {isOwnBusiness && (
                <div className="product-actions">
                  <button 
                    className="action-button view-button"
                    title="Voir le produit"
                    onClick={() => alert(`Voir les détails du produit: ${product.name}`)}
                  >
                    <Eye size={18} />
                  </button>
                  <button 
                    className="action-button edit-button"
                    title="Modifier le produit"
                    onClick={() => handleEditProduct(product)}
                  >
                    <Edit size={18} />
                  </button>
                  <button 
                    className="action-button delete-button"
                    title="Supprimer le produit"
                    onClick={() => handleDeleteProduct(product.id)}
                  >
                    <Trash2 size={18} />
                  </button>
                  <button 
                    className="action-button marketplace-button"
                    title="Publier sur le marketplace"
                    onClick={() => handlePublishToMarketplace(product.id)}
                  >
                    <ShoppingCart size={18} />
                  </button>
                  <button 
                    className="action-button reviews-button"
                    title="Demander des avis"
                    onClick={() => handleRequestReviews(product.id)}
                  >
                    <MessageCircle size={18} />
                  </button>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default CatalogueManager;
