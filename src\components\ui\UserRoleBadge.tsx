import React from 'react';
import { UserRole } from '../../types/index';
import { Building2, User } from 'lucide-react';

interface UserRoleBadgeProps {
  role: UserRole | string | null | undefined;
  className?: string;
  showIcon?: boolean;
}

/**
 * Composant robuste pour afficher les badges de rôle utilisateur
 * Solution définitive pour éviter les problèmes récurrents d'affichage "Membre"/"Entreprise"
 */
const UserRoleBadge: React.FC<UserRoleBadgeProps> = ({
  role,
  className = '',
  showIcon = true
}) => {
  // Normaliser et valider le rôle
  const normalizeRole = (inputRole: UserRole | string | null | undefined): UserRole => {
    if (!inputRole) {
      console.warn('UserRoleBadge: Rôle manquant, utilisation de STANDARD par défaut');
      return UserRole.STANDARD;
    }

    // Convertir en string et nettoyer
    const roleString = String(inputRole).toLowerCase().trim();

    // Mapping robuste de tous les cas possibles
    switch (roleString) {
      case 'business':
      case UserRole.BUSINESS:
        return UserRole.BUSINESS;
      case 'admin':
      case UserRole.ADMIN:
        return UserRole.ADMIN;
      case 'standard':
      case UserRole.STANDARD:
      case 'member':
      case 'user':
        return UserRole.STANDARD;
      default:
        console.warn(`UserRoleBadge: Rôle inconnu "${inputRole}", utilisation de STANDARD par défaut`);
        return UserRole.STANDARD;
    }
  };

  const validatedRole = normalizeRole(role);

  // Configuration des badges
  const getBadgeConfig = (userRole: UserRole) => {
    switch (userRole) {
      case UserRole.BUSINESS:
        return {
          label: 'Entreprise',
          icon: <Building2 size={12} />,
          bgColor: 'bg-blue-600',
          textColor: 'text-white',
          hoverColor: 'hover:bg-blue-700'
        };
      case UserRole.ADMIN:
        return {
          label: 'Admin',
          icon: <User size={12} />,
          bgColor: 'bg-red-600',
          textColor: 'text-white',
          hoverColor: 'hover:bg-red-700'
        };
      case UserRole.STANDARD:
      default:
        return {
          label: 'Membre',
          icon: <User size={12} />,
          bgColor: 'bg-gray-600',
          textColor: 'text-white',
          hoverColor: 'hover:bg-gray-700'
        };
    }
  };

  const config = getBadgeConfig(validatedRole);

  // Debug log en mode développement
  if (process.env.NODE_ENV === 'development') {
    console.log('UserRoleBadge:', {
      inputRole: role,
      inputType: typeof role,
      validatedRole: validatedRole,
      displayLabel: config.label
    });

    // Debug spécial pour les cas problématiques
    if (role && role !== validatedRole) {
      console.warn('⚠️ UserRoleBadge - Rôle transformé:', {
        original: role,
        transformed: validatedRole,
        reason: 'Normalisation'
      });
    }
  }

  return (
    <span
      className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-semibold transition-colors ${config.bgColor} ${config.textColor} ${config.hoverColor} ${className}`}
      data-role={validatedRole}
      data-original-role={role}
      title={`Rôle: ${config.label}`}
    >
      {showIcon && (
        <span className="mr-1">
          {config.icon}
        </span>
      )}
      {config.label}
    </span>
  );
};

export default UserRoleBadge;
