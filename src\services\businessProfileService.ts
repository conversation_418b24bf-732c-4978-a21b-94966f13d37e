import { supabase } from '../lib/supabase';
import { IBusinessUser } from '../types';

export interface BusinessProfileUpdateData {
  businessName?: string;
  businessDescription?: string;
  businessCategory?: string;
  businessAddress?: string;
  businessPhone?: string;
  businessEmail?: string;
  businessWebsite?: string;
  businessLicense?: string;
  foundedYear?: number;
  employeeCount?: number;
  coverPhotoUrl?: string;
}

/**
 * Service pour gérer les profils d'entreprise
 */
export class BusinessProfileService {
  
  /**
   * Récupère le profil complet d'une entreprise
   */
  static async getBusinessProfile(businessId: string): Promise<IBusinessUser | null> {
    try {
      // Récupérer les données du profil de base
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select(`
          id,
          username,
          email,
          profile_picture,
          cover_photo_url,
          role,
          status,
          created_at,
          updated_at,
          city,
          country,
          age,
          gender,
          post_count,
          comment_count,
          followers_count,
          following_count
        `)
        .eq('id', businessId)
        .single();

      if (profileError) throw profileError;

      // Récupérer les données spécifiques à l'entreprise
      const { data: businessData, error: businessError } = await supabase
        .from('business_profiles')
        .select(`
          business_name,
          business_status,
          business_description,
          business_category,
          business_address,
          business_phone,
          business_email,
          business_website,
          business_license,
          founded_year,
          employee_count,
          wallet,
          sales_count,
          average_rating,
          total_reviews,
          followers_count,
          following_count,
          cover_photo_url
        `)
        .eq('id', businessId)
        .single();

      if (businessError) throw businessError;

      // Combiner les données
      const businessUser: IBusinessUser = {
        id: profileData.id,
        username: profileData.username,
        email: profileData.email,
        profilePicture: profileData.profile_picture,
        coverPhotoUrl: businessData.cover_photo_url || profileData.cover_photo_url,
        role: profileData.role,
        status: profileData.status,
        createdAt: new Date(profileData.created_at),
        city: profileData.city,
        country: profileData.country,
        age: profileData.age,
        gender: profileData.gender,
        post_count: profileData.post_count || 0,
        comment_count: profileData.comment_count || 0,
        followers_count: businessData.followers_count || profileData.followers_count || 0,
        following_count: businessData.following_count || profileData.following_count || 0,
        recommendations_count: 0,
        businessName: businessData.business_name,
        businessStatus: businessData.business_status,
        businessDescription: businessData.business_description,
        businessCategory: businessData.business_category,
        businessAddress: businessData.business_address,
        businessPhone: businessData.business_phone,
        businessEmail: businessData.business_email,
        businessWebsite: businessData.business_website,
        businessLicense: businessData.business_license,
        foundedYear: businessData.founded_year,
        employeeCount: businessData.employee_count,
        wallet: businessData.wallet,
        salesCount: businessData.sales_count,
        averageRating: businessData.average_rating,
        totalReviews: businessData.total_reviews,
      };

      return businessUser;
    } catch (error) {
      console.error('Erreur lors de la récupération du profil business:', error);
      return null;
    }
  }

  /**
   * Met à jour le profil d'entreprise
   */
  static async updateBusinessProfile(
    businessId: string, 
    updates: BusinessProfileUpdateData
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Séparer les mises à jour pour la table profiles et business_profiles
      const profileUpdates: any = {};
      const businessUpdates: any = {};

      // Répartir les champs selon leur table
      if (updates.coverPhotoUrl !== undefined) {
        profileUpdates.cover_photo_url = updates.coverPhotoUrl;
        businessUpdates.cover_photo_url = updates.coverPhotoUrl;
      }

      // Champs spécifiques à business_profiles
      if (updates.businessName !== undefined) businessUpdates.business_name = updates.businessName;
      if (updates.businessDescription !== undefined) businessUpdates.business_description = updates.businessDescription;
      if (updates.businessCategory !== undefined) businessUpdates.business_category = updates.businessCategory;
      if (updates.businessAddress !== undefined) businessUpdates.business_address = updates.businessAddress;
      if (updates.businessPhone !== undefined) businessUpdates.business_phone = updates.businessPhone;
      if (updates.businessEmail !== undefined) businessUpdates.business_email = updates.businessEmail;
      if (updates.businessWebsite !== undefined) businessUpdates.business_website = updates.businessWebsite;
      if (updates.businessLicense !== undefined) businessUpdates.business_license = updates.businessLicense;
      if (updates.foundedYear !== undefined) businessUpdates.founded_year = updates.foundedYear;
      if (updates.employeeCount !== undefined) businessUpdates.employee_count = updates.employeeCount;

      // Mettre à jour la table profiles si nécessaire
      if (Object.keys(profileUpdates).length > 0) {
        const { error: profileError } = await supabase
          .from('profiles')
          .update({
            ...profileUpdates,
            updated_at: new Date().toISOString()
          })
          .eq('id', businessId);

        if (profileError) throw profileError;
      }

      // Mettre à jour la table business_profiles
      if (Object.keys(businessUpdates).length > 0) {
        const { error: businessError } = await supabase
          .from('business_profiles')
          .update({
            ...businessUpdates,
            updated_at: new Date().toISOString()
          })
          .eq('id', businessId);

        if (businessError) throw businessError;
      }

      return { success: true };
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour du profil business:', error);
      return { 
        success: false, 
        error: error.message || 'Erreur lors de la mise à jour du profil' 
      };
    }
  }

  /**
   * Crée un nouveau profil d'entreprise
   */
  static async createBusinessProfile(
    businessId: string,
    businessData: BusinessProfileUpdateData
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('business_profiles')
        .insert({
          id: businessId,
          business_name: businessData.businessName,
          business_description: businessData.businessDescription,
          business_category: businessData.businessCategory,
          business_address: businessData.businessAddress,
          business_phone: businessData.businessPhone,
          business_email: businessData.businessEmail,
          business_website: businessData.businessWebsite,
          business_license: businessData.businessLicense,
          founded_year: businessData.foundedYear,
          employee_count: businessData.employeeCount || 1,
          cover_photo_url: businessData.coverPhotoUrl,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) throw error;

      return { success: true };
    } catch (error: any) {
      console.error('Erreur lors de la création du profil business:', error);
      return { 
        success: false, 
        error: error.message || 'Erreur lors de la création du profil' 
      };
    }
  }

  /**
   * Vérifie si un profil d'entreprise existe
   */
  static async businessProfileExists(businessId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('business_profiles')
        .select('id')
        .eq('id', businessId)
        .single();

      return !error && !!data;
    } catch (error) {
      return false;
    }
  }

  /**
   * Met à jour les statistiques de l'entreprise
   */
  static async updateBusinessStats(
    businessId: string,
    stats: {
      salesCount?: number;
      averageRating?: number;
      totalReviews?: number;
      followersCount?: number;
      followingCount?: number;
    }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const updates: any = {};
      
      if (stats.salesCount !== undefined) updates.sales_count = stats.salesCount;
      if (stats.averageRating !== undefined) updates.average_rating = stats.averageRating;
      if (stats.totalReviews !== undefined) updates.total_reviews = stats.totalReviews;
      if (stats.followersCount !== undefined) updates.followers_count = stats.followersCount;
      if (stats.followingCount !== undefined) updates.following_count = stats.followingCount;

      const { error } = await supabase
        .from('business_profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', businessId);

      if (error) throw error;

      return { success: true };
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour des statistiques:', error);
      return { 
        success: false, 
        error: error.message || 'Erreur lors de la mise à jour des statistiques' 
      };
    }
  }

  /**
   * Recherche d'entreprises par catégorie
   */
  static async searchBusinessesByCategory(category: string): Promise<IBusinessUser[]> {
    try {
      const { data, error } = await supabase
        .from('business_profiles')
        .select(`
          *,
          profiles!inner(
            id,
            username,
            email,
            profile_picture,
            cover_photo_url,
            role,
            status,
            created_at,
            city,
            country
          )
        `)
        .eq('business_category', category)
        .eq('business_status', 'active');

      if (error) throw error;

      return data?.map(item => ({
        id: item.profiles.id,
        username: item.profiles.username,
        email: item.profiles.email,
        profilePicture: item.profiles.profile_picture,
        coverPhotoUrl: item.cover_photo_url || item.profiles.cover_photo_url,
        role: item.profiles.role,
        status: item.profiles.status,
        createdAt: new Date(item.profiles.created_at),
        city: item.profiles.city,
        country: item.profiles.country,
        post_count: 0,
        comment_count: 0,
        followers_count: item.followers_count || 0,
        following_count: item.following_count || 0,
        recommendations_count: 0,
        businessName: item.business_name,
        businessStatus: item.business_status,
        businessDescription: item.business_description,
        businessCategory: item.business_category,
        businessAddress: item.business_address,
        businessPhone: item.business_phone,
        businessEmail: item.business_email,
        businessWebsite: item.business_website,
        businessLicense: item.business_license,
        foundedYear: item.founded_year,
        employeeCount: item.employee_count,
        wallet: item.wallet,
        salesCount: item.sales_count,
        averageRating: item.average_rating,
        totalReviews: item.total_reviews,
      })) || [];
    } catch (error) {
      console.error('Erreur lors de la recherche d\'entreprises:', error);
      return [];
    }
  }
}
