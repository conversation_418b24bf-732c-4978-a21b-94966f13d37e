import React, { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

import { Star, Search, Filter, Trash2, Edit, ThumbsUp, ThumbsDown, TrendingUp, Award, BarChart3, Info } from 'lucide-react';
import Button from '../components/ui/Button';
import Card, { CardBody } from '../components/ui/Card';
import StarRating from '../components/ui/StarRating';
import { UserReviewsService, UserReview } from '../services/userReviewsService';
import '../styles/ReviewsPage.css';

const ReviewsPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [reviews, setReviews] = useState<UserReview[]>([]);
  const [stats, setStats] = useState({
    totalReviews: 0,
    averageRating: 0,
    positiveReviews: 0,
    neutralReviews: 0,
    negativeReviews: 0
  });
  const [filterRating, setFilterRating] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [editingReviewId, setEditingReviewId] = useState<string | null>(null);
  const [editReviewText, setEditReviewText] = useState<string>('');

  useEffect(() => {
    const loadUserReviews = async () => {
      if (currentUser) {
        setLoading(true);
        try {
          // Récupérer les avis de l'utilisateur
          const userReviews = await UserReviewsService.getUserReviews(currentUser.id);
          setReviews(userReviews);

          // Récupérer les statistiques
          const userStats = await UserReviewsService.getUserReviewsStats(currentUser.id);
          setStats(userStats);
        } catch (error) {
          console.error('Erreur lors du chargement des avis:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    loadUserReviews();
  }, [currentUser]);

  // Rediriger si l'utilisateur n'est pas connecté
  if (!currentUser) {
    return <Navigate to="/auth" replace />;
  }

  // Fonction renderStars supprimée - remplacée par le composant StarRating

  // Fonction pour gérer la suppression d'un avis
  const handleDeleteReview = async (reviewId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet avis ?')) {
      if (currentUser) {
        const success = await UserReviewsService.deleteUserReview(reviewId, currentUser.id);
        if (success) {
          setReviews(reviews.filter(review => review.id !== reviewId));
          // Mettre à jour les statistiques
          const userStats = await UserReviewsService.getUserReviewsStats(currentUser.id);
          setStats(userStats);
        } else {
          alert('Erreur lors de la suppression de l\'avis');
        }
      }
    }
  };

  // Fonction pour commencer l'édition d'un avis
  const handleEditReview = (review: UserReview) => {
    setEditingReviewId(review.id);
    setEditReviewText(review.comment);
    // Note non modifiable - pas besoin de la stocker dans l'état
  };

  // Fonction pour sauvegarder l'édition d'un avis
  const handleSaveEdit = async (reviewId: string) => {
    if (currentUser) {
      // Ne pas permettre la modification de la note, seulement le commentaire
      const success = await UserReviewsService.updateUserReview(reviewId, currentUser.id, {
        description: editReviewText
        // rating: editReviewRating // Note non modifiable
      });

      if (success) {
        setReviews(reviews.map(review => {
          if (review.id === reviewId) {
            return {
              ...review,
              comment: editReviewText
              // rating: editReviewRating // Note non modifiable
            };
          }
          return review;
        }));
        setEditingReviewId(null);

        // Mettre à jour les statistiques
        const userStats = await UserReviewsService.getUserReviewsStats(currentUser.id);
        setStats(userStats);
      } else {
        alert('Erreur lors de la mise à jour de l\'avis');
      }
    }
  };

  // Fonction pour annuler l'édition d'un avis
  const handleCancelEdit = () => {
    setEditingReviewId(null);
  };

  // Fonction pour gérer les likes/dislikes
  const handleLikeDislike = (reviewId: string, action: 'like' | 'dislike') => {
    setReviews(reviews.map(review => {
      if (review.id === reviewId) {
        // Si l'utilisateur a déjà liké/disliké, on annule son action
        if (action === 'like' && review.userHasLiked) {
          return {
            ...review,
            likes: review.likes - 1,
            userHasLiked: false
          };
        } else if (action === 'dislike' && review.userHasDisliked) {
          return {
            ...review,
            dislikes: review.dislikes - 1,
            userHasDisliked: false
          };
        }
        // Si l'utilisateur n'a pas encore liké/disliké ou change d'avis
        else if (action === 'like') {
          return {
            ...review,
            likes: review.likes + 1,
            dislikes: review.userHasDisliked ? review.dislikes - 1 : review.dislikes,
            userHasLiked: true,
            userHasDisliked: false
          };
        } else { // dislike
          return {
            ...review,
            dislikes: review.dislikes + 1,
            likes: review.userHasLiked ? review.likes - 1 : review.likes,
            userHasLiked: false,
            userHasDisliked: true
          };
        }
      }
      return review;
    }));
  };

  // Filtrer les avis en fonction des critères
  const filteredReviews = reviews.filter(review => {
    // Filtre par note
    if (filterRating !== 'all' && review.rating !== parseInt(filterRating)) {
      return false;
    }

    // Filtre par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        review.productName.toLowerCase().includes(query) ||
        review.businessName.toLowerCase().includes(query) ||
        review.comment.toLowerCase().includes(query)
      );
    }

    return true;
  });

  // Trier les avis par date (les plus récents d'abord)
  const sortedReviews = [...filteredReviews].sort((a, b) =>
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  // Afficher un message de chargement pendant la récupération des données
  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="sr-only">Chargement...</span>
          </div>
          <p className="mt-2">Chargement de vos avis...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="reviews-page">
      <h1 className="page-title">Mes avis</h1>

      {/* Message informatif sur la modification des notes */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-start">
          <Info className="text-blue-600 mr-3 mt-0.5 flex-shrink-0" size={20} />
          <div>
            <h3 className="text-blue-800 font-semibold mb-1">Politique de modification des avis</h3>
            <p className="text-blue-700 text-sm">
              Vous pouvez modifier le <strong>texte de vos commentaires</strong> à tout moment,
              mais les <strong>notes (étoiles) ne peuvent pas être modifiées</strong> pour préserver
              l'intégrité du système d'évaluation et la confiance des autres utilisateurs.
            </p>
          </div>
        </div>
      </div>

      {/* Statistiques des avis */}
      {stats.totalReviews > 0 && (
        <div className="reviews-stats mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardBody className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <BarChart3 className="text-blue-600" size={24} />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">{stats.totalReviews}</h3>
                <p className="text-sm text-gray-600">Avis publiés</p>
              </CardBody>
            </Card>

            <Card>
              <CardBody className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Star className="text-yellow-500" size={24} />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">{stats.averageRating.toFixed(1)}</h3>
                <p className="text-sm text-gray-600">Note moyenne</p>
              </CardBody>
            </Card>

            <Card>
              <CardBody className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <TrendingUp className="text-green-600" size={24} />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">{stats.positiveReviews}</h3>
                <p className="text-sm text-gray-600">Avis positifs</p>
              </CardBody>
            </Card>

            <Card>
              <CardBody className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Award className="text-purple-600" size={24} />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">{Math.round((stats.positiveReviews / stats.totalReviews) * 100)}%</h3>
                <p className="text-sm text-gray-600">Satisfaction</p>
              </CardBody>
            </Card>
          </div>
        </div>
      )}

      {/* Filtres et recherche */}
      <div className="reviews-filters-container">
        <div className="review-filter">
          <label><Filter size={16} /> Note:</label>
          <select value={filterRating} onChange={e => setFilterRating(e.target.value)}>
            <option value="all">Toutes les notes</option>
            <option value="5">5 étoiles</option>
            <option value="4">4 étoiles</option>
            <option value="3">3 étoiles</option>
            <option value="2">2 étoiles</option>
            <option value="1">1 étoile</option>
          </select>
        </div>

        <div className="review-search">
          <Search size={16} />
          <input
            type="text"
            placeholder="Rechercher un avis..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* Liste des avis */}
      <div className="reviews-list">
        {sortedReviews.length === 0 ? (
          <div className="no-reviews text-center py-12">
            <div className="mb-4">
              <Star className="mx-auto text-gray-400" size={48} />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun avis trouvé</h3>
            {searchQuery || filterRating !== 'all' ? (
              <p className="text-gray-600">Aucun avis ne correspond à vos critères de recherche.</p>
            ) : (
              <div>
                <p className="text-gray-600 mb-4">Vous n'avez pas encore publié d'avis.</p>
                <p className="text-gray-500">Partagez votre expérience sur les produits et services que vous avez utilisés !</p>
              </div>
            )}
          </div>
        ) : (
          sortedReviews.map(review => (
            <Card key={review.id} className="review-card">
              <CardBody>
                <div className="review-header">
                  <div className="product-info">
                    <img src={review.productImageUrl} alt={review.productName} className="product-image" />
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="product-name">{review.productName}</h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${UserReviewsService.getReviewTypeBadgeColor(review.type)}`}>
                          {UserReviewsService.getReviewTypeText(review.type)}
                        </span>
                      </div>
                      <p className="business-name">{review.businessName}</p>
                      {review.category && (
                        <p className="text-xs text-gray-500 mt-1">Catégorie: {review.category}</p>
                      )}
                    </div>
                  </div>

                  <div className="review-meta">
                    <div className="review-rating">
                      <StarRating
                        rating={review.rating}
                        size={16}
                        interactive={false}
                        showValue={false}
                      />
                    </div>
                    <p className="review-date">{UserReviewsService.formatDate(review.date)}</p>
                  </div>
                </div>

                {editingReviewId === review.id ? (
                  <div className="edit-review-form">
                    <div className="edit-rating">
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <StarRating
                          rating={review.rating}
                          size={24}
                          interactive={false}
                          showValue={true}
                          showLabel={true}
                          label="Note (non modifiable):"
                          className="mb-3"
                        />
                        <p className="text-xs text-gray-500 italic flex items-center">
                          🔒 Les notes ne peuvent pas être modifiées pour préserver l'intégrité du système d'évaluation
                        </p>
                      </div>
                    </div>
                    <div className="mt-4">
                      <label className="text-sm font-medium text-gray-700 mb-2 block">
                        Commentaire (modifiable):
                      </label>
                      <textarea
                        value={editReviewText}
                        onChange={e => setEditReviewText(e.target.value)}
                        rows={4}
                        className="edit-comment"
                        placeholder="Modifiez votre commentaire ici..."
                      />
                    </div>
                    <div className="edit-actions">
                      <Button variant="primary" onClick={() => handleSaveEdit(review.id)}>Enregistrer</Button>
                      <Button variant="secondary" onClick={handleCancelEdit}>Annuler</Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <p className="review-comment">{review.comment}</p>

                    {/* Tags */}
                    {review.tags && review.tags.length > 0 && (
                      <div className="review-tags mt-3 mb-3">
                        <div className="flex flex-wrap gap-2">
                          {review.tags.map((tag, index) => (
                            <span key={index} className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">
                              #{tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="review-actions">
                      <div className="review-feedback">
                        <button
                          className={`like-btn ${review.userHasLiked ? 'active' : ''}`}
                          onClick={() => handleLikeDislike(review.id, 'like')}
                        >
                          <ThumbsUp size={16} /> <span>{review.likes}</span>
                        </button>
                        <button
                          className={`dislike-btn ${review.userHasDisliked ? 'active' : ''}`}
                          onClick={() => handleLikeDislike(review.id, 'dislike')}
                        >
                          <ThumbsDown size={16} /> <span>{review.dislikes}</span>
                        </button>
                      </div>

                      <div className="review-edit-actions">
                        <Button
                          variant="secondary"
                          className="edit-btn"
                          onClick={() => handleEditReview(review)}
                        >
                          <Edit size={16} /> Modifier
                        </Button>
                        <Button
                          variant="danger"
                          className="delete-btn"
                          onClick={() => handleDeleteReview(review.id)}
                        >
                          <Trash2 size={16} /> Supprimer
                        </Button>
                      </div>
                    </div>
                  </>
                )}

                {/* Réponses aux avis */}
                {review.replies && review.replies.length > 0 && (
                  <div className="review-replies">
                    <h4>Réponses</h4>
                    {review.replies.map(reply => (
                      <div key={reply.id} className="review-reply">
                        <div className="reply-header">
                          <strong>{reply.businessName}</strong>
                          <span className="reply-date">{UserReviewsService.formatDate(reply.date)}</span>
                        </div>
                        <p className="reply-comment">{reply.comment}</p>
                      </div>
                    ))}
                  </div>
                )}
              </CardBody>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default ReviewsPage;
