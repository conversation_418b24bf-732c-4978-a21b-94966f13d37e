-- Migration pour créer le système d'interactions avec les offres
-- Cette migration ajoute les tables pour tracker les likes, commentaires et partages sur les offres

-- =====================================================
-- 1. TABLE DES LIKES SUR LES OFFRES
-- =====================================================
CREATE TABLE IF NOT EXISTS offer_likes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  offer_id text NOT NULL, -- ID de l'offre (peut être différent d'un UUID)
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  
  -- Métadonnées
  created_at TIMESTAMPTZ DEFAULT now(),
  
  -- Contraintes
  UNIQUE(offer_id, user_id) -- Un utilisateur ne peut liker qu'une fois par offre
);

-- =====================================================
-- 2. TABLE DES COMMENTAIRES SUR LES OFFRES
-- =====================================================
CREATE TABLE IF NOT EXISTS offer_comments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  offer_id text NOT NULL, -- ID de l'offre
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  parent_comment_id uuid REFERENCES offer_comments(id) ON DELETE CASCADE, -- Pour les réponses
  
  -- Contenu
  content TEXT NOT NULL CHECK (length(content) > 0 AND length(content) <= 1000),
  
  -- Modération
  is_approved BOOLEAN DEFAULT true,
  is_flagged BOOLEAN DEFAULT false,
  flagged_reason TEXT,
  
  -- Métadonnées
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- 3. TABLE DES PARTAGES D'OFFRES
-- =====================================================
CREATE TABLE IF NOT EXISTS offer_shares (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  offer_id text NOT NULL, -- ID de l'offre
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  
  -- Type de partage
  share_type VARCHAR(20) NOT NULL CHECK (share_type IN (
    'facebook', 'twitter', 'whatsapp', 'email', 'copy_link', 'internal'
  )),
  
  -- Métadonnées
  shared_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- 4. INDEXES POUR OPTIMISER LES PERFORMANCES
-- =====================================================

-- Index pour les likes d'offres
CREATE INDEX IF NOT EXISTS idx_offer_likes_offer_id ON offer_likes(offer_id);
CREATE INDEX IF NOT EXISTS idx_offer_likes_user_id ON offer_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_offer_likes_created_at ON offer_likes(created_at);

-- Index pour les commentaires d'offres
CREATE INDEX IF NOT EXISTS idx_offer_comments_offer_id ON offer_comments(offer_id);
CREATE INDEX IF NOT EXISTS idx_offer_comments_user_id ON offer_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_offer_comments_parent_id ON offer_comments(parent_comment_id);
CREATE INDEX IF NOT EXISTS idx_offer_comments_created_at ON offer_comments(created_at);

-- Index pour les partages d'offres
CREATE INDEX IF NOT EXISTS idx_offer_shares_offer_id ON offer_shares(offer_id);
CREATE INDEX IF NOT EXISTS idx_offer_shares_user_id ON offer_shares(user_id);
CREATE INDEX IF NOT EXISTS idx_offer_shares_type ON offer_shares(share_type);
CREATE INDEX IF NOT EXISTS idx_offer_shares_shared_at ON offer_shares(shared_at);

-- =====================================================
-- 5. POLITIQUES RLS (ROW LEVEL SECURITY)
-- =====================================================

-- Activer RLS sur toutes les tables
ALTER TABLE offer_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE offer_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE offer_shares ENABLE ROW LEVEL SECURITY;

-- Politiques pour offer_likes
CREATE POLICY "Les utilisateurs peuvent voir tous les likes d'offres" ON offer_likes
  FOR SELECT USING (true);

CREATE POLICY "Les utilisateurs connectés peuvent liker des offres" ON offer_likes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres likes d'offres" ON offer_likes
  FOR DELETE USING (auth.uid() = user_id);

-- Politiques pour offer_comments
CREATE POLICY "Les utilisateurs peuvent voir tous les commentaires d'offres approuvés" ON offer_comments
  FOR SELECT USING (is_approved = true OR auth.uid() = user_id);

CREATE POLICY "Les utilisateurs connectés peuvent commenter des offres" ON offer_comments
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent modifier leurs propres commentaires d'offres" ON offer_comments
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres commentaires d'offres" ON offer_comments
  FOR DELETE USING (auth.uid() = user_id);

-- Politiques pour offer_shares
CREATE POLICY "Les utilisateurs peuvent voir tous les partages d'offres" ON offer_shares
  FOR SELECT USING (true);

CREATE POLICY "Les utilisateurs connectés peuvent partager des offres" ON offer_shares
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- =====================================================
-- 6. FONCTIONS UTILITAIRES
-- =====================================================

-- Fonction pour obtenir le nombre de likes d'une offre
CREATE OR REPLACE FUNCTION get_offer_likes_count(offer_id_param text)
RETURNS integer AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::integer
    FROM offer_likes
    WHERE offer_id = offer_id_param
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour obtenir le nombre de commentaires d'une offre
CREATE OR REPLACE FUNCTION get_offer_comments_count(offer_id_param text)
RETURNS integer AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::integer
    FROM offer_comments
    WHERE offer_id = offer_id_param AND is_approved = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour obtenir le nombre de partages d'une offre
CREATE OR REPLACE FUNCTION get_offer_shares_count(offer_id_param text)
RETURNS integer AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::integer
    FROM offer_shares
    WHERE offer_id = offer_id_param
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour vérifier si un utilisateur a liké une offre
CREATE OR REPLACE FUNCTION has_user_liked_offer(offer_id_param text, user_id_param uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM offer_likes
    WHERE offer_id = offer_id_param AND user_id = user_id_param
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 7. TRIGGERS POUR MISE À JOUR AUTOMATIQUE
-- =====================================================

-- Trigger pour mettre à jour updated_at sur offer_comments
CREATE OR REPLACE FUNCTION update_offer_comments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_offer_comments_updated_at
  BEFORE UPDATE ON offer_comments
  FOR EACH ROW
  EXECUTE FUNCTION update_offer_comments_updated_at();

-- =====================================================
-- 8. COMMENTAIRES SUR LES TABLES
-- =====================================================

COMMENT ON TABLE offer_likes IS 'Table pour stocker les likes sur les offres et promotions';
COMMENT ON TABLE offer_comments IS 'Table pour stocker les commentaires sur les offres et promotions';
COMMENT ON TABLE offer_shares IS 'Table pour stocker les partages d''offres et promotions';

COMMENT ON COLUMN offer_likes.offer_id IS 'ID de l''offre (peut être différent d''un UUID)';
COMMENT ON COLUMN offer_comments.offer_id IS 'ID de l''offre commentée';
COMMENT ON COLUMN offer_shares.offer_id IS 'ID de l''offre partagée';
COMMENT ON COLUMN offer_shares.share_type IS 'Type de partage: facebook, twitter, whatsapp, email, copy_link, internal';

-- =====================================================
-- 9. DONNÉES DE TEST (OPTIONNEL)
-- =====================================================

-- Insérer quelques likes de test pour les offres existantes
-- (Seulement si des utilisateurs de test existent)
DO $$
DECLARE
  test_user_id uuid;
BEGIN
  -- Récupérer un utilisateur de test
  SELECT id INTO test_user_id FROM profiles WHERE email LIKE '%test%' LIMIT 1;
  
  IF test_user_id IS NOT NULL THEN
    -- Ajouter des likes de test
    INSERT INTO offer_likes (offer_id, user_id) VALUES
      ('00000000-0000-0000-0000-000000000001', test_user_id),
      ('00000000-0000-0000-0000-000000000002', test_user_id)
    ON CONFLICT (offer_id, user_id) DO NOTHING;
    
    -- Ajouter des partages de test
    INSERT INTO offer_shares (offer_id, user_id, share_type) VALUES
      ('00000000-0000-0000-0000-000000000001', test_user_id, 'facebook'),
      ('00000000-0000-0000-0000-000000000002', test_user_id, 'whatsapp')
    ON CONFLICT DO NOTHING;
  END IF;
END $$;
