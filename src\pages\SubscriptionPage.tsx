import React, { useState, useEffect } from 'react';
import {
  CreditCard, Calendar, CheckCircle, XCircle, AlertTriangle,
  Crown, Star, Zap, Shield, Gift, Clock, TrendingUp,
  Package, Users, BarChart3, Headphones, RefreshCw
} from 'lucide-react';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import Button from '../components/ui/Button';
import { useAuth } from '../context/AuthContext';
import { SubscriptionService, SubscriptionStatus } from '../services/subscriptionService';
import PaymentFlow from '../components/payment/PaymentFlow';
import PaymentReceipt from '../components/payment/PaymentReceipt';
import { PaymentReceipt as PaymentReceiptType } from '../services/paymentService';

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  duration: number; // en jours
  features: string[];
  icon: React.ReactNode;
  color: string;
  popular?: boolean;
  description: string;
}



const SubscriptionPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [showPaymentFlow, setShowPaymentFlow] = useState(false);
  const [selectedPlanForPayment, setSelectedPlanForPayment] = useState<SubscriptionPlan | null>(null);
  const [paymentReceipt, setPaymentReceipt] = useState<PaymentReceiptType | null>(null);
  const [showReceipt, setShowReceipt] = useState(false);

  // Plans d'abonnement disponibles
  const subscriptionPlans: SubscriptionPlan[] = [
    {
      id: 'free-trial',
      name: 'Essai Gratuit',
      price: 0,
      duration: 7,
      description: 'Découvrez toutes nos fonctionnalités pendant 7 jours',
      icon: <Gift className="text-green-600" size={24} />,
      color: 'green',
      features: [
        'Accès complet à l\'IA Conseiller',
        'Tableaux de bord interactifs',
        'Gestion des avis clients',
        'Notifications automatiques',
        'Support par email',
        'Jusqu\'à 100 avis analysés'
      ]
    },
    {
      id: 'starter',
      name: 'Starter',
      price: 15000, // F CFA
      duration: 30,
      description: 'Parfait pour les petites entreprises qui démarrent',
      icon: <Star className="text-blue-600" size={24} />,
      color: 'blue',
      features: [
        'Toutes les fonctionnalités de l\'essai',
        'Jusqu\'à 500 avis analysés/mois',
        'Recommandations IA illimitées',
        'Export des rapports PDF',
        'Support prioritaire',
        'Intégration API basique'
      ]
    },
    {
      id: 'professional',
      name: 'Professional',
      price: 35000, // F CFA
      duration: 30,
      description: 'Pour les entreprises en croissance',
      icon: <Crown className="text-purple-600" size={24} />,
      color: 'purple',
      popular: true,
      features: [
        'Toutes les fonctionnalités Starter',
        'Avis illimités',
        'Analyses prédictives avancées',
        'Tableaux de bord personnalisés',
        'Support téléphonique 24/7',
        'Intégration API complète',
        'Formation personnalisée',
        'Gestionnaire de compte dédié'
      ]
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: 75000, // F CFA
      duration: 30,
      description: 'Solution complète pour les grandes entreprises',
      icon: <Shield className="text-gold-600" size={24} />,
      color: 'yellow',
      features: [
        'Toutes les fonctionnalités Professional',
        'Multi-comptes et équipes',
        'Analyses personnalisées',
        'SLA garanti 99.9%',
        'Support dédié premium',
        'Intégrations sur mesure',
        'Formations d\'équipe',
        'Rapports exécutifs mensuels'
      ]
    }
  ];

  useEffect(() => {
    loadSubscriptionStatus();
  }, []);

  const loadSubscriptionStatus = async () => {
    setLoading(true);
    try {
      if (!currentUser?.id) {
        console.warn('Aucun utilisateur connecté');
        // Utiliser des données de test pour la démonstration
        const demoStatus: SubscriptionStatus = {
          isActive: true,
          plan: 'free-trial',
          planName: 'Essai Gratuit',
          startDate: new Date().toISOString(),
          endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          daysRemaining: 7,
          isTrialPeriod: true,
          autoRenew: false
        };
        setSubscriptionStatus(demoStatus);
        setLoading(false);
        return;
      }

      // Pour la démonstration, utiliser des données simulées
      console.log('🎯 Mode démonstration - Utilisation de données simulées');
      const demoStatus: SubscriptionStatus = {
        isActive: true,
        plan: 'free-trial',
        planName: 'Essai Gratuit',
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        daysRemaining: 7,
        isTrialPeriod: true,
        autoRenew: false
      };
      setSubscriptionStatus(demoStatus);

    } catch (error) {
      console.error('Erreur lors du chargement du statut d\'abonnement:', error);

      // Fallback avec données de test en cas d'erreur
      const fallbackStatus: SubscriptionStatus = {
        isActive: true,
        plan: 'free-trial',
        planName: 'Essai Gratuit',
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        daysRemaining: 7,
        isTrialPeriod: true,
        autoRenew: false
      };
      setSubscriptionStatus(fallbackStatus);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (planId: string) => {
    setSelectedPlan(planId);

    try {
      if (!currentUser?.id) {
        alert('Erreur : Utilisateur non connecté');
        return;
      }

      const plan = subscriptionPlans.find(p => p.id === planId);
      if (!plan) {
        alert('Plan non trouvé');
        return;
      }

      if (planId === 'free-trial') {
        // Pour l'essai gratuit, simuler l'activation
        console.log('🎁 Activation de l\'essai gratuit simulée');
        const demoStatus: SubscriptionStatus = {
          isActive: true,
          plan: 'free-trial',
          planName: 'Essai Gratuit',
          startDate: new Date().toISOString(),
          endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          daysRemaining: 7,
          isTrialPeriod: true,
          autoRenew: false
        };
        setSubscriptionStatus(demoStatus);
        alert('Essai gratuit de 7 jours activé avec succès !');
      } else {
        // Pour les plans payants, ouvrir le processus de paiement
        setSelectedPlanForPayment(plan);
        setShowPaymentFlow(true);
      }
    } catch (error) {
      console.error('Erreur lors de la souscription:', error);
      alert('Erreur lors de la souscription. Veuillez réessayer.');
    } finally {
      setSelectedPlan(null);
    }
  };

  const handlePaymentSuccess = (receipt: PaymentReceiptType) => {
    setPaymentReceipt(receipt);
    setShowPaymentFlow(false);
    setShowReceipt(true);
    // Recharger le statut d'abonnement
    loadSubscriptionStatus();
  };

  const handlePaymentCancel = () => {
    setShowPaymentFlow(false);
    setSelectedPlanForPayment(null);
  };

  const handleCloseReceipt = () => {
    setShowReceipt(false);
    setPaymentReceipt(null);
  };

  const getStatusColor = (status: SubscriptionStatus) => {
    if (!status.isActive) return 'red';
    if (status.isTrialPeriod) return 'blue';
    if (status.daysRemaining <= 3) return 'yellow';
    return 'green';
  };

  const getStatusIcon = (status: SubscriptionStatus) => {
    if (!status.isActive) return <XCircle className="text-red-600" size={24} />;
    if (status.isTrialPeriod) return <Clock className="text-blue-600" size={24} />;
    if (status.daysRemaining <= 3) return <AlertTriangle className="text-yellow-600" size={24} />;
    return <CheckCircle className="text-green-600" size={24} />;
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price) + ' F CFA';
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <RefreshCw className="mx-auto text-blue-600 mb-4 animate-spin" size={48} />
            <p className="text-gray-600">Chargement de votre abonnement...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* En-tête */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Mon Abonnement</h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Gérez votre abonnement et accédez aux fonctionnalités avancées de Customeroom Business
        </p>
      </div>

      {/* Statut actuel de l'abonnement */}
      {subscriptionStatus && (
        <Card className={`border-l-4 border-${getStatusColor(subscriptionStatus)}-500`}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getStatusIcon(subscriptionStatus)}
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    Statut de l'abonnement
                  </h2>
                  <p className="text-gray-600">
                    {subscriptionStatus.isTrialPeriod ? 'Période d\'essai gratuite' : 'Abonnement actif'}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-2xl font-bold text-${getStatusColor(subscriptionStatus)}-600`}>
                  {subscriptionStatus.daysRemaining} jours
                </div>
                <div className="text-sm text-gray-600">restants</div>
              </div>
            </div>
          </CardHeader>

          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Plan actuel</h4>
                <p className="text-gray-600 capitalize">
                  {subscriptionStatus.planName}
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Date d'expiration</h4>
                <p className="text-gray-600">
                  {new Date(subscriptionStatus.endDate).toLocaleDateString('fr-FR')}
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Renouvellement automatique</h4>
                <p className={`font-medium ${subscriptionStatus.autoRenew ? 'text-green-600' : 'text-gray-600'}`}>
                  {subscriptionStatus.autoRenew ? 'Activé' : 'Désactivé'}
                </p>
              </div>
            </div>

            {subscriptionStatus.isTrialPeriod && subscriptionStatus.daysRemaining <= 3 && (
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="text-yellow-600 mt-0.5" size={20} />
                  <div>
                    <h4 className="font-medium text-yellow-800">Votre essai gratuit expire bientôt !</h4>
                    <p className="text-yellow-700 text-sm mt-1">
                      Choisissez un plan pour continuer à bénéficier de toutes nos fonctionnalités.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardBody>
        </Card>
      )}

      {/* Plans d'abonnement */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">
          Choisissez votre plan
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {subscriptionPlans.map((plan) => (
            <Card
              key={plan.id}
              className={`relative ${plan.popular ? 'ring-2 ring-purple-500 shadow-lg' : ''} hover:shadow-lg transition-shadow`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                    Plus populaire
                  </span>
                </div>
              )}

              <CardHeader className="text-center">
                <div className="flex justify-center mb-3">
                  {plan.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900">{plan.name}</h3>
                <p className="text-gray-600 text-sm mt-1">{plan.description}</p>
                <div className="mt-4">
                  <div className="text-3xl font-bold text-gray-900">
                    {plan.price === 0 ? 'Gratuit' : formatPrice(plan.price)}
                  </div>
                  <div className="text-sm text-gray-600">
                    {plan.price === 0 ? '7 jours' : 'par mois'}
                  </div>
                </div>
              </CardHeader>

              <CardBody>
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <CheckCircle className="text-green-500 mt-0.5 flex-shrink-0" size={16} />
                      <span className="text-sm text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  onClick={() => handleSubscribe(plan.id)}
                  variant={plan.popular ? 'primary' : 'outline'}
                  className="w-full"
                  disabled={selectedPlan === plan.id || (subscriptionStatus?.plan === plan.id && subscriptionStatus.isActive)}
                >
                  {selectedPlan === plan.id ? (
                    <>
                      <RefreshCw className="animate-spin mr-2" size={16} />
                      Traitement...
                    </>
                  ) : subscriptionStatus?.plan === plan.id && subscriptionStatus.isActive ? (
                    'Plan actuel'
                  ) : plan.price === 0 ? (
                    'Commencer l\'essai'
                  ) : (
                    'Choisir ce plan'
                  )}
                </Button>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>

      {/* Avantages de l'abonnement */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold text-gray-900">
            Pourquoi choisir Customeroom Business ?
          </h2>
        </CardHeader>

        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <BarChart3 className="mx-auto text-blue-600 mb-3" size={32} />
              <h4 className="font-medium text-gray-900 mb-2">Analyses Avancées</h4>
              <p className="text-sm text-gray-600">
                Tableaux de bord interactifs et insights IA pour optimiser votre business
              </p>
            </div>

            <div className="text-center">
              <TrendingUp className="mx-auto text-green-600 mb-3" size={32} />
              <h4 className="font-medium text-gray-900 mb-2">Croissance Garantie</h4>
              <p className="text-sm text-gray-600">
                Recommandations personnalisées pour améliorer votre satisfaction client
              </p>
            </div>

            <div className="text-center">
              <Users className="mx-auto text-purple-600 mb-3" size={32} />
              <h4 className="font-medium text-gray-900 mb-2">Gestion Client</h4>
              <p className="text-sm text-gray-600">
                Centralisez et analysez tous vos avis clients en un seul endroit
              </p>
            </div>

            <div className="text-center">
              <Headphones className="mx-auto text-orange-600 mb-3" size={32} />
              <h4 className="font-medium text-gray-900 mb-2">Support Dédié</h4>
              <p className="text-sm text-gray-600">
                Équipe support réactive pour vous accompagner dans votre réussite
              </p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* FAQ ou informations supplémentaires */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold text-gray-900">
            Questions fréquentes
          </h2>
        </CardHeader>

        <CardBody>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">
                Puis-je changer de plan à tout moment ?
              </h4>
              <p className="text-gray-600 text-sm">
                Oui, vous pouvez upgrader ou downgrader votre plan à tout moment.
                Les changements prennent effet immédiatement.
              </p>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-2">
                Que se passe-t-il à la fin de mon essai gratuit ?
              </h4>
              <p className="text-gray-600 text-sm">
                À la fin de votre essai de 7 jours, vous devrez choisir un plan payant
                pour continuer à utiliser nos services. Aucun paiement automatique n'est effectué.
              </p>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-2">
                Puis-je annuler mon abonnement ?
              </h4>
              <p className="text-gray-600 text-sm">
                Oui, vous pouvez annuler votre abonnement à tout moment.
                Vous continuerez à avoir accès aux fonctionnalités jusqu'à la fin de votre période payée.
              </p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Modal de processus de paiement */}
      {showPaymentFlow && selectedPlanForPayment && currentUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <PaymentFlow
              planId={selectedPlanForPayment.id}
              planName={selectedPlanForPayment.name}
              amount={selectedPlanForPayment.price}
              businessId={currentUser.id}
              onSuccess={handlePaymentSuccess}
              onCancel={handlePaymentCancel}
            />
          </div>
        </div>
      )}

      {/* Modal de reçu de paiement */}
      {showReceipt && paymentReceipt && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <PaymentReceipt
              receipt={paymentReceipt}
              onClose={handleCloseReceipt}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionPage;
