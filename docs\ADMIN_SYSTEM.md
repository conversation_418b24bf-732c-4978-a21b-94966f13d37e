# Système d'Administration Customeroom

## Vue d'ensemble

Le système d'administration de Customeroom est une solution complète de gestion de plateforme avec des niveaux d'accès granulaires, un audit complet et des outils de modération avancés.

## 🏗️ Architecture

### Niveaux d'Administration

1. **Super Admin** (`super_admin`)
   - Accès complet à toutes les fonctionnalités
   - Gestion des autres administrateurs
   - Configuration système
   - Accès aux données sensibles

2. **Platform Admin** (`platform_admin`)
   - Gestion globale de la plateforme
   - Vue d'ensemble des utilisateurs et entreprises
   - Analytics et rapports
   - Gestion des paiements

3. **Content Moderator** (`content_moderator`)
   - Modération des publications
   - Gestion des signalements
   - Suspension/bannissement d'utilisateurs
   - Modération des commentaires

4. **Support Admin** (`support_admin`)
   - Gestion des tickets de support
   - Chat en direct avec les utilisateurs
   - Accès aux données utilisateur pour support
   - Gestion des alertes

5. **Business Admin** (`business_admin`)
   - Gestion spécifique des comptes entreprise
   - Vérification des entreprises
   - Gestion des abonnements business
   - Validation des documents

6. **User Admin** (`user_admin`)
   - Gestion des comptes utilisateurs
   - Modification des statuts utilisateur
   - Consultation de l'activité utilisateur
   - Gestion des profils

7. **Analytics Admin** (`analytics_admin`)
   - Accès aux analytics détaillées
   - Export de données
   - Génération de rapports personnalisés
   - Métriques avancées

## 🚀 Installation et Configuration

### 1. Migration de la Base de Données

```sql
-- Exécuter la migration
psql -f supabase/migrations/20250602000002_create_admin_system.sql
```

### 2. Création du Premier Super Admin

1. Créer un compte utilisateur normal via l'interface
2. Modifier le script `scripts/create-super-admin.sql` avec l'email du compte
3. Exécuter le script dans Supabase SQL Editor

```sql
-- Modifier cette ligne dans le script
target_email text := '<EMAIL>';
```

### 3. Accès au Tableau de Bord

- URL : `/admin`
- Accessible uniquement aux utilisateurs avec le rôle `admin`
- Redirection automatique si non autorisé

## 📊 Fonctionnalités Principales

### Tableau de Bord

- **Statistiques en temps réel** : Utilisateurs, entreprises, posts, commandes
- **Métriques d'activité** : Inscriptions du jour, admins actifs, alertes
- **Graphiques de croissance** : Évolution sur 30 jours
- **Actions rapides** : Accès direct aux sections principales

### Gestion des Utilisateurs

- **Liste complète** avec filtres et recherche
- **Actions en masse** : Suspension, vérification, changement de rôle
- **Profils détaillés** : Activité, statistiques, historique
- **Export de données** : CSV, Excel, JSON

### Gestion des Entreprises

- **Vérification des comptes** business
- **Validation des documents** officiels
- **Gestion des abonnements** et paiements
- **Statistiques business** détaillées

### Modération de Contenu

- **Queue de modération** avec priorités
- **Signalements utilisateur** centralisés
- **Actions de modération** : Approuver, rejeter, supprimer
- **Historique des actions** de modération

### Système d'Alertes

- **Alertes automatiques** du système
- **Priorités** : Low, Medium, High, Critical
- **Assignation** aux administrateurs
- **Suivi des résolutions** avec notes

### Audit et Logs

- **Traçabilité complète** de toutes les actions admin
- **Logs détaillés** avec métadonnées
- **Recherche et filtrage** avancés
- **Export des logs** pour conformité

## 🔐 Sécurité et Permissions

### Row Level Security (RLS)

Toutes les tables admin utilisent RLS pour sécuriser l'accès :

```sql
-- Exemple de politique
CREATE POLICY "Admins can view admin profiles based on level"
  ON admin_profiles
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM admin_profiles ap
      WHERE ap.user_id = auth.uid()
      AND ap.is_active = true
      AND (
        ap.admin_level = 'super_admin' OR
        ap.admin_level = 'platform_admin' OR
        ap.id = admin_profiles.id
      )
    )
  );
```

### Vérification des Permissions

```typescript
// Vérifier une permission spécifique
const hasPermission = await AdminService.hasPermission('MANAGE_USERS');

// Vérifier un niveau d'accès
const canAccess = useAdminPermissions().hasLevel(AdminLevel.PLATFORM_ADMIN);
```

### Sessions Administrateur

- **Tokens de session** sécurisés
- **Expiration automatique** configurable
- **Tracking IP et User-Agent**
- **Déconnexion forcée** possible

## 🛠️ Utilisation

### Hooks React

```typescript
// Hook principal pour les permissions
const { adminProfile, hasLevel, canAccess } = useAdminPermissions();

// Vérification de permission spécifique
const { hasPermission, loading } = usePermissionCheck('MANAGE_USERS');

// Accès par section
const sectionAccess = useSectionAccess();
```

### Protection des Routes

```typescript
// HOC pour protéger les composants
const ProtectedComponent = withAdminProtection(
  MyComponent,
  AdminLevel.PLATFORM_ADMIN
);
```

### Service d'Administration

```typescript
// Exécuter une action utilisateur
await AdminService.executeUserAction({
  userId: 'user-id',
  action: 'suspend',
  reason: 'Violation des conditions',
  duration: 7
});

// Créer une alerte
await AdminService.createAlert(
  'security',
  'Tentative de connexion suspecte',
  'Plusieurs tentatives échouées détectées',
  AlertPriority.HIGH
);
```

## 📈 Analytics et Rapports

### Métriques Disponibles

- **Croissance utilisateurs** : Inscriptions par jour/semaine/mois
- **Activité business** : Nouvelles entreprises, vérifications
- **Engagement** : Posts, commentaires, interactions
- **Revenus** : Abonnements, commissions, paiements
- **Performance** : Temps de réponse, erreurs système

### Export de Données

```typescript
// Exporter des données utilisateur
const exportRequest: ExportRequest = {
  type: 'users',
  format: 'xlsx',
  filters: { role: 'business' },
  dateRange: { start: '2024-01-01', end: '2024-12-31' }
};
```

## 🚨 Alertes et Monitoring

### Types d'Alertes

- **Système** : Erreurs, maintenance, mises à jour
- **Sécurité** : Tentatives de piratage, accès suspects
- **Utilisateur** : Signalements, violations, problèmes
- **Business** : Paiements échoués, documents expirés

### Notifications

- **Email** automatique pour alertes critiques
- **Dashboard** en temps réel
- **Mobile** push notifications (à implémenter)

## 🔧 Maintenance

### Tâches Régulières

1. **Nettoyage des logs** anciens (> 6 mois)
2. **Archivage des sessions** expirées
3. **Mise à jour des permissions** selon évolutions
4. **Backup des données** d'audit

### Scripts de Maintenance

```sql
-- Nettoyer les logs anciens
DELETE FROM admin_audit_log 
WHERE created_at < NOW() - INTERVAL '6 months';

-- Nettoyer les sessions expirées
DELETE FROM admin_sessions 
WHERE expires_at < NOW() AND is_active = false;
```

## 📝 Développement

### Ajouter un Nouveau Niveau Admin

1. Modifier l'enum `admin_level` dans la migration
2. Ajouter les permissions correspondantes
3. Mettre à jour les types TypeScript
4. Implémenter les vérifications d'accès

### Ajouter une Nouvelle Permission

```sql
INSERT INTO admin_permissions (
  permission_code,
  permission_name,
  description,
  category,
  required_level,
  is_sensitive
) VALUES (
  'NEW_PERMISSION',
  'Nouvelle Permission',
  'Description de la permission',
  'category',
  'required_level',
  false
);
```

## 🐛 Dépannage

### Problèmes Courants

1. **Accès refusé** : Vérifier le rôle et le niveau admin
2. **Permissions manquantes** : Vérifier les politiques RLS
3. **Sessions expirées** : Renouveler la session admin
4. **Logs manquants** : Vérifier les triggers d'audit

### Logs de Debug

```sql
-- Vérifier les permissions d'un admin
SELECT * FROM admin_profiles WHERE user_id = 'user-id';

-- Voir les dernières actions
SELECT * FROM admin_audit_log 
ORDER BY created_at DESC 
LIMIT 10;
```

## 📞 Support

Pour toute question ou problème avec le système d'administration :

1. Consulter cette documentation
2. Vérifier les logs d'audit
3. Contacter l'équipe de développement
4. Créer un ticket de support interne

---

**Version** : 1.0.0  
**Dernière mise à jour** : Décembre 2024  
**Auteur** : Équipe Customeroom
