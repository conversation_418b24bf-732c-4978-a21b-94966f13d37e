.reviews-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

/* Filtres et recherche */
.reviews-filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
  background-color: var(--bg-card);
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.review-filter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.review-filter label {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.review-filter select {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-input);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.review-search {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  position: relative;
}

.review-search svg {
  position: absolute;
  left: 0.75rem;
  color: var(--text-secondary);
}

.review-search input {
  padding: 0.5rem 0.5rem 0.5rem 2.25rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-input);
  color: var(--text-primary);
  font-size: 0.9rem;
  width: 100%;
}

/* Liste des avis */
.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.review-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.review-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.product-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

.product-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.business-name {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.review-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.review-rating {
  display: flex;
  gap: 0.25rem;
}

.star-filled {
  color: #ffc107;
}

.star-empty {
  color: #d1d5db;
}

.review-date {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.review-comment {
  margin-bottom: 1.5rem;
  line-height: 1.6;
  color: var(--text-primary);
}

/* Actions sur les avis */
.review-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.review-feedback {
  display: flex;
  gap: 1rem;
}

.like-btn, .dislike-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.like-btn:hover, .dislike-btn:hover {
  background-color: var(--bg-hover);
}

.like-btn.active {
  color: #4caf50;
}

.dislike-btn.active {
  color: #f44336;
}

.review-edit-actions {
  display: flex;
  gap: 0.5rem;
}

/* Formulaire d'édition */
.edit-review-form {
  margin-bottom: 1rem;
}

.edit-rating {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.star-rating-input {
  display: flex;
  gap: 0.5rem;
}

.star-rating-input svg {
  cursor: pointer;
}

.star-rating-display {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.star-rating-display svg {
  cursor: default;
  transition: all 0.2s ease;
}

.star-rating-display svg:hover {
  transform: scale(1.05);
}

/* Zone d'affichage des étoiles avec fond */
.star-rating-display.bg-gray-50 {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
}

/* Conteneur des étoiles */
.star-rating-display .flex.items-center {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Styles pour le composant StarRating */
.star-rating {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.star-rating .flex.items-center {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.star-rating svg {
  transition: all 0.2s ease;
}

.star-rating svg:hover {
  filter: brightness(1.1);
}

.edit-comment {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-input);
  color: var(--text-primary);
  font-size: 0.95rem;
  margin-bottom: 1rem;
  resize: vertical;
  transition: border-color 0.2s ease;
  font-family: inherit;
  line-height: 1.5;
}

.edit-comment:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.edit-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* Réponses aux avis */
.review-replies {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.review-replies h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.review-reply {
  background-color: var(--bg-secondary);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.reply-date {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.reply-comment {
  color: var(--text-primary);
  line-height: 1.5;
}

.no-reviews {
  text-align: center;
  padding: 3rem;
  background-color: var(--bg-card);
  border-radius: 8px;
  color: var(--text-secondary);
}

/* Responsive */
@media (max-width: 768px) {
  .reviews-page {
    padding: 1rem;
  }

  .review-header {
    flex-direction: column;
    gap: 1rem;
  }

  .review-meta {
    align-items: flex-start;
  }

  .review-actions {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .review-edit-actions {
    width: 100%;
    justify-content: space-between;
  }
}
