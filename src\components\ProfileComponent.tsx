import React, { useState, useEffect } from 'react';
import { useFollow } from '../context/FollowContext';
import { supabase } from '../lib/supabase';

interface ProfileComponentProps {
  userId: string;
}

interface UserProfile {
  id: string;
  username: string;
  profile_picture?: string;
}

const ProfileComponent: React.FC<ProfileComponentProps> = ({ userId }) => {
  const { isFollowing, followUser, unfollowUser, getFollowers, getFollowing } = useFollow();
  const [followers, setFollowers] = useState<string[]>([]);
  const [following, setFollowing] = useState<string[]>([]);
  const [followerProfiles, setFollowerProfiles] = useState<UserProfile[]>([]);
  const [followingProfiles, setFollowingProfiles] = useState<UserProfile[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  
  useEffect(() => {
    const loadFollowData = async () => {
      setIsLoading(true);
      try {
        const followersList = await getFollowers(userId);
        const followingList = await getFollowing(userId);
        
        setFollowers(followersList);
        setFollowing(followingList);
        
        // Récupérer les détails des profils des abonnés
        if (followersList.length > 0) {
          const { data: followerData, error: followerError } = await supabase
            .from('profiles')
            .select('id, username, profile_picture')
            .in('id', followersList);
            
          if (!followerError && followerData) {
            setFollowerProfiles(followerData);
          }
        }
        
        // Récupérer les détails des profils des abonnements
        if (followingList.length > 0) {
          const { data: followingData, error: followingError } = await supabase
            .from('profiles')
            .select('id, username, profile_picture')
            .in('id', followingList);
            
          if (!followingError && followingData) {
            setFollowingProfiles(followingData);
          }
        }
      } catch (error) {
        console.error("Erreur lors du chargement des données d'abonnement:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadFollowData();
  }, [userId, getFollowers, getFollowing]);
  
  const handleFollowToggle = async () => {
    try {
      if (isFollowing(userId)) {
        await unfollowUser(userId);
        // Mettre à jour l'état local après le désabonnement
        setFollowers(prev => prev.filter(id => id !== userId));
      } else {
        await followUser(userId);
        // Mettre à jour l'état local après l'abonnement
        setFollowers(prev => [...prev, userId]);
      }
    } catch (error) {
      console.error("Erreur lors de la modification de l'abonnement:", error);
    }
  };
  
  return (
    <div className="profile-container">
      <button 
        onClick={handleFollowToggle}
        className={`follow-button ${isFollowing(userId) ? 'following' : ''}`}
      >
        {isFollowing(userId) ? 'Se désabonner' : 'Suivre'}
      </button>
      
      <div className="followers-section">
        <h3>Abonnés: {followers.length}</h3>
        {isLoading ? (
          <p>Chargement des abonnés...</p>
        ) : (
          <div className="followers-list">
            {followerProfiles.length > 0 ? (
              followerProfiles.map(profile => (
                <div key={profile.id} className="follower-item">
                  <img 
                    src={profile.profile_picture || '/default-avatar.png'} 
                    alt={`${profile.username}`} 
                    className="follower-avatar"
                  />
                  <span className="follower-username">{profile.username}</span>
                </div>
              ))
            ) : (
              <p>Aucun abonné pour le moment</p>
            )}
          </div>
        )}
      </div>
      
      <div className="following-section">
        <h3>Abonnements: {following.length}</h3>
        {isLoading ? (
          <p>Chargement des abonnements...</p>
        ) : (
          <div className="following-list">
            {followingProfiles.length > 0 ? (
              followingProfiles.map(profile => (
                <div key={profile.id} className="following-item">
                  <img 
                    src={profile.profile_picture || '/default-avatar.png'} 
                    alt={`${profile.username}`} 
                    className="following-avatar"
                  />
                  <span className="following-username">{profile.username}</span>
                </div>
              ))
            ) : (
              <p>Aucun abonnement pour le moment</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfileComponent;