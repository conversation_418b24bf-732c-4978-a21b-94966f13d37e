import React, { useState, useEffect } from 'react';
import {
  LayoutDashboard,
  Users,
  Building2,
  FileText,
  AlertTriangle,
  Shield,
  BarChart3,
  CreditCard,
  Settings,
  LogOut,
  Menu,
  X
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { AdminService } from '../services/adminService';
import { AdminProfile, AdminLevel } from '../types/admin';
import { UserRole } from '../types';
import Card, { CardBody } from '../components/ui/Card';
import Button from '../components/ui/Button';
import AdminDashboard from '../components/admin/AdminDashboard';
import UserManagement from '../components/admin/UserManagement';
import AdminManagement from '../components/admin/AdminManagement';
import BusinessManagement from '../components/admin/BusinessManagement';
import ContentModeration from '../components/admin/ContentModeration';
import SystemAlerts from '../components/admin/SystemAlerts';
import AdvancedAnalytics from '../components/admin/AdvancedAnalytics';
import SystemSettings from '../components/admin/SystemSettings';
import PaymentManagement from '../components/admin/PaymentManagement';

type AdminSection = 'dashboard' | 'users' | 'businesses' | 'content' | 'alerts' | 'admins' | 'analytics' | 'payments' | 'settings';

const AdminDashboardPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [adminProfile, setAdminProfile] = useState<AdminProfile | null>(null);
  const [activeSection, setActiveSection] = useState<AdminSection>('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAdminProfile();
  }, []);

  const loadAdminProfile = async () => {
    try {
      setLoading(true);
      const profile = await AdminService.getCurrentAdminProfile();
      setAdminProfile(profile);
    } catch (error) {
      console.error('Erreur lors du chargement du profil admin:', error);
    } finally {
      setLoading(false);
    }
  };

  // Vérifier que l'utilisateur est admin
  if (!currentUser || currentUser.role !== UserRole.ADMIN) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <Card className="max-w-md w-full">
          <CardBody className="text-center p-8">
            <Shield className="mx-auto text-red-500 mb-4" size={48} />
            <h1 className="text-2xl font-bold text-red-600 mb-2">Accès Refusé</h1>
            <p className="text-gray-600 mb-4">
              Cette page est réservée aux administrateurs de la plateforme.
            </p>
            <Button onClick={() => window.history.back()}>
              Retour
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const menuItems = [
    {
      id: 'dashboard' as AdminSection,
      label: 'Tableau de Bord',
      icon: <LayoutDashboard size={20} />,
      requiredLevel: AdminLevel.SUPPORT_ADMIN
    },
    {
      id: 'users' as AdminSection,
      label: 'Gestion Utilisateurs',
      icon: <Users size={20} />,
      requiredLevel: AdminLevel.USER_ADMIN
    },
    {
      id: 'businesses' as AdminSection,
      label: 'Gestion Entreprises',
      icon: <Building2 size={20} />,
      requiredLevel: AdminLevel.BUSINESS_ADMIN
    },
    {
      id: 'content' as AdminSection,
      label: 'Modération Contenu',
      icon: <FileText size={20} />,
      requiredLevel: AdminLevel.CONTENT_MODERATOR
    },
    {
      id: 'alerts' as AdminSection,
      label: 'Alertes Système',
      icon: <AlertTriangle size={20} />,
      requiredLevel: AdminLevel.SUPPORT_ADMIN
    },
    {
      id: 'admins' as AdminSection,
      label: 'Gestion Admins',
      icon: <Shield size={20} />,
      requiredLevel: AdminLevel.PLATFORM_ADMIN
    },
    {
      id: 'analytics' as AdminSection,
      label: 'Analytics',
      icon: <BarChart3 size={20} />,
      requiredLevel: AdminLevel.ANALYTICS_ADMIN
    },
    {
      id: 'payments' as AdminSection,
      label: 'Gestion Paiements',
      icon: <CreditCard size={20} />,
      requiredLevel: AdminLevel.BUSINESS_ADMIN
    },
    {
      id: 'settings' as AdminSection,
      label: 'Paramètres',
      icon: <Settings size={20} />,
      requiredLevel: AdminLevel.PLATFORM_ADMIN
    }
  ];

  const hasAccess = (requiredLevel: AdminLevel): boolean => {
    if (!adminProfile) return false;
    
    // Super admin a accès à tout
    if (adminProfile.admin_level === AdminLevel.SUPER_ADMIN) return true;
    
    // Platform admin a accès à presque tout
    if (adminProfile.admin_level === AdminLevel.PLATFORM_ADMIN && 
        requiredLevel !== AdminLevel.SUPER_ADMIN) return true;
    
    // Vérifier le niveau exact
    return adminProfile.admin_level === requiredLevel;
  };

  const getAdminLevelBadge = (level: AdminLevel) => {
    const colors = {
      [AdminLevel.SUPER_ADMIN]: 'bg-red-100 text-red-800',
      [AdminLevel.PLATFORM_ADMIN]: 'bg-purple-100 text-purple-800',
      [AdminLevel.CONTENT_MODERATOR]: 'bg-blue-100 text-blue-800',
      [AdminLevel.SUPPORT_ADMIN]: 'bg-green-100 text-green-800',
      [AdminLevel.BUSINESS_ADMIN]: 'bg-orange-100 text-orange-800',
      [AdminLevel.USER_ADMIN]: 'bg-yellow-100 text-yellow-800',
      [AdminLevel.ANALYTICS_ADMIN]: 'bg-indigo-100 text-indigo-800'
    };

    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return <AdminDashboard onNavigateToSection={setActiveSection} />;
      case 'users':
        return <UserManagement />;
      case 'businesses':
        return <BusinessManagement />;
      case 'content':
        return <ContentModeration />;
      case 'alerts':
        return <SystemAlerts />;
      case 'admins':
        return <AdminManagement onNavigateToSection={setActiveSection} />;
      case 'analytics':
        return <AdvancedAnalytics />;
      case 'payments':
        return <PaymentManagement />;
      case 'settings':
        return <SystemSettings />;
      default:
        return <AdminDashboard onNavigateToSection={setActiveSection} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center">
            <Button
              variant="outline"
              size="sm"
              className="lg:hidden mr-3"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
            </Button>
            <h1 className="text-xl font-semibold text-gray-900">
              Administration Customeroom
            </h1>
          </div>
          
          {adminProfile && (
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-sm font-medium text-gray-900">
                  {adminProfile.user?.username}
                </div>
                <div className="text-xs text-gray-500">
                  {adminProfile.admin_code}
                </div>
              </div>
              <span className={`px-2 py-1 text-xs rounded-full ${getAdminLevelBadge(adminProfile.admin_level)}`}>
                {adminProfile.admin_level.replace('_', ' ')}
              </span>
              <img
                className="h-8 w-8 rounded-full object-cover"
                src={adminProfile.user?.profile_picture || '/default-avatar.png'}
                alt={adminProfile.user?.username}
              />
            </div>
          )}
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className={`
          fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
          lg:translate-x-0 lg:static lg:inset-0
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        `}>
          <div className="flex flex-col h-full pt-16 lg:pt-0">
            <nav className="flex-1 px-4 py-6 space-y-2">
              {menuItems.map((item) => {
                const hasItemAccess = hasAccess(item.requiredLevel);
                return (
                  <button
                    key={item.id}
                    onClick={() => {
                      if (hasItemAccess) {
                        setActiveSection(item.id);
                        setSidebarOpen(false);
                      }
                    }}
                    disabled={!hasItemAccess}
                    className={`
                      w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors
                      ${activeSection === item.id
                        ? 'bg-blue-100 text-blue-700'
                        : hasItemAccess
                          ? 'text-gray-700 hover:bg-gray-100'
                          : 'text-gray-400 cursor-not-allowed'
                      }
                    `}
                  >
                    {item.icon}
                    <span className="ml-3">{item.label}</span>
                    {!hasItemAccess && (
                      <Shield size={16} className="ml-auto text-gray-400" />
                    )}
                  </button>
                );
              })}
            </nav>
            
            <div className="p-4 border-t border-gray-200">
              <Button
                variant="outline"
                className="w-full justify-start text-red-600 hover:text-red-700"
                leftIcon={<LogOut size={16} />}
                onClick={() => {
                  // Déconnexion admin
                  window.location.href = '/';
                }}
              >
                Déconnexion
              </Button>
            </div>
          </div>
        </aside>

        {/* Overlay pour mobile */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main content */}
        <main className="flex-1 lg:ml-0">
          <div className="p-6">
            {renderContent()}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminDashboardPage;
