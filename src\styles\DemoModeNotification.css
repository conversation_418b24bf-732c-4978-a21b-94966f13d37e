/* Notification du mode démonstration */
.demo-mode-notification {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
  animation: slideInRight 0.3s ease-out;
}

.demo-notification-content {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  border: 1px solid #f59e0b;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  color: white;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  position: relative;
}

.demo-icon {
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  flex-shrink: 0;
}

.demo-message {
  flex: 1;
}

.demo-message h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.demo-message p {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  line-height: 1.4;
  opacity: 0.95;
}

.demo-instructions {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.8rem;
  line-height: 1.3;
}

.demo-instructions code {
  background: rgba(0, 0, 0, 0.2);
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  margin-left: 0.25rem;
}

.demo-close-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 6px;
  padding: 0.375rem;
  color: #92400e;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.demo-close-btn:hover {
  background: white;
  color: #78350f;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* Animation d'entrée */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .demo-mode-notification {
    position: fixed;
    top: 70px;
    left: 10px;
    right: 10px;
    max-width: none;
  }

  .demo-notification-content {
    padding: 0.875rem;
  }

  .demo-message h4 {
    font-size: 0.9rem;
  }

  .demo-message p {
    font-size: 0.8rem;
  }

  .demo-instructions {
    font-size: 0.75rem;
    flex-direction: column;
    gap: 0.375rem;
  }

  .demo-instructions code {
    font-size: 0.7rem;
    margin-left: 0;
    margin-top: 0.25rem;
    display: block;
  }
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
  .demo-notification-content {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    border-color: #d97706;
  }
}
