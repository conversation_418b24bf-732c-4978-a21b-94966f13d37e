-- Migration pour créer les tables de commentaires et notes du marketplace
-- Fichier: create_marketplace_comments_ratings.sql

-- Table pour les commentaires des produits du marketplace
CREATE TABLE IF NOT EXISTS marketcomen (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id VARCHAR(255) NOT NULL,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    comment TEXT NOT NULL,
    is_verified_purchase BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT TRUE,
    parent_comment_id UUID REFERENCES marketcomen(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Index pour améliorer les performances
    CONSTRAINT marketcomen_comment_length CHECK (char_length(comment) >= 10 AND char_length(comment) <= 1000)
);

-- Table pour les notes des produits du marketplace
CREATE TABLE IF NOT EXISTS marketrating (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id VARCHAR(255) NOT NULL,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    is_verified_purchase BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Un utilisateur ne peut noter qu'une seule fois par produit
    UNIQUE(product_id, user_id)
);

-- Index pour améliorer les performances des requêtes
CREATE INDEX IF NOT EXISTS idx_marketcomen_product_id ON marketcomen(product_id);
CREATE INDEX IF NOT EXISTS idx_marketcomen_user_id ON marketcomen(user_id);
CREATE INDEX IF NOT EXISTS idx_marketcomen_created_at ON marketcomen(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_marketcomen_parent_comment_id ON marketcomen(parent_comment_id);

CREATE INDEX IF NOT EXISTS idx_marketrating_product_id ON marketrating(product_id);
CREATE INDEX IF NOT EXISTS idx_marketrating_user_id ON marketrating(user_id);
CREATE INDEX IF NOT EXISTS idx_marketrating_created_at ON marketrating(created_at DESC);

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour mettre à jour automatiquement updated_at
CREATE TRIGGER update_marketcomen_updated_at 
    BEFORE UPDATE ON marketcomen 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_marketrating_updated_at 
    BEFORE UPDATE ON marketrating 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Politique de sécurité RLS (Row Level Security)
ALTER TABLE marketcomen ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketrating ENABLE ROW LEVEL SECURITY;

-- Politiques pour marketcomen
-- Lecture : tout le monde peut lire les commentaires approuvés
CREATE POLICY "Anyone can read approved comments" ON marketcomen
    FOR SELECT USING (is_approved = true);

-- Insertion : utilisateurs authentifiés peuvent créer des commentaires
CREATE POLICY "Authenticated users can create comments" ON marketcomen
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Mise à jour : utilisateurs peuvent modifier leurs propres commentaires
CREATE POLICY "Users can update their own comments" ON marketcomen
    FOR UPDATE USING (auth.uid() = user_id);

-- Suppression : utilisateurs peuvent supprimer leurs propres commentaires
CREATE POLICY "Users can delete their own comments" ON marketcomen
    FOR DELETE USING (auth.uid() = user_id);

-- Politiques pour marketrating
-- Lecture : tout le monde peut lire les notes
CREATE POLICY "Anyone can read ratings" ON marketrating
    FOR SELECT USING (true);

-- Insertion : utilisateurs authentifiés peuvent créer des notes
CREATE POLICY "Authenticated users can create ratings" ON marketrating
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Mise à jour : utilisateurs peuvent modifier leurs propres notes
CREATE POLICY "Users can update their own ratings" ON marketrating
    FOR UPDATE USING (auth.uid() = user_id);

-- Suppression : utilisateurs peuvent supprimer leurs propres notes
CREATE POLICY "Users can delete their own ratings" ON marketrating
    FOR DELETE USING (auth.uid() = user_id);

-- Vue pour obtenir les statistiques des produits
CREATE OR REPLACE VIEW product_stats AS
SELECT 
    product_id,
    COUNT(DISTINCT r.id) as total_ratings,
    ROUND(AVG(r.rating), 2) as average_rating,
    COUNT(DISTINCT c.id) as total_comments,
    COUNT(DISTINCT CASE WHEN r.is_verified_purchase THEN r.id END) as verified_ratings,
    COUNT(DISTINCT CASE WHEN c.is_verified_purchase THEN c.id END) as verified_comments
FROM marketrating r
FULL OUTER JOIN marketcomen c ON r.product_id = c.product_id
GROUP BY product_id;

-- Vue pour obtenir les commentaires avec les informations utilisateur
CREATE OR REPLACE VIEW comments_with_user_info AS
SELECT 
    c.id,
    c.product_id,
    c.user_id,
    c.comment,
    c.is_verified_purchase,
    c.is_approved,
    c.parent_comment_id,
    c.created_at,
    c.updated_at,
    u.username,
    u.profile_picture,
    r.rating as user_rating
FROM marketcomen c
LEFT JOIN users u ON c.user_id = u.id
LEFT JOIN marketrating r ON c.product_id = r.product_id AND c.user_id = r.user_id
WHERE c.is_approved = true
ORDER BY c.created_at DESC;

-- Fonction pour calculer la note moyenne d'un produit
CREATE OR REPLACE FUNCTION get_product_average_rating(p_product_id VARCHAR)
RETURNS DECIMAL AS $$
DECLARE
    avg_rating DECIMAL;
BEGIN
    SELECT ROUND(AVG(rating), 2) INTO avg_rating
    FROM marketrating
    WHERE product_id = p_product_id;
    
    RETURN COALESCE(avg_rating, 0);
END;
$$ LANGUAGE plpgsql;

-- Fonction pour obtenir le nombre total de commentaires d'un produit
CREATE OR REPLACE FUNCTION get_product_comment_count(p_product_id VARCHAR)
RETURNS INTEGER AS $$
DECLARE
    comment_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO comment_count
    FROM marketcomen
    WHERE product_id = p_product_id AND is_approved = true;
    
    RETURN COALESCE(comment_count, 0);
END;
$$ LANGUAGE plpgsql;

-- Insérer quelques données de test
INSERT INTO marketcomen (product_id, user_id, comment, is_verified_purchase) VALUES
('1', (SELECT id FROM auth.users LIMIT 1), 'Excellent smartphone, très satisfait de mon achat ! L''écran est magnifique et la batterie tient toute la journée.', true),
('1', (SELECT id FROM auth.users OFFSET 1 LIMIT 1), 'Bon produit mais un peu cher. La qualité est au rendez-vous cependant.', false),
('2', (SELECT id FROM auth.users LIMIT 1), 'Cette huile Olgane est fantastique ! Ma peau n''a jamais été aussi douce. Je recommande vivement.', true),
('2', (SELECT id FROM auth.users OFFSET 1 LIMIT 1), 'Produit naturel de qualité, livraison rapide. Très content de cet achat local.', true),
('3', (SELECT id FROM auth.users LIMIT 1), 'Crème hydratante correcte, texture agréable mais l''effet n''est pas extraordinaire.', false),
('5', (SELECT id FROM auth.users OFFSET 1 LIMIT 1), 'Savon au karité excellent ! Parfait pour les peaux sensibles comme la mienne.', true),
('6', (SELECT id FROM auth.users LIMIT 1), 'Ordinateur portable très performant, parfait pour le travail. Livraison soignée.', true);

INSERT INTO marketrating (product_id, user_id, rating, is_verified_purchase) VALUES
('1', (SELECT id FROM auth.users LIMIT 1), 5, true),
('1', (SELECT id FROM auth.users OFFSET 1 LIMIT 1), 4, false),
('2', (SELECT id FROM auth.users LIMIT 1), 5, true),
('2', (SELECT id FROM auth.users OFFSET 1 LIMIT 1), 5, true),
('3', (SELECT id FROM auth.users LIMIT 1), 3, false),
('5', (SELECT id FROM auth.users OFFSET 1 LIMIT 1), 5, true),
('6', (SELECT id FROM auth.users LIMIT 1), 5, true);

-- Commentaires de réponse (exemple)
INSERT INTO marketcomen (product_id, user_id, comment, parent_comment_id, is_verified_purchase) VALUES
('1', (SELECT id FROM auth.users OFFSET 2 LIMIT 1), 'Je suis d''accord, excellent rapport qualité-prix !', 
 (SELECT id FROM marketcomen WHERE product_id = '1' LIMIT 1), false);

COMMENT ON TABLE marketcomen IS 'Table des commentaires des produits du marketplace';
COMMENT ON TABLE marketrating IS 'Table des notes des produits du marketplace';
COMMENT ON VIEW product_stats IS 'Vue des statistiques agrégées des produits';
COMMENT ON VIEW comments_with_user_info IS 'Vue des commentaires avec informations utilisateur';
