/* Advanced Analytics Dashboard Styles */
.advanced-analytics-dashboard {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.analytics-title h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.analytics-title p {
  color: #6b7280;
  font-size: 0.875rem;
}

.analytics-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.time-range-selector {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 4px;
}

.time-range-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
}

.time-range-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.export-buttons {
  display: flex;
  gap: 0.5rem;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #e5e7eb;
  background: white;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
}

.export-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.export-btn.pdf {
  color: #dc2626;
  border-color: #fecaca;
}

.export-btn.excel {
  color: #059669;
  border-color: #a7f3d0;
}

.analytics-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 2rem;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-btn.active {
  color: #4f46e5;
  border-bottom-color: #4f46e5;
}

.tab-btn:hover:not(.active) {
  color: #374151;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.analytics-metric-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.analytics-metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 12px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.trend-up {
  color: #10b981;
}

.trend-down {
  color: #ef4444;
}

.trend-neutral {
  color: #6b7280;
}

.trend-positive {
  color: #10b981;
}

.trend-negative {
  color: #ef4444;
}

.metric-content h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.metric-description {
  font-size: 0.75rem;
  color: #9ca3af;
  margin: 0;
}

/* Chart Styles */
.analytics-chart-container {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chart-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.chart-legend {
  display: flex;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: end;
  justify-content: center;
}

.chart-bars {
  display: flex;
  gap: 1rem;
  align-items: end;
  height: 100%;
  width: 100%;
  justify-content: space-around;
}

.chart-bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.chart-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.chart-bars-container {
  display: flex;
  gap: 2px;
  align-items: end;
  height: 250px;
}

.chart-bar {
  width: 8px;
  border-radius: 2px 2px 0 0;
  min-height: 4px;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  opacity: 0.8;
}

/* Audience Insights */
.audience-insights {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
}

.audience-insights h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1.5rem;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.insight-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.age-distribution {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.age-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.age-label {
  font-size: 0.875rem;
  color: #6b7280;
  min-width: 60px;
}

.age-progress {
  flex: 1;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.age-fill {
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.age-percentage {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  min-width: 40px;
  text-align: right;
}

.interests-list, .geography-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.interest-item, .geography-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 8px;
}

.interest-name, .city-name {
  font-size: 0.875rem;
  color: #374151;
}

.interest-percentage, .city-percentage {
  font-size: 0.875rem;
  font-weight: 600;
  color: #4f46e5;
}

.city-progress {
  flex: 1;
  height: 4px;
  background: #f3f4f6;
  border-radius: 2px;
  margin: 0 1rem;
  overflow: hidden;
}

.city-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Interactive Charts Styles */
.interactive-charts-container {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.charts-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  background: #f8fafc;
  padding: 0.5rem;
  border-radius: 12px;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-btn:hover:not(.active) {
  background: rgba(255, 255, 255, 0.5);
  color: #374151;
}

/* Performance Chart */
.interactive-chart {
  background: #fafbfc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.chart-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.metric-selector {
  display: flex;
  gap: 0.25rem;
  background: white;
  padding: 0.25rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.metric-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
}

.metric-btn.active {
  background: #4f46e5;
  color: white;
}

.chart-container {
  display: flex;
  height: 400px;
  gap: 1rem;
}

.chart-y-axis {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 60px;
  padding-right: 1rem;
}

.y-axis-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-align: right;
}

.chart-area {
  flex: 1;
  position: relative;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.grid-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background: #f3f4f6;
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 100%;
  padding: 1rem;
  gap: 0.5rem;
}

.bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 80px;
  position: relative;
  cursor: pointer;
}

.bars-container {
  display: flex;
  gap: 2px;
  align-items: end;
  height: 320px;
  width: 100%;
  justify-content: center;
}

.bar {
  width: 12px;
  border-radius: 2px 2px 0 0;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 2px;
}

.bar:hover {
  opacity: 0.8;
  transform: scaleY(1.05);
}

.bar-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.5rem;
  text-align: center;
}

.chart-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
  white-space: nowrap;
  z-index: 10;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.chart-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: #1f2937;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.tooltip-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tooltip-color {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.tooltip-color.impressions { background: #4f46e5; }
.tooltip-color.clicks { background: #10b981; }
.tooltip-color.conversions { background: #f59e0b; }
.tooltip-color.spend { background: #ef4444; }

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Audience Chart */
.audience-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.audience-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.pie-chart-container {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.pie-chart {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    #4f46e5 0deg 72deg,
    #10b981 72deg 144deg,
    #f59e0b 144deg 216deg,
    #ef4444 216deg 288deg,
    #8b5cf6 288deg 360deg
  );
  animation: pieRotate 2s ease-out;
}

@keyframes pieRotate {
  from { transform: rotate(-90deg) scale(0); }
  to { transform: rotate(-90deg) scale(1); }
}

.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.pie-legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
}

.interests-bars, .geography-chart {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.interest-bar, .geo-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.interest-label, .geo-label {
  font-size: 0.875rem;
  color: #374151;
  min-width: 80px;
}

.interest-progress, .geo-bar {
  flex: 1;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.interest-fill, .geo-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 1s ease-out;
}

.interest-value, .geo-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #4f46e5;
  min-width: 40px;
  text-align: right;
}

/* Budget Chart */
.budget-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.budget-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.budget-bars {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.budget-bar {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.budget-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.campaign-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.budget-amounts {
  font-size: 0.875rem;
  color: #6b7280;
}

.budget-progress {
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.budget-used {
  height: 100%;
  border-radius: 4px;
  transition: width 1s ease-out;
}

.budget-percentage {
  font-size: 0.875rem;
  font-weight: 600;
  text-align: right;
  color: #4f46e5;
}

.timeline-chart {
  display: flex;
  align-items: end;
  gap: 1rem;
  height: 200px;
  padding: 1rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.timeline-bars {
  display: flex;
  gap: 4px;
  align-items: end;
  height: 150px;
}

.timeline-bar {
  width: 16px;
  border-radius: 2px 2px 0 0;
  transition: height 1s ease-out;
}

.timeline-bar.budget {
  background: #e5e7eb;
}

.timeline-bar.spent {
  background: #4f46e5;
}

.timeline-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .advanced-analytics-dashboard {
    padding: 1rem;
  }

  .analytics-header {
    flex-direction: column;
    align-items: stretch;
  }

  .analytics-controls {
    justify-content: space-between;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .chart-bars {
    gap: 0.5rem;
  }

  .chart-bar {
    width: 6px;
  }

  .interactive-charts-container {
    padding: 1rem;
  }

  .charts-navigation {
    flex-direction: column;
  }

  .chart-container {
    height: 300px;
  }

  .audience-grid {
    grid-template-columns: 1fr;
  }

  .budget-grid {
    grid-template-columns: 1fr;
  }

  .pie-chart-container {
    flex-direction: column;
    text-align: center;
  }
}
