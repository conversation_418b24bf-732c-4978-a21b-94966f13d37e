import React, { useState, useEffect } from 'react';
import { Bell, BellRing } from 'lucide-react';
import { BusinessNotificationService, AutoNotificationService } from '../../services/businessNotificationService';
import NotificationCenter from './NotificationCenter';

interface NotificationBellProps {
  businessId: string;
  className?: string;
}

const NotificationBell: React.FC<NotificationBellProps> = ({ businessId, className = '' }) => {
  const [unreadCount, setUnreadCount] = useState(0);
  const [isNotificationCenterOpen, setIsNotificationCenterOpen] = useState(false);
  const [hasNewNotification, setHasNewNotification] = useState(false);
  const [stopMonitoring, setStopMonitoring] = useState<(() => void) | null>(null);

  useEffect(() => {
    if (businessId) {
      loadUnreadCount();
      requestNotificationPermission();
      startAutoMonitoring();
    }

    return () => {
      if (stopMonitoring) {
        stopMonitoring();
      }
    };
  }, [businessId]);

  const loadUnreadCount = async () => {
    try {
      const count = await BusinessNotificationService.getUnreadCount(businessId);
      const previousCount = unreadCount;
      setUnreadCount(count);
      
      // Détecter une nouvelle notification
      if (count > previousCount && previousCount > 0) {
        setHasNewNotification(true);
        // Réinitialiser l'animation après 3 secondes
        setTimeout(() => setHasNewNotification(false), 3000);
      }
    } catch (error) {
      console.error('Erreur lors du chargement du compteur de notifications:', error);
    }
  };

  const requestNotificationPermission = async () => {
    try {
      await BusinessNotificationService.requestNotificationPermission();
    } catch (error) {
      console.error('Erreur lors de la demande de permission de notification:', error);
    }
  };

  const startAutoMonitoring = () => {
    try {
      // Démarrer la surveillance automatique toutes les 5 minutes (pour la démo)
      const stopFn = AutoNotificationService.startAutoMonitoring(businessId, 5);
      setStopMonitoring(() => stopFn);
      
      // Recharger le compteur toutes les 30 secondes
      const interval = setInterval(loadUnreadCount, 30000);
      
      return () => {
        clearInterval(interval);
        if (stopFn) stopFn();
      };
    } catch (error) {
      console.error('Erreur lors du démarrage de la surveillance:', error);
    }
  };

  const handleBellClick = () => {
    setIsNotificationCenterOpen(true);
    setHasNewNotification(false);
  };

  const handleNotificationCenterClose = () => {
    setIsNotificationCenterOpen(false);
    // Recharger le compteur après fermeture
    setTimeout(loadUnreadCount, 500);
  };

  return (
    <>
      <button
        onClick={handleBellClick}
        className={`relative p-2 text-gray-600 hover:text-gray-900 transition-colors ${className}`}
        title={`${unreadCount} notification${unreadCount !== 1 ? 's' : ''} non lue${unreadCount !== 1 ? 's' : ''}`}
      >
        {/* Icône de cloche */}
        <div className="relative">
          {hasNewNotification ? (
            <BellRing 
              size={24} 
              className="text-blue-600 animate-bounce" 
            />
          ) : (
            <Bell 
              size={24} 
              className={unreadCount > 0 ? 'text-blue-600' : 'text-gray-600'} 
            />
          )}
          
          {/* Badge de compteur */}
          {unreadCount > 0 && (
            <span className={`absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center ${
              hasNewNotification ? 'animate-pulse' : ''
            }`}>
              {unreadCount > 99 ? '99+' : unreadCount}
            </span>
          )}
          
          {/* Indicateur de nouvelle notification */}
          {hasNewNotification && (
            <span className="absolute -top-1 -right-1 bg-red-500 rounded-full h-3 w-3 animate-ping"></span>
          )}
        </div>
      </button>

      {/* Centre de notifications */}
      <NotificationCenter
        businessId={businessId}
        isOpen={isNotificationCenterOpen}
        onClose={handleNotificationCenterClose}
      />
    </>
  );
};

export default NotificationBell;
