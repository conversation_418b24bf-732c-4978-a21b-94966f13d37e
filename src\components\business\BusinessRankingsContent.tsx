import React, { useState, useEffect } from 'react';
import { Trophy, TrendingUp, TrendingDown, Star, Info, Search, Filter } from 'lucide-react';
import { IBusinessUser } from '../../types';
import { supabase } from '../../lib/supabase';
import { getProductsByBusinessId } from '../../services/productService';
import '../../styles/BusinessRankings.css';

// Types pour les classements
interface ProductRanking {
  id: string;
  productId: string;
  productName: string;
  category: string;
  currentRank: number;
  previousRank: number;
  totalInCategory: number;
  averageRating: number;
  totalReviews: number;
  totalSales: number;
  imageUrl?: string;
  isTopSeller?: boolean;
  isTopRated?: boolean;
  isTrending?: boolean;
}

interface BusinessRankingsContentProps {
  businessUser: IBusinessUser;
}

const BusinessRankingsContent: React.FC<BusinessRankingsContentProps> = ({ businessUser }) => {
  const [productRankings, setProductRankings] = useState<ProductRanking[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [categories, setCategories] = useState<string[]>([]);

  // Fonction pour calculer le classement d'un produit dans sa catégorie
  const calculateProductRanking = async (product: any, allProducts: any[]): Promise<ProductRanking> => {
    // Filtrer les produits de la même catégorie
    const categoryProducts = allProducts.filter(p => p.category === product.category);
    
    // Trier par note moyenne (décroissant) puis par nombre d'avis
    const sortedProducts = categoryProducts.sort((a, b) => {
      if (b.average_rating !== a.average_rating) {
        return (b.average_rating || 0) - (a.average_rating || 0);
      }
      return (b.review_count || 0) - (a.review_count || 0);
    });
    
    // Trouver la position du produit
    const currentRank = sortedProducts.findIndex(p => p.id === product.id) + 1;
    
    // Simuler un rang précédent (pour l'exemple, on ajoute/soustrait 1-3 positions)
    const previousRank = currentRank + Math.floor(Math.random() * 6) - 3;
    
    return {
      id: `RANK-${product.id}`,
      productId: product.id,
      productName: product.name,
      category: product.category,
      currentRank,
      previousRank: Math.max(1, previousRank),
      totalInCategory: categoryProducts.length,
      averageRating: product.average_rating || 0,
      totalReviews: product.review_count || 0,
      totalSales: Math.floor(Math.random() * 500) + 50, // Simulé pour l'exemple
      imageUrl: product.images?.[0] || `https://via.placeholder.com/300x300?text=${encodeURIComponent(product.name)}`,
      isTopSeller: currentRank <= 3,
      isTopRated: (product.average_rating || 0) >= 4.5,
      isTrending: currentRank < previousRank
    };
  };

  useEffect(() => {
    const fetchRealProductRankings = async () => {
      setLoading(true);
      try {
        // Récupérer les produits de l'entreprise
        const businessProducts = await getProductsByBusinessId(businessUser.id);
        
        // Récupérer tous les produits pour calculer les classements
        const { data: allProducts, error } = await supabase
          .from('products')
          .select(`
            id, name, category, average_rating, images,
            (SELECT COUNT(*) FROM posts WHERE posts.product_name = products.name) as review_count
          `);
        
        if (error) throw error;
        
        // Calculer les classements pour chaque produit de l'entreprise
        const rankings: ProductRanking[] = [];
        for (const product of businessProducts) {
          const ranking = await calculateProductRanking(product, allProducts || []);
          rankings.push(ranking);
        }
        
        setProductRankings(rankings);
        
        // Extraire les catégories uniques
        const uniqueCategories = Array.from(new Set(rankings.map(ranking => ranking.category)));
        setCategories(uniqueCategories);
        
      } catch (error) {
        console.error('Erreur lors de la récupération des classements:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRealProductRankings();
  }, [businessUser.id]);

  // Filtrer les classements
  const filteredRankings = productRankings.filter(ranking => {
    if (filterCategory !== 'all' && ranking.category !== filterCategory) {
      return false;
    }
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        ranking.productName.toLowerCase().includes(query) ||
        ranking.category.toLowerCase().includes(query)
      );
    }
    return true;
  });

  if (loading) {
    return (
      <div className="loading-state">
        <div className="loading-content">
          <div className="loading-spinner">
            <Trophy size={48} className="spinning-trophy" />
          </div>
          <h2>Analyse des classements en cours...</h2>
          <p>Calcul des positions de vos produits dans chaque catégorie</p>
          <div className="loading-steps">
            <div className="loading-step">📊 Récupération des données produits</div>
            <div className="loading-step">🏆 Calcul des classements par catégorie</div>
            <div className="loading-step">📈 Analyse des tendances</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="business-rankings-content">
      {/* En-tête avec statistiques */}
      <div className="rankings-header">
        <div>
          <h1 className="rankings-title">Classements de vos produits</h1>
          <p className="rankings-subtitle">Suivez la position de vos produits dans leur catégorie respective</p>
        </div>
      </div>

      {/* Statistiques */}
      <div className="rankings-stats">
        <div className="stat-card">
          <div className="stat-icon">
            <Trophy size={24} className="text-yellow-500" />
          </div>
          <div className="stat-content">
            <div className="stat-value">
              {productRankings.filter(r => r.currentRank <= 3).length}
            </div>
            <div className="stat-label">Produits dans le Top 3</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">
            <Trophy size={24} className="text-blue-500" />
          </div>
          <div className="stat-content">
            <div className="stat-value">
              {productRankings.filter(r => r.currentRank <= 10).length}
            </div>
            <div className="stat-label">Produits dans le Top 10</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">
            <TrendingUp size={24} className="text-green-500" />
          </div>
          <div className="stat-content">
            <div className="stat-value">
              {productRankings.filter(r => r.currentRank < r.previousRank).length}
            </div>
            <div className="stat-label">Produits en progression</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">
            <Star size={24} className="text-purple-500" />
          </div>
          <div className="stat-content">
            <div className="stat-value">
              {productRankings.reduce((sum, r) => sum + r.totalReviews, 0)}
            </div>
            <div className="stat-label">Avis totaux</div>
          </div>
        </div>
      </div>

      {/* Section des produits dans les différents Top */}
      <div className="top-products-section">
        <h2 className="section-title">Vos produits dans les classements</h2>
        
        {/* Top 3 */}
        <div className="top-category">
          <div className="top-category-header">
            <Trophy size={20} className="text-yellow-500" />
            <h3>Top 3 de leur catégorie</h3>
          </div>
          {productRankings.filter(r => r.currentRank <= 3).length > 0 ? (
            <div className="top-products-grid">
              {productRankings
                .filter(r => r.currentRank <= 3)
                .map(product => (
                  <div key={product.id} className="top-product-card">
                    <div className="top-product-rank">#{product.currentRank}</div>
                    <img src={product.imageUrl} alt={product.productName} className="top-product-image" />
                    <div className="top-product-info">
                      <h4>{product.productName}</h4>
                      <p>Catégorie: {product.category}</p>
                      <div className="top-product-rating">
                        <span>{product.averageRating.toFixed(1)}</span>
                        <span className="stars">{'★'.repeat(Math.round(product.averageRating))}</span>
                        <span className="review-count">({product.totalReviews})</span>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <p className="no-top-products">Aucun produit dans le Top 3 pour le moment.</p>
          )}
        </div>

        {/* Top 4-10 */}
        <div className="top-category">
          <div className="top-category-header">
            <Trophy size={20} className="text-blue-500" />
            <h3>Top 4-10 de leur catégorie</h3>
          </div>
          {productRankings.filter(r => r.currentRank > 3 && r.currentRank <= 10).length > 0 ? (
            <div className="top-products-grid">
              {productRankings
                .filter(r => r.currentRank > 3 && r.currentRank <= 10)
                .map(product => (
                  <div key={product.id} className="top-product-card">
                    <div className="top-product-rank">#{product.currentRank}</div>
                    <img src={product.imageUrl} alt={product.productName} className="top-product-image" />
                    <div className="top-product-info">
                      <h4>{product.productName}</h4>
                      <p>Catégorie: {product.category}</p>
                      <div className="top-product-rating">
                        <span>{product.averageRating.toFixed(1)}</span>
                        <span className="stars">{'★'.repeat(Math.round(product.averageRating))}</span>
                        <span className="review-count">({product.totalReviews})</span>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <p className="no-top-products">Aucun produit dans le Top 4-10 pour le moment.</p>
          )}
        </div>

        {/* Produits en forte progression */}
        <div className="top-category">
          <div className="top-category-header">
            <TrendingUp size={20} className="text-green-500" />
            <h3>Produits en forte progression</h3>
          </div>
          {productRankings.filter(r => (r.previousRank - r.currentRank) >= 3).length > 0 ? (
            <div className="top-products-grid">
              {productRankings
                .filter(r => (r.previousRank - r.currentRank) >= 3)
                .sort((a, b) => (b.previousRank - b.currentRank) - (a.previousRank - a.currentRank))
                .slice(0, 4)
                .map(product => (
                  <div key={product.id} className="top-product-card trending">
                    <div className="top-product-rank">#{product.currentRank}</div>
                    <div className="trend-indicator">+{product.previousRank - product.currentRank}</div>
                    <img src={product.imageUrl} alt={product.productName} className="top-product-image" />
                    <div className="top-product-info">
                      <h4>{product.productName}</h4>
                      <p>Catégorie: {product.category}</p>
                      <div className="top-product-trend">
                        <TrendingUp size={16} />
                        <span>De #{product.previousRank} à #{product.currentRank}</span>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <p className="no-top-products">Aucun produit en forte progression pour le moment.</p>
          )}
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="rankings-filters">
        <div className="search-container">
          <Search size={20} className="search-icon" />
          <input
            type="text"
            placeholder="Rechercher un produit..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-container">
          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
            className="filter-select"
          >
            <option value="all">Toutes les catégories</option>
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Liste détaillée des produits */}
      {filteredRankings.length === 0 ? (
        <div className="no-rankings">
          <Trophy size={48} />
          <p>Aucun produit ne correspond à vos critères de recherche.</p>
        </div>
      ) : (
        <div className="rankings-grid">
          {filteredRankings.map((ranking) => (
            <div key={ranking.id} className="ranking-card">
              <div className="ranking-image">
                <img src={ranking.imageUrl} alt={ranking.productName} />
                <div className={`ranking-badge ${ranking.currentRank <= 3 ? 'top-3' : ''}`}>
                  #{ranking.currentRank}
                </div>
              </div>
              <div className="ranking-content">
                <h3 className="ranking-product-name">{ranking.productName}</h3>
                <div className="ranking-category">{ranking.category}</div>

                <div className="ranking-details">
                  <div className="ranking-detail-item">
                    <div className="detail-label">Classement</div>
                    <div className="detail-value">
                      <span className="rank-position">#{ranking.currentRank}</span>
                      <span className={`rank-trend ${ranking.currentRank < ranking.previousRank ? 'trend-up' : ranking.currentRank > ranking.previousRank ? 'trend-down' : 'trend-stable'}`}>
                        {ranking.currentRank < ranking.previousRank ? (
                          <>
                            <TrendingUp size={14} />
                            +{ranking.previousRank - ranking.currentRank}
                          </>
                        ) : ranking.currentRank > ranking.previousRank ? (
                          <>
                            <TrendingDown size={14} />
                            -{ranking.currentRank - ranking.previousRank}
                          </>
                        ) : (
                          '='
                        )}
                      </span>
                    </div>
                  </div>

                  <div className="ranking-detail-item">
                    <div className="detail-label">Évaluation</div>
                    <div className="detail-value rating">
                      <span className="rating-value">{ranking.averageRating.toFixed(1)}</span>
                      <span className="rating-stars">
                        {'★'.repeat(Math.round(ranking.averageRating))}
                        {'☆'.repeat(5 - Math.round(ranking.averageRating))}
                      </span>
                      <span className="rating-count">({ranking.totalReviews})</span>
                    </div>
                  </div>

                  <div className="ranking-detail-item">
                    <div className="detail-label">Position dans la catégorie</div>
                    <div className="detail-value">{ranking.currentRank} sur {ranking.totalInCategory}</div>
                  </div>
                </div>

                <div className="ranking-progress">
                  <div className="progress-label">
                    <span>Performance</span>
                    <span>{Math.round(100 - ((ranking.currentRank / ranking.totalInCategory) * 100))}%</span>
                  </div>
                  <div className="progress-bar-container">
                    <div
                      className="progress-bar"
                      style={{ width: `${100 - ((ranking.currentRank / ranking.totalInCategory) * 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default BusinessRankingsContent;
