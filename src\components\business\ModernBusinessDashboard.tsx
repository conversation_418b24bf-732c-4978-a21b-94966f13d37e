import React, { useState, useEffect } from 'react';
import {
  TrendingUp, TrendingDown, Users, ShoppingBag, Star, DollarSign,
  Package, MessageSquare, AlertTriangle, CheckCircle, Clock, Target,
  BarChart3, PieChart, Calendar, Bell, Plus, ArrowRight, Eye,
  RefreshCw, Download, Settings, Zap, Award, Globe, Brain, Activity,
  CreditCard, FileText, Filter, Search, MoreVertical, ExternalLink,
  ArrowUpRight, ArrowDownRight, Minus, TrendingUpIcon, BarChart2,
  LineChart, PieChartIcon, Users2, ShoppingCart, Heart, MessageCircle,
  Bookmark, Share2, Edit3, Trash2, MoreHorizontal, ChevronDown,
  ChevronUp, Info, AlertCircle, CheckCircle2, XCircle, Clock3,
  Calendar as CalendarIcon, MapPin, Phone, Mail, Globe2, Instagram,
  Facebook, Twitter, Linkedin, Youtube, Camera, Upload, X, Save
} from 'lucide-react';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';
import { useAuth } from '../../context/AuthContext';
import { formatAmount } from '../../utils/formatUtils';
import {
  BusinessDashboardService,
  DashboardMetrics,
  RecentActivity,
  BusinessAlert
} from '../../services/businessDashboardService';
import {
  LineChart as RechartsLineChart,
  BarChart as RechartsBarChart,
  PieChart as RechartsPieChart,
  AreaChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
  Area,
  Line,
  Bar,
  Pie
} from 'recharts';
import DashboardExport from './DashboardExport';
import ActivityModal from './ActivityModal';
import '../../styles/ModernBusinessDashboard.css';

interface ModernBusinessDashboardProps {
  businessId: string;
}

// Interface pour les données des graphiques
interface ChartDataPoint {
  date: string;
  ventes: number;
  commandes: number;
  visiteurs: number;
}

interface CategoryData {
  name: string;
  value: number;
  color: string;
}

interface TopProduct {
  name: string;
  sales: number;
  revenue: number;
  growth: number;
  image: string;
}

const ModernBusinessDashboard: React.FC<ModernBusinessDashboardProps> = ({ businessId }) => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [alerts, setAlerts] = useState<BusinessAlert[]>([]);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [selectedChart, setSelectedChart] = useState('sales');
  const [showObjectives, setShowObjectives] = useState(true);
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [categoryData, setCategoryData] = useState<CategoryData[]>([]);
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [showAllActivity, setShowAllActivity] = useState(false);
  const [activityFilter, setActivityFilter] = useState<'all' | 'orders' | 'reviews' | 'products' | 'messages'>('all');
  const [showActivityModal, setShowActivityModal] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, [businessId, selectedPeriod]);

  const loadDashboardData = async () => {
    setLoading(true);
    console.log('🚀 Chargement du dashboard pour businessId:', businessId);
    try {
      const [metricsData, alertsData, activityData, chartDataResult, categoryDataResult, topProductsData] = await Promise.all([
        BusinessDashboardService.getMetrics(businessId, selectedPeriod),
        BusinessDashboardService.getAlerts(businessId),
        BusinessDashboardService.getRecentActivity(businessId, 20),
        BusinessDashboardService.getChartData(businessId, selectedPeriod),
        BusinessDashboardService.getCategoryData(businessId),
        BusinessDashboardService.getTopProducts(businessId)
      ]);

      console.log('📊 Données reçues - Activités:', activityData?.length || 0);
      setMetrics(metricsData);
      setAlerts(alertsData);
      setRecentActivity(activityData);
      setChartData(chartDataResult);
      setCategoryData(categoryDataResult);
      setTopProducts(topProductsData);
    } catch (error) {
      console.error('❌ Erreur lors du chargement du dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Il y a moins d\'une heure';
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `Il y a ${diffInDays}j`;
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order':
        return <ShoppingBag size={16} className="text-green-600" />;
      case 'review':
        return <Star size={16} className="text-yellow-600" />;
      case 'product':
        return <Package size={16} className="text-blue-600" />;
      case 'message':
        return <MessageSquare size={16} className="text-purple-600" />;
      default:
        return <Activity size={16} className="text-gray-600" />;
    }
  };

  const getActivityBgColor = (type: string) => {
    switch (type) {
      case 'order':
        return 'bg-green-100';
      case 'review':
        return 'bg-yellow-100';
      case 'product':
        return 'bg-blue-100';
      case 'message':
        return 'bg-purple-100';
      default:
        return 'bg-gray-100';
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      success: { bg: 'bg-green-100', text: 'text-green-800', label: 'Succès' },
      warning: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Attention' },
      info: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Info' },
      error: { bg: 'bg-red-100', text: 'text-red-800', label: 'Erreur' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.info;

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const filteredActivity = recentActivity.filter(activity =>
    activityFilter === 'all' || activity.type === activityFilter
  );

  const displayedActivity = showAllActivity ? filteredActivity : filteredActivity.slice(0, 5);

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <ArrowUpRight size={16} className="text-green-500" />;
    if (growth < 0) return <ArrowDownRight size={16} className="text-red-500" />;
    return <Minus size={16} className="text-gray-400" />;
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-600';
    if (growth < 0) return 'text-red-600';
    return 'text-gray-500';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw size={48} className="animate-spin text-blue-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Chargement du tableau de bord</h3>
          <p className="text-gray-600">Récupération des données de votre entreprise...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header moderne avec gradient */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white">
        <div className="px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">Tableau de bord</h1>
              <p className="text-blue-100">Bienvenue, {currentUser?.username || 'Entreprise'}</p>
              <p className="text-blue-200 text-sm mt-1">Vue d'ensemble de votre activité business</p>
            </div>
            <div className="flex items-center space-x-3">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/30"
              >
                <option value="7d" className="text-gray-900">7 derniers jours</option>
                <option value="30d" className="text-gray-900">30 derniers jours</option>
                <option value="90d" className="text-gray-900">90 derniers jours</option>
                <option value="1y" className="text-gray-900">1 an</option>
              </select>
              <Button
                onClick={loadDashboardData}
                disabled={loading}
                className="bg-white/10 hover:bg-white/20 border-white/20 text-white"
                size="sm"
              >
                <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
                <span className="ml-2">Actualiser</span>
              </Button>
              <DashboardExport
                metrics={metrics}
                businessName={currentUser?.businessName || 'Mon Entreprise'}
                period={selectedPeriod}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="px-6 py-6 space-y-6">
        {/* Alertes importantes */}
        {alerts.length > 0 && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="text-amber-500" size={20} />
                <h3 className="text-lg font-semibold text-gray-900">Alertes importantes</h3>
              </div>
              <span className="bg-amber-100 text-amber-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                {alerts.length} alerte{alerts.length > 1 ? 's' : ''}
              </span>
            </div>
            <div className="space-y-3">
              {alerts.slice(0, 3).map(alert => (
                <div key={alert.id} className="flex items-start space-x-3 p-3 bg-amber-50 rounded-lg border border-amber-200">
                  <AlertTriangle className="text-amber-500 mt-0.5" size={16} />
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{alert.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{alert.message}</p>
                  </div>
                  <span className="text-xs text-amber-600">{formatTimeAgo(alert.timestamp)}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Métriques principales - Grid moderne */}
        {metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
            {/* Chiffre d'affaires */}
            <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <DollarSign size={24} className="text-white/80" />
                  <div className="flex items-center space-x-1">
                    {getGrowthIcon(metrics.revenueGrowth)}
                    <span className="text-sm font-medium text-white/90">
                      {metrics.revenueGrowth > 0 ? '+' : ''}{metrics.revenueGrowth}%
                    </span>
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">{formatAmount(metrics.totalRevenue)}</h3>
                  <p className="text-green-100 text-sm">Chiffre d'affaires</p>
                </div>
              </CardBody>
            </Card>

            {/* Commandes */}
            <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <ShoppingBag size={24} className="text-white/80" />
                  <div className="flex items-center space-x-1">
                    {getGrowthIcon(metrics.ordersGrowth)}
                    <span className="text-sm font-medium text-white/90">
                      {metrics.ordersGrowth > 0 ? '+' : ''}{metrics.ordersGrowth}%
                    </span>
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">{metrics.totalOrders}</h3>
                  <p className="text-blue-100 text-sm">Commandes</p>
                </div>
              </CardBody>
            </Card>

            {/* Produits */}
            <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <Package size={24} className="text-white/80" />
                  <div className="flex items-center space-x-1">
                    {getGrowthIcon(metrics.productsGrowth)}
                    <span className="text-sm font-medium text-white/90">
                      {metrics.productsGrowth > 0 ? '+' : ''}{metrics.productsGrowth}%
                    </span>
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">{metrics.totalProducts}</h3>
                  <p className="text-purple-100 text-sm">Produits</p>
                </div>
              </CardBody>
            </Card>

            {/* Note moyenne */}
            <Card className="bg-gradient-to-br from-amber-500 to-amber-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <Star size={24} className="text-white/80" />
                  <div className="flex items-center space-x-1">
                    {getGrowthIcon(metrics.ratingGrowth)}
                    <span className="text-sm font-medium text-white/90">
                      {metrics.ratingGrowth > 0 ? '+' : ''}{metrics.ratingGrowth}%
                    </span>
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">{metrics.averageRating.toFixed(1)}/5</h3>
                  <p className="text-amber-100 text-sm">Note moyenne</p>
                </div>
              </CardBody>
            </Card>

            {/* Avis clients */}
            <Card className="bg-gradient-to-br from-indigo-500 to-indigo-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <MessageSquare size={24} className="text-white/80" />
                  <div className="flex items-center space-x-1">
                    {getGrowthIcon(metrics.reviewsGrowth)}
                    <span className="text-sm font-medium text-white/90">
                      {metrics.reviewsGrowth > 0 ? '+' : ''}{metrics.reviewsGrowth}%
                    </span>
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">{metrics.totalReviews}</h3>
                  <p className="text-indigo-100 text-sm">Avis clients</p>
                </div>
              </CardBody>
            </Card>

            {/* Taux de conversion */}
            <Card className="bg-gradient-to-br from-teal-500 to-teal-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <Target size={24} className="text-white/80" />
                  <div className="flex items-center space-x-1">
                    {getGrowthIcon(metrics.conversionGrowth)}
                    <span className="text-sm font-medium text-white/90">
                      {metrics.conversionGrowth > 0 ? '+' : ''}{metrics.conversionGrowth}%
                    </span>
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">{metrics.conversionRate.toFixed(1)}%</h3>
                  <p className="text-teal-100 text-sm">Taux de conversion</p>
                </div>
              </CardBody>
            </Card>
          </div>
        )}

        {/* Contenu principal - Grid 2 colonnes */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Activité récente - 2/3 de la largeur */}
          <div className="lg:col-span-2">
            <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-2">
                    <Activity className="text-blue-600" size={20} />
                    <h3 className="text-lg font-semibold text-gray-900">Activité récente</h3>
                    <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                      {filteredActivity.length}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <select
                      value={activityFilter}
                      onChange={(e) => setActivityFilter(e.target.value as any)}
                      className="text-sm border border-gray-200 rounded-lg px-3 py-1 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">Tout</option>
                      <option value="orders">Commandes</option>
                      <option value="reviews">Avis</option>
                      <option value="products">Produits</option>
                      <option value="messages">Messages</option>
                    </select>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-blue-600 border-blue-200 hover:bg-blue-50"
                      onClick={() => setShowActivityModal(true)}
                    >
                      <Eye size={14} />
                      <span className="ml-2">Voir tout</span>
                    </Button>
                  </div>
                </div>

                <div className="space-y-3">
                  {displayedActivity.map(activity => (
                    <div key={activity.id} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all duration-200 group">
                      <div className="flex-shrink-0">
                        <div className={`w-10 h-10 ${getActivityBgColor(activity.type)} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform`}>
                          {getActivityIcon(activity.type)}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h4 className="text-sm font-medium text-gray-900 truncate">{activity.title}</h4>
                              {getStatusBadge(activity.status)}
                            </div>
                            <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                            {activity.amount && (
                              <p className="text-sm font-medium text-green-600 mt-2 flex items-center">
                                <DollarSign size={12} className="mr-1" />
                                {formatAmount(activity.amount)}
                              </p>
                            )}
                          </div>
                          <div className="flex flex-col items-end space-y-1">
                            <span className="text-xs text-gray-500">{formatTimeAgo(activity.timestamp)}</span>
                            <button className="text-xs text-blue-600 hover:text-blue-800 opacity-0 group-hover:opacity-100 transition-opacity">
                              Détails
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {filteredActivity.length === 0 && (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Activity className="h-8 w-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune activité récente</h3>
                    <p className="text-sm text-gray-500 mb-4">
                      {activityFilter === 'all'
                        ? "L'activité de votre entreprise apparaîtra ici."
                        : `Aucune activité de type "${activityFilter}" trouvée.`
                      }
                    </p>
                    <Button size="sm" variant="outline" onClick={loadDashboardData}>
                      <RefreshCw size={14} />
                      <span className="ml-2">Actualiser</span>
                    </Button>
                  </div>
                )}

                {showAllActivity && filteredActivity.length > 5 && (
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>Affichage de {displayedActivity.length} sur {filteredActivity.length} activités</span>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setShowAllActivity(false)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        Réduire
                      </Button>
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          </div>

          {/* Actions rapides - 1/3 de la largeur */}
          <div className="lg:col-span-1">
            <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
              <CardBody className="p-6">
                <div className="flex items-center space-x-2 mb-6">
                  <Zap className="text-amber-500" size={20} />
                  <h3 className="text-lg font-semibold text-gray-900">Actions rapides</h3>
                </div>

                <div className="space-y-3">
                  <button
                    onClick={() => window.location.href = '/product-management'}
                    className="w-full flex items-center space-x-3 p-3 text-left bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors group"
                  >
                    <div className="flex-shrink-0 w-10 h-10 bg-blue-100 group-hover:bg-blue-200 rounded-lg flex items-center justify-center">
                      <Package size={16} className="text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">Ajouter un produit</h4>
                      <p className="text-xs text-gray-600">Nouveau produit au catalogue</p>
                    </div>
                    <ArrowRight size={16} className="text-gray-400 group-hover:text-blue-600" />
                  </button>

                  <button
                    onClick={() => window.location.href = '/business-orders'}
                    className="w-full flex items-center space-x-3 p-3 text-left bg-green-50 hover:bg-green-100 rounded-lg transition-colors group"
                  >
                    <div className="flex-shrink-0 w-10 h-10 bg-green-100 group-hover:bg-green-200 rounded-lg flex items-center justify-center">
                      <ShoppingBag size={16} className="text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">Voir les commandes</h4>
                      <p className="text-xs text-gray-600">Gérer les commandes</p>
                    </div>
                    <ArrowRight size={16} className="text-gray-400 group-hover:text-green-600" />
                  </button>

                  <button
                    onClick={() => {
                      // Changer d'onglet vers IA Conseiller
                      const event = new CustomEvent('changeBusinessTab', { detail: 'ai-recommendations' });
                      window.dispatchEvent(event);
                    }}
                    className="w-full flex items-center space-x-3 p-3 text-left bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors group"
                  >
                    <div className="flex-shrink-0 w-10 h-10 bg-purple-100 group-hover:bg-purple-200 rounded-lg flex items-center justify-center">
                      <Brain size={16} className="text-purple-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">IA Business</h4>
                      <p className="text-xs text-gray-600">Recommandations intelligentes</p>
                    </div>
                    <ArrowRight size={16} className="text-gray-400 group-hover:text-purple-600" />
                  </button>

                  <button
                    onClick={() => window.location.href = '/business-sales'}
                    className="w-full flex items-center space-x-3 p-3 text-left bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors group"
                  >
                    <div className="flex-shrink-0 w-10 h-10 bg-indigo-100 group-hover:bg-indigo-200 rounded-lg flex items-center justify-center">
                      <BarChart3 size={16} className="text-indigo-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">Analytics</h4>
                      <p className="text-xs text-gray-600">Statistiques détaillées</p>
                    </div>
                    <ArrowRight size={16} className="text-gray-400 group-hover:text-indigo-600" />
                  </button>

                  <button
                    onClick={() => window.location.href = '/business-ads'}
                    className="w-full flex items-center space-x-3 p-3 text-left bg-amber-50 hover:bg-amber-100 rounded-lg transition-colors group"
                  >
                    <div className="flex-shrink-0 w-10 h-10 bg-amber-100 group-hover:bg-amber-200 rounded-lg flex items-center justify-center">
                      <Zap size={16} className="text-amber-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">Créer une pub</h4>
                      <p className="text-xs text-gray-600">Nouvelle publicité</p>
                    </div>
                    <ArrowRight size={16} className="text-gray-400 group-hover:text-amber-600" />
                  </button>

                  <button
                    onClick={() => window.location.href = '/settings'}
                    className="w-full flex items-center space-x-3 p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group"
                  >
                    <div className="flex-shrink-0 w-10 h-10 bg-gray-100 group-hover:bg-gray-200 rounded-lg flex items-center justify-center">
                      <Settings size={16} className="text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">Paramètres</h4>
                      <p className="text-xs text-gray-600">Configuration du compte</p>
                    </div>
                    <ArrowRight size={16} className="text-gray-400 group-hover:text-gray-600" />
                  </button>
                </div>
              </CardBody>
            </Card>
          </div>
        </div>

        {/* Objectifs et KPIs */}
        {showObjectives && (
          <Card className="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 shadow-sm">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-2">
                  <Target className="text-indigo-600" size={20} />
                  <h3 className="text-lg font-semibold text-gray-900">Objectifs du mois</h3>
                </div>
                <button
                  onClick={() => setShowObjectives(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={16} />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Objectif CA */}
                <div className="bg-white rounded-lg p-4 border border-indigo-100">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-900">Chiffre d'affaires</h4>
                    <span className="text-xs text-indigo-600 bg-indigo-100 px-2 py-1 rounded-full">75%</span>
                  </div>
                  <div className="mb-2">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>2,250,000 F CFA</span>
                      <span>3,000,000 F CFA</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-indigo-600 h-2 rounded-full" style={{ width: '75%' }}></div>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500">Objectif mensuel</p>
                </div>

                {/* Objectif Commandes */}
                <div className="bg-white rounded-lg p-4 border border-green-100">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-900">Commandes</h4>
                    <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">85%</span>
                  </div>
                  <div className="mb-2">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>170</span>
                      <span>200</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500">Objectif mensuel</p>
                </div>

                {/* Objectif Satisfaction */}
                <div className="bg-white rounded-lg p-4 border border-amber-100">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-900">Satisfaction</h4>
                    <span className="text-xs text-amber-600 bg-amber-100 px-2 py-1 rounded-full">92%</span>
                  </div>
                  <div className="mb-2">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>4.6/5</span>
                      <span>4.8/5</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-amber-500 h-2 rounded-full" style={{ width: '92%' }}></div>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500">Note moyenne cible</p>
                </div>
              </div>
            </CardBody>
          </Card>
        )}

        {/* Section graphiques et analytics améliorée */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Graphiques interactifs - 2/3 de la largeur */}
          <div className="lg:col-span-2">
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="text-blue-600" size={20} />
                    <h3 className="text-lg font-semibold text-gray-900">Analytics avancées</h3>
                  </div>
                  <div className="flex items-center space-x-2">
                    <select
                      value={selectedChart}
                      onChange={(e) => setSelectedChart(e.target.value)}
                      className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="sales">Ventes</option>
                      <option value="orders">Commandes</option>
                      <option value="visitors">Visiteurs</option>
                    </select>
                    <Button size="sm" variant="outline">
                      <Download size={14} />
                    </Button>
                  </div>
                </div>

                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    {selectedChart === 'sales' && (
                      <AreaChart data={chartData}>
                        <defs>
                          <linearGradient id="colorSales" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                            <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis dataKey="date" stroke="#6b7280" fontSize={12} />
                        <YAxis stroke="#6b7280" fontSize={12} />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'white',
                            border: '1px solid #e5e7eb',
                            borderRadius: '8px',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                          }}
                          formatter={(value: any) => [formatAmount(value), 'Ventes']}
                        />
                        <Area
                          type="monotone"
                          dataKey="ventes"
                          stroke="#3B82F6"
                          strokeWidth={2}
                          fillOpacity={1}
                          fill="url(#colorSales)"
                        />
                      </AreaChart>
                    )}

                    {selectedChart === 'orders' && (
                      <RechartsBarChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis dataKey="date" stroke="#6b7280" fontSize={12} />
                        <YAxis stroke="#6b7280" fontSize={12} />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'white',
                            border: '1px solid #e5e7eb',
                            borderRadius: '8px',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                          }}
                          formatter={(value: any) => [value, 'Commandes']}
                        />
                        <Bar dataKey="commandes" fill="#10B981" radius={[4, 4, 0, 0]} />
                      </RechartsBarChart>
                    )}

                    {selectedChart === 'visitors' && (
                      <RechartsLineChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis dataKey="date" stroke="#6b7280" fontSize={12} />
                        <YAxis stroke="#6b7280" fontSize={12} />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'white',
                            border: '1px solid #e5e7eb',
                            borderRadius: '8px',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                          }}
                          formatter={(value: any) => [value, 'Visiteurs']}
                        />
                        <Line
                          type="monotone"
                          dataKey="visiteurs"
                          stroke="#8B5CF6"
                          strokeWidth={3}
                          dot={{ fill: '#8B5CF6', strokeWidth: 2, r: 4 }}
                          activeDot={{ r: 6, stroke: '#8B5CF6', strokeWidth: 2 }}
                        />
                      </RechartsLineChart>
                    )}
                  </ResponsiveContainer>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Répartition par catégories - 1/3 de la largeur */}
          <div className="lg:col-span-1">
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-2">
                    <PieChartIcon className="text-purple-600" size={20} />
                    <h3 className="text-lg font-semibold text-gray-900">Répartition</h3>
                  </div>
                </div>

                <div className="h-64 mb-4">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={categoryData}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={80}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {categoryData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value: any) => [`${value}%`, 'Part']}
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #e5e7eb',
                          borderRadius: '8px',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>

                <div className="space-y-2">
                  {categoryData.map((category, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: category.color }}
                        ></div>
                        <span className="text-sm text-gray-700">{category.name}</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900">{category.value}%</span>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          </div>
        </div>

        {/* Section Insights IA et Recommandations */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Insights IA */}
          <Card className="bg-gradient-to-br from-purple-50 to-indigo-50 border border-purple-200 shadow-sm">
            <CardBody className="p-6">
              <div className="flex items-center space-x-2 mb-6">
                <Brain className="text-purple-600" size={20} />
                <h3 className="text-lg font-semibold text-gray-900">Insights IA</h3>
                <span className="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  Nouveau
                </span>
              </div>

              <div className="space-y-4">
                <div className="bg-white rounded-lg p-4 border border-purple-100">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <TrendingUp size={16} className="text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900 mb-1">Tendance positive</h4>
                      <p className="text-sm text-gray-600">Vos ventes d'électronique ont augmenté de 23% cette semaine. Considérez augmenter votre stock.</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 border border-purple-100">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
                      <AlertTriangle size={16} className="text-amber-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900 mb-1">Attention requise</h4>
                      <p className="text-sm text-gray-600">Le taux d'abandon panier est élevé (65%). Vérifiez vos prix et processus de commande.</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 border border-purple-100">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <Target size={16} className="text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900 mb-1">Opportunité</h4>
                      <p className="text-sm text-gray-600">Vos clients recherchent souvent "accessoires smartphone". Pensez à élargir cette gamme.</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 pt-4 border-t border-purple-200">
                <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white">
                  <Brain size={16} />
                  <span className="ml-2">Voir toutes les recommandations IA</span>
                </Button>
              </div>
            </CardBody>
          </Card>

          {/* Top produits amélioré */}
          <Card className="bg-white border border-gray-200 shadow-sm">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-2">
                  <Award className="text-amber-500" size={20} />
                  <h3 className="text-lg font-semibold text-gray-900">Top produits</h3>
                </div>
                <Button size="sm" variant="outline">
                  <ExternalLink size={14} />
                  <span className="ml-2">Voir tous</span>
                </Button>
              </div>

              <div className="space-y-4">
                {topProducts.map((product, index) => (
                  <div key={index} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center text-lg border border-gray-200">
                        {product.image}
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">{product.name}</h4>
                      <p className="text-xs text-gray-600">{product.sales} ventes ce mois</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">{formatAmount(product.revenue)}</p>
                      <div className="flex items-center justify-end space-x-1">
                        {product.growth > 0 ? (
                          <ArrowUpRight size={12} className="text-green-500" />
                        ) : (
                          <ArrowDownRight size={12} className="text-red-500" />
                        )}
                        <span className={`text-xs font-medium ${
                          product.growth > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {product.growth > 0 ? '+' : ''}{product.growth}%
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-gray-900">
                      {topProducts.reduce((sum, product) => sum + product.sales, 0)}
                    </p>
                    <p className="text-xs text-gray-600">Total ventes</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-600">
                      {formatAmount(topProducts.reduce((sum, product) => sum + product.revenue, 0))}
                    </p>
                    <p className="text-xs text-gray-600">CA total</p>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Section notifications et tâches */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Notifications importantes */}
          <div className="lg:col-span-2">
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-2">
                    <Bell className="text-blue-600" size={20} />
                    <h3 className="text-lg font-semibold text-gray-900">Notifications importantes</h3>
                  </div>
                  <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                    3 nouvelles
                  </span>
                </div>

                <div className="space-y-3">
                  {[
                    {
                      type: 'urgent',
                      title: 'Stock faible',
                      message: 'iPhone 15 Pro - Plus que 3 unités en stock',
                      time: '5 min',
                      icon: AlertTriangle,
                      color: 'red'
                    },
                    {
                      type: 'info',
                      title: 'Nouvelle commande',
                      message: 'Commande #12847 - 2 articles - 85,000 F CFA',
                      time: '12 min',
                      icon: ShoppingBag,
                      color: 'blue'
                    },
                    {
                      type: 'success',
                      title: 'Avis positif',
                      message: 'Nouveau avis 5 étoiles sur MacBook Air M3',
                      time: '1h',
                      icon: Star,
                      color: 'green'
                    },
                    {
                      type: 'warning',
                      title: 'Paiement en attente',
                      message: 'Commande #12845 - Paiement non confirmé',
                      time: '2h',
                      icon: Clock,
                      color: 'amber'
                    }
                  ].map((notification, index) => (
                    <div key={index} className={`flex items-start space-x-3 p-3 rounded-lg border ${
                      notification.color === 'red' ? 'bg-red-50 border-red-200' :
                      notification.color === 'blue' ? 'bg-blue-50 border-blue-200' :
                      notification.color === 'green' ? 'bg-green-50 border-green-200' :
                      'bg-amber-50 border-amber-200'
                    }`}>
                      <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                        notification.color === 'red' ? 'bg-red-100' :
                        notification.color === 'blue' ? 'bg-blue-100' :
                        notification.color === 'green' ? 'bg-green-100' :
                        'bg-amber-100'
                      }`}>
                        <notification.icon size={16} className={
                          notification.color === 'red' ? 'text-red-600' :
                          notification.color === 'blue' ? 'text-blue-600' :
                          notification.color === 'green' ? 'text-green-600' :
                          'text-amber-600'
                        } />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-gray-900">{notification.title}</h4>
                          <span className="text-xs text-gray-500">{notification.time}</span>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-4 pt-4 border-t border-gray-200">
                  <Button variant="outline" className="w-full">
                    <Eye size={16} />
                    <span className="ml-2">Voir toutes les notifications</span>
                  </Button>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Tâches rapides */}
          <div className="lg:col-span-1">
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardBody className="p-6">
                <div className="flex items-center space-x-2 mb-6">
                  <CheckCircle className="text-green-600" size={20} />
                  <h3 className="text-lg font-semibold text-gray-900">Tâches du jour</h3>
                </div>

                <div className="space-y-3">
                  {[
                    { task: 'Répondre aux messages clients', completed: false, priority: 'high' },
                    { task: 'Mettre à jour le stock iPhone', completed: false, priority: 'high' },
                    { task: 'Préparer commande #12847', completed: true, priority: 'medium' },
                    { task: 'Publier nouvelle promotion', completed: false, priority: 'low' },
                    { task: 'Analyser les ventes de la semaine', completed: true, priority: 'medium' }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={item.completed}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                        readOnly
                      />
                      <div className="flex-1">
                        <p className={`text-sm ${item.completed ? 'text-gray-500 line-through' : 'text-gray-900'}`}>
                          {item.task}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`text-xs px-2 py-0.5 rounded-full ${
                            item.priority === 'high' ? 'bg-red-100 text-red-800' :
                            item.priority === 'medium' ? 'bg-amber-100 text-amber-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {item.priority === 'high' ? 'Urgent' :
                             item.priority === 'medium' ? 'Moyen' : 'Faible'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6 pt-4 border-t border-gray-200">
                  <Button className="w-full bg-green-600 hover:bg-green-700 text-white" size="sm">
                    <Plus size={16} />
                    <span className="ml-2">Ajouter une tâche</span>
                  </Button>
                </div>
              </CardBody>
            </Card>
          </div>
        </div>
      </div>

      {/* Modal d'activité détaillée */}
      <ActivityModal
        isOpen={showActivityModal}
        onClose={() => setShowActivityModal(false)}
        activities={recentActivity}
        businessName={currentUser?.businessName || 'Mon Entreprise'}
        onRefresh={loadDashboardData}
      />
    </div>
  );
};

export default ModernBusinessDashboard;
