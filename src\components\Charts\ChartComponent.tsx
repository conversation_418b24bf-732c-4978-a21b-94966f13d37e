import React, { useEffect, useRef } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Bar, Pie, Doughnut } from 'react-chartjs-2';
import './ChartComponent.css';

// Enregistrer les composants Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface ChartComponentProps {
  type: 'line' | 'bar' | 'pie' | 'doughnut';
  data: any;
  options?: any;
  title?: string;
  height?: number;
  className?: string;
}

const ChartComponent: React.FC<ChartComponentProps> = ({
  type,
  data,
  options = {},
  title,
  height = 300,
  className = ''
}) => {
  const chartRef = useRef<any>(null);

  // Options par défaut pour tous les graphiques
  const defaultOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
            family: 'Inter, sans-serif'
          }
        }
      },
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold',
          family: 'Inter, sans-serif'
        },
        padding: {
          top: 10,
          bottom: 30
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        padding: 12,
        titleFont: {
          size: 14,
          weight: 'bold'
        },
        bodyFont: {
          size: 13
        }
      }
    },
    scales: type === 'line' || type === 'bar' ? {
      x: {
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            size: 11
          }
        }
      },
      y: {
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            size: 11
          }
        }
      }
    } : {},
    animation: {
      duration: 1000,
      easing: 'easeInOutQuart'
    },
    interaction: {
      intersect: false,
      mode: 'index' as const
    }
  };

  // Fusionner les options par défaut avec les options personnalisées
  const mergedOptions = {
    ...defaultOptions,
    ...options,
    plugins: {
      ...defaultOptions.plugins,
      ...options.plugins
    }
  };

  // Fonction pour rendre le bon type de graphique
  const renderChart = () => {
    const commonProps = {
      ref: chartRef,
      data,
      options: mergedOptions,
      height
    };

    switch (type) {
      case 'line':
        return <Line {...commonProps} />;
      case 'bar':
        return <Bar {...commonProps} />;
      case 'pie':
        return <Pie {...commonProps} />;
      case 'doughnut':
        return <Doughnut {...commonProps} />;
      default:
        return <Line {...commonProps} />;
    }
  };

  return (
    <div className={`chart-container ${className}`}>
      <div className="chart-wrapper" style={{ height: `${height}px` }}>
        {renderChart()}
      </div>
    </div>
  );
};

// Composants spécialisés pour différents types de données

// Graphique d'évolution des ventes
export const SalesEvolutionChart: React.FC<{ salesData: any[] }> = ({ salesData }) => {
  const data = {
    labels: salesData.map(item => item.date),
    datasets: [
      {
        label: 'Chiffre d\'affaires',
        data: salesData.map(item => item.revenue),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: 'white',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8
      },
      {
        label: 'Nombre de ventes',
        data: salesData.map(item => item.count),
        borderColor: 'rgb(16, 185, 129)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(16, 185, 129)',
        pointBorderColor: 'white',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
        yAxisID: 'y1'
      }
    ]
  };

  const options = {
    scales: {
      y: {
        type: 'linear' as const,
        display: true,
        position: 'left' as const,
        title: {
          display: true,
          text: 'Chiffre d\'affaires (F CFA)'
        }
      },
      y1: {
        type: 'linear' as const,
        display: true,
        position: 'right' as const,
        title: {
          display: true,
          text: 'Nombre de ventes'
        },
        grid: {
          drawOnChartArea: false
        }
      }
    }
  };

  return (
    <ChartComponent
      type="line"
      data={data}
      options={options}
      title="Évolution des ventes"
      height={350}
    />
  );
};

// Graphique de répartition par catégorie
export const CategoryDistributionChart: React.FC<{ categoryData: any[] }> = ({ categoryData }) => {
  const colors = [
    'rgba(59, 130, 246, 0.8)',
    'rgba(16, 185, 129, 0.8)',
    'rgba(245, 158, 11, 0.8)',
    'rgba(239, 68, 68, 0.8)',
    'rgba(139, 92, 246, 0.8)',
    'rgba(236, 72, 153, 0.8)'
  ];

  const data = {
    labels: categoryData.map(item => item.category),
    datasets: [
      {
        data: categoryData.map(item => item.value),
        backgroundColor: colors.slice(0, categoryData.length),
        borderColor: colors.slice(0, categoryData.length).map(color => color.replace('0.8', '1')),
        borderWidth: 2,
        hoverOffset: 10
      }
    ]
  };

  const options = {
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          padding: 20,
          generateLabels: (chart: any) => {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              return data.labels.map((label: string, i: number) => {
                const value = data.datasets[0].data[i];
                const total = data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
                const percentage = ((value / total) * 100).toFixed(1);
                
                return {
                  text: `${label} (${percentage}%)`,
                  fillStyle: data.datasets[0].backgroundColor[i],
                  strokeStyle: data.datasets[0].borderColor[i],
                  lineWidth: data.datasets[0].borderWidth,
                  hidden: false,
                  index: i
                };
              });
            }
            return [];
          }
        }
      }
    }
  };

  return (
    <ChartComponent
      type="doughnut"
      data={data}
      options={options}
      title="Répartition par catégorie"
      height={350}
    />
  );
};

// Graphique de comparaison mensuelle
export const MonthlyComparisonChart: React.FC<{ monthlyData: any[] }> = ({ monthlyData }) => {
  const data = {
    labels: monthlyData.map(item => item.month),
    datasets: [
      {
        label: 'Mois actuel',
        data: monthlyData.map(item => item.current),
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 2,
        borderRadius: 6,
        borderSkipped: false
      },
      {
        label: 'Mois précédent',
        data: monthlyData.map(item => item.previous),
        backgroundColor: 'rgba(156, 163, 175, 0.6)',
        borderColor: 'rgb(156, 163, 175)',
        borderWidth: 2,
        borderRadius: 6,
        borderSkipped: false
      }
    ]
  };

  const options = {
    plugins: {
      tooltip: {
        callbacks: {
          afterLabel: (context: any) => {
            const current = context.dataset.data[context.dataIndex];
            const otherDataset = context.chart.data.datasets.find((d: any) => d !== context.dataset);
            const previous = otherDataset?.data[context.dataIndex] || 0;
            const change = ((current - previous) / previous * 100).toFixed(1);
            return `Évolution: ${change > 0 ? '+' : ''}${change}%`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Valeur'
        }
      }
    }
  };

  return (
    <ChartComponent
      type="bar"
      data={data}
      options={options}
      title="Comparaison mensuelle"
      height={350}
    />
  );
};

export default ChartComponent;
