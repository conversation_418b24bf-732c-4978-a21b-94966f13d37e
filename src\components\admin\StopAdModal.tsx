import React, { useState } from 'react';
import {
  X,
  AlertTriangle,
  XCircle,
  DollarSign,
  Calendar,
  TrendingUp,
  Building2,
  Clock,
  RefreshCw
} from 'lucide-react';
import Modal from '../ui/Modal';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';
import { formatAmount } from '../../utils/formatUtils';
import { AdminAdPayment } from '../../services/adminPaymentService';

interface StopAdModalProps {
  adPayment: AdminAdPayment | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (adPaymentId: string, reason: string, refundType: 'none' | 'partial' | 'full') => void;
}

const StopAdModal: React.FC<StopAdModalProps> = ({
  adPayment,
  isOpen,
  onClose,
  onConfirm
}) => {
  const [reason, setReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [refundType, setRefundType] = useState<'none' | 'partial' | 'full'>('partial');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const predefinedReasons = [
    'Contenu inapproprié ou non conforme',
    'Violation des politiques publicitaires',
    'Demande de l\'annonceur',
    'Performance insuffisante',
    'Budget épuisé prématurément',
    'Problème technique détecté',
    'Plaintes d\'utilisateurs',
    'Enquête en cours',
    'Autre (préciser ci-dessous)'
  ];

  const handleSubmit = async () => {
    if (!adPayment || !reason) return;

    const finalReason = reason === 'Autre (préciser ci-dessous)' ? customReason : reason;
    
    if (!finalReason.trim()) {
      alert('Veuillez préciser la raison de l\'arrêt.');
      return;
    }

    setIsSubmitting(true);
    try {
      await onConfirm(adPayment.id, finalReason, refundType);
      onClose();
      resetForm();
    } catch (error) {
      console.error('Erreur lors de l\'arrêt de la publicité:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setReason('');
    setCustomReason('');
    setRefundType('partial');
  };

  const calculateRefundAmount = () => {
    if (!adPayment) return 0;
    
    const startDate = new Date(adPayment.startDate);
    const endDate = new Date(adPayment.endDate);
    const today = new Date();
    
    const totalDuration = endDate.getTime() - startDate.getTime();
    const elapsed = today.getTime() - startDate.getTime();
    const remaining = endDate.getTime() - today.getTime();
    
    const progress = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
    const remainingPercentage = Math.max(0, (remaining / totalDuration) * 100);
    
    switch (refundType) {
      case 'full':
        return adPayment.amount;
      case 'partial':
        return Math.floor(adPayment.amount * (remainingPercentage / 100));
      case 'none':
      default:
        return 0;
    }
  };

  const calculateDaysRemaining = () => {
    if (!adPayment) return 0;
    const endDate = new Date(adPayment.endDate);
    const today = new Date();
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const calculateProgress = () => {
    if (!adPayment) return 0;
    const startDate = new Date(adPayment.startDate);
    const endDate = new Date(adPayment.endDate);
    const today = new Date();
    
    const totalDuration = endDate.getTime() - startDate.getTime();
    const elapsed = today.getTime() - startDate.getTime();
    
    return Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
  };

  const getImpactDescription = () => {
    const impacts = [];
    
    impacts.push('Arrêt immédiat de la diffusion');
    impacts.push('Statistiques finales figées');
    impacts.push('Rapport de performance disponible');
    
    if (refundType === 'full') {
      impacts.push('Remboursement intégral du budget');
    } else if (refundType === 'partial') {
      impacts.push('Remboursement proportionnel au temps restant');
    } else {
      impacts.push('Aucun remboursement');
    }
    
    impacts.push('Notification automatique à l\'annonceur');
    
    return impacts;
  };

  if (!adPayment) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <div className="p-6">
        {/* En-tête */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 rounded-full">
              <XCircle className="text-red-600" size={20} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Arrêter la publicité</h2>
              <p className="text-gray-600">{adPayment.adTitle}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Informations de la publicité */}
        <Card className="mb-6">
          <CardBody className="p-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center space-x-2">
                <Building2 size={16} className="text-gray-400" />
                <span className="text-gray-600">Entreprise:</span>
                <span className="font-medium">{adPayment.businessName}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar size={16} className="text-gray-400" />
                <span className="text-gray-600">Durée:</span>
                <span className="font-medium">{adPayment.duration} jours</span>
              </div>
              <div className="flex items-center space-x-2">
                <DollarSign size={16} className="text-gray-400" />
                <span className="text-gray-600">Budget:</span>
                <span className="font-medium">{formatAmount(adPayment.amount)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock size={16} className="text-gray-400" />
                <span className="text-gray-600">Jours restants:</span>
                <span className="font-medium">{calculateDaysRemaining()} jours</span>
              </div>
              <div className="flex items-center space-x-2">
                <TrendingUp size={16} className="text-gray-400" />
                <span className="text-gray-600">Impressions:</span>
                <span className="font-medium">{adPayment.impressions.toLocaleString()}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-gray-600">Progression:</span>
                <span className="font-medium">{calculateProgress().toFixed(1)}%</span>
              </div>
            </div>
          </CardBody>
        </Card>

        <div className="space-y-6">
          {/* Raison de l'arrêt */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Raison de l'arrêt *
            </label>
            <div className="space-y-2">
              {predefinedReasons.map((predefinedReason) => (
                <label key={predefinedReason} className="flex items-center">
                  <input
                    type="radio"
                    name="reason"
                    value={predefinedReason}
                    checked={reason === predefinedReason}
                    onChange={(e) => setReason(e.target.value)}
                    className="mr-2"
                  />
                  <span className="text-sm">{predefinedReason}</span>
                </label>
              ))}
            </div>
            
            {reason === 'Autre (préciser ci-dessous)' && (
              <textarea
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                placeholder="Veuillez préciser la raison de l'arrêt..."
                className="mt-2 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                required
              />
            )}
          </div>

          {/* Type de remboursement */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Politique de remboursement
            </label>
            <div className="space-y-3">
              <label className="flex items-start space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="refundType"
                  value="none"
                  checked={refundType === 'none'}
                  onChange={(e) => setRefundType(e.target.value as any)}
                  className="mt-1"
                />
                <div className="flex-1">
                  <div className="font-medium text-gray-900">Aucun remboursement</div>
                  <div className="text-sm text-gray-600">
                    Le budget est conservé en raison de la violation ou de la performance
                  </div>
                  <div className="text-sm font-medium text-gray-900 mt-1">
                    Montant: {formatAmount(0)}
                  </div>
                </div>
              </label>
              
              <label className="flex items-start space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="refundType"
                  value="partial"
                  checked={refundType === 'partial'}
                  onChange={(e) => setRefundType(e.target.value as any)}
                  className="mt-1"
                />
                <div className="flex-1">
                  <div className="font-medium text-gray-900">Remboursement proportionnel</div>
                  <div className="text-sm text-gray-600">
                    Remboursement basé sur le temps de diffusion restant
                  </div>
                  <div className="text-sm font-medium text-green-600 mt-1">
                    Montant: {formatAmount(calculateRefundAmount())}
                  </div>
                </div>
              </label>
              
              <label className="flex items-start space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="refundType"
                  value="full"
                  checked={refundType === 'full'}
                  onChange={(e) => setRefundType(e.target.value as any)}
                  className="mt-1"
                />
                <div className="flex-1">
                  <div className="font-medium text-gray-900">Remboursement intégral</div>
                  <div className="text-sm text-gray-600">
                    Remboursement complet du budget (erreur technique ou administrative)
                  </div>
                  <div className="text-sm font-medium text-green-600 mt-1">
                    Montant: {formatAmount(calculateRefundAmount())}
                  </div>
                </div>
              </label>
            </div>
          </div>

          {/* Résumé de l'impact */}
          <Card>
            <CardBody className="p-4">
              <div className="flex items-center space-x-2 mb-3">
                <AlertTriangle className="text-orange-500" size={16} />
                <h4 className="font-medium text-gray-900">Impact de l'arrêt</h4>
              </div>
              <ul className="space-y-1 text-sm text-gray-600">
                {getImpactDescription().map((impact, index) => (
                  <li key={index} className="flex items-center space-x-2">
                    <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                    <span>{impact}</span>
                  </li>
                ))}
              </ul>
            </CardBody>
          </Card>

          {/* Statistiques finales */}
          <Card>
            <CardBody className="p-4">
              <h4 className="font-medium text-gray-900 mb-3">Statistiques finales</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Impressions totales:</span>
                  <span className="font-medium">{adPayment.impressions.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Clics totaux:</span>
                  <span className="font-medium">{adPayment.clicks}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">CTR final:</span>
                  <span className="font-medium">{((adPayment.clicks / adPayment.impressions) * 100).toFixed(2)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">CPC moyen:</span>
                  <span className="font-medium">{formatAmount(adPayment.amount / adPayment.clicks)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Budget dépensé:</span>
                  <span className="font-medium">{formatAmount(Math.floor(adPayment.amount * (calculateProgress() / 100)))}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Durée effective:</span>
                  <span className="font-medium">{Math.floor(adPayment.duration * (calculateProgress() / 100))} jours</span>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Avertissement */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="text-red-500 mt-0.5" size={16} />
              <div>
                <h4 className="font-medium text-red-800 mb-1">Attention</h4>
                <p className="text-sm text-red-700">
                  Cette action arrêtera immédiatement la diffusion de la publicité. 
                  L'annonceur sera notifié par email de cet arrêt avec la raison fournie.
                  {refundType !== 'none' && ' Le remboursement sera traité dans les 3-5 jours ouvrables.'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Annuler
          </Button>
          <Button
            variant="danger"
            onClick={handleSubmit}
            disabled={!reason || isSubmitting}
            leftIcon={isSubmitting ? <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div> : <XCircle size={16} />}
          >
            {isSubmitting ? 'Arrêt en cours...' : 'Arrêter la publicité'}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default StopAdModal;
