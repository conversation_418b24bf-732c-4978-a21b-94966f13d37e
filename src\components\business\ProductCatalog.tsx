import React from 'react';
import { IProduct, QualityBadge } from '../../types';
import { Star, ShoppingCart, Tag } from 'lucide-react';

interface ProductCatalogProps {
  products: IProduct[];
  isOwnBusiness: boolean;
}

const ProductCatalog: React.FC<ProductCatalogProps> = ({ products, isOwnBusiness }) => {
  if (!products || products.length === 0) {
    return (
      <div className="empty-catalog">
        <p className="text-center text-gray-500 my-8">Aucun produit dans le catalogue pour le moment.</p>
        {isOwnBusiness && (
          <div className="text-center">
            <button className="add-product-button">
              <ShoppingCart size={16} className="mr-2" />
              Ajouter un produit
            </button>
          </div>
        )}
      </div>
    );
  }

  const getBadgeClass = (badge: QualityBadge) => {
    switch (badge) {
      case QualityBadge.GOLD:
        return 'badge-gold';
      case QualityBadge.SILVER:
        return 'badge-silver';
      case QualityBadge.BRONZE:
        return 'badge-bronze';
      default:
        return '';
    }
  };

  return (
    <div className="product-catalog">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {products.map((product) => (
          <div key={product.id} className="product-card">
            <div className="product-image-container">
              <img 
                src={product.images && product.images.length > 0 ? product.images[0] : '/default-product.jpg'} 
                alt={product.name} 
                className="product-image"
              />
              {product.qualityBadge !== QualityBadge.NONE && (
                <div className={`quality-badge ${getBadgeClass(product.qualityBadge)}`}>
                  {product.qualityBadge}
                </div>
              )}
            </div>
            <div className="product-details">
              <h3 className="product-name">{product.name}</h3>
              <div className="product-category">
                <Tag size={14} className="mr-1" />
                {product.category}
              </div>
              <p className="product-description">{product.description}</p>
              <div className="product-footer">
                <div className="product-rating">
                  <Star size={16} className="text-yellow-400" />
                  <span>{product.averageRating.toFixed(1)}</span>
                </div>
                <div className="product-price">{product.price.toFixed(2)} €</div>
              </div>
              {product.negotiable && (
                <div className="negotiable-tag">Négociable</div>
              )}
              {product.stock !== undefined && product.stock <= 5 && (
                <div className="low-stock-tag">Plus que {product.stock} en stock</div>
              )}
            </div>
          </div>
        ))}
      </div>
      
      {isOwnBusiness && (
        <div className="text-center mt-6">
          <button className="add-product-button">
            <ShoppingCart size={16} className="mr-2" />
            Ajouter un produit
          </button>
        </div>
      )}
    </div>
  );
};

export default ProductCatalog;
