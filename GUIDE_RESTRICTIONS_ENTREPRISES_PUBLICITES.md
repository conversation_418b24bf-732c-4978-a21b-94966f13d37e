# 🚫 Guide des Restrictions - Entreprises et Publicités

## 📋 Vue d'Ensemble

Ce guide explique les restrictions mises en place pour empêcher les comptes entreprise de commenter les publicités, tout en maintenant leur capacité à liker et partager.

## 🎯 Objectif des Restrictions

### Pourquoi Empêcher les Entreprises de Commenter ?

1. **Authenticité des Retours** : Maintenir la crédibilité des commentaires clients
2. **Éviter les Conflits d'Intérêts** : Empêcher la promotion croisée entre entreprises
3. **Préserver l'Expérience Utilisateur** : Garder les commentaires centrés sur l'expérience client
4. **Conformité aux Bonnes Pratiques** : Respecter les standards des plateformes publicitaires

## 🔧 Implémentation Technique

### 1. Validation Côté Service (`adInteractionService.ts`)

```typescript
// Vérification du rôle utilisateur avant ajout de commentaire
if (userProfile.role === UserRole.BUSINESS) {
  throw new Error('Les comptes entreprise ne peuvent pas commenter les publicités...');
}
```

**Fonctionnalités** :
- ✅ Vérification automatique du rôle utilisateur
- ✅ Message d'erreur explicite
- ✅ Blocage au niveau service pour sécurité maximale

### 2. Interface Utilisateur (`FeedAdCard.tsx`)

```typescript
// Utilisation des utilitaires de permissions
const adPermissions = getAdPermissions(currentUser?.role);
const { canComment, isBusiness, restrictions } = adPermissions;
```

**Fonctionnalités** :
- ✅ Bouton de commentaire désactivé pour les entreprises
- ✅ Message d'information contextuel
- ✅ Tooltip explicatif au survol
- ✅ Style visuel différencié (opacité réduite)

### 3. Validation Base de Données (Migration SQL)

```sql
-- Trigger de validation avant insertion
CREATE TRIGGER validate_ad_comment_permissions
  BEFORE INSERT ON ad_comments
  FOR EACH ROW
  EXECUTE FUNCTION check_ad_comment_permissions();
```

**Fonctionnalités** :
- ✅ Contrainte au niveau base de données
- ✅ Politique RLS (Row Level Security)
- ✅ Fonction de validation automatique
- ✅ Table d'audit des violations

## 📱 Expérience Utilisateur

### Pour les Utilisateurs Réguliers
- ✅ **Commentaires** : Autorisés sans restriction
- ✅ **Likes** : Autorisés sans restriction  
- ✅ **Partages** : Autorisés sans restriction

### Pour les Comptes Entreprise
- 🚫 **Commentaires** : Bloqués avec message explicatif
- ✅ **Likes** : Autorisés (engagement positif)
- ✅ **Partages** : Autorisés (amplification du contenu)

### Messages d'Information

#### Message Principal
```
"Les comptes entreprise ne peuvent pas commenter les publicités. 
Seuls les utilisateurs réguliers peuvent interagir avec les publicités 
pour maintenir l'authenticité des retours clients."
```

#### Tooltip au Survol
```
"Les comptes entreprise ne peuvent pas commenter les publicités"
```

## 🛡️ Sécurité Multi-Niveaux

### Niveau 1 : Interface Utilisateur
- Bouton désactivé visuellement
- Formulaire de commentaire masqué
- Message d'information affiché

### Niveau 2 : Service Frontend
- Validation du rôle avant appel API
- Gestion d'erreur avec message utilisateur
- Blocage des tentatives côté client

### Niveau 3 : Base de Données
- Trigger de validation automatique
- Politique RLS restrictive
- Fonction de vérification des permissions

### Niveau 4 : Audit et Monitoring
- Table de violations pour traçabilité
- Logs des tentatives bloquées
- Monitoring des comportements suspects

## 📊 Permissions Détaillées

| Action | Utilisateur Standard | Entreprise | Admin |
|--------|---------------------|------------|-------|
| Commenter | ✅ Autorisé | 🚫 Bloqué | ✅ Autorisé |
| Liker | ✅ Autorisé | ✅ Autorisé | ✅ Autorisé |
| Partager | ✅ Autorisé | ✅ Autorisé | ✅ Autorisé |
| Voir commentaires | ✅ Autorisé | ✅ Autorisé | ✅ Autorisé |

## 🔍 Utilitaires de Permissions

### Fichier : `src/utils/adPermissions.ts`

```typescript
// Vérification des permissions
export const canUserCommentOnAd = (userRole: UserRole): boolean => {
  return userRole === UserRole.STANDARD || userRole === UserRole.ADMIN;
};

// Obtention des permissions complètes
export const getAdPermissions = (userRole: UserRole) => {
  return {
    canComment: canUserCommentOnAd(userRole),
    canLike: canUserLikeAd(userRole),
    canShare: canUserShareAd(userRole),
    isBusiness: isBusinessUser(userRole),
    restrictions: {
      comment: getAdRestrictionMessage('comment', userRole)
    }
  };
};
```

## 🧪 Tests et Validation

### Tests Manuels Recommandés

1. **Compte Entreprise** :
   - ✅ Vérifier que le bouton commentaire est désactivé
   - ✅ Confirmer l'affichage du message d'information
   - ✅ Tester que les likes/partages fonctionnent
   - ✅ Vérifier le tooltip au survol

2. **Compte Utilisateur** :
   - ✅ Confirmer que tous les boutons sont actifs
   - ✅ Tester l'ajout de commentaires
   - ✅ Vérifier les likes et partages

3. **Tests de Sécurité** :
   - ✅ Tentative de contournement côté client
   - ✅ Appel direct à l'API avec compte entreprise
   - ✅ Vérification des logs de violations

## 📈 Métriques et Monitoring

### Métriques à Surveiller
- Nombre de tentatives de commentaires bloquées
- Taux d'engagement des entreprises (likes/partages)
- Satisfaction utilisateur avec les restrictions

### Tables de Monitoring
- `ad_comment_violations` : Tentatives bloquées
- `ad_engagement_metrics` : Métriques d'engagement
- Logs applicatifs pour debugging

## 🚀 Déploiement

### Étapes de Déploiement

1. **Migration Base de Données** :
   ```bash
   # Appliquer la migration
   supabase db push
   ```

2. **Déploiement Frontend** :
   ```bash
   # Build et déploiement
   npm run build
   npm run deploy
   ```

3. **Tests Post-Déploiement** :
   - Vérifier les restrictions en production
   - Tester avec différents types de comptes
   - Monitorer les logs d'erreur

## 📞 Support et Maintenance

### Points de Contact
- **Développement** : Équipe technique
- **Support Utilisateur** : Service client
- **Monitoring** : Équipe DevOps

### Documentation Associée
- `GUIDE_SYSTEME_PUBLICITES.md` : Système publicitaire complet
- `GUIDE_INTERACTIONS_PUBLICITAIRES.md` : Interactions utilisateur
- Documentation API Supabase

---

## ✅ Résumé

Les restrictions sont maintenant en place pour :
- 🚫 **Empêcher** les entreprises de commenter les publicités
- ✅ **Permettre** aux entreprises de liker et partager
- 🛡️ **Sécuriser** avec validation multi-niveaux
- 📱 **Informer** les utilisateurs avec des messages clairs
- 📊 **Monitorer** les tentatives et violations

**Objectif atteint** : Maintenir l'authenticité des commentaires tout en préservant l'engagement des entreprises ! 🎯
