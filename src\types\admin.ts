// Types pour le système d'administration

export enum AdminLevel {
  SUPER_ADMIN = 'super_admin',
  PLATFORM_ADMIN = 'platform_admin',
  CONTENT_MODERATOR = 'content_moderator',
  SUPPORT_ADMIN = 'support_admin',
  BUSINESS_ADMIN = 'business_admin',
  USER_ADMIN = 'user_admin',
  ANALYTICS_ADMIN = 'analytics_admin'
}

export enum AlertPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum AlertStatus {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  RESOLVED = 'resolved',
  DISMISSED = 'dismissed'
}

export interface AdminProfile {
  id: string;
  user_id: string;
  admin_level: AdminLevel;
  admin_code: string;
  department?: string;
  supervisor_id?: string;
  permissions: Record<string, any>;
  restrictions: Record<string, any>;
  is_active: boolean;
  last_login?: string;
  login_count: number;
  notes?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
  activated_at?: string;
  deactivated_at?: string;
  
  // Relations
  user?: {
    id: string;
    username: string;
    email: string;
    profile_picture?: string;
  };
  supervisor?: AdminProfile;
  subordinates?: AdminProfile[];
}

export interface AdminPermission {
  id: string;
  permission_code: string;
  permission_name: string;
  description?: string;
  category: string;
  required_level: AdminLevel;
  is_sensitive: boolean;
  created_at: string;
  updated_at: string;
}

export interface AdminSession {
  id: string;
  admin_id: string;
  session_token: string;
  ip_address?: string;
  user_agent?: string;
  location?: Record<string, any>;
  is_active: boolean;
  expires_at: string;
  created_at: string;
  ended_at?: string;
}

export interface AdminAuditLog {
  id: string;
  admin_id: string;
  action_type: string;
  action_description: string;
  target_type?: string;
  target_id?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  metadata: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  success: boolean;
  error_message?: string;
  created_at: string;
  
  // Relations
  admin?: {
    id: string;
    admin_code: string;
    user: {
      username: string;
      email: string;
    };
  };
}

export interface AdminAlert {
  id: string;
  alert_type: string;
  priority: AlertPriority;
  title: string;
  message: string;
  details: Record<string, any>;
  assigned_to?: string;
  created_by?: string;
  status: AlertStatus;
  resolution_notes?: string;
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  
  // Relations
  assignee?: AdminProfile;
  creator?: AdminProfile;
}

export interface AdminReport {
  id: string;
  report_type: string;
  title: string;
  description?: string;
  data: Record<string, any>;
  filters: Record<string, any>;
  generated_by: string;
  is_scheduled: boolean;
  schedule_config?: Record<string, any>;
  shared_with: string[];
  is_public: boolean;
  created_at: string;
  updated_at: string;
  
  // Relations
  generator?: AdminProfile;
}

// Types pour les statistiques du tableau de bord
export interface AdminDashboardStats {
  totalUsers: number;
  totalBusinesses: number;
  totalPosts: number;
  totalOrders: number;
  activeAdmins: number;
  pendingAlerts: number;
  todayRegistrations: number;
  monthlyRevenue: number;
  
  // Graphiques
  userGrowth: Array<{ date: string; count: number }>;
  businessGrowth: Array<{ date: string; count: number }>;
  revenueGrowth: Array<{ date: string; amount: number }>;
  alertsByPriority: Array<{ priority: AlertPriority; count: number }>;
}

// Types pour les actions d'administration
export interface AdminAction {
  type: string;
  label: string;
  description: string;
  requiredPermission: string;
  isSensitive: boolean;
  confirmationRequired: boolean;
}

// Types pour la gestion des utilisateurs
export interface UserManagementAction {
  userId: string;
  action: 'suspend' | 'activate' | 'delete' | 'verify' | 'change_role' | 'reset_password';
  reason?: string;
  duration?: number; // Pour les suspensions temporaires
  newRole?: string; // Pour les changements de rôle
}

// Types pour la gestion des entreprises
export interface BusinessManagementAction {
  businessId: string;
  action: 'verify' | 'suspend' | 'activate' | 'change_status' | 'approve_documents';
  reason?: string;
  newStatus?: string;
  documents?: string[];
}

// Types pour la modération de contenu
export interface ContentModerationAction {
  contentId: string;
  contentType: 'post' | 'comment' | 'review' | 'ad';
  action: 'approve' | 'reject' | 'flag' | 'remove' | 'edit';
  reason?: string;
  moderatorNotes?: string;
}

// Types pour les filtres et recherche
export interface AdminFilters {
  dateRange?: {
    start: string;
    end: string;
  };
  adminLevel?: AdminLevel[];
  status?: string[];
  department?: string[];
  searchTerm?: string;
}

// Types pour les permissions d'accès
export interface AccessControl {
  canView: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canCreate: boolean;
  restrictions?: string[];
}

// Types pour les notifications admin
export interface AdminNotification {
  id: string;
  type: 'alert' | 'system' | 'security' | 'user_action';
  title: string;
  message: string;
  priority: AlertPriority;
  read: boolean;
  created_at: string;
  expires_at?: string;
  action_url?: string;
}

// Types pour l'export de données
export interface ExportRequest {
  type: 'users' | 'businesses' | 'posts' | 'orders' | 'analytics';
  format: 'csv' | 'xlsx' | 'json' | 'pdf';
  filters: Record<string, any>;
  columns?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
}

// Types pour les métriques en temps réel
export interface RealTimeMetrics {
  activeUsers: number;
  onlineAdmins: number;
  pendingTickets: number;
  systemLoad: number;
  errorRate: number;
  responseTime: number;
  lastUpdated: string;
}
