-- Script de vérification rapide après correction
-- <PERSON><PERSON>z et exécutez ce script dans Supabase SQL Editor

-- Test 1: Vérifier que la fonction clean_interests existe et fonctionne
SELECT 
    'Test fonction clean_interests' as test,
    clean_interests('  sport  ,   musique,voyage   ,  ') as result,
    CASE 
        WHEN clean_interests('  sport  ,   musique,voyage   ,  ') = 'sport, musique, voyage'
        THEN '✅ SUCCÈS'
        ELSE '❌ ÉCHEC'
    END as status;

-- Test 2: Vérifier les triggers
SELECT 
    'Triggers créés' as test,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) >= 2 THEN '✅ SUCCÈS'
        ELSE '❌ ÉCHEC'
    END as status
FROM information_schema.triggers 
WHERE trigger_schema = 'public' 
AND trigger_name IN ('clean_interests_trigger', 'on_auth_user_created');

-- Test 3: Vérifier les fonctions
SELECT 
    'Fonctions créées' as test,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) >= 3 THEN '✅ SUCCÈS'
        ELSE '❌ ÉCHEC'
    END as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('clean_interests', 'trigger_clean_interests', 'handle_new_user');

-- Test 4: Résumé final
SELECT 
    '=== RÉSUMÉ DE LA CORRECTION ===' as message
UNION ALL
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'clean_interests')
        THEN '✅ Fonction clean_interests: OK'
        ELSE '❌ Fonction clean_interests: MANQUANTE'
    END
UNION ALL
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'clean_interests_trigger')
        THEN '✅ Trigger clean_interests: OK'
        ELSE '❌ Trigger clean_interests: MANQUANT'
    END
UNION ALL
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'on_auth_user_created')
        THEN '✅ Trigger inscription: OK'
        ELSE '❌ Trigger inscription: MANQUANT'
    END
UNION ALL
SELECT 
    '🎉 CORRECTION TERMINÉE - Testez l''inscription maintenant !';
