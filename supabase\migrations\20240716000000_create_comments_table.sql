-- Migration pour la table comments
CREATE TABLE public.comments ( 
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), 
  post_id UUID NOT NULL REFERENCES public.posts(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE, 
  content TEXT NOT NULL, 
  has_used_product BOOLEAN DEFAULT false,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  created_at TIMESTAMPTZ DEFAULT NOW(), 
  updated_at TIMESTAMPTZ DEFAULT NOW() 
); 

-- Add RLS policies 
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY; 

-- Policy to allow users to see all comments
CREATE POLICY "Anyone can view comments" 
ON public.comments FOR SELECT 
TO authenticated
USING (true); 

-- Policy to allow users to insert their own comments
CREATE POLICY "Users can insert their own comments" 
ON public.comments FOR INSERT 
TO authenticated
WITH CHECK (auth.uid() = user_id); 

-- Policy to allow users to update their own comments
CREATE POLICY "Users can update their own comments" 
ON public.comments FOR UPDATE 
TO authenticated
USING (auth.uid() = user_id);

-- Policy to allow users to delete their own comments
CREATE POLICY "Users can delete their own comments" 
ON public.comments FOR DELETE 
TO authenticated
USING (auth.uid() = user_id);

-- Grant access to authenticated users 
GRANT SELECT, INSERT, UPDATE, DELETE ON public.comments TO authenticated;