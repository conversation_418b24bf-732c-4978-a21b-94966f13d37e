# 🗄️ Guide : Migration de la table profil utilisateur

## ✅ **Migration créée avec succès**

Une migration complète a été créée pour enrichir la table `profiles` avec tous les nouveaux champs du formulaire "Éditer le profil" développé.

---

## 📋 **Fichiers créés**

### **1. Migration SQL :**
- **Fichier** : `supabase/migrations/20250128000000_enhance_profiles_with_form_fields.sql`
- **Objectif** : Ajouter les nouveaux champs et fonctionnalités à la table profiles

### **2. Service TypeScript :**
- **Fichier** : `src/services/profileService.ts`
- **Objectif** : Gérer les mises à jour de profil avec validation et upload d'images

### **3. Types mis à jour :**
- **Fichier** : `src/types/index.ts`
- **Objectif** : Ajouter les nouveaux types pour le formulaire et les réponses

### **4. Script de test :**
- **Fichier** : `scripts/run-profile-migration.sql`
- **Objectif** : Tester la migration et les nouvelles fonctionnalités

---

## 🆕 **Nouveaux champs ajoutés**

### **Champs de base :**
```sql
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS bio TEXT,                    -- Biographie (max 500 caractères)
ADD COLUMN IF NOT EXISTS website TEXT,                -- Site web (format URL)
ADD COLUMN IF NOT EXISTS phone VARCHAR(20),           -- Téléphone (privé)
ADD COLUMN IF NOT EXISTS profession VARCHAR(100),     -- Profession/métier
ADD COLUMN IF NOT EXISTS interests TEXT;              -- Centres d'intérêt
```

### **Champs existants mis à jour :**
```sql
-- Genre étendu avec plus d'options
ALTER TABLE profiles
ADD CONSTRAINT gender_check CHECK (
  gender IS NULL OR 
  gender IN ('Homme', 'Femme', 'Autre', 'Préfère ne pas dire')
);
```

---

## 🔒 **Contraintes de validation**

### **1. Biographie :**
```sql
ALTER TABLE profiles
ADD CONSTRAINT bio_length_check CHECK (bio IS NULL OR char_length(bio) <= 500);
```

### **2. Site web :**
```sql
ALTER TABLE profiles
ADD CONSTRAINT website_format_check CHECK (
  website IS NULL OR 
  website = '' OR 
  website ~* '^https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(/.*)?$'
);
```

### **3. Téléphone :**
```sql
ALTER TABLE profiles
ADD CONSTRAINT phone_format_check CHECK (
  phone IS NULL OR 
  phone = '' OR 
  phone ~* '^\+?[1-9]\d{1,14}$'
);
```

### **4. Profession :**
```sql
ALTER TABLE profiles
ADD CONSTRAINT profession_length_check CHECK (
  profession IS NULL OR 
  char_length(profession) <= 100
);
```

### **5. Centres d'intérêt :**
```sql
ALTER TABLE profiles
ADD CONSTRAINT interests_length_check CHECK (
  interests IS NULL OR 
  char_length(interests) <= 500
);
```

---

## ⚡ **Fonctions SQL créées**

### **1. Nettoyage des centres d'intérêt :**
```sql
CREATE OR REPLACE FUNCTION clean_interests(input_interests TEXT)
RETURNS TEXT AS $$
BEGIN
  IF input_interests IS NULL OR trim(input_interests) = '' THEN
    RETURN NULL;
  END IF;
  
  -- Nettoyer les espaces en trop et normaliser les virgules
  RETURN trim(regexp_replace(input_interests, '\s*,\s*', ', ', 'g'));
END;
$$ LANGUAGE plpgsql IMMUTABLE;
```

### **2. Statistiques de complétion du profil :**
```sql
CREATE OR REPLACE FUNCTION get_profile_completion_stats(profile_id UUID)
RETURNS JSON AS $$
DECLARE
  completion_percentage INTEGER;
  missing_fields TEXT[];
BEGIN
  -- Calcule le pourcentage de complétion sur 9 champs
  -- Retourne JSON avec pourcentage et champs manquants
END;
$$ LANGUAGE plpgsql STABLE;
```

### **3. Mise à jour sécurisée du profil :**
```sql
CREATE OR REPLACE FUNCTION update_user_profile(
  profile_id UUID,
  new_username TEXT DEFAULT NULL,
  new_city TEXT DEFAULT NULL,
  -- ... autres paramètres
)
RETURNS JSON AS $$
BEGIN
  -- Vérification des permissions
  -- Mise à jour avec validation
  -- Retour du résultat
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

---

## 📊 **Vue enrichie créée**

### **profiles_with_stats :**
```sql
CREATE OR REPLACE VIEW profiles_with_stats AS
SELECT 
  p.*,
  get_profile_completion_stats(p.id) as completion_stats,
  CASE 
    WHEN p.bio IS NOT NULL AND p.profession IS NOT NULL AND p.city IS NOT NULL 
    THEN true 
    ELSE false 
  END as is_profile_complete,
  CASE 
    WHEN p.interests IS NOT NULL 
    THEN string_to_array(p.interests, ',') 
    ELSE ARRAY[]::TEXT[] 
  END as interests_array
FROM profiles p;
```

**Utilisation :**
```sql
-- Récupérer les profils avec statistiques
SELECT 
  username,
  completion_stats,
  is_profile_complete,
  interests_array
FROM profiles_with_stats 
WHERE username = 'john_doe';
```

---

## 🔍 **Index de performance**

### **Index créés :**
```sql
-- Index pour recherche par profession
CREATE INDEX IF NOT EXISTS profiles_profession_idx 
ON profiles(profession) WHERE profession IS NOT NULL;

-- Index composite pour localisation
CREATE INDEX IF NOT EXISTS profiles_city_country_idx 
ON profiles(city, country) WHERE city IS NOT NULL AND country IS NOT NULL;
```

---

## 🔧 **Triggers automatiques**

### **1. Nettoyage automatique des centres d'intérêt :**
```sql
CREATE TRIGGER clean_interests_trigger
  BEFORE INSERT OR UPDATE OF interests ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION trigger_clean_interests();
```

### **2. Mise à jour automatique du timestamp :**
```sql
-- Le trigger updated_at existant continue de fonctionner
CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
```

---

## 🛡️ **Sécurité RLS (Row Level Security)**

### **Politiques mises à jour :**
```sql
-- Les numéros de téléphone restent privés
CREATE POLICY "Phone numbers are private" ON profiles
  FOR SELECT
  USING (
    CASE 
      WHEN auth.uid() = id THEN true  -- L'utilisateur voit son téléphone
      ELSE false  -- Les autres ne voient pas le téléphone
    END
  );

-- Mise à jour autorisée pour le propriétaire
CREATE POLICY "Users can update their own profile"
  ON profiles
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);
```

---

## 📝 **Types TypeScript mis à jour**

### **Interface IUser enrichie :**
```typescript
export interface IUser {
  // ... champs existants
  gender?: 'Homme' | 'Femme' | 'Autre' | 'Préfère ne pas dire';
  // Nouveaux champs du formulaire développé
  bio?: string;
  website?: string;
  phone?: string;
  profession?: string;
  interests?: string;
}
```

### **Nouveaux types créés :**
```typescript
// Type pour les données du formulaire
export interface IProfileFormData {
  username: string;
  city: string;
  country: string;
  age: string;
  gender: string;
  bio: string;
  website: string;
  phone: string;
  profession: string;
  interests: string;
  profilePictureFile: File | null;
  coverPhotoFile: File | null;
}

// Type pour les statistiques de complétion
export interface IProfileCompletionStats {
  completion_percentage: number;
  missing_fields: string[];
  total_fields: number;
  completed_fields: number;
}

// Type pour la réponse de mise à jour
export interface IProfileUpdateResponse {
  success?: boolean;
  error?: string;
  message?: string;
}
```

---

## 🚀 **Service ProfileService créé**

### **Fonctionnalités principales :**
```typescript
export class ProfileService {
  // Mise à jour du profil avec validation
  static async updateProfile(userId: string, formData: IProfileFormData): Promise<IProfileUpdateResponse>
  
  // Statistiques de complétion
  static async getProfileCompletionStats(userId: string): Promise<IProfileCompletionStats | null>
  
  // Récupération du profil complet
  static async getFullProfile(userId: string): Promise<IUser | null>
  
  // Validation des données du formulaire
  static validateFormData(formData: IProfileFormData): { isValid: boolean; errors: string[] }
  
  // Nettoyage des centres d'intérêt
  static cleanInterests(interests: string): string
  
  // Upload des images (privé)
  private static async uploadProfilePicture(userId: string, file: File): Promise<string | null>
  private static async uploadCoverPhoto(userId: string, file: File): Promise<string | null>
}
```

---

## 📋 **Instructions d'exécution**

### **1. Exécuter la migration :**
```bash
# Via Supabase CLI
supabase db push

# Ou via l'interface Supabase
# Copier le contenu de 20250128000000_enhance_profiles_with_form_fields.sql
# dans l'éditeur SQL de Supabase et exécuter
```

### **2. Tester la migration :**
```bash
# Exécuter le script de test dans l'éditeur SQL Supabase
# Fichier: scripts/run-profile-migration.sql
```

### **3. Vérifier les buckets de stockage :**
```sql
-- Créer les buckets pour les images si nécessaire
INSERT INTO storage.buckets (id, name, public) 
VALUES 
  ('profile-pictures', 'profile-pictures', true),
  ('cover-photos', 'cover-photos', true);
```

---

## ✅ **Validation de la migration**

### **Vérifications à effectuer :**

1. **Structure de la table :**
   ```sql
   SELECT column_name, data_type, is_nullable 
   FROM information_schema.columns 
   WHERE table_name = 'profiles';
   ```

2. **Contraintes :**
   ```sql
   SELECT constraint_name, constraint_type
   FROM information_schema.table_constraints 
   WHERE table_name = 'profiles';
   ```

3. **Fonctions :**
   ```sql
   SELECT routine_name, routine_type
   FROM information_schema.routines 
   WHERE routine_name LIKE '%profile%';
   ```

4. **Index :**
   ```sql
   SELECT indexname, indexdef
   FROM pg_indexes 
   WHERE tablename = 'profiles';
   ```

---

## 🎯 **Résumé des améliorations**

### ✅ **Base de données :**
- **5 nouveaux champs** ajoutés avec contraintes de validation
- **3 fonctions SQL** pour la gestion et les statistiques
- **1 vue enrichie** avec calculs automatiques
- **2 index** pour améliorer les performances
- **Triggers automatiques** pour le nettoyage des données
- **Politiques RLS** pour la sécurité

### ✅ **Code TypeScript :**
- **Types enrichis** pour tous les nouveaux champs
- **Service complet** avec validation et upload
- **Composant mis à jour** pour utiliser le nouveau service
- **Gestion d'erreurs** robuste

### ✅ **Fonctionnalités :**
- **Validation côté client et serveur**
- **Upload d'images** sécurisé
- **Statistiques de complétion** du profil
- **Nettoyage automatique** des données
- **Recherche optimisée** par profession et localisation

**La migration est prête à être déployée !** 🚀

---

**Prochaines étapes :**
1. Exécuter la migration en production
2. Tester le formulaire avec de vraies données
3. Monitorer les performances des nouvelles requêtes
4. Ajouter des analytics sur la complétion des profils
