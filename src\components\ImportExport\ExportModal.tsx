import React, { useState } from 'react';
import {
  X,
  Download,
  FileText,
  FileSpreadsheet,
  Filter,
  Calendar,
  CheckCircle,
  RefreshCw,
  Settings,
  BarChart3,
  Package,
  DollarSign,
  Tag
} from 'lucide-react';
import { formatAmount } from '../../utils/formatUtils';
import './ImportExport.css';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  category: string;
  subcategory: string;
  brand: string;
  sku: string;
  stock: number;
  minStock: number;
  images: string[];
  tags: string[];
  status: 'active' | 'inactive' | 'out_of_stock';
  rating: number;
  reviewsCount: number;
  salesCount: number;
  revenue: number;
  createdAt: string;
  updatedAt: string;
}

interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  products: Product[];
  selectedProducts?: string[];
}

interface ExportOptions {
  format: 'csv' | 'excel' | 'json';
  scope: 'all' | 'selected' | 'filtered';
  fields: string[];
  includeImages: boolean;
  includeStats: boolean;
  dateRange: {
    start: string;
    end: string;
  };
  filters: {
    categories: string[];
    status: string[];
    minStock: number | null;
    maxStock: number | null;
  };
}

const ExportModal: React.FC<ExportModalProps> = ({
  isOpen,
  onClose,
  products,
  selectedProducts = []
}) => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'excel',
    scope: selectedProducts.length > 0 ? 'selected' : 'all',
    fields: ['name', 'price', 'category', 'brand', 'stock', 'status'],
    includeImages: false,
    includeStats: true,
    dateRange: {
      start: '',
      end: ''
    },
    filters: {
      categories: [],
      status: [],
      minStock: null,
      maxStock: null
    }
  });

  const [isExporting, setIsExporting] = useState(false);
  const [exportStep, setExportStep] = useState<'config' | 'processing' | 'complete'>('config');

  if (!isOpen) return null;

  const availableFields = [
    { key: 'name', label: 'Nom du produit', category: 'basic' },
    { key: 'description', label: 'Description', category: 'basic' },
    { key: 'price', label: 'Prix', category: 'pricing' },
    { key: 'originalPrice', label: 'Prix original', category: 'pricing' },
    { key: 'category', label: 'Catégorie', category: 'classification' },
    { key: 'subcategory', label: 'Sous-catégorie', category: 'classification' },
    { key: 'brand', label: 'Marque', category: 'basic' },
    { key: 'sku', label: 'SKU', category: 'basic' },
    { key: 'stock', label: 'Stock', category: 'inventory' },
    { key: 'minStock', label: 'Stock minimum', category: 'inventory' },
    { key: 'status', label: 'Statut', category: 'basic' },
    { key: 'tags', label: 'Tags', category: 'classification' },
    { key: 'rating', label: 'Note moyenne', category: 'stats' },
    { key: 'reviewsCount', label: 'Nombre d\'avis', category: 'stats' },
    { key: 'salesCount', label: 'Ventes totales', category: 'stats' },
    { key: 'revenue', label: 'Chiffre d\'affaires', category: 'stats' },
    { key: 'createdAt', label: 'Date de création', category: 'dates' },
    { key: 'updatedAt', label: 'Dernière modification', category: 'dates' }
  ];

  const fieldCategories = {
    basic: 'Informations de base',
    pricing: 'Prix et tarification',
    classification: 'Catégorisation',
    inventory: 'Stock et inventaire',
    stats: 'Statistiques',
    dates: 'Dates'
  };

  const categories = [...new Set(products.map(p => p.category))];
  const statusOptions = ['active', 'inactive', 'out_of_stock'];

  const getFilteredProducts = () => {
    let filtered = products;

    // Scope
    if (exportOptions.scope === 'selected') {
      filtered = products.filter(p => selectedProducts.includes(p.id));
    }

    // Filtres
    if (exportOptions.filters.categories.length > 0) {
      filtered = filtered.filter(p => exportOptions.filters.categories.includes(p.category));
    }

    if (exportOptions.filters.status.length > 0) {
      filtered = filtered.filter(p => exportOptions.filters.status.includes(p.status));
    }

    if (exportOptions.filters.minStock !== null) {
      filtered = filtered.filter(p => p.stock >= exportOptions.filters.minStock!);
    }

    if (exportOptions.filters.maxStock !== null) {
      filtered = filtered.filter(p => p.stock <= exportOptions.filters.maxStock!);
    }

    // Date range
    if (exportOptions.dateRange.start) {
      filtered = filtered.filter(p => p.createdAt >= exportOptions.dateRange.start);
    }

    if (exportOptions.dateRange.end) {
      filtered = filtered.filter(p => p.createdAt <= exportOptions.dateRange.end);
    }

    return filtered;
  };

  const handleFieldToggle = (fieldKey: string) => {
    setExportOptions(prev => ({
      ...prev,
      fields: prev.fields.includes(fieldKey)
        ? prev.fields.filter(f => f !== fieldKey)
        : [...prev.fields, fieldKey]
    }));
  };

  const handleCategoryToggle = (category: string) => {
    const categoryFields = availableFields
      .filter(f => f.category === category)
      .map(f => f.key);

    const allSelected = categoryFields.every(f => exportOptions.fields.includes(f));

    if (allSelected) {
      setExportOptions(prev => ({
        ...prev,
        fields: prev.fields.filter(f => !categoryFields.includes(f))
      }));
    } else {
      setExportOptions(prev => ({
        ...prev,
        fields: [...new Set([...prev.fields, ...categoryFields])]
      }));
    }
  };

  const generateExportData = (products: Product[]) => {
    return products.map(product => {
      const exportData: any = {};

      exportOptions.fields.forEach(field => {
        if (field === 'tags') {
          exportData[field] = product.tags.join(';');
        } else if (field === 'images' && exportOptions.includeImages) {
          exportData[field] = product.images.join(';');
        } else {
          exportData[field] = product[field as keyof Product];
        }
      });

      return exportData;
    });
  };

  const handleExport = async () => {
    setIsExporting(true);
    setExportStep('processing');

    try {
      const filteredProducts = getFilteredProducts();
      const exportData = generateExportData(filteredProducts);

      // Simulation du traitement
      await new Promise(resolve => setTimeout(resolve, 2000));

      if (exportOptions.format === 'csv') {
        exportToCSV(exportData);
      } else if (exportOptions.format === 'excel') {
        exportToExcel(exportData);
      } else if (exportOptions.format === 'json') {
        exportToJSON(exportData);
      }

      setExportStep('complete');
      setTimeout(() => {
        onClose();
        setExportStep('config');
      }, 2000);

    } catch (error) {
      alert('Erreur lors de l\'exportation');
      setExportStep('config');
    } finally {
      setIsExporting(false);
    }
  };

  const exportToCSV = (data: any[]) => {
    if (data.length === 0) return;

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row =>
        headers.map(header => {
          const value = row[header];
          return typeof value === 'string' && value.includes(',')
            ? `"${value}"`
            : value;
        }).join(',')
      )
    ].join('\n');

    downloadFile(csvContent, 'produits-export.csv', 'text/csv');
  };

  const exportToExcel = (data: any[]) => {
    // Simulation d'export Excel (nécessiterait une vraie librairie comme xlsx)
    const csvContent = data.length > 0 ? [
      Object.keys(data[0]).join('\t'),
      ...data.map(row => Object.values(row).join('\t'))
    ].join('\n') : '';

    downloadFile(csvContent, 'produits-export.xlsx', 'application/vnd.ms-excel');
  };

  const exportToJSON = (data: any[]) => {
    const jsonContent = JSON.stringify({
      exportDate: new Date().toISOString(),
      totalProducts: data.length,
      products: data
    }, null, 2);

    downloadFile(jsonContent, 'produits-export.json', 'application/json');
  };

  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const filteredProducts = getFilteredProducts();

  return (
    <div className="modal-overlay">
      <div className="modal-container large">
        <div className="modal-header">
          <div className="modal-title">
            <Download size={24} />
            <h2>Exporter les produits</h2>
          </div>
          <button className="modal-close" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="modal-content">
          {exportStep === 'config' && (
            <>
              {/* Format et portée */}
              <div className="export-section">
                <h3>Format et portée</h3>
                <div className="format-options">
                  <div className="format-grid">
                    <button
                      className={`format-card ${exportOptions.format === 'excel' ? 'active' : ''}`}
                      onClick={() => setExportOptions(prev => ({ ...prev, format: 'excel' }))}
                    >
                      <FileSpreadsheet size={24} />
                      <span>Excel</span>
                      <small>Recommandé pour l'analyse</small>
                    </button>

                    <button
                      className={`format-card ${exportOptions.format === 'csv' ? 'active' : ''}`}
                      onClick={() => setExportOptions(prev => ({ ...prev, format: 'csv' }))}
                    >
                      <FileText size={24} />
                      <span>CSV</span>
                      <small>Compatible universellement</small>
                    </button>

                    <button
                      className={`format-card ${exportOptions.format === 'json' ? 'active' : ''}`}
                      onClick={() => setExportOptions(prev => ({ ...prev, format: 'json' }))}
                    >
                      <Package size={24} />
                      <span>JSON</span>
                      <small>Pour les développeurs</small>
                    </button>
                  </div>
                </div>

                <div className="scope-options">
                  <h4>Produits à exporter</h4>
                  <div className="scope-grid">
                    <label className="scope-option">
                      <input
                        type="radio"
                        name="scope"
                        value="all"
                        checked={exportOptions.scope === 'all'}
                        onChange={(e) => setExportOptions(prev => ({ ...prev, scope: e.target.value as any }))}
                      />
                      <span>Tous les produits ({products.length})</span>
                    </label>

                    {selectedProducts.length > 0 && (
                      <label className="scope-option">
                        <input
                          type="radio"
                          name="scope"
                          value="selected"
                          checked={exportOptions.scope === 'selected'}
                          onChange={(e) => setExportOptions(prev => ({ ...prev, scope: e.target.value as any }))}
                        />
                        <span>Produits sélectionnés ({selectedProducts.length})</span>
                      </label>
                    )}

                    <label className="scope-option">
                      <input
                        type="radio"
                        name="scope"
                        value="filtered"
                        checked={exportOptions.scope === 'filtered'}
                        onChange={(e) => setExportOptions(prev => ({ ...prev, scope: e.target.value as any }))}
                      />
                      <span>Avec filtres ({filteredProducts.length})</span>
                    </label>
                  </div>
                </div>
              </div>

              {/* Sélection des champs */}
              <div className="export-section">
                <h3>Champs à exporter</h3>
                <div className="fields-selection">
                  {Object.entries(fieldCategories).map(([categoryKey, categoryLabel]) => {
                    const categoryFields = availableFields.filter(f => f.category === categoryKey);
                    const selectedCount = categoryFields.filter(f => exportOptions.fields.includes(f.key)).length;

                    return (
                      <div key={categoryKey} className="field-category">
                        <div className="category-header">
                          <button
                            className="category-toggle"
                            onClick={() => handleCategoryToggle(categoryKey)}
                          >
                            <CheckCircle
                              size={16}
                              className={selectedCount === categoryFields.length ? 'all-selected' : selectedCount > 0 ? 'partial-selected' : ''}
                            />
                            <span>{categoryLabel}</span>
                            <small>({selectedCount}/{categoryFields.length})</small>
                          </button>
                        </div>

                        <div className="category-fields">
                          {categoryFields.map(field => (
                            <label key={field.key} className="field-option">
                              <input
                                type="checkbox"
                                checked={exportOptions.fields.includes(field.key)}
                                onChange={() => handleFieldToggle(field.key)}
                              />
                              <span>{field.label}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Filtres avancés */}
              {exportOptions.scope === 'filtered' && (
                <div className="export-section">
                  <h3>Filtres avancés</h3>
                  <div className="filters-grid">
                    <div className="filter-group">
                      <label>Catégories</label>
                      <div className="checkbox-group">
                        {categories.map(category => (
                          <label key={category} className="checkbox-item">
                            <input
                              type="checkbox"
                              checked={exportOptions.filters.categories.includes(category)}
                              onChange={(e) => {
                                const categories = e.target.checked
                                  ? [...exportOptions.filters.categories, category]
                                  : exportOptions.filters.categories.filter(c => c !== category);
                                setExportOptions(prev => ({
                                  ...prev,
                                  filters: { ...prev.filters, categories }
                                }));
                              }}
                            />
                            <span>{category}</span>
                          </label>
                        ))}
                      </div>
                    </div>

                    <div className="filter-group">
                      <label>Statut</label>
                      <div className="checkbox-group">
                        {statusOptions.map(status => (
                          <label key={status} className="checkbox-item">
                            <input
                              type="checkbox"
                              checked={exportOptions.filters.status.includes(status)}
                              onChange={(e) => {
                                const statuses = e.target.checked
                                  ? [...exportOptions.filters.status, status]
                                  : exportOptions.filters.status.filter(s => s !== status);
                                setExportOptions(prev => ({
                                  ...prev,
                                  filters: { ...prev.filters, status: statuses }
                                }));
                              }}
                            />
                            <span>{status}</span>
                          </label>
                        ))}
                      </div>
                    </div>

                    <div className="filter-group">
                      <label>Stock</label>
                      <div className="range-inputs">
                        <input
                          type="number"
                          placeholder="Min"
                          value={exportOptions.filters.minStock || ''}
                          onChange={(e) => setExportOptions(prev => ({
                            ...prev,
                            filters: { ...prev.filters, minStock: e.target.value ? parseInt(e.target.value) : null }
                          }))}
                        />
                        <input
                          type="number"
                          placeholder="Max"
                          value={exportOptions.filters.maxStock || ''}
                          onChange={(e) => setExportOptions(prev => ({
                            ...prev,
                            filters: { ...prev.filters, maxStock: e.target.value ? parseInt(e.target.value) : null }
                          }))}
                        />
                      </div>
                    </div>

                    <div className="filter-group">
                      <label>Période de création</label>
                      <div className="date-inputs">
                        <input
                          type="date"
                          value={exportOptions.dateRange.start}
                          onChange={(e) => setExportOptions(prev => ({
                            ...prev,
                            dateRange: { ...prev.dateRange, start: e.target.value }
                          }))}
                        />
                        <input
                          type="date"
                          value={exportOptions.dateRange.end}
                          onChange={(e) => setExportOptions(prev => ({
                            ...prev,
                            dateRange: { ...prev.dateRange, end: e.target.value }
                          }))}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Options supplémentaires */}
              <div className="export-section">
                <h3>Options supplémentaires</h3>
                <div className="additional-options">
                  <label className="option-item">
                    <input
                      type="checkbox"
                      checked={exportOptions.includeImages}
                      onChange={(e) => setExportOptions(prev => ({ ...prev, includeImages: e.target.checked }))}
                    />
                    <span>Inclure les URLs des images</span>
                  </label>

                  <label className="option-item">
                    <input
                      type="checkbox"
                      checked={exportOptions.includeStats}
                      onChange={(e) => setExportOptions(prev => ({ ...prev, includeStats: e.target.checked }))}
                    />
                    <span>Inclure les statistiques de vente</span>
                  </label>
                </div>
              </div>

              {/* Résumé */}
              <div className="export-summary">
                <div className="summary-stats">
                  <div className="summary-item">
                    <Package size={16} />
                    <span>{filteredProducts.length} produits</span>
                  </div>
                  <div className="summary-item">
                    <BarChart3 size={16} />
                    <span>{exportOptions.fields.length} champs</span>
                  </div>
                  <div className="summary-item">
                    <FileSpreadsheet size={16} />
                    <span>Format {exportOptions.format.toUpperCase()}</span>
                  </div>
                </div>
              </div>

              <div className="modal-actions">
                <button className="btn-secondary" onClick={onClose}>
                  Annuler
                </button>
                <button
                  className="btn-primary"
                  onClick={handleExport}
                  disabled={exportOptions.fields.length === 0 || filteredProducts.length === 0}
                >
                  <Download size={16} />
                  Exporter
                </button>
              </div>
            </>
          )}

          {exportStep === 'processing' && (
            <div className="processing-step">
              <div className="processing-animation">
                <RefreshCw size={48} className="spinning" />
                <h3>Exportation en cours...</h3>
                <p>Génération du fichier {exportOptions.format.toUpperCase()}</p>
              </div>
            </div>
          )}

          {exportStep === 'complete' && (
            <div className="complete-step">
              <div className="complete-animation">
                <CheckCircle size={48} className="success" />
                <h3>Exportation terminée !</h3>
                <p>Le fichier a été téléchargé avec succès</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExportModal;
