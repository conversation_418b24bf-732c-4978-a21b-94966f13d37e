import { supabase } from '../lib/supabase';
import {
  IMarketComment,
  IMarketRating,
  IProductStats,
  ICreateComment,
  ICreateRating
} from '../types';
import { toast } from 'react-toastify';
import {
  getMockProductComments,
  getMockProductRatings,
  getMockProductStats,
  getMockUserRating,
  addMockComment,
  addOrUpdateMockRating
} from '../data/mockReviewsData';

// Service pour gérer les commentaires et notes du marketplace

// ==================== COMMENTAIRES ====================

// Récupérer tous les commentaires d'un produit
export const getProductComments = async (productId: string): Promise<IMarketComment[]> => {
  try {
    // Utiliser la vraie base de données Supabase
    const { data, error } = await supabase
      .from('comments_with_user_info')
      .select('*')
      .eq('product_id', productId)
      .is('parent_comment_id', null)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erreur lors de la récupération des commentaires:', error);
      // Fallback vers les données simulées en cas d'erreur
      return getMockProductComments(productId);
    }

    // Récupérer les réponses pour chaque commentaire
    const commentsWithReplies = await Promise.all(
      (data || []).map(async (comment) => {
        const replies = await getCommentReplies(comment.id);
        return {
          id: comment.id,
          productId: comment.product_id,
          userId: comment.user_id,
          comment: comment.comment,
          isVerifiedPurchase: comment.is_verified_purchase,
          isApproved: comment.is_approved,
          parentCommentId: comment.parent_comment_id,
          createdAt: new Date(comment.created_at),
          updatedAt: new Date(comment.updated_at),
          username: comment.username || 'Utilisateur',
          profilePicture: comment.profile_picture,
          userRating: comment.user_rating,
          replies
        } as IMarketComment;
      })
    );

    return commentsWithReplies;
  } catch (error) {
    console.error('Erreur lors de la récupération des commentaires:', error);
    // Fallback vers les données simulées en cas d'erreur
    return getMockProductComments(productId);
  }
};

// Récupérer les réponses d'un commentaire
export const getCommentReplies = async (parentCommentId: string): Promise<IMarketComment[]> => {
  try {
    const { data, error } = await supabase
      .from('comments_with_user_info')
      .select('*')
      .eq('parent_comment_id', parentCommentId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Erreur lors de la récupération des réponses:', error);
      return [];
    }

    return (data || []).map(comment => ({
      id: comment.id,
      productId: comment.product_id,
      userId: comment.user_id,
      comment: comment.comment,
      isVerifiedPurchase: comment.is_verified_purchase,
      isApproved: comment.is_approved,
      parentCommentId: comment.parent_comment_id,
      createdAt: new Date(comment.created_at),
      updatedAt: new Date(comment.updated_at),
      username: comment.username || 'Utilisateur',
      profilePicture: comment.profile_picture,
      userRating: comment.user_rating
    })) as IMarketComment[];
  } catch (error) {
    console.error('Erreur lors de la récupération des réponses:', error);
    return [];
  }
};

// Ajouter un nouveau commentaire
export const addComment = async (commentData: ICreateComment): Promise<IMarketComment | null> => {
  try {
    // Utiliser la vraie authentification Supabase
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      toast.error('Vous devez être connecté pour commenter');
      return null;
    }

    // Vérifier si l'utilisateur a acheté le produit (simulation pour l'instant)
    const isVerifiedPurchase = await checkVerifiedPurchase(user.id, commentData.productId);

    const { data, error } = await supabase
      .from('marketcomen')
      .insert([{
        product_id: commentData.productId,
        user_id: user.id,
        comment: commentData.comment,
        parent_comment_id: commentData.parentCommentId,
        is_verified_purchase: isVerifiedPurchase,
        is_approved: true // Auto-approuvé pour l'instant
      }])
      .select()
      .single();

    if (error) {
      console.error('Erreur lors de l\'ajout du commentaire:', error);
      toast.error('Erreur lors de l\'ajout du commentaire');
      return null;
    }

    toast.success('Commentaire ajouté avec succès');

    return {
      id: data.id,
      productId: data.product_id,
      userId: data.user_id,
      comment: data.comment,
      isVerifiedPurchase: data.is_verified_purchase,
      isApproved: data.is_approved,
      parentCommentId: data.parent_comment_id,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    } as IMarketComment;
  } catch (error) {
    console.error('Erreur lors de l\'ajout du commentaire:', error);
    toast.error('Erreur lors de l\'ajout du commentaire');
    return null;
  }
};

// Supprimer un commentaire
export const deleteComment = async (commentId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('marketcomen')
      .delete()
      .eq('id', commentId);

    if (error) {
      console.error('Erreur lors de la suppression du commentaire:', error);
      toast.error('Erreur lors de la suppression du commentaire');
      return false;
    }

    toast.success('Commentaire supprimé avec succès');
    return true;
  } catch (error) {
    console.error('Erreur lors de la suppression du commentaire:', error);
    toast.error('Erreur lors de la suppression du commentaire');
    return false;
  }
};

// ==================== NOTES ====================

// Récupérer toutes les notes d'un produit
export const getProductRatings = async (productId: string): Promise<IMarketRating[]> => {
  try {
    const { data, error } = await supabase
      .from('marketrating')
      .select('*')
      .eq('product_id', productId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erreur lors de la récupération des notes:', error);
      return [];
    }

    return (data || []).map(rating => ({
      id: rating.id,
      productId: rating.product_id,
      userId: rating.user_id,
      rating: rating.rating,
      isVerifiedPurchase: rating.is_verified_purchase,
      createdAt: new Date(rating.created_at),
      updatedAt: new Date(rating.updated_at)
    })) as IMarketRating[];
  } catch (error) {
    console.error('Erreur lors de la récupération des notes:', error);
    return [];
  }
};

// Ajouter ou mettre à jour une note
export const addOrUpdateRating = async (ratingData: ICreateRating): Promise<IMarketRating | null> => {
  try {
    // Utiliser la vraie authentification Supabase
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      toast.error('Vous devez être connecté pour noter');
      return null;
    }

    // Vérifier si l'utilisateur a acheté le produit (simulation pour l'instant)
    const isVerifiedPurchase = await checkVerifiedPurchase(user.id, ratingData.productId);

    // Vérifier si l'utilisateur a déjà noté ce produit
    const { data: existingRating } = await supabase
      .from('marketrating')
      .select('*')
      .eq('product_id', ratingData.productId)
      .eq('user_id', user.id)
      .single();

    let result;

    if (existingRating) {
      // Mettre à jour la note existante
      const { data, error } = await supabase
        .from('marketrating')
        .update({
          rating: ratingData.rating,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingRating.id)
        .select()
        .single();

      if (error) {
        console.error('Erreur lors de la mise à jour de la note:', error);
        toast.error('Erreur lors de la mise à jour de la note');
        return null;
      }

      result = data;
      toast.success('Note mise à jour avec succès');
    } else {
      // Créer une nouvelle note
      const { data, error } = await supabase
        .from('marketrating')
        .insert([{
          product_id: ratingData.productId,
          user_id: user.id,
          rating: ratingData.rating,
          is_verified_purchase: isVerifiedPurchase
        }])
        .select()
        .single();

      if (error) {
        console.error('Erreur lors de l\'ajout de la note:', error);
        toast.error('Erreur lors de l\'ajout de la note');
        return null;
      }

      result = data;
      toast.success('Note ajoutée avec succès');
    }

    return {
      id: result.id,
      productId: result.product_id,
      userId: result.user_id,
      rating: result.rating,
      isVerifiedPurchase: result.is_verified_purchase,
      createdAt: new Date(result.created_at),
      updatedAt: new Date(result.updated_at)
    } as IMarketRating;
  } catch (error) {
    console.error('Erreur lors de l\'ajout/mise à jour de la note:', error);
    toast.error('Erreur lors de l\'ajout de la note');
    return null;
  }
};

// Récupérer la note d'un utilisateur pour un produit
export const getUserRating = async (productId: string, userId: string): Promise<IMarketRating | null> => {
  try {
    const { data, error } = await supabase
      .from('marketrating')
      .select('*')
      .eq('product_id', productId)
      .eq('user_id', userId)
      .single();

    if (error || !data) {
      return null;
    }

    return {
      id: data.id,
      productId: data.product_id,
      userId: data.user_id,
      rating: data.rating,
      isVerifiedPurchase: data.is_verified_purchase,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    } as IMarketRating;
  } catch (error) {
    console.error('Erreur lors de la récupération de la note utilisateur:', error);
    return null;
  }
};

// ==================== STATISTIQUES ====================

// Récupérer les statistiques d'un produit
export const getProductStats = async (productId: string): Promise<IProductStats | null> => {
  try {
    const { data, error } = await supabase
      .from('product_stats')
      .select('*')
      .eq('product_id', productId)
      .single();

    if (error || !data) {
      // Retourner des statistiques vides si aucune donnée trouvée
      return {
        productId,
        totalRatings: 0,
        averageRating: 0,
        totalComments: 0,
        verifiedRatings: 0,
        verifiedComments: 0
      };
    }

    return {
      productId: data.product_id,
      totalRatings: data.total_ratings || 0,
      averageRating: parseFloat(data.average_rating) || 0,
      totalComments: data.total_comments || 0,
      verifiedRatings: data.verified_ratings || 0,
      verifiedComments: data.verified_comments || 0
    } as IProductStats;
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    // Fallback vers les données simulées en cas d'erreur
    return getMockProductStats(productId);
  }
};

// ==================== UTILITAIRES ====================

// Vérifier si un utilisateur a acheté un produit (simulation)
const checkVerifiedPurchase = async (userId: string, productId: string): Promise<boolean> => {
  // Dans une vraie application, ceci vérifierait dans la table des commandes
  // Pour l'instant, on simule avec une probabilité de 70%
  return Math.random() > 0.3;
};

// Récupérer tous les commentaires et notes d'un utilisateur
export const getUserReviews = async (userId: string) => {
  try {
    const [comments, ratings] = await Promise.all([
      supabase
        .from('comments_with_user_info')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false }),
      supabase
        .from('marketrating')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
    ]);

    return {
      comments: comments.data || [],
      ratings: ratings.data || []
    };
  } catch (error) {
    console.error('Erreur lors de la récupération des avis utilisateur:', error);
    return { comments: [], ratings: [] };
  }
};
