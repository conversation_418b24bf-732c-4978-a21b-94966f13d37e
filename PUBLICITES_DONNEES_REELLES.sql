-- ✅ PUBLICITÉS AVEC DONNÉES RÉELLES CAMEROUNAISES
-- Script pour créer des campagnes publicitaires avec de vraies entreprises et produits du Cameroun

-- =====================================================
-- 1. SUPPRIMER LES ANCIENNES DONNÉES DE TEST
-- =====================================================
DELETE FROM ad_engagement_metrics WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid,
  '00000000-0000-0000-0000-000000000004'::uuid,
  '00000000-0000-0000-0000-000000000005'::uuid
);

DELETE FROM ad_likes WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid,
  '00000000-0000-0000-0000-000000000004'::uuid,
  '00000000-0000-0000-0000-000000000005'::uuid
);

DELETE FROM ad_comments WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid,
  '00000000-0000-0000-0000-000000000004'::uuid,
  '00000000-0000-0000-0000-000000000005'::uuid
);

DELETE FROM ad_shares WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid,
  '00000000-0000-0000-0000-000000000004'::uuid,
  '00000000-0000-0000-0000-000000000005'::uuid
);

DELETE FROM ad_creatives WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid,
  '00000000-0000-0000-0000-000000000004'::uuid,
  '00000000-0000-0000-0000-000000000005'::uuid
);

DELETE FROM ad_campaigns WHERE id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid,
  '00000000-0000-0000-0000-000000000004'::uuid,
  '00000000-0000-0000-0000-000000000005'::uuid
);

-- =====================================================
-- 2. CRÉER DES ENTREPRISES RÉELLES CAMEROUNAISES
-- =====================================================

DO $$
DECLARE
  dexima_id uuid := '10000000-0000-0000-0000-000000000001'::uuid;
  olgane_id uuid := '10000000-0000-0000-0000-000000000002'::uuid;
  guinness_id uuid := '10000000-0000-0000-0000-000000000003'::uuid;
  mtn_id uuid := '10000000-0000-0000-0000-000000000004'::uuid;
  orange_id uuid := '10000000-0000-0000-0000-000000000005'::uuid;
BEGIN
  -- Créer les profils d'entreprises réelles
  INSERT INTO profiles (id, email, username, role, full_name, profile_picture, created_at) VALUES
  (dexima_id, '<EMAIL>', 'Dexima', 'business', 'Dexima Cameroun', 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100', now()),
  (olgane_id, '<EMAIL>', 'Olgane Cosmetics', 'business', 'Olgane Cosmetics Cameroun', 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=100', now()),
  (guinness_id, '<EMAIL>', 'Guinness Cameroun', 'business', 'Guinness Cameroun SA', 'https://images.unsplash.com/photo-1608270586620-248524c67de9?w=100', now()),
  (mtn_id, '<EMAIL>', 'MTN Cameroun', 'business', 'MTN Cameroun', 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=100', now()),
  (orange_id, '<EMAIL>', 'Orange Cameroun', 'business', 'Orange Cameroun', 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=100', now())
  ON CONFLICT (id) DO UPDATE SET
    username = EXCLUDED.username,
    full_name = EXCLUDED.full_name,
    profile_picture = EXCLUDED.profile_picture;

  -- Créer les campagnes publicitaires avec données réelles
  INSERT INTO ad_campaigns (
    id,
    business_id,
    title,
    description,
    image_url,
    target_url,
    bid_amount,
    daily_budget,
    total_budget,
    placements,
    status,
    start_date,
    impressions,
    clicks
  ) VALUES 
  -- 1. Dexima - Huile de beauté Olgane
  (
    '00000000-0000-0000-0000-000000000001'::uuid,
    dexima_id,
    'Huile de Beauté Olgane - Promotion Spéciale',
    'Découvrez l''huile de beauté Olgane, produit 100% naturel made in Cameroun. Nourrissez votre peau avec nos ingrédients locaux de qualité. Promotion limitée : -25% sur tous nos produits de beauté.',
    'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=600',
    'https://dexima.cm/produits/huile-olgane',
    350.00,
    8000.00,
    80000.00,
    '["newsfeed", "sidebar"]'::jsonb,
    'active',
    now(),
    245,
    18
  ),
  -- 2. Olgane Cosmetics - Crème hydratante
  (
    '00000000-0000-0000-0000-000000000002'::uuid,
    olgane_id,
    'Crème Hydratante Visage Olgane - Nouveauté',
    'Nouvelle crème hydratante visage Olgane enrichie au beurre de karité du Cameroun. Formule unique pour tous types de peaux. Testée dermatologiquement. Disponible en pharmacies et boutiques Olgane.',
    'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=600',
    'https://olgane.cm/nouveautes/creme-hydratante',
    280.00,
    6000.00,
    60000.00,
    '["newsfeed", "marketplace"]'::jsonb,
    'active',
    now(),
    189,
    12
  ),
  -- 3. Guinness Cameroun - Bière
  (
    '00000000-0000-0000-0000-000000000003'::uuid,
    guinness_id,
    'Guinness - La Bière des Vrais Connaisseurs',
    'Savourez l''authentique goût Guinness, brassée avec passion au Cameroun. Bière noire premium aux arômes uniques. Parfaite pour vos moments de détente et célébrations. Disponible dans tous les bars et supermarchés.',
    'https://images.unsplash.com/photo-1608270586620-248524c67de9?w=600',
    'https://guinness.cm/produits',
    400.00,
    10000.00,
    100000.00,
    '["newsfeed", "sidebar", "offers"]'::jsonb,
    'active',
    now(),
    312,
    25
  ),
  -- 4. MTN Cameroun - Forfait Internet
  (
    '00000000-0000-0000-0000-000000000004'::uuid,
    mtn_id,
    'MTN 4G+ - Forfait Internet Illimité',
    'Profitez de la meilleure connexion 4G+ au Cameroun avec MTN. Forfait internet illimité à partir de 5000 F CFA/mois. Couverture nationale, débit ultra-rapide. Souscrivez maintenant via *123# ou dans nos agences.',
    'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600',
    'https://mtn.cm/forfaits-internet',
    500.00,
    15000.00,
    150000.00,
    '["newsfeed", "sidebar", "marketplace"]'::jsonb,
    'active',
    now(),
    456,
    38
  ),
  -- 5. Orange Cameroun - Mobile Money
  (
    '00000000-0000-0000-0000-000000000005'::uuid,
    orange_id,
    'Orange Money - Transferts Gratuits',
    'Envoyez de l''argent partout au Cameroun avec Orange Money. Transferts gratuits entre comptes Orange Money ce mois-ci. Service disponible 24h/24, 7j/7. Plus de 10 000 points de service dans tout le pays.',
    'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600',
    'https://orange.cm/orange-money',
    450.00,
    12000.00,
    120000.00,
    '["newsfeed", "offers"]'::jsonb,
    'active',
    now(),
    378,
    31
  );

END $$;

-- =====================================================
-- 3. CRÉER LES MÉTRIQUES D'ENGAGEMENT INITIALES
-- =====================================================

INSERT INTO ad_engagement_metrics (
  campaign_id,
  total_likes,
  total_comments,
  total_shares,
  engagement_rate,
  last_updated
) VALUES 
('00000000-0000-0000-0000-000000000001'::uuid, 0, 0, 0, 0.0, now()),
('00000000-0000-0000-0000-000000000002'::uuid, 0, 0, 0, 0.0, now()),
('00000000-0000-0000-0000-000000000003'::uuid, 0, 0, 0, 0.0, now()),
('00000000-0000-0000-0000-000000000004'::uuid, 0, 0, 0, 0.0, now()),
('00000000-0000-0000-0000-000000000005'::uuid, 0, 0, 0, 0.0, now());

-- =====================================================
-- 4. VÉRIFICATION DES DONNÉES CRÉÉES
-- =====================================================

-- Afficher les entreprises créées
SELECT 
  id,
  username as "Nom Entreprise",
  full_name as "Nom Complet",
  email as "Email",
  'Entreprise créée ✅' as "Statut"
FROM profiles 
WHERE role = 'business' 
AND id IN (
  '10000000-0000-0000-0000-000000000001'::uuid,
  '10000000-0000-0000-0000-000000000002'::uuid,
  '10000000-0000-0000-0000-000000000003'::uuid,
  '10000000-0000-0000-0000-000000000004'::uuid,
  '10000000-0000-0000-0000-000000000005'::uuid
);

-- Afficher les campagnes créées
SELECT 
  id,
  title as "Titre Publicité",
  bid_amount as "Enchère (F CFA)",
  impressions as "Impressions",
  clicks as "Clics",
  'Campagne active ✅' as "Statut"
FROM ad_campaigns 
WHERE id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid,
  '00000000-0000-0000-0000-000000000004'::uuid,
  '00000000-0000-0000-0000-000000000005'::uuid
);

-- Afficher un résumé des publicités par entreprise
SELECT 
  p.username as "Entreprise",
  c.title as "Publicité",
  c.bid_amount as "Budget (F CFA)",
  c.impressions as "Vues",
  c.clicks as "Clics"
FROM ad_campaigns c
JOIN profiles p ON c.business_id = p.id
WHERE c.id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid,
  '00000000-0000-0000-0000-000000000004'::uuid,
  '00000000-0000-0000-0000-000000000005'::uuid
)
ORDER BY p.username;
