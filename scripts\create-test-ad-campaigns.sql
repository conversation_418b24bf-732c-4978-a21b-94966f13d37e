-- Script pour créer les campagnes publicitaires de test
-- Exécutez ce script dans l'éditeur SQL de Supabase pour créer les campagnes nécessaires

-- =====================================================
-- 1. VÉRIFICATION DES PRÉREQUIS
-- =====================================================

-- Vérifier que les tables existent
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'ad_campaigns') THEN
    RAISE EXCEPTION 'Table ad_campaigns n''existe pas. Exécutez d''abord les migrations publicitaires.';
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles') THEN
    RAISE EXCEPTION 'Table profiles n''existe pas.';
  END IF;
  
  RAISE NOTICE 'Tables vérifiées avec succès.';
END $$;

-- =====================================================
-- 2. CRÉER UN UTILISATEUR BUSINESS DE TEST SI NÉCESSAIRE
-- =====================================================

-- Insérer un profil business de test si il n'existe pas
INSERT INTO profiles (
  id,
  username,
  email,
  role,
  full_name,
  bio,
  profile_picture
) VALUES (
  '11111111-1111-1111-1111-111111111111'::uuid,
  'olgane_cosmetics',
  '<EMAIL>',
  'business',
  'Olgane Cosmetics',
  'Spécialiste en produits de beauté naturels et bio',
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=100'
) ON CONFLICT (id) DO UPDATE SET
  username = EXCLUDED.username,
  email = EXCLUDED.email,
  role = EXCLUDED.role,
  updated_at = now();

-- =====================================================
-- 3. CRÉER LES CAMPAGNES PUBLICITAIRES DE TEST
-- =====================================================

-- Supprimer les anciennes campagnes de test si elles existent
DELETE FROM ad_campaigns WHERE id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid
);

-- Insérer les nouvelles campagnes
INSERT INTO ad_campaigns (
  id,
  business_id,
  title,
  description,
  image_url,
  target_url,
  bid_amount,
  daily_budget,
  total_budget,
  placements,
  status,
  start_date,
  impressions,
  clicks,
  created_at
) VALUES 
-- Campagne 1: Promotion Été - Huile de Beauté
(
  '00000000-0000-0000-0000-000000000001'::uuid,
  '11111111-1111-1111-1111-111111111111'::uuid,
  'Promotion Été - Huile de Beauté',
  'Profitez de notre offre spéciale été avec 20% de réduction sur notre huile de beauté Olgane.',
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
  'https://example.com/promo-ete',
  250.00,
  5000.00,
  50000.00,
  '["newsfeed", "sidebar"]'::jsonb,
  'active',
  now(),
  150,
  8,
  now()
),
-- Campagne 2: Lancement Crème Hydratante
(
  '00000000-0000-0000-0000-000000000002'::uuid,
  '11111111-1111-1111-1111-111111111111'::uuid,
  'Lancement Crème Hydratante',
  'Découvrez notre nouvelle crème hydratante visage pour une peau éclatante et revitalisée.',
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
  'https://example.com/creme-hydratante',
  200.00,
  4000.00,
  40000.00,
  '["newsfeed", "marketplace"]'::jsonb,
  'active',
  now(),
  120,
  6,
  now()
),
-- Campagne 3: Promo Flash Sérum Anti-âge
(
  '00000000-0000-0000-0000-000000000003'::uuid,
  '11111111-1111-1111-1111-111111111111'::uuid,
  'Promo Flash Sérum Anti-âge',
  'Offre flash de 48h : -30% sur notre sérum anti-âge révolutionnaire.',
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
  'https://example.com/serum-anti-age',
  300.00,
  7000.00,
  70000.00,
  '["newsfeed", "sidebar", "marketplace"]'::jsonb,
  'active',
  now(),
  200,
  12,
  now()
);

-- =====================================================
-- 4. CRÉER LES CRÉATIFS PUBLICITAIRES
-- =====================================================

-- Supprimer les anciens créatifs
DELETE FROM ad_creatives WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid
);

-- Insérer les créatifs
INSERT INTO ad_creatives (
  campaign_id,
  title,
  description,
  image_url,
  call_to_action,
  status
) VALUES
(
  '00000000-0000-0000-0000-000000000001'::uuid,
  'Promotion Été - Huile de Beauté',
  'Profitez de notre offre spéciale été avec 20% de réduction sur notre huile de beauté Olgane.',
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
  'En savoir plus',
  'active'
),
(
  '00000000-0000-0000-0000-000000000002'::uuid,
  'Lancement Crème Hydratante',
  'Découvrez notre nouvelle crème hydratante visage pour une peau éclatante et revitalisée.',
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
  'Découvrir',
  'active'
),
(
  '00000000-0000-0000-0000-000000000003'::uuid,
  'Promo Flash Sérum Anti-âge',
  'Offre flash de 48h : -30% sur notre sérum anti-âge révolutionnaire.',
  'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400',
  'Profiter de l\'offre',
  'active'
);

-- =====================================================
-- 5. INITIALISER LES MÉTRIQUES D'ENGAGEMENT
-- =====================================================

-- Supprimer les anciennes métriques
DELETE FROM ad_engagement_metrics WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid
);

-- Insérer les métriques initiales
INSERT INTO ad_engagement_metrics (
  campaign_id,
  total_likes,
  total_comments,
  total_shares,
  engagement_rate,
  last_updated
) VALUES
(
  '00000000-0000-0000-0000-000000000001'::uuid,
  5,
  3,
  2,
  6.67, -- (5+3+2)/150*100
  now()
),
(
  '00000000-0000-0000-0000-000000000002'::uuid,
  3,
  2,
  1,
  5.00, -- (3+2+1)/120*100
  now()
),
(
  '00000000-0000-0000-0000-000000000003'::uuid,
  8,
  5,
  4,
  8.50, -- (8+5+4)/200*100
  now()
);

-- =====================================================
-- 6. VÉRIFICATION DES DONNÉES CRÉÉES
-- =====================================================

-- Afficher les campagnes créées
SELECT 
  'Campagnes créées:' as info,
  id,
  title,
  status,
  impressions,
  clicks,
  ROUND((clicks::decimal / NULLIF(impressions, 0) * 100), 2) as ctr_percent
FROM ad_campaigns 
WHERE id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid
)
ORDER BY created_at;

-- Afficher les métriques d'engagement
SELECT 
  'Métriques d\'engagement:' as info,
  campaign_id,
  total_likes,
  total_comments,
  total_shares,
  engagement_rate
FROM ad_engagement_metrics 
WHERE campaign_id IN (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000003'::uuid
)
ORDER BY campaign_id;

-- =====================================================
-- 7. MESSAGE DE SUCCÈS
-- =====================================================

SELECT 
  '✅ CAMPAGNES PUBLICITAIRES CRÉÉES AVEC SUCCÈS !' as message,
  '🎯 Les boutons d''interaction devraient maintenant fonctionner' as note,
  '🧪 Testez sur /ad-buttons-test ou dans le fil d''actualité' as instruction;

-- =====================================================
-- 8. INSTRUCTIONS POUR LES DÉVELOPPEURS
-- =====================================================

/*
INSTRUCTIONS POST-EXÉCUTION :

1. ✅ Les campagnes publicitaires sont maintenant créées avec les UUIDs corrects
2. ✅ Les boutons d'interaction devraient fonctionner
3. ✅ Les métriques d'engagement sont initialisées

POUR TESTER :
- Allez sur http://localhost:5173/ad-buttons-test
- Ou testez directement dans le fil d'actualité
- Ouvrez la console (F12) pour voir les logs de debug

CAMPAGNES CRÉÉES :
- 00000000-0000-0000-0000-000000000001 : Promotion Été - Huile de Beauté
- 00000000-0000-0000-0000-000000000002 : Lancement Crème Hydratante  
- 00000000-0000-0000-0000-000000000003 : Promo Flash Sérum Anti-âge

Si les boutons ne fonctionnent toujours pas :
1. Vérifiez que vous êtes connecté
2. Vérifiez les logs dans la console
3. Vérifiez que les politiques RLS autorisent les interactions
*/
