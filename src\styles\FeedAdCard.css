.feed-ad-card {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease, opacity 0.5s ease;
  opacity: 0;
  transform: translateY(10px);
}

.feed-ad-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.feed-ad-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.feed-ad-header {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.feed-ad-business-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.feed-ad-business-logo {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #e5e7eb;
}

.feed-ad-business-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 0.25rem 0;
}

.feed-ad-sponsored-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.feed-ad-sponsored-label {
  font-size: 0.75rem;
  color: #6b7280;
}

.feed-ad-info-icon {
  color: #9ca3af;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feed-ad-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #1f2937;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  width: 200px;
  text-align: center;
  z-index: 10;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  animation: fade-in 0.2s ease-in-out;
}

.feed-ad-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: #1f2937 transparent transparent transparent;
}

.feed-ad-content {
  padding: 1rem;
  position: relative;
}

.feed-ad-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 0.5rem 0;
}

.feed-ad-description {
  font-size: 0.875rem;
  color: #4b5563;
  margin: 0;
  line-height: 1.5;
}

.feed-ad-discount-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: #ef4444;
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  z-index: 5;
}

.feed-ad-image-container {
  position: relative;
  overflow: hidden;
}

.feed-ad-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.feed-ad-promo-banner {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(239, 68, 68, 0.8);
  color: white;
  text-align: center;
  padding: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.feed-ad-image-container:hover .feed-ad-promo-banner {
  transform: translateY(0);
}

.feed-ad-footer {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.feed-ad-cta-button {
  align-self: flex-start;
}

.feed-ad-engagement {
  display: flex;
  gap: 1rem;
  border-top: 1px solid #f3f4f6;
  padding-top: 1rem;
  position: relative;
  z-index: 1;
}

.feed-ad-engagement-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 0.875rem;
  padding: 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s, transform 0.1s;
  position: relative;
  z-index: 2 !important;
  pointer-events: auto !important;
  user-select: none;
  min-height: 40px;
  min-width: 80px;
}

.feed-ad-engagement-button:hover {
  background-color: #f3f4f6;
  color: #111827;
  transform: translateY(-1px);
}

.feed-ad-engagement-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none !important;
}

.feed-ad-engagement-button:disabled:hover {
  background-color: transparent;
  color: inherit;
  transform: none;
}

/* Styles pour améliorer la cliquabilité */
.feed-ad-engagement-button:active {
  transform: scale(0.98);
  background-color: #e5e7eb;
}

.feed-ad-engagement-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.feed-ad-engagement-button.liked {
  color: #ef4444;
}

.feed-ad-engagement-button.liked:hover {
  background-color: #fef2f2;
  color: #dc2626;
}

/* Animations */
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(14, 165, 233, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(14, 165, 233, 0);
  }
}

.zoom-effect:hover {
  transform: scale(1.05);
}

.shine-effect {
  position: relative;
  overflow: hidden;
}

.shine-effect::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  100% {
    transform: translateX(100%) rotate(30deg);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}



/* Styles pour le menu de partage amélioré */
.share-menu {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 16px;
  min-width: 280px;
  z-index: 50;
  animation: shareMenuSlideIn 0.2s ease-out;
}

@keyframes shareMenuSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.share-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}

.share-option:hover {
  background-color: #f8fafc;
  transform: translateX(2px);
}

.share-option-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.share-option-content {
  flex: 1;
}

.share-option-title {
  font-weight: 500;
  color: #1f2937;
  margin: 0;
  font-size: 14px;
}

.share-option-description {
  font-size: 12px;
  color: #6b7280;
  margin: 2px 0 0 0;
}

.share-menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.share-menu-title {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
  margin: 0;
}

.share-menu-close {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.share-menu-close:hover {
  color: #6b7280;
}

.share-menu-separator {
  border-top: 1px solid #f3f4f6;
  margin: 12px 0;
}

.share-menu-stats {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #f3f4f6;
  text-align: center;
}

.share-menu-stats-text {
  font-size: 12px;
  color: #6b7280;
}

/* Responsive styles */
@media (max-width: 768px) {
  .share-menu {
    min-width: 260px;
    padding: 12px;
  }

  .share-option {
    padding: 10px;
  }

  .share-option-icon {
    width: 28px;
    height: 28px;
  }
}

@media (max-width: 640px) {
  .feed-ad-engagement-button span {
    display: none;
  }

  .feed-ad-engagement {
    justify-content: space-around;
  }

  .share-menu {
    min-width: 240px;
    padding: 10px;
  }
}
