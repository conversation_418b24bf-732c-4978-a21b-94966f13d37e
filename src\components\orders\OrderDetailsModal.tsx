import React, { useState } from 'react';
import {
  X, Package, User, Phone, Mail, MapPin, Calendar, DollarSign,
  Truck, CheckCircle, Clock, Edit, Printer, MessageSquare,
  AlertTriangle, RefreshCw, Download, Copy, ExternalLink
} from 'lucide-react';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';
import { formatAmount } from '../../utils/formatUtils';
import '../../styles/OrderDetailsModal.css';

interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  sku?: string;
}

interface ShippingAddress {
  name: string;
  phone: string;
  email: string;
  street: string;
  city: string;
  postalCode: string;
  country: string;
  instructions?: string;
}

interface BusinessOrder {
  id: string;
  orderNumber: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  items: OrderItem[];
  subtotal: number;
  shippingCost: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: string;
  shippingAddress: ShippingAddress;
  trackingNumber?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  estimatedDelivery?: Date;
  priority: 'low' | 'normal' | 'high' | 'urgent';
}

interface OrderDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  order: BusinessOrder | null;
  onUpdateStatus: (orderId: string, newStatus: string) => void;
  onAddTracking: (orderId: string, trackingNumber: string) => void;
}

const OrderDetailsModal: React.FC<OrderDetailsModalProps> = ({
  isOpen,
  onClose,
  order,
  onUpdateStatus,
  onAddTracking
}) => {
  const [activeTab, setActiveTab] = useState<'details' | 'customer' | 'shipping' | 'timeline'>('details');
  const [newStatus, setNewStatus] = useState('');
  const [trackingNumber, setTrackingNumber] = useState('');
  const [showStatusUpdate, setShowStatusUpdate] = useState(false);
  const [showTrackingUpdate, setShowTrackingUpdate] = useState(false);

  if (!isOpen || !order) return null;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock size={16} />;
      case 'confirmed': return <CheckCircle size={16} />;
      case 'processing': return <RefreshCw size={16} />;
      case 'shipped': return <Truck size={16} />;
      case 'delivered': return <CheckCircle size={16} />;
      case 'cancelled': return <X size={16} />;
      default: return <Clock size={16} />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return 'En attente';
      case 'confirmed': return 'Confirmée';
      case 'processing': return 'En traitement';
      case 'shipped': return 'Expédiée';
      case 'delivered': return 'Livrée';
      case 'cancelled': return 'Annulée';
      default: return status;
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const handleStatusUpdate = () => {
    if (newStatus && order) {
      onUpdateStatus(order.id, newStatus);
      setShowStatusUpdate(false);
      setNewStatus('');
    }
  };

  const handleTrackingUpdate = () => {
    if (trackingNumber && order) {
      onAddTracking(order.id, trackingNumber);
      setShowTrackingUpdate(false);
      setTrackingNumber('');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="order-details-modal-overlay">
      <div className="order-details-modal">
        {/* En-tête */}
        <div className="modal-header">
          <div className="header-left">
            <div className="order-title">
              <Package size={24} />
              <div>
                <h2>Commande {order.orderNumber}</h2>
                <p>Créée le {formatDate(order.createdAt)}</p>
              </div>
            </div>
          </div>
          
          <div className="header-actions">
            <Button variant="outline" size="sm">
              <Printer size={16} />
              Imprimer
            </Button>
            <Button variant="outline" size="sm">
              <Download size={16} />
              PDF
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X size={16} />
            </Button>
          </div>
        </div>

        {/* Statut et actions rapides */}
        <div className="order-status-bar">
          <div className="status-info">
            <div className={`status-badge ${order.status}`}>
              {getStatusIcon(order.status)}
              <span>{getStatusLabel(order.status)}</span>
            </div>
            
            <div className={`payment-badge ${order.paymentStatus}`}>
              <DollarSign size={14} />
              <span>
                {order.paymentStatus === 'paid' ? 'Payé' : 
                 order.paymentStatus === 'pending' ? 'En attente' : 
                 order.paymentStatus === 'failed' ? 'Échec' : 'Remboursé'}
              </span>
            </div>

            <div className="total-amount">
              <span>Total: {formatAmount(order.totalAmount)}</span>
            </div>
          </div>

          <div className="quick-actions">
            <Button size="sm" onClick={() => setShowStatusUpdate(true)}>
              <Edit size={14} />
              Changer statut
            </Button>
            
            {!order.trackingNumber && (
              <Button size="sm" variant="outline" onClick={() => setShowTrackingUpdate(true)}>
                <Truck size={14} />
                Ajouter suivi
              </Button>
            )}
            
            <Button size="sm" variant="outline">
              <MessageSquare size={14} />
              Contacter client
            </Button>
          </div>
        </div>

        {/* Onglets */}
        <div className="modal-tabs">
          <button
            className={`tab ${activeTab === 'details' ? 'active' : ''}`}
            onClick={() => setActiveTab('details')}
          >
            <Package size={16} />
            Détails
          </button>
          <button
            className={`tab ${activeTab === 'customer' ? 'active' : ''}`}
            onClick={() => setActiveTab('customer')}
          >
            <User size={16} />
            Client
          </button>
          <button
            className={`tab ${activeTab === 'shipping' ? 'active' : ''}`}
            onClick={() => setActiveTab('shipping')}
          >
            <Truck size={16} />
            Livraison
          </button>
          <button
            className={`tab ${activeTab === 'timeline' ? 'active' : ''}`}
            onClick={() => setActiveTab('timeline')}
          >
            <Calendar size={16} />
            Historique
          </button>
        </div>

        {/* Contenu des onglets */}
        <div className="modal-content">
          {activeTab === 'details' && (
            <div className="details-tab">
              {/* Produits */}
              <Card>
                <CardBody>
                  <h3>Produits commandés</h3>
                  <div className="order-items">
                    {order.items.map((item) => (
                      <div key={item.id} className="order-item">
                        <img src={item.productImage} alt={item.productName} />
                        <div className="item-details">
                          <div className="item-name">{item.productName}</div>
                          {item.sku && <div className="item-sku">SKU: {item.sku}</div>}
                          <div className="item-price">
                            {formatAmount(item.unitPrice)} × {item.quantity}
                          </div>
                        </div>
                        <div className="item-total">
                          {formatAmount(item.subtotal)}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>

              {/* Résumé financier */}
              <Card>
                <CardBody>
                  <h3>Résumé financier</h3>
                  <div className="financial-summary">
                    <div className="summary-line">
                      <span>Sous-total</span>
                      <span>{formatAmount(order.subtotal)}</span>
                    </div>
                    <div className="summary-line">
                      <span>Frais de livraison</span>
                      <span>{formatAmount(order.shippingCost)}</span>
                    </div>
                    {order.discountAmount > 0 && (
                      <div className="summary-line discount">
                        <span>Remise</span>
                        <span>-{formatAmount(order.discountAmount)}</span>
                      </div>
                    )}
                    {order.taxAmount > 0 && (
                      <div className="summary-line">
                        <span>Taxes</span>
                        <span>{formatAmount(order.taxAmount)}</span>
                      </div>
                    )}
                    <div className="summary-line total">
                      <span>Total</span>
                      <span>{formatAmount(order.totalAmount)}</span>
                    </div>
                  </div>
                  
                  <div className="payment-info">
                    <div className="payment-method">
                      <strong>Méthode de paiement:</strong> {order.paymentMethod}
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          )}

          {activeTab === 'customer' && (
            <div className="customer-tab">
              <Card>
                <CardBody>
                  <h3>Informations client</h3>
                  <div className="customer-details">
                    <div className="detail-row">
                      <User size={16} />
                      <span>Nom:</span>
                      <span>{order.customerName}</span>
                      <Button size="sm" variant="ghost" onClick={() => copyToClipboard(order.customerName)}>
                        <Copy size={12} />
                      </Button>
                    </div>
                    
                    <div className="detail-row">
                      <Mail size={16} />
                      <span>Email:</span>
                      <span>{order.customerEmail}</span>
                      <Button size="sm" variant="ghost" onClick={() => copyToClipboard(order.customerEmail)}>
                        <Copy size={12} />
                      </Button>
                    </div>
                    
                    <div className="detail-row">
                      <Phone size={16} />
                      <span>Téléphone:</span>
                      <span>{order.customerPhone}</span>
                      <Button size="sm" variant="ghost" onClick={() => copyToClipboard(order.customerPhone)}>
                        <Copy size={12} />
                      </Button>
                    </div>
                  </div>

                  <div className="customer-actions">
                    <Button>
                      <Mail size={14} />
                      Envoyer email
                    </Button>
                    <Button variant="outline">
                      <Phone size={14} />
                      Appeler
                    </Button>
                    <Button variant="outline">
                      <ExternalLink size={14} />
                      Voir profil
                    </Button>
                  </div>
                </CardBody>
              </Card>
            </div>
          )}

          {activeTab === 'shipping' && (
            <div className="shipping-tab">
              <Card>
                <CardBody>
                  <h3>Adresse de livraison</h3>
                  <div className="shipping-address">
                    <div className="address-details">
                      <div><strong>{order.shippingAddress.name}</strong></div>
                      <div>{order.shippingAddress.street}</div>
                      <div>{order.shippingAddress.city}, {order.shippingAddress.postalCode}</div>
                      <div>{order.shippingAddress.country}</div>
                      <div>{order.shippingAddress.phone}</div>
                    </div>
                    
                    {order.shippingAddress.instructions && (
                      <div className="delivery-instructions">
                        <strong>Instructions de livraison:</strong>
                        <p>{order.shippingAddress.instructions}</p>
                      </div>
                    )}
                  </div>

                  {order.trackingNumber && (
                    <div className="tracking-info">
                      <h4>Suivi de livraison</h4>
                      <div className="tracking-number">
                        <span>Numéro de suivi: <strong>{order.trackingNumber}</strong></span>
                        <Button size="sm" variant="ghost" onClick={() => copyToClipboard(order.trackingNumber!)}>
                          <Copy size={12} />
                        </Button>
                      </div>
                      
                      {order.estimatedDelivery && (
                        <div className="estimated-delivery">
                          <Calendar size={14} />
                          <span>Livraison estimée: {formatDate(order.estimatedDelivery)}</span>
                        </div>
                      )}
                    </div>
                  )}
                </CardBody>
              </Card>
            </div>
          )}

          {activeTab === 'timeline' && (
            <div className="timeline-tab">
              <Card>
                <CardBody>
                  <h3>Historique de la commande</h3>
                  <div className="order-timeline">
                    <div className="timeline-item">
                      <div className="timeline-icon">
                        <Package size={16} />
                      </div>
                      <div className="timeline-content">
                        <div className="timeline-title">Commande créée</div>
                        <div className="timeline-date">{formatDate(order.createdAt)}</div>
                      </div>
                    </div>
                    
                    {order.status !== 'pending' && (
                      <div className="timeline-item">
                        <div className="timeline-icon">
                          <CheckCircle size={16} />
                        </div>
                        <div className="timeline-content">
                          <div className="timeline-title">Commande confirmée</div>
                          <div className="timeline-date">{formatDate(order.updatedAt)}</div>
                        </div>
                      </div>
                    )}
                    
                    {order.trackingNumber && (
                      <div className="timeline-item">
                        <div className="timeline-icon">
                          <Truck size={16} />
                        </div>
                        <div className="timeline-content">
                          <div className="timeline-title">Commande expédiée</div>
                          <div className="timeline-date">{formatDate(order.updatedAt)}</div>
                          <div className="timeline-note">Numéro de suivi: {order.trackingNumber}</div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardBody>
              </Card>
            </div>
          )}
        </div>

        {/* Modales de mise à jour */}
        {showStatusUpdate && (
          <div className="update-modal">
            <div className="update-content">
              <h4>Changer le statut</h4>
              <select value={newStatus} onChange={(e) => setNewStatus(e.target.value)}>
                <option value="">Sélectionner un statut</option>
                <option value="pending">En attente</option>
                <option value="confirmed">Confirmée</option>
                <option value="processing">En traitement</option>
                <option value="shipped">Expédiée</option>
                <option value="delivered">Livrée</option>
                <option value="cancelled">Annulée</option>
              </select>
              <div className="update-actions">
                <Button onClick={handleStatusUpdate}>Confirmer</Button>
                <Button variant="outline" onClick={() => setShowStatusUpdate(false)}>Annuler</Button>
              </div>
            </div>
          </div>
        )}

        {showTrackingUpdate && (
          <div className="update-modal">
            <div className="update-content">
              <h4>Ajouter un numéro de suivi</h4>
              <input
                type="text"
                placeholder="Numéro de suivi"
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
              />
              <div className="update-actions">
                <Button onClick={handleTrackingUpdate}>Ajouter</Button>
                <Button variant="outline" onClick={() => setShowTrackingUpdate(false)}>Annuler</Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderDetailsModal;
