import { supabase } from '../lib/supabase';
import { UserRole } from '../types/index';

/**
 * Utilitaires pour diagnostiquer et corriger les problèmes de rôles utilisateur
 */

/**
 * Diagnostique les rôles utilisateur dans la base de données
 */
export const diagnoseUserRoles = async () => {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('diagnoseUserRoles: Fonction disponible uniquement en mode développement');
    return;
  }

  console.log('🔍 DIAGNOSTIC DES RÔLES UTILISATEUR');
  
  try {
    // Récupérer tous les profils
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username, email, role')
      .order('username');

    if (profilesError) {
      console.error('Erreur lors de la récupération des profils:', profilesError);
      return;
    }

    console.log('📊 Profils trouvés:', profiles?.length || 0);
    
    // Analyser les rôles
    const roleStats = profiles?.reduce((acc, profile) => {
      acc[profile.role] = (acc[profile.role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    console.log('📈 Répartition des rôles:', roleStats);

    // Chercher spécifiquement Dexima
    const dexima = profiles?.find(p => 
      p.username?.toLowerCase().includes('dexima') || 
      p.email?.toLowerCase().includes('dexima')
    );

    if (dexima) {
      console.log('🏢 DEXIMA TROUVÉ:', dexima);
      
      // Vérifier le profil business
      const { data: businessProfile, error: businessError } = await supabase
        .from('business_profiles')
        .select('*')
        .eq('id', dexima.id)
        .single();

      if (businessError) {
        console.warn('⚠️ Pas de profil business pour Dexima:', businessError);
      } else {
        console.log('🏢 Profil business Dexima:', businessProfile);
      }
    } else {
      console.warn('⚠️ Dexima non trouvé dans les profils');
    }

    // Lister tous les utilisateurs business
    const businessUsers = profiles?.filter(p => p.role === 'business') || [];
    console.log('🏢 Utilisateurs business:', businessUsers);

  } catch (error) {
    console.error('Erreur lors du diagnostic:', error);
  }
};

/**
 * Corrige le rôle d'un utilisateur spécifique
 */
export const fixUserRole = async (username: string, newRole: UserRole) => {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('fixUserRole: Fonction disponible uniquement en mode développement');
    return;
  }

  console.log(`🔧 Correction du rôle pour ${username} → ${newRole}`);

  try {
    // Trouver l'utilisateur
    const { data: user, error: findError } = await supabase
      .from('profiles')
      .select('id, username, role')
      .ilike('username', `%${username}%`)
      .single();

    if (findError || !user) {
      console.error('Utilisateur non trouvé:', findError);
      return;
    }

    console.log('👤 Utilisateur trouvé:', user);

    // Mettre à jour le rôle
    const { data: updatedUser, error: updateError } = await supabase
      .from('profiles')
      .update({ role: newRole })
      .eq('id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('Erreur lors de la mise à jour:', updateError);
      return;
    }

    console.log('✅ Rôle mis à jour:', updatedUser);

    // Si c'est un business, créer le profil business si nécessaire
    if (newRole === UserRole.BUSINESS) {
      const { data: existingBusiness } = await supabase
        .from('business_profiles')
        .select('id')
        .eq('id', user.id)
        .single();

      if (!existingBusiness) {
        const { data: newBusiness, error: businessError } = await supabase
          .from('business_profiles')
          .insert({
            id: user.id,
            business_name: user.username,
            business_description: 'Profil business créé automatiquement'
          })
          .select()
          .single();

        if (businessError) {
          console.error('Erreur lors de la création du profil business:', businessError);
        } else {
          console.log('🏢 Profil business créé:', newBusiness);
        }
      }
    }

  } catch (error) {
    console.error('Erreur lors de la correction:', error);
  }
};

/**
 * Fonction de test rapide pour Dexima
 */
export const fixDexima = async () => {
  console.log('🔧 Correction rapide pour Dexima...');
  await diagnoseUserRoles();
  await fixUserRole('dexima', UserRole.BUSINESS);
  console.log('✅ Correction terminée. Rafraîchissez la page pour voir les changements.');
};

// Exposer les fonctions globalement en mode développement
if (process.env.NODE_ENV === 'development') {
  (window as any).diagnoseUserRoles = diagnoseUserRoles;
  (window as any).fixUserRole = fixUserRole;
  (window as any).fixDexima = fixDexima;
  
  console.log('🛠️ Fonctions de debug disponibles:');
  console.log('- window.diagnoseUserRoles()');
  console.log('- window.fixUserRole("username", "business")');
  console.log('- window.fixDexima()');
}
