-- =====================================================
-- MIGRATION: Système de Paiement et d'Abonnement Complet
-- Version: 1.0.0
-- Date: 2024-12-19
-- Description: Création de toutes les tables pour le système de paiement,
--              abonnements, reçus et gestion des méthodes de paiement
-- =====================================================

-- Extension pour UUID si pas déjà activée
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. TABLE DES PLANS D'ABONNEMENT
-- =====================================================
CREATE TABLE subscription_plans (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'XOF',
    duration_days INTEGER NOT NULL DEFAULT 30,
    features JSONB NOT NULL DEFAULT '[]',
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_trial BOOLEAN NOT NULL DEFAULT false,
    max_reviews INTEGER,
    max_products INTEGER,
    has_ai_advisor BOOLEAN NOT NULL DEFAULT false,
    has_advanced_analytics BOOLEAN NOT NULL DEFAULT false,
    has_api_access BOOLEAN NOT NULL DEFAULT false,
    has_priority_support BOOLEAN NOT NULL DEFAULT false,
    has_phone_support BOOLEAN NOT NULL DEFAULT false,
    has_dedicated_manager BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les requêtes fréquentes
CREATE INDEX idx_subscription_plans_active ON subscription_plans(is_active);
CREATE INDEX idx_subscription_plans_price ON subscription_plans(price);

-- =====================================================
-- 2. TABLE DES ABONNEMENTS BUSINESS
-- =====================================================
CREATE TABLE business_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL,
    plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(id),
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_trial BOOLEAN NOT NULL DEFAULT false,
    auto_renew BOOLEAN NOT NULL DEFAULT false,
    trial_used BOOLEAN NOT NULL DEFAULT false,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les requêtes fréquentes
CREATE INDEX idx_business_subscriptions_business_id ON business_subscriptions(business_id);
CREATE INDEX idx_business_subscriptions_active ON business_subscriptions(business_id, is_active);
CREATE INDEX idx_business_subscriptions_end_date ON business_subscriptions(end_date);
CREATE INDEX idx_business_subscriptions_plan ON business_subscriptions(plan_id);

-- =====================================================
-- 3. TABLE DES MÉTHODES DE PAIEMENT
-- =====================================================
CREATE TABLE payment_methods (
    id VARCHAR(50) PRIMARY KEY,
    business_id UUID NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('card', 'mobile_money', 'bank_transfer', 'crypto')),
    provider VARCHAR(50) NOT NULL, -- Visa, Mastercard, Orange Money, MTN Money, etc.

    -- Pour les cartes bancaires
    card_last4 VARCHAR(4),
    card_brand VARCHAR(20),
    card_exp_month INTEGER,
    card_exp_year INTEGER,
    card_fingerprint VARCHAR(100),

    -- Pour mobile money
    phone_number VARCHAR(20),
    phone_country_code VARCHAR(5),

    -- Pour virements bancaires
    bank_name VARCHAR(100),
    bank_account_last4 VARCHAR(4),
    bank_routing_number VARCHAR(20),

    -- Métadonnées
    is_default BOOLEAN NOT NULL DEFAULT false,
    is_verified BOOLEAN NOT NULL DEFAULT false,
    metadata JSONB DEFAULT '{}',

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les requêtes fréquentes
CREATE INDEX idx_payment_methods_business_id ON payment_methods(business_id);
CREATE INDEX idx_payment_methods_type ON payment_methods(type);
CREATE INDEX idx_payment_methods_default ON payment_methods(business_id, is_default);

-- =====================================================
-- 4. TABLE DES INTENTIONS DE PAIEMENT
-- =====================================================
CREATE TABLE payment_intents (
    id VARCHAR(50) PRIMARY KEY,
    business_id UUID NOT NULL,
    plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(id),
    payment_method_id VARCHAR(50) REFERENCES payment_methods(id),

    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'XOF',

    status VARCHAR(20) NOT NULL DEFAULT 'pending'
        CHECK (status IN ('pending', 'processing', 'succeeded', 'failed', 'canceled', 'requires_action')),

    -- Détails du traitement
    processor VARCHAR(50), -- stripe, paypal, orange_money, mtn_money, etc.
    processor_payment_id VARCHAR(100),
    processor_response JSONB,

    -- Métadonnées
    description TEXT,
    metadata JSONB DEFAULT '{}',

    -- Gestion des erreurs
    failure_code VARCHAR(50),
    failure_message TEXT,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Index pour les requêtes fréquentes
CREATE INDEX idx_payment_intents_business_id ON payment_intents(business_id);
CREATE INDEX idx_payment_intents_status ON payment_intents(status);
CREATE INDEX idx_payment_intents_created_at ON payment_intents(created_at);
CREATE INDEX idx_payment_intents_processor ON payment_intents(processor);

-- =====================================================
-- 5. TABLE DES REÇUS DE PAIEMENT
-- =====================================================
CREATE TABLE payment_receipts (
    id VARCHAR(50) PRIMARY KEY,
    payment_intent_id VARCHAR(50) NOT NULL REFERENCES payment_intents(id),
    business_id UUID NOT NULL,
    plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(id),

    -- Informations de facturation
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    plan_name VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'XOF',

    -- Détails du paiement
    payment_method JSONB NOT NULL, -- Copie des détails de la méthode de paiement
    transaction_id VARCHAR(100) NOT NULL,

    status VARCHAR(20) NOT NULL DEFAULT 'paid'
        CHECK (status IN ('paid', 'refunded', 'partially_refunded', 'disputed')),

    -- Informations business (snapshot au moment du paiement)
    business_info JSONB NOT NULL,

    -- URLs et fichiers
    receipt_url TEXT,
    pdf_path TEXT,

    -- Gestion des remboursements
    refunded_amount DECIMAL(10,2) DEFAULT 0.00,
    refunded_at TIMESTAMP WITH TIME ZONE,
    refund_reason TEXT,

    -- Timestamps
    paid_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les requêtes fréquentes
CREATE INDEX idx_payment_receipts_business_id ON payment_receipts(business_id);
CREATE INDEX idx_payment_receipts_invoice_number ON payment_receipts(invoice_number);
CREATE INDEX idx_payment_receipts_payment_intent ON payment_receipts(payment_intent_id);
CREATE INDEX idx_payment_receipts_paid_at ON payment_receipts(paid_at);
CREATE INDEX idx_payment_receipts_status ON payment_receipts(status);

-- =====================================================
-- 6. TABLE DES ÉVÉNEMENTS DE PAIEMENT (AUDIT TRAIL)
-- =====================================================
CREATE TABLE payment_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_intent_id VARCHAR(50) NOT NULL REFERENCES payment_intents(id),
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB NOT NULL DEFAULT '{}',

    -- Métadonnées
    source VARCHAR(50), -- api, webhook, manual, system
    user_id UUID,
    ip_address INET,
    user_agent TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour l'audit trail
CREATE INDEX idx_payment_events_payment_intent ON payment_events(payment_intent_id);
CREATE INDEX idx_payment_events_type ON payment_events(event_type);
CREATE INDEX idx_payment_events_created_at ON payment_events(created_at);

-- =====================================================
-- 7. TABLE DES WEBHOOKS DE PAIEMENT
-- =====================================================
CREATE TABLE payment_webhooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    provider VARCHAR(50) NOT NULL,
    event_id VARCHAR(100) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    payment_intent_id VARCHAR(50) REFERENCES payment_intents(id),

    -- Données du webhook
    raw_data JSONB NOT NULL,
    processed BOOLEAN NOT NULL DEFAULT false,
    processed_at TIMESTAMP WITH TIME ZONE,

    -- Gestion des erreurs
    error_count INTEGER NOT NULL DEFAULT 0,
    last_error TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les webhooks
CREATE INDEX idx_payment_webhooks_provider ON payment_webhooks(provider);
CREATE INDEX idx_payment_webhooks_event_id ON payment_webhooks(provider, event_id);
CREATE INDEX idx_payment_webhooks_processed ON payment_webhooks(processed);
CREATE INDEX idx_payment_webhooks_payment_intent ON payment_webhooks(payment_intent_id);

-- =====================================================
-- 8. TABLE DES COUPONS ET RÉDUCTIONS
-- =====================================================
CREATE TABLE subscription_coupons (
    id VARCHAR(50) PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,

    -- Type de réduction
    discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount')),
    discount_value DECIMAL(10,2) NOT NULL,

    -- Restrictions
    min_amount DECIMAL(10,2),
    max_discount DECIMAL(10,2),
    applicable_plans TEXT[], -- Array des plan_ids applicables

    -- Limites d'utilisation
    usage_limit INTEGER,
    usage_count INTEGER NOT NULL DEFAULT 0,
    usage_limit_per_customer INTEGER,

    -- Validité
    valid_from TIMESTAMP WITH TIME ZONE,
    valid_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN NOT NULL DEFAULT true,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les coupons
CREATE INDEX idx_subscription_coupons_code ON subscription_coupons(code);
CREATE INDEX idx_subscription_coupons_active ON subscription_coupons(is_active);
CREATE INDEX idx_subscription_coupons_valid ON subscription_coupons(valid_from, valid_until);

-- =====================================================
-- 9. TABLE D'UTILISATION DES COUPONS
-- =====================================================
CREATE TABLE coupon_usages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    coupon_id VARCHAR(50) NOT NULL REFERENCES subscription_coupons(id),
    business_id UUID NOT NULL,
    payment_intent_id VARCHAR(50) NOT NULL REFERENCES payment_intents(id),

    discount_amount DECIMAL(10,2) NOT NULL,
    original_amount DECIMAL(10,2) NOT NULL,
    final_amount DECIMAL(10,2) NOT NULL,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour l'utilisation des coupons
CREATE INDEX idx_coupon_usages_coupon_id ON coupon_usages(coupon_id);
CREATE INDEX idx_coupon_usages_business_id ON coupon_usages(business_id);
CREATE INDEX idx_coupon_usages_payment_intent ON coupon_usages(payment_intent_id);

-- =====================================================
-- 10. FONCTIONS ET TRIGGERS
-- =====================================================

-- Fonction pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour updated_at
CREATE TRIGGER update_subscription_plans_updated_at
    BEFORE UPDATE ON subscription_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_business_subscriptions_updated_at
    BEFORE UPDATE ON business_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_methods_updated_at
    BEFORE UPDATE ON payment_methods
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_intents_updated_at
    BEFORE UPDATE ON payment_intents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_receipts_updated_at
    BEFORE UPDATE ON payment_receipts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscription_coupons_updated_at
    BEFORE UPDATE ON subscription_coupons
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Fonction pour générer un numéro de facture unique
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
    year_part TEXT;
    sequence_part TEXT;
    invoice_number TEXT;
BEGIN
    year_part := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;

    -- Générer un numéro séquentiel basé sur le timestamp
    sequence_part := LPAD((EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000)::BIGINT::TEXT, 10, '0');
    sequence_part := RIGHT(sequence_part, 6);

    invoice_number := 'INV-' || year_part || '-' || sequence_part;

    -- Vérifier l'unicité et incrémenter si nécessaire
    WHILE EXISTS (SELECT 1 FROM payment_receipts WHERE invoice_number = invoice_number) LOOP
        sequence_part := LPAD((sequence_part::INTEGER + 1)::TEXT, 6, '0');
        invoice_number := 'INV-' || year_part || '-' || sequence_part;
    END LOOP;

    RETURN invoice_number;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour vérifier si un business peut utiliser un coupon
CREATE OR REPLACE FUNCTION can_use_coupon(
    p_coupon_code TEXT,
    p_business_id UUID,
    p_plan_id TEXT,
    p_amount DECIMAL
)
RETURNS BOOLEAN AS $$
DECLARE
    coupon_record subscription_coupons%ROWTYPE;
    usage_count_for_customer INTEGER;
BEGIN
    -- Récupérer le coupon
    SELECT * INTO coupon_record
    FROM subscription_coupons
    WHERE code = p_coupon_code AND is_active = true;

    -- Vérifier si le coupon existe
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- Vérifier la validité temporelle
    IF coupon_record.valid_from IS NOT NULL AND CURRENT_TIMESTAMP < coupon_record.valid_from THEN
        RETURN FALSE;
    END IF;

    IF coupon_record.valid_until IS NOT NULL AND CURRENT_TIMESTAMP > coupon_record.valid_until THEN
        RETURN FALSE;
    END IF;

    -- Vérifier la limite d'utilisation globale
    IF coupon_record.usage_limit IS NOT NULL AND coupon_record.usage_count >= coupon_record.usage_limit THEN
        RETURN FALSE;
    END IF;

    -- Vérifier la limite d'utilisation par client
    IF coupon_record.usage_limit_per_customer IS NOT NULL THEN
        SELECT COUNT(*) INTO usage_count_for_customer
        FROM coupon_usages
        WHERE coupon_id = coupon_record.id AND business_id = p_business_id;

        IF usage_count_for_customer >= coupon_record.usage_limit_per_customer THEN
            RETURN FALSE;
        END IF;
    END IF;

    -- Vérifier le montant minimum
    IF coupon_record.min_amount IS NOT NULL AND p_amount < coupon_record.min_amount THEN
        RETURN FALSE;
    END IF;

    -- Vérifier les plans applicables
    IF coupon_record.applicable_plans IS NOT NULL AND NOT (p_plan_id = ANY(coupon_record.applicable_plans)) THEN
        RETURN FALSE;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour calculer la réduction d'un coupon
CREATE OR REPLACE FUNCTION calculate_coupon_discount(
    p_coupon_code TEXT,
    p_amount DECIMAL
)
RETURNS DECIMAL AS $$
DECLARE
    coupon_record subscription_coupons%ROWTYPE;
    discount_amount DECIMAL;
BEGIN
    -- Récupérer le coupon
    SELECT * INTO coupon_record
    FROM subscription_coupons
    WHERE code = p_coupon_code AND is_active = true;

    IF NOT FOUND THEN
        RETURN 0;
    END IF;

    -- Calculer la réduction
    IF coupon_record.discount_type = 'percentage' THEN
        discount_amount := p_amount * (coupon_record.discount_value / 100);
    ELSE
        discount_amount := coupon_record.discount_value;
    END IF;

    -- Appliquer la limite maximale de réduction
    IF coupon_record.max_discount IS NOT NULL AND discount_amount > coupon_record.max_discount THEN
        discount_amount := coupon_record.max_discount;
    END IF;

    -- S'assurer que la réduction ne dépasse pas le montant
    IF discount_amount > p_amount THEN
        discount_amount := p_amount;
    END IF;

    RETURN discount_amount;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 11. VUES UTILES
-- =====================================================

-- Vue pour les abonnements actifs avec détails du plan
CREATE VIEW active_subscriptions AS
SELECT
    bs.*,
    sp.name as plan_name,
    sp.price as plan_price,
    sp.currency as plan_currency,
    sp.duration_days,
    sp.features,
    (bs.end_date - CURRENT_TIMESTAMP) as time_remaining,
    EXTRACT(DAYS FROM (bs.end_date - CURRENT_TIMESTAMP)) as days_remaining
FROM business_subscriptions bs
JOIN subscription_plans sp ON bs.plan_id = sp.id
WHERE bs.is_active = true;

-- Vue pour les statistiques de paiement par business
CREATE VIEW business_payment_stats AS
SELECT
    pr.business_id,
    COUNT(*) as total_payments,
    SUM(pr.amount) as total_amount,
    AVG(pr.amount) as average_amount,
    MIN(pr.paid_at) as first_payment,
    MAX(pr.paid_at) as last_payment,
    COUNT(CASE WHEN pr.status = 'paid' THEN 1 END) as successful_payments,
    COUNT(CASE WHEN pr.status = 'refunded' THEN 1 END) as refunded_payments,
    SUM(CASE WHEN pr.status = 'refunded' THEN pr.amount ELSE 0 END) as total_refunded
FROM payment_receipts pr
GROUP BY pr.business_id;

-- Vue pour les revenus par plan
CREATE VIEW plan_revenue_stats AS
SELECT
    sp.id as plan_id,
    sp.name as plan_name,
    COUNT(pr.id) as total_subscriptions,
    SUM(pr.amount) as total_revenue,
    AVG(pr.amount) as average_revenue,
    COUNT(CASE WHEN pr.paid_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as subscriptions_last_30_days,
    SUM(CASE WHEN pr.paid_at >= CURRENT_DATE - INTERVAL '30 days' THEN pr.amount ELSE 0 END) as revenue_last_30_days
FROM subscription_plans sp
LEFT JOIN payment_receipts pr ON sp.id = pr.plan_id AND pr.status = 'paid'
GROUP BY sp.id, sp.name
ORDER BY total_revenue DESC;

-- =====================================================
-- 12. DONNÉES INITIALES
-- =====================================================

-- Insertion des plans d'abonnement par défaut
INSERT INTO subscription_plans (id, name, description, price, duration_days, features, is_trial, max_reviews, has_ai_advisor, has_advanced_analytics, has_api_access, has_priority_support, has_phone_support, has_dedicated_manager) VALUES
('free-trial', 'Essai Gratuit', 'Découvrez toutes nos fonctionnalités pendant 7 jours', 0.00, 7,
 '["Accès complet à l''IA Conseiller", "Tableaux de bord interactifs", "Gestion des avis clients", "Notifications automatiques", "Support par email", "Jusqu''à 100 avis analysés"]'::jsonb,
 true, 100, true, false, false, false, false, false),

('starter', 'Starter', 'Parfait pour les petites entreprises qui démarrent', 15000.00, 30,
 '["Toutes les fonctionnalités de l''essai", "Jusqu''à 500 avis analysés/mois", "Recommandations IA illimitées", "Export des rapports PDF", "Support prioritaire", "Intégration API basique"]'::jsonb,
 false, 500, true, false, true, true, false, false),

('professional', 'Professional', 'Pour les entreprises en croissance', 35000.00, 30,
 '["Toutes les fonctionnalités Starter", "Avis illimités", "Analyses prédictives avancées", "Tableaux de bord personnalisés", "Support téléphonique 24/7", "Intégration API complète", "Formation personnalisée", "Gestionnaire de compte dédié"]'::jsonb,
 false, null, true, true, true, true, true, true),

('enterprise', 'Enterprise', 'Solution complète pour les grandes entreprises', 75000.00, 30,
 '["Toutes les fonctionnalités Professional", "Multi-comptes et équipes", "Analyses personnalisées", "SLA garanti 99.9%", "Support dédié premium", "Intégrations sur mesure", "Formations d''équipe", "Rapports exécutifs mensuels"]'::jsonb,
 false, null, true, true, true, true, true, true);

-- =====================================================
-- COMMENTAIRES ET DOCUMENTATION
-- =====================================================

COMMENT ON TABLE subscription_plans IS 'Plans d''abonnement disponibles avec leurs fonctionnalités et tarifs';
COMMENT ON TABLE business_subscriptions IS 'Abonnements actifs et historiques des entreprises';
COMMENT ON TABLE payment_methods IS 'Méthodes de paiement enregistrées par les entreprises';
COMMENT ON TABLE payment_intents IS 'Intentions de paiement avec leur statut de traitement';
COMMENT ON TABLE payment_receipts IS 'Reçus de paiement générés après transactions réussies';
COMMENT ON TABLE payment_events IS 'Journal d''audit de tous les événements de paiement';
COMMENT ON TABLE payment_webhooks IS 'Webhooks reçus des processeurs de paiement';
COMMENT ON TABLE subscription_coupons IS 'Coupons de réduction pour les abonnements';
COMMENT ON TABLE coupon_usages IS 'Historique d''utilisation des coupons';

-- =====================================================
-- FIN DE LA MIGRATION
-- =====================================================
