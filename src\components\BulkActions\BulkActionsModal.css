/* Bulk Actions Modal - Styles Ultra-Avancés */

/* Modal Base */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.modal-container.large {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-title h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.selected-count {
  padding: 4px 12px;
  background: #3b82f6;
  color: white;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.modal-close:hover {
  background: #e5e7eb;
  color: #374151;
  transform: scale(1.05);
}

/* Modal Content */
.modal-content {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

/* Action Selector */
.action-selector {
  margin-bottom: 32px;
}

.action-selector h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.action-card:hover {
  border-color: #3b82f6;
  background: #f8fafc;
  color: #3b82f6;
  transform: translateY(-2px);
}

.action-card.active {
  border-color: #3b82f6;
  background: #dbeafe;
  color: #1e40af;
}

.action-card.danger {
  border-color: #fecaca;
  color: #dc2626;
}

.action-card.danger:hover {
  border-color: #ef4444;
  background: #fef2f2;
  color: #dc2626;
}

.action-card.danger.active {
  border-color: #ef4444;
  background: #fee2e2;
  color: #991b1b;
}

.action-card span {
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
}

/* Action Configuration */
.action-config {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.action-config h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.config-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Form Elements */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input.error,
.form-group select.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group small {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}

.error-text {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 4px;
}

/* Status Options */
.status-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.status-option {
  padding: 8px 16px;
  border: 2px solid;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-option.green {
  border-color: #d1fae5;
  color: #065f46;
}

.status-option.green:hover,
.status-option.green.active {
  border-color: #10b981;
  background: #d1fae5;
}

.status-option.gray {
  border-color: #f3f4f6;
  color: #6b7280;
}

.status-option.gray:hover,
.status-option.gray.active {
  border-color: #9ca3af;
  background: #f3f4f6;
}

.status-option.red {
  border-color: #fecaca;
  color: #991b1b;
}

.status-option.red:hover,
.status-option.red.active {
  border-color: #ef4444;
  background: #fee2e2;
}

/* Warning Box */
.warning-box {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 8px;
  color: #92400e;
}

.warning-box strong {
  color: #78350f;
}

.warning-box p {
  margin: 4px 0 0 0;
  line-height: 1.4;
}

/* Preview Section */
.preview-section {
  margin-bottom: 24px;
}

.preview-section h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.preview-box {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #dbeafe;
  border: 1px solid #93c5fd;
  border-radius: 8px;
  color: #1e40af;
  font-weight: 500;
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #991b1b;
  font-size: 0.875rem;
  margin-bottom: 20px;
}

/* Modal Actions */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
  margin-top: 24px;
}

.btn-primary,
.btn-secondary,
.btn-danger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 0.875rem;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
}

.btn-danger:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

/* Animations */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-container.large {
    max-height: 95vh;
  }
  
  .modal-header {
    padding: 16px;
  }
  
  .modal-content {
    padding: 16px;
  }
  
  .action-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
  }
  
  .action-card {
    padding: 16px 12px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .modal-actions {
    flex-direction: column-reverse;
  }
  
  .btn-primary,
  .btn-secondary,
  .btn-danger {
    width: 100%;
    justify-content: center;
  }
  
  .status-options {
    flex-direction: column;
  }
  
  .status-option {
    text-align: center;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .modal-container.large {
    background: #1f2937;
  }
  
  .modal-header {
    background: #374151;
    border-color: #4b5563;
  }
  
  .modal-title h2 {
    color: #f9fafb;
  }
  
  .modal-close {
    background: #4b5563;
    color: #d1d5db;
  }
  
  .modal-close:hover {
    background: #6b7280;
    color: #f9fafb;
  }
  
  .action-config {
    background: #374151;
    border-color: #4b5563;
  }
  
  .action-config h3,
  .action-selector h3,
  .preview-section h3 {
    color: #f9fafb;
  }
  
  .form-group label {
    color: #d1d5db;
  }
  
  .form-group input,
  .form-group select {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .action-card {
    background: #374151;
    border-color: #4b5563;
    color: #d1d5db;
  }
  
  .action-card:hover {
    background: #4b5563;
    border-color: #3b82f6;
    color: #60a5fa;
  }
}

/* Accessibility */
.modal-container:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.btn-primary:focus,
.btn-secondary:focus,
.btn-danger:focus,
.modal-close:focus,
.action-card:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .modal-overlay,
  .modal-container,
  .action-card,
  .btn-primary,
  .btn-secondary,
  .btn-danger,
  .spinning {
    animation: none;
    transition: none;
  }
  
  .action-card:hover,
  .btn-primary:hover,
  .btn-danger:hover,
  .modal-close:hover {
    transform: none;
  }
}
