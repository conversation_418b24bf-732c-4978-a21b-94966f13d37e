-- Migration pour ajouter la colonne likes à la table posts
-- Cette migration ajoute la fonctionnalité de likes aux posts

-- =====================================================
-- 1. AJOUTER LA COLONNE LIKES À LA TABLE POSTS
-- =====================================================

-- Ajout de la colonne likes de type uuid[] avec une valeur par défaut ARRAY[]
ALTER TABLE posts
ADD COLUMN IF NOT EXISTS likes UUID[] DEFAULT ARRAY[]::UUID[];

-- Commentaire explicatif sur l'utilisation de cette colonne
COMMENT ON COLUMN posts.likes IS 'Liste des UUIDs des utilisateurs qui ont aimé ce post';

-- =====================================================
-- 2. AJOUTER LA COLONNE SHARES À LA TABLE POSTS
-- =====================================================

-- Ajout de la colonne shares pour tracker les partages
ALTER TABLE posts
ADD COLUMN IF NOT EXISTS shares UUID[] DEFAULT ARRAY[]::UUID[];

-- Commentaire explicatif sur l'utilisation de cette colonne
COMMENT ON COLUMN posts.shares IS 'Liste des UUIDs des utilisateurs qui ont partagé ce post';

-- =====================================================
-- 3. CRÉER DES INDEX POUR OPTIMISER LES PERFORMANCES
-- =====================================================

-- Index pour améliorer les performances des requêtes sur les likes
CREATE INDEX IF NOT EXISTS posts_likes_idx ON posts USING GIN (likes);

-- Index pour améliorer les performances des requêtes sur les recommendations
CREATE INDEX IF NOT EXISTS posts_recommendations_idx ON posts USING GIN (recommendations);

-- Index pour améliorer les performances des requêtes sur les shares
CREATE INDEX IF NOT EXISTS posts_shares_idx ON posts USING GIN (shares);

-- =====================================================
-- 4. METTRE À JOUR LA VUE posts_with_author_details
-- =====================================================

-- Recréer la vue pour inclure les nouvelles colonnes
CREATE OR REPLACE VIEW posts_with_author_details AS
SELECT
  p.*,
  u.username as author_username,
  u.profile_picture as author_profile_picture,
  u.status as author_status,
  u.role as author_role,
  COALESCE(
    (
      SELECT json_agg(
        json_build_object(
          'id', c.id,
          'userId', c.user_id,
          'username', cu.username,
          'profilePicture', cu.profile_picture,
          'content', c.content,
          'hasUsedProduct', c.has_used_product,
          'rating', c.rating,
          'createdAt', c.created_at,
          'authorRole', cu.role,
          'businessName', CASE WHEN cu.role = 'business' THEN cu.username ELSE NULL END
        ) ORDER BY c.created_at ASC
      )
      FROM comments c
      LEFT JOIN profiles cu ON c.user_id = cu.id
      WHERE c.post_id = p.id
    ),
    '[]'::json
  ) as comments
FROM posts p
LEFT JOIN profiles u ON p.user_id = u.id;

-- Accorder l'accès à la vue
GRANT SELECT ON posts_with_author_details TO authenticated;

-- =====================================================
-- 5. FONCTIONS UTILITAIRES POUR LES LIKES
-- =====================================================

-- Fonction pour ajouter un like à un post
CREATE OR REPLACE FUNCTION add_like_to_post(post_id UUID, user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  current_likes UUID[];
  updated_likes UUID[];
BEGIN
  -- Récupérer les likes actuels
  SELECT likes INTO current_likes FROM posts WHERE id = post_id;
  
  -- Vérifier si l'utilisateur a déjà liké
  IF user_id = ANY(current_likes) THEN
    RETURN FALSE; -- Déjà liké
  END IF;
  
  -- Ajouter le like
  updated_likes := array_append(current_likes, user_id);
  
  -- Mettre à jour le post
  UPDATE posts SET likes = updated_likes WHERE id = post_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour retirer un like d'un post
CREATE OR REPLACE FUNCTION remove_like_from_post(post_id UUID, user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  current_likes UUID[];
  updated_likes UUID[];
BEGIN
  -- Récupérer les likes actuels
  SELECT likes INTO current_likes FROM posts WHERE id = post_id;
  
  -- Vérifier si l'utilisateur a liké
  IF NOT (user_id = ANY(current_likes)) THEN
    RETURN FALSE; -- Pas encore liké
  END IF;
  
  -- Retirer le like
  updated_likes := array_remove(current_likes, user_id);
  
  -- Mettre à jour le post
  UPDATE posts SET likes = updated_likes WHERE id = post_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. POLITIQUES RLS POUR LES LIKES
-- =====================================================

-- Les politiques existantes pour UPDATE couvrent déjà les likes
-- car elles permettent aux utilisateurs authentifiés de modifier les posts
-- Nous pourrions ajouter des politiques plus spécifiques si nécessaire

-- =====================================================
-- 7. INITIALISER LES DONNÉES EXISTANTES
-- =====================================================

-- Initialiser la colonne likes pour tous les posts existants (si elle est NULL)
UPDATE posts 
SET likes = ARRAY[]::UUID[] 
WHERE likes IS NULL;

-- Initialiser la colonne shares pour tous les posts existants (si elle est NULL)
UPDATE posts 
SET shares = ARRAY[]::UUID[] 
WHERE shares IS NULL;

-- =====================================================
-- 8. COMMENTAIRES POUR DOCUMENTATION
-- =====================================================

COMMENT ON FUNCTION add_like_to_post(UUID, UUID) IS 'Ajoute un like d''un utilisateur à un post';
COMMENT ON FUNCTION remove_like_from_post(UUID, UUID) IS 'Retire un like d''un utilisateur d''un post';
