import React, { useState, useEffect } from 'react';
import {
  Crown,
  Shield,
  CheckCircle,
  Clock,
  AlertTriangle,
  ArrowLeft,
  CreditCard,
  Building2
} from 'lucide-react';
import Card, { CardBody } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import SubscriptionCodeGenerator from '../../components/business/SubscriptionCodeGenerator';

interface BusinessSubscription {
  id: string;
  planType: 'trial' | 'monthly' | 'yearly';
  status: 'active' | 'expired' | 'cancelled' | 'pending';
  startDate: string;
  endDate: string;
  amount: number;
  autoRenewal: boolean;
}

interface PendingCode {
  id: string;
  code: string;
  planType: 'trial' | 'monthly' | 'yearly';
  amount: number;
  generatedAt: string;
  expiresAt: string;
  status: 'pending' | 'validated' | 'rejected' | 'expired';
}

const SubscriptionPage: React.FC = () => {
  const [currentSubscription, setCurrentSubscription] = useState<BusinessSubscription | null>(null);
  const [pendingCodes, setPendingCodes] = useState<PendingCode[]>([]);
  const [showCodeGenerator, setShowCodeGenerator] = useState(false);
  const [loading, setLoading] = useState(true);

  // Données mockées pour la démonstration
  const mockBusinessData = {
    id: 'business_123',
    name: 'Dexima Cosmétiques',
    email: '<EMAIL>'
  };

  useEffect(() => {
    loadSubscriptionData();
  }, []);

  const loadSubscriptionData = async () => {
    setLoading(true);
    try {
      // Simuler le chargement des données
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Données mockées
      setCurrentSubscription({
        id: 'sub_001',
        planType: 'monthly',
        status: 'active',
        startDate: '2024-11-01T00:00:00Z',
        endDate: '2024-12-01T00:00:00Z',
        amount: 25000,
        autoRenewal: true
      });

      setPendingCodes([
        {
          id: 'code_001',
          code: '02500001',
          planType: 'monthly',
          amount: 25000,
          generatedAt: '2024-11-28T10:30:00Z',
          expiresAt: '2024-11-29T10:30:00Z',
          status: 'pending'
        }
      ]);

    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCodeGenerated = (code: string) => {
    console.log('Nouveau code généré:', code);
    // Recharger les données pour afficher le nouveau code
    loadSubscriptionData();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'expired':
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle size={16} />;
      case 'pending':
        return <Clock size={16} />;
      case 'expired':
      case 'cancelled':
        return <AlertTriangle size={16} />;
      default:
        return <AlertTriangle size={16} />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Actif';
      case 'pending':
        return 'En attente';
      case 'expired':
        return 'Expiré';
      case 'cancelled':
        return 'Annulé';
      default:
        return status;
    }
  };

  const getPlanLabel = (planType: string) => {
    switch (planType) {
      case 'trial':
        return 'Essai gratuit';
      case 'monthly':
        return 'Plan mensuel';
      case 'yearly':
        return 'Plan annuel';
      default:
        return planType;
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XAF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount).replace('XAF', 'F CFA');
  };

  const getDaysRemaining = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const getTimeUntilExpiration = (expiresAt: string) => {
    const expiration = new Date(expiresAt);
    const now = new Date();
    const diffMs = expiration.getTime() - now.getTime();
    
    if (diffMs <= 0) return 'Expiré';
    
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    return diffHours > 0 ? `${diffHours}h ${diffMinutes}m` : `${diffMinutes}m`;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (showCodeGenerator) {
    return (
      <div className="space-y-6">
        {/* En-tête avec retour */}
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            leftIcon={<ArrowLeft size={16} />}
            onClick={() => setShowCodeGenerator(false)}
          >
            Retour
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Souscrire à un abonnement</h1>
            <p className="text-gray-600">Générez un code de validation sécurisé</p>
          </div>
        </div>

        {/* Générateur de code */}
        <SubscriptionCodeGenerator
          businessId={mockBusinessData.id}
          businessName={mockBusinessData.name}
          onCodeGenerated={handleCodeGenerated}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mon Abonnement</h1>
          <p className="text-gray-600 mt-2">Gérez votre abonnement et vos codes de validation</p>
        </div>
        <div className="flex items-center space-x-3">
          <Building2 size={20} className="text-gray-400" />
          <span className="font-medium text-gray-900">{mockBusinessData.name}</span>
        </div>
      </div>

      {/* Abonnement actuel */}
      {currentSubscription && (
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <Crown className="text-yellow-500" size={24} />
                <h2 className="text-xl font-semibold text-gray-900">Abonnement Actuel</h2>
              </div>
              <span className={`inline-flex items-center px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(currentSubscription.status)}`}>
                {getStatusIcon(currentSubscription.status)}
                <span className="ml-1">{getStatusLabel(currentSubscription.status)}</span>
              </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <div className="text-sm text-gray-600">Plan</div>
                <div className="font-semibold text-gray-900">{getPlanLabel(currentSubscription.planType)}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Montant</div>
                <div className="font-semibold text-gray-900">{formatAmount(currentSubscription.amount)}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Expire le</div>
                <div className="font-semibold text-gray-900">
                  {new Date(currentSubscription.endDate).toLocaleDateString('fr-FR')}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Jours restants</div>
                <div className="font-semibold text-gray-900">
                  {getDaysRemaining(currentSubscription.endDate)} jours
                </div>
              </div>
            </div>

            {currentSubscription.status === 'active' && (
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="text-green-500" size={16} />
                  <span className="text-green-800 font-medium">
                    Votre abonnement est actif et toutes les fonctionnalités sont disponibles.
                  </span>
                </div>
              </div>
            )}
          </CardBody>
        </Card>
      )}

      {/* Codes en attente */}
      {pendingCodes.length > 0 && (
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Shield className="text-blue-500" size={24} />
              <h2 className="text-xl font-semibold text-gray-900">Codes en Attente de Validation</h2>
            </div>

            <div className="space-y-4">
              {pendingCodes.map((code) => (
                <div key={code.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div>
                        <div className="text-sm text-gray-600">Code de validation</div>
                        <div className="text-xl font-mono font-bold text-blue-600">
                          {code.code.replace(/(\d{4})(\d{4})/, '$1-$2')}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Plan</div>
                        <div className="font-medium text-gray-900">{getPlanLabel(code.planType)}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Montant</div>
                        <div className="font-medium text-gray-900">{formatAmount(code.amount)}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-600">Expire dans</div>
                      <div className="font-medium text-orange-600">
                        {getTimeUntilExpiration(code.expiresAt)}
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Clock className="text-yellow-500" size={16} />
                      <span className="text-yellow-800 text-sm">
                        Ce code est en attente de validation par un administrateur. 
                        Vous recevrez une notification une fois le code traité.
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardBody className="p-6">
            <div className="text-center">
              <CreditCard className="mx-auto text-blue-500 mb-4" size={48} />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Souscrire à un Abonnement
              </h3>
              <p className="text-gray-600 mb-4">
                Choisissez un plan et générez un code de validation sécurisé
              </p>
              <Button
                onClick={() => setShowCodeGenerator(true)}
                leftIcon={<Shield size={16} />}
                className="w-full"
              >
                Choisir un Plan
              </Button>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <div className="text-center">
              <Clock className="mx-auto text-gray-500 mb-4" size={48} />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Historique des Codes
              </h3>
              <p className="text-gray-600 mb-4">
                Consultez l'historique de vos codes de validation
              </p>
              <Button
                variant="outline"
                leftIcon={<Shield size={16} />}
                className="w-full"
              >
                Voir l'Historique
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Informations importantes */}
      <Card>
        <CardBody className="p-6">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="text-blue-500 mt-0.5" size={20} />
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Informations Importantes</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Les codes de validation expirent après 24 heures</li>
                <li>• Chaque code doit être validé par un administrateur</li>
                <li>• Vous recevrez une notification par email lors de la validation</li>
                <li>• L'abonnement est activé immédiatement après validation</li>
                <li>• Conservez vos codes en lieu sûr jusqu'à validation</li>
              </ul>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default SubscriptionPage;
