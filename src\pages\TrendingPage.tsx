import React, { useMemo } from 'react';
import { usePosts } from '../context/PostsContext';
import PostCard from '../components/posts/PostCard';
import { TrendingUp } from 'lucide-react';

const TrendingPage: React.FC = () => {
  const { posts, loading } = usePosts();

  const trendingPosts = useMemo(() => {
    if (loading || posts.length === 0) {
      return [];
    }
    // Sort by number of likes in descending order
    // For a more advanced trending algorithm, consider recency and other engagement metrics
    return [...posts].sort((a, b) => (b.likes?.length || 0) - (a.likes?.length || 0));
  }, [posts, loading]);

  if (loading) {
    return <div className="text-center py-10">Chargement des tendances...</div>;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8 flex items-center">
        <TrendingUp size={32} className="mr-3 text-red-500" />
        <h1 className="text-3xl font-bold text-gray-900">Tendances Actuelles</h1>
      </div>
      <p className="mt-2 mb-6 text-gray-600">Découvrez les posts les plus populaires et les plus aimés par la communauté.</p>

      {trendingPosts.length === 0 && !loading && (
         <div className="text-center py-10 bg-white p-6 rounded-lg shadow">
          <TrendingUp size={48} className="mx-auto text-gray-400 mb-4" />
          <p className="text-xl text-gray-700 font-semibold">Aucune tendance à afficher pour le moment.</p>
          <p className="text-gray-500 mt-2">Interagissez avec les posts pour influencer les tendances !</p>
        </div>
      )}

      {trendingPosts.length > 0 && (
        <div className="space-y-6">
          {trendingPosts.map(post => (
            <PostCard key={post.id} post={post} />
          ))}
        </div>
      )}
    </div>
  );
};

export default TrendingPage;
