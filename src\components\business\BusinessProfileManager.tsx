import React, { useState } from 'react';
import { Building, MapPin, Phone, Mail, Globe, Calendar, Users, Award, Edit, Save, X } from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import { IBusinessUser } from '../../types';
import { BusinessProfileService } from '../../services/businessProfileService';

interface BusinessProfileManagerProps {
  businessUser: IBusinessUser;
  onUpdate: (data: Partial<IBusinessUser>) => void;
}

const BusinessProfileManager: React.FC<BusinessProfileManagerProps> = ({ businessUser, onUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    businessName: businessUser.businessName || '',
    businessDescription: businessUser.businessDescription || '',
    businessAddress: businessUser.businessAddress || '',
    businessPhone: businessUser.businessPhone || '',
    businessEmail: businessUser.businessEmail || '',
    businessWebsite: businessUser.businessWebsite || '',
    businessCategory: businessUser.businessCategory || '',
    businessLicense: businessUser.businessLicense || '',
    foundedYear: businessUser.foundedYear || new Date().getFullYear(),
    employeeCount: businessUser.employeeCount || 1,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'foundedYear' || name === 'employeeCount' ? parseInt(value) || 0 : value
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await BusinessProfileService.updateBusinessProfile(businessUser.id, formData);

      if (result.success) {
        onUpdate(formData);
        setIsEditing(false);
      } else {
        setError(result.error || 'Erreur lors de la mise à jour');
      }
    } catch (err) {
      setError('Erreur lors de la mise à jour du profil');
      console.error('Erreur:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      businessName: businessUser.businessName || '',
      businessDescription: businessUser.businessDescription || '',
      businessAddress: businessUser.businessAddress || '',
      businessPhone: businessUser.businessPhone || '',
      businessEmail: businessUser.businessEmail || '',
      businessWebsite: businessUser.businessWebsite || '',
      businessCategory: businessUser.businessCategory || '',
      businessLicense: businessUser.businessLicense || '',
      foundedYear: businessUser.foundedYear || new Date().getFullYear(),
      employeeCount: businessUser.employeeCount || 1,
    });
    setIsEditing(false);
  };

  const businessCategories = [
    'Beauté et Cosmétiques',
    'Mode et Vêtements',
    'Alimentation et Boissons',
    'Électronique et High-Tech',
    'Maison et Décoration',
    'Santé et Bien-être',
    'Sports et Loisirs',
    'Automobile',
    'Services',
    'Autre'
  ];

  const employeeRanges = [
    { value: 1, label: '1 employé' },
    { value: 5, label: '2-5 employés' },
    { value: 10, label: '6-10 employés' },
    { value: 25, label: '11-25 employés' },
    { value: 50, label: '26-50 employés' },
    { value: 100, label: '51-100 employés' },
    { value: 500, label: '100+ employés' },
  ];

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Mon entreprise</h2>
          <p className="text-gray-600">Gérez les informations de votre entreprise</p>
        </div>
        {!isEditing ? (
          <Button
            variant="primary"
            leftIcon={<Edit size={16} />}
            onClick={() => setIsEditing(true)}
          >
            Modifier
          </Button>
        ) : (
          <div className="flex space-x-2">
            <Button
              variant="primary"
              leftIcon={<Save size={16} />}
              onClick={handleSave}
              disabled={isLoading}
            >
              {isLoading ? 'Enregistrement...' : 'Enregistrer'}
            </Button>
            <Button
              variant="outline"
              leftIcon={<X size={16} />}
              onClick={handleCancel}
              disabled={isLoading}
            >
              Annuler
            </Button>
          </div>
        )}
      </div>

      {/* Affichage des erreurs */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <X className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Erreur</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Informations générales */}
      <Card>
        <CardBody>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Building size={20} className="mr-2" />
            Informations générales
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nom de l'entreprise
              </label>
              {isEditing ? (
                <input
                  type="text"
                  name="businessName"
                  value={formData.businessName}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Nom de votre entreprise"
                />
              ) : (
                <p className="text-gray-900">{businessUser.businessName || 'Non renseigné'}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Catégorie d'activité
              </label>
              {isEditing ? (
                <select
                  name="businessCategory"
                  value={formData.businessCategory}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Sélectionnez une catégorie</option>
                  {businessCategories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              ) : (
                <p className="text-gray-900">{businessUser.businessCategory || 'Non renseigné'}</p>
              )}
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description de l'entreprise
              </label>
              {isEditing ? (
                <textarea
                  name="businessDescription"
                  value={formData.businessDescription}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Décrivez votre entreprise et ses activités"
                />
              ) : (
                <p className="text-gray-900">{businessUser.businessDescription || 'Non renseigné'}</p>
              )}
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Coordonnées */}
      <Card>
        <CardBody>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <MapPin size={20} className="mr-2" />
            Coordonnées
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Adresse
              </label>
              {isEditing ? (
                <input
                  type="text"
                  name="businessAddress"
                  value={formData.businessAddress}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Adresse complète de votre entreprise"
                />
              ) : (
                <p className="text-gray-900 flex items-center">
                  <MapPin size={16} className="mr-2" />
                  {businessUser.businessAddress || 'Non renseigné'}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Téléphone
              </label>
              {isEditing ? (
                <input
                  type="tel"
                  name="businessPhone"
                  value={formData.businessPhone}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="+225 XX XX XX XX XX"
                />
              ) : (
                <p className="text-gray-900 flex items-center">
                  <Phone size={16} className="mr-2" />
                  {businessUser.businessPhone || 'Non renseigné'}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email professionnel
              </label>
              {isEditing ? (
                <input
                  type="email"
                  name="businessEmail"
                  value={formData.businessEmail}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              ) : (
                <p className="text-gray-900 flex items-center">
                  <Mail size={16} className="mr-2" />
                  {businessUser.businessEmail || 'Non renseigné'}
                </p>
              )}
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Site web
              </label>
              {isEditing ? (
                <input
                  type="url"
                  name="businessWebsite"
                  value={formData.businessWebsite}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="https://www.entreprise.com"
                />
              ) : (
                <p className="text-gray-900 flex items-center">
                  <Globe size={16} className="mr-2" />
                  {businessUser.businessWebsite ? (
                    <a
                      href={businessUser.businessWebsite}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {businessUser.businessWebsite}
                    </a>
                  ) : (
                    'Non renseigné'
                  )}
                </p>
              )}
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Informations légales et administratives */}
      <Card>
        <CardBody>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Award size={20} className="mr-2" />
            Informations légales
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Numéro de licence/RCCM
              </label>
              {isEditing ? (
                <input
                  type="text"
                  name="businessLicense"
                  value={formData.businessLicense}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Numéro d'immatriculation"
                />
              ) : (
                <p className="text-gray-900">{businessUser.businessLicense || 'Non renseigné'}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Année de création
              </label>
              {isEditing ? (
                <input
                  type="number"
                  name="foundedYear"
                  value={formData.foundedYear}
                  onChange={handleInputChange}
                  min="1900"
                  max={new Date().getFullYear()}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              ) : (
                <p className="text-gray-900 flex items-center">
                  <Calendar size={16} className="mr-2" />
                  {businessUser.foundedYear || 'Non renseigné'}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nombre d'employés
              </label>
              {isEditing ? (
                <select
                  name="employeeCount"
                  value={formData.employeeCount}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {employeeRanges.map((range) => (
                    <option key={range.value} value={range.value}>
                      {range.label}
                    </option>
                  ))}
                </select>
              ) : (
                <p className="text-gray-900 flex items-center">
                  <Users size={16} className="mr-2" />
                  {employeeRanges.find(r => r.value === businessUser.employeeCount)?.label || 'Non renseigné'}
                </p>
              )}
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Statistiques de l'entreprise */}
      <Card>
        <CardBody>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Statistiques</h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{businessUser.salesCount || 0}</div>
              <div className="text-sm text-gray-500">Ventes réalisées</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{businessUser.catalog?.length || 0}</div>
              <div className="text-sm text-gray-500">Produits au catalogue</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {new Date().getFullYear() - (businessUser.foundedYear || new Date().getFullYear())} ans
              </div>
              <div className="text-sm text-gray-500">D'expérience</div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default BusinessProfileManager;
