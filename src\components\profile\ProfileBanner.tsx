import React, { useState } from 'react';
import { MapPin, Calendar, Globe, Edit } from 'lucide-react';
import Avatar from '../ui/Avatar';
import Button from '../ui/Button';
import UserStatusBadge from '../ui/UserStatusBadge';
import BusinessStatusBadge from '../ui/BusinessStatusBadge';
import VerificationBadge from '../ui/VerificationBadge';
import { IUser, UserRole, IBusinessUser, BusinessStatus } from '../../types';
import { DEFAULT_IMAGES } from '../../constants/defaultImages';
import EditProfileModal from './EditProfileModal';

const getAvatarSrc = (user: IUser) => {
  // Vérifier d'abord si l'utilisateur a une photo de profil
  if (user.profilePicture && user.profilePicture.trim() !== '') {
    return user.profilePicture;
  }

  // Fallback selon le type d'utilisateur
  if (user.role === UserRole.BUSINESS && 'businessName' in user) {
    return DEFAULT_IMAGES.BUSINESS;
  }
  return DEFAULT_IMAGES.AVATAR;
};

const getCoverSrc = (user: IUser) => {
  // Vérifier si l'utilisateur a une photo de couverture
  if (user.coverPhotoUrl && user.coverPhotoUrl.trim() !== '') {
    return user.coverPhotoUrl;
  }

  // Fallback vers l'image par défaut
  return DEFAULT_IMAGES.COVER;
};

interface ProfileBannerProps {
  user: IUser;
  isFollowing: boolean;
  onFollow: () => void;
  isLoading: boolean;
  isOwnProfile: boolean;
  onProfileUpdate: (data: any) => Promise<void>;
}

const ProfileBanner: React.FC<ProfileBannerProps> = ({
  user,
  isFollowing,
  onFollow,
  isLoading,
  isOwnProfile,
  onProfileUpdate
}) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [coverImageError, setCoverImageError] = useState(false);
  const [avatarImageError, setAvatarImageError] = useState(false);

  // Gestionnaire d'erreur pour l'image de couverture
  const handleCoverError = () => {
    setCoverImageError(true);
  };

  // Gestionnaire d'erreur pour l'avatar
  const handleAvatarError = () => {
    setAvatarImageError(true);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      {/* Cover Photo */}
      <div className="relative h-32">
        {!coverImageError && user.coverPhotoUrl ? (
          <img
            src={getCoverSrc(user)}
            alt="Couverture"
            className="w-full h-full object-cover"
            onError={handleCoverError}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 flex items-center justify-center">
            <div className="text-center text-white">
              <p className="text-sm opacity-90">
                {user.coverPhotoUrl ? 'Erreur de chargement' : 'Aucune photo de couverture'}
              </p>
            </div>
          </div>
        )}
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 to-black/60" />
      </div>

      {/* Profile Info */}
      <div className="relative px-8 pb-6">
        {/* Avatar and Username */}
        <div className="flex items-end space-x-4 -mt-16">
          <div className="relative">
            {!avatarImageError && user.profilePicture ? (
              <Avatar
                src={getAvatarSrc(user)}
                alt={user.role === UserRole.BUSINESS && 'businessName' in user ? (user as IBusinessUser).businessName : user.username}
                size="custom"
                customSize="w-32 h-32"
                className="border-4 border-white shadow-lg"
              />
            ) : (
              <div className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 border-4 border-white shadow-lg flex items-center justify-center">
                <span className="text-white text-2xl font-bold">
                  {user.username.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
            <span className="absolute bottom-2 right-2 bg-blue-600 text-white text-sm px-3 py-1 rounded-full">
              {user.role === UserRole.BUSINESS ? 'Entreprise' : 'Membre'}
            </span>
          </div>
          <div className="flex-1 pb-4">
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-4xl font-bold text-white drop-shadow-lg">
                {user.role === UserRole.BUSINESS && typeof (user as any).businessName === 'string' && (user as any).businessName.trim() !== ''
                  ? (user as any).businessName
                  : user.username}
              </h1>
              <VerificationBadge
                isVerified={(user as any).is_verified || false}
                userRole={user.role}
                businessVerified={user.role === UserRole.BUSINESS ? (user as any).business_verified || false : false}
                size="lg"
                variant="default"
                showTooltip={true}
              />
            </div>
            <div className="flex items-center space-x-3">
              {/* Badge de statut - Entreprise (priorité) */}
              {user.role === UserRole.BUSINESS ? (
                'businessStatus' in user && (user as IBusinessUser).businessStatus ? (
                  <BusinessStatusBadge
                    status={(user as IBusinessUser).businessStatus!}
                    size="lg"
                    variant="gradient"
                    className="shadow-md"
                  />
                ) : (
                  /* Badge par défaut pour les entreprises sans businessStatus */
                  <BusinessStatusBadge
                    status={BusinessStatus.NEW}
                    size="lg"
                    variant="gradient"
                    className="shadow-md"
                  />
                )
              ) : user.status ? (
                /* Badge de statut - Utilisateur normal */
                <UserStatusBadge
                  status={user.status}
                  size="lg"
                  variant="gradient"
                  className="shadow-md"
                />
              ) : null}
              {user.role === UserRole.BUSINESS && (
                <span className="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                  Entreprise
                </span>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {isOwnProfile ? (
              <Button
                onClick={() => setIsEditModalOpen(true)}
                variant="outline"
                size="lg"
                className="mb-4"
                leftIcon={<Edit size={18} />}
              >
                Éditer le profil
              </Button>
            ) : (
              <Button
                onClick={onFollow}
                disabled={isLoading}
                variant="primary"
                size="lg"
                className="mb-4"
              >
                {isLoading ? 'Chargement...' : (isFollowing ? 'Abonné' : 'Suivre')}
              </Button>
            )}
          </div>
        </div>

        {/* User Details */}
        <div className="mt-4 space-y-3">
          {/* Première ligne : Localisation et site web */}
          <div className="flex items-center space-x-6 text-white/90">
            {user.city && user.country && (
              <div className="flex items-center">
                <MapPin className="w-5 h-5 mr-2" />
                <span>{user.city}, {user.country}</span>
              </div>
            )}
            <div className="flex items-center">
              <Globe className="w-5 h-5 mr-2" />
              <a
                href={`https://customeroom.com/@${user.role === UserRole.BUSINESS && 'businessName' in user ? (user as IBusinessUser).businessName : user.username}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-200 hover:text-white hover:underline"
              >
                customeroom.com/@{user.role === UserRole.BUSINESS && 'businessName' in user ? (user as IBusinessUser).businessName : user.username}
              </a>
            </div>
            <div className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              <span>
                Membre depuis {new Date(user.createdAt).toLocaleDateString('fr-FR', {
                  month: 'long',
                  year: 'numeric'
                })}
              </span>
            </div>
          </div>

          {/* Deuxième ligne : Statistiques de followers/following */}
          <div className="flex items-center space-x-6 text-sm">
            <div className="flex items-center space-x-4">
              <span className="text-white/80">
                <span className="font-semibold text-white">{(user as any).followers_count || 0}</span> abonné{((user as any).followers_count || 0) > 1 ? 's' : ''}
              </span>
              <span className="text-white/80">
                <span className="font-semibold text-white">{(user as any).following_count || 0}</span> abonnement{((user as any).following_count || 0) > 1 ? 's' : ''}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Modal d'édition du profil */}
      <EditProfileModal
        user={user}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSave={onProfileUpdate}
      />
    </div>
  );
};

export default ProfileBanner;
