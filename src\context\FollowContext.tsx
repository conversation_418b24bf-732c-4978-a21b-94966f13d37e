import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { ISubscription, ISubscriptionPlan } from '../types';
import { useAuth } from './AuthContext';

interface FollowContextType {
  // Fonctions existantes
  followUser: (userId: string) => Promise<void>;
  unfollowUser: (userId: string) => Promise<void>;
  isFollowing: (userId: string) => Promise<boolean>;
  getFollowersDetails: (userId: string) => Promise<any[]>;
  getFollowingDetails: (userId: string) => Promise<any[]>;

  // Nouvelles fonctions pour le système d'abonnement
  subscribeToUser: (userId: string, level: 'basic' | 'premium' | 'vip') => Promise<void>;
  subscribeToBusinesss: (businessId: string, level: 'basic' | 'premium' | 'vip') => Promise<void>;
  cancelSubscription: (subscriptionId: string) => Promise<void>;
  pauseSubscription: (subscriptionId: string) => Promise<void>;
  resumeSubscription: (subscriptionId: string) => Promise<void>;
  getActiveSubscriptions: () => Promise<ISubscription[]>;
  getSubscriptionPlans: () => Promise<ISubscriptionPlan[]>;
  getUserSubscribers: (userId: string) => Promise<any[]>;
  getSubscriptionDetails: (subscriptionId: string) => Promise<ISubscription | null>;
  toggleNotifications: (subscriptionId: string, enabled: boolean) => Promise<void>;
  toggleAutoRenewal: (subscriptionId: string, enabled: boolean) => Promise<void>;
}

const FollowContext = createContext<FollowContextType | undefined>(undefined);

export const FollowProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { currentUser } = useAuth();
  const [subscriptionPlans, setSubscriptionPlans] = useState<ISubscriptionPlan[]>([]);
  const [following, setFollowing] = useState<string[]>([]);

  // Charger les plans d'abonnement au démarrage
  useEffect(() => {
    const loadSubscriptionPlans = async () => {
      // Dans une vraie application, nous récupérerions les plans depuis la base de données
      // Pour cette simulation, nous créons des plans fictifs
      const mockPlans: ISubscriptionPlan[] = [
        {
          id: 'plan-basic-user',
          name: 'Abonnement Basic',
          level: 'basic',
          price: 0,
          duration: 0, // illimité
          features: ['Accès aux publications', 'Notifications de base'],
          targetType: 'user',
          isActive: true
        },
        {
          id: 'plan-premium-user',
          name: 'Abonnement Premium',
          level: 'premium',
          price: 1500,
          duration: 30,
          features: ['Accès aux publications exclusives', 'Notifications prioritaires', 'Badge supporter'],
          targetType: 'user',
          isActive: true
        },
        {
          id: 'plan-vip-user',
          name: 'Abonnement VIP',
          level: 'vip',
          price: 5000,
          duration: 30,
          features: ['Accès à tout le contenu', 'Notifications en temps réel', 'Badge VIP', 'Accès aux événements privés'],
          targetType: 'user',
          isActive: true
        },
        {
          id: 'plan-basic-business',
          name: 'Abonnement Business Basic',
          level: 'basic',
          price: 0,
          duration: 0, // illimité
          features: ['Accès aux offres standards', 'Notifications de base'],
          targetType: 'business',
          isActive: true
        },
        {
          id: 'plan-premium-business',
          name: 'Abonnement Business Premium',
          level: 'premium',
          price: 3000,
          duration: 30,
          features: ['Accès aux offres exclusives', 'Notifications prioritaires', 'Réductions spéciales'],
          targetType: 'business',
          isActive: true
        },
        {
          id: 'plan-vip-business',
          name: 'Abonnement Business VIP',
          level: 'vip',
          price: 10000,
          duration: 30,
          features: ['Accès à toutes les offres', 'Notifications en temps réel', 'Réductions maximales', 'Invitations aux lancements de produits'],
          targetType: 'business',
          isActive: true
        }
      ];

      setSubscriptionPlans(mockPlans);
    };

    loadSubscriptionPlans();
  }, []);

  // Fonctions existantes
  const followUser = async (userId: string) => {
    if (!currentUser) return;

    try {
      const { error } = await supabase
        .from('followers')
        .insert([
          {
            follower_id: currentUser.id,
            following_id: userId
          }
        ]);

      if (error) throw error;

      // Update local state
      setFollowing([...following, userId]);
    } catch (error) {
      console.error("Erreur lors du suivi:", error);
      throw error;
    }
  };

  const unfollowUser = async (userId: string) => {
    if (!currentUser) return;

    try {
      const { error } = await supabase
        .from('followers')
        .delete()
        .match({
          follower_id: currentUser.id,
          following_id: userId
        });

      if (error) throw error;

      // Update local state
      setFollowing(following.filter((id: string) => id !== userId));
    } catch (error) {
      console.error("Erreur lors de l'arrêt du suivi:", error);
      throw error;
    }
  };

  const isFollowing = async (userId: string) => {
    if (!currentUser) return false;

    try {
      const { data, error } = await supabase
        .from('followers')
        .select('id')
        .eq('follower_id', currentUser.id)
        .eq('following_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned

      return !!data;
    } catch (error) {
      console.error("Erreur lors de la vérification du suivi:", error);
      return false;
    }
  };

  const getFollowersDetails = async (userId: string) => {
    try {
      // Récupérer les relations de suivi d'abord
      const { data: followersData, error: followersError } = await supabase
        .from('followers')
        .select('follower_id')
        .eq('following_id', userId);

      if (followersError) throw followersError;
      if (!followersData || followersData.length === 0) return [];

      // Récupérer les profils des abonnés
      const followerIds = followersData.map(f => f.follower_id);
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select(`
          id,
          username,
          profile_picture,
          role,
          city,
          country,
          followers_count,
          following_count
        `)
        .in('id', followerIds);

      if (profilesError) throw profilesError;

      let enrichedProfiles = profiles || [];

      // Récupérer les informations business pour les comptes business
      const businessIds = enrichedProfiles.filter(p => p.role === 'business').map(p => p.id);
      if (businessIds.length > 0) {
        const { data: businessData, error: businessError } = await supabase
          .from('business_profiles')
          .select('id, business_name')
          .in('id', businessIds);

        if (!businessError && businessData) {
          // Ajouter les noms d'entreprise aux profils
          enrichedProfiles = enrichedProfiles.map(profile => {
            const businessInfo = businessData.find(b => b.id === profile.id);
            return {
              ...profile,
              business_name: businessInfo?.business_name || null
            };
          });
        }
      }

      return enrichedProfiles.map(profile => ({
        id: profile.id,
        username: profile.username,
        profile_picture: profile.profile_picture,
        role: profile.role,
        businessName: profile.business_name,
        city: profile.city,
        country: profile.country,
        followers_count: profile.followers_count || 0,
        following_count: profile.following_count || 0
      }));
    } catch (error) {
      console.error('Erreur lors de la récupération des abonnés:', error);
      return [];
    }
  };

  const getFollowingDetails = async (userId: string) => {
    try {
      // Récupérer les relations de suivi d'abord
      const { data: followingData, error: followingError } = await supabase
        .from('followers')
        .select('following_id')
        .eq('follower_id', userId);

      if (followingError) throw followingError;
      if (!followingData || followingData.length === 0) return [];

      // Récupérer les profils des personnes suivies
      const followingIds = followingData.map(f => f.following_id);
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select(`
          id,
          username,
          profile_picture,
          role,
          city,
          country,
          followers_count,
          following_count
        `)
        .in('id', followingIds);

      if (profilesError) throw profilesError;

      let enrichedProfiles = profiles || [];

      // Récupérer les informations business pour les comptes business
      const businessIds = enrichedProfiles.filter(p => p.role === 'business').map(p => p.id);
      if (businessIds.length > 0) {
        const { data: businessData, error: businessError } = await supabase
          .from('business_profiles')
          .select('id, business_name')
          .in('id', businessIds);

        if (!businessError && businessData) {
          // Ajouter les noms d'entreprise aux profils
          enrichedProfiles = enrichedProfiles.map(profile => {
            const businessInfo = businessData.find(b => b.id === profile.id);
            return {
              ...profile,
              business_name: businessInfo?.business_name || null
            };
          });
        }
      }

      return enrichedProfiles.map(profile => ({
        id: profile.id,
        username: profile.username,
        profile_picture: profile.profile_picture,
        role: profile.role,
        businessName: profile.business_name,
        city: profile.city,
        country: profile.country,
        followers_count: profile.followers_count || 0,
        following_count: profile.following_count || 0
      }));
    } catch (error) {
      console.error('Erreur lors de la récupération des abonnements:', error);
      return [];
    }
  };

  // Nouvelles fonctions pour le système d'abonnement
  const subscribeToUser = async (userId: string, level: 'basic' | 'premium' | 'vip') => {
    if (!currentUser) return;

    try {
      const plan = subscriptionPlans.find(p => p.level === level && p.targetType === 'user');
      if (!plan) throw new Error("Plan d'abonnement non trouvé");

      const expiresAt = plan.duration > 0
        ? new Date(Date.now() + plan.duration * 24 * 60 * 60 * 1000)
        : undefined;

      const subscription: ISubscription = {
        id: `sub-${Date.now()}`,
        subscriberId: currentUser.id,
        targetId: userId,
        targetType: 'user',
        level,
        createdAt: new Date(),
        updatedAt: new Date(),
        status: 'active',
        notificationsEnabled: true,
        autoRenewal: true,
        expiresAt
      };

      // Dans une vraie application, nous sauvegarderions l'abonnement dans la base de données
      console.log("Nouvel abonnement créé:", subscription);

      // Si c'est un abonnement payant, traiter le paiement
      if (plan.price > 0) {
        // Logique de paiement ici
        console.log(`Paiement de ${plan.price} F CFA traité pour l'abonnement ${subscription.id}`);
      }

      // Suivre automatiquement l'utilisateur
      await followUser(userId);

    } catch (error) {
      console.error("Erreur l'abonnement:", error);
      throw error;
    }
  };

  const subscribeToBusinesss = async (businessId: string, level: 'basic' | 'premium' | 'vip') => {
    if (!currentUser) return;

    try {
      const plan = subscriptionPlans.find(p => p.level === level && p.targetType === 'business');
      if (!plan) throw new Error("Plan d'abonnement non trouvé");

      const expiresAt = plan.duration > 0
        ? new Date(Date.now() + plan.duration * 24 * 60 * 60 * 1000)
        : undefined;

      const subscription: ISubscription = {
        id: `sub-${Date.now()}`,
        subscriberId: currentUser.id,
        targetId: businessId,
        targetType: 'business',
        level,
        createdAt: new Date(),
        updatedAt: new Date(),
        status: 'active',
        notificationsEnabled: true,
        autoRenewal: true,
        expiresAt
      };

      // Dans une vraie application, nous sauvegarderions l'abonnement dans la base de données
      console.log("Nouvel abonnement business créé:", subscription);

      // Si c'est un abonnement payant, traiter le paiement
      if (plan.price > 0) {
        // Logique de paiement ici
        console.log(`Paiement de ${plan.price} F CFA traité pour l'abonnement business ${subscription.id}`);
      }

      // Suivre automatiquement l'entreprise
      await followUser(businessId);

    } catch (error) {
      console.error("Erreur l'abonnement business:", error);
      throw error;
    }
  };

  const cancelSubscription = async (subscriptionId: string) => {
    // Logique pour annuler un abonnement
    console.log(`Abonnement ${subscriptionId} annulé`);
  };

  const pauseSubscription = async (subscriptionId: string) => {
    // Logique pour mettre en pause un abonnement
    console.log(`Abonnement ${subscriptionId} mis en pause`);
  };

  const resumeSubscription = async (subscriptionId: string) => {
    // Logique pour reprendre un abonnement
    console.log(`Abonnement ${subscriptionId} repris`);
  };

  const getActiveSubscriptions = async (): Promise<ISubscription[]> => {
    // Dans une vraie application, nous récupérerions les abonnements depuis la base de données
    // Pour cette simulation, nous retournons un tableau vide
    return [];
  };

  const getSubscriptionPlans = async (): Promise<ISubscriptionPlan[]> => {
    return subscriptionPlans;
  };

  const getUserSubscribers = async (userId: string): Promise<any[]> => {
    // Dans une vraie application, nous récupérerions les abonnés depuis la base de données
    // Pour cette simulation, nous retournons un tableau vide
    return [];
  };

  const getSubscriptionDetails = async (subscriptionId: string): Promise<ISubscription | null> => {
    // Dans une vraie application, nous récupérerions les détails de l'abonnement depuis la base de données
    // Pour cette simulation, nous retournons null
    return null;
  };

  const toggleNotifications = async (subscriptionId: string, enabled: boolean) => {
    // Logique pour activer/désactiver les notifications
    console.log(`Notifications pour l'abonnement ${subscriptionId} ${enabled ? 'activées' : 'désactivées'}`);
  };

  const toggleAutoRenewal = async (subscriptionId: string, enabled: boolean) => {
    // Logique pour activer/désactiver le renouvellement automatique
    console.log(`Renouvellement automatique pour l'abonnement ${subscriptionId} ${enabled ? 'activé' : 'désactivé'}`);
  };

  return (
    <FollowContext.Provider
      value={{
        followUser,
        unfollowUser,
        isFollowing,
        getFollowersDetails,
        getFollowingDetails,
        subscribeToUser,
        subscribeToBusinesss,
        cancelSubscription,
        pauseSubscription,
        resumeSubscription,
        getActiveSubscriptions,
        getSubscriptionPlans,
        getUserSubscribers,
        getSubscriptionDetails,
        toggleNotifications,
        toggleAutoRenewal
      }}
    >
      {children}
    </FollowContext.Provider>
  );
};

export const useFollow = () => {
  const context = useContext(FollowContext);
  if (context === undefined) {
    throw new Error('useFollow must be used within a FollowProvider');
  }
  return context;
};
