-- C<PERSON>er la table des abonnements business
CREATE TABLE IF NOT EXISTS public.business_subscriptions (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_id text NOT NULL,
  start_date timestamptz NOT NULL,
  end_date timestamptz NOT NULL,
  is_active boolean DEFAULT true,
  is_trial boolean DEFAULT false,
  auto_renew boolean DEFAULT false,
  payment_status text DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- C<PERSON>er des index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_business_subscriptions_business_id ON public.business_subscriptions(business_id);
CREATE INDEX IF NOT EXISTS idx_business_subscriptions_is_active ON public.business_subscriptions(is_active);
CREATE INDEX IF NOT EXISTS idx_business_subscriptions_end_date ON public.business_subscriptions(end_date);
CREATE INDEX IF NOT EXISTS idx_business_subscriptions_created_at ON public.business_subscriptions(created_at DESC);

-- Activer RLS
ALTER TABLE public.business_subscriptions ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre aux utilisateurs de voir leurs propres abonnements
CREATE POLICY "Users can view their own subscriptions" ON public.business_subscriptions
  FOR SELECT USING (auth.uid() = business_id);

-- Politique pour permettre l'insertion d'abonnements (pour le système)
CREATE POLICY "System can insert subscriptions" ON public.business_subscriptions
  FOR INSERT WITH CHECK (true);

-- Politique pour permettre la mise à jour d'abonnements (pour le système)
CREATE POLICY "System can update subscriptions" ON public.business_subscriptions
  FOR UPDATE WITH CHECK (true);

-- Politique pour permettre aux admins de voir tous les abonnements
CREATE POLICY "Admins can view all subscriptions" ON public.business_subscriptions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.admin_profiles 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

-- Politique pour permettre aux admins de mettre à jour les abonnements
CREATE POLICY "Admins can update all subscriptions" ON public.business_subscriptions
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.admin_profiles 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_business_subscriptions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour automatiquement updated_at
CREATE TRIGGER update_business_subscriptions_updated_at
  BEFORE UPDATE ON public.business_subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION update_business_subscriptions_updated_at();

-- Donner les permissions nécessaires
GRANT SELECT, INSERT, UPDATE ON public.business_subscriptions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.business_subscriptions TO service_role;
