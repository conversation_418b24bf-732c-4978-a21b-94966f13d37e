-- Migration pour ajouter les informations d'entreprise aux commentaires
-- <PERSON>la permet d'identifier les commentaires d'entreprises et d'afficher leur nom d'entreprise

-- Ajouter les colonnes pour les informations d'entreprise
ALTER TABLE public.comments 
ADD COLUMN author_role VARCHAR(20) DEFAULT 'standard',
ADD COLUMN business_name VARCHAR(255);

-- Ajouter une contrainte pour author_role
ALTER TABLE public.comments 
ADD CONSTRAINT comments_author_role_check 
CHECK (author_role IN ('standard', 'business', 'admin'));

-- Mettre à jour les commentaires existants en fonction du rôle de l'utilisateur
UPDATE public.comments 
SET author_role = (
  SELECT COALESCE(u.role, 'standard')
  FROM auth.users au
  LEFT JOIN public.users u ON au.id = u.id
  WHERE au.id = comments.user_id
);

-- Mettre à jour le business_name pour les utilisateurs business
UPDATE public.comments 
SET business_name = (
  SELECT u.business_name
  FROM public.users u
  WHERE u.id = comments.user_id AND u.role = 'business'
)
WHERE author_role = 'business';
