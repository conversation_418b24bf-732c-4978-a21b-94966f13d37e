import React, { useState, useEffect } from 'react';
import {
  Database,
  CheckCircle,
  AlertCircle,
  Loader,
  RefreshCw,
  Package,
  Bell,
  Settings
} from 'lucide-react';
import Button from '../ui/Button';
import {
  initializeOrderTrackingSystem,
  checkOrderTrackingSystem,
  createTestTrackingData
} from '../../utils/initializeDatabase';
import { OrderTrackingService } from '../../services/orderTrackingService';
import { NotificationService } from '../../services/notificationService';

const OrderTrackingSetup: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<{
    isConfigured: boolean;
    missingTables: string[];
    hasTestData: boolean;
  } | null>(null);

  const [loading, setLoading] = useState(false);
  const [initializing, setInitializing] = useState(false);

  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  // Vérifier le statut du système
  const checkStatus = async () => {
    setLoading(true);
    try {
      const status = await checkOrderTrackingSystem();
      setSystemStatus(status);

      if (status.isConfigured) {
        setMessage({ type: 'success', text: 'Système de suivi configuré et opérationnel !' });
      } else {
        setMessage({
          type: 'error',
          text: `Tables manquantes: ${status.missingTables.join(', ')}`
        });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Erreur lors de la vérification du système' });
    } finally {
      setLoading(false);
    }
  };

  // Initialiser le système
  const initializeSystem = async () => {
    setInitializing(true);
    setMessage({ type: 'info', text: 'Initialisation du système en cours...' });

    try {
      const success = await initializeOrderTrackingSystem();

      if (success) {
        setMessage({ type: 'success', text: 'Système initialisé avec succès !' });
        await checkStatus(); // Revérifier le statut
      } else {
        setMessage({ type: 'error', text: 'Échec de l\'initialisation du système' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Erreur lors de l\'initialisation' });
    } finally {
      setInitializing(false);
    }
  };



  // Demander les permissions de notification
  const requestNotificationPermission = async () => {
    const granted = await NotificationService.requestNotificationPermission();
    if (granted) {
      setMessage({ type: 'success', text: 'Permissions de notification accordées !' });
    } else {
      setMessage({ type: 'error', text: 'Permissions de notification refusées' });
    }
  };

  useEffect(() => {
    checkStatus();
  }, []);

  const getStatusIcon = () => {
    if (loading) return <Loader className="w-5 h-5 animate-spin" />;
    if (systemStatus?.isConfigured) return <CheckCircle className="w-5 h-5 text-green-500" />;
    return <AlertCircle className="w-5 h-5 text-red-500" />;
  };

  const getStatusColor = () => {
    if (systemStatus?.isConfigured) return 'border-green-200 bg-green-50';
    return 'border-red-200 bg-red-50';
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        {/* Header */}
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Settings className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Configuration du Suivi de Commande</h1>
              <p className="text-gray-600">Initialisez et testez le système de suivi automatique</p>
            </div>
          </div>
        </div>

        {/* Statut du système */}
        <div className="p-6">
          <div className={`border rounded-lg p-4 ${getStatusColor()}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getStatusIcon()}
                <div>
                  <h3 className="font-semibold text-gray-900">Statut du Système</h3>
                  <p className="text-sm text-gray-600">
                    {loading ? 'Vérification en cours...' :
                     systemStatus?.isConfigured ? 'Système opérationnel' : 'Configuration requise'}
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={checkStatus}
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Vérifier
              </Button>
            </div>

            {systemStatus && (
              <div className="mt-4 space-y-2">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <Database className="w-4 h-4 text-gray-500" />
                    <span className="text-sm">
                      Tables: {systemStatus.missingTables.length === 0 ? '✅ OK' : '❌ Manquantes'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Package className="w-4 h-4 text-gray-500" />
                    <span className="text-sm">
                      Données test: {systemStatus.hasTestData ? '✅ Présentes' : '⚠️ Absentes'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Bell className="w-4 h-4 text-gray-500" />
                    <span className="text-sm">
                      Notifications: {Notification.permission === 'granted' ? '✅ Activées' : '⚠️ Désactivées'}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="p-6 border-t border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Initialiser le système */}
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">1. Initialisation</h4>
              <Button
                variant="primary"
                fullWidth
                onClick={initializeSystem}
                disabled={initializing || systemStatus?.isConfigured}
                className="flex items-center justify-center space-x-2"
              >
                {initializing ? (
                  <Loader className="w-4 h-4 animate-spin" />
                ) : (
                  <Database className="w-4 h-4" />
                )}
                <span>{initializing ? 'Initialisation...' : 'Initialiser'}</span>
              </Button>
            </div>

            {/* Permissions notifications */}
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">2. Notifications</h4>
              <Button
                variant="outline"
                fullWidth
                onClick={requestNotificationPermission}
                disabled={Notification.permission === 'granted'}
                className="flex items-center justify-center space-x-2"
              >
                <Bell className="w-4 h-4" />
                <span>
                  {Notification.permission === 'granted' ? 'Activées' : 'Activer'}
                </span>
              </Button>
            </div>

            {/* Données de test */}
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">3. Données Test</h4>
              <Button
                variant="outline"
                fullWidth
                onClick={createTestTrackingData}
                disabled={!systemStatus?.isConfigured}
                className="flex items-center justify-center space-x-2"
              >
                <Package className="w-4 h-4" />
                <span>Créer</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Messages */}
        {message && (
          <div className="p-6 border-t border-gray-100">
            <div className={`p-4 rounded-lg border ${
              message.type === 'success' ? 'border-green-200 bg-green-50 text-green-800' :
              message.type === 'error' ? 'border-red-200 bg-red-50 text-red-800' :
              'border-blue-200 bg-blue-50 text-blue-800'
            }`}>
              <div className="flex items-center space-x-2">
                {message.type === 'success' && <CheckCircle className="w-4 h-4" />}
                {message.type === 'error' && <AlertCircle className="w-4 h-4" />}
                {message.type === 'info' && <Loader className="w-4 h-4 animate-spin" />}
                <span className="text-sm font-medium">{message.text}</span>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="p-6 border-t border-gray-100 bg-gray-50">
          <h4 className="font-medium text-gray-900 mb-3">Instructions</h4>
          <div className="space-y-2 text-sm text-gray-600">
            <p>1. <strong>Initialisez</strong> le système pour créer les tables nécessaires</p>
            <p>2. <strong>Activez</strong> les notifications pour recevoir les mises à jour</p>
            <p>3. <strong>Créez</strong> des données de test si nécessaire</p>
            <p>4. <strong>Testez</strong> le système en achetant un produit sur le marketplace</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderTrackingSetup;
