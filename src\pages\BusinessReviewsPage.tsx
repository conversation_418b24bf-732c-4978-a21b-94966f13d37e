import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '../context/AuthContext';
import { usePosts } from '../context/PostsContext';
import { supabase } from '../lib/supabase';
import { UserRole, IPost, IComment } from '../types/index';
import { Navigate } from 'react-router-dom';
import { MessageSquare, Search, Filter, ChevronDown, ArrowDownUp, Star, ThumbsUp, X, AlertCircle } from 'lucide-react';
import '../styles/BusinessReviews.css';

const BusinessReviewsPage: React.FC = () => {
  const { currentUser } = useAuth();
  const { posts, addComment } = usePosts();
  const [loading, setLoading] = useState(false);
  const [filterRating, setFilterRating] = useState<string>('all');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterResponse, setFilterResponse] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'rating'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedPost, setSelectedPost] = useState<IPost | null>(null);
  const [showReviewResponse, setShowReviewResponse] = useState(false);
  const [responseText, setResponseText] = useState('');

  // Filtrer les posts concernant cette entreprise (UNIQUEMENT les avis des clients, pas les posts de l'entreprise)
  const businessPosts = useMemo(() => {
    if (!currentUser || currentUser.role !== UserRole.BUSINESS) return [];

    const businessName = (currentUser as any).businessName;
    if (!businessName) return [];



    const filteredPosts = posts.filter(post => {
      // Le post doit mentionner cette entreprise
      const isAboutThisBusiness = post.businessName.toLowerCase() === businessName.toLowerCase();

      if (!isAboutThisBusiness) return false;

      // Cas 1: Post créé par un client qui mentionne l'entreprise
      const isClientPost = post.userId !== currentUser.id;

      // Cas 2: Post créé par l'entreprise qui a reçu des commentaires de clients
      const isBusinessPostWithClientComments = post.userId === currentUser.id &&
        post.comments && post.comments.length > 0 &&
        post.comments.some(comment => comment.userId !== currentUser.id);

      const matches = isClientPost || isBusinessPostWithClientComments;



      return matches;
    });


    return filteredPosts;
  }, [posts, currentUser]);

  // Calculer les statistiques
  const reviewStats = useMemo(() => {
    const totalReviews = businessPosts.length;

    // Calculer la note moyenne
    let totalRating = 0;
    let ratingCount = 0;

    businessPosts.forEach(post => {
      if (post.rating && post.rating > 0) {
        totalRating += post.rating;
        ratingCount++;
      }

      post.comments?.forEach(comment => {
        if (comment.hasUsedProduct && comment.rating && comment.rating > 0) {
          totalRating += comment.rating;
          ratingCount++;
        }
      });
    });

    const averageRating = ratingCount > 0 ? totalRating / ratingCount : 0;
    const positiveReviews = businessPosts.filter(post =>
      (post.rating && post.rating >= 4) || (post as any).type === 'favorite'
    ).length;

    // Compter les avis en attente de réponse (posts sans réponse de l'entreprise)
    const pendingResponses = businessPosts.filter(post =>
      !post.comments?.some(comment => comment.userId === currentUser?.id)
    ).length;

    return {
      totalReviews,
      averageRating,
      positiveReviews,
      pendingResponses
    };
  }, [businessPosts, currentUser?.id]);

  // Obtenir les catégories uniques
  const categories = useMemo(() => {
    return [...new Set(businessPosts.map(post => post.category).filter(Boolean))];
  }, [businessPosts]);

  useEffect(() => {
    // Plus besoin de charger des données fictives
    setLoading(false);
  }, []);

  // Rediriger si l'utilisateur n'est pas une entreprise
  if (currentUser && currentUser.role !== UserRole.BUSINESS) {
    return <Navigate to="/profile" replace />;
  }

  // Filtrer les posts en fonction des critères
  const filteredPosts = useMemo(() => {
    return businessPosts.filter(post => {
      // Filtre par note
      if (filterRating !== 'all') {
        const rating = parseInt(filterRating);
        if (post.rating !== rating) {
          return false;
        }
      }

      // Filtre par catégorie
      if (filterCategory !== 'all' && post.category !== filterCategory) {
        return false;
      }

      // Filtre par réponse
      const hasBusinessResponse = post.comments?.some(comment => comment.userId === currentUser?.id);
      if (filterResponse === 'responded' && !hasBusinessResponse) {
        return false;
      } else if (filterResponse === 'pending' && hasBusinessResponse) {
        return false;
      }

      // Filtre par recherche
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          post.description.toLowerCase().includes(query) ||
          post.username.toLowerCase().includes(query) ||
          post.businessName.toLowerCase().includes(query)
        );
      }

      return true;
    });
  }, [businessPosts, filterRating, filterCategory, filterResponse, searchQuery, currentUser?.id]);

  // Trier les posts
  const sortedPosts = useMemo(() => {
    return [...filteredPosts].sort((a, b) => {
      if (sortBy === 'date') {
        return sortOrder === 'asc'
          ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      } else {
        const aRating = a.rating || 0;
        const bRating = b.rating || 0;
        return sortOrder === 'asc'
          ? aRating - bRating
          : bRating - aRating;
      }
    });
  }, [filteredPosts, sortBy, sortOrder]);

  // Fonction pour formater la date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };

  // Fonction pour générer les étoiles
  const renderStars = (rating: number) => {
    return (
      <div className="stars">
        {[...Array(5)].map((_, index) => (
          <span key={index} className={index < rating ? 'star-filled' : 'star-empty'}>
            {index < rating ? '★' : '☆'}
          </span>
        ))}
      </div>
    );
  };

  // Fonction pour répondre à un post
  const handleRespondToPost = (post: any, targetComment?: any) => {
    setSelectedPost(post);
    // Chercher une réponse existante de l'entreprise
    const existingResponse = post.comments?.find((comment: any) => comment.userId === currentUser?.id);

    if (existingResponse) {
      // Si une réponse existe déjà, la charger pour modification
      setResponseText(existingResponse.content);
    } else {
      // Nouvelle réponse : déterminer à qui on répond
      let targetName = '';

      if (targetComment) {
        // Réponse à un commentaire spécifique
        targetName = targetComment.username;
      } else if (post.userId !== currentUser?.id) {
        // Réponse au post d'un client
        targetName = post.username;
      }

      // Pré-remplir avec le nom de la personne
      const initialText = targetName ? `${targetName}, ` : '';
      setResponseText(initialText);
    }

    setShowReviewResponse(true);
  };

  // Fonction pour soumettre une réponse
  const handleSubmitResponse = async () => {
    if (selectedPost && responseText.trim() && currentUser) {
      // Vérifier s'il existe déjà une réponse de l'entreprise
      const existingResponse = selectedPost.comments?.find(comment => comment.userId === currentUser.id);

      if (existingResponse) {
        // Modifier la réponse existante
        try {
          const { error } = await supabase
            .from('comments')
            .update({ content: responseText })
            .eq('id', existingResponse.id);

          if (error) {
            console.error('Erreur lors de la modification de la réponse:', error);
            alert('Erreur lors de la modification de la réponse');
            return;
          }

          // Forcer un rafraîchissement des données
          window.location.reload();

          console.log('✅ Réponse modifiée avec succès');
        } catch (error) {
          console.error('Erreur lors de la modification:', error);
          alert('Erreur lors de la modification de la réponse');
          return;
        }
      } else {
        // Ajouter une nouvelle réponse
        const businessName = (currentUser as any).businessName || currentUser.username;
        const newComment: Partial<IComment> = {
          content: responseText,
          userId: currentUser.id,
          username: businessName, // Utiliser le nom de l'entreprise comme username
          profilePicture: currentUser.profilePicture,
          hasUsedProduct: false,
          authorRole: UserRole.BUSINESS,
          businessName: businessName
        };

        await addComment(selectedPost.id, newComment);
      }

      setShowReviewResponse(false);
      setSelectedPost(null);
      setResponseText('');
    }
  };

  // Afficher un message de chargement pendant la récupération des données
  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="sr-only">Chargement...</span>
          </div>
          <p className="mt-2">Chargement de vos avis...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="business-reviews-container">
      <div className="reviews-header">
        <div>
          <h1 className="reviews-title">Avis sur vos produits</h1>
          <p className="reviews-subtitle">Consultez et répondez aux avis de vos clients</p>


        </div>
      </div>

      <div className="reviews-stats">
        <div className="stat-card">
          <div className="stat-icon">
            <MessageSquare size={24} className="text-blue-500" />
          </div>
          <div className="stat-content">
            <div className="stat-value">{reviewStats.totalReviews}</div>
            <div className="stat-label">Avis totaux</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">
            <Star size={24} className="text-yellow-500" />
          </div>
          <div className="stat-content">
            <div className="stat-value">{reviewStats.averageRating.toFixed(1)}</div>
            <div className="stat-label">Note moyenne</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">
            <ThumbsUp size={24} className="text-green-500" />
          </div>
          <div className="stat-content">
            <div className="stat-value">{reviewStats.positiveReviews}</div>
            <div className="stat-label">Avis positifs</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">
            <AlertCircle size={24} className="text-red-500" />
          </div>
          <div className="stat-content">
            <div className="stat-value">{reviewStats.pendingResponses}</div>
            <div className="stat-label">En attente de réponse</div>
          </div>
        </div>
      </div>

      <div className="reviews-filters">
        <div className="search-container">
          <Search size={20} className="search-icon" />
          <input
            type="text"
            placeholder="Rechercher un avis..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-container">
          <div className="filter-dropdown">
            <button className="filter-button">
              <Star size={16} />
              <span>Note: {filterRating === 'all' ? 'Toutes' : filterRating}</span>
              <ChevronDown size={16} />
            </button>
            <div className="filter-dropdown-content">
              <div
                className={`filter-option ${filterRating === 'all' ? 'selected' : ''}`}
                onClick={() => setFilterRating('all')}
              >
                Toutes les notes
              </div>
              {[5, 4, 3, 2, 1].map((rating) => (
                <div
                  key={rating}
                  className={`filter-option ${filterRating === rating.toString() ? 'selected' : ''}`}
                  onClick={() => setFilterRating(rating.toString())}
                >
                  {rating} {rating > 1 ? 'étoiles' : 'étoile'}
                </div>
              ))}
            </div>
          </div>

          <div className="filter-dropdown">
            <button className="filter-button">
              <Filter size={16} />
              <span>Catégorie: {filterCategory === 'all' ? 'Toutes' : filterCategory}</span>
              <ChevronDown size={16} />
            </button>
            <div className="filter-dropdown-content">
              <div
                className={`filter-option ${filterCategory === 'all' ? 'selected' : ''}`}
                onClick={() => setFilterCategory('all')}
              >
                Toutes les catégories
              </div>
              {categories.map((category) => (
                <div
                  key={category}
                  className={`filter-option ${filterCategory === category ? 'selected' : ''}`}
                  onClick={() => setFilterCategory(category)}
                >
                  {category}
                </div>
              ))}
            </div>
          </div>

          <div className="filter-dropdown">
            <button className="filter-button">
              <MessageSquare size={16} />
              <span>Réponse: {
                filterResponse === 'all' ? 'Tous' :
                filterResponse === 'responded' ? 'Répondus' : 'En attente'
              }</span>
              <ChevronDown size={16} />
            </button>
            <div className="filter-dropdown-content">
              <div
                className={`filter-option ${filterResponse === 'all' ? 'selected' : ''}`}
                onClick={() => setFilterResponse('all')}
              >
                Tous les avis
              </div>
              <div
                className={`filter-option ${filterResponse === 'responded' ? 'selected' : ''}`}
                onClick={() => setFilterResponse('responded')}
              >
                Avis répondus
              </div>
              <div
                className={`filter-option ${filterResponse === 'pending' ? 'selected' : ''}`}
                onClick={() => setFilterResponse('pending')}
              >
                Avis en attente
              </div>
            </div>
          </div>

          <div className="sort-dropdown">
            <button className="sort-button" onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}>
              <ArrowDownUp size={16} />
              <span>Trier par: {sortBy === 'date' ? 'Date' : 'Note'} ({sortOrder === 'asc' ? '↑' : '↓'})</span>
            </button>
            <div className="sort-dropdown-content">
              <div
                className={`sort-option ${sortBy === 'date' ? 'selected' : ''}`}
                onClick={() => setSortBy('date')}
              >
                Date
              </div>
              <div
                className={`sort-option ${sortBy === 'rating' ? 'selected' : ''}`}
                onClick={() => setSortBy('rating')}
              >
                Note
              </div>
            </div>
          </div>
        </div>
      </div>



      {sortedPosts.length === 0 ? (
        <div className="no-reviews">
          <MessageSquare size={48} />
          <p>Aucun avis ne correspond à vos critères de recherche.</p>
        </div>
      ) : (
        <div className="reviews-list">
          {sortedPosts.map((post) => {
            const businessResponse = post.comments?.find(comment => comment.userId === currentUser?.id);
            const isBusinessPost = post.userId === currentUser?.id;
            const clientComments = post.comments?.filter(comment => comment.userId !== currentUser?.id) || [];

            // Si c'est un post de l'entreprise, on affiche chaque commentaire client comme un avis séparé
            if (isBusinessPost && clientComments.length > 0) {
              return clientComments.map((comment) => (
                <div key={`${post.id}-${comment.id}`} className="review-card">
                  <div className="review-header">
                    <div className="review-user-info">
                      {comment.profilePicture ? (
                        <img src={comment.profilePicture} alt={comment.username} className="user-avatar" />
                      ) : (
                        <div className="user-avatar-placeholder">{comment.username?.charAt(0) || 'U'}</div>
                      )}
                      <div>
                        <div className="user-name">{comment.username}</div>
                        <div className="review-date">{formatDate(new Date(comment.createdAt))}</div>
                      </div>
                    </div>
                    <div className="review-product-info">
                      <div className="product-name">{post.productName}</div>
                      <div className="product-category">{post.category}</div>
                    </div>
                  </div>

                  <div className="review-content">
                    <div className="review-rating">
                      {comment.rating && renderStars(comment.rating)}
                    </div>
                    <p className="review-comment">{comment.content}</p>
                    {comment.hasUsedProduct && (
                      <div className="verified-purchase">
                        <span className="verified-badge">✓ Achat vérifié</span>
                      </div>
                    )}
                  </div>

                  {businessResponse && (
                    <div className="business-response">
                      <div className="response-header">
                        <div className="business-name">Réponse de votre entreprise</div>
                        <div className="response-date">{formatDate(new Date(businessResponse.createdAt))}</div>
                      </div>
                      <p className="response-text">{businessResponse.content}</p>
                    </div>
                  )}

                  <div className="review-actions">
                    <button
                      className="respond-button"
                      onClick={() => handleRespondToPost(post)}
                    >
                      {businessResponse ? 'Modifier la réponse' : 'Répondre'}
                    </button>
                  </div>
                </div>
              ));
            }

            // Si c'est un post de client, on affiche le post comme un avis
            return (
              <div key={post.id} className={`review-card ${!businessResponse ? 'pending-response' : ''}`}>
                <div className="review-header">
                  <div className="review-user-info">
                    {post.userProfilePicture ? (
                      <img src={post.userProfilePicture} alt={post.username} className="user-avatar" />
                    ) : (
                      <div className="user-avatar-placeholder">{post.username.charAt(0)}</div>
                    )}
                    <div>
                      <div className="user-name">{post.username}</div>
                      <div className="review-date">{formatDate(new Date(post.createdAt))}</div>
                    </div>
                  </div>
                  <div className="review-product-info">
                    <div className="product-name">{post.productName}</div>
                    <div className="product-category">{post.category}</div>
                  </div>
                </div>

                <div className="review-content">
                  <div className="review-rating">
                    {post.rating && renderStars(post.rating)}
                  </div>
                  <p className="review-comment">{post.description}</p>
                  <div className="review-feedback">
                    <div className="feedback-item">
                      <ThumbsUp size={14} />
                      <span>{post.likes}</span>
                    </div>
                  </div>
                </div>

                {businessResponse && (
                  <div className="business-response">
                    <div className="response-header">
                      <div className="business-name">Réponse de votre entreprise</div>
                      <div className="response-date">{formatDate(new Date(businessResponse.createdAt))}</div>
                    </div>
                    <p className="response-text">{businessResponse.content}</p>
                  </div>
                )}

                <div className="review-actions">
                  <button
                    className="respond-button"
                    onClick={() => handleRespondToPost(post)}
                  >
                    {businessResponse ? 'Modifier la réponse' : 'Répondre'}
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Modal de réponse à un avis */}
      {showReviewResponse && selectedPost && (
        <div className="review-response-modal">
          <div className="response-modal-content">
            <div className="response-modal-header">
              <div>
                <h2>Répondre à l'avis</h2>
                {selectedPost && (
                  <p className="response-target">
                    Réponse à <strong>{(selectedPost as any).username}</strong>
                  </p>
                )}
              </div>
              <button
                className="close-button"
                onClick={() => setShowReviewResponse(false)}
              >
                <X size={24} />
              </button>
            </div>

            <div className="response-modal-body">
              <div className="original-review">
                <div className="review-product-name">Produit : {(selectedPost as any).productName}</div>
                {(selectedPost as any).rating && (
                  <div className="review-rating">{renderStars((selectedPost as any).rating)}</div>
                )}
                <div className="review-user-name">Avis de : {(selectedPost as any).username}</div>
                <p className="review-comment">{(selectedPost as any).description}</p>
              </div>

              <div className="response-form">
                <div className="response-author-info">
                  <div className="author-indicator">
                    <span className="author-label">Réponse en tant que :</span>
                    <span className="business-name-badge">
                      🏢 {(currentUser as any)?.businessName || currentUser?.username}
                    </span>
                  </div>
                </div>
                <label htmlFor="responseText">Votre réponse :</label>
                <textarea
                  id="responseText"
                  value={responseText}
                  onChange={(e) => setResponseText(e.target.value)}
                  placeholder={
                    responseText.includes(',')
                      ? `Continuez votre réponse personnalisée...`
                      : `Rédigez votre réponse en tant que ${(currentUser as any)?.businessName || currentUser?.username}...`
                  }
                  rows={5}
                ></textarea>
              </div>
            </div>

            <div className="response-modal-footer">
              <button
                className="cancel-button"
                onClick={() => setShowReviewResponse(false)}
              >
                Annuler
              </button>
              <button
                className="submit-button"
                onClick={handleSubmitResponse}
                disabled={!responseText.trim()}
              >
                Publier la réponse
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessReviewsPage;
