-- SCRIPT DE TEST ULTRA-SÉCURISÉ POUR LA VALIDATION
-- Ce script découvre d'abord la structure avant d'agir

-- =====================================================
-- 1. DÉCOUVRIR LA STRUCTURE EXACTE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 DÉCOUVERTE DE LA STRUCTURE DES TABLES';
    RAISE NOTICE '=========================================';
    RAISE NOTICE '';
END $$;

-- Colonnes de business_subscriptions
SELECT 
    '📋 COLONNES BUSINESS_SUBSCRIPTIONS' as section,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'business_subscriptions' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Colonnes de subscription_codes
SELECT 
    '🔐 COLONNES SUBSCRIPTION_CODES' as section,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'subscription_codes' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- =====================================================
-- 2. ÉTAT ACTUEL
-- =====================================================

-- Code existant
SELECT 
    '📋 CODE ACTUEL' as section,
    *
FROM subscription_codes 
WHERE code = '02500076';

-- Abonnements existants
SELECT 
    '🏢 ABONNEMENTS EXISTANTS' as section,
    *
FROM business_subscriptions 
WHERE business_id = 'f22bf671-e198-4890-9cd7-029ab3596180';

-- =====================================================
-- 3. VALIDATION DU CODE (SÉCURISÉE)
-- =====================================================

-- Valider le code
UPDATE subscription_codes 
SET 
    status = 'validated',
    validated_at = NOW(),
    validated_by = NULL,
    admin_notes = 'Test validation manuelle',
    updated_at = NOW()
WHERE code = '02500076';

-- Vérifier la validation
SELECT 
    '✅ CODE VALIDÉ' as section,
    code,
    business_name,
    status,
    validated_at,
    admin_notes
FROM subscription_codes 
WHERE code = '02500076';

-- =====================================================
-- 4. CRÉATION D'ABONNEMENT (VERSION SÉCURISÉE)
-- =====================================================

-- Désactiver les abonnements existants
UPDATE business_subscriptions 
SET is_active = false
WHERE business_id = 'f22bf671-e198-4890-9cd7-029ab3596180' 
AND is_active = true;

-- Créer un abonnement avec colonnes minimales garanties
DO $$
DECLARE
    has_plan_type boolean;
    has_amount boolean;
    has_payment_method boolean;
BEGIN
    -- Vérifier quelles colonnes existent
    SELECT EXISTS(
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'business_subscriptions' 
        AND column_name = 'plan_type'
    ) INTO has_plan_type;
    
    SELECT EXISTS(
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'business_subscriptions' 
        AND column_name = 'amount'
    ) INTO has_amount;
    
    SELECT EXISTS(
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'business_subscriptions' 
        AND column_name = 'payment_method'
    ) INTO has_payment_method;
    
    RAISE NOTICE '📊 COLONNES DISPONIBLES:';
    RAISE NOTICE '   plan_type: %', has_plan_type;
    RAISE NOTICE '   amount: %', has_amount;
    RAISE NOTICE '   payment_method: %', has_payment_method;
    RAISE NOTICE '';
    
    -- Créer l'abonnement avec les colonnes disponibles
    IF has_plan_type AND has_amount AND has_payment_method THEN
        -- Version complète
        INSERT INTO business_subscriptions (
            business_id, plan_id, plan_type, is_active, start_date, end_date, 
            amount, payment_method, created_at, updated_at,
            payment_status, stripe_subscription_id
        ) VALUES (
            'f22bf671-e198-4890-9cd7-029ab3596180', 'monthly', 'monthly', true, 
            NOW(), NOW() + INTERVAL '1 month', 25000, 'Code validation', NOW(), NOW(),
            'paid', NULL
        );
        RAISE NOTICE '✅ Abonnement créé (version complète)';
    ELSIF has_plan_type THEN
        -- Version avec plan_type
        INSERT INTO business_subscriptions (
            business_id, plan_id, plan_type, is_active, start_date, end_date, created_at, updated_at,
            payment_status, payment_method, stripe_subscription_id
        ) VALUES (
            'f22bf671-e198-4890-9cd7-029ab3596180', 'monthly', 'monthly', true, 
            NOW(), NOW() + INTERVAL '1 month', NOW(), NOW(),
            'paid', NULL, NULL
        );
        RAISE NOTICE '✅ Abonnement créé (avec plan_type)';
    ELSE
        -- Version minimale
        INSERT INTO business_subscriptions (
            business_id, plan_id, is_active, start_date, end_date, created_at, updated_at,
            payment_status, payment_method, stripe_subscription_id
        ) VALUES (
            'f22bf671-e198-4890-9cd7-029ab3596180', 'monthly', true, 
            NOW(), NOW() + INTERVAL '1 month', NOW(), NOW(),
            'paid', NULL, NULL
        );
        RAISE NOTICE '✅ Abonnement créé (version minimale)';
    END IF;
    
END $$;

-- =====================================================
-- 5. VÉRIFICATION FINALE
-- =====================================================

-- Vérifier le code
SELECT 
    '🔐 CODE FINAL' as section,
    code, business_name, status, validated_at
FROM subscription_codes 
WHERE code = '02500076';

-- Vérifier l'abonnement
SELECT 
    '🏢 ABONNEMENT FINAL' as section,
    *
FROM business_subscriptions 
WHERE business_id = 'f22bf671-e198-4890-9cd7-029ab3596180'
AND is_active = true;

-- Statistiques finales
DO $$
DECLARE
    code_validated boolean;
    has_subscription boolean;
BEGIN
    SELECT status = 'validated' INTO code_validated
    FROM subscription_codes WHERE code = '02500076';
    
    SELECT EXISTS(
        SELECT 1 FROM business_subscriptions 
        WHERE business_id = 'f22bf671-e198-4890-9cd7-029ab3596180' 
        AND is_active = true
    ) INTO has_subscription;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎯 RÉSULTAT FINAL:';
    RAISE NOTICE '==================';
    RAISE NOTICE '✅ Code validé: %', code_validated;
    RAISE NOTICE '🏢 Abonnement actif: %', has_subscription;
    
    IF code_validated AND has_subscription THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 SUCCÈS COMPLET !';
        RAISE NOTICE 'Le code est validé et l''abonnement est créé.';
        RAISE NOTICE 'Rechargez maintenant l''interface admin.';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '⚠️ PROBLÈME DÉTECTÉ';
        RAISE NOTICE 'Vérifiez les erreurs ci-dessus.';
    END IF;
    RAISE NOTICE '';
END $$;
