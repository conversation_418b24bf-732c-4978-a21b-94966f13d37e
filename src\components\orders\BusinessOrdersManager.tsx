import React, { useState, useEffect } from 'react';
import {
  Package, Search, Filter, Download, Eye, Edit, Truck, CheckCircle,
  Clock, AlertTriangle, X, Calendar, DollarSign, User, Phone, Mail,
  MapPin, FileText, Printer, MessageSquare, Star, TrendingUp,
  BarChart3, PieChart, RefreshCw, Plus, Settings, Bell
} from 'lucide-react';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';
import { useAuth } from '../../context/AuthContext';
import { formatAmount } from '../../utils/formatUtils';
import { RealOrder } from '../../services/ordersService';
import { useBusinessOrders } from '../../hooks/useBusinessOrders';
import '../../styles/BusinessOrdersManager.css';

// Utilisation des types du service
type BusinessOrder = RealOrder;

interface BusinessOrdersManagerProps {
  businessId: string;
}

const BusinessOrdersManager: React.FC<BusinessOrdersManagerProps> = ({ businessId }) => {
  const { currentUser } = useAuth();

  // Utilisation du hook personnalisé pour les commandes
  const {
    orders,
    loading,
    error,
    refreshOrders,
    updateOrderStatus,
    addTrackingNumber,
    statistics: orderStats
  } = useBusinessOrders(businessId);

  const [filteredOrders, setFilteredOrders] = useState<BusinessOrder[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [showOrderDetails, setShowOrderDetails] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'status' | 'priority'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    filterOrders();
  }, [orders, searchQuery, statusFilter, dateFilter, priorityFilter, sortBy, sortOrder]);

  // Fonction pour actualiser les commandes
  const loadOrders = async () => {
    await refreshOrders();
  };

  const filterOrders = () => {
    let filtered = [...orders];

    // Filtrage par recherche
    if (searchQuery) {
      filtered = filtered.filter(order =>
        order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.customerEmail.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.items.some(item => item.productName.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Filtrage par statut
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    // Filtrage par priorité
    if (priorityFilter !== 'all') {
      filtered = filtered.filter(order => order.priority === priorityFilter);
    }

    // Filtrage par date
    if (dateFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();

      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          filtered = filtered.filter(order => order.createdAt >= filterDate);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          filtered = filtered.filter(order => order.createdAt >= filterDate);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          filtered = filtered.filter(order => order.createdAt >= filterDate);
          break;
      }
    }

    // Tri
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'date':
          aValue = a.createdAt.getTime();
          bValue = b.createdAt.getTime();
          break;
        case 'amount':
          aValue = a.totalAmount;
          bValue = b.totalAmount;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'priority':
          const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };
          aValue = priorityOrder[a.priority];
          bValue = priorityOrder[b.priority];
          break;
        default:
          aValue = a.createdAt.getTime();
          bValue = b.createdAt.getTime();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredOrders(filtered);
  };

  // Les fonctions updateOrderStatus et addTrackingNumber sont maintenant fournies par le hook

  return (
    <div className="business-orders-manager">
      {/* En-tête avec statistiques */}
      <div className="orders-header">
        <div className="header-content">
          <div className="header-left">
            <div className="page-title">
              <Package size={28} />
              <div>
                <h1>Gestion des Commandes</h1>
                <p>Suivez et gérez toutes vos commandes clients</p>
              </div>
            </div>
          </div>

          <div className="header-actions">
            <Button className="refresh-btn" onClick={loadOrders}>
              <RefreshCw size={16} />
              Actualiser
            </Button>
            <Button className="export-btn">
              <Download size={16} />
              Exporter
            </Button>
          </div>
        </div>

        {/* Statistiques rapides */}
        <div className="orders-stats">
          <div className="stat-card">
            <div className="stat-icon total">
              <Package size={20} />
            </div>
            <div className="stat-content">
              <div className="stat-value">{orderStats.total}</div>
              <div className="stat-label">Total commandes</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon pending">
              <Clock size={20} />
            </div>
            <div className="stat-content">
              <div className="stat-value">{orderStats.pending}</div>
              <div className="stat-label">En attente</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon processing">
              <RefreshCw size={20} />
            </div>
            <div className="stat-content">
              <div className="stat-value">{orderStats.processing}</div>
              <div className="stat-label">En traitement</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon shipped">
              <Truck size={20} />
            </div>
            <div className="stat-content">
              <div className="stat-value">{orderStats.shipped}</div>
              <div className="stat-label">Expédiées</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon revenue">
              <DollarSign size={20} />
            </div>
            <div className="stat-content">
              <div className="stat-value">{formatAmount(orderStats.totalRevenue)}</div>
              <div className="stat-label">Chiffre d'affaires</div>
            </div>
          </div>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="orders-filters">
        <div className="filters-row">
          <div className="search-box">
            <Search size={16} />
            <input
              type="text"
              placeholder="Rechercher par numéro, client, produit..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="filter-group">
            <select value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>
              <option value="all">Tous les statuts</option>
              <option value="pending">En attente</option>
              <option value="confirmed">Confirmées</option>
              <option value="processing">En traitement</option>
              <option value="shipped">Expédiées</option>
              <option value="delivered">Livrées</option>
              <option value="cancelled">Annulées</option>
            </select>

            <select value={priorityFilter} onChange={(e) => setPriorityFilter(e.target.value)}>
              <option value="all">Toutes priorités</option>
              <option value="urgent">Urgent</option>
              <option value="high">Élevée</option>
              <option value="normal">Normale</option>
              <option value="low">Faible</option>
            </select>

            <select value={dateFilter} onChange={(e) => setDateFilter(e.target.value)}>
              <option value="all">Toutes les dates</option>
              <option value="today">Aujourd'hui</option>
              <option value="week">Cette semaine</option>
              <option value="month">Ce mois</option>
            </select>
          </div>

          <div className="sort-group">
            <select value={sortBy} onChange={(e) => setSortBy(e.target.value as any)}>
              <option value="date">Trier par date</option>
              <option value="amount">Trier par montant</option>
              <option value="status">Trier par statut</option>
              <option value="priority">Trier par priorité</option>
            </select>

            <Button
              className="sort-order-btn"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </Button>
          </div>
        </div>
      </div>

      {/* Liste des commandes */}
      <div className="orders-content">
        {error ? (
          <div className="error-state">
            <AlertTriangle size={48} />
            <h3>Erreur de chargement</h3>
            <p>{error}</p>
            <Button onClick={loadOrders}>
              <RefreshCw size={16} />
              Réessayer
            </Button>
          </div>
        ) : loading ? (
          <div className="loading-state">
            <RefreshCw size={24} className="spinning" />
            <p>Chargement des commandes...</p>
          </div>
        ) : filteredOrders.length === 0 ? (
          <div className="empty-state">
            <Package size={48} />
            <h3>Aucune commande trouvée</h3>
            <p>
              {orders.length === 0
                ? "Vous n'avez encore reçu aucune commande."
                : "Aucune commande ne correspond à vos critères de recherche."
              }
            </p>
            {orders.length === 0 && (
              <Button onClick={() => window.location.href = '/marketplace'}>
                Voir le marketplace
              </Button>
            )}
          </div>
        ) : (
          <div className="orders-table-container">
            <table className="orders-table">
              <thead>
                <tr>
                  <th>
                    <input
                      type="checkbox"
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedOrders(filteredOrders.map(o => o.id));
                        } else {
                          setSelectedOrders([]);
                        }
                      }}
                    />
                  </th>
                  <th>Commande</th>
                  <th>Client</th>
                  <th>Produits</th>
                  <th>Montant</th>
                  <th>Statut</th>
                  <th>Priorité</th>
                  <th>Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredOrders.map((order) => (
                  <OrderRow
                    key={order.id}
                    order={order}
                    isSelected={selectedOrders.includes(order.id)}
                    onSelect={(selected) => {
                      if (selected) {
                        setSelectedOrders([...selectedOrders, order.id]);
                      } else {
                        setSelectedOrders(selectedOrders.filter(id => id !== order.id));
                      }
                    }}
                    onViewDetails={() => setShowOrderDetails(order.id)}
                  />
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

// Composant pour une ligne de commande
interface OrderRowProps {
  order: BusinessOrder;
  isSelected: boolean;
  onSelect: (selected: boolean) => void;
  onViewDetails: () => void;
}

const OrderRow: React.FC<OrderRowProps> = ({ order, isSelected, onSelect, onViewDetails }) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock size={14} />;
      case 'confirmed': return <CheckCircle size={14} />;
      case 'processing': return <RefreshCw size={14} />;
      case 'shipped': return <Truck size={14} />;
      case 'delivered': return <CheckCircle size={14} />;
      case 'cancelled': return <X size={14} />;
      default: return <Clock size={14} />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return 'En attente';
      case 'confirmed': return 'Confirmée';
      case 'processing': return 'En traitement';
      case 'shipped': return 'Expédiée';
      case 'delivered': return 'Livrée';
      case 'cancelled': return 'Annulée';
      default: return status;
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent': return '🔴';
      case 'high': return '🟠';
      case 'normal': return '🟡';
      case 'low': return '🟢';
      default: return '🟡';
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <tr className={`order-row ${isSelected ? 'selected' : ''}`}>
      <td>
        <input
          type="checkbox"
          checked={isSelected}
          onChange={(e) => onSelect(e.target.checked)}
        />
      </td>

      <td>
        <div className="order-info">
          <div className="order-number">{order.orderNumber}</div>
          {order.trackingNumber && (
            <div className="tracking-number">📦 {order.trackingNumber}</div>
          )}
        </div>
      </td>

      <td>
        <div className="customer-info">
          <div className="customer-name">{order.customerName}</div>
          <div className="customer-contact">
            <span>{order.customerPhone}</span>
          </div>
        </div>
      </td>

      <td>
        <div className="products-info">
          <div className="products-count">
            {order.items.length} produit{order.items.length > 1 ? 's' : ''}
          </div>
          <div className="main-product">
            {order.items[0].productName}
            {order.items.length > 1 && ` +${order.items.length - 1} autre${order.items.length > 2 ? 's' : ''}`}
          </div>
        </div>
      </td>

      <td>
        <div className="amount-info">
          <div className="total-amount">{formatAmount(order.totalAmount)}</div>
          <div className="payment-status">
            <span className={`payment-badge ${order.paymentStatus}`}>
              {order.paymentStatus === 'paid' ? 'Payé' :
               order.paymentStatus === 'pending' ? 'En attente' :
               order.paymentStatus === 'failed' ? 'Échec' : 'Remboursé'}
            </span>
          </div>
        </div>
      </td>

      <td>
        <div className={`status-badge ${order.status}`}>
          {getStatusIcon(order.status)}
          <span>{getStatusLabel(order.status)}</span>
        </div>
      </td>

      <td>
        <div className="priority-info">
          <span className={`priority-badge ${order.priority}`}>
            {getPriorityIcon(order.priority)} {order.priority}
          </span>
        </div>
      </td>

      <td>
        <div className="date-info">
          <div className="created-date">{formatDate(order.createdAt)}</div>
          {order.estimatedDelivery && (
            <div className="delivery-date">
              📅 {formatDate(order.estimatedDelivery)}
            </div>
          )}
        </div>
      </td>

      <td>
        <div className="order-actions">
          <Button size="sm" onClick={onViewDetails}>
            <Eye size={14} />
          </Button>
          <Button size="sm" variant="outline">
            <Edit size={14} />
          </Button>
          <Button size="sm" variant="outline">
            <Printer size={14} />
          </Button>
        </div>
      </td>
    </tr>
  );
};

export default BusinessOrdersManager;
