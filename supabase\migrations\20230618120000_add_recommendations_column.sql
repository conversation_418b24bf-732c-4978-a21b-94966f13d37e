-- Migration pour ajouter la colonne recommendations à la table posts et établir la relation avec profiles

-- Ajout de la colonne recommendations de type uuid[] avec une valeur par défaut ARRAY[]
ALTER TABLE posts
ADD COLUMN IF NOT EXISTS recommendations UUID[] DEFAULT ARRAY[]::UUID[];

-- Ajout d'une contrainte de clé étrangère entre posts.user_id et profiles.id
ALTER TABLE posts
DROP CONSTRAINT IF EXISTS posts_user_id_fkey,
ADD CONSTRAINT posts_user_id_fkey 
FOREIGN KEY (user_id) 
REFERENCES profiles(id) 
ON DELETE CASCADE;

-- Commentaire explicatif sur l'utilisation de cette colonne
COMMENT ON COLUMN posts.recommendations IS 'Liste des UUIDs des utilisateurs qui ont recommandé ce post';

-- Vous pouvez également utiliser text[] si vous préférez stocker des identifiants sous forme de texte
-- ALTER TABLE posts
-- ADD COLUMN IF NOT EXISTS recommendations TEXT[] DEFAULT ARRAY[]::TEXT[];

-- Commentaire explicatif sur l'utilisation de cette colonne
COMMENT ON COLUMN posts.recommendations IS 'Liste des UUIDs des posts recommandés en relation avec ce post';