import React from 'react';
import { PostType } from '../../types/index';
import { validatePostType, getPostTypeDisplayName, getPostTypeColor } from '../../utils/testPostTypes';

interface PostTypeBadgeProps {
  type: PostType;
  className?: string;
}

const PostTypeBadge: React.FC<PostTypeBadgeProps> = ({ type, className = '' }) => {
  // Valider et nettoyer le type de post
  const validatedType = validatePostType(type);

  // Debug log to track badge rendering (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.log('PostTypeBadge rendering with type:', type, '-> validated:', validatedType);
  }

  const displayName = getPostTypeDisplayName(validatedType);
  const colors = getPostTypeColor(validatedType);

  return (
    <span
      className={`mt-1 inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${colors.bg} ${colors.text} ${className}`}
      data-post-type={validatedType}
      data-original-type={type}
    >
      {displayName}
    </span>
  );
};

export default PostTypeBadge;
