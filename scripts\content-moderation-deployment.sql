-- DÉPLOIEMENT COMPLET DE LA MODÉRATION DE CONTENU
-- Exécutez ce script dans Supabase SQL Editor

-- =====================================================
-- 1. VÉRIFIER ET CRÉER LES COLONNES DE MODÉRATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 Vérification de la structure des tables de modération...';
    
    -- Ajouter les colonnes de modération aux posts si nécessaire
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'moderation_status'
    ) THEN
        ALTER TABLE posts ADD COLUMN moderation_status TEXT DEFAULT 'approved';
        RAISE NOTICE '✅ Colonne moderation_status ajoutée à posts';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'is_flagged'
    ) THEN
        ALTER TABLE posts ADD COLUMN is_flagged BOOLEAN DEFAULT false;
        RAISE NOTICE '✅ Colonne is_flagged ajoutée à posts';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'flag_reason'
    ) THEN
        ALTER TABLE posts ADD COLUMN flag_reason TEXT;
        RAISE NOTICE '✅ Colonne flag_reason ajoutée à posts';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'moderator_notes'
    ) THEN
        ALTER TABLE posts ADD COLUMN moderator_notes TEXT;
        RAISE NOTICE '✅ Colonne moderator_notes ajoutée à posts';
    END IF;
    
    -- Ajouter les colonnes de modération aux commentaires marketplace si nécessaire
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'marketcomen' AND column_name = 'moderation_status'
    ) THEN
        ALTER TABLE marketcomen ADD COLUMN moderation_status TEXT DEFAULT 'approved';
        RAISE NOTICE '✅ Colonne moderation_status ajoutée à marketcomen';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'marketcomen' AND column_name = 'is_flagged'
    ) THEN
        ALTER TABLE marketcomen ADD COLUMN is_flagged BOOLEAN DEFAULT false;
        RAISE NOTICE '✅ Colonne is_flagged ajoutée à marketcomen';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'marketcomen' AND column_name = 'moderator_notes'
    ) THEN
        ALTER TABLE marketcomen ADD COLUMN moderator_notes TEXT;
        RAISE NOTICE '✅ Colonne moderator_notes ajoutée à marketcomen';
    END IF;
    
END $$;

-- =====================================================
-- 2. CRÉER DES DONNÉES DE TEST POUR LA MODÉRATION
-- =====================================================

DO $$
DECLARE
    test_user_id uuid;
    test_business_id uuid;
    test_post_id uuid;
    test_comment_id uuid;
    test_campaign_id uuid;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🧪 Création de contenu de test pour la modération...';
    
    -- Récupérer un utilisateur existant ou en créer un
    SELECT id INTO test_user_id FROM profiles WHERE role = 'standard' LIMIT 1;
    
    IF test_user_id IS NULL THEN
        test_user_id := gen_random_uuid();
        INSERT INTO profiles (
            id, username, email, role, status, created_at, updated_at
        ) VALUES (
            test_user_id,
            'test_moderation_user',
            '<EMAIL>',
            'standard',
            'member',
            NOW(),
            NOW()
        );
        RAISE NOTICE '✅ Utilisateur de test créé';
    END IF;
    
    -- Récupérer une entreprise existante ou en créer une
    SELECT bp.id INTO test_business_id 
    FROM business_profiles bp 
    JOIN profiles p ON bp.id = p.id 
    WHERE p.role = 'business' 
    LIMIT 1;
    
    IF test_business_id IS NULL THEN
        test_business_id := gen_random_uuid();
        INSERT INTO profiles (
            id, username, email, role, status, created_at, updated_at
        ) VALUES (
            test_business_id,
            'test_moderation_business',
            '<EMAIL>',
            'business',
            'contributor',
            NOW(),
            NOW()
        );
        
        INSERT INTO business_profiles (
            id, business_name, business_description, business_category,
            business_status, business_verified, created_at, updated_at
        ) VALUES (
            test_business_id,
            'Test Moderation Business',
            'Entreprise de test pour la modération',
            'Technology',
            'verified',
            true,
            NOW(),
            NOW()
        );
        RAISE NOTICE '✅ Entreprise de test créée';
    END IF;
    
    -- Créer des posts de test avec différents statuts
    INSERT INTO posts (
        id, user_id, type, business_name, product_name, category,
        description, rating, moderation_status, is_flagged, flag_reason,
        created_at, updated_at
    ) VALUES 
    (
        gen_random_uuid(),
        test_user_id,
        'coup_de_coeur',
        'Test Business',
        'Produit Excellent',
        'Technology',
        'Ce produit est vraiment fantastique ! Je le recommande vivement à tous. La qualité est au rendez-vous et le service client est exceptionnel.',
        5,
        'approved',
        false,
        NULL,
        NOW() - INTERVAL '2 days',
        NOW()
    ),
    (
        gen_random_uuid(),
        test_user_id,
        'coup_de_gueule',
        'Test Business 2',
        'Produit Décevant',
        'Services',
        'Très déçu de cet achat. Le produit ne correspond pas du tout à la description. Service client inexistant.',
        1,
        'pending',
        false,
        NULL,
        NOW() - INTERVAL '1 day',
        NOW()
    ),
    (
        gen_random_uuid(),
        test_user_id,
        'coup_de_coeur',
        'Test Business 3',
        'Produit Suspect',
        'Fashion',
        'Ce message contient du contenu inapproprié et des mots offensants qui devraient être modérés.',
        3,
        'rejected',
        true,
        'Contenu inapproprié détecté',
        NOW() - INTERVAL '3 hours',
        NOW()
    );
    
    RAISE NOTICE '✅ Posts de test créés avec différents statuts';
    
    -- Créer des commentaires marketplace de test
    INSERT INTO marketcomen (
        id, product_id, user_id, comment, is_approved, is_verified_purchase,
        moderation_status, is_flagged, created_at, updated_at
    ) VALUES 
    (
        gen_random_uuid(),
        'TEST_PRODUCT_1',
        test_user_id,
        'Excellent produit, je le recommande vivement ! Livraison rapide et emballage soigné.',
        true,
        true,
        'approved',
        false,
        NOW() - INTERVAL '1 day',
        NOW()
    ),
    (
        gen_random_uuid(),
        'TEST_PRODUCT_2',
        test_user_id,
        'Produit de mauvaise qualité, je ne recommande pas du tout.',
        false,
        false,
        'pending',
        false,
        NOW() - INTERVAL '2 hours',
        NOW()
    ),
    (
        gen_random_uuid(),
        'TEST_PRODUCT_3',
        test_user_id,
        'Commentaire avec du spam et des liens suspects: www.site-malveillant.com',
        false,
        false,
        'rejected',
        true,
        NOW() - INTERVAL '30 minutes',
        NOW()
    );
    
    RAISE NOTICE '✅ Commentaires marketplace de test créés';
    
    -- Créer une campagne publicitaire de test si elle n'existe pas
    SELECT id INTO test_campaign_id FROM ad_campaigns LIMIT 1;
    
    IF test_campaign_id IS NULL THEN
        INSERT INTO ad_campaigns (
            id, business_id, title, description, budget, status,
            start_date, end_date, created_at, updated_at
        ) VALUES (
            gen_random_uuid(),
            test_business_id,
            'Campagne de Test Modération',
            'Campagne publicitaire pour tester la modération des commentaires',
            1000.00,
            'active',
            NOW(),
            NOW() + INTERVAL '30 days',
            NOW(),
            NOW()
        ) RETURNING id INTO test_campaign_id;
        
        RAISE NOTICE '✅ Campagne publicitaire de test créée';
    END IF;
    
    -- Créer des commentaires de publicité de test
    INSERT INTO ad_comments (
        id, campaign_id, user_id, content, is_approved, is_flagged,
        flagged_reason, created_at, updated_at
    ) VALUES 
    (
        gen_random_uuid(),
        test_campaign_id,
        test_user_id,
        'Super publicité ! Cette entreprise a l''air très professionnelle.',
        true,
        false,
        NULL,
        NOW() - INTERVAL '1 day',
        NOW()
    ),
    (
        gen_random_uuid(),
        test_campaign_id,
        test_user_id,
        'Publicité mensongère, attention aux arnaques !',
        false,
        false,
        NULL,
        NOW() - INTERVAL '3 hours',
        NOW()
    ),
    (
        gen_random_uuid(),
        test_campaign_id,
        test_user_id,
        'Commentaire avec contenu offensant et inapproprié qui doit être supprimé.',
        false,
        true,
        'Contenu offensant signalé par les utilisateurs',
        NOW() - INTERVAL '1 hour',
        NOW()
    );
    
    RAISE NOTICE '✅ Commentaires de publicité de test créés';
    
END $$;

-- =====================================================
-- 3. CRÉER UNE ALERTE DE DÉPLOIEMENT
-- =====================================================

DO $$
DECLARE
    admin_profile_id uuid;
    total_content integer;
    pending_content integer;
    flagged_content integer;
BEGIN
    SELECT id INTO admin_profile_id FROM admin_profiles LIMIT 1;
    
    -- Compter le contenu
    SELECT 
        (SELECT COUNT(*) FROM posts) + 
        (SELECT COUNT(*) FROM marketcomen) + 
        (SELECT COUNT(*) FROM ad_comments)
    INTO total_content;
    
    SELECT 
        (SELECT COUNT(*) FROM posts WHERE moderation_status = 'pending') + 
        (SELECT COUNT(*) FROM marketcomen WHERE moderation_status = 'pending') + 
        (SELECT COUNT(*) FROM ad_comments WHERE is_approved = false AND is_flagged = false)
    INTO pending_content;
    
    SELECT 
        (SELECT COUNT(*) FROM posts WHERE is_flagged = true) + 
        (SELECT COUNT(*) FROM marketcomen WHERE is_flagged = true) + 
        (SELECT COUNT(*) FROM ad_comments WHERE is_flagged = true)
    INTO flagged_content;
    
    IF admin_profile_id IS NOT NULL THEN
        INSERT INTO admin_alerts (
            alert_type, 
            priority, 
            title, 
            message, 
            details, 
            created_by, 
            status
        ) VALUES (
            'deployment', 
            'high', 
            'Modération de Contenu Complètement Déployée', 
            FORMAT('Le système complet de modération de contenu est maintenant opérationnel. %s éléments au total, %s en attente, %s signalés.', total_content, pending_content, flagged_content), 
            FORMAT('{"deployment": "content_moderation", "stats": {"total": %s, "pending": %s, "flagged": %s}, "features": ["posts_moderation", "ad_comments_moderation", "marketplace_reviews_moderation", "bulk_actions", "detailed_view", "export", "real_time_stats"]}', total_content, pending_content, flagged_content)::jsonb, 
            admin_profile_id, 
            'open'
        );
        
        RAISE NOTICE '✅ Alerte de déploiement créée';
    END IF;
END $$;

-- =====================================================
-- 4. RÉSUMÉ FINAL
-- =====================================================

DO $$
DECLARE
    total_posts integer;
    total_ad_comments integer;
    total_market_comments integer;
    pending_moderation integer;
    flagged_content integer;
BEGIN
    -- Statistiques finales
    SELECT COUNT(*) INTO total_posts FROM posts;
    SELECT COUNT(*) INTO total_ad_comments FROM ad_comments;
    SELECT COUNT(*) INTO total_market_comments FROM marketcomen;
    
    SELECT 
        (SELECT COUNT(*) FROM posts WHERE moderation_status = 'pending') + 
        (SELECT COUNT(*) FROM marketcomen WHERE moderation_status = 'pending') + 
        (SELECT COUNT(*) FROM ad_comments WHERE is_approved = false AND is_flagged = false)
    INTO pending_moderation;
    
    SELECT 
        (SELECT COUNT(*) FROM posts WHERE is_flagged = true) + 
        (SELECT COUNT(*) FROM marketcomen WHERE is_flagged = true) + 
        (SELECT COUNT(*) FROM ad_comments WHERE is_flagged = true)
    INTO flagged_content;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎊 ===============================================';
    RAISE NOTICE '🎊 MODÉRATION DE CONTENU COMPLÈTEMENT DÉPLOYÉE !';
    RAISE NOTICE '🎊 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '📊 STATISTIQUES FINALES:';
    RAISE NOTICE '   📝 Publications: %', total_posts;
    RAISE NOTICE '   💬 Commentaires publicités: %', total_ad_comments;
    RAISE NOTICE '   ⭐ Avis marketplace: %', total_market_comments;
    RAISE NOTICE '   ⏳ En attente de modération: %', pending_moderation;
    RAISE NOTICE '   🚩 Contenu signalé: %', flagged_content;
    RAISE NOTICE '';
    RAISE NOTICE '🚀 FONCTIONNALITÉS DISPONIBLES:';
    RAISE NOTICE '';
    RAISE NOTICE '📋 TYPES DE CONTENU MODÉRÉS:';
    RAISE NOTICE '   ✅ Publications (coup de cœur/coup de gueule)';
    RAISE NOTICE '   ✅ Commentaires de publicités';
    RAISE NOTICE '   ✅ Avis produits marketplace';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 ACTIONS DE MODÉRATION:';
    RAISE NOTICE '   ✅ Approuver le contenu';
    RAISE NOTICE '   ✅ Rejeter le contenu';
    RAISE NOTICE '   ✅ Signaler le contenu';
    RAISE NOTICE '   ✅ Supprimer le contenu';
    RAISE NOTICE '   ✅ Actions en lot';
    RAISE NOTICE '';
    RAISE NOTICE '📊 INTERFACE AVANCÉE:';
    RAISE NOTICE '   ✅ Statistiques en temps réel';
    RAISE NOTICE '   ✅ Filtres multiples (type, statut, priorité)';
    RAISE NOTICE '   ✅ Recherche globale';
    RAISE NOTICE '   ✅ Vue détaillée du contenu';
    RAISE NOTICE '   ✅ Export des données';
    RAISE NOTICE '   ✅ Pagination intelligente';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 TESTEZ MAINTENANT:';
    RAISE NOTICE '1. Allez dans "Modération Contenu"';
    RAISE NOTICE '2. Explorez les statistiques';
    RAISE NOTICE '3. Testez les filtres et la recherche';
    RAISE NOTICE '4. Cliquez sur 👁️ pour voir les détails';
    RAISE NOTICE '5. Testez les actions avec ⋮';
    RAISE NOTICE '6. Utilisez les actions en lot';
    RAISE NOTICE '7. Exportez les données';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 SYSTÈME PRÊT POUR LA PRODUCTION !';
    RAISE NOTICE '';
END $$;
