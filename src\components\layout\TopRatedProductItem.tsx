import React from 'react';
import { IPost } from '../../types';
import { Star as StarIcon } from 'lucide-react'; // Renamed to avoid conflict if Star component is used

interface TopRatedProductItemProps {
  post: IPost & { calculatedAverageRating: number }; // Expect post with pre-calculated average rating
  rank: number; // Overall rank 1, 2, or 3
}

const medalPaths: { [key: number]: string } = {
  1: '/medals/gold-medal.png',
  2: '/medals/silver-medal.png',
  3: '/medals/bronze-medal.png',
};

const medalAltText: { [key: number]: string } = {
  1: 'Médaille d\'Or',
  2: 'Médaille d\'Argent',
  3: 'Médaille de Bronze',
};

const TopRatedProductItem: React.FC<TopRatedProductItemProps> = ({ post, rank }) => {
  return (
    <li className="flex items-center py-3 px-1 hover:bg-gray-50 rounded-md transition-colors duration-150">
      <img 
        src={post.images[0] || 'https://via.placeholder.com/64'} 
        alt={post.productName} 
        className="w-12 h-12 object-cover rounded-md mr-3 flex-shrink-0" 
      />
      <div className="flex-1 min-w-0">
        <p className="text-sm font-semibold text-gray-800 truncate" title={post.productName}>
          {post.productName}
        </p>
        <p className="text-xs text-gray-500 truncate" title={post.businessName}>
          {post.businessName}
        </p>
        <div className="flex items-center mt-0.5">
          <StarIcon className="w-3 h-3 text-yellow-400 fill-current mr-1" />
          <span className="text-xs text-gray-600 font-medium">{post.calculatedAverageRating.toFixed(1)}</span>
        </div>
      </div>
      {rank >= 1 && rank <= 3 && (
        <img 
          src={medalPaths[rank]} 
          alt={medalAltText[rank]} 
          className="w-5 h-8 object-contain ml-2 flex-shrink-0" // Small vertical medal
        />
      )}
    </li>
  );
};

export default TopRatedProductItem;
