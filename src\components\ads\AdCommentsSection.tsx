import React, { useState, useEffect } from 'react';
import { MessageCircle, Building2, User, ChevronDown, ChevronUp } from 'lucide-react';
import Avatar from '../ui/Avatar';
import { adInteractionService, AdCommentWithResponse } from '../../services/adInteractionService';
import { formatTimeAgo } from '../../utils/dateUtils';

interface AdCommentsSectionProps {
  campaignId: string;
  commentsCount: number;
  onCommentsCountChange: (count: number) => void;
}

const AdCommentsSection: React.FC<AdCommentsSectionProps> = ({ 
  campaignId, 
  commentsCount, 
  onCommentsCountChange 
}) => {
  const [comments, setComments] = useState<AdCommentWithResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [expanded, setExpanded] = useState(false);

  // Charger les commentaires avec les réponses
  const loadComments = async () => {
    if (!showComments) return;
    
    setLoading(true);
    try {
      const commentsData = await adInteractionService.getAdCommentsWithResponses(campaignId);
      setComments(commentsData);
      onCommentsCountChange(commentsData.length);
    } catch (error) {
      console.error('Erreur lors du chargement des commentaires:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (showComments) {
      loadComments();
    }
  }, [showComments, campaignId]);

  const toggleComments = () => {
    setShowComments(!showComments);
    if (!showComments) {
      setExpanded(false);
    }
  };

  const displayedComments = expanded ? comments : comments.slice(0, 2);

  if (commentsCount === 0) {
    return null;
  }

  return (
    <div className="border-t border-gray-200 pt-3">
      {/* Bouton pour afficher/masquer les commentaires */}
      <button
        onClick={toggleComments}
        className="flex items-center text-sm text-gray-600 hover:text-gray-800 transition-colors mb-3"
      >
        <MessageCircle size={16} className="mr-2" />
        <span>
          {showComments ? 'Masquer' : 'Voir'} les commentaires ({commentsCount})
        </span>
        {showComments ? <ChevronUp size={16} className="ml-1" /> : <ChevronDown size={16} className="ml-1" />}
      </button>

      {/* Section des commentaires */}
      {showComments && (
        <div className="space-y-4">
          {loading ? (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-sm text-gray-500 mt-2">Chargement des commentaires...</p>
            </div>
          ) : comments.length === 0 ? (
            <div className="text-center py-4 text-gray-500">
              <MessageCircle size={32} className="mx-auto mb-2 text-gray-300" />
              <p className="text-sm">Aucun commentaire pour le moment</p>
            </div>
          ) : (
            <>
              {/* Liste des commentaires */}
              <div className="space-y-4">
                {displayedComments.map((comment) => (
                  <div key={comment.id} className="space-y-3">
                    {/* Commentaire principal */}
                    <div className="flex items-start space-x-3">
                      <Avatar
                        src={comment.profile_picture}
                        alt={comment.username || 'Utilisateur'}
                        size="sm"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="bg-gray-50 rounded-lg p-3">
                          <div className="flex items-center space-x-2 mb-1">
                            <User size={14} className="text-gray-500" />
                            <span className="font-medium text-gray-900 text-sm">
                              {comment.username || 'Utilisateur anonyme'}
                            </span>
                            <span className="text-xs text-gray-500">
                              {formatTimeAgo(new Date(comment.created_at))}
                            </span>
                          </div>
                          <p className="text-gray-700 text-sm leading-relaxed">
                            {comment.content}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Réponse de l'entreprise */}
                    {comment.businessResponse && (
                      <div className="ml-12 flex items-start space-x-3">
                        <Avatar
                          src={comment.businessResponse.businessProfilePicture}
                          alt={comment.businessResponse.businessName}
                          size="sm"
                        />
                        <div className="flex-1 min-w-0">
                          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                            <div className="flex items-center space-x-2 mb-1">
                              <Building2 size={14} className="text-blue-600" />
                              <span className="font-medium text-blue-900 text-sm">
                                {comment.businessResponse.businessName}
                              </span>
                              <span className="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded-full">
                                Entreprise
                              </span>
                              <span className="text-xs text-blue-600">
                                {formatTimeAgo(comment.businessResponse.createdAt)}
                              </span>
                            </div>
                            <p className="text-blue-800 text-sm leading-relaxed">
                              {comment.businessResponse.content}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Bouton pour voir plus/moins de commentaires */}
              {comments.length > 2 && (
                <button
                  onClick={() => setExpanded(!expanded)}
                  className="text-sm text-blue-600 hover:text-blue-800 transition-colors flex items-center"
                >
                  {expanded ? (
                    <>
                      <ChevronUp size={16} className="mr-1" />
                      Voir moins de commentaires
                    </>
                  ) : (
                    <>
                      <ChevronDown size={16} className="mr-1" />
                      Voir {comments.length - 2} commentaire(s) de plus
                    </>
                  )}
                </button>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default AdCommentsSection;
