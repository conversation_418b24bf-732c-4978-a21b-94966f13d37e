import React, { useEffect, useState } from 'react';
import { CheckCircle, Crown, Calendar, X, Sparkles } from 'lucide-react';

interface SubscriptionActivationNotificationProps {
  isVisible: boolean;
  planName: string;
  daysRemaining: number;
  onClose: () => void;
}

const SubscriptionActivationNotification: React.FC<SubscriptionActivationNotificationProps> = ({
  isVisible,
  planName,
  daysRemaining,
  onClose
}) => {
  const [showCon<PERSON>tti, setShow<PERSON>on<PERSON>tti] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setShowConfetti(true);
      // Auto-fermer après 8 secondes
      const timer = setTimeout(() => {
        onClose();
      }, 8000);
      return () => clearTimeout(timer);
    }
  }, [isVisible, onClose]);

  return (
    <>
      {isVisible && (
        <>
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 animate-fade-in"
            onClick={onClose}
          >
            {/* Notification Card */}
            <div
              className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden transform transition-all duration-500 animate-scale-in"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header avec gradient */}
              <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-6 text-white relative overflow-hidden">
                {/* Bouton fermer */}
                <button
                  onClick={onClose}
                  className="absolute top-4 right-4 text-white/80 hover:text-white transition-colors"
                >
                  <X size={20} />
                </button>

                {/* Icône principale avec animation */}
                <div className="flex justify-center mb-4 animate-bounce-in">
                  <div className="bg-white/20 rounded-full p-4">
                    <CheckCircle size={48} className="text-white" />
                  </div>
                </div>

                {/* Titre */}
                <h2 className="text-2xl font-bold text-center mb-2 animate-slide-up">
                  🎉 Abonnement Activé !
                </h2>

                {/* Sous-titre */}
                <p className="text-center text-white/90 animate-slide-up-delay">
                  Votre paiement a été validé par l'administrateur
                </p>

                {/* Éléments décoratifs */}
                {showConfetti && (
                  <div className="absolute inset-0 pointer-events-none">
                    {[...Array(20)].map((_, i) => (
                      <div
                        key={i}
                        className="absolute w-2 h-2 bg-yellow-300 rounded-full animate-confetti"
                        style={{
                          left: `${Math.random() * 100}%`,
                          top: `${Math.random() * 50}%`,
                          animationDelay: `${Math.random() * 0.5}s`
                        }}
                      />
                    ))}
                  </div>
                )}
              </div>

              {/* Contenu principal */}
              <div className="p-6">
                {/* Informations de l'abonnement */}
                <div className="bg-gray-50 rounded-xl p-4 mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <Crown className="text-yellow-500" size={20} />
                      <span className="font-semibold text-gray-900">Formule Active</span>
                    </div>
                    <span className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium">
                      Actif
                    </span>
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{planName}</h3>
                  
                  <div className="flex items-center space-x-2 text-gray-600">
                    <Calendar size={16} />
                    <span className="text-sm">
                      <span className="font-semibold text-green-600">{daysRemaining}</span> jour{daysRemaining > 1 ? 's' : ''} restant{daysRemaining > 1 ? 's' : ''}
                    </span>
                  </div>
                </div>

                {/* Message de bienvenue */}
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 mb-3">
                    <Sparkles className="text-yellow-500" size={20} />
                    <span className="text-gray-700 font-medium">Profitez de toutes vos fonctionnalités !</span>
                    <Sparkles className="text-yellow-500" size={20} />
                  </div>
                  
                  <p className="text-gray-600 text-sm">
                    Votre abonnement est maintenant actif. Vous pouvez accéder à toutes les fonctionnalités de votre formule.
                  </p>
                </div>

                {/* Bouton d'action */}
                <button
                  onClick={onClose}
                  className="w-full mt-6 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold py-3 px-6 rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-lg transform hover:scale-105 active:scale-95"
                >
                  Continuer vers le tableau de bord
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default SubscriptionActivationNotification;
