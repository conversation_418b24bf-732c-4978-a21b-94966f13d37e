import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ointer, DollarSign, Target, Clock } from 'lucide-react';
import Button from '../ui/Button';
import { formatAmount } from '../../utils/formatUtils';

// Types pour les statistiques détaillées
interface AdStatisticsData {
  impressions: {
    total: number;
    daily: number[];
    dates: string[];
  };
  clicks: {
    total: number;
    daily: number[];
  };
  ctr: number;
  cpc: number;
  spent: number;
  remainingBudget: number;
  audienceReach: {
    total: number;
    demographics: {
      label: string;
      value: number;
    }[];
  };
  conversions: {
    total: number;
    rate: number;
  };
}

interface AdStatisticsModalProps {
  isOpen: boolean;
  onClose: () => void;
  campaignId: string;
  campaignTitle: string;
  statistics: AdStatisticsData;
}

const AdStatisticsModal: React.FC<AdStatisticsModalProps> = ({
  isOpen,
  onClose,
  campaignId,
  campaignTitle,
  statistics
}) => {
  if (!isOpen) return null;

  // Formater les nombres pour l'affichage
  const formatNumber = (num: number): string => {
    return num.toLocaleString('fr-FR');
  };



  // Formater les pourcentages
  const formatPercentage = (value: number): string => {
    return `${value.toFixed(2)}%`;
  };

  return (
    <div className="ad-statistics-modal-overlay">
      <div className="ad-statistics-modal">
        <div className="ad-statistics-modal-header">
          <div>
            <h2 className="ad-statistics-modal-title">Statistiques de campagne</h2>
            <p className="ad-statistics-modal-subtitle">{campaignTitle} (ID: {campaignId})</p>
          </div>
          <button className="ad-statistics-modal-close" onClick={onClose}>
            <X size={24} />
          </button>
        </div>

        <div className="ad-statistics-modal-content">
          {/* Résumé des statistiques */}
          <div className="ad-statistics-summary">
            <div className="ad-statistics-card">
              <div className="ad-statistics-card-icon">
                <Users size={24} />
              </div>
              <div className="ad-statistics-card-content">
                <h3>Impressions</h3>
                <p className="ad-statistics-card-value">{formatNumber(statistics.impressions.total)}</p>
              </div>
            </div>

            <div className="ad-statistics-card">
              <div className="ad-statistics-card-icon">
                <MousePointer size={24} />
              </div>
              <div className="ad-statistics-card-content">
                <h3>Clics</h3>
                <p className="ad-statistics-card-value">{formatNumber(statistics.clicks.total)}</p>
              </div>
            </div>

            <div className="ad-statistics-card">
              <div className="ad-statistics-card-icon">
                <TrendingUp size={24} />
              </div>
              <div className="ad-statistics-card-content">
                <h3>CTR</h3>
                <p className="ad-statistics-card-value">{formatPercentage(statistics.ctr)}</p>
              </div>
            </div>

            <div className="ad-statistics-card">
              <div className="ad-statistics-card-icon">
                <DollarSign size={24} />
              </div>
              <div className="ad-statistics-card-content">
                <h3>CPC</h3>
                <p className="ad-statistics-card-value">{formatAmount(statistics.cpc)}</p>
              </div>
            </div>
          </div>

          {/* Graphique des impressions (simulé) */}
          <div className="ad-statistics-chart-container">
            <h3 className="ad-statistics-section-title">
              <BarChart2 size={18} className="ad-statistics-section-icon" />
              Évolution des impressions
            </h3>
            <div className="ad-statistics-chart">
              <div className="ad-statistics-chart-bars">
                {statistics.impressions.daily.map((value, index) => {
                  const maxValue = Math.max(...statistics.impressions.daily);
                  const percentage = (value / maxValue) * 100;

                  return (
                    <div key={index} className="ad-statistics-chart-bar-container">
                      <div
                        className="ad-statistics-chart-bar"
                        style={{ height: `${percentage}%` }}
                        title={`${statistics.impressions.dates[index]}: ${formatNumber(value)} impressions`}
                      ></div>
                      <div className="ad-statistics-chart-label">{statistics.impressions.dates[index].split('-')[2]}</div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Statistiques détaillées */}
          <div className="ad-statistics-details">
            <div className="ad-statistics-details-section">
              <h3 className="ad-statistics-section-title">
                <Target size={18} className="ad-statistics-section-icon" />
                Audience atteinte
              </h3>
              <p className="ad-statistics-details-value">Total: {formatNumber(statistics.audienceReach.total)} utilisateurs</p>

              <div className="ad-statistics-demographics">
                {statistics.audienceReach.demographics.map((demo, index) => (
                  <div key={index} className="ad-statistics-demographic-item">
                    <div className="ad-statistics-demographic-label">{demo.label}</div>
                    <div className="ad-statistics-demographic-bar-container">
                      <div
                        className="ad-statistics-demographic-bar"
                        style={{ width: `${(demo.value / statistics.audienceReach.total) * 100}%` }}
                      ></div>
                      <span className="ad-statistics-demographic-value">{formatPercentage((demo.value / statistics.audienceReach.total) * 100)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="ad-statistics-details-section">
              <h3 className="ad-statistics-section-title">
                <DollarSign size={18} className="ad-statistics-section-icon" />
                Budget et dépenses
              </h3>
              <div className="ad-statistics-budget-container">
                <div className="ad-statistics-budget-item">
                  <span>Dépensé:</span>
                  <span>{formatAmount(statistics.spent)}</span>
                </div>
                <div className="ad-statistics-budget-item">
                  <span>Restant:</span>
                  <span>{formatAmount(statistics.remainingBudget)}</span>
                </div>
                <div className="ad-statistics-budget-progress">
                  <div
                    className="ad-statistics-budget-progress-bar"
                    style={{ width: `${(statistics.spent / (statistics.spent + statistics.remainingBudget)) * 100}%` }}
                  ></div>
                </div>
                <div className="ad-statistics-budget-labels">
                  <span>0%</span>
                  <span>50%</span>
                  <span>100%</span>
                </div>
              </div>
            </div>

            <div className="ad-statistics-details-section">
              <h3 className="ad-statistics-section-title">
                <Clock size={18} className="ad-statistics-section-icon" />
                Conversions
              </h3>
              <div className="ad-statistics-conversions">
                <div className="ad-statistics-conversion-item">
                  <span>Total:</span>
                  <span>{formatNumber(statistics.conversions.total)}</span>
                </div>
                <div className="ad-statistics-conversion-item">
                  <span>Taux de conversion:</span>
                  <span>{formatPercentage(statistics.conversions.rate)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="ad-statistics-modal-footer">
          <Button variant="outline" onClick={onClose}>Fermer</Button>
          <Button variant="primary" rightIcon={<BarChart2 size={16} />}>Télécharger le rapport</Button>
        </div>
      </div>
    </div>
  );
};

export default AdStatisticsModal;
