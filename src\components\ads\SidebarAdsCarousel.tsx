import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, ExternalLink } from 'lucide-react';
import Button from '../ui/Button';

// Type pour les annonces publicitaires
export interface SidebarAd {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  targetUrl: string;
  businessName: string;
  businessLogo?: string;
}

// Données mockées pour les annonces publicitaires
const mockSidebarAds: SidebarAd[] = [
  {
    id: 'AD-001',
    title: 'Promotion Été - Huile de Beauté',
    description: 'Profitez de notre offre spéciale été avec 20% de réduction sur notre huile de beauté <PERSON>.',
    imageUrl: 'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=600&h=300&fit=crop&crop=center',
    targetUrl: '#',
    businessName: 'Olgane Cosmetics',
    businessLogo: 'https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?w=50&h=50&fit=crop&crop=center'
  },
  {
    id: 'AD-002',
    title: 'Lancement Crème Hydratante',
    description: 'Découvrez notre nouvelle crème hydratante visage pour une peau éclatante et revitalisée.',
    imageUrl: 'https://images.unsplash.com/photo-1570194065650-d99fb4bedf0a?w=600&h=300&fit=crop&crop=center',
    targetUrl: '#',
    businessName: 'Olgane Cosmetics',
    businessLogo: 'https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?w=50&h=50&fit=crop&crop=center'
  },
  {
    id: 'AD-003',
    title: 'Promo Flash Sérum Anti-âge',
    description: 'Offre flash de 48h : -30% sur notre sérum anti-âge révolutionnaire.',
    imageUrl: 'https://images.unsplash.com/photo-1620916566398-39f1143ab7be?w=600&h=300&fit=crop&crop=center',
    targetUrl: '#',
    businessName: 'Olgane Cosmetics',
    businessLogo: 'https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?w=50&h=50&fit=crop&crop=center'
  },
  {
    id: 'AD-004',
    title: 'Campagne Savon Naturel',
    description: 'Savon 100% naturel et artisanal, fabriqué localement avec des ingrédients biologiques.',
    imageUrl: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=600&h=300&fit=crop&crop=center',
    targetUrl: '#',
    businessName: 'Olgane Cosmetics',
    businessLogo: 'https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?w=50&h=50&fit=crop&crop=center'
  },
  {
    id: 'AD-005',
    title: 'Masque Capillaire - Nutrition Intense',
    description: 'Masque capillaire pour cheveux secs et abîmés. Nutrition intense et réparation profonde.',
    imageUrl: 'https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=600&h=300&fit=crop&crop=center',
    targetUrl: '#',
    businessName: 'Olgane Cosmetics',
    businessLogo: 'https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?w=50&h=50&fit=crop&crop=center'
  }
];

const SidebarAdsCarousel: React.FC = () => {
  // État pour suivre les annonces actuellement affichées
  const [visibleAds, setVisibleAds] = useState<SidebarAd[]>([]);
  // Nombre d'annonces à afficher à la fois
  const displayCount = 3;
  // Index de départ pour la rotation
  const [startIndex, setStartIndex] = useState(0);
  // État pour gérer les erreurs d'images
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());
  // État pour gérer le chargement des images
  const [loadingImages, setLoadingImages] = useState<Set<string>>(new Set());

  // Effet pour initialiser les annonces visibles
  useEffect(() => {
    setVisibleAds(getVisibleAds());
  }, [startIndex]);

  // Effet pour la rotation automatique des annonces
  useEffect(() => {
    const rotationInterval = setInterval(() => {
      rotateAds();
    }, 10000); // Rotation toutes les 10 secondes

    return () => clearInterval(rotationInterval);
  }, [startIndex]);

  // Fonction pour obtenir les annonces actuellement visibles
  const getVisibleAds = () => {
    const ads = [];
    for (let i = 0; i < displayCount; i++) {
      const index = (startIndex + i) % mockSidebarAds.length;
      ads.push(mockSidebarAds[index]);
    }
    return ads;
  };

  // Fonction pour faire tourner les annonces
  const rotateAds = () => {
    setStartIndex((prevIndex) => (prevIndex + 1) % mockSidebarAds.length);
  };

  // Fonction pour passer à l'annonce précédente
  const prevAd = () => {
    setStartIndex((prevIndex) =>
      prevIndex === 0 ? mockSidebarAds.length - 1 : prevIndex - 1
    );
  };

  // Fonction pour passer à l'annonce suivante
  const nextAd = () => {
    rotateAds();
  };

  // Fonction pour gérer les erreurs d'images
  const handleImageError = (imageUrl: string) => {
    setImageErrors(prev => new Set(prev).add(imageUrl));
    setLoadingImages(prev => {
      const newSet = new Set(prev);
      newSet.delete(imageUrl);
      return newSet;
    });
  };

  // Fonction pour gérer le début du chargement d'image
  const handleImageLoadStart = (imageUrl: string) => {
    setLoadingImages(prev => new Set(prev).add(imageUrl));
  };

  // Fonction pour gérer la fin du chargement d'image
  const handleImageLoadEnd = (imageUrl: string) => {
    setLoadingImages(prev => {
      const newSet = new Set(prev);
      newSet.delete(imageUrl);
      return newSet;
    });
  };

  // Fonction pour obtenir une couleur de fallback basée sur le titre
  const getFallbackGradient = (title: string) => {
    // Générer une couleur basée sur le hash du titre
    let hash = 0;
    for (let i = 0; i < title.length; i++) {
      const char = title.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }

    const colors = [
      'from-blue-500 to-purple-600',
      'from-green-500 to-blue-600',
      'from-purple-500 to-pink-600',
      'from-yellow-500 to-red-600',
      'from-indigo-500 to-purple-600',
      'from-pink-500 to-rose-600',
      'from-cyan-500 to-blue-600',
      'from-emerald-500 to-teal-600'
    ];

    return colors[Math.abs(hash) % colors.length];
  };

  return (
    <div className="sidebar-ads-carousel">
      {/* Contrôles de navigation */}
      <div className="flex justify-between items-center mb-3">
        <button
          onClick={prevAd}
          className="nav-button p-1.5 rounded-full bg-gray-100 hover:bg-gray-200 shadow-sm"
          aria-label="Annonce précédente"
        >
          <ChevronLeft size={16} className="text-gray-600" />
        </button>
        <div className="text-xs text-gray-500 font-medium">
          {startIndex + 1}-{Math.min(startIndex + displayCount, mockSidebarAds.length)} sur {mockSidebarAds.length}
        </div>
        <button
          onClick={nextAd}
          className="nav-button p-1.5 rounded-full bg-gray-100 hover:bg-gray-200 shadow-sm"
          aria-label="Annonce suivante"
        >
          <ChevronRight size={16} className="text-gray-600" />
        </button>
      </div>

      {/* Liste des annonces */}
      <div className="space-y-4">
        {visibleAds.map((ad, index) => (
          <div
            key={ad.id}
            className="ad-card ad-fade-in bg-white border border-gray-100 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="relative overflow-hidden">
              {imageErrors.has(ad.imageUrl) ? (
                <div className={`w-full h-32 bg-gradient-to-br ${getFallbackGradient(ad.title)} flex items-center justify-center`}>
                  <div className="text-white text-center p-4">
                    <h4 className="text-sm font-bold drop-shadow-lg line-clamp-2">{ad.title}</h4>
                  </div>
                </div>
              ) : (
                <div className="relative">
                  <img
                    src={ad.imageUrl}
                    alt={ad.title}
                    className="ad-image w-full h-32 object-cover"
                    onError={() => handleImageError(ad.imageUrl)}
                    onLoadStart={() => handleImageLoadStart(ad.imageUrl)}
                    onLoad={() => handleImageLoadEnd(ad.imageUrl)}
                    loading="lazy"
                  />
                  {loadingImages.has(ad.imageUrl) && (
                    <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
                      <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                  )}
                </div>
              )}
              <div className="absolute top-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded-full font-medium">
                Pub
              </div>
              {/* Overlay gradient pour améliorer la lisibilité */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="p-3">
              <div className="flex items-center mb-2">
                {ad.businessLogo && !imageErrors.has(ad.businessLogo) ? (
                  <img
                    src={ad.businessLogo}
                    alt={ad.businessName}
                    className="w-5 h-5 rounded-full mr-2 object-cover"
                    onError={() => handleImageError(ad.businessLogo!)}
                  />
                ) : (
                  <div className="w-5 h-5 rounded-full mr-2 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                    <span className="text-white text-xs font-bold">
                      {ad.businessName.charAt(0)}
                    </span>
                  </div>
                )}
                <span className="text-xs text-gray-500 font-medium">{ad.businessName}</span>
              </div>
              <h4 className="text-sm font-semibold text-gray-900 mb-1 line-clamp-2">{ad.title}</h4>
              <p className="text-xs text-gray-600 mb-3 line-clamp-2">{ad.description}</p>
              <Button
                variant="primary"
                size="sm"
                fullWidth
                rightIcon={<ExternalLink size={12} />}
                onClick={() => window.open(ad.targetUrl, '_blank')}
                className="text-xs"
              >
                En savoir plus
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SidebarAdsCarousel;
