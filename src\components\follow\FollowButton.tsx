import React, { useState, useEffect } from 'react';
import { UserPlus, UserMinus, Clock, Check, X } from 'lucide-react';
import Button from '../ui/Button';
import { useAuth } from '../../context/AuthContext';
import { useFollow } from '../../context/FollowContext';
import { useFollowRequests } from '../../context/FollowRequestContext';
import { UserRole } from '../../types';

interface FollowButtonProps {
  targetUserId: string;
  targetUsername: string;
  targetRole?: UserRole;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'outline' | 'ghost';
  className?: string;
  showText?: boolean;
}

const FollowButton: React.FC<FollowButtonProps> = ({
  targetUserId,
  targetUsername,
  targetRole,
  size = 'sm',
  variant = 'primary',
  className = '',
  showText = true
}) => {
  const { currentUser } = useAuth();
  const { isFollowing } = useFollow();
  const { sendFollowRequest, isRequestPending, cancelRequest, sentRequests } = useFollowRequests();
  
  const [isFollowingUser, setIsFollowingUser] = useState(false);
  const [isPendingRequest, setIsPendingRequest] = useState(false);
  const [loading, setLoading] = useState(false);
  const [checkingStatus, setCheckingStatus] = useState(true);

  // Vérifier le statut de suivi et de demande
  useEffect(() => {
    const checkStatus = async () => {
      if (!currentUser || currentUser.id === targetUserId) {
        setCheckingStatus(false);
        return;
      }

      try {
        // Vérifier si on suit déjà l'utilisateur
        const following = await isFollowing(targetUserId);
        setIsFollowingUser(following);

        // Vérifier s'il y a une demande en attente
        const pending = isRequestPending(targetUserId);
        setIsPendingRequest(pending);

      } catch (error) {
        console.error('Erreur lors de la vérification du statut:', error);
      } finally {
        setCheckingStatus(false);
      }
    };

    checkStatus();
  }, [currentUser, targetUserId, isFollowing, isRequestPending, sentRequests]);

  // Ne pas afficher le bouton si c'est le même utilisateur
  if (!currentUser || currentUser.id === targetUserId) {
    return null;
  }

  const handleFollowClick = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      if (isFollowingUser) {
        // Unfollow (logique existante)
        // Cette partie nécessiterait une fonction unfollow dans FollowContext
        console.log('Unfollow functionality to be implemented');
      } else if (isPendingRequest) {
        // Annuler la demande
        const pendingRequest = sentRequests.find(req => 
          req.targetId === targetUserId && req.status === 'pending'
        );
        
        if (pendingRequest) {
          const result = await cancelRequest(pendingRequest.id);
          if (result.success) {
            setIsPendingRequest(false);
          } else {
            console.error('Erreur lors de l\'annulation:', result.error);
          }
        }
      } else {
        // Envoyer une demande de suivi
        const result = await sendFollowRequest(targetUserId);
        if (result.success) {
          if (result.directFollow) {
            setIsFollowingUser(true);
          } else {
            setIsPendingRequest(true);
          }
        } else {
          console.error('Erreur lors de l\'envoi de la demande:', result.error);
        }
      }
    } catch (error) {
      console.error('Erreur lors de l\'action de suivi:', error);
    } finally {
      setLoading(false);
    }
  };

  // États du bouton
  const getButtonConfig = () => {
    if (checkingStatus) {
      return {
        text: 'Chargement...',
        icon: <Clock size={16} />,
        variant: 'outline' as const,
        disabled: true
      };
    }

    if (isFollowingUser) {
      return {
        text: 'Suivi',
        icon: <Check size={16} />,
        variant: 'outline' as const,
        disabled: false
      };
    }

    if (isPendingRequest) {
      return {
        text: 'Demande envoyée',
        icon: <Clock size={16} />,
        variant: 'outline' as const,
        disabled: false
      };
    }

    // Déterminer le texte selon le type d'utilisateur
    const followText = targetRole === UserRole.BUSINESS ? 'Suivre' : 'Suivre';
    
    return {
      text: followText,
      icon: <UserPlus size={16} />,
      variant: variant,
      disabled: false
    };
  };

  const buttonConfig = getButtonConfig();

  return (
    <Button
      variant={buttonConfig.variant}
      size={size}
      onClick={handleFollowClick}
      disabled={buttonConfig.disabled || loading}
      className={`${className} ${loading ? 'opacity-70' : ''}`}
      leftIcon={buttonConfig.icon}
    >
      {showText && (loading ? 'Chargement...' : buttonConfig.text)}
    </Button>
  );
};

export default FollowButton;
