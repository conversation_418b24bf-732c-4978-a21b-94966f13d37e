import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../context/AuthContext';
import { 
  SalesDataService, 
  ProductsDataService, 
  RankingsDataService,
  RealSale,
  RealSalesStats,
  RealProductStats
} from '../services/dataService';
import { IProduct } from '../types';

// Hook pour les données de ventes
export const useRealSalesData = () => {
  const { currentUser } = useAuth();
  const [sales, setSales] = useState<RealSale[]>([]);
  const [salesStats, setSalesStats] = useState<RealSalesStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSalesData = useCallback(async () => {
    if (!currentUser?.id) return;

    try {
      setLoading(true);
      setError(null);

      // Récupérer les ventes et statistiques en parallèle
      const [salesData, statsData] = await Promise.all([
        SalesDataService.getSalesByBusinessId(currentUser.id),
        SalesDataService.calculateSalesStats(currentUser.id)
      ]);

      setSales(salesData);
      setSalesStats(statsData);
    } catch (err) {
      console.error('Erreur lors du chargement des données de vente:', err);
      setError('Erreur lors du chargement des données de vente');
    } finally {
      setLoading(false);
    }
  }, [currentUser?.id]);

  const refreshSalesData = useCallback(() => {
    fetchSalesData();
  }, [fetchSalesData]);

  useEffect(() => {
    fetchSalesData();
  }, [fetchSalesData]);

  return {
    sales,
    salesStats,
    loading,
    error,
    refreshSalesData
  };
};

// Hook pour les données de produits
export const useRealProductsData = () => {
  const { currentUser } = useAuth();
  const [products, setProducts] = useState<IProduct[]>([]);
  const [productStats, setProductStats] = useState<RealProductStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProductsData = useCallback(async () => {
    if (!currentUser?.id) return;

    try {
      setLoading(true);
      setError(null);

      // Récupérer les produits et statistiques en parallèle
      const [productsData, statsData] = await Promise.all([
        ProductsDataService.getProductsByBusinessId(currentUser.id),
        ProductsDataService.calculateProductStats(currentUser.id)
      ]);

      setProducts(productsData);
      setProductStats(statsData);
    } catch (err) {
      console.error('Erreur lors du chargement des données de produits:', err);
      setError('Erreur lors du chargement des données de produits');
    } finally {
      setLoading(false);
    }
  }, [currentUser?.id]);

  const updateProduct = useCallback(async (productId: string, updates: Partial<IProduct>) => {
    try {
      const success = await ProductsDataService.updateProduct(productId, updates);
      if (success) {
        // Mettre à jour localement
        setProducts(prev => prev.map(p => 
          p.id === productId ? { ...p, ...updates } : p
        ));
        return true;
      }
      return false;
    } catch (err) {
      console.error('Erreur lors de la mise à jour du produit:', err);
      return false;
    }
  }, []);

  const deleteProduct = useCallback(async (productId: string) => {
    try {
      const success = await ProductsDataService.deleteProduct(productId);
      if (success) {
        // Supprimer localement
        setProducts(prev => prev.filter(p => p.id !== productId));
        // Recalculer les stats
        fetchProductsData();
        return true;
      }
      return false;
    } catch (err) {
      console.error('Erreur lors de la suppression du produit:', err);
      return false;
    }
  }, [fetchProductsData]);

  const refreshProductsData = useCallback(() => {
    fetchProductsData();
  }, [fetchProductsData]);

  useEffect(() => {
    fetchProductsData();
  }, [fetchProductsData]);

  return {
    products,
    productStats,
    loading,
    error,
    updateProduct,
    deleteProduct,
    refreshProductsData
  };
};

// Hook pour les données de classements
export const useRealRankingsData = () => {
  const { currentUser } = useAuth();
  const [rankings, setRankings] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRankingsData = useCallback(async () => {
    if (!currentUser?.id) return;

    try {
      setLoading(true);
      setError(null);

      const rankingsData = await RankingsDataService.calculateProductRankings(currentUser.id);
      setRankings(rankingsData);
    } catch (err) {
      console.error('Erreur lors du chargement des données de classements:', err);
      setError('Erreur lors du chargement des données de classements');
    } finally {
      setLoading(false);
    }
  }, [currentUser?.id]);

  const refreshRankingsData = useCallback(() => {
    fetchRankingsData();
  }, [fetchRankingsData]);

  useEffect(() => {
    fetchRankingsData();
  }, [fetchRankingsData]);

  return {
    rankings,
    loading,
    error,
    refreshRankingsData
  };
};

// Hook combiné pour toutes les données business
export const useRealBusinessData = () => {
  const salesData = useRealSalesData();
  const productsData = useRealProductsData();
  const rankingsData = useRealRankingsData();

  const loading = salesData.loading || productsData.loading || rankingsData.loading;
  const hasError = salesData.error || productsData.error || rankingsData.error;

  const refreshAllData = useCallback(() => {
    salesData.refreshSalesData();
    productsData.refreshProductsData();
    rankingsData.refreshRankingsData();
  }, [salesData.refreshSalesData, productsData.refreshProductsData, rankingsData.refreshRankingsData]);

  return {
    // Données de ventes
    sales: salesData.sales,
    salesStats: salesData.salesStats,
    
    // Données de produits
    products: productsData.products,
    productStats: productsData.productStats,
    updateProduct: productsData.updateProduct,
    deleteProduct: productsData.deleteProduct,
    
    // Données de classements
    rankings: rankingsData.rankings,
    
    // États globaux
    loading,
    hasError,
    errors: {
      sales: salesData.error,
      products: productsData.error,
      rankings: rankingsData.error
    },
    
    // Actions
    refreshAllData,
    refreshSalesData: salesData.refreshSalesData,
    refreshProductsData: productsData.refreshProductsData,
    refreshRankingsData: rankingsData.refreshRankingsData
  };
};

// Hook pour les données en temps réel avec polling
export const useRealTimeData = (intervalMs: number = 30000) => {
  const businessData = useRealBusinessData();

  useEffect(() => {
    const interval = setInterval(() => {
      businessData.refreshAllData();
    }, intervalMs);

    return () => clearInterval(interval);
  }, [businessData.refreshAllData, intervalMs]);

  return businessData;
};

// Hook pour les données avec cache
export const useCachedData = () => {
  const [cache, setCache] = useState<Map<string, { data: any; timestamp: number }>>(new Map());
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  const getCachedData = useCallback((key: string) => {
    const cached = cache.get(key);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }, [cache]);

  const setCachedData = useCallback((key: string, data: any) => {
    setCache(prev => new Map(prev.set(key, { data, timestamp: Date.now() })));
  }, []);

  const clearCache = useCallback(() => {
    setCache(new Map());
  }, []);

  return {
    getCachedData,
    setCachedData,
    clearCache
  };
};

export default {
  useRealSalesData,
  useRealProductsData,
  useRealRankingsData,
  useRealBusinessData,
  useRealTimeData,
  useCachedData
};
