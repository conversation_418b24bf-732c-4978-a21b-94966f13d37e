import React from 'react';
import { Bell, MessageCircle, Heart, Star, User, AlertCircle } from 'lucide-react';
import Avatar from '../ui/Avatar';
import { INotification, NotificationType, useNotifications } from '../../context/NotificationsContext';
import { useNavigate } from 'react-router-dom';

interface NotificationItemProps {
  notification: INotification;
  onClose?: () => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onClose }) => {
  const { markAsRead } = useNotifications();
  const navigate = useNavigate();

  const handleClick = () => {
    markAsRead(notification.id);
    
    // Navigation selon le type de notification
    if (notification.relatedItemId) {
      switch (notification.relatedItemType) {
        case 'post':
          navigate(`/posts/${notification.relatedItemId}`);
          break;
        case 'business':
          navigate(`/profile/${notification.relatedItemId}`);
          break;
        default:
          // Par défaut, ne rien faire
          break;
      }
    }
    
    if (onClose) {
      onClose();
    }
  };

  const getIcon = () => {
    switch (notification.type) {
      case NotificationType.MENTION:
        return <AlertCircle size={18} className="text-blue-500" />;
      case NotificationType.COMMENT:
        return <MessageCircle size={18} className="text-green-500" />;
      case NotificationType.LIKE:
        return <Heart size={18} className="text-red-500" />;
      case NotificationType.RECOMMENDATION:
        return <Star size={18} className="text-amber-500" />;
      case NotificationType.REVIEW:
        return <Star size={18} className="text-purple-500" />;
      case NotificationType.FOLLOW:
        return <User size={18} className="text-indigo-500" />;
      case NotificationType.SYSTEM:
      default:
        return <Bell size={18} className="text-gray-500" />;
    }
  };

  return (
    <div 
      className={`flex items-start p-3 hover:bg-gray-50 cursor-pointer ${!notification.read ? 'bg-blue-50' : ''}`}
      onClick={handleClick}
    >
      <div className="mr-3 mt-1">
        {getIcon()}
      </div>
      <div className="flex-1">
        {notification.senderProfilePicture && (
          <div className="flex items-center mb-1">
            <Avatar 
              src={notification.senderProfilePicture} 
              alt={notification.senderName || 'Utilisateur'} 
              size="sm" 
            />
            <span className="ml-2 font-medium">{notification.senderName}</span>
          </div>
        )}
        <p className="text-sm text-gray-700">{notification.message}</p>
        <p className="text-xs text-gray-500 mt-1">
          {new Date(notification.createdAt).toLocaleDateString()} à {new Date(notification.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
        </p>
      </div>
      {!notification.read && (
        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
      )}
    </div>
  );
};

export default NotificationItem;
