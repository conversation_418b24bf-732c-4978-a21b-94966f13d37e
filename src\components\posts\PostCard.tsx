import React, { useState, useMemo } from 'react';
import { ThumbsUp, MessageCircle, Share2, Star, Send, Trophy } from 'lucide-react';
import ShareModal from './ShareModal';
import { IPost, PostType, UserRole, IComment, BusinessStatus } from '../../types/index';
import { useAuth } from '../../context/AuthContext';
import { usePosts } from '../../context/PostsContext';
import { getAverageRatingForPost, getCategoryRankForPost } from '../../lib/utils';
import UserStatusBadge from '../ui/UserStatusBadge';
import BusinessStatusBadge from '../ui/BusinessStatusBadge';
import Avatar from '../ui/Avatar';
import Card, { CardFooter } from '../ui/Card';
import Button from '../ui/Button';
import Tooltip from '../ui/Tooltip';
import PostTypeBadge from './PostTypeBadge';
import BusinessNameDisplay from './BusinessNameDisplay';
import PostImageDisplay from './PostImageDisplay';
import UserRoleBadge from '../ui/UserRoleBadge';
import PostActions from './PostActions';
import EditPostModal from './EditPostModal';
import FollowButton from '../follow/FollowButton';
import { Link } from 'react-router-dom';
import { DEFAULT_IMAGES } from '../../constants/defaultImages';

interface PostCardProps {
  post: IPost;
}

const PostCard: React.FC<PostCardProps> = ({ post }) => {
  const { currentUser } = useAuth();
  const { posts: allPosts, likePost, addComment, toggleRecommendPost, sharePost } = usePosts();





  const [commentText, setCommentText] = useState('');
  const [showCommentForm, setShowCommentForm] = useState(false);
  const [hasUsedProduct, setHasUsedProduct] = useState<boolean | null>(null);
  const [productRating, setProductRating] = useState(0);
  const [showAllComments, setShowAllComments] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isLiking, setIsLiking] = useState(false);

  const isLiked = currentUser && post.likes ? post.likes.includes(currentUser.id) : false;
  const isRecommended = currentUser && post.recommendations ? post.recommendations.includes(currentUser.id) : false;
  const hasUserReviewed = currentUser ? post.comments.some(c => c.userId === currentUser.id && c.hasUsedProduct) : false;
  const isBusinessOwner = currentUser?.role === UserRole.BUSINESS && (currentUser as any).businessName === post.businessName;
  const isBusinessUser = currentUser?.role === UserRole.BUSINESS;
  const isOwnPost = currentUser && post.userId === currentUser.id;



  const handleLike = async () => {
    if (!currentUser || currentUser.role === UserRole.BUSINESS || isLiking) return;

    setIsLiking(true);
    try {
      await likePost(post.id, currentUser.id);
    } catch (error) {
      console.error('Erreur lors du like:', error);
    } finally {
      setIsLiking(false);
    }
  };

  const handleRecommend = () => {
    if (!currentUser || currentUser.role === UserRole.BUSINESS) return;
    toggleRecommendPost(post.id, currentUser.id);
  };

  const handleCommentButtonClick = () => {
    if (!currentUser || isBusinessUser) return;
    setShowCommentForm(true);
    if (hasUsedProduct === null) {
      setHasUsedProduct(null);
      setProductRating(0);
    }
  };

  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentUser || !commentText.trim() || hasUsedProduct === null || isBusinessUser) return;
    if (hasUsedProduct && hasUserReviewed) {
      alert('Vous avez déjà donné un avis sur ce produit.');
      return;
    }
    if (hasUsedProduct && productRating === 0) {
      alert('Veuillez attribuer une note au produit.');
      return;
    }
    addComment(post.id, {
      userId: currentUser.id,
      username: currentUser.username,
      profilePicture: currentUser.profilePicture || '',
      content: commentText,
      hasUsedProduct,
      rating: hasUsedProduct ? productRating : undefined,
      createdAt: new Date(),
    });
    setCommentText('');
    setHasUsedProduct(null);
    setProductRating(0);
    setShowCommentForm(false);
  };

  const formatDate = (dateInput: Date | string) => {
    return new Date(dateInput).toLocaleString('fr-FR', {
      day: 'numeric', month: 'long', year: 'numeric', hour: '2-digit', minute: '2-digit'
    });
  };

  const handleShare = () => {
    if (!currentUser) {
      alert('Vous devez être connecté pour partager un post.');
      return;
    }
    setIsShareModalOpen(true);
  };

  const handleShareComplete = async (platform: string) => {
    if (!currentUser) return;

    try {
      // 🔄 ENVOYER NOTIFICATION DE PARTAGE À L'AUTEUR
      await sharePost(post.id, currentUser.id, platform);
    } catch (error) {
      console.error('Erreur lors du partage:', error);
    }
  };

  const calculatedRating = useMemo(() => getAverageRatingForPost(post), [post]);
  const categoryRank = useMemo(() => getCategoryRankForPost(post, allPosts), [post, allPosts]);

  const getAvatarSrc = (post: IPost) => {
    if (post.authorRole === UserRole.BUSINESS) {
      return post.userProfilePicture || DEFAULT_IMAGES.BUSINESS;
    }
    return post.userProfilePicture || DEFAULT_IMAGES.AVATAR;
  };

  return (
    <Card className="mb-6 overflow-hidden">
      <div className={`p-4 ${
        post.type === PostType.FAVORITE ? 'bg-green-50' :
        post.type === PostType.COMPLAINT ? 'bg-red-50' :
        post.type === PostType.REVIEW ? 'bg-blue-50' : 'bg-gray-50'
      }`}>
        {/* User Info & Medal Container */}
          <div className="relative mb-4">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <Link to={`/profile/${post.userId}`}>
                  <Avatar src={getAvatarSrc(post)} alt={post.username} size="md" className="hover:ring-2 hover:ring-blue-400 transition-all" />
                </Link>
                <div className="ml-3">
                  <div className="flex items-center space-x-2">
                    <Link to={`/profile/${post.userId}`} className="font-semibold text-gray-900 hover:text-blue-600 hover:underline transition-colors">
                      {post.authorRole === UserRole.BUSINESS && post.businessName
                        ? post.businessName
                        : post.username}
                    </Link>
                    {/* Badge de statut utilisateur - Affichage conditionnel selon le rôle */}
                    {post.authorRole === UserRole.BUSINESS && post.businessStatus ? (
                      /* Pour les entreprises, afficher le BusinessStatusBadge avec le vrai statut */
                      <BusinessStatusBadge
                        status={post.businessStatus}
                        size="sm"
                        variant="default"
                        className="ml-2"
                      />
                    ) : post.authorStatus ? (
                      /* Pour les utilisateurs normaux, afficher le UserStatusBadge */
                      <UserStatusBadge
                        status={post.authorStatus}
                        size="sm"
                        variant="default"
                        className="ml-2"
                      />
                    ) : null}
                  </div>
                  <p className="text-xs text-gray-500">{formatDate(post.createdAt)}</p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {/* Badge Entreprise/Membre - Composant robuste */}
                <UserRoleBadge
                  role={post.authorRole}
                  showIcon={true}
                />
                {/* Bouton Suivre/Ne plus suivre */}
                {currentUser && !isOwnPost && (
                  <FollowButton
                    targetUserId={post.userId}
                    targetUsername={post.username}
                    targetRole={post.authorRole}
                    size="sm"
                    variant="primary"
                  />
                )}

                {/* Actions du post (modifier/supprimer) */}
                <PostActions
                  post={post}
                  onEdit={() => setIsEditModalOpen(true)}
                />
              </div>
          </div>

          {/* Medal Display - Positioned Absolutely */}
          {categoryRank.rank === 1 && (
            <img
              src="/medals/gold-medal.png"
              alt="Médaille d'Or - Rang 1"
              className="absolute top-0 right-0 h-20 w-14 object-contain z-10" // Approx 80px tall, 56px wide
              style={{ transform: 'translate(10%, -10%)' }} // Slight offset for overlap
            />
          )}
          {categoryRank.rank === 2 && (
            <img
              src="/medals/silver-medal.png"
              alt="Médaille d'Argent - Rang 2"
              className="absolute top-0 right-0 h-20 w-14 object-contain z-10"
              style={{ transform: 'translate(10%, -10%)' }}
            />
          )}
          {categoryRank.rank === 3 && (
            <img
              src="/medals/bronze-medal.png"
              alt="Médaille de Bronze - Rang 3"
              className="absolute top-0 right-0 h-20 w-14 object-contain z-10"
              style={{ transform: 'translate(10%, -10%)' }}
            />
          )}
        </div>

        {/* Business/Product Info */}
        <div className="mb-3">
          <div className="flex-1">
            {/* Always show business name - robust display */}
            <BusinessNameDisplay
              businessName={post.businessName}
              className="mb-1"
              showIcon={true}
            />

            {post.productName && (
              <p className="text-md text-gray-700 font-medium mb-1 flex items-center">
                📦 <span className="ml-1">{post.productName}</span>
              </p>
            )}

            <p className="text-sm text-gray-600 mb-2 flex items-center">
              🏷️ <span className="ml-1">Catégorie: {post.category || 'Non spécifiée'}</span>
            </p>

            {/* Post Type Badge */}
            <PostTypeBadge type={post.type} />

          </div>
        </div>

        {/* Post Images - Robust Display */}
        <PostImageDisplay
          images={post.images || []}
          title={post.title}
          postId={post.id}
        />

        {/* Post Description */}
        {post.description && (
          <div className="p-4 mb-3 bg-white rounded-lg shadow">
            {post.type === PostType.REVIEW && (
              <div className="mb-2 text-blue-700 text-sm font-medium flex items-center gap-2">
                <MessageCircle size={16} className="inline-block" />
                Cette publication a été créée par l'entreprise pour solliciter vos avis sur ce produit/service.
              </div>
            )}
            <p className="text-gray-700">{post.description}</p>
          </div>
        )}

        {/* Rating Section */}
        <div className="flex items-center justify-between p-4 mb-3 bg-white rounded-lg shadow">
          <div className="flex items-center">
            <span className="font-medium mr-2">Note:</span>
            {Array.from({ length: 5 }).map((_, i) => (
              <Star key={i} size={20} className={`mr-0.5 ${i < calculatedRating.average ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`} />
            ))}
            <span className="ml-2 text-sm text-gray-600">({calculatedRating.average.toFixed(1)}/5)</span>
            <span className="ml-3 text-xs text-gray-500">
              Basée sur {calculatedRating.count} avis
            </span>
          </div>
          <Button variant="primary" size="sm">En savoir +</Button>
        </div>

        {/* Category Rank Display */}
        {post.category && categoryRank.totalInCategory > 0 && (
          <div className="mt-3 mb-3 px-3 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg shadow-md flex items-center justify-between">
            <div className="flex items-center">
              <Trophy size={20} className="mr-2 text-yellow-300" />
              <span className="font-medium">Classement (Catégorie: {post.category})</span>
            </div>
            <span className="text-lg font-bold bg-white text-indigo-700 px-2 py-0.5 rounded">
              #{categoryRank.rank} / {categoryRank.totalInCategory}
            </span>
          </div>
        )}
      </div>

      {/* Stats Bar */}
      <div className="px-4 py-2 border-t border-gray-200 flex items-center space-x-2 text-sm text-gray-500">
        <div className="flex items-center">
          <ThumbsUp size={16} className="mr-1" />
          <span>{post.likes?.length || 0}</span>
        </div>
        <div className="flex items-center">
          <span>{post.comments?.length || 0} commentaires</span>
        </div>
        <div className="flex items-center">
          <span>{post.shares?.length || 0} partages</span>
        </div>
        <div className="flex items-center">
          <Star size={16} className="mr-1" />
          <span>{post.recommendations?.length || 0}</span>
        </div>
      </div>

      {/* Action Buttons */}
      <CardFooter className="flex justify-between items-center p-4 border-t border-gray-200">
        <div className="flex items-center justify-between w-full">
          {currentUser?.role === UserRole.BUSINESS ? (
            <Tooltip content="Les entreprises ne peuvent pas aimer les posts" position="top">
              <div className="flex items-center text-gray-400 opacity-50 cursor-not-allowed px-4 py-2">
                <ThumbsUp size={20} className="mr-2" />
                <span>J'aime</span>
              </div>
            </Tooltip>
          ) : (
            <button
              onClick={handleLike}
              className={`flex items-center px-4 py-2 ${isLiked ? 'text-blue-600 font-medium' : 'text-gray-600'} ${!currentUser || isLiking ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 rounded-md'} transition-colors duration-200`}
              disabled={!currentUser || isLiking}
              title={`${post.likes?.length || 0} like(s)`}
            >
              <ThumbsUp size={20} className={`mr-2 ${isLiked ? 'fill-blue-600' : ''} transition-all duration-200`} />
              <span>{isLiking ? 'En cours...' : 'J\'aime'}</span>
            </button>
          )}

          {isBusinessUser ? (
            <Tooltip content="Les entreprises ne peuvent pas commenter les publications" position="top">
              <div className="flex items-center text-gray-400 opacity-50 cursor-not-allowed px-4 py-2">
                <MessageCircle size={20} className="mr-2" />
                <span>Commenter</span>
              </div>
            </Tooltip>
          ) : (
            <button
              onClick={handleCommentButtonClick}
              className={`flex items-center px-4 py-2 text-gray-600 ${!currentUser ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 rounded-md'}`}
              disabled={!currentUser}
            >
              <MessageCircle size={20} className="mr-2" />
              <span>Commenter</span>
            </button>
          )}

          <button
            onClick={handleShare}
            className="flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-md"
          >
            <Share2 size={20} className="mr-2" />
            <span>Partager</span>
          </button>

          {currentUser?.role === UserRole.BUSINESS ? (
            <Tooltip content="Les entreprises ne peuvent pas recommander les posts" position="top">
              <div className="flex items-center text-gray-400 opacity-50 cursor-not-allowed px-4 py-2">
                <Star size={20} className="mr-2" />
                <span>Recommander</span>
              </div>
            </Tooltip>
          ) : (
            <button
              onClick={handleRecommend}
              className={`flex items-center px-4 py-2 ${isRecommended ? 'text-amber-500 font-medium' : 'text-gray-600'} ${!currentUser ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 rounded-md'}`}
              disabled={!currentUser}
            >
              <Star size={20} className={`mr-2 ${isRecommended ? 'fill-amber-500' : ''}`} />
              <span>Recommander</span>
            </button>
          )}
        </div>
      </CardFooter>

      {/* Comment Form */}
      {showCommentForm && currentUser && !isBusinessOwner && (
        <div className="p-4 border-t border-gray-200">
          {hasUsedProduct === null && (
            <div className="mb-3">
              <p className="text-sm font-medium text-gray-900 mb-2">Avez-vous déjà utilisé ce produit ou service ?</p>
              <div className="flex space-x-2">
                <Button size="sm" variant="outline" onClick={() => setHasUsedProduct(true)}>Oui</Button>
                <Button size="sm" variant="outline" onClick={() => setHasUsedProduct(false)}>Non</Button>
              </div>
            </div>
          )}
          {hasUsedProduct === true && !hasUserReviewed && (
            <div className="mb-3">
              <p className="text-sm font-medium text-gray-900 mb-1">Votre note :</p>
              <div className="flex space-x-1">
                {Array.from({ length: 5 }).map((_, i) => (
                  <button key={i} type="button" onClick={() => setProductRating(i + 1)} className="text-2xl focus:outline-none p-0.5">
                    <Star size={22} className={i < productRating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300 hover:text-yellow-300'} />
                  </button>
                ))}
              </div>
            </div>
          )}
          {hasUsedProduct !== null && (
            <form onSubmit={handleCommentSubmit}>
              <div className="flex items-center">
                <Avatar src={currentUser.profilePicture || ''} alt={currentUser.username} size="sm" />
                <input type="text" value={commentText} onChange={(e) => setCommentText(e.target.value)} placeholder="Écrire un commentaire..." className="flex-1 mx-2 px-3 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-1 focus:ring-blue-500" required />
                <Button type="submit" size="icon" variant="ghost" className="text-blue-600" disabled={!commentText.trim() || (hasUsedProduct && productRating === 0 && !hasUserReviewed)}>
                  <Send size={20} />
                </Button>
              </div>
              <div className="flex justify-end mt-2">
                 <Button variant="outline" size="sm" type="button" onClick={() => { setShowCommentForm(false); setHasUsedProduct(null); setProductRating(0); setCommentText(''); }}>Annuler</Button>
              </div>
            </form>
          )}
        </div>
      )}

      {/* Comments Display Section */}
      {post.comments && post.comments.length > 0 && (
        <div className="px-4 pt-3 pb-4 border-t border-gray-200">
          <h4 className="text-sm font-semibold text-gray-800 mb-3">
            Commentaires ({post.comments.length})
          </h4>
          <div className="space-y-3">
            {(showAllComments ? post.comments : post.comments.slice(0, 1)).map((comment: IComment) => {
              // Show business name for business-authored comments if present
              const isBusinessComment = comment.authorRole === UserRole.BUSINESS && !!comment.businessName;
              const isOwnComment = comment.userId === currentUser?.id;
              const displayName = isBusinessComment ? comment.businessName : comment.username;
              const avatarAlt = displayName;
              return (
                <div key={comment.id} className="flex items-start">
                  <Avatar src={comment.profilePicture || ''} alt={avatarAlt} size="sm" className="mt-1" />
                  <div className="ml-3 flex-1">
                    <div className={`rounded-lg px-3 py-2 text-sm ${
                      isBusinessComment
                        ? 'bg-green-50 border border-green-200'
                        : isOwnComment
                          ? 'bg-purple-50 border border-purple-200'
                          : comment.hasUsedProduct
                            ? 'bg-blue-50 border border-blue-100'
                            : 'bg-gray-50 border border-gray-100'
                    }`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-gray-900">{displayName}</span>

                          {/* Badge de statut utilisateur pour les commentaires - Affichage conditionnel */}
                          {comment.authorStatus && !isBusinessComment ? (
                            /* Pour les utilisateurs normaux, afficher le UserStatusBadge */
                            <UserStatusBadge
                              status={comment.authorStatus}
                              size="xs"
                              variant="default"
                            />
                          ) : null}

                          {isBusinessComment && (
                            <span className="text-xs font-medium text-green-700 bg-green-100 px-2 py-0.5 rounded-full flex items-center gap-1">
                              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                              </svg>
                              Entreprise
                            </span>
                          )}
                          {isOwnComment && !isBusinessComment && (
                            <span className="text-xs font-medium text-purple-700 bg-purple-100 px-2 py-0.5 rounded-full">
                              Vous
                            </span>
                          )}
                        </div>
                        {comment.hasUsedProduct && !isBusinessComment && (
                          <span className="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-0.5 rounded-full">
                            Avis vérifié
                          </span>
                        )}
                      </div>
                      {comment.hasUsedProduct && typeof comment.rating === 'number' && comment.rating > 0 && (
                        <div className="flex mt-1 mb-1">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <Star key={i} size={14} className={i < comment.rating! ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'} />
                          ))}
                        </div>
                      )}
                      {isBusinessComment && (
                        <div className="text-xs text-green-600 font-medium mb-1">
                          💼 Réponse officielle de l'entreprise
                        </div>
                      )}
                      <p className="text-gray-700 whitespace-pre-wrap">{comment.content}</p>
                    </div>
                    <div className="flex items-center mt-1 text-xs text-gray-500 space-x-2">
                      <button className="font-medium hover:underline">J'aime</button>
                      <span>•</span>
                      <button className="font-medium hover:underline">Répondre</button>
                      <span>•</span>
                      <span>{formatDate(comment.createdAt)}</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
          {post.comments.length > 1 && !showAllComments && (
            <Button
              variant="link"
              size="sm"
              onClick={() => setShowAllComments(true)}
              className="mt-2 text-blue-600 hover:text-blue-800 px-0"
            >
              Voir les {post.comments.length -1} autres commentaires
            </Button>
          )}
          {post.comments.length > 1 && showAllComments && (
             <Button
              variant="link"
              size="sm"
              onClick={() => setShowAllComments(false)}
              className="mt-2 text-blue-600 hover:text-blue-800 px-0"
            >
              Masquer les commentaires
            </Button>
          )}
        </div>
      )}

      {/* Modal d'édition */}
      <EditPostModal
        post={post}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
      />

      {/* Modal de partage */}
      <ShareModal
        post={post}
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
        onShareComplete={handleShareComplete}
      />
    </Card>
  );
};

export default PostCard;
