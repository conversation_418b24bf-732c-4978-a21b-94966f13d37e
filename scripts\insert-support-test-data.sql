-- Script pour insérer des données de test dans le système de support
-- Exécutez ce script dans l'éditeur SQL de Supabase après avoir appliqué les migrations

-- =====================================================
-- 1. INSÉRER DES TICKETS DE SUPPORT DE TEST
-- =====================================================

-- Récupérer l'ID d'un utilisateur existant (remplacez par un vrai ID)
-- Vous pouvez obtenir un ID utilisateur avec : SELECT id FROM profiles LIMIT 1;

-- Ticket 1 : Problème technique
INSERT INTO support_tickets (
  user_id,
  subject,
  description,
  category,
  priority,
  status
) VALUES (
  (SELECT id FROM profiles WHERE role = 'user' LIMIT 1),
  'Problème de connexion à mon compte',
  'Je n''arrive pas à me connecter depuis ce matin. Le message d''erreur indique "Identifiants incorrects" mais je suis sûr de mon mot de passe. J''ai essayé plusieurs fois sans succès.',
  'technical',
  'high',
  'in_progress'
);

-- Ticket 2 : Question sur les abonnements
INSERT INTO support_tickets (
  user_id,
  subject,
  description,
  category,
  priority,
  status
) VALUES (
  (SELECT id FROM profiles WHERE role = 'user' LIMIT 1),
  'Question sur les différents plans d''abonnement',
  'Quelles sont les différences entre les plans Business et Enterprise ? J''aimerais comprendre les fonctionnalités incluses dans chaque plan.',
  'billing',
  'normal',
  'resolved'
);

-- Ticket 3 : Demande de fonctionnalité
INSERT INTO support_tickets (
  user_id,
  subject,
  description,
  category,
  priority,
  status
) VALUES (
  (SELECT id FROM profiles WHERE role = 'business' LIMIT 1),
  'Suggestion d''amélioration pour l''IA',
  'Il serait intéressant d''avoir plus d''options de personnalisation pour les recommandations de l''IA. Pourriez-vous ajouter des filtres par secteur d''activité ?',
  'feature',
  'low',
  'open'
);

-- =====================================================
-- 2. AJOUTER DES MESSAGES AUX TICKETS
-- =====================================================

-- Messages pour le ticket 1
INSERT INTO ticket_messages (
  ticket_id,
  sender_id,
  content,
  message_type
) VALUES 
(
  (SELECT id FROM support_tickets WHERE subject LIKE '%connexion%' LIMIT 1),
  (SELECT user_id FROM support_tickets WHERE subject LIKE '%connexion%' LIMIT 1),
  'Je n''arrive pas à me connecter depuis ce matin. Le message d''erreur indique "Identifiants incorrects" mais je suis sûr de mon mot de passe.',
  'reply'
),
(
  (SELECT id FROM support_tickets WHERE subject LIKE '%connexion%' LIMIT 1),
  (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1),
  'Bonjour, merci pour votre message. Nous allons vérifier votre compte. Pouvez-vous essayer de réinitialiser votre mot de passe via le lien "Mot de passe oublié" ?',
  'reply'
),
(
  (SELECT id FROM support_tickets WHERE subject LIKE '%connexion%' LIMIT 1),
  (SELECT user_id FROM support_tickets WHERE subject LIKE '%connexion%' LIMIT 1),
  'J''ai essayé de réinitialiser mon mot de passe mais je ne reçois pas l''email de réinitialisation.',
  'reply'
);

-- Messages pour le ticket 2
INSERT INTO ticket_messages (
  ticket_id,
  sender_id,
  content,
  message_type
) VALUES 
(
  (SELECT id FROM support_tickets WHERE subject LIKE '%abonnement%' LIMIT 1),
  (SELECT user_id FROM support_tickets WHERE subject LIKE '%abonnement%' LIMIT 1),
  'Quelles sont les différences entre les plans Business et Enterprise ?',
  'reply'
),
(
  (SELECT id FROM support_tickets WHERE subject LIKE '%abonnement%' LIMIT 1),
  (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1),
  'Le plan Business inclut toutes les fonctionnalités de base plus l''IA de recommandation. Le plan Enterprise ajoute l''analyse avancée, le support prioritaire et l''API personnalisée. Voulez-vous que je vous envoie une comparaison détaillée ?',
  'reply'
),
(
  (SELECT id FROM support_tickets WHERE subject LIKE '%abonnement%' LIMIT 1),
  (SELECT user_id FROM support_tickets WHERE subject LIKE '%abonnement%' LIMIT 1),
  'Parfait, merci pour ces informations ! Cela répond à ma question.',
  'reply'
);

-- =====================================================
-- 3. CRÉER DES POSTS COMMUNAUTAIRES DE TEST
-- =====================================================

-- Post 1 : Conseils
INSERT INTO community_posts (
  author_id,
  title,
  content,
  category,
  tags,
  votes_up,
  votes_down,
  views_count,
  replies_count,
  is_solved
) VALUES (
  (SELECT id FROM profiles WHERE role = 'user' LIMIT 1),
  'Comment optimiser ses avis pour plus de visibilité ?',
  'Bonjour à tous ! Je cherche des conseils pour rendre mes avis plus utiles et visibles dans la communauté. Quelles sont vos meilleures pratiques pour écrire des avis qui aident vraiment les autres utilisateurs ?',
  'conseils',
  '["avis", "visibilité", "conseils", "communauté"]',
  15,
  2,
  234,
  8,
  true
);

-- Post 2 : Bug report
INSERT INTO community_posts (
  author_id,
  title,
  content,
  category,
  tags,
  votes_up,
  votes_down,
  views_count,
  replies_count,
  is_solved
) VALUES (
  (SELECT id FROM profiles WHERE role = 'business' LIMIT 1),
  'Bug dans l''IA de recommandation',
  'J''ai remarqué que l''IA me recommande toujours les mêmes produits, même après avoir mis à jour mes préférences. Est-ce que d''autres utilisateurs ont rencontré ce problème ?',
  'bugs',
  '["ia", "bug", "recommandations", "preferences"]',
  7,
  1,
  89,
  3,
  false
);

-- Post 3 : Question générale
INSERT INTO community_posts (
  author_id,
  title,
  content,
  category,
  tags,
  votes_up,
  votes_down,
  views_count,
  replies_count,
  is_solved
) VALUES (
  (SELECT id FROM profiles WHERE role = 'user' OFFSET 1 LIMIT 1),
  'Comment fonctionne le système de notation ?',
  'Je suis nouveau sur la plateforme et j''aimerais comprendre comment fonctionne exactement le système de notation des produits. Les notes sont-elles pondérées d''une manière particulière ?',
  'questions',
  '["notation", "systeme", "produits", "debutant"]',
  12,
  0,
  156,
  5,
  true
);

-- =====================================================
-- 4. AJOUTER DES RÉPONSES AUX POSTS COMMUNAUTAIRES
-- =====================================================

-- Réponses au post sur l'optimisation des avis
INSERT INTO community_replies (
  post_id,
  author_id,
  content,
  votes_up,
  votes_down,
  is_solution
) VALUES 
(
  (SELECT id FROM community_posts WHERE title LIKE '%optimiser%' LIMIT 1),
  (SELECT id FROM profiles WHERE role = 'business' LIMIT 1),
  'Excellente question ! Voici mes conseils : 1) Soyez spécifique dans vos descriptions, 2) Ajoutez des photos si possible, 3) Mentionnez le contexte d''utilisation, 4) Soyez honnête sur les points positifs ET négatifs.',
  8,
  0,
  true
),
(
  (SELECT id FROM community_posts WHERE title LIKE '%optimiser%' LIMIT 1),
  (SELECT id FROM profiles WHERE role = 'user' OFFSET 2 LIMIT 1),
  'Je suis d''accord avec les conseils précédents. J''ajouterais aussi : utilisez des mots-clés pertinents et répondez aux questions que vous vous posiez avant d''acheter le produit.',
  5,
  0,
  false
);

-- Réponses au post sur le bug IA
INSERT INTO community_replies (
  post_id,
  author_id,
  content,
  votes_up,
  votes_down
) VALUES 
(
  (SELECT id FROM community_posts WHERE title LIKE '%Bug dans%' LIMIT 1),
  (SELECT id FROM profiles WHERE role = 'user' LIMIT 1),
  'J''ai eu le même problème ! J''ai résolu en me déconnectant complètement puis en me reconnectant. Ça a forcé l''IA à recharger mes préférences.',
  3,
  0
),
(
  (SELECT id FROM community_posts WHERE title LIKE '%Bug dans%' LIMIT 1),
  (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1),
  'Merci pour ce signalement. Nous avons identifié le problème et un correctif sera déployé dans la prochaine mise à jour. En attendant, la solution de déconnexion/reconnexion devrait fonctionner.',
  4,
  0
);

-- =====================================================
-- 5. AJOUTER DES VOTES COMMUNAUTAIRES
-- =====================================================

-- Votes pour les posts
INSERT INTO community_votes (user_id, post_id, vote_type) VALUES
((SELECT id FROM profiles WHERE role = 'user' LIMIT 1), (SELECT id FROM community_posts LIMIT 1), 'up'),
((SELECT id FROM profiles WHERE role = 'business' LIMIT 1), (SELECT id FROM community_posts LIMIT 1), 'up'),
((SELECT id FROM profiles WHERE role = 'user' OFFSET 1 LIMIT 1), (SELECT id FROM community_posts OFFSET 1 LIMIT 1), 'up');

-- =====================================================
-- 6. AJOUTER DES ÉVALUATIONS FAQ
-- =====================================================

-- Évaluations pour les FAQ
INSERT INTO faq_ratings (user_id, faq_question, is_helpful, feedback) VALUES
((SELECT id FROM profiles WHERE role = 'user' LIMIT 1), 'Comment puis-je modifier mon profil ?', true, 'Très clair et utile'),
((SELECT id FROM profiles WHERE role = 'business' LIMIT 1), 'Comment puis-je modifier mon profil ?', true, NULL),
((SELECT id FROM profiles WHERE role = 'user' OFFSET 1 LIMIT 1), 'Comment puis-je poster un avis ?', true, 'Instructions parfaites'),
((SELECT id FROM profiles WHERE role = 'user' LIMIT 1), 'Que faire si j''oublie mon mot de passe ?', true, 'Procédure simple à suivre');

-- =====================================================
-- VÉRIFICATION DES DONNÉES INSÉRÉES
-- =====================================================

-- Vérifier les tickets créés
SELECT 
  id,
  subject,
  category,
  priority,
  status,
  created_at
FROM support_tickets
ORDER BY created_at DESC;

-- Vérifier les posts communautaires
SELECT 
  id,
  title,
  category,
  votes_up,
  votes_down,
  views_count,
  replies_count,
  is_solved,
  created_at
FROM community_posts
ORDER BY created_at DESC;

-- Vérifier les messages de tickets
SELECT 
  tm.id,
  tm.ticket_id,
  p.username as sender,
  tm.content,
  tm.created_at
FROM ticket_messages tm
JOIN profiles p ON tm.sender_id = p.id
ORDER BY tm.created_at DESC;

-- Vérifier les réponses communautaires
SELECT 
  cr.id,
  cp.title as post_title,
  p.username as author,
  cr.content,
  cr.votes_up,
  cr.is_solution,
  cr.created_at
FROM community_replies cr
JOIN community_posts cp ON cr.post_id = cp.id
JOIN profiles p ON cr.author_id = p.id
ORDER BY cr.created_at DESC;
