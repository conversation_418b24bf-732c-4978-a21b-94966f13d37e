// Système de rotation et priorisation des publicités

export interface AdCampaign {
  id: string;
  businessId: string;
  title: string;
  description: string;
  imageUrl: string;
  targetUrl: string;
  businessName: string;
  businessLogo: string;
  
  // Paramètres d'enchères et budget
  bidAmount: number; // Enchère par clic en F CFA
  dailyBudget: number;
  totalBudget: number;
  spent: number;
  
  // Ciblage
  placements: string[]; // ['newsfeed', 'sidebar', 'offers']
  targeting: {
    demographics: {
      ageRanges: string[];
      genders: string[];
    };
    location: {
      cities: string[];
    };
    devices: string[];
  };
  
  // Statut et performance
  status: 'active' | 'paused' | 'completed' | 'draft';
  startDate: Date;
  endDate: Date;
  
  // Métriques de performance
  impressions: number;
  clicks: number;
  ctr: number; // Click-through rate
  qualityScore: number; // Score de qualité (1-10)
  
  // Métadonnées
  createdAt: Date;
  updatedAt: Date;
}

export interface AdSlotConfig {
  id: string;
  name: string;
  maxAdsPerHour: number;
  rotationIntervalMinutes: number;
  priorityWeight: number; // Poids pour le calcul de priorité
}

export interface UserContext {
  id: string;
  age?: number;
  gender?: string;
  city?: string;
  device: 'mobile' | 'desktop' | 'tablet';
  interests: string[];
  recentActivity: string[];
}

// Configuration des emplacements publicitaires
export const AD_SLOT_CONFIGS: Record<string, AdSlotConfig> = {
  newsfeed: {
    id: 'newsfeed',
    name: 'Fil d\'actualité',
    maxAdsPerHour: 12,
    rotationIntervalMinutes: 5,
    priorityWeight: 1.0
  },
  sidebar: {
    id: 'sidebar',
    name: 'Barre latérale',
    maxAdsPerHour: 6,
    rotationIntervalMinutes: 10,
    priorityWeight: 0.7
  },
  offers: {
    id: 'offers',
    name: 'Offres et promotions',
    maxAdsPerHour: 24,
    rotationIntervalMinutes: 2.5,
    priorityWeight: 0.8
  },
  marketplace: {
    id: 'marketplace',
    name: 'Marketplace',
    maxAdsPerHour: 20,
    rotationIntervalMinutes: 3,
    priorityWeight: 1.2
  },
  search: {
    id: 'search',
    name: 'Résultats de recherche',
    maxAdsPerHour: 30,
    rotationIntervalMinutes: 2,
    priorityWeight: 1.5
  }
};

/**
 * Calcule le score de priorité d'une publicité
 */
export function calculateAdPriority(
  campaign: AdCampaign,
  placement: string,
  userContext: UserContext
): number {
  let score = 0;

  // 1. Enchère (40% du score)
  const bidWeight = 0.4;
  const normalizedBid = Math.min(campaign.bidAmount / 1000, 1); // Normaliser sur 1000 F CFA max
  score += normalizedBid * bidWeight;

  // 2. Score de qualité (30% du score)
  const qualityWeight = 0.3;
  score += (campaign.qualityScore / 10) * qualityWeight;

  // 3. Pertinence du ciblage (20% du score)
  const relevanceWeight = 0.2;
  const relevanceScore = calculateTargetingRelevance(campaign, userContext);
  score += relevanceScore * relevanceWeight;

  // 4. Performance historique (10% du score)
  const performanceWeight = 0.1;
  const performanceScore = Math.min(campaign.ctr / 5, 1); // CTR normalisé sur 5%
  score += performanceScore * performanceWeight;

  // 5. Bonus pour les nouveaux annonceurs
  const daysSinceCreation = (Date.now() - campaign.createdAt.getTime()) / (1000 * 60 * 60 * 24);
  if (daysSinceCreation < 7) {
    score *= 1.1; // Bonus de 10% pour les nouvelles campagnes
  }

  // 6. Pénalité si le budget quotidien est presque épuisé
  const dailySpentRatio = campaign.spent / campaign.dailyBudget;
  if (dailySpentRatio > 0.8) {
    score *= 0.5; // Réduire la priorité si le budget est presque épuisé
  }

  // 7. Appliquer le poids de l'emplacement
  const slotConfig = AD_SLOT_CONFIGS[placement];
  if (slotConfig) {
    score *= slotConfig.priorityWeight;
  }

  return Math.max(0, Math.min(1, score)); // Garder le score entre 0 et 1
}

/**
 * Calcule la pertinence du ciblage pour un utilisateur
 */
function calculateTargetingRelevance(campaign: AdCampaign, userContext: UserContext): number {
  let relevanceScore = 0;
  let totalCriteria = 0;

  // Vérifier l'âge
  if (campaign.targeting.demographics.ageRanges.length > 0 && userContext.age) {
    totalCriteria++;
    const userAgeRange = getAgeRange(userContext.age);
    if (campaign.targeting.demographics.ageRanges.includes(userAgeRange)) {
      relevanceScore++;
    }
  }

  // Vérifier le genre
  if (campaign.targeting.demographics.genders.length > 0 && userContext.gender) {
    totalCriteria++;
    if (campaign.targeting.demographics.genders.includes(userContext.gender) || 
        campaign.targeting.demographics.genders.includes('all')) {
      relevanceScore++;
    }
  }

  // Vérifier la localisation
  if (campaign.targeting.location.cities.length > 0 && userContext.city) {
    totalCriteria++;
    if (campaign.targeting.location.cities.includes(userContext.city)) {
      relevanceScore++;
    }
  }

  // Vérifier l'appareil
  if (campaign.targeting.devices.length > 0) {
    totalCriteria++;
    if (campaign.targeting.devices.includes(userContext.device)) {
      relevanceScore++;
    }
  }

  return totalCriteria > 0 ? relevanceScore / totalCriteria : 1;
}

/**
 * Détermine la tranche d'âge d'un utilisateur
 */
function getAgeRange(age: number): string {
  if (age >= 18 && age <= 24) return '18-24';
  if (age >= 25 && age <= 34) return '25-34';
  if (age >= 35 && age <= 44) return '35-44';
  if (age >= 45 && age <= 54) return '45-54';
  if (age >= 55 && age <= 64) return '55-64';
  if (age >= 65) return '65+';
  return '18-24'; // Par défaut
}

/**
 * Sélectionne les publicités à afficher pour un emplacement donné
 */
export function selectAdsForPlacement(
  campaigns: AdCampaign[],
  placement: string,
  userContext: UserContext,
  maxAds: number = 1
): AdCampaign[] {
  // Filtrer les campagnes actives pour cet emplacement
  const eligibleCampaigns = campaigns.filter(campaign => 
    campaign.status === 'active' &&
    campaign.placements.includes(placement) &&
    campaign.spent < campaign.dailyBudget &&
    new Date() >= campaign.startDate &&
    new Date() <= campaign.endDate
  );

  // Calculer les scores de priorité
  const campaignsWithScores = eligibleCampaigns.map(campaign => ({
    campaign,
    priority: calculateAdPriority(campaign, placement, userContext)
  }));

  // Trier par score de priorité (décroissant)
  campaignsWithScores.sort((a, b) => b.priority - a.priority);

  // Sélectionner les meilleures campagnes
  return campaignsWithScores
    .slice(0, maxAds)
    .map(item => item.campaign);
}

/**
 * Système de rotation équitable avec pondération
 */
export function getRotatedAds(
  campaigns: AdCampaign[],
  placement: string,
  userContext: UserContext,
  rotationIndex: number = 0
): AdCampaign[] {
  const slotConfig = AD_SLOT_CONFIGS[placement];
  if (!slotConfig) return [];

  const eligibleCampaigns = campaigns.filter(campaign => 
    campaign.status === 'active' &&
    campaign.placements.includes(placement) &&
    campaign.spent < campaign.dailyBudget
  );

  if (eligibleCampaigns.length === 0) return [];

  // Calculer les poids pour la rotation
  const campaignsWithWeights = eligibleCampaigns.map(campaign => ({
    campaign,
    weight: calculateAdPriority(campaign, placement, userContext)
  }));

  // Rotation pondérée
  const totalWeight = campaignsWithWeights.reduce((sum, item) => sum + item.weight, 0);
  const normalizedWeights = campaignsWithWeights.map(item => ({
    ...item,
    normalizedWeight: item.weight / totalWeight
  }));

  // Sélection basée sur la rotation et les poids
  const selectedCampaigns: AdCampaign[] = [];
  const adsToShow = Math.min(slotConfig.maxAdsPerHour / 12, eligibleCampaigns.length); // Nombre d'ads par rotation

  for (let i = 0; i < adsToShow; i++) {
    const index = (rotationIndex + i) % normalizedWeights.length;
    selectedCampaigns.push(normalizedWeights[index].campaign);
  }

  return selectedCampaigns;
}

/**
 * Enregistre une impression publicitaire
 */
export function recordAdImpression(campaignId: string): void {
  // Cette fonction sera connectée à votre base de données
  console.log(`Impression enregistrée pour la campagne ${campaignId}`);
  
  // TODO: Implémenter l'enregistrement en base de données
  // - Incrémenter le compteur d'impressions
  // - Mettre à jour les métriques de performance
  // - Vérifier les limites de budget
}

/**
 * Enregistre un clic publicitaire
 */
export function recordAdClick(campaignId: string, cost: number): void {
  // Cette fonction sera connectée à votre base de données
  console.log(`Clic enregistré pour la campagne ${campaignId}, coût: ${cost} F CFA`);
  
  // TODO: Implémenter l'enregistrement en base de données
  // - Incrémenter le compteur de clics
  // - Déduire le coût du budget
  // - Mettre à jour le CTR
  // - Vérifier si la campagne doit être mise en pause
}
