import React, { useState } from 'react';
import { Heart, Share2, Eye, RefreshCw, Database } from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import { adInteractionService } from '../../services/adInteractionService';
import { useAuth } from '../../context/AuthContext';

interface OffersInteractionDebugProps {
  offerId: string;
  offerTitle: string;
}

const OffersInteractionDebug: React.FC<OffersInteractionDebugProps> = ({ offerId, offerTitle }) => {
  const { currentUser } = useAuth();
  const [debugData, setDebugData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const runDiagnostic = async () => {
    if (!currentUser) {
      setDebugData({ error: 'Utilisateur non connecté' });
      return;
    }

    setLoading(true);
    const results: any = {
      offerId,
      offerTitle,
      userId: currentUser.id,
      timestamp: new Date().toISOString(),
      tests: {}
    };

    try {
      // Test 1: Vérifier si l'utilisateur a liké
      console.log('🔍 Test 1: Vérification du like...');
      const hasLiked = await adInteractionService.hasUserLikedAd(offerId, currentUser.id);
      results.tests.hasLiked = { success: true, result: hasLiked };

      // Test 2: Récupérer les métriques
      console.log('🔍 Test 2: Récupération des métriques...');
      const metrics = await adInteractionService.getEngagementMetrics(offerId);
      results.tests.metrics = { success: true, result: metrics };

      // Test 3: Tenter un like
      console.log('🔍 Test 3: Test de like...');
      const likeResult = await adInteractionService.likeAd(offerId, currentUser.id);
      results.tests.like = { success: true, result: likeResult };

      // Test 4: Tenter un unlike
      console.log('🔍 Test 4: Test de unlike...');
      const unlikeResult = await adInteractionService.unlikeAd(offerId, currentUser.id);
      results.tests.unlike = { success: true, result: unlikeResult };

      // Test 5: Test de partage
      console.log('🔍 Test 5: Test de partage...');
      const shareResult = await adInteractionService.shareAd(offerId, currentUser.id, 'copy_link');
      results.tests.share = { success: true, result: shareResult };

      // Test 6: Métriques finales
      console.log('🔍 Test 6: Métriques finales...');
      const finalMetrics = await adInteractionService.getEngagementMetrics(offerId);
      results.tests.finalMetrics = { success: true, result: finalMetrics };

    } catch (error) {
      console.error('❌ Erreur lors du diagnostic:', error);
      results.error = error;
    }

    setDebugData(results);
    setLoading(false);
  };

  const resetData = () => {
    setDebugData(null);
  };

  return (
    <Card className="border-2 border-yellow-300 bg-yellow-50">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Database className="text-yellow-600" size={20} />
            <h3 className="font-semibold text-yellow-800">Debug Interactions Offres</h3>
          </div>
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={runDiagnostic}
              disabled={loading}
              leftIcon={loading ? <RefreshCw className="animate-spin" size={16} /> : <Eye size={16} />}
            >
              {loading ? 'Test...' : 'Tester'}
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={resetData}
              leftIcon={<RefreshCw size={16} />}
            >
              Reset
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardBody>
        <div className="space-y-4">
          <div className="text-sm">
            <p><strong>Offre ID:</strong> {offerId}</p>
            <p><strong>Titre:</strong> {offerTitle}</p>
            <p><strong>Utilisateur:</strong> {currentUser?.id || 'Non connecté'}</p>
          </div>

          {debugData && (
            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-semibold mb-3 text-gray-800">Résultats des Tests</h4>
              
              {debugData.error ? (
                <div className="text-red-600 font-medium">
                  ❌ Erreur: {debugData.error.toString()}
                </div>
              ) : (
                <div className="space-y-3">
                  {Object.entries(debugData.tests).map(([testName, testResult]: [string, any]) => (
                    <div key={testName} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="font-medium text-gray-700">
                        {testName === 'hasLiked' && '👍 A liké'}
                        {testName === 'metrics' && '📊 Métriques'}
                        {testName === 'like' && '❤️ Like'}
                        {testName === 'unlike' && '💔 Unlike'}
                        {testName === 'share' && '📤 Partage'}
                        {testName === 'finalMetrics' && '📈 Métriques finales'}
                      </span>
                      <div className="text-right">
                        {testResult.success ? (
                          <span className="text-green-600 font-medium">
                            ✅ {JSON.stringify(testResult.result)}
                          </span>
                        ) : (
                          <span className="text-red-600 font-medium">
                            ❌ Échec
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          <div className="text-xs text-yellow-700 bg-yellow-100 p-3 rounded">
            <p><strong>💡 Ce composant de debug :</strong></p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>Teste toutes les fonctions d'interaction</li>
              <li>Vérifie la connexion à la base de données</li>
              <li>Affiche les compteurs en temps réel</li>
              <li>Diagnostique les problèmes de compteurs</li>
            </ul>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default OffersInteractionDebug;
