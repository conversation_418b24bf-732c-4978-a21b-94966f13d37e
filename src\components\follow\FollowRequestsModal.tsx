import React, { useState } from 'react';
import { X, Check, UserPlus, Clock, MessageCircle } from 'lucide-react';
import Modal from '../ui/Modal';
import Button from '../ui/Button';
import Avatar from '../ui/Avatar';
import UserStatusBadge from '../ui/UserStatusBadge';
import UserRoleBadge from '../ui/UserRoleBadge';
import { useFollowRequests } from '../../context/FollowRequestContext';
import { IFollowRequest } from '../../types';
import { DEFAULT_IMAGES } from '../../constants/defaultImages';

interface FollowRequestsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const FollowRequestsModal: React.FC<FollowRequestsModalProps> = ({
  isOpen,
  onClose
}) => {
  const { receivedRequests, respondToRequest, loading } = useFollowRequests();
  const [processingRequests, setProcessingRequests] = useState<Set<string>>(new Set());

  const handleResponse = async (requestId: string, response: 'accepted' | 'rejected') => {
    setProcessingRequests(prev => new Set(prev).add(requestId));
    
    try {
      const result = await respondToRequest(requestId, response);
      if (!result.success) {
        console.error('Erreur lors de la réponse:', result.error);
      }
    } catch (error) {
      console.error('Erreur lors de la réponse à la demande:', error);
    } finally {
      setProcessingRequests(prev => {
        const newSet = new Set(prev);
        newSet.delete(requestId);
        return newSet;
      });
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString('fr-FR', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getAvatarSrc = (request: IFollowRequest) => {
    if (!request.requesterProfile) return DEFAULT_IMAGES.AVATAR;
    
    if (request.requesterProfile.role === 'business') {
      return request.requesterProfile.profilePicture || DEFAULT_IMAGES.BUSINESS;
    }
    return request.requesterProfile.profilePicture || DEFAULT_IMAGES.AVATAR;
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Demandes de suivi">
      <div className="max-w-md mx-auto">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Chargement...</span>
          </div>
        ) : receivedRequests.length === 0 ? (
          <div className="text-center py-8">
            <UserPlus size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucune demande de suivi
            </h3>
            <p className="text-gray-500">
              Vous n'avez pas de nouvelles demandes de suivi en attente.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                {receivedRequests.length} demande{receivedRequests.length > 1 ? 's' : ''} en attente
              </h3>
            </div>

            <div className="space-y-3 max-h-96 overflow-y-auto">
              {receivedRequests.map((request) => (
                <div
                  key={request.id}
                  className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start space-x-3">
                    {/* Avatar */}
                    <Avatar
                      src={getAvatarSrc(request)}
                      alt={request.requesterProfile?.username || 'Utilisateur'}
                      size="md"
                      className="flex-shrink-0"
                    />

                    {/* Contenu */}
                    <div className="flex-1 min-w-0">
                      {/* En-tête */}
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-semibold text-gray-900 truncate">
                          {request.requesterProfile?.businessName || request.requesterProfile?.username || 'Utilisateur inconnu'}
                        </h4>
                        
                        {/* Badges */}
                        <div className="flex items-center space-x-1">
                          {request.requesterProfile?.status && (
                            <UserStatusBadge
                              status={request.requesterProfile.status}
                              size="xs"
                              variant="default"
                            />
                          )}
                          {request.requesterProfile?.role && (
                            <UserRoleBadge
                              role={request.requesterProfile.role}
                              showIcon={false}
                            />
                          )}
                        </div>
                      </div>

                      {/* Message */}
                      {request.message && (
                        <div className="bg-gray-50 rounded-md p-2 mb-2">
                          <div className="flex items-start space-x-1">
                            <MessageCircle size={14} className="text-gray-400 mt-0.5 flex-shrink-0" />
                            <p className="text-sm text-gray-700 italic">
                              "{request.message}"
                            </p>
                          </div>
                        </div>
                      )}

                      {/* Date */}
                      <div className="flex items-center space-x-1 text-xs text-gray-500 mb-3">
                        <Clock size={12} />
                        <span>{formatDate(request.createdAt)}</span>
                      </div>

                      {/* Actions */}
                      <div className="flex space-x-2">
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={() => handleResponse(request.id, 'accepted')}
                          disabled={processingRequests.has(request.id)}
                          leftIcon={<Check size={14} />}
                          className="flex-1"
                        >
                          {processingRequests.has(request.id) ? 'Traitement...' : 'Accepter'}
                        </Button>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleResponse(request.id, 'rejected')}
                          disabled={processingRequests.has(request.id)}
                          leftIcon={<X size={14} />}
                          className="flex-1"
                        >
                          {processingRequests.has(request.id) ? 'Traitement...' : 'Refuser'}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={onClose}
            className="w-full"
          >
            Fermer
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default FollowRequestsModal;
