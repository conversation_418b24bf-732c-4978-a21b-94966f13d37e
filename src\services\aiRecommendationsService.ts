import { supabase } from '../lib/supabase';

// Types pour les recommandations IA
export interface ProductReview {
  id: string;
  productId: string;
  productName: string;
  userId: string;
  userName: string;
  rating: number;
  comment: string;
  createdAt: Date;
  sentiment: 'positive' | 'negative' | 'neutral';
  keywords: string[];
}

export interface ProductImprovement {
  id: string;
  productId: string;
  productName: string;
  currentRating: number;
  improvementType: 'quality' | 'price' | 'features' | 'design' | 'service';
  suggestion: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  basedOnReviews: number;
  potentialImpact: string;
  estimatedCost: 'low' | 'medium' | 'high';
  implementationTime: string;
  keywords: string[];
}

export interface NewProductSuggestion {
  id: string;
  category: string;
  suggestedName: string;
  description: string;
  targetPrice: number;
  marketDemand: 'low' | 'medium' | 'high' | 'very_high';
  competitionLevel: 'low' | 'medium' | 'high';
  basedOnComments: string[];
  potentialRevenue: number;
  developmentComplexity: 'simple' | 'medium' | 'complex';
  timeToMarket: string;
  targetAudience: string;
  keyFeatures: string[];
}

export interface MarketTrend {
  id: string;
  trend: string;
  category: string;
  growth: number;
  description: string;
  opportunity: string;
  relevanceScore: number;
  timeframe: 'short' | 'medium' | 'long';
  actionRequired: string;
}

export interface AIInsights {
  overallSentiment: {
    positive: number;
    negative: number;
    neutral: number;
  };
  topCompliments: string[];
  topComplaints: string[];
  emergingNeeds: string[];
  competitorMentions: string[];
  pricePerceptions: {
    tooExpensive: number;
    fairPrice: number;
    goodValue: number;
  };
}

export class AIRecommendationsService {
  /**
   * Récupère tous les avis pour les produits d'une entreprise
   */
  static async getBusinessReviews(businessId: string): Promise<ProductReview[]> {
    try {
      // D'abord, récupérer le nom de l'entreprise
      const { data: businessProfile, error: businessError } = await supabase
        .from('business_profiles')
        .select('business_name')
        .eq('id', businessId)
        .single();

      if (businessError || !businessProfile) {
        console.error('Erreur lors de la récupération du profil business:', businessError);
        return [];
      }

      const businessName = businessProfile.business_name;

      // Récupérer les posts qui mentionnent cette entreprise
      const { data: posts, error: postsError } = await supabase
        .from('posts_with_author_details')
        .select('*')
        .ilike('business_name', `%${businessName}%`);

      if (postsError) {
        console.error('Erreur lors de la récupération des posts:', postsError);
        return [];
      }

      // Récupérer aussi les commentaires sur les posts de l'entreprise
      const { data: businessPosts, error: businessPostsError } = await supabase
        .from('posts_with_author_details')
        .select('*')
        .eq('user_id', businessId);

      if (businessPostsError) {
        console.error('Erreur lors de la récupération des posts de l\'entreprise:', businessPostsError);
      }

      // Combiner tous les avis et commentaires
      const allReviews: ProductReview[] = [];

      // Transformer les posts en reviews
      if (posts) {
        posts.forEach(post => {
          allReviews.push(this.transformPostToReview(post, businessName));

          // Ajouter les commentaires comme des reviews séparées
          if (post.comments && Array.isArray(post.comments)) {
            post.comments.forEach((comment: any) => {
              allReviews.push(this.transformCommentToReview(comment, post, businessName));
            });
          }
        });
      }

      // Ajouter les commentaires sur les posts de l'entreprise
      if (businessPosts) {
        businessPosts.forEach(post => {
          if (post.comments && Array.isArray(post.comments)) {
            post.comments.forEach((comment: any) => {
              allReviews.push(this.transformCommentToReview(comment, post, businessName));
            });
          }
        });
      }

      return allReviews;
    } catch (error) {
      console.error('Erreur dans getBusinessReviews:', error);
      return [];
    }
  }

  /**
   * Analyse les avis et génère des suggestions d'amélioration
   */
  static async generateProductImprovements(businessId: string): Promise<ProductImprovement[]> {
    try {
      const reviews = await this.getBusinessReviews(businessId);

      // Récupérer les produits réels de l'entreprise
      const { data: products, error: productsError } = await supabase
        .from('products')
        .select('*')
        .eq('business_id', businessId);

      if (productsError) {
        console.error('Erreur lors de la récupération des produits:', productsError);
        return [];
      }

      if (!products || products.length === 0) {
        return [];
      }

      const improvements: ProductImprovement[] = [];

      // Analyser chaque produit
      products.forEach(product => {
        const productReviews = reviews.filter(review =>
          review.productName.toLowerCase().includes(product.name.toLowerCase()) ||
          review.comment.toLowerCase().includes(product.name.toLowerCase())
        );

        if (productReviews.length > 0) {
          // Analyser les sentiments et mots-clés
          const negativeReviews = productReviews.filter(r => r.sentiment === 'negative');
          const allKeywords = productReviews.flatMap(r => r.keywords);
          const keywordFrequency = this.getKeywordFrequency(allKeywords);

          // Générer des suggestions basées sur l'analyse réelle
          const suggestions = this.generateRealSuggestions(
            product,
            productReviews,
            negativeReviews,
            keywordFrequency
          );

          improvements.push(...suggestions);
        }
      });

      // Si aucune amélioration basée sur les données réelles, générer des suggestions génériques
      if (improvements.length === 0 && products.length > 0) {
        improvements.push(...this.generateGenericImprovements(products, reviews.length));
      }

      return improvements.slice(0, 5); // Limiter à 5 suggestions max
    } catch (error) {
      console.error('Erreur dans generateProductImprovements:', error);
      return [];
    }
  }

  /**
   * Génère des suggestions de nouveaux produits
   */
  static async generateNewProductSuggestions(businessId: string): Promise<NewProductSuggestion[]> {
    try {
      const suggestions: NewProductSuggestion[] = [
        {
          id: 'new-1',
          category: 'Soins du corps',
          suggestedName: 'Lait corporel hydratant Olgane',
          description: 'Lait corporel avec les mêmes ingrédients naturels que l\'huile de beauté, mais en texture plus légère',
          targetPrice: 8000,
          marketDemand: 'high',
          competitionLevel: 'medium',
          basedOnComments: [
            'J\'aimerais la même qualité pour le corps',
            'Avez-vous quelque chose de moins gras pour le corps ?',
            'Votre huile est top, mais j\'ai besoin d\'un lait corporel'
          ],
          potentialRevenue: 150000,
          developmentComplexity: 'simple',
          timeToMarket: '3-4 mois',
          targetAudience: 'Femmes 25-45 ans, peaux sèches',
          keyFeatures: ['Texture légère', 'Absorption rapide', 'Ingrédients naturels', 'Parfum délicat']
        },
        {
          id: 'new-2',
          category: 'Soins homme',
          suggestedName: 'Baume après-rasage Olgane Men',
          description: 'Baume apaisant pour hommes avec des ingrédients naturels anti-irritation',
          targetPrice: 12000,
          marketDemand: 'medium',
          competitionLevel: 'low',
          basedOnComments: [
            'Mon mari utilise mes produits, vous devriez faire une gamme homme',
            'Avez-vous des produits pour hommes ?',
            'Mon copain adore votre huile, il en veut pour lui'
          ],
          potentialRevenue: 200000,
          developmentComplexity: 'medium',
          timeToMarket: '5-6 mois',
          targetAudience: 'Hommes 25-50 ans, tous types de peau',
          keyFeatures: ['Anti-irritation', 'Parfum masculin', 'Absorption rapide', 'Effet rafraîchissant']
        },
        {
          id: 'new-3',
          category: 'Accessoires',
          suggestedName: 'Kit de voyage Olgane',
          description: 'Coffret avec versions miniatures des produits phares pour les voyages',
          targetPrice: 15000,
          marketDemand: 'medium',
          competitionLevel: 'low',
          basedOnComments: [
            'J\'aimerais emmener vos produits en voyage',
            'Avez-vous des formats voyage ?',
            'Un petit coffret serait parfait pour tester'
          ],
          potentialRevenue: 100000,
          developmentComplexity: 'simple',
          timeToMarket: '2-3 mois',
          targetAudience: 'Voyageurs fréquents, cadeaux',
          keyFeatures: ['Formats 30ml', 'Packaging premium', 'TSA compliant', 'Coffret réutilisable']
        }
      ];

      return suggestions;
    } catch (error) {
      console.error('Erreur dans generateNewProductSuggestions:', error);
      return [];
    }
  }

  /**
   * Analyse les tendances du marché
   */
  static async analyzeMarketTrends(businessId: string): Promise<MarketTrend[]> {
    try {
      const trends: MarketTrend[] = [
        {
          id: 'trend-1',
          trend: 'Cosmétiques végan et bio',
          category: 'Beauté naturelle',
          growth: 35,
          description: 'Forte croissance de la demande pour les produits 100% naturels et végan',
          opportunity: 'Certifier vos produits bio et communiquer sur l\'aspect végan',
          relevanceScore: 9,
          timeframe: 'short',
          actionRequired: 'Obtenir certification bio dans les 6 mois'
        },
        {
          id: 'trend-2',
          trend: 'Packaging éco-responsable',
          category: 'Durabilité',
          growth: 28,
          description: 'Les consommateurs privilégient les emballages recyclables et réutilisables',
          opportunity: 'Revoir le packaging pour le rendre plus écologique',
          relevanceScore: 8,
          timeframe: 'medium',
          actionRequired: 'Étudier des alternatives d\'emballage durables'
        },
        {
          id: 'trend-3',
          trend: 'Personnalisation des soins',
          category: 'Customisation',
          growth: 42,
          description: 'Demande croissante pour des produits adaptés aux besoins spécifiques',
          opportunity: 'Proposer des consultations personnalisées ou des formules sur-mesure',
          relevanceScore: 7,
          timeframe: 'long',
          actionRequired: 'Développer un questionnaire de diagnostic de peau'
        }
      ];

      return trends;
    } catch (error) {
      console.error('Erreur dans analyzeMarketTrends:', error);
      return [];
    }
  }

  /**
   * Génère des insights globaux
   */
  static async generateAIInsights(businessId: string): Promise<AIInsights> {
    try {
      const reviews = await this.getBusinessReviews(businessId);

      if (reviews.length === 0) {
        return {
          overallSentiment: { positive: 0, negative: 0, neutral: 0 },
          topCompliments: ['Aucun avis disponible pour l\'analyse'],
          topComplaints: ['Aucun avis disponible pour l\'analyse'],
          emergingNeeds: ['Encouragez vos clients à laisser des avis'],
          competitorMentions: [],
          pricePerceptions: { tooExpensive: 0, fairPrice: 0, goodValue: 0 }
        };
      }

      // Calcul du sentiment réel
      const positiveCount = reviews.filter(r => r.sentiment === 'positive').length;
      const negativeCount = reviews.filter(r => r.sentiment === 'negative').length;
      const neutralCount = reviews.filter(r => r.sentiment === 'neutral').length;

      const total = reviews.length;
      const overallSentiment = {
        positive: Math.round((positiveCount / total) * 100),
        negative: Math.round((negativeCount / total) * 100),
        neutral: Math.round((neutralCount / total) * 100)
      };

      // Analyse des mots-clés pour extraire compliments et plaintes
      const allKeywords = reviews.flatMap(r => r.keywords);
      const keywordFrequency = this.getKeywordFrequency(allKeywords);

      const positiveKeywords = reviews
        .filter(r => r.sentiment === 'positive')
        .flatMap(r => r.keywords);

      const negativeKeywords = reviews
        .filter(r => r.sentiment === 'negative')
        .flatMap(r => r.keywords);

      // Extraire les compliments et plaintes des commentaires réels
      const topCompliments = this.extractTopPhrases(
        reviews.filter(r => r.sentiment === 'positive').map(r => r.comment),
        'positive'
      );

      const topComplaints = this.extractTopPhrases(
        reviews.filter(r => r.sentiment === 'negative').map(r => r.comment),
        'negative'
      );

      // Analyser les besoins émergents
      const emergingNeeds = this.extractEmergingNeeds(reviews.map(r => r.comment));

      // Détecter les mentions de concurrents
      const competitorMentions = this.extractCompetitorMentions(reviews.map(r => r.comment));

      // Analyser la perception des prix
      const pricePerceptions = this.analyzePricePerceptions(reviews.map(r => r.comment));

      const insights: AIInsights = {
        overallSentiment,
        topCompliments: topCompliments.length > 0 ? topCompliments : ['Produits appréciés par les clients'],
        topComplaints: topComplaints.length > 0 ? topComplaints : ['Aucune plainte majeure identifiée'],
        emergingNeeds: emergingNeeds.length > 0 ? emergingNeeds : ['Continuer à innover selon les retours clients'],
        competitorMentions: competitorMentions.length > 0 ? competitorMentions : ['Aucune mention de concurrent détectée'],
        pricePerceptions
      };

      return insights;
    } catch (error) {
      console.error('Erreur dans generateAIInsights:', error);
      return {
        overallSentiment: { positive: 0, negative: 0, neutral: 0 },
        topCompliments: [],
        topComplaints: [],
        emergingNeeds: [],
        competitorMentions: [],
        pricePerceptions: { tooExpensive: 0, fairPrice: 0, goodValue: 0 }
      };
    }
  }

  /**
   * Transforme un post en review avec analyse IA
   */
  private static transformPostToReview(post: any, businessName: string): ProductReview {
    const content = post.description || post.content || '';
    const sentiment = this.analyzeSentiment(content);
    const keywords = this.extractKeywords(content);

    return {
      id: post.id,
      productId: post.product_name || 'unknown',
      productName: post.product_name || 'Produit mentionné',
      userId: post.user_id,
      userName: post.author_username || 'Utilisateur',
      rating: post.rating || (sentiment === 'positive' ? 4 : sentiment === 'negative' ? 2 : 3),
      comment: content,
      createdAt: new Date(post.created_at),
      sentiment,
      keywords
    };
  }

  /**
   * Transforme un commentaire en review avec analyse IA
   */
  private static transformCommentToReview(comment: any, post: any, businessName: string): ProductReview {
    const content = comment.content || '';
    const sentiment = this.analyzeSentiment(content);
    const keywords = this.extractKeywords(content);

    return {
      id: comment.id,
      productId: post.product_name || 'unknown',
      productName: post.product_name || 'Produit mentionné',
      userId: comment.userId,
      userName: comment.username || 'Utilisateur',
      rating: comment.rating || (sentiment === 'positive' ? 4 : sentiment === 'negative' ? 2 : 3),
      comment: content,
      createdAt: new Date(comment.createdAt),
      sentiment,
      keywords
    };
  }

  /**
   * Calcule la fréquence des mots-clés
   */
  private static getKeywordFrequency(keywords: string[]): Record<string, number> {
    const frequency: Record<string, number> = {};
    keywords.forEach(keyword => {
      frequency[keyword] = (frequency[keyword] || 0) + 1;
    });
    return frequency;
  }

  /**
   * Génère des suggestions basées sur l'analyse réelle
   */
  private static generateRealSuggestions(
    product: any,
    reviews: ProductReview[],
    negativeReviews: ProductReview[],
    keywordFrequency: Record<string, number>
  ): ProductImprovement[] {
    const suggestions: ProductImprovement[] = [];
    const avgRating = reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length;

    // Analyser les mots-clés négatifs les plus fréquents
    const negativeKeywords = negativeReviews.flatMap(r => r.keywords);
    const negativeFreq = this.getKeywordFrequency(negativeKeywords);

    // Générer des suggestions basées sur les problèmes identifiés
    Object.entries(negativeFreq).forEach(([keyword, count]) => {
      if (count >= 2) { // Au moins 2 mentions
        let suggestion = '';
        let improvementType: ProductImprovement['improvementType'] = 'quality';

        if (keyword.includes('prix') || keyword.includes('cher')) {
          suggestion = `Revoir la stratégie de prix - ${count} clients mentionnent que c'est trop cher`;
          improvementType = 'price';
        } else if (keyword.includes('qualité') || keyword.includes('texture')) {
          suggestion = `Améliorer la qualité/texture - ${count} retours négatifs identifiés`;
          improvementType = 'quality';
        } else if (keyword.includes('service') || keyword.includes('livraison')) {
          suggestion = `Améliorer le service client/livraison - ${count} plaintes reçues`;
          improvementType = 'service';
        } else {
          suggestion = `Améliorer l'aspect "${keyword}" - ${count} mentions négatives`;
          improvementType = 'features';
        }

        suggestions.push({
          id: `real-imp-${product.id}-${keyword}`,
          productId: product.id,
          productName: product.name,
          currentRating: avgRating,
          improvementType,
          suggestion,
          priority: count >= 5 ? 'urgent' : count >= 3 ? 'high' : 'medium',
          basedOnReviews: count,
          potentialImpact: `Amélioration estimée de ${(count * 0.2).toFixed(1)} points de rating`,
          estimatedCost: improvementType === 'price' ? 'low' : improvementType === 'quality' ? 'high' : 'medium',
          implementationTime: improvementType === 'price' ? '1 mois' : improvementType === 'quality' ? '3-6 mois' : '2-4 mois',
          keywords: [keyword]
        });
      }
    });

    return suggestions;
  }

  /**
   * Génère des améliorations génériques si pas assez de données
   */
  private static generateGenericImprovements(products: any[], reviewCount: number): ProductImprovement[] {
    if (products.length === 0) return [];

    const product = products[0]; // Prendre le premier produit

    return [{
      id: `generic-imp-${product.id}`,
      productId: product.id,
      productName: product.name,
      currentRating: product.average_rating || 3.5,
      improvementType: 'quality',
      suggestion: `Encourager plus d'avis clients pour identifier les axes d'amélioration (${reviewCount} avis actuellement)`,
      priority: 'medium',
      basedOnReviews: reviewCount,
      potentialImpact: 'Meilleure compréhension des attentes clients',
      estimatedCost: 'low',
      implementationTime: '1 mois',
      keywords: ['avis', 'feedback', 'amélioration']
    }];
  }

  /**
   * Extrait les phrases les plus importantes des commentaires
   */
  private static extractTopPhrases(comments: string[], sentiment: 'positive' | 'negative'): string[] {
    const phrases: string[] = [];
    const keywords = sentiment === 'positive'
      ? ['excellent', 'parfait', 'recommande', 'satisfait', 'efficace', 'top', 'bon']
      : ['mauvais', 'décevant', 'cher', 'inefficace', 'problème', 'déçu', 'lent'];

    comments.forEach(comment => {
      const lowerComment = comment.toLowerCase();
      keywords.forEach(keyword => {
        if (lowerComment.includes(keyword)) {
          // Extraire la phrase contenant le mot-clé
          const sentences = comment.split(/[.!?]+/);
          const relevantSentence = sentences.find(s => s.toLowerCase().includes(keyword));
          if (relevantSentence && relevantSentence.trim().length > 10) {
            phrases.push(relevantSentence.trim());
          }
        }
      });
    });

    // Retourner les phrases uniques, limitées à 5
    return [...new Set(phrases)].slice(0, 5);
  }

  /**
   * Extrait les besoins émergents des commentaires
   */
  private static extractEmergingNeeds(comments: string[]): string[] {
    const needs: string[] = [];
    const needKeywords = [
      'j\'aimerais', 'il faudrait', 'vous devriez', 'manque', 'besoin de',
      'serait bien', 'pourquoi pas', 'suggestion', 'idée'
    ];

    comments.forEach(comment => {
      const lowerComment = comment.toLowerCase();
      needKeywords.forEach(keyword => {
        if (lowerComment.includes(keyword)) {
          // Extraire la phrase contenant le besoin
          const sentences = comment.split(/[.!?]+/);
          const needSentence = sentences.find(s => s.toLowerCase().includes(keyword));
          if (needSentence && needSentence.trim().length > 15) {
            needs.push(needSentence.trim());
          }
        }
      });
    });

    return [...new Set(needs)].slice(0, 5);
  }

  /**
   * Extrait les mentions de concurrents
   */
  private static extractCompetitorMentions(comments: string[]): string[] {
    const mentions: string[] = [];
    const competitorKeywords = [
      'concurrent', 'autre marque', 'comparé à', 'moins cher que',
      'plus efficace que', 'mieux que', 'contrairement à'
    ];

    comments.forEach(comment => {
      const lowerComment = comment.toLowerCase();
      competitorKeywords.forEach(keyword => {
        if (lowerComment.includes(keyword)) {
          const sentences = comment.split(/[.!?]+/);
          const competitorSentence = sentences.find(s => s.toLowerCase().includes(keyword));
          if (competitorSentence && competitorSentence.trim().length > 10) {
            mentions.push(competitorSentence.trim());
          }
        }
      });
    });

    return [...new Set(mentions)].slice(0, 3);
  }

  /**
   * Analyse la perception des prix dans les commentaires
   */
  private static analyzePricePerceptions(comments: string[]): { tooExpensive: number; fairPrice: number; goodValue: number } {
    let tooExpensive = 0;
    let fairPrice = 0;
    let goodValue = 0;

    comments.forEach(comment => {
      const lowerComment = comment.toLowerCase();

      if (lowerComment.includes('cher') || lowerComment.includes('prix élevé') || lowerComment.includes('coûteux')) {
        tooExpensive++;
      } else if (lowerComment.includes('bon rapport') || lowerComment.includes('qualité prix') || lowerComment.includes('vaut le prix')) {
        goodValue++;
      } else if (lowerComment.includes('prix correct') || lowerComment.includes('prix raisonnable') || lowerComment.includes('abordable')) {
        fairPrice++;
      }
    });

    const total = tooExpensive + fairPrice + goodValue;
    if (total === 0) {
      return { tooExpensive: 0, fairPrice: 100, goodValue: 0 };
    }

    return {
      tooExpensive: Math.round((tooExpensive / total) * 100),
      fairPrice: Math.round((fairPrice / total) * 100),
      goodValue: Math.round((goodValue / total) * 100)
    };
  }

  /**
   * Analyse de sentiment simulée
   */
  private static analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = ['bon', 'excellent', 'parfait', 'recommande', 'satisfait', 'efficace', 'top'];
    const negativeWords = ['mauvais', 'décevant', 'cher', 'inefficace', 'problème', 'déçu'];

    const lowerText = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  /**
   * Extraction de mots-clés simulée
   */
  private static extractKeywords(text: string): string[] {
    const keywords = ['qualité', 'prix', 'texture', 'efficacité', 'naturel', 'bio', 'peau', 'hydratant'];
    const lowerText = text.toLowerCase();
    return keywords.filter(keyword => lowerText.includes(keyword));
  }
}
