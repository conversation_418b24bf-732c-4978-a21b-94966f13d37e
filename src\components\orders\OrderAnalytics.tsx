import React, { useState, useEffect } from 'react';
import {
  TrendingUp, TrendingDown, DollarSign, Package, Clock, Users,
  Calendar, BarChart3, <PERSON><PERSON>hart, Target, Award, AlertCircle
} from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import { formatAmount } from '../../utils/formatUtils';
import '../../styles/OrderAnalytics.css';

interface OrderAnalyticsProps {
  businessId: string;
  dateRange: 'today' | 'week' | 'month' | 'quarter' | 'year';
  onDateRangeChange: (range: string) => void;
}

interface AnalyticsData {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  conversionRate: number;
  topProducts: Array<{
    id: string;
    name: string;
    orders: number;
    revenue: number;
  }>;
  ordersByStatus: Record<string, number>;
  revenueByMonth: Array<{
    month: string;
    revenue: number;
    orders: number;
  }>;
  customerMetrics: {
    newCustomers: number;
    returningCustomers: number;
    averageOrdersPerCustomer: number;
  };
  trends: {
    ordersGrowth: number;
    revenueGrowth: number;
    aovGrowth: number;
  };
}

const OrderAnalytics: React.FC<OrderAnalyticsProps> = ({
  businessId,
  dateRange,
  onDateRangeChange
}) => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'products' | 'customers' | 'trends'>('overview');

  useEffect(() => {
    loadAnalytics();
  }, [businessId, dateRange]);

  const loadAnalytics = async () => {
    setLoading(true);
    try {
      // Simulation de données analytiques
      const mockAnalytics: AnalyticsData = {
        totalOrders: 156,
        totalRevenue: 2450000,
        averageOrderValue: 15705,
        conversionRate: 3.2,
        topProducts: [
          { id: '1', name: 'Huile de beauté Olgane', orders: 45, revenue: 675000 },
          { id: '2', name: 'Crème hydratante visage', orders: 38, revenue: 475000 },
          { id: '3', name: 'Sérum anti-âge', orders: 32, revenue: 800000 },
          { id: '4', name: 'Masque purifiant', orders: 28, revenue: 350000 },
          { id: '5', name: 'Lotion tonique', orders: 13, revenue: 149500 }
        ],
        ordersByStatus: {
          pending: 12,
          confirmed: 28,
          processing: 15,
          shipped: 34,
          delivered: 62,
          cancelled: 5
        },
        revenueByMonth: [
          { month: 'Jan', revenue: 180000, orders: 24 },
          { month: 'Fév', revenue: 220000, orders: 31 },
          { month: 'Mar', revenue: 195000, orders: 28 },
          { month: 'Avr', revenue: 285000, orders: 38 },
          { month: 'Mai', revenue: 320000, orders: 42 },
          { month: 'Juin', revenue: 275000, orders: 35 }
        ],
        customerMetrics: {
          newCustomers: 89,
          returningCustomers: 67,
          averageOrdersPerCustomer: 2.3
        },
        trends: {
          ordersGrowth: 15.8,
          revenueGrowth: 23.4,
          aovGrowth: 6.7
        }
      };

      setAnalytics(mockAnalytics);
    } catch (error) {
      console.error('Erreur lors du chargement des analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatGrowth = (value: number) => {
    const isPositive = value >= 0;
    return (
      <span className={`growth-indicator ${isPositive ? 'positive' : 'negative'}`}>
        {isPositive ? <TrendingUp size={14} /> : <TrendingDown size={14} />}
        {Math.abs(value).toFixed(1)}%
      </span>
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#f59e0b';
      case 'confirmed': return '#3b82f6';
      case 'processing': return '#8b5cf6';
      case 'shipped': return '#10b981';
      case 'delivered': return '#059669';
      case 'cancelled': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return 'En attente';
      case 'confirmed': return 'Confirmées';
      case 'processing': return 'En traitement';
      case 'shipped': return 'Expédiées';
      case 'delivered': return 'Livrées';
      case 'cancelled': return 'Annulées';
      default: return status;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardBody>
          <div className="analytics-loading">
            <BarChart3 size={48} />
            <p>Chargement des analytics...</p>
          </div>
        </CardBody>
      </Card>
    );
  }

  if (!analytics) {
    return (
      <Card>
        <CardBody>
          <div className="analytics-error">
            <AlertCircle size={48} />
            <p>Erreur lors du chargement des données</p>
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="order-analytics">
      {/* En-tête avec sélecteur de période */}
      <div className="analytics-header">
        <div className="header-left">
          <h2>Analytics des Commandes</h2>
          <p>Analysez les performances de vos ventes</p>
        </div>
        
        <div className="date-range-selector">
          {['today', 'week', 'month', 'quarter', 'year'].map((range) => (
            <button
              key={range}
              className={`range-btn ${dateRange === range ? 'active' : ''}`}
              onClick={() => onDateRangeChange(range)}
            >
              {range === 'today' ? 'Aujourd\'hui' :
               range === 'week' ? 'Semaine' :
               range === 'month' ? 'Mois' :
               range === 'quarter' ? 'Trimestre' : 'Année'}
            </button>
          ))}
        </div>
      </div>

      {/* Métriques principales */}
      <div className="main-metrics">
        <Card className="metric-card">
          <CardBody>
            <div className="metric-content">
              <div className="metric-icon revenue">
                <DollarSign size={24} />
              </div>
              <div className="metric-details">
                <div className="metric-value">{formatAmount(analytics.totalRevenue)}</div>
                <div className="metric-label">Chiffre d'affaires</div>
                <div className="metric-growth">{formatGrowth(analytics.trends.revenueGrowth)}</div>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="metric-card">
          <CardBody>
            <div className="metric-content">
              <div className="metric-icon orders">
                <Package size={24} />
              </div>
              <div className="metric-details">
                <div className="metric-value">{analytics.totalOrders}</div>
                <div className="metric-label">Total commandes</div>
                <div className="metric-growth">{formatGrowth(analytics.trends.ordersGrowth)}</div>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="metric-card">
          <CardBody>
            <div className="metric-content">
              <div className="metric-icon aov">
                <Target size={24} />
              </div>
              <div className="metric-details">
                <div className="metric-value">{formatAmount(analytics.averageOrderValue)}</div>
                <div className="metric-label">Panier moyen</div>
                <div className="metric-growth">{formatGrowth(analytics.trends.aovGrowth)}</div>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="metric-card">
          <CardBody>
            <div className="metric-content">
              <div className="metric-icon conversion">
                <TrendingUp size={24} />
              </div>
              <div className="metric-details">
                <div className="metric-value">{analytics.conversionRate}%</div>
                <div className="metric-label">Taux de conversion</div>
                <div className="metric-growth">{formatGrowth(2.1)}</div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Onglets */}
      <div className="analytics-tabs">
        <button
          className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          <BarChart3 size={16} />
          Vue d'ensemble
        </button>
        <button
          className={`tab ${activeTab === 'products' ? 'active' : ''}`}
          onClick={() => setActiveTab('products')}
        >
          <Package size={16} />
          Produits
        </button>
        <button
          className={`tab ${activeTab === 'customers' ? 'active' : ''}`}
          onClick={() => setActiveTab('customers')}
        >
          <Users size={16} />
          Clients
        </button>
        <button
          className={`tab ${activeTab === 'trends' ? 'active' : ''}`}
          onClick={() => setActiveTab('trends')}
        >
          <TrendingUp size={16} />
          Tendances
        </button>
      </div>

      {/* Contenu des onglets */}
      <div className="analytics-content">
        {activeTab === 'overview' && (
          <div className="overview-tab">
            {/* Répartition par statut */}
            <Card>
              <CardBody>
                <h3>Répartition des commandes par statut</h3>
                <div className="status-distribution">
                  {Object.entries(analytics.ordersByStatus).map(([status, count]) => (
                    <div key={status} className="status-item">
                      <div className="status-info">
                        <div
                          className="status-color"
                          style={{ backgroundColor: getStatusColor(status) }}
                        ></div>
                        <span className="status-label">{getStatusLabel(status)}</span>
                      </div>
                      <div className="status-count">{count}</div>
                      <div className="status-percentage">
                        {((count / analytics.totalOrders) * 100).toFixed(1)}%
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* Évolution mensuelle */}
            <Card>
              <CardBody>
                <h3>Évolution du chiffre d'affaires</h3>
                <div className="revenue-chart">
                  {analytics.revenueByMonth.map((data, index) => (
                    <div key={index} className="chart-bar">
                      <div
                        className="bar"
                        style={{
                          height: `${(data.revenue / Math.max(...analytics.revenueByMonth.map(d => d.revenue))) * 100}%`
                        }}
                      ></div>
                      <div className="bar-label">{data.month}</div>
                      <div className="bar-value">{formatAmount(data.revenue)}</div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          </div>
        )}

        {activeTab === 'products' && (
          <div className="products-tab">
            <Card>
              <CardBody>
                <h3>Top 5 des produits</h3>
                <div className="top-products">
                  {analytics.topProducts.map((product, index) => (
                    <div key={product.id} className="product-item">
                      <div className="product-rank">#{index + 1}</div>
                      <div className="product-info">
                        <div className="product-name">{product.name}</div>
                        <div className="product-stats">
                          {product.orders} commandes • {formatAmount(product.revenue)}
                        </div>
                      </div>
                      <div className="product-badge">
                        <Award size={16} />
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          </div>
        )}

        {activeTab === 'customers' && (
          <div className="customers-tab">
            <div className="customer-metrics">
              <Card>
                <CardBody>
                  <div className="customer-metric">
                    <Users size={24} />
                    <div>
                      <div className="metric-value">{analytics.customerMetrics.newCustomers}</div>
                      <div className="metric-label">Nouveaux clients</div>
                    </div>
                  </div>
                </CardBody>
              </Card>

              <Card>
                <CardBody>
                  <div className="customer-metric">
                    <Users size={24} />
                    <div>
                      <div className="metric-value">{analytics.customerMetrics.returningCustomers}</div>
                      <div className="metric-label">Clients fidèles</div>
                    </div>
                  </div>
                </CardBody>
              </Card>

              <Card>
                <CardBody>
                  <div className="customer-metric">
                    <Target size={24} />
                    <div>
                      <div className="metric-value">{analytics.customerMetrics.averageOrdersPerCustomer}</div>
                      <div className="metric-label">Commandes/client</div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          </div>
        )}

        {activeTab === 'trends' && (
          <div className="trends-tab">
            <Card>
              <CardBody>
                <h3>Tendances de croissance</h3>
                <div className="growth-trends">
                  <div className="trend-item">
                    <div className="trend-label">Commandes</div>
                    <div className="trend-value">{formatGrowth(analytics.trends.ordersGrowth)}</div>
                  </div>
                  <div className="trend-item">
                    <div className="trend-label">Chiffre d'affaires</div>
                    <div className="trend-value">{formatGrowth(analytics.trends.revenueGrowth)}</div>
                  </div>
                  <div className="trend-item">
                    <div className="trend-label">Panier moyen</div>
                    <div className="trend-value">{formatGrowth(analytics.trends.aovGrowth)}</div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderAnalytics;
