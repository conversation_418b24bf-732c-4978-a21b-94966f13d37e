import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>quare, Star, Heart, Share2, ThumbsUp, ThumbsDown,
  Filter, Search, Calendar, TrendingUp, TrendingDown, Award,
  User, Package, Eye, BarChart3, RefreshCw, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>ly, Send, X
} from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import Avatar from '../ui/Avatar';
import { RealBusinessDataService } from '../../services/realBusinessDataService';
import { usePosts } from '../../context/PostsContext';
import { useAuth } from '../../context/AuthContext';
import { UserRole } from '../../types';

interface BusinessReviewsManagerProps {
  businessId: string;
  businessName: string;
}

interface ReviewData {
  id: string;
  user_id: string;
  author_name: string;
  author_avatar?: string;
  product_name: string;
  category: string;
  type: 'coup_de_coeur' | 'coup_de_gueule' | 'demande_avis';
  rating?: number;
  description: string;
  created_at: string;
  likesCount: number;
  sharesCount: number;
  recommendationsCount: number;
  commentsCount: number;
  image?: string;
  comments?: any[]; // Ajout des commentaires pour gérer les réponses
  originalPostId?: string; // ID du post original pour les commentaires
}

const BusinessReviewsManager: React.FC<BusinessReviewsManagerProps> = ({ businessId, businessName }) => {
  const { addComment, posts: globalPosts, refreshPosts } = usePosts();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [reviews, setReviews] = useState<ReviewData[]>([]);
  const [filteredReviews, setFilteredReviews] = useState<ReviewData[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'coup_de_coeur' | 'coup_de_gueule' | 'demande_avis'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'recent' | 'rating' | 'engagement'>('recent');

  // États pour la gestion des réponses
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');
  const [submittingReply, setSubmittingReply] = useState(false);

  // État pour gérer l'affichage des commentaires
  const [expandedComments, setExpandedComments] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadBusinessReviews();
  }, [businessId, globalPosts]); // Recharger quand les posts globaux changent

  useEffect(() => {
    filterAndSortReviews();
  }, [reviews, selectedFilter, searchTerm, sortBy]);

  const loadBusinessReviews = async () => {
    setLoading(true);
    try {
      console.log('🔍 Chargement des avis pour:', businessName);

      // Utiliser directement les posts du contexte global pour avoir les données les plus récentes
      const businessPosts = globalPosts.filter(post =>
        post.businessName?.toLowerCase() === businessName.toLowerCase()
      );

      // Transformer les posts en format d'avis et inclure les commentaires comme avis séparés
      const reviewsData: ReviewData[] = [];

      businessPosts.forEach((post: any) => {
        // Ajouter le post principal
        reviewsData.push({
          id: post.id,
          user_id: post.userId,
          author_name: post.username || 'Utilisateur anonyme',
          author_avatar: post.userProfilePicture,
          product_name: post.productName || 'Produit/Service',
          category: post.category || 'Général',
          type: post.type,
          rating: post.rating,
          description: post.description || '',
          created_at: post.createdAt,
          likesCount: Array.isArray(post.likes) ? post.likes.length : 0,
          sharesCount: Array.isArray(post.shares) ? post.shares.length : 0,
          recommendationsCount: Array.isArray(post.recommendations) ? post.recommendations.length : 0,
          commentsCount: Array.isArray(post.comments) ? post.comments.length : 0,
          image: post.images && post.images.length > 0 ? post.images[0] : undefined,
          comments: post.comments || []
        });

        // Ajouter chaque commentaire comme un avis séparé (seulement ceux avec hasUsedProduct = true)
        if (post.comments && Array.isArray(post.comments)) {
          post.comments.forEach((comment: any) => {
            if (comment.hasUsedProduct && comment.rating) {
              reviewsData.push({
                id: `comment-${comment.id}`,
                user_id: comment.userId,
                author_name: comment.username || 'Utilisateur anonyme',
                author_avatar: comment.profilePicture,
                product_name: post.productName || 'Produit/Service',
                category: post.category || 'Général',
                type: 'coup_de_coeur', // Les commentaires avec rating sont considérés comme des avis
                rating: comment.rating,
                description: comment.content,
                created_at: comment.createdAt,
                likesCount: 0, // Les commentaires n'ont pas de likes séparés
                sharesCount: 0,
                recommendationsCount: 0,
                commentsCount: 0,
                image: undefined,
                comments: [],
                originalPostId: post.id // Stocker l'ID du post original
              });
            }
          });
        }
      });

      setReviews(reviewsData);
      console.log('✅ Avis chargés depuis le contexte global:', reviewsData.length);

      // Debug pour vérifier les données d'auteur
      console.log('🔍 Debug auteurs:', reviewsData.map(r => ({
        id: r.id,
        author_name: r.author_name,
        user_id: r.user_id,
        type: r.type
      })));
    } catch (error) {
      console.error('❌ Erreur lors du chargement des avis:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortReviews = () => {
    let filtered = [...reviews];

    // Filtrer par type
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(review => review.type === selectedFilter);
    }

    // Filtrer par recherche
    if (searchTerm) {
      filtered = filtered.filter(review =>
        review.product_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.author_name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Trier
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'recent':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'engagement':
          const engagementA = a.likesCount + a.sharesCount + a.recommendationsCount + a.commentsCount;
          const engagementB = b.likesCount + b.sharesCount + b.recommendationsCount + b.commentsCount;
          return engagementB - engagementA;
        default:
          return 0;
      }
    });

    setFilteredReviews(filtered);
  };

  // Fonction pour répondre à un avis
  const handleReplyToReview = async (reviewId: string) => {
    if (!replyText.trim() || !currentUser || submittingReply) return;

    // Trouver l'avis cible
    const targetReview = reviews.find(r => r.id === reviewId);
    if (!targetReview) {
      console.error('❌ Avis non trouvé');
      return;
    }

    // Déterminer l'ID du post à utiliser (post original ou commentaire)
    const postIdToUse = targetReview.originalPostId || reviewId;

    console.log('🔍 Réponse à:', {
      reviewId,
      postIdToUse,
      isComment: !!targetReview.originalPostId
    });

    // Vérifier si l'entreprise essaie de commenter son propre post
    if (targetReview.user_id === currentUser.id) {
      console.error('❌ Une entreprise ne peut pas commenter ses propres posts');
      alert('Vous ne pouvez pas commenter vos propres posts. Vous pouvez seulement répondre aux commentaires des clients.');
      return;
    }

    // Vérifier si l'entreprise essaie de répondre à sa propre réponse
    if (targetReview.comments) {
      const businessReply = targetReview.comments.find(comment => comment.userId === currentUser.id);
      if (businessReply && replyText.includes(`Bonjour ${businessReply.username || currentUser.username},`)) {
        console.error('❌ Une entreprise ne peut pas répondre à sa propre réponse');
        alert('Vous ne pouvez pas répondre à votre propre réponse. Vous pouvez seulement répondre aux commentaires des clients.');
        return;
      }
    }

    setSubmittingReply(true);
    try {
      // Récupérer le nom de l'entreprise depuis le profil utilisateur ou les props
      const enterpriseName = (currentUser as any).businessName || businessName || currentUser.username;

      await addComment(postIdToUse, {
        userId: currentUser.id,
        username: enterpriseName, // Utiliser le nom de l'entreprise
        profilePicture: currentUser.profilePicture || '',
        content: replyText.trim(),
        hasUsedProduct: false, // Les réponses d'entreprise ne sont pas des avis produit
        createdAt: new Date(),
        authorRole: UserRole.BUSINESS, // Marquer comme réponse d'entreprise
        businessName: enterpriseName, // Ajouter le nom de l'entreprise
      });

      // Réinitialiser le formulaire
      setReplyText('');
      setReplyingTo(null);

      console.log('✅ Réponse ajoutée avec succès - synchronisation automatique');
    } catch (error) {
      console.error('❌ Erreur lors de l\'ajout de la réponse:', error);
    } finally {
      setSubmittingReply(false);
    }
  };

  // Vérifier si l'entreprise a déjà répondu à un avis
  const hasBusinessReplied = (review: ReviewData) => {
    return review.comments?.some(comment => comment.userId === currentUser?.id);
  };

  // Obtenir la réponse de l'entreprise
  const getBusinessReply = (review: ReviewData) => {
    return review.comments?.find(comment => comment.userId === currentUser?.id);
  };

  // Fonction pour basculer l'affichage des commentaires
  const toggleCommentsExpansion = (reviewId: string) => {
    const newExpanded = new Set(expandedComments);
    if (newExpanded.has(reviewId)) {
      newExpanded.delete(reviewId);
    } else {
      newExpanded.add(reviewId);
    }
    setExpandedComments(newExpanded);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'coup_de_coeur':
        return <Heart className="text-green-600" size={20} />;
      case 'coup_de_gueule':
        return <TrendingDown className="text-red-600" size={20} />;
      case 'demande_avis':
        return <MessageSquare className="text-blue-600" size={20} />;
      default:
        return <MessageSquare className="text-gray-600" size={20} />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'coup_de_coeur':
        return 'Coup de Cœur';
      case 'coup_de_gueule':
        return 'Coup de Gueule';
      case 'demande_avis':
        return 'Demande d\'Avis';
      default:
        return 'Avis';
    }
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'coup_de_coeur':
        return 'bg-green-100 text-green-800';
      case 'coup_de_gueule':
        return 'bg-red-100 text-red-800';
      case 'demande_avis':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  // Calculer les statistiques
  const stats = {
    total: reviews.length,
    coupDeCoeur: reviews.filter(r => r.type === 'coup_de_coeur').length,
    coupDeGueule: reviews.filter(r => r.type === 'coup_de_gueule').length,
    demandeAvis: reviews.filter(r => r.type === 'demande_avis').length,
    averageRating: reviews.length > 0 
      ? reviews.filter(r => r.rating).reduce((sum, r) => sum + (r.rating || 0), 0) / reviews.filter(r => r.rating).length 
      : 0,
    totalEngagement: reviews.reduce((sum, r) => sum + r.likesCount + r.sharesCount + r.recommendationsCount + r.commentsCount, 0)
  };

  return (
    <div className="space-y-6">
      {/* En-tête avec statistiques */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <MessageSquare className="text-blue-600" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Mes Avis</h2>
                <p className="text-gray-600">Tous les avis des utilisateurs concernant vos produits</p>
              </div>
            </div>
            <Button onClick={loadBusinessReviews} variant="outline" size="sm" disabled={loading}>
              {loading ? <RefreshCw className="animate-spin" size={16} /> : <RefreshCw size={16} />}
            </Button>
          </div>
        </CardHeader>
        
        <CardBody>
          {/* Statistiques rapides */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <div className="bg-blue-50 rounded-lg p-3 text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
              <div className="text-sm text-blue-700">Total Avis</div>
            </div>
            <div className="bg-green-50 rounded-lg p-3 text-center">
              <div className="text-2xl font-bold text-green-600">{stats.coupDeCoeur}</div>
              <div className="text-sm text-green-700">Coups de Cœur</div>
            </div>
            <div className="bg-red-50 rounded-lg p-3 text-center">
              <div className="text-2xl font-bold text-red-600">{stats.coupDeGueule}</div>
              <div className="text-sm text-red-700">Coups de Gueule</div>
            </div>
            <div className="bg-yellow-50 rounded-lg p-3 text-center">
              <div className="text-2xl font-bold text-yellow-600">{stats.averageRating.toFixed(1)}</div>
              <div className="text-sm text-yellow-700">Note Moyenne</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-3 text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.totalEngagement}</div>
              <div className="text-sm text-purple-700">Engagement Total</div>
            </div>
          </div>

          {/* Contrôles de filtrage */}
          <div className="flex flex-wrap items-center gap-4 mb-6">
            {/* Filtres par type */}
            <div className="flex items-center space-x-2">
              <Filter className="text-gray-600" size={16} />
              <span className="text-sm font-medium text-gray-700">Type :</span>
              <div className="flex space-x-1">
                {[
                  { value: 'all', label: 'Tous' },
                  { value: 'coup_de_coeur', label: 'Coups de Cœur' },
                  { value: 'coup_de_gueule', label: 'Coups de Gueule' },
                  { value: 'demande_avis', label: 'Demandes d\'Avis' }
                ].map(filter => (
                  <button
                    key={filter.value}
                    onClick={() => setSelectedFilter(filter.value as any)}
                    className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                      selectedFilter === filter.value
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {filter.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Tri */}
            <div className="flex items-center space-x-2">
              <BarChart3 className="text-gray-600" size={16} />
              <span className="text-sm font-medium text-gray-700">Trier par :</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="text-sm border border-gray-300 rounded px-2 py-1"
              >
                <option value="recent">Plus récents</option>
                <option value="rating">Note</option>
                <option value="engagement">Engagement</option>
              </select>
            </div>

            {/* Recherche */}
            <div className="flex items-center space-x-2 flex-1 max-w-md">
              <Search className="text-gray-600" size={16} />
              <input
                type="text"
                placeholder="Rechercher dans les avis..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex-1 text-sm border border-gray-300 rounded px-3 py-1"
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Message d'information pour l'IA */}
      <Card className="border-l-4 border-purple-500 bg-purple-50">
        <CardBody>
          <div className="flex items-start space-x-3">
            <AlertTriangle className="text-purple-600 mt-1" size={20} />
            <div>
              <h3 className="text-lg font-medium text-purple-800 mb-2">
                Données pour l'IA Conseiller
              </h3>
              <p className="text-purple-700 mb-3">
                Ces avis sont automatiquement analysés par l'IA pour générer des recommandations personnalisées et identifier les tendances client.
              </p>
              <div className="text-sm text-purple-600">
                <strong>L'IA utilise ces informations pour :</strong>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Analyser les sentiments des clients (positifs/négatifs)</li>
                  <li>Identifier les produits les plus appréciés ou critiqués</li>
                  <li>Détecter les problèmes récurrents dans les avis</li>
                  <li>Suggérer des améliorations basées sur les retours clients</li>
                  <li>Proposer de nouveaux produits selon les demandes</li>
                </ul>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Liste des avis */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <MessageSquare className="mx-auto text-blue-600 mb-4 animate-pulse" size={48} />
            <p className="text-gray-600">Chargement des avis...</p>
          </div>
        </div>
      ) : filteredReviews.length === 0 ? (
        <Card>
          <CardBody className="text-center py-12">
            <MessageSquare className="mx-auto text-gray-400 mb-4" size={48} />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun avis trouvé</h3>
            <p className="text-gray-600">
              {searchTerm || selectedFilter !== 'all' 
                ? 'Aucun avis ne correspond à vos critères de recherche.'
                : 'Aucun avis n\'a encore été publié concernant vos produits.'}
            </p>
          </CardBody>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredReviews.slice(0, 10).map((review) => (
            <Card key={review.id} className="hover:shadow-md transition-shadow">
              <CardBody>
                <div className="space-y-4">
                  {/* En-tête de l'avis */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <Avatar
                        src={review.author_avatar}
                        alt={review.author_name}
                        size="md"
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium text-gray-900">{review.author_name}</h4>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getTypeBadgeColor(review.type)}`}>
                            {getTypeLabel(review.type)}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Package size={14} />
                          <span>{review.product_name}</span>
                          <span>•</span>
                          <span>{review.category}</span>
                          <span>•</span>
                          <Calendar size={14} />
                          <span>{new Date(review.created_at).toLocaleDateString('fr-FR')}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getTypeIcon(review.type)}
                      {review.rating && (
                        <div className="flex items-center space-x-1">
                          {renderStars(review.rating)}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Contenu de l'avis */}
                  <div className="pl-12">
                    <p className="text-gray-700 mb-3">{review.description}</p>
                    
                    {/* Image si présente */}
                    {review.image && (
                      <img
                        src={review.image}
                        alt="Image de l'avis"
                        className="rounded-lg max-w-md max-h-64 object-cover mb-3"
                      />
                    )}

                    {/* Métriques d'engagement */}
                    <div className="flex items-center space-x-6 text-sm text-gray-600 mb-4">
                      <div className="flex items-center space-x-1">
                        <Heart size={16} className="text-red-500" />
                        <span>{review.likesCount}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Share2 size={16} className="text-blue-500" />
                        <span>{review.sharesCount}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Award size={16} className="text-green-500" />
                        <span>{review.recommendationsCount}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MessageSquare size={16} className="text-purple-500" />
                        <span>{review.commentsCount}</span>
                      </div>
                    </div>

                    {/* Réponse de l'entreprise si elle existe */}
                    {hasBusinessReplied(review) && (
                      <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
                        <div className="flex items-start space-x-3">
                          <Avatar
                            src={currentUser?.profilePicture}
                            alt={currentUser?.username || 'Entreprise'}
                            size="sm"
                          />
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="font-medium text-blue-900">Réponse de votre entreprise</span>
                              <span className="text-xs text-blue-600">
                                {new Date(getBusinessReply(review)?.createdAt || '').toLocaleDateString('fr-FR')}
                              </span>
                            </div>
                            <p className="text-blue-800">{getBusinessReply(review)?.content}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Afficher tous les commentaires du post si c'est un post principal */}
                    {!review.id.startsWith('comment-') && review.comments && review.comments.length > 0 && (
                      <div className="mt-4 space-y-3">
                        <h5 className="font-medium text-gray-900 text-sm">Commentaires sur ce post ({review.comments.length})</h5>

                        {/* Afficher les commentaires selon l'état d'expansion */}
                        {(expandedComments.has(review.id) ? review.comments : review.comments.slice(0, 3)).map((comment: any) => {
                          const isBusinessReply = comment.userId === currentUser?.id;
                          return (
                            <div key={comment.id} className={`p-3 rounded-lg ${isBusinessReply ? 'bg-blue-50 border-l-4 border-blue-400' : 'bg-gray-50'}`}>
                              <div className="flex items-start space-x-2">
                                <Avatar
                                  src={comment.profilePicture}
                                  alt={comment.username}
                                  size="sm"
                                />
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <span className="font-medium text-sm">{comment.username}</span>
                                    {isBusinessReply && (
                                      <span className="inline-block px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">
                                        Réponse de l'entreprise
                                      </span>
                                    )}
                                    {comment.hasUsedProduct && comment.rating && (
                                      <div className="flex items-center">
                                        {renderStars(comment.rating)}
                                      </div>
                                    )}
                                    <span className="text-xs text-gray-500">
                                      {new Date(comment.createdAt).toLocaleDateString('fr-FR')}
                                    </span>
                                  </div>
                                  <p className="text-sm text-gray-700">{comment.content}</p>
                                  {comment.hasUsedProduct && (
                                    <span className="inline-block mt-1 px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full">
                                      Utilisateur vérifié
                                    </span>
                                  )}

                                  {/* Bouton répondre spécifique à ce commentaire - seulement si ce n'est pas la réponse de l'entreprise elle-même */}
                                  {!isBusinessReply && (
                                    <div className="mt-2">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="text-xs h-6 px-2"
                                        onClick={() => {
                                          setReplyingTo(review.id);
                                          setReplyText(`Bonjour ${comment.username}, `);
                                        }}
                                      >
                                        Répondre
                                      </Button>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          );
                        })}

                        {/* Bouton pour voir plus/moins de commentaires */}
                        {review.comments.length > 3 && (
                          <div className="text-center">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => toggleCommentsExpansion(review.id)}
                            >
                              {expandedComments.has(review.id)
                                ? 'Masquer les commentaires'
                                : `Voir tous les ${review.comments.length} commentaires`
                              }
                            </Button>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Bouton de réponse ou formulaire de réponse */}
                    {replyingTo === review.id ? (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-start space-x-3">
                          <Avatar
                            src={currentUser?.profilePicture}
                            alt={currentUser?.username || 'Vous'}
                            size="sm"
                          />
                          <div className="flex-1">
                            <textarea
                              value={replyText}
                              onChange={(e) => setReplyText(e.target.value)}
                              placeholder={hasBusinessReplied(review) ? "Modifier votre réponse..." : `Répondre à ${review.author_name}...`}
                              className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              rows={3}
                            />
                            <div className="flex items-center justify-between mt-3">
                              <div className="text-sm text-gray-500">
                                {replyText.length}/500 caractères
                              </div>
                              <div className="flex items-center space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setReplyingTo(null);
                                    setReplyText('');
                                  }}
                                >
                                  <X size={16} />
                                  Annuler
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => handleReplyToReview(review.id)}
                                  disabled={!replyText.trim() || submittingReply || replyText.length > 500}
                                >
                                  {submittingReply ? (
                                    <RefreshCw size={16} className="animate-spin" />
                                  ) : (
                                    <Send size={16} />
                                  )}
                                  {hasBusinessReplied(review) ? 'Modifier' : 'Répondre'}
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex justify-end">
                        {/* Bouton Répondre - seulement si ce n'est pas le post de l'entreprise elle-même */}
                        {review.user_id !== currentUser?.id ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setReplyingTo(review.id);
                              // Préremplir avec le nom de l'auteur
                              const existingReply = getBusinessReply(review)?.content || '';
                              const prefixedText = hasBusinessReplied(review)
                                ? existingReply
                                : `Bonjour ${review.author_name}, `;
                              setReplyText(prefixedText);
                            }}
                          >
                            <Reply size={16} />
                            {hasBusinessReplied(review) ? 'Modifier la réponse' : 'Répondre'}
                          </Button>
                        ) : (
                          // Message informatif pour les posts de l'entreprise
                          <div className="text-sm text-gray-500 italic">
                            💡 Utilisez les boutons "Répondre" sur les commentaires individuels
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </CardBody>
            </Card>
          ))}
          
          {filteredReviews.length > 10 && (
            <Card className="bg-gray-50">
              <CardBody className="text-center">
                <p className="text-gray-600">
                  Affichage des 10 premiers avis sur {filteredReviews.length} au total
                </p>
                <Button variant="outline" size="sm" className="mt-2">
                  Voir tous les avis
                </Button>
              </CardBody>
            </Card>
          )}
        </div>
      )}

      {/* Résumé en bas */}
      {filteredReviews.length > 0 && (
        <Card className="bg-gray-50">
          <CardBody>
            <div className="text-center text-sm text-gray-600">
              Affichage de {Math.min(10, filteredReviews.length)} avis sur {reviews.length} au total
              {selectedFilter !== 'all' && ` • Filtre: ${getTypeLabel(selectedFilter)}`}
              {searchTerm && ` • Recherche: "${searchTerm}"`}
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default BusinessReviewsManager;
