import React, { useState, useEffect } from 'react';
import { CheckCircle, XCircle, Clock, AlertTriangle } from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';

interface ImageStatus {
  url: string;
  status: 'loading' | 'success' | 'error' | 'pending';
  loadTime?: number;
  error?: string;
}

const ImageDiagnostic: React.FC = () => {
  const [imageStatuses, setImageStatuses] = useState<ImageStatus[]>([]);

  // URLs d'images à tester
  const testImages = [
    'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=600&h=300&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1570194065650-d99fb4bedf0a?w=600&h=300&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1620916566398-39f1143ab7be?w=600&h=300&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=600&h=300&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=600&h=300&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?w=50&h=50&fit=crop&crop=center'
  ];

  useEffect(() => {
    // Initialiser les statuts
    setImageStatuses(testImages.map(url => ({ url, status: 'pending' })));

    // Tester chaque image
    testImages.forEach((url, index) => {
      testImage(url, index);
    });
  }, []);

  const testImage = (url: string, index: number) => {
    const startTime = Date.now();
    
    // Mettre à jour le statut à "loading"
    setImageStatuses(prev => 
      prev.map((item, i) => 
        i === index ? { ...item, status: 'loading' } : item
      )
    );

    const img = new Image();
    
    img.onload = () => {
      const loadTime = Date.now() - startTime;
      setImageStatuses(prev => 
        prev.map((item, i) => 
          i === index ? { ...item, status: 'success', loadTime } : item
        )
      );
    };

    img.onerror = (error) => {
      setImageStatuses(prev => 
        prev.map((item, i) => 
          i === index ? { 
            ...item, 
            status: 'error', 
            error: 'Impossible de charger l\'image'
          } : item
        )
      );
    };

    img.src = url;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'loading':
        return <Clock className="text-yellow-500 animate-spin" size={16} />;
      case 'success':
        return <CheckCircle className="text-green-500" size={16} />;
      case 'error':
        return <XCircle className="text-red-500" size={16} />;
      default:
        return <AlertTriangle className="text-gray-400" size={16} />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'loading':
        return 'Chargement...';
      case 'success':
        return 'Succès';
      case 'error':
        return 'Erreur';
      default:
        return 'En attente';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'loading':
        return 'text-yellow-600 bg-yellow-50';
      case 'success':
        return 'text-green-600 bg-green-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const successCount = imageStatuses.filter(img => img.status === 'success').length;
  const errorCount = imageStatuses.filter(img => img.status === 'error').length;
  const loadingCount = imageStatuses.filter(img => img.status === 'loading').length;
  const averageLoadTime = imageStatuses
    .filter(img => img.loadTime)
    .reduce((acc, img) => acc + (img.loadTime || 0), 0) / 
    imageStatuses.filter(img => img.loadTime).length || 0;

  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold text-gray-900">🔍 Diagnostic des Images</h3>
      </CardHeader>
      <CardBody>
        {/* Résumé */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{successCount}</div>
            <div className="text-sm text-green-700">Succès</div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{errorCount}</div>
            <div className="text-sm text-red-700">Erreurs</div>
          </div>
          <div className="text-center p-3 bg-yellow-50 rounded-lg">
            <div className="text-2xl font-bold text-yellow-600">{loadingCount}</div>
            <div className="text-sm text-yellow-700">En cours</div>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {averageLoadTime ? Math.round(averageLoadTime) : 0}ms
            </div>
            <div className="text-sm text-blue-700">Temps moyen</div>
          </div>
        </div>

        {/* Liste détaillée */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Détails par image :</h4>
          {imageStatuses.map((image, index) => (
            <div 
              key={index}
              className={`flex items-center justify-between p-3 rounded-lg border ${getStatusColor(image.status)}`}
            >
              <div className="flex items-center space-x-3">
                {getStatusIcon(image.status)}
                <div>
                  <div className="font-medium text-sm">
                    Image {index + 1}
                  </div>
                  <div className="text-xs text-gray-500 truncate max-w-xs">
                    {image.url}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm font-medium">
                  {getStatusText(image.status)}
                </div>
                {image.loadTime && (
                  <div className="text-xs text-gray-500">
                    {image.loadTime}ms
                  </div>
                )}
                {image.error && (
                  <div className="text-xs text-red-600">
                    {image.error}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Actions */}
        <div className="mt-6 flex space-x-3">
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
          >
            Relancer le test
          </button>
          <button
            onClick={() => {
              const report = {
                timestamp: new Date().toISOString(),
                total: imageStatuses.length,
                success: successCount,
                errors: errorCount,
                averageLoadTime: Math.round(averageLoadTime),
                details: imageStatuses
              };
              console.log('Rapport de diagnostic des images:', report);
              alert('Rapport généré dans la console');
            }}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm"
          >
            Générer rapport
          </button>
        </div>

        {/* Conseils */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h5 className="font-medium text-blue-900 mb-2">💡 Conseils de dépannage</h5>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Si toutes les images échouent : vérifiez votre connexion internet</li>
            <li>• Si certaines images échouent : les URLs peuvent être invalides</li>
            <li>• Temps de chargement élevé : considérez l'optimisation des images</li>
            <li>• Erreurs CORS : vérifiez les politiques de partage de ressources</li>
          </ul>
        </div>
      </CardBody>
    </Card>
  );
};

export default ImageDiagnostic;
