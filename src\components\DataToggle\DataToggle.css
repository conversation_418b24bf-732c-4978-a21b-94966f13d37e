/* Data Toggle Component - Styles Ultra-Avancés */
.data-toggle-container {
  position: relative;
  background: white;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 20px;
}

.data-toggle-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

/* Data Source Indicator */
.data-toggle-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.data-source-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.real-data-icon {
  color: #10b981;
}

.mock-data-icon {
  color: #f59e0b;
}

.loading-icon {
  color: #3b82f6;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.data-source-label {
  color: #374151;
}

.info-button {
  width: 24px;
  height: 24px;
  border: none;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.info-button:hover {
  background: #e5e7eb;
  color: #374151;
  transform: scale(1.1);
}

/* Toggle Switch */
.toggle-switch-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-label {
  font-size: 0.8rem;
  font-weight: 500;
  color: #6b7280;
  transition: color 0.2s ease;
}

.toggle-label.demo {
  color: #f59e0b;
}

.toggle-label.real {
  color: #10b981;
}

.toggle-switch {
  position: relative;
  width: 52px;
  height: 28px;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-switch.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.toggle-track {
  width: 100%;
  height: 100%;
  background: #e5e7eb;
  border-radius: 14px;
  position: relative;
  transition: all 0.3s ease;
}

.toggle-switch.active .toggle-track {
  background: #10b981;
}

.toggle-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: #6b7280;
}

.toggle-switch.active .toggle-thumb {
  transform: translateX(24px);
  color: #10b981;
}

.toggle-switch:hover:not(.disabled) .toggle-thumb {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transform: scale(1.05);
}

.toggle-switch.active:hover:not(.disabled) .toggle-thumb {
  transform: translateX(24px) scale(1.05);
}

/* Status Indicator */
.data-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-indicator.success {
  background: #d1fae5;
  color: #065f46;
}

.status-indicator.warning {
  background: #fef3c7;
  color: #92400e;
}

/* Info Panel */
.data-info-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  z-index: 100;
  margin-top: 8px;
  animation: slideDown 0.3s ease;
  overflow: hidden;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.info-header h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-info {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  font-size: 18px;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-info:hover {
  background: #e5e7eb;
  color: #374151;
}

.info-content {
  padding: 20px;
}

.info-section {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.info-icon.demo {
  background: #fef3c7;
  color: #f59e0b;
}

.info-icon.real {
  background: #d1fae5;
  color: #10b981;
}

.info-text h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.info-text p {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.info-text ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-text li {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.info-footer {
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

.recommendation {
  font-size: 0.8rem;
  color: #374151;
  line-height: 1.4;
}

.recommendation strong {
  color: #1f2937;
}

/* Responsive Design */
@media (max-width: 768px) {
  .data-toggle-wrapper {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .data-toggle-info {
    justify-content: space-between;
  }
  
  .toggle-switch-container {
    justify-content: center;
  }
  
  .data-status {
    justify-content: center;
  }
  
  .info-section {
    flex-direction: column;
    gap: 12px;
  }
  
  .info-icon {
    align-self: flex-start;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .data-toggle-container {
    background: #1f2937;
    border-color: #374151;
  }
  
  .data-source-label {
    color: #f9fafb;
  }
  
  .info-button {
    background: #374151;
    color: #9ca3af;
  }
  
  .info-button:hover {
    background: #4b5563;
    color: #f9fafb;
  }
  
  .data-info-panel {
    background: #1f2937;
    border-color: #374151;
  }
  
  .info-header {
    background: #374151;
    border-color: #4b5563;
  }
  
  .info-header h4 {
    color: #f9fafb;
  }
  
  .info-text h5 {
    color: #f9fafb;
  }
  
  .info-text p,
  .info-text li {
    color: #d1d5db;
  }
  
  .info-footer {
    background: #374151;
    border-color: #4b5563;
  }
  
  .recommendation {
    color: #d1d5db;
  }
  
  .recommendation strong {
    color: #f9fafb;
  }
}

/* Accessibility */
.toggle-switch:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.info-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .toggle-thumb,
  .toggle-track,
  .data-info-panel,
  .loading-icon {
    animation: none;
    transition: none;
  }
}
