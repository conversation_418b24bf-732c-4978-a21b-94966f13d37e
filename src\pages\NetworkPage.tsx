import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useFollow } from '../context/FollowContext';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../lib/supabase';
import Avatar from '../components/ui/Avatar';
import Button from '../components/ui/Button';
import Card, { CardBody } from '../components/ui/Card';
import { Users, UserPlus, UserMinus, ArrowLeft, Search } from 'lucide-react';
import { Link } from 'react-router-dom';

interface UserProfile {
  id: string;
  username: string;
  profile_picture?: string;
  bio?: string;
  role?: string;
  businessName?: string;
}

const NetworkPage: React.FC = () => {
  const { userId, tab = 'followers' } = useParams<{ userId: string; tab?: string }>();
  const navigate = useNavigate();
  const { getFollowersDetails, getFollowingDetails, followUser, unfollowUser, isFollowing } = useFollow();
  const { currentUser } = useAuth();
  
  const [activeTab, setActiveTab] = useState<'followers' | 'following'>(
    tab === 'following' ? 'following' : 'followers'
  );
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [followStatus, setFollowStatus] = useState<Record<string, boolean>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [profileOwner, setProfileOwner] = useState<UserProfile | null>(null);

  useEffect(() => {
    // Update the URL when tab changes
    navigate(`/network/${userId}/${activeTab}`, { replace: true });
  }, [activeTab, userId, navigate]);

  useEffect(() => {
    const fetchProfileOwner = async () => {
      if (!userId) return;
      
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('id, username, profile_picture, bio, role')
          .eq('id', userId)
          .single();
          
        if (error) throw error;
        setProfileOwner(data);
      } catch (error) {
        console.error('Erreur lors de la récupération du profil:', error);
      }
    };
    
    fetchProfileOwner();
  }, [userId]);

  useEffect(() => {
    const fetchData = async () => {
      if (!userId) return;
      
      setLoading(true);
      try {
        let userData: UserProfile[] = [];
        
        if (activeTab === 'followers') {
          userData = await getFollowersDetails(userId);
        } else {
          userData = await getFollowingDetails(userId);
        }
        
        setUsers(userData);
        setFilteredUsers(userData);
        
        // Check if current user is following each user
        if (currentUser) {
          const statusMap: Record<string, boolean> = {};
          for (const user of userData) {
            statusMap[user.id] = await isFollowing(user.id);
          }
          setFollowStatus(statusMap);
        }
      } catch (error) {
        console.error(`Erreur lors de la récupération des ${activeTab}:`, error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [userId, activeTab, getFollowersDetails, getFollowingDetails, isFollowing, currentUser]);

  useEffect(() => {
    // Filter users based on search query
    if (searchQuery.trim() === '') {
      setFilteredUsers(users);
    } else {
      const filtered = users.filter(user => 
        user.username.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredUsers(filtered);
    }
  }, [searchQuery, users]);

  const handleFollowToggle = async (targetUserId: string) => {
    if (!currentUser) return;
    
    try {
      if (followStatus[targetUserId]) {
        await unfollowUser(targetUserId);
        setFollowStatus(prev => ({ ...prev, [targetUserId]: false }));
      } else {
        await followUser(targetUserId);
        setFollowStatus(prev => ({ ...prev, [targetUserId]: true }));
      }
    } catch (error) {
      console.error("Erreur lors de la modification de l'abonnement:", error);
    }
  };

  const isOwnProfile = currentUser?.id === userId;

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="mb-6">
        <button 
          onClick={() => navigate(`/profile/${userId}`)}
          className="flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft size={20} className="mr-2" />
          Retour au profil
        </button>
      </div>
      
      <div className="flex flex-col md:flex-row gap-6">
        <div className="w-full md:w-1/4">
          <Card className="sticky top-20">
            <CardBody>
              {profileOwner ? (
                <div className="text-center">
                  <Avatar 
                    src={profileOwner.profile_picture} 
                    alt={profileOwner.username} 
                    size="xl"
                    className="mx-auto mb-4"
                  />
                  <h2 className="text-xl font-semibold">{profileOwner.username}</h2>
                  {profileOwner.bio && (
                    <p className="text-gray-600 mt-2">{profileOwner.bio}</p>
                  )}
                  
                  <div className="mt-4 flex justify-center space-x-4">
                    <div className="text-center">
                      <div className="text-xl font-semibold">{users.length}</div>
                      <div className="text-sm text-gray-500">
                        {activeTab === 'followers' ? 'Abonnés' : 'Abonnements'}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                </div>
              )}
            </CardBody>
          </Card>
        </div>
        
        <div className="w-full md:w-3/4">
          <Card>
            <CardBody>
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">
                  {isOwnProfile ? 'Votre réseau' : `Réseau de ${profileOwner?.username}`}
                </h1>
              </div>
              
              <div className="flex border-b mb-6">
                <button
                  className={`py-2 px-4 font-medium flex items-center ${activeTab === 'followers' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
                  onClick={() => setActiveTab('followers')}
                >
                  <Users size={18} className="mr-2" />
                  Abonnés
                </button>
                <button
                  className={`py-2 px-4 font-medium flex items-center ${activeTab === 'following' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
                  onClick={() => setActiveTab('following')}
                >
                  <UserPlus size={18} className="mr-2" />
                  Abonnements
                </button>
              </div>
              
              <div className="mb-6">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder={`Rechercher parmi les ${activeTab === 'followers' ? 'abonnés' : 'abonnements'}...`}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              
              {loading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                </div>
              ) : filteredUsers.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  {searchQuery ? (
                    `Aucun ${activeTab === 'followers' ? 'abonné' : 'abonnement'} ne correspond à votre recherche.`
                  ) : (
                    activeTab === 'followers' 
                      ? `${isOwnProfile ? 'Vous n\'avez' : `${profileOwner?.username} n'a`} pas encore d'abonnés.` 
                      : `${isOwnProfile ? 'Vous ne suivez' : `${profileOwner?.username} ne suit`} personne pour le moment.`
                  )}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {filteredUsers.map(user => (
                    <div key={user.id} className="border rounded-lg p-4 flex items-center justify-between">
                      <Link to={`/profile/${user.id}`} className="flex items-center flex-1">
                        <Avatar 
                          src={user.profile_picture} 
                          alt={user.role === 'business' && user.businessName ? user.businessName : user.username} 
                          size="md"
                          className="mr-3"
                        />
                        <div>
                          <h3 className="font-medium text-gray-900">{user.role === 'business' && user.businessName ? user.businessName : user.username}</h3>
                          {user.bio && (
                            <p className="text-sm text-gray-500 line-clamp-1">{user.bio}</p>
                          )}
                        </div>
                      </Link>
                      
                      {currentUser && currentUser.id !== user.id && (
                        <Button
                          variant={followStatus[user.id] ? "outline" : "primary"}
                          size="sm"
                          onClick={() => handleFollowToggle(user.id)}
                          className={followStatus[user.id] ? "text-gray-700" : ""}
                          leftIcon={followStatus[user.id] ? <UserMinus size={16} /> : <UserPlus size={16} />}
                        >
                          {followStatus[user.id] ? 'Abonné' : 'Suivre'}
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default NetworkPage;
