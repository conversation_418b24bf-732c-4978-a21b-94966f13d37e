import React from 'react';
import { ShoppingBag, Search, Plus } from 'lucide-react';
import Button from '../ui/Button';

interface EmptyOrdersStateProps {
  hasFilters: boolean;
  onClearFilters: () => void;
  onStartShopping?: () => void;
}

const EmptyOrdersState: React.FC<EmptyOrdersStateProps> = ({
  hasFilters,
  onClearFilters,
  onStartShopping
}) => {
  if (hasFilters) {
    // État quand il y a des filtres actifs mais aucun résultat
    return (
      <div className="bg-white rounded-xl p-12 text-center shadow-sm border border-gray-100">
        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
          <Search className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune commande trouvée</h3>
        <p className="text-gray-500 mb-6">
          Aucune commande ne correspond à vos critères de recherche.
          <br />
          Essayez de modifier vos filtres ou votre recherche.
        </p>
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button variant="primary" onClick={onClearFilters}>
            Effacer les filtres
          </Button>
          <Button variant="outline" onClick={onClearFilters}>
            Voir toutes les commandes
          </Button>
        </div>
      </div>
    );
  }

  // État quand il n'y a aucune commande du tout
  return (
    <div className="bg-white rounded-xl p-12 text-center shadow-sm border border-gray-100">
      <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
        <ShoppingBag className="w-10 h-10 text-blue-600" />
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-3">Aucune commande pour le moment</h3>
      <p className="text-gray-500 mb-8 max-w-md mx-auto">
        Vous n'avez pas encore passé de commande. Découvrez notre marketplace et trouvez des produits incroyables de nos entreprises partenaires.
      </p>
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        {onStartShopping && (
          <Button variant="primary" onClick={onStartShopping} className="flex items-center space-x-2">
            <Plus size={16} />
            <span>Commencer mes achats</span>
          </Button>
        )}
        <Button variant="outline" onClick={() => window.location.href = '/marketplace'}>
          Découvrir le marketplace
        </Button>
      </div>
      
      {/* Suggestions */}
      <div className="mt-8 pt-8 border-t border-gray-100">
        <h4 className="text-sm font-medium text-gray-900 mb-4">Suggestions pour commencer</h4>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-2 mx-auto">
              <ShoppingBag className="w-4 h-4 text-blue-600" />
            </div>
            <p className="font-medium text-blue-900">Explorez les produits</p>
            <p className="text-blue-700 mt-1">Découvrez notre sélection de produits locaux</p>
          </div>
          <div className="p-4 bg-green-50 rounded-lg">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mb-2 mx-auto">
              <Search className="w-4 h-4 text-green-600" />
            </div>
            <p className="font-medium text-green-900">Recherchez par catégorie</p>
            <p className="text-green-700 mt-1">Trouvez exactement ce que vous cherchez</p>
          </div>
          <div className="p-4 bg-purple-50 rounded-lg">
            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mb-2 mx-auto">
              <Plus className="w-4 h-4 text-purple-600" />
            </div>
            <p className="font-medium text-purple-900">Suivez vos entreprises</p>
            <p className="text-purple-700 mt-1">Restez informé des nouveautés</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmptyOrdersState;
