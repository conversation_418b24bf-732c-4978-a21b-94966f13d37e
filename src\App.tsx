import React, { useState } from 'react';
import { AuthProvider } from './context/AuthContext';
import { PostsProvider } from './context/PostsContext';
import { MarketplaceProvider } from './context/MarketplaceContext';
import { FollowProvider } from './context/FollowContext';
import { FollowRequestProvider } from './context/FollowRequestContext';
import Navbar from './components/layout/Navbar';
import Sidebar from './components/layout/Sidebar';
import RightSidebar from './components/layout/RightSidebar';
import Footer from './components/layout/Footer';
import HomePage from './pages/HomePage';
import AuthPage from './pages/AuthPage';
import ProfilePage from './pages/ProfilePage';
import BusinessProfilePage from './pages/BusinessProfilePage';
import NetworkPage from './pages/NetworkPage';
import MarketplacePage from './pages/MarketplacePage';
import RecommendationsPage from './pages/RecommendationsPage';
import SupportPage from './pages/SupportPage';
import MyRecommendationsPage from './pages/MyRecommendationsPage';
import TopProductsPage from './pages/TopProductsPage';
import TopRatedProductsPage from './pages/TopRatedProductsPage';
import TrendingPage from './pages/TrendingPage';
import BusinessesPage from './pages/BusinessesPage';
import SearchResultsPage from './pages/SearchResultsPage';
import AIRecommendationsPage from './pages/AIRecommendationsPage';
import BusinessOrdersPage from './pages/BusinessOrdersPage';
import BusinessSalesPage from './pages/BusinessSalesPage';
import BusinessProductsPage from './pages/BusinessProductsPage';
import BusinessRankingsPage from './pages/BusinessRankingsPage';
import BusinessReviewsPage from './pages/BusinessReviewsPage';
import BusinessAdsPage from './pages/BusinessAdsPage';
import OffersAndPromotionsPage from './pages/OffersAndPromotionsPage';
import BusinessSubscriptionPage from './pages/BusinessSubscriptionPage';
import OrdersPage from './pages/OrdersPage';
import ReviewsPage from './pages/ReviewsPage';
import NotificationTestPage from './pages/NotificationTestPage';
import UserStatusLevelsPage from './pages/UserStatusLevelsPage';
import UsersPage from './pages/UsersPage';
import TestStatusPage from './pages/TestStatusPage';
import SettingsPage from './pages/SettingsPage';
import NegotiationsPage from './pages/NegotiationsPage';
import ProductManagementPage from './pages/ProductManagementPage';
import SubscriptionPage from './pages/SubscriptionPageSimple';
import PaymentDemoPage from './pages/PaymentDemoPage';
import FeaturesOverviewPage from './pages/FeaturesOverviewPage';
import AdsTestPage from './pages/AdsTestPage';
import OffersTestPage from './pages/OffersTestPage';
import OffersLayoutTestPage from './pages/OffersLayoutTestPage';
import AdRestrictionsTestPage from './pages/AdRestrictionsTestPage';
import FeedAdCardTest from './components/ads/FeedAdCardTest';
import AdCampaignDiagnostic from './components/ads/AdCampaignDiagnostic';
import DebugFollowRequestPage from './pages/DebugFollowRequestPage';
import ToastTestPage from './pages/ToastTestPage';
import AdminDashboardPage from './pages/AdminDashboardPage';
import AboutPage from './pages/AboutPage';
import PrivacyPolicyPage from './pages/PrivacyPolicyPage';
import TermsOfServicePage from './pages/TermsOfServicePage';
import LikeButtonTest from './components/debug/LikeButtonTest';

import './utils/followRequestDiagnostic'; // Importer pour rendre disponible dans la console
import './utils/toastTester'; // Importer les utilitaires de test toast
import './utils/testOfferInteractions'; // Importer les utilitaires de test des offres
import { useAuth } from './context/AuthContext';
import { UserRole } from './types';
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import ProfileRoute from './routes/ProfileRoute';
import { NotificationsProvider } from './context/NotificationsContext';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import { ToastContainer } from 'react-toastify';
import { ToastProvider } from './components/ui/ToastContainer';

type PageState = 'home' | 'profile' | 'marketplace' | 'recommendations' | 'support' | 'my-recommendations' | 'top-products' | 'trending' | 'businesses' | 'search-results' | 'ai-recommendations' | 'business-orders' | 'business-sales' | 'business-products' | 'business-rankings' | 'business-reviews' | 'business-ads' | 'offers-and-promotions' | 'orders' | 'user-reviews' | 'notification-test' | 'network' | 'user-status-levels' | 'users' | 'settings' | 'negotiations' | 'product-management' | 'subscription';

const Layout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [contentLoaded, setContentLoaded] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  // Updated PageState type to include all possible page states

  // Convert pathname to PageState
  const getPageStateFromPath = (pathname: string): PageState => {
    if (pathname === '/') return 'home';
    if (pathname === '/profile') return 'profile';
    if (pathname === '/marketplace') return 'marketplace';
    if (pathname === '/recommendations') return 'recommendations';
    if (pathname === '/support') return 'support';
    if (pathname === '/my-recommendations') return 'my-recommendations';
    if (pathname === '/top-products') return 'top-products';
    if (pathname === '/trending') return 'trending';
    if (pathname === '/businesses') return 'businesses';
    if (pathname === '/ai-recommendations') return 'ai-recommendations';
    if (pathname === '/business-orders') return 'business-orders';
    if (pathname === '/business-sales') return 'business-sales';
    if (pathname === '/business-products') return 'business-products';
    if (pathname === '/business-rankings') return 'business-rankings';
    if (pathname === '/business-reviews') return 'business-reviews';
    if (pathname === '/business-ads') return 'business-ads';
    if (pathname === '/offers-and-promotions') return 'offers-and-promotions';
    if (pathname === '/orders') return 'orders';
    if (pathname === '/user-reviews') return 'user-reviews';
    if (pathname === '/notification-test') return 'notification-test';
    if (pathname === '/user-status-levels') return 'user-status-levels';
    if (pathname === '/users') return 'users';
    if (pathname === '/settings') return 'settings';
    if (pathname === '/negotiations') return 'negotiations';
    if (pathname === '/product-management') return 'product-management';
    if (pathname === '/business-subscription') return 'subscription';
    if (pathname.startsWith('/network')) return 'network';
    if (pathname.startsWith('/search')) return 'search-results';
    return 'home'; // Default to home if no match
  };

  const currentPage = getPageStateFromPath(location.pathname);

  // Effet pour gérer le chargement du contenu
  React.useEffect(() => {
    setContentLoaded(false);
    // Délai très court pour permettre au contenu de se charger
    const timer = setTimeout(() => {
      setContentLoaded(true);
    }, 50);
    return () => clearTimeout(timer);
  }, [location.pathname]);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSearchSubmit = (query: string) => {
    navigate(`/search?q=${encodeURIComponent(query)}`);
  };

  if (!isAuthenticated) {
    return <AuthPage />;
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Navbar
        toggleSidebar={toggleSidebar}
        sidebarOpen={sidebarOpen}
        onProfileClick={() => navigate('/profile')}
        onHomeClick={() => navigate('/')}
        onMarketplaceClick={() => navigate('/marketplace')}
        onRecommendationsClick={() => navigate('/recommendations')}
        onSupportClick={() => navigate('/support')}
        onSearchSubmit={handleSearchSubmit}
        currentPage={currentPage as any}
        onOffersAndPromotionsClick={() => navigate('/offers-and-promotions')}
        onSettingsClick={() => navigate('/settings')}
        onOrdersClick={() => navigate('/orders')}
        onMyRecommendationsClick={() => navigate('/my-recommendations')}
        onBusinessProductsClick={() => navigate('/business-products')}
        onBusinessSalesClick={() => navigate('/business-sales')}
        onAIRecommendationsClick={() => navigate('/ai-recommendations')}
        onNegotiationsClick={() => navigate('/negotiations')}
        onTopProductsClick={() => navigate('/top-products')}
        onTrendingClick={() => navigate('/trending')}
        onSubscriptionClick={() => navigate('/business-subscription')}
        onAdminClick={() => navigate('/admin')}
      />

      <div className="flex">
        <Sidebar
          isOpen={sidebarOpen}
          currentPage={currentPage as any}
          onProfileClick={() => navigate('/profile')}
          onMarketplaceClick={() => navigate('/marketplace')}
          onMyRecommendationsClick={() => navigate('/my-recommendations')}
          onTopProductsClick={() => navigate('/top-products')}
          onTrendingClick={() => navigate('/trending')}
          onBusinessesClick={() => navigate('/businesses')}
          onAIRecommendationsClick={() => navigate('/ai-recommendations')}
          onBusinessOrdersClick={() => navigate('/business-orders')}
          onBusinessSalesClick={() => navigate('/business-sales')}
          onBusinessProductsClick={() => navigate('/product-management')}
          onBusinessRankingsClick={() => navigate('/business-rankings')}
          onBusinessReviewsClick={() => navigate('/business-reviews')}
          onBusinessAdsClick={() => navigate('/business-ads')}
          onOrdersClick={() => navigate('/orders')}
          onUserReviewsClick={() => navigate('/user-reviews')}
          onUserStatusLevelsClick={() => navigate('/user-status-levels')}
          onUsersClick={() => navigate('/users')}
          onSettingsClick={() => navigate('/settings')}
        />

        <main className="flex-1 transition-all duration-300 md:ml-64">
          <div className="max-w-7xl mx-auto py-6">
            <div className="flex flex-col lg:flex-row">
              {/* Main content */}
              <div className="relative w-full">
                {/* Indicateur de chargement subtil */}
                {!contentLoaded && (
                  <div className="absolute top-0 left-0 w-full h-1 bg-gray-200 overflow-hidden">
                    <div className="h-full bg-blue-500 animate-pulse"></div>
                  </div>
                )}

                <div className={`transition-all duration-300 ${contentLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'}`}>
                  {children}
                </div>
              </div>

              {/* Right sidebar */}
              <RightSidebar onNavigateToTopProducts={() => navigate('/top-rated-products')} />
            </div>
          </div>
        </main>
      </div>

      {/* Footer - affiché seulement quand le contenu est chargé */}
      <div className={`transition-opacity duration-300 ${contentLoaded ? 'opacity-100' : 'opacity-0'}`}>
        <Footer />
      </div>
    </div>
  );
};

function App() {
  return (
    <AuthProvider>
      <NotificationsProvider>
        <ToastProvider>
          <PostsProvider>
            <MarketplaceProvider>
              <FollowProvider>
                <FollowRequestProvider>
              <Routes>
                <Route path="/login" element={<AuthPage />} />
                <Route path="/forgot-password" element={<ForgotPassword />} />
                <Route path="/reset-password" element={<ResetPassword />} />
                <Route path="/" element={<Layout><HomePage /></Layout>} />
                <Route path="/profile" element={<Layout><ProfileRoute /></Layout>} />
                <Route path="/profile/:userId" element={<Layout><ProfileRoute /></Layout>} />
                <Route path="/business/:businessId" element={<Layout><BusinessProfilePage /></Layout>} />
                <Route path="/network" element={<Layout><NetworkPage /></Layout>} />
                <Route path="/marketplace" element={<Layout><MarketplacePage /></Layout>} />
                <Route path="/recommendations" element={<Layout><RecommendationsPage /></Layout>} />
                <Route path="/support" element={<Layout><SupportPage /></Layout>} />
                <Route path="/my-recommendations" element={<Layout><MyRecommendationsPage /></Layout>} />
                <Route path="/top-products" element={<Layout><TopProductsPage /></Layout>} />
                <Route path="/top-rated-products" element={<Layout><TopRatedProductsPage /></Layout>} />
                <Route path="/trending" element={<Layout><TrendingPage /></Layout>} />
                <Route path="/businesses" element={<Layout><BusinessesPage /></Layout>} />
                <Route path="/search" element={<Layout><SearchResultsPage searchQuery="" /></Layout>} />
                <Route path="/ai-recommendations" element={<Layout><AIRecommendationsPage /></Layout>} />
                <Route path="/business-orders" element={<Layout><BusinessOrdersPage /></Layout>} />
                <Route path="/business-sales" element={<Layout><BusinessSalesPage /></Layout>} />
                <Route path="/business-products" element={<Layout><BusinessProductsPage /></Layout>} />
                <Route path="/business-rankings" element={<Layout><BusinessRankingsPage /></Layout>} />
                <Route path="/business-reviews" element={<Layout><BusinessReviewsPage /></Layout>} />
                <Route path="/business-ads" element={<Layout><BusinessAdsPage /></Layout>} />
                <Route path="/offers-and-promotions" element={<Layout><OffersAndPromotionsPage /></Layout>} />
                <Route path="/orders" element={<Layout><OrdersPage /></Layout>} />
                <Route path="/user-reviews" element={<Layout><ReviewsPage /></Layout>} />
                <Route path="/user-status-levels" element={<Layout><UserStatusLevelsPage /></Layout>} />
                <Route path="/users" element={<Layout><UsersPage /></Layout>} />
                <Route path="/test-status" element={<Layout><TestStatusPage /></Layout>} />
                <Route path="/settings" element={<Layout><SettingsPage /></Layout>} />
                <Route path="/negotiations" element={<Layout><NegotiationsPage /></Layout>} />
                <Route path="/product-management" element={<Layout><ProductManagementPage /></Layout>} />
                <Route path="/subscription" element={<Layout><SubscriptionPage /></Layout>} />
                <Route path="/business-subscription" element={<Layout><BusinessSubscriptionPage /></Layout>} />
                <Route path="/payment-demo" element={<Layout><PaymentDemoPage /></Layout>} />
                <Route path="/features" element={<Layout><FeaturesOverviewPage /></Layout>} />
                <Route path="/ads-test" element={<Layout><AdsTestPage /></Layout>} />
                <Route path="/offers-test" element={<Layout><OffersTestPage /></Layout>} />
                <Route path="/offers-layout-test" element={<Layout><OffersLayoutTestPage /></Layout>} />
                <Route path="/ad-restrictions-test" element={<Layout><AdRestrictionsTestPage /></Layout>} />
                <Route path="/ad-buttons-test" element={<Layout><FeedAdCardTest /></Layout>} />
                <Route path="/ad-diagnostic" element={<Layout><AdCampaignDiagnostic /></Layout>} />
                <Route path="/debug-follow-requests" element={<Layout><DebugFollowRequestPage /></Layout>} />
                <Route path="/toast-test" element={<Layout><ToastTestPage /></Layout>} />
                <Route path="/notification-test" element={<Layout><NotificationTestPage /></Layout>} />
                <Route path="/admin" element={<AdminDashboardPage />} />

                {/* Pages légales */}
                <Route path="/about" element={<Layout><AboutPage /></Layout>} />
                <Route path="/privacy" element={<Layout><PrivacyPolicyPage /></Layout>} />
                <Route path="/terms" element={<Layout><TermsOfServicePage /></Layout>} />
                <Route path="/like-test" element={<Layout><LikeButtonTest /></Layout>} />


                <Route path="*" element={<Navigate to="/" />} />
              </Routes>
                <ToastContainer />
                </FollowRequestProvider>
              </FollowProvider>
            </MarketplaceProvider>
          </PostsProvider>
        </ToastProvider>
      </NotificationsProvider>
    </AuthProvider>
  );
}

export default App;
