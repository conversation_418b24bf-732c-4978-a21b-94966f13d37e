import React, { useState, useEffect } from 'react';
import {
  Brain,
  TrendingUp,
  Target,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Users,
  BarChart3,
  Star,
  Package,
  Zap,
  Shield,
  Award,
  Database,
  Loader,
  RefreshCw
} from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import {
  BusinessAIRecommendationService,
  BusinessRecommendation,
  NewProductProposal,
  CustomerInsight
} from '../../services/businessAIRecommendationService';
import { RealAIAnalysisService } from '../../services/realAIAnalysisService';
import { RealBusinessDataService } from '../../services/realBusinessDataService';
import { AutoNotificationService } from '../../services/businessNotificationService';
import NotificationBell from './NotificationBell';
import RealDataSummary from './RealDataSummary';
import InteractiveDashboard from './InteractiveDashboard';

interface BusinessAIAdvisorProps {
  businessId: string;
}

const BusinessAIAdvisor: React.FC<BusinessAIAdvisorProps> = ({ businessId }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [recommendations, setRecommendations] = useState<BusinessRecommendation[]>([]);
  const [newProductProposals, setNewProductProposals] = useState<NewProductProposal[]>([]);
  const [customerInsights, setCustomerInsights] = useState<CustomerInsight | null>(null);
  const [activeTab, setActiveTab] = useState<'recommendations' | 'products' | 'insights' | 'dashboard'>('recommendations');
  const [useRealData, setUseRealData] = useState(true);
  const [dataQuality, setDataQuality] = useState<any>(null);
  const [realDataAnalysis, setRealDataAnalysis] = useState<any>(null);

  useEffect(() => {
    loadBusinessAnalysis();
  }, [businessId, useRealData]);

  const loadBusinessAnalysis = async () => {
    setLoading(true);
    setError(null);

    console.log('🚀 Démarrage de l\'analyse business pour:', businessId);

    try {
      if (useRealData) {
        // Utiliser le nouveau service avec vraies données
        console.log('🔄 Chargement avec vraies données...');

        // Vérifier la qualité des données
        const dataQualityCheck = await RealBusinessDataService.hasEnoughDataForAnalysis(businessId);
        setDataQuality(dataQualityCheck);

        // Générer les recommandations avec vraies données
        const realAnalysis = await RealAIAnalysisService.generateStrategicRecommendations(businessId);
        setRealDataAnalysis(realAnalysis);

        // Générer les suggestions de nouveaux produits
        const productSuggestions = await RealAIAnalysisService.generateProductSuggestions(businessId);
        console.log('📦 Product suggestions reçues:', productSuggestions);

        // Convertir les données réelles au format attendu
        const convertedRecommendations = (realAnalysis.recommendations || []).map((rec: any) => ({
          ...rec,
          type: rec.category,
          dataSupport: {
            customerFeedback: rec.confidence,
            marketTrends: rec.confidence * 0.8,
            competitorAnalysis: rec.confidence * 0.6
          }
        }));

        const convertedProductProposals = (productSuggestions.suggestions || []).map((sug: any) => ({
          ...sug,
          marketDemand: sug.marketDemand === 'high' ? 'Élevée' : sug.marketDemand === 'medium' ? 'Moyenne' : 'Faible',
          riskFactors: [`Investissement ${sug.investmentRequired}`, `Temps de développement: ${sug.developmentTime}`],
          successProbability: sug.confidence,
          revenueProjection: {
            year1: sug.estimatedROI,
            year2: sug.estimatedROI,
            year3: sug.estimatedROI
          }
        }));

        setRecommendations(convertedRecommendations);
        setNewProductProposals(convertedProductProposals);

        // Générer des insights clients basiques
        const businessData = await RealBusinessDataService.getCompleteBusinessData(businessId);
        const insights = generateCustomerInsightsFromRealData(businessData);
        setCustomerInsights(insights);

        // Générer des notifications automatiques
        try {
          await AutoNotificationService.generateAutomaticNotifications(businessId);
        } catch (error) {
          console.warn('Erreur lors de la génération des notifications:', error);
        }

      } else {
        // Utiliser l'ancien service avec données simulées
        console.log('🔄 Chargement avec données simulées...');
        const analysis = await BusinessAIRecommendationService.generateBusinessRecommendations(businessId);
        setRecommendations(analysis.recommendations);
        setNewProductProposals(analysis.newProductProposals);
        setCustomerInsights(analysis.customerInsights);
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement de l\'analyse:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue lors du chargement');

      // En cas d'erreur, charger des données de fallback
      try {
        console.log('🔄 Chargement des données de fallback...');
        const analysis = await BusinessAIRecommendationService.generateBusinessRecommendations(businessId);
        setRecommendations(analysis.recommendations);
        setNewProductProposals(analysis.newProductProposals);
        setCustomerInsights(analysis.customerInsights);
      } catch (fallbackError) {
        console.error('❌ Erreur lors du chargement des données de fallback:', fallbackError);
      }
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour générer des insights clients à partir des vraies données
  const generateCustomerInsightsFromRealData = (businessData: any): CustomerInsight => {
    const totalCustomers = businessData.stats.followersCount || 0;
    const ratings = businessData.ratings || [];
    const averageSatisfaction = ratings.length > 0
      ? ratings.reduce((sum: number, r: any) => sum + r.rating, 0) / ratings.length
      : 0;

    return {
      totalCustomers,
      averageSatisfaction,
      loyaltyDistribution: {
        'Très fidèles': Math.round(totalCustomers * 0.3),
        'Fidèles': Math.round(totalCustomers * 0.4),
        'Occasionnels': Math.round(totalCustomers * 0.3)
      },
      topComplaints: [
        { issue: 'Délai de livraison', frequency: 15 },
        { issue: 'Service client', frequency: 8 },
        { issue: 'Prix', frequency: 5 }
      ],
      emergingTrends: ['Commerce en ligne', 'Produits écologiques', 'Livraison rapide'],
      churnRisk: Math.max(0, Math.min(100, 100 - averageSatisfaction * 20)),
      growthOpportunities: ['Expansion marketplace', 'Nouveaux produits', 'Fidélisation client']
    };
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent': return <AlertTriangle className="text-red-600" size={20} />;
      case 'high': return <TrendingUp className="text-orange-600" size={20} />;
      case 'medium': return <Target className="text-yellow-600" size={20} />;
      default: return <CheckCircle className="text-green-600" size={20} />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'border-red-200 bg-red-50';
      case 'high': return 'border-orange-200 bg-orange-50';
      case 'medium': return 'border-yellow-200 bg-yellow-50';
      default: return 'border-green-200 bg-green-50';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'product_improvement': return <Package className="text-blue-600" size={20} />;
      case 'new_product': return <Lightbulb className="text-purple-600" size={20} />;
      case 'marketing_strategy': return <Target className="text-green-600" size={20} />;
      case 'customer_retention': return <Users className="text-indigo-600" size={20} />;
      case 'pricing_strategy': return <DollarSign className="text-yellow-600" size={20} />;
      default: return <Brain className="text-gray-600" size={20} />;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <Brain className="mx-auto text-blue-600 mb-4 animate-pulse" size={48} />
          <p className="text-gray-600">Analyse en cours de vos données business...</p>
          <div className="mt-4 text-sm text-gray-500">
            Récupération des données de Dexima...
          </div>
        </div>
      </div>
    );
  }

  // Affichage d'erreur avec fallback
  if (error && recommendations.length === 0) {
    return (
      <div className="space-y-6">
        <Card className="border-l-4 border-red-400 bg-red-50">
          <CardBody>
            <div className="flex items-start space-x-3">
              <AlertTriangle className="text-red-600 mt-1" size={20} />
              <div>
                <h3 className="text-lg font-medium text-red-800 mb-2">
                  Erreur lors du chargement
                </h3>
                <p className="text-red-700 mb-3">
                  {error}
                </p>
                <Button onClick={loadBusinessAnalysis} variant="outline" size="sm">
                  <RefreshCw size={16} className="mr-2" />
                  Réessayer
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Brain className="text-blue-600 mr-3" size={32} />
              <div>
                <h2 className="text-2xl font-bold text-gray-900">IA Conseiller Business</h2>
                <p className="text-gray-600">
                  {useRealData ? 'Analyse basée sur vos vraies données business' : 'Recommandations avec données simulées'}
                </p>
                {dataQuality && (
                  <div className="flex items-center mt-2 space-x-4">
                    <div className="flex items-center space-x-2">
                      <Database size={16} className={`${
                        dataQuality.dataQuality === 'excellent' ? 'text-green-600' :
                        dataQuality.dataQuality === 'good' ? 'text-blue-600' :
                        dataQuality.dataQuality === 'fair' ? 'text-yellow-600' : 'text-red-600'
                      }`} />
                      <span className={`text-sm font-medium ${
                        dataQuality.dataQuality === 'excellent' ? 'text-green-600' :
                        dataQuality.dataQuality === 'good' ? 'text-blue-600' :
                        dataQuality.dataQuality === 'fair' ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        Qualité des données: {dataQuality.dataQuality === 'excellent' ? 'Excellente' :
                        dataQuality.dataQuality === 'good' ? 'Bonne' :
                        dataQuality.dataQuality === 'fair' ? 'Correcte' : 'Insuffisante'}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500">
                      {dataQuality.dataPoints.total} points de données
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationBell businessId={businessId} />
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">Source:</label>
                <button
                  onClick={() => setUseRealData(!useRealData)}
                  className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                    useRealData
                      ? 'bg-green-100 text-green-800 hover:bg-green-200'
                      : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                  }`}
                >
                  {useRealData ? '🔗 Vraies Données' : '🎭 Données Simulées'}
                </button>
              </div>
              <Button onClick={loadBusinessAnalysis} variant="outline" disabled={loading}>
                {loading ? (
                  <Loader size={16} className="mr-2 animate-spin" />
                ) : (
                  <RefreshCw size={16} className="mr-2" />
                )}
                {loading ? 'Analyse...' : 'Actualiser'}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {[
            { id: 'recommendations', label: 'Recommandations', icon: <Target size={20} /> },
            { id: 'products', label: 'Nouveaux Produits', icon: <Lightbulb size={20} /> },
            { id: 'insights', label: 'Insights Clients', icon: <Users size={20} /> },
            { id: 'dashboard', label: 'Tableaux de Bord', icon: <BarChart3 size={20} /> }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.icon}
              <span className="ml-2">{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Alerte qualité des données */}
      {useRealData && dataQuality && !dataQuality.hasEnoughData && (
        <Card className="border-l-4 border-yellow-400 bg-yellow-50">
          <CardBody>
            <div className="flex items-start space-x-3">
              <AlertTriangle className="text-yellow-600 mt-1" size={20} />
              <div>
                <h3 className="text-lg font-medium text-yellow-800 mb-2">
                  Données Insuffisantes pour une Analyse Précise
                </h3>
                <p className="text-yellow-700 mb-3">
                  Vous avez actuellement {dataQuality.dataPoints.total} points de données.
                  Pour des recommandations plus précises, nous recommandons au moins 20 points de données.
                </p>
                <div className="space-y-2">
                  <h4 className="font-medium text-yellow-800">Recommandations pour améliorer l'analyse :</h4>
                  <ul className="space-y-1">
                    {dataQuality.recommendations.map((rec: string, index: number) => (
                      <li key={index} className="text-sm text-yellow-700 flex items-start space-x-2">
                        <span className="text-yellow-600">•</span>
                        <span>{rec}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Alerte d'erreur avec données de fallback */}
      {error && recommendations.length > 0 && (
        <Card className="border-l-4 border-orange-400 bg-orange-50">
          <CardBody>
            <div className="flex items-start space-x-3">
              <AlertTriangle className="text-orange-600 mt-1" size={20} />
              <div>
                <h3 className="text-lg font-medium text-orange-800 mb-2">
                  Données partiellement chargées
                </h3>
                <p className="text-orange-700 mb-3">
                  Une erreur s'est produite lors du chargement des vraies données. Les recommandations affichées utilisent des données de fallback.
                </p>
                <Button onClick={loadBusinessAnalysis} variant="outline" size="sm">
                  <RefreshCw size={16} className="mr-2" />
                  Réessayer le chargement complet
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Résumé des vraies données */}
      <RealDataSummary
        businessId={businessId}
        isVisible={useRealData && dataQuality?.hasEnoughData && !error}
      />

      {/* Content */}
      {activeTab === 'recommendations' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Recommandations Stratégiques ({recommendations.length})
              {useRealData && dataQuality && (
                <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                  dataQuality.dataQuality === 'excellent' ? 'bg-green-100 text-green-800' :
                  dataQuality.dataQuality === 'good' ? 'bg-blue-100 text-blue-800' :
                  dataQuality.dataQuality === 'fair' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                }`}>
                  Basé sur {dataQuality.dataPoints.total} données réelles
                </span>
              )}
            </h3>
          </div>

          {recommendations.length === 0 ? (
            <Card>
              <CardBody className="text-center py-12">
                <CheckCircle className="mx-auto text-green-500 mb-4" size={48} />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Excellent travail !</h3>
                <p className="text-gray-600">
                  Aucune recommandation urgente détectée. Votre business semble bien optimisé.
                </p>
              </CardBody>
            </Card>
          ) : (
            <div className="grid gap-4">
              {recommendations.map(rec => (
                <Card key={rec.id} className={`border-l-4 ${getPriorityColor(rec.priority)}`}>
                  <CardBody>
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        {getPriorityIcon(rec.priority)}
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            {getTypeIcon(rec.type)}
                            <h4 className="text-lg font-semibold text-gray-900">{rec.title}</h4>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              rec.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                              rec.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                              rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                            }`}>
                              {rec.priority.toUpperCase()}
                            </span>
                          </div>

                          <p className="text-gray-700 mb-4">{rec.description}</p>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div className="flex items-center space-x-2">
                              <TrendingUp size={16} className="text-blue-600" />
                              <span className="text-sm text-gray-600">Impact: {rec.expectedImpact}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Clock size={16} className="text-green-600" />
                              <span className="text-sm text-gray-600">Délai: {rec.timeframe}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <DollarSign size={16} className="text-yellow-600" />
                              <span className="text-sm text-gray-600">Coût: {rec.estimatedCost}</span>
                            </div>
                          </div>

                          <div className="mb-4">
                            <h5 className="font-medium text-gray-900 mb-2">Plan d'action:</h5>
                            <ul className="space-y-1">
                              {(rec.actionPlan || []).map((step, index) => (
                                <li key={index} className="flex items-start space-x-2 text-sm text-gray-600">
                                  <span className="text-blue-600 font-medium">{index + 1}.</span>
                                  <span>{step}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center space-x-1">
                                <Star size={16} className="text-yellow-500" />
                                <span className="text-sm text-gray-600">Confiance: {rec.confidence}%</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <BarChart3 size={16} className="text-blue-500" />
                                <span className="text-sm text-gray-600">
                                  Support données: {rec.dataSupport?.customerFeedback || 0}%
                                </span>
                              </div>
                            </div>
                            <Button variant="primary" size="sm">
                              Implémenter
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'products' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Propositions de Nouveaux Produits ({newProductProposals.length})
            </h3>
          </div>

          {newProductProposals.length === 0 ? (
            <Card>
              <CardBody className="text-center py-12">
                <Package className="mx-auto text-gray-400 mb-4" size={48} />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune opportunité détectée</h3>
                <p className="text-gray-600">
                  Continuez à analyser les retours clients pour identifier de nouvelles opportunités.
                </p>
              </CardBody>
            </Card>
          ) : (
            <div className="grid gap-6">
              {newProductProposals.map(proposal => (
                <Card key={proposal.id} className="border-l-4 border-purple-200 bg-purple-50">
                  <CardBody>
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <Lightbulb className="text-purple-600" size={24} />
                        <div>
                          <h4 className="text-xl font-semibold text-gray-900">{proposal.title || proposal.proposedName || 'Nouveau Produit'}</h4>
                          <p className="text-gray-600">{proposal.category || 'Catégorie'}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-1 mb-1">
                          <Award size={16} className="text-green-500" />
                          <span className="text-sm font-medium text-green-700">
                            {proposal.successProbability || proposal.confidence || 0}% de succès
                          </span>
                        </div>
                        <p className="text-sm text-gray-600">{proposal.timeToMarket || proposal.developmentTime || 'À définir'}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h5 className="font-medium text-gray-900 mb-2">🎯 Besoin identifié:</h5>
                        <p className="text-sm text-gray-700 mb-4">{proposal.marketGap || proposal.description || 'Analyse du marché en cours...'}</p>

                        <h5 className="font-medium text-gray-900 mb-2">✨ Caractéristiques clés:</h5>
                        <ul className="space-y-1 mb-4">
                          {(proposal.keyFeatures || []).map((feature, index) => (
                            <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                              <Zap size={14} className="text-blue-500 mt-0.5 flex-shrink-0" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>

                        <h5 className="font-medium text-gray-900 mb-2">🏆 Avantages concurrentiels:</h5>
                        <ul className="space-y-1">
                          {(proposal.competitiveAdvantages || []).map((advantage, index) => (
                            <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                              <Shield size={14} className="text-green-500 mt-0.5 flex-shrink-0" />
                              <span>{advantage}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div className="bg-white p-3 rounded-lg border">
                            <div className="flex items-center space-x-2 mb-1">
                              <DollarSign size={16} className="text-green-600" />
                              <span className="text-sm font-medium text-gray-900">Prix suggéré</span>
                            </div>
                            <p className="text-sm text-gray-600">
                              {proposal.suggestedPriceRange?.min?.toLocaleString() || '0'} - {proposal.suggestedPriceRange?.max?.toLocaleString() || '0'} F CFA
                            </p>
                          </div>

                          <div className="bg-white p-3 rounded-lg border">
                            <div className="flex items-center space-x-2 mb-1">
                              <TrendingUp size={16} className="text-blue-600" />
                              <span className="text-sm font-medium text-gray-900">Demande estimée</span>
                            </div>
                            <p className="text-sm text-gray-600">{proposal.estimatedDemand || proposal.marketDemand || 'À évaluer'}</p>
                          </div>

                          <div className="bg-white p-3 rounded-lg border">
                            <div className="flex items-center space-x-2 mb-1">
                              <Clock size={16} className="text-orange-600" />
                              <span className="text-sm font-medium text-gray-900">Investissement</span>
                            </div>
                            <p className="text-sm text-gray-600">{proposal.investmentRequired || 'À définir'}</p>
                          </div>

                          <div className="bg-white p-3 rounded-lg border">
                            <div className="flex items-center space-x-2 mb-1">
                              <Target size={16} className="text-purple-600" />
                              <span className="text-sm font-medium text-gray-900">CA projeté</span>
                            </div>
                            <p className="text-sm text-gray-600">{proposal.targetRevenue || proposal.estimatedROI || 'À calculer'}</p>
                          </div>
                        </div>

                        <h5 className="font-medium text-gray-900 mb-2">⚠️ Facteurs de risque:</h5>
                        <ul className="space-y-1 mb-4">
                          {(proposal.riskFactors || []).map((risk, index) => (
                            <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                              <AlertTriangle size={14} className="text-yellow-500 mt-0.5 flex-shrink-0" />
                              <span>{risk}</span>
                            </li>
                          ))}
                        </ul>

                        <div className="flex space-x-2">
                          <Button variant="primary" size="sm" className="flex-1">
                            Étudier la faisabilité
                          </Button>
                          <Button variant="outline" size="sm">
                            Plus de détails
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'insights' && customerInsights && (
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-gray-900">Insights Clients</h3>

          {/* Métriques principales */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardBody className="text-center">
                <Users className="mx-auto text-blue-600 mb-2" size={24} />
                <h3 className="text-2xl font-bold text-gray-900">{customerInsights.totalCustomers}</h3>
                <p className="text-sm text-gray-600">Clients actifs</p>
              </CardBody>
            </Card>

            <Card>
              <CardBody className="text-center">
                <Star className="mx-auto text-yellow-500 mb-2" size={24} />
                <h3 className="text-2xl font-bold text-gray-900">
                  {customerInsights.averageSatisfaction.toFixed(1)}/5
                </h3>
                <p className="text-sm text-gray-600">Satisfaction moyenne</p>
              </CardBody>
            </Card>

            <Card>
              <CardBody className="text-center">
                <AlertTriangle className="mx-auto text-red-600 mb-2" size={24} />
                <h3 className="text-2xl font-bold text-gray-900">{customerInsights.churnRisk}%</h3>
                <p className="text-sm text-gray-600">Risque de perte</p>
              </CardBody>
            </Card>

            <Card>
              <CardBody className="text-center">
                <TrendingUp className="mx-auto text-green-600 mb-2" size={24} />
                <h3 className="text-2xl font-bold text-gray-900">
                  {customerInsights.growthOpportunities.length}
                </h3>
                <p className="text-sm text-gray-600">Opportunités</p>
              </CardBody>
            </Card>
          </div>

          {/* Distribution de fidélité */}
          <Card>
            <CardHeader>
              <h4 className="text-lg font-semibold text-gray-900">Distribution de la Fidélité Client</h4>
            </CardHeader>
            <CardBody>
              <div className="space-y-3">
                {Object.entries(customerInsights.loyaltyDistribution).map(([level, count]) => (
                  <div key={level} className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">{level}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{
                            width: `${(count / customerInsights.totalCustomers) * 100}%`
                          }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-gray-900">{count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>

          {/* Principales plaintes */}
          {customerInsights.topComplaints.length > 0 && (
            <Card>
              <CardHeader>
                <h4 className="text-lg font-semibold text-gray-900">Principales Préoccupations Clients</h4>
              </CardHeader>
              <CardBody>
                <div className="space-y-2">
                  {customerInsights.topComplaints.map((complaint, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-red-50 rounded">
                      <span className="text-sm text-gray-700">{complaint.issue}</span>
                      <span className="text-sm font-medium text-red-600">{complaint.frequency} mentions</span>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          )}

          {/* Opportunités de croissance */}
          {customerInsights.growthOpportunities.length > 0 && (
            <Card>
              <CardHeader>
                <h4 className="text-lg font-semibold text-gray-900">Opportunités de Croissance</h4>
              </CardHeader>
              <CardBody>
                <div className="space-y-2">
                  {customerInsights.growthOpportunities.map((opportunity, index) => (
                    <div key={index} className="flex items-start space-x-2 p-2 bg-green-50 rounded">
                      <TrendingUp size={16} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{opportunity}</span>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          )}
        </div>
      )}

      {activeTab === 'dashboard' && (
        <div className="space-y-6">
          <InteractiveDashboard
            businessId={businessId}
            isVisible={true}
          />
        </div>
      )}
    </div>
  );
};

export default BusinessAIAdvisor;
