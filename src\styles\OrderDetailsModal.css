/* <PERSON><PERSON> de détails de commande */
.order-details-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.order-details-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* En-tête */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.header-left .order-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.order-title h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.order-title p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

/* Barre de statut */
.order-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.confirmed {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge.processing {
  background: #ede9fe;
  color: #6b21a8;
}

.status-badge.shipped {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.delivered {
  background: #dcfce7;
  color: #166534;
}

.status-badge.cancelled {
  background: #fee2e2;
  color: #991b1b;
}

.payment-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.payment-badge.paid {
  background: #d1fae5;
  color: #065f46;
}

.payment-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

.payment-badge.failed {
  background: #fee2e2;
  color: #991b1b;
}

.total-amount {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.quick-actions {
  display: flex;
  gap: 0.5rem;
}

/* Onglets */
.modal-tabs {
  display: flex;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab:hover {
  color: #374151;
  background: #f3f4f6;
}

.tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: white;
}

/* Contenu */
.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.details-tab,
.customer-tab,
.shipping-tab,
.timeline-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Produits */
.order-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.order-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.order-item img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
}

.item-details {
  flex: 1;
}

.item-name {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.item-sku {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.item-price {
  font-size: 0.875rem;
  color: #374151;
}

.item-total {
  font-weight: 600;
  color: #1f2937;
  font-size: 1rem;
}

/* Résumé financier */
.financial-summary {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.summary-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.summary-line.discount {
  color: #059669;
}

.summary-line.total {
  border-top: 1px solid #e5e7eb;
  padding-top: 0.75rem;
  font-weight: 600;
  font-size: 1.125rem;
}

.payment-info {
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
}

.payment-method {
  color: #374151;
}

/* Détails client */
.customer-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 8px;
}

.detail-row span:nth-child(2) {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.detail-row span:nth-child(3) {
  flex: 1;
  color: #1f2937;
}

.customer-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

/* Livraison */
.shipping-address {
  margin-bottom: 1.5rem;
}

.address-details {
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.delivery-instructions {
  padding: 1rem;
  background: #fef3c7;
  border-radius: 8px;
  border-left: 4px solid #f59e0b;
}

.delivery-instructions p {
  margin: 0.5rem 0 0 0;
  color: #92400e;
}

.tracking-info {
  padding: 1rem;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.tracking-info h4 {
  margin: 0 0 1rem 0;
  color: #1e40af;
}

.tracking-number {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.estimated-delivery {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1e40af;
  font-size: 0.875rem;
}

/* Timeline */
.order-timeline {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-left: 2px solid #e5e7eb;
  position: relative;
}

.timeline-item:last-child {
  border-left-color: transparent;
}

.timeline-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.timeline-date {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.timeline-note {
  font-size: 0.75rem;
  color: #374151;
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
}

/* Modales de mise à jour */
.update-modal {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.update-content {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  min-width: 300px;
}

.update-content h4 {
  margin: 0 0 1rem 0;
  color: #1f2937;
}

.update-content select,
.update-content input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.update-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* Responsive */
@media (max-width: 768px) {
  .order-details-modal {
    margin: 0;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }

  .order-status-bar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .status-info {
    justify-content: space-between;
  }

  .quick-actions {
    justify-content: center;
  }

  .modal-tabs {
    overflow-x: auto;
  }

  .tab {
    white-space: nowrap;
    min-width: fit-content;
  }

  .customer-actions {
    flex-direction: column;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .detail-row span:nth-child(2) {
    min-width: auto;
  }
}
