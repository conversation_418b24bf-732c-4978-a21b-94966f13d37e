import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { usePosts } from '../context/PostsContext';
import { useFollow } from '../context/FollowContext';
import { supabase } from '../lib/supabase';
import { IUser, UserRole, UserStatus } from '../types';
import EditProfileModal from '../components/profile/EditProfileModal';
import ProfileBanner from '../components/profile/ProfileBanner';
import ProfileStats from '../components/profile/ProfileStats';
import ProfileTabs from '../components/profile/ProfileTabs';

interface ProfilePageProps {
  userId?: string;
}

const ProfilePage: React.FC<ProfilePageProps> = ({ userId }) => {
  const { currentUser, updateUserProfile } = useAuth();
  const { getPostsByUser } = usePosts();
  const { isFollowing, followUser, unfollowUser } = useFollow();
  
  const [activeTab, setActiveTab] = useState('statut');
  const [profileUser, setProfileUser] = useState<IUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [isFollowingUser, setIsFollowingUser] = useState(false);
  const [followLoading, setFollowLoading] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Déterminer l'ID du profil à afficher
  const effectiveUserId = userId || currentUser?.id;

  useEffect(() => {
    const fetchProfileUser = async () => {
      setLoading(true);
      try {
        if (!effectiveUserId) {
          setLoading(false);
          return;
        }
        
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', effectiveUserId)
          .single();
        
        if (profileError) throw profileError;
        
        let userData: IUser = {
          id: profileData.id,
          username: profileData.username,
          email: profileData.email,
          profilePicture: profileData.profile_picture,
          coverPhotoUrl: profileData.cover_photo,
          role: profileData.role as UserRole,
          createdAt: typeof profileData.created_at === 'string' ? profileData.created_at : new Date(profileData.created_at).toISOString(),
          status: profileData.status as UserStatus,
          post_count: profileData.post_count || 0,
          city: profileData.city,
          country: profileData.country
        };
        
        setProfileUser(userData);
        
        if (currentUser && effectiveUserId !== currentUser.id) {
          const following = await isFollowing(effectiveUserId);
          setIsFollowingUser(following);
        }
        
        // Fix: Map posts to match IPost type in types.ts
        // Removed mapping for updatedAt and setUserPosts, as they are not needed and caused errors
      } catch (error) {
        console.error('Erreur lors de la récupération du profil:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchProfileUser();
  }, [effectiveUserId, currentUser, getPostsByUser, isFollowing]);

  const handleFollowToggle = async () => {
    if (!currentUser || !profileUser) return;
    setFollowLoading(true);
    try {
      if (isFollowingUser) {
        await unfollowUser(profileUser.id);
        setIsFollowingUser(false);
      } else {
        await followUser(profileUser.id);
        setIsFollowingUser(true);
      }
    } catch (error) {
      console.error("Erreur lors de la modification de l'abonnement:", error);
    } finally {
      setFollowLoading(false);
    }
  };

  const handleProfileUpdate = async (updatedData: any) => {
    try {
      setLoading(true);
      if (!currentUser) throw new Error('Utilisateur non authentifié.');
      await updateUserProfile(currentUser.id, updatedData);
      // Always refetch profile data from Supabase after update
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', effectiveUserId)
        .single();
      if (!profileError && profileData) {
        setProfileUser({
          ...profileUser!,
          ...profileData
        });
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }
  
  if (!profileUser) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Profil introuvable</h2>
          <p className="text-gray-600">Le profil que vous recherchez n'existe pas ou a été supprimé.</p>
        </div>
      </div>
    );
  }

  const isOwnProfile = currentUser?.id === profileUser.id;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Profile Banner */}
        <ProfileBanner
          user={profileUser}
          isFollowing={isFollowingUser}
          onFollow={handleFollowToggle}
          isLoading={followLoading}
          isOwnProfile={isOwnProfile}
          onProfileUpdate={handleProfileUpdate}
        />

        {/* Main Content */}
        <div className="mt-8 grid grid-cols-12 gap-8">
          {/* Left Sidebar */}
          <div className="col-span-12 lg:col-span-4 space-y-6">
            <ProfileStats user={profileUser} />
          </div>

          {/* Main Content Area */}
          <div className="col-span-12 lg:col-span-8">
            <ProfileTabs activeTab={activeTab} onTabChange={setActiveTab} />
            {/* Tab content will go here */}
          </div>
        </div>
      </div>

      {/* Edit Profile Modal */}
      {isOwnProfile && (
        <EditProfileModal
          user={profileUser}
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSave={handleProfileUpdate}
        />
      )}
    </div>
  );
};

export default ProfilePage;
