import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { UserStatusService, StatusLevel } from '../services/userStatusService';
import { UserStatus } from '../types';
import Card, { CardBody } from '../components/ui/Card';
import { ChevronRight, CheckCircle, Clock, Star, Users, MessageSquare, Heart, Calendar, TrendingUp } from 'lucide-react';

const UserStatusLevelsPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [statusLevels] = useState<StatusLevel[]>(UserStatusService.getAllStatusLevels());
  const [userProgress, setUserProgress] = useState<any>(null);

  useEffect(() => {
    if (currentUser && currentUser.status) {
      try {
        // Calculer le progrès de l'utilisateur vers le niveau suivant
        const userStats = {
          profileComplete: !!(currentUser.profilePicture && currentUser.coverPhotoUrl && currentUser.country && currentUser.city),
          postCount: currentUser.post_count || 0,
          commentCount: currentUser.comment_count || 0,
          likesReceived: 0, // À implémenter avec les vraies données
          followersCount: currentUser.followers_count || 0,
          recommendationsCount: currentUser.recommendations_count || 0,
          accountAgeDays: Math.floor((Date.now() - new Date(currentUser.createdAt).getTime()) / (1000 * 60 * 60 * 24)),
          qualityScore: 7, // À implémenter avec un vrai calcul
          engagementRate: 10 // À implémenter avec un vrai calcul
        };

        const progress = UserStatusService.calculateProgressToNextLevel(currentUser.status, userStats);
        setUserProgress(progress);
      } catch (error) {
        console.error('Erreur lors du calcul du progrès:', error);
        setUserProgress({
          nextLevel: null,
          progress: 0,
          missingCriteria: ['Erreur lors du calcul du progrès']
        });
      }
    }
  }, [currentUser]);

  const getCurrentStatusIndex = () => {
    if (!currentUser) return 0;
    return statusLevels.findIndex(level => level.status === currentUser.status);
  };

  const isStatusUnlocked = (index: number) => {
    return index <= getCurrentStatusIndex();
  };

  const isCurrentStatus = (status: UserStatus) => {
    return currentUser?.status === status;
  };

  const renderStatusIcon = (level: StatusLevel | null, index: number) => {
    if (!level) {
      return (
        <div className="flex items-center justify-center w-16 h-16 rounded-full border-4 border-gray-300 bg-gray-100 text-gray-400">
          <Clock size={24} />
        </div>
      );
    }

    const IconComponent = level.icon;
    const isUnlocked = isStatusUnlocked(index);
    const isCurrent = isCurrentStatus(level.status);

    return (
      <div className={`flex items-center justify-center w-16 h-16 rounded-full border-4 ${
        isCurrent
          ? `border-${level.color.replace('#', '')} ${level.bgColor} ${level.textColor}`
          : isUnlocked
            ? 'border-green-500 bg-green-100 text-green-600'
            : 'border-gray-300 bg-gray-100 text-gray-400'
      }`}>
        {isCurrent ? (
          <IconComponent size={24} />
        ) : isUnlocked ? (
          <CheckCircle size={24} />
        ) : (
          <Clock size={24} />
        )}
      </div>
    );
  };

  const renderProgressBar = (progress: number) => (
    <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
      <div
        className="bg-blue-600 h-3 rounded-full transition-all duration-300"
        style={{ width: `${progress}%` }}
      ></div>
    </div>
  );

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Niveaux de Statut Utilisateur</h1>
        <p className="text-lg text-gray-600 max-w-3xl mx-auto">
          Progressez dans la communauté en participant activement. Chaque niveau débloque de nouveaux avantages et privilèges.
        </p>
      </div>

      {/* Statut actuel et progrès */}
      {currentUser && userProgress && currentUser.status && (
        <Card className="mb-8 border-2 border-blue-200">
          <CardBody>
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Votre Statut Actuel</h2>
              <div className="flex items-center justify-center mb-4">
                {renderStatusIcon(UserStatusService.getStatusLevel(currentUser.status), getCurrentStatusIndex())}
                <div className="ml-4 text-left">
                  {(() => {
                    const currentLevel = UserStatusService.getStatusLevel(currentUser.status);
                    return currentLevel ? (
                      <>
                        <h3 className="text-lg font-medium text-gray-900">
                          {currentLevel.name}
                        </h3>
                        <p className="text-gray-600">
                          {currentLevel.description}
                        </p>
                      </>
                    ) : (
                      <>
                        <h3 className="text-lg font-medium text-gray-900">
                          Statut inconnu
                        </h3>
                        <p className="text-gray-600">
                          Statut utilisateur non reconnu
                        </p>
                      </>
                    );
                  })()}
                </div>
              </div>

              {userProgress.nextLevel && (
                <div className="mt-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      Progrès vers {userProgress.nextLevel.name}
                    </span>
                    <span className="text-sm font-medium text-blue-600">
                      {userProgress.progress}%
                    </span>
                  </div>
                  {renderProgressBar(userProgress.progress)}

                  {userProgress.missingCriteria.length > 0 && (
                    <div className="text-left">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Critères manquants :</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {userProgress.missingCriteria.map((criteria, index) => (
                          <li key={index} className="flex items-center">
                            <Clock size={14} className="mr-2 text-orange-500" />
                            {criteria}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Timeline des niveaux */}
      <div className="space-y-6">
        {statusLevels.map((level, index) => {
          const isUnlocked = isStatusUnlocked(index);
          const isCurrent = isCurrentStatus(level.status);
          const isNext = userProgress?.nextLevel?.status === level.status;

          return (
            <Card
              key={level.status}
              className={`transition-all duration-300 ${
                isCurrent
                  ? 'border-2 border-blue-500 shadow-lg'
                  : isNext
                    ? 'border-2 border-orange-300 shadow-md'
                    : isUnlocked
                      ? 'border-green-200'
                      : 'border-gray-200 opacity-75'
              }`}
            >
              <CardBody>
                <div className="flex items-start space-x-6">
                  {/* Icône et connexion */}
                  <div className="flex flex-col items-center">
                    {renderStatusIcon(level, index)}
                    {index < statusLevels.length - 1 && (
                      <div className={`w-1 h-16 mt-4 ${
                        isUnlocked ? 'bg-green-300' : 'bg-gray-200'
                      }`}></div>
                    )}
                  </div>

                  {/* Contenu */}
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h3 className={`text-xl font-semibold ${
                          isCurrent ? 'text-blue-600' : isUnlocked ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          {level.name}
                          {isCurrent && <span className="ml-2 text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Actuel</span>}
                          {isNext && <span className="ml-2 text-sm bg-orange-100 text-orange-800 px-2 py-1 rounded-full">Suivant</span>}
                        </h3>
                        <p className="text-gray-600 mt-1">{level.description}</p>
                      </div>
                    </div>

                    {/* Critères */}
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Critères requis :</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {UserStatusService.formatCriteria(level.criteria).map((criteria, criteriaIndex) => (
                          <div key={criteriaIndex} className="flex items-center text-sm text-gray-600">
                            <CheckCircle size={14} className={`mr-2 ${isUnlocked ? 'text-green-500' : 'text-gray-400'}`} />
                            {criteria}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Avantages */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Avantages :</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {level.benefits.map((benefit, benefitIndex) => (
                          <div key={benefitIndex} className="flex items-center text-sm text-gray-600">
                            <Star size={14} className={`mr-2 ${isUnlocked ? 'text-yellow-500' : 'text-gray-400'}`} />
                            {benefit}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          );
        })}
      </div>

      {/* Conseils pour progresser */}
      <Card className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50">
        <CardBody>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">💡 Conseils pour progresser</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center">
              <MessageSquare className="mx-auto text-blue-600 mb-2" size={32} />
              <h4 className="font-medium text-gray-900">Publiez des avis</h4>
              <p className="text-sm text-gray-600">Partagez vos expériences authentiques</p>
            </div>
            <div className="text-center">
              <Users className="mx-auto text-green-600 mb-2" size={32} />
              <h4 className="font-medium text-gray-900">Interagissez</h4>
              <p className="text-sm text-gray-600">Commentez et aidez la communauté</p>
            </div>
            <div className="text-center">
              <Heart className="mx-auto text-red-600 mb-2" size={32} />
              <h4 className="font-medium text-gray-900">Soyez apprécié</h4>
              <p className="text-sm text-gray-600">Créez du contenu de qualité</p>
            </div>
            <div className="text-center">
              <TrendingUp className="mx-auto text-purple-600 mb-2" size={32} />
              <h4 className="font-medium text-gray-900">Restez actif</h4>
              <p className="text-sm text-gray-600">Participez régulièrement</p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default UserStatusLevelsPage;
