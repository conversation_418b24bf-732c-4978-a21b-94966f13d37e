import React from 'react';
import { AdCampaign } from '../../types';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, <PERSON>C<PERSON>, <PERSON>, Cell } from 'recharts';
import '../../styles/AdPerformanceCharts.css';

interface AdPerformanceChartsProps {
  campaign: AdCampaign | null;
  allCampaigns: AdCampaign[];
}

const AdPerformanceCharts: React.FC<AdPerformanceChartsProps> = ({ campaign, allCampaigns }) => {
  // Couleurs pour les graphiques
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];
  
  // Données pour le graphique d'évolution des clics et impressions (simulées)
  const generateDailyData = (campaign: AdCampaign) => {
    if (!campaign) return [];
    
    const startDate = new Date(campaign.startDate);
    const endDate = new Date(campaign.endDate);
    const today = new Date();
    const currentDate = endDate < today ? endDate : today;
    
    const days = Math.ceil((currentDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const dailyImpressions = campaign.impressions / (days || 1);
    const dailyClicks = campaign.clicks / (days || 1);
    
    const data = [];
    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      
      // Ajouter une variation aléatoire pour rendre les données plus réalistes
      const randomFactor = 0.5 + Math.random();
      const randomFactor2 = 0.5 + Math.random();
      
      data.push({
        date: date.toLocaleDateString('fr-FR'),
        impressions: Math.round(dailyImpressions * randomFactor),
        clics: Math.round(dailyClicks * randomFactor2)
      });
    }
    
    return data;
  };
  
  // Données pour le graphique de comparaison des campagnes
  const generateCampaignsComparisonData = () => {
    return allCampaigns.map(campaign => ({
      name: campaign.title.length > 15 ? campaign.title.substring(0, 15) + '...' : campaign.title,
      ctr: campaign.ctr,
      budget: campaign.budget / 1000, // Diviser par 1000 pour une meilleure échelle
      impressions: campaign.impressions / 1000 // Diviser par 1000 pour une meilleure échelle
    }));
  };
  
  // Données pour le graphique en camembert des statuts
  const generateStatusData = () => {
    const statusCount = {
      active: 0,
      paused: 0,
      completed: 0,
      draft: 0
    };
    
    allCampaigns.forEach(campaign => {
      statusCount[campaign.status as keyof typeof statusCount]++;
    });
    
    return [
      { name: 'Actives', value: statusCount.active, color: '#4CAF50' },
      { name: 'En pause', value: statusCount.paused, color: '#FFC107' },
      { name: 'Terminées', value: statusCount.completed, color: '#9E9E9E' },
      { name: 'Brouillons', value: statusCount.draft, color: '#2196F3' }
    ].filter(item => item.value > 0);
  };
  
  // Si aucune campagne n'est sélectionnée, afficher les statistiques globales
  if (!campaign) {
    return (
      <div className="ad-performance-charts">
        <h2>Performance de toutes les campagnes</h2>
        
        <div className="charts-grid">
          <div className="chart-container">
            <h3>Comparaison des campagnes (CTR %)</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart
                data={generateCampaignsComparisonData()}
                margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" angle={-45} textAnchor="end" height={70} />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}%`, 'CTR']} />
                <Legend />
                <Bar dataKey="ctr" name="Taux de clics (%)" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          
          <div className="chart-container">
            <h3>Statut des campagnes</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={generateStatusData()}
                  cx="50%"
                  cy="50%"
                  labelLine={true}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  nameKey="name"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {generateStatusData().map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [value, 'Nombre de campagnes']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
          
          <div className="chart-container">
            <h3>Budget vs Impressions</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart
                data={generateCampaignsComparisonData()}
                margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" angle={-45} textAnchor="end" height={70} />
                <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                <Tooltip formatter={(value, name) => [
                  name === 'budget' ? `${value * 1000} F CFA` : `${value * 1000}`,
                  name === 'budget' ? 'Budget' : 'Impressions'
                ]} />
                <Legend />
                <Bar yAxisId="left" dataKey="budget" name="Budget (milliers F CFA)" fill="#8884d8" />
                <Bar yAxisId="right" dataKey="impressions" name="Impressions (milliers)" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    );
  }
  
  // Afficher les statistiques d'une campagne spécifique
  return (
    <div className="ad-performance-charts">
      <h2>Performance de la campagne: {campaign.title}</h2>
      
      <div className="charts-grid">
        <div className="chart-container">
          <h3>Évolution des clics et impressions</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart
              data={generateDailyData(campaign)}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
              <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
              <Tooltip />
              <Legend />
              <Line yAxisId="left" type="monotone" dataKey="impressions" name="Impressions" stroke="#8884d8" activeDot={{ r: 8 }} />
              <Line yAxisId="right" type="monotone" dataKey="clics" name="Clics" stroke="#82ca9d" />
            </LineChart>
          </ResponsiveContainer>
        </div>
        
        <div className="chart-container">
          <h3>Statistiques clés</h3>
          <div className="campaign-stats">
            <div className="stat-card">
              <div className="stat-value">{campaign.impressions.toLocaleString()}</div>
              <div className="stat-label">Impressions</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{campaign.clicks.toLocaleString()}</div>
              <div className="stat-label">Clics</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{campaign.ctr.toFixed(2)}%</div>
              <div className="stat-label">CTR</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{campaign.conversions.toLocaleString()}</div>
              <div className="stat-label">Conversions</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{campaign.cpc.toFixed(0)} F</div>
              <div className="stat-label">Coût par clic</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{((campaign.spent / campaign.budget) * 100).toFixed(0)}%</div>
              <div className="stat-label">Budget utilisé</div>
            </div>
          </div>
        </div>
        
        <div className="chart-container">
          <h3>Répartition du budget</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={[
                  { name: 'Dépensé', value: campaign.spent },
                  { name: 'Restant', value: campaign.budget - campaign.spent }
                ]}
                cx="50%"
                cy="50%"
                labelLine={true}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                <Cell fill="#FF8042" />
                <Cell fill="#00C49F" />
              </Pie>
              <Tooltip formatter={(value) => [`${value.toLocaleString()} F CFA`, '']} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default AdPerformanceCharts;
