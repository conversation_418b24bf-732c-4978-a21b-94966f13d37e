import React from 'react';
import { useAuth } from '../context/AuthContext';
import { UserRole, BusinessStatus, IProduct, IBusinessUser } from '../types/index';
import { Navigate } from 'react-router-dom';
import CatalogueManager from '../components/business/CatalogueManager';
import { useState, useEffect } from 'react';

const CataloguePage: React.FC = () => {
  const { currentUser } = useAuth();
  const [businessData, setBusinessData] = useState<IBusinessUser | null>(null);
  const [products, setProducts] = useState<IProduct[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBusinessAndProducts = async () => {
      if (currentUser && currentUser.role === UserRole.BUSINESS) {
        // Construction de l'objet businessData fictif (ou réel si tu veux)
        const business: IBusinessUser = {
          id: currentUser.id,
          username: currentUser.username,
          email: currentUser.email,
          role: UserRole.BUSINESS,
          profilePicture: currentUser.profilePicture,
          businessName: 'Dexima',
          businessStatus: BusinessStatus.VERIFIED,
          businessDescription: '',
          createdAt: typeof currentUser.createdAt === 'string' ? new Date(currentUser.createdAt) : currentUser.createdAt,
          status: currentUser.status,
          following_count: 0,
          followers_count: 0,
          comment_count: 0,
          recommendations_count: 0,
          post_count: 0,
        };
        setBusinessData(business);
        // Charger les produits réels depuis Supabase
        const realProducts = await import('../services/productService').then(s => s.getProductsByBusinessId(business.id));
        // Convertir les produits pour correspondre au type de src/types/index.ts
        const convertedProducts = realProducts.map((p: any) => ({
          ...p,
          createdAt: p.createdAt ? new Date(p.createdAt) : new Date(),
          images: Array.isArray(p.images) ? p.images : [], // Always ensure images is an array
        }));
        setProducts(convertedProducts);
        setLoading(false);
      }
    };
    fetchBusinessAndProducts();
  }, [currentUser]);

  // Rediriger si l'utilisateur n'est pas une entreprise
  if (currentUser && currentUser.role !== UserRole.BUSINESS) {
    return <Navigate to="/profile" replace />;
  }

  // Afficher un message de chargement pendant la récupération des données
  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="sr-only">Chargement...</span>
          </div>
          <p className="mt-2">Chargement de votre catalogue...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">Catalogue de produits et services</h1>
        <p className="text-gray-600 mb-6">
          Gérez vos produits et services, publiez-les sur le marketplace et recueillez des avis de vos clients.
        </p>
        
        {businessData && (
          <CatalogueManager
            businessData={businessData}
            products={products}
            isOwnBusiness={true}
            onProductsUpdate={setProducts}
          />
        )}
      </div>
    </div>
  );
};

export default CataloguePage;
