-- Migration pour ajouter la colonne tracking_number à la table orders

-- Ajout de la colonne tracking_number
ALTER TABLE orders
ADD COLUMN IF NOT EXISTS tracking_number VARCHAR(50);

-- Ajout d'un index pour améliorer les performances des recherches par numéro de suivi
CREATE INDEX IF NOT EXISTS idx_orders_tracking_number ON orders(tracking_number);

-- Commentaire sur la colonne pour la documentation
COMMENT ON COLUMN orders.tracking_number IS 'Numéro de suivi pour le système de tracking des commandes';

-- Mise à jour de la fonction addTrackingNumber dans le service OrdersService
-- Note: Cette migration ne modifie que la structure de la base de données.
-- Le code TypeScript doit être mis à jour séparément pour utiliser cette nouvelle colonne.