# 🗄️ Migrations du Système de Paiement Customeroom

## 📋 Vue d'Ensemble

Ce dossier contient toutes les migrations SQL nécessaires pour déployer le système de paiement et d'abonnement complet de Customeroom Business.

## 📁 Structure des Fichiers

```
migrations/
├── 001_create_payment_system.sql    # Tables principales et données initiales
├── 002_payment_procedures.sql       # Procédures stockées et fonctions
├── 003_payment_performance.sql      # Optimisations et index de performance
├── deploy_payment_system.sql        # Script de déploiement complet
└── README.md                        # Ce fichier
```

## 🚀 Déploiement Rapide

### Option 1: Déploiement Automatique (Recommandé)

```bash
# Se connecter à PostgreSQL
psql -U your_username -d your_database

# Exécuter le script de déploiement complet
\i migrations/deploy_payment_system.sql
```

### Option 2: Déploiement Manuel

```bash
# Exécuter les migrations dans l'ordre
psql -U your_username -d your_database -f migrations/001_create_payment_system.sql
psql -U your_username -d your_database -f migrations/002_payment_procedures.sql
psql -U your_username -d your_database -f migrations/003_payment_performance.sql
```

## 📊 Tables Créées

### Tables Principales
- **`subscription_plans`** - Plans d'abonnement disponibles
- **`business_subscriptions`** - Abonnements des entreprises
- **`payment_methods`** - Méthodes de paiement enregistrées
- **`payment_intents`** - Intentions de paiement
- **`payment_receipts`** - Reçus de paiement générés
- **`payment_events`** - Journal d'audit des événements
- **`payment_webhooks`** - Webhooks des processeurs de paiement
- **`subscription_coupons`** - Coupons de réduction
- **`coupon_usages`** - Historique d'utilisation des coupons

### Vues Matérialisées
- **`monthly_revenue_stats`** - Statistiques de revenus mensuels
- **`subscription_metrics`** - Métriques d'abonnement par plan
- **`active_subscriptions`** - Vue des abonnements actifs
- **`business_payment_stats`** - Statistiques de paiement par entreprise
- **`plan_revenue_stats`** - Revenus par plan d'abonnement

## ⚙️ Fonctions Principales

### Gestion des Abonnements
```sql
-- Créer un essai gratuit
SELECT * FROM create_free_trial('business_id_uuid');

-- Obtenir le statut d'abonnement
SELECT * FROM get_subscription_status('business_id_uuid');

-- Annuler un abonnement
SELECT * FROM cancel_subscription('business_id_uuid', 'Raison d''annulation');
```

### Traitement des Paiements
```sql
-- Traiter un paiement réussi
SELECT * FROM process_payment('payment_intent_id', 'processor_payment_id');

-- Marquer un paiement comme échoué
SELECT * FROM fail_payment('payment_intent_id', 'failure_code', 'Message d''erreur');
```

### Gestion des Coupons
```sql
-- Vérifier si un coupon peut être utilisé
SELECT can_use_coupon('COUPON_CODE', 'business_id_uuid', 'plan_id', 15000.00);

-- Appliquer un coupon à un paiement
SELECT * FROM apply_coupon('payment_intent_id', 'COUPON_CODE');
```

### Maintenance et Monitoring
```sql
-- Maintenance quotidienne
SELECT * FROM daily_maintenance();

-- Détecter les anomalies
SELECT * FROM detect_payment_anomalies();

-- Nettoyer les anciennes données
SELECT * FROM cleanup_old_data(365); -- Garder 365 jours
```

## 💳 Plans d'Abonnement Pré-configurés

| Plan | Prix (F CFA) | Durée | Fonctionnalités |
|------|-------------|-------|-----------------|
| **Essai Gratuit** | 0 | 7 jours | Accès complet limité |
| **Starter** | 15,000 | 30 jours | 500 avis/mois, API basique |
| **Professional** | 35,000 | 30 jours | Avis illimités, IA avancée |
| **Enterprise** | 75,000 | 30 jours | Multi-comptes, support dédié |

## 🔧 Configuration Post-Déploiement

### 1. Variables d'Environnement

```env
# Base de données
DATABASE_URL=postgresql://user:password@localhost:5432/customeroom
PAYMENT_SCHEMA=public

# Processeurs de paiement
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
ORANGE_MONEY_API_KEY=...
MTN_MONEY_API_KEY=...

# Configuration des reçus
INVOICE_PREFIX=INV
COMPANY_NAME=Customeroom
COMPANY_ADDRESS=Abidjan, Côte d'Ivoire
```

### 2. Tâches Cron Recommandées

```bash
# Maintenance quotidienne à 2h du matin
0 2 * * * psql -d customeroom -c "SELECT daily_maintenance();"

# Rafraîchissement des statistiques toutes les heures
0 * * * * psql -d customeroom -c "SELECT refresh_payment_stats();"

# Vérification des anomalies toutes les 15 minutes
*/15 * * * * psql -d customeroom -c "SELECT * FROM detect_payment_anomalies();"
```

### 3. Index et Performance

Les migrations incluent automatiquement :
- ✅ Index optimisés pour les requêtes fréquentes
- ✅ Contraintes de validation des données
- ✅ Politiques de sécurité (RLS)
- ✅ Vues matérialisées pour les statistiques

## 🔒 Sécurité

### Row Level Security (RLS)
Les tables sensibles sont protégées par RLS :
- Les entreprises ne voient que leurs propres données
- Authentification requise pour tous les accès
- Politiques granulaires par table

### Audit Trail
Tous les événements sont enregistrés dans `payment_events` :
- Créations d'abonnements
- Paiements réussis/échoués
- Annulations
- Utilisations de coupons

## 📈 Monitoring et Alertes

### Métriques Disponibles
```sql
-- Revenus du mois en cours
SELECT * FROM monthly_revenue_stats 
WHERE month = DATE_TRUNC('month', CURRENT_DATE);

-- Métriques par plan
SELECT * FROM subscription_metrics ORDER BY total_revenue DESC;

-- Anomalies détectées
SELECT * FROM detect_payment_anomalies();
```

### Alertes Automatiques
Le système détecte automatiquement :
- Taux d'échec de paiement élevé (>10%)
- Webhooks non traités (>1h)
- Abonnements expirés non nettoyés

## 🛠️ Maintenance

### Nettoyage Automatique
```sql
-- Nettoyer les données anciennes (par défaut 365 jours)
SELECT * FROM cleanup_old_data();

-- Nettoyer les abonnements expirés
SELECT * FROM cleanup_expired_subscriptions();
```

### Sauvegarde Recommandée
```bash
# Sauvegarde complète
pg_dump -U user -d customeroom --schema-only > schema_backup.sql
pg_dump -U user -d customeroom --data-only -t subscription_plans > plans_backup.sql

# Sauvegarde des données de paiement
pg_dump -U user -d customeroom -t payment_receipts -t business_subscriptions > payments_backup.sql
```

## 🔄 Rollback

En cas de problème, pour revenir en arrière :

```sql
-- Supprimer toutes les tables du système de paiement
DROP TABLE IF EXISTS coupon_usages CASCADE;
DROP TABLE IF EXISTS subscription_coupons CASCADE;
DROP TABLE IF EXISTS payment_webhooks CASCADE;
DROP TABLE IF EXISTS payment_events CASCADE;
DROP TABLE IF EXISTS payment_receipts CASCADE;
DROP TABLE IF EXISTS payment_intents CASCADE;
DROP TABLE IF EXISTS payment_methods CASCADE;
DROP TABLE IF EXISTS business_subscriptions CASCADE;
DROP TABLE IF EXISTS subscription_plans CASCADE;

-- Supprimer les vues matérialisées
DROP MATERIALIZED VIEW IF EXISTS monthly_revenue_stats;
DROP MATERIALIZED VIEW IF EXISTS subscription_metrics;

-- Supprimer les fonctions
DROP FUNCTION IF EXISTS create_free_trial CASCADE;
DROP FUNCTION IF EXISTS process_payment CASCADE;
-- ... (autres fonctions)
```

## 📞 Support

Pour toute question ou problème :
1. Vérifiez les logs PostgreSQL
2. Exécutez `SELECT * FROM detect_payment_anomalies();`
3. Consultez la documentation des fonctions avec `\df`
4. Contactez l'équipe de développement

---

## ✅ Checklist de Déploiement

- [ ] PostgreSQL 12+ installé
- [ ] Extensions uuid-ossp et pgcrypto disponibles
- [ ] Permissions de création de tables accordées
- [ ] Script de déploiement exécuté sans erreur
- [ ] Tests de fonctionnement validés
- [ ] Variables d'environnement configurées
- [ ] Tâches cron planifiées
- [ ] Monitoring mis en place
- [ ] Sauvegarde configurée

**Le système de paiement est prêt pour la production !** 🚀
