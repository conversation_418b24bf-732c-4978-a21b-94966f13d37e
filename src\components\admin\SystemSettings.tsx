import React, { useState, useEffect } from 'react';
import {
  Settings,
  Server,
  Database,
  Shield,
  Mail,
  Globe,
  CreditCard,
  Users,
  FileText,
  Bell,
  Lock,
  Key,
  Zap,
  Monitor,
  HardDrive,
  Wifi,
  AlertTriangle,
  CheckCircle,
  Save,
  RefreshCw,
  Download,
  Upload,
  Eye,
  EyeOff,
  Copy,
  Edit3,
  Trash2,
  Plus
} from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import { supabase } from '../../lib/supabase';

// Types pour les paramètres système
interface SystemConfig {
  id: string;
  category: string;
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'json' | 'password';
  description: string;
  is_sensitive: boolean;
  is_readonly: boolean;
  created_at: string;
  updated_at: string;
}

interface ConfigCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  settings: SystemConfig[];
}

const SystemSettings: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeCategory, setActiveCategory] = useState('general');
  const [configs, setConfigs] = useState<SystemConfig[]>([]);
  const [showSensitive, setShowSensitive] = useState<Record<string, boolean>>({});
  const [editingConfig, setEditingConfig] = useState<string | null>(null);
  const [newConfigModal, setNewConfigModal] = useState(false);

  const categories: ConfigCategory[] = [
    {
      id: 'general',
      name: 'Général',
      description: 'Paramètres généraux de la plateforme (nom, version, maintenance)',
      icon: Settings,
      color: 'bg-blue-500',
      settings: []
    },
    {
      id: 'database',
      name: 'Base de Données',
      description: 'Configuration de la base de données (pool, timeout, backup)',
      icon: Database,
      color: 'bg-green-500',
      settings: []
    },
    {
      id: 'security',
      name: 'Sécurité',
      description: 'Paramètres de sécurité et authentification (JWT, mots de passe)',
      icon: Shield,
      color: 'bg-red-500',
      settings: []
    },
    {
      id: 'email',
      name: 'Email',
      description: 'Configuration des emails et notifications (SMTP, expéditeur)',
      icon: Mail,
      color: 'bg-purple-500',
      settings: []
    },
    {
      id: 'api',
      name: 'API',
      description: 'Configuration des APIs et intégrations (rate limiting, CORS)',
      icon: Globe,
      color: 'bg-indigo-500',
      settings: []
    },
    {
      id: 'payment',
      name: 'Paiements',
      description: 'Configuration des systèmes de paiement (Stripe, commissions)',
      icon: CreditCard,
      color: 'bg-orange-500',
      settings: []
    },
    {
      id: 'performance',
      name: 'Performance',
      description: 'Paramètres de performance et cache (TTL, compression, CDN)',
      icon: Zap,
      color: 'bg-yellow-500',
      settings: []
    },
    {
      id: 'monitoring',
      name: 'Monitoring',
      description: 'Surveillance et logs système (niveau, rétention, analytics)',
      icon: Monitor,
      color: 'bg-cyan-500',
      settings: []
    }
  ];

  useEffect(() => {
    loadSystemConfigs();
  }, []);

  const loadSystemConfigs = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔧 Chargement des paramètres système...');

      // Charger les configurations depuis la base de données
      const { data: configData, error: configError } = await supabase
        .from('system_configs')
        .select('*')
        .order('category, key');

      if (configError) {
        console.warn('⚠️ Table system_configs non trouvée, utilisation des paramètres par défaut');
        // Utiliser des paramètres par défaut si la table n'existe pas
        setConfigs(getDefaultConfigs());
      } else {
        setConfigs(configData || []);
      }

      console.log('✅ Paramètres système chargés');

    } catch (error) {
      console.error('Erreur lors du chargement des paramètres:', error);
      setError(`Erreur de chargement: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
      // Utiliser des paramètres par défaut en cas d'erreur
      setConfigs(getDefaultConfigs());
    } finally {
      setLoading(false);
    }
  };

  const getDefaultConfigs = (): SystemConfig[] => {
    return [
      // Paramètres généraux
      {
        id: '1',
        category: 'general',
        key: 'app_name',
        value: 'Customeroom',
        type: 'string',
        description: 'Nom de l\'application',
        is_sensitive: false,
        is_readonly: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '2',
        category: 'general',
        key: 'app_version',
        value: '1.0.0',
        type: 'string',
        description: 'Version de l\'application',
        is_sensitive: false,
        is_readonly: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '3',
        category: 'general',
        key: 'maintenance_mode',
        value: false,
        type: 'boolean',
        description: 'Mode maintenance activé',
        is_sensitive: false,
        is_readonly: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '4',
        category: 'general',
        key: 'max_users',
        value: 10000,
        type: 'number',
        description: 'Nombre maximum d\'utilisateurs',
        is_sensitive: false,
        is_readonly: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      // Paramètres de sécurité
      {
        id: '5',
        category: 'security',
        key: 'session_timeout',
        value: 3600,
        type: 'number',
        description: 'Timeout de session (secondes)',
        is_sensitive: false,
        is_readonly: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '6',
        category: 'security',
        key: 'password_min_length',
        value: 8,
        type: 'number',
        description: 'Longueur minimale du mot de passe',
        is_sensitive: false,
        is_readonly: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '7',
        category: 'security',
        key: 'jwt_secret',
        value: '***HIDDEN***',
        type: 'password',
        description: 'Clé secrète JWT',
        is_sensitive: true,
        is_readonly: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      // Paramètres email
      {
        id: '8',
        category: 'email',
        key: 'smtp_host',
        value: 'smtp.gmail.com',
        type: 'string',
        description: 'Serveur SMTP',
        is_sensitive: false,
        is_readonly: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '9',
        category: 'email',
        key: 'smtp_port',
        value: 587,
        type: 'number',
        description: 'Port SMTP',
        is_sensitive: false,
        is_readonly: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '10',
        category: 'email',
        key: 'smtp_password',
        value: '***HIDDEN***',
        type: 'password',
        description: 'Mot de passe SMTP',
        is_sensitive: true,
        is_readonly: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      // Paramètres API
      {
        id: '11',
        category: 'api',
        key: 'api_rate_limit',
        value: 1000,
        type: 'number',
        description: 'Limite de requêtes API par heure',
        is_sensitive: false,
        is_readonly: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '12',
        category: 'api',
        key: 'api_key_expiry',
        value: 365,
        type: 'number',
        description: 'Expiration des clés API (jours)',
        is_sensitive: false,
        is_readonly: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      // Paramètres de performance
      {
        id: '13',
        category: 'performance',
        key: 'cache_ttl',
        value: 3600,
        type: 'number',
        description: 'TTL du cache (secondes)',
        is_sensitive: false,
        is_readonly: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '14',
        category: 'performance',
        key: 'max_file_size',
        value: 10485760,
        type: 'number',
        description: 'Taille max des fichiers (bytes)',
        is_sensitive: false,
        is_readonly: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  };

  const saveConfig = async (configId: string, newValue: any) => {
    try {
      setSaving(true);
      setError(null);

      // Mettre à jour localement
      setConfigs(prev => prev.map(config => 
        config.id === configId 
          ? { ...config, value: newValue, updated_at: new Date().toISOString() }
          : config
      ));

      // Essayer de sauvegarder en base de données
      const { error } = await supabase
        .from('system_configs')
        .update({ 
          value: newValue, 
          updated_at: new Date().toISOString() 
        })
        .eq('id', configId);

      if (error) {
        console.warn('⚠️ Impossible de sauvegarder en base:', error.message);
        setSuccess('Configuration mise à jour localement (base de données non disponible)');
      } else {
        setSuccess('Configuration sauvegardée avec succès');
      }

      setEditingConfig(null);

    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      setError(`Erreur de sauvegarde: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    } finally {
      setSaving(false);
    }
  };

  const toggleSensitiveVisibility = (configId: string) => {
    setShowSensitive(prev => ({
      ...prev,
      [configId]: !prev[configId]
    }));
  };

  const copyToClipboard = (value: string) => {
    navigator.clipboard.writeText(value);
    setSuccess('Valeur copiée dans le presse-papiers');
  };

  const exportConfigs = () => {
    const exportData = configs.map(config => ({
      category: config.category,
      key: config.key,
      value: config.is_sensitive ? '***HIDDEN***' : config.value,
      type: config.type,
      description: config.description
    }));

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `customeroom-config-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const addNewConfig = async (newConfig: Omit<SystemConfig, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      setSaving(true);
      setError(null);

      // Créer un nouvel ID temporaire
      const tempId = `temp_${Date.now()}`;
      const now = new Date().toISOString();

      const configWithId: SystemConfig = {
        ...newConfig,
        id: tempId,
        created_at: now,
        updated_at: now
      };

      // Ajouter localement
      setConfigs(prev => [...prev, configWithId]);

      // Essayer de sauvegarder en base de données
      const { data, error } = await supabase
        .from('system_configs')
        .insert([{
          category: newConfig.category,
          key: newConfig.key,
          value: newConfig.value,
          type: newConfig.type,
          description: newConfig.description,
          is_sensitive: newConfig.is_sensitive,
          is_readonly: newConfig.is_readonly
        }])
        .select()
        .single();

      if (error) {
        console.warn('⚠️ Impossible de sauvegarder en base:', error.message);
        setSuccess('Paramètre ajouté localement (base de données non disponible)');
      } else {
        // Remplacer l'ID temporaire par l'ID réel
        setConfigs(prev => prev.map(config =>
          config.id === tempId ? { ...configWithId, id: data.id } : config
        ));
        setSuccess('Paramètre ajouté avec succès');
      }

    } catch (error) {
      console.error('Erreur lors de l\'ajout:', error);
      setError(`Erreur d'ajout: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    } finally {
      setSaving(false);
    }
  };

  const getCurrentCategoryConfigs = () => {
    return configs.filter(config => config.category === activeCategory);
  };

  const getCategoryHelp = (categoryId: string): string => {
    const helpTexts: Record<string, string> = {
      general: "Configurez les paramètres de base de votre plateforme Customeroom : nom de l'application, version, mode maintenance, et limites utilisateurs. Ces paramètres affectent l'ensemble du système.",
      database: "Gérez les paramètres de performance de votre base de données : taille du pool de connexions, timeouts, rétention des sauvegardes. Modifiez avec précaution car cela peut affecter les performances.",
      security: "Configurez la sécurité de votre plateforme : durée des sessions, politique des mots de passe, clés de chiffrement. Les paramètres sensibles sont automatiquement masqués.",
      email: "Paramétrez votre serveur SMTP pour l'envoi d'emails : notifications, confirmations, réinitialisations de mot de passe. Testez la configuration après modification.",
      api: "Contrôlez l'accès à vos APIs : limites de taux, origines CORS autorisées, expiration des clés. Ces paramètres protègent votre API contre les abus.",
      payment: "Configurez vos systèmes de paiement Stripe : clés publiques/privées, devise par défaut, taux de commission. Vérifiez en mode test avant la production.",
      performance: "Optimisez les performances : cache TTL, compression, CDN, taille maximale des fichiers. Ces paramètres impactent directement l'expérience utilisateur.",
      monitoring: "Configurez la surveillance système : niveau de logs, rétention, analytics. Ces paramètres vous aident à diagnostiquer les problèmes et optimiser votre plateforme."
    };

    return helpTexts[categoryId] || "Configurez les paramètres de cette catégorie selon vos besoins.";
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Chargement des paramètres système...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Paramètres Système</h1>
          <p className="text-gray-600 mt-2">
            Configuration et gestion des paramètres de la plateforme
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            leftIcon={<Download size={16} />}
            onClick={exportConfigs}
          >
            Exporter
          </Button>
          <Button
            leftIcon={<RefreshCw size={16} />}
            onClick={() => loadSystemConfigs()}
          >
            Actualiser
          </Button>
        </div>
      </div>

      {/* Messages */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardBody className="p-4">
            <div className="flex items-center space-x-2 text-red-700">
              <AlertTriangle size={20} />
              <div>
                <h3 className="font-medium">Erreur</h3>
                <p className="text-sm">{error}</p>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {success && (
        <Card className="border-green-200 bg-green-50">
          <CardBody className="p-4">
            <div className="flex items-center space-x-2 text-green-700">
              <CheckCircle size={20} />
              <div>
                <h3 className="font-medium">Succès</h3>
                <p className="text-sm">{success}</p>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Menu des catégories */}
        <div className="lg:col-span-1">
          <Card>
            <CardBody className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Catégories</h3>
              <nav className="space-y-2">
                {categories.map((category) => {
                  const Icon = category.icon;
                  const configCount = configs.filter(c => c.category === category.id).length;
                  
                  return (
                    <button
                      key={category.id}
                      onClick={() => setActiveCategory(category.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                        activeCategory === category.id
                          ? 'bg-blue-100 text-blue-700 border border-blue-200'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      <div className={`p-1 rounded ${category.color}`}>
                        <Icon size={16} className="text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">{category.name}</div>
                        <div className="text-xs text-gray-500">{configCount} paramètres</div>
                      </div>
                    </button>
                  );
                })}
              </nav>
            </CardBody>
          </Card>
        </div>

        {/* Contenu des paramètres */}
        <div className="lg:col-span-3">
          <Card>
            <CardBody className="p-6">
              {(() => {
                const currentCategory = categories.find(c => c.id === activeCategory);
                const categoryConfigs = getCurrentCategoryConfigs();
                
                return (
                  <div>
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center space-x-3">
                        {currentCategory && (
                          <>
                            <div className={`p-2 rounded-lg ${currentCategory.color}`}>
                              <currentCategory.icon size={20} className="text-white" />
                            </div>
                            <div>
                              <h2 className="text-xl font-semibold text-gray-900">
                                {currentCategory.name}
                              </h2>
                              <p className="text-gray-600 text-sm">
                                {currentCategory.description}
                              </p>
                            </div>
                          </>
                        )}
                      </div>
                      <Button
                        variant="outline"
                        leftIcon={<Plus size={16} />}
                        onClick={() => setNewConfigModal(true)}
                      >
                        Ajouter
                      </Button>
                    </div>

                    {/* Aide contextuelle */}
                    {currentCategory && (
                      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h4 className="font-medium text-blue-900 mb-2">
                          💡 À propos de cette catégorie
                        </h4>
                        <p className="text-blue-700 text-sm">
                          {getCategoryHelp(currentCategory.id)}
                        </p>
                      </div>
                    )}

                    <div className="space-y-4">
                      {categoryConfigs.length > 0 ? (
                        categoryConfigs.map((config) => (
                          <ConfigItem
                            key={config.id}
                            config={config}
                            isEditing={editingConfig === config.id}
                            showSensitive={showSensitive[config.id] || false}
                            onEdit={() => setEditingConfig(config.id)}
                            onSave={(value) => saveConfig(config.id, value)}
                            onCancel={() => setEditingConfig(null)}
                            onToggleVisibility={() => toggleSensitiveVisibility(config.id)}
                            onCopy={() => copyToClipboard(String(config.value))}
                            saving={saving}
                          />
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <Settings size={48} className="mx-auto text-gray-400 mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">
                            Aucun paramètre
                          </h3>
                          <p className="text-gray-600">
                            Aucun paramètre configuré pour cette catégorie.
                          </p>
                          <Button
                            className="mt-4"
                            leftIcon={<Plus size={16} />}
                            onClick={() => setNewConfigModal(true)}
                          >
                            Ajouter le premier paramètre
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })()}
            </CardBody>
          </Card>
        </div>
      </div>

      {/* Modal pour nouveau paramètre */}
      <NewConfigModal
        isOpen={newConfigModal}
        onClose={() => setNewConfigModal(false)}
        onSave={addNewConfig}
        category={activeCategory}
      />
    </div>
  );
};

// Composant pour un paramètre individuel
interface ConfigItemProps {
  config: SystemConfig;
  isEditing: boolean;
  showSensitive: boolean;
  onEdit: () => void;
  onSave: (value: any) => void;
  onCancel: () => void;
  onToggleVisibility: () => void;
  onCopy: () => void;
  saving: boolean;
}

const ConfigItem: React.FC<ConfigItemProps> = ({
  config,
  isEditing,
  showSensitive,
  onEdit,
  onSave,
  onCancel,
  onToggleVisibility,
  onCopy,
  saving
}) => {
  const [editValue, setEditValue] = useState(config.value);

  useEffect(() => {
    setEditValue(config.value);
  }, [config.value, isEditing]);

  const handleSave = () => {
    let processedValue = editValue;

    // Traitement selon le type
    switch (config.type) {
      case 'number':
        processedValue = Number(editValue);
        break;
      case 'boolean':
        processedValue = Boolean(editValue);
        break;
      case 'json':
        try {
          processedValue = JSON.parse(editValue);
        } catch {
          alert('JSON invalide');
          return;
        }
        break;
    }

    onSave(processedValue);
  };

  const formatValue = (value: any) => {
    if (config.is_sensitive && !showSensitive) {
      return '***HIDDEN***';
    }

    switch (config.type) {
      case 'boolean':
        return value ? 'Activé' : 'Désactivé';
      case 'json':
        return JSON.stringify(value, null, 2);
      case 'number':
        return value.toLocaleString('fr-FR');
      default:
        return String(value);
    }
  };

  const getTypeIcon = () => {
    switch (config.type) {
      case 'password':
        return <Key size={16} className="text-red-500" />;
      case 'boolean':
        return config.value ? <CheckCircle size={16} className="text-green-500" /> : <AlertTriangle size={16} className="text-gray-500" />;
      case 'number':
        return <HardDrive size={16} className="text-blue-500" />;
      case 'json':
        return <FileText size={16} className="text-purple-500" />;
      default:
        return <Settings size={16} className="text-gray-500" />;
    }
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            {getTypeIcon()}
            <h4 className="font-medium text-gray-900">{config.key}</h4>
            {config.is_sensitive && (
              <span className="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full">
                Sensible
              </span>
            )}
            {config.is_readonly && (
              <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                Lecture seule
              </span>
            )}
          </div>

          <p className="text-sm text-gray-600 mb-3">{config.description}</p>

          {isEditing ? (
            <div className="space-y-3">
              {config.type === 'boolean' ? (
                <select
                  value={editValue ? 'true' : 'false'}
                  onChange={(e) => setEditValue(e.target.value === 'true')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="true">Activé</option>
                  <option value="false">Désactivé</option>
                </select>
              ) : config.type === 'json' ? (
                <textarea
                  value={typeof editValue === 'string' ? editValue : JSON.stringify(editValue, null, 2)}
                  onChange={(e) => setEditValue(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                  placeholder="JSON valide..."
                />
              ) : (
                <input
                  type={config.type === 'password' ? 'password' : config.type === 'number' ? 'number' : 'text'}
                  value={editValue}
                  onChange={(e) => setEditValue(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder={`Entrez ${config.type === 'number' ? 'un nombre' : 'une valeur'}...`}
                />
              )}

              <div className="flex space-x-2">
                <Button
                  size="sm"
                  onClick={handleSave}
                  disabled={saving}
                  leftIcon={saving ? <RefreshCw size={14} className="animate-spin" /> : <Save size={14} />}
                >
                  Sauvegarder
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={onCancel}
                  disabled={saving}
                >
                  Annuler
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div className="flex-1">
                {config.type === 'json' ? (
                  <pre className="bg-gray-50 p-2 rounded text-sm font-mono overflow-x-auto">
                    {formatValue(config.value)}
                  </pre>
                ) : (
                  <span className={`font-mono text-sm ${
                    config.type === 'boolean'
                      ? config.value
                        ? 'text-green-600'
                        : 'text-gray-600'
                      : 'text-gray-900'
                  }`}>
                    {formatValue(config.value)}
                  </span>
                )}
              </div>

              <div className="flex items-center space-x-2 ml-4">
                {config.is_sensitive && (
                  <button
                    onClick={onToggleVisibility}
                    className="p-1 text-gray-400 hover:text-gray-600"
                    title={showSensitive ? 'Masquer' : 'Afficher'}
                  >
                    {showSensitive ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                )}

                <button
                  onClick={onCopy}
                  className="p-1 text-gray-400 hover:text-gray-600"
                  title="Copier"
                >
                  <Copy size={16} />
                </button>

                {!config.is_readonly && (
                  <button
                    onClick={onEdit}
                    className="p-1 text-gray-400 hover:text-gray-600"
                    title="Modifier"
                  >
                    <Edit3 size={16} />
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
        <span>Type: {config.type}</span>
        <span>Modifié: {new Date(config.updated_at).toLocaleString('fr-FR')}</span>
      </div>
    </div>
  );
};

// Modal pour ajouter un nouveau paramètre
interface NewConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (config: Omit<SystemConfig, 'id' | 'created_at' | 'updated_at'>) => void;
  category: string;
}

const NewConfigModal: React.FC<NewConfigModalProps> = ({ isOpen, onClose, onSave, category }) => {
  const [formData, setFormData] = useState({
    key: '',
    value: '',
    type: 'string' as SystemConfig['type'],
    description: '',
    is_sensitive: false,
    is_readonly: false
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.key || !formData.description) {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    let processedValue: any = formData.value;

    try {
      switch (formData.type) {
        case 'number':
          processedValue = Number(formData.value);
          if (isNaN(processedValue)) {
            alert('Valeur numérique invalide');
            return;
          }
          break;
        case 'boolean':
          processedValue = formData.value === 'true';
          break;
        case 'json':
          processedValue = JSON.parse(formData.value);
          break;
      }
    } catch (error) {
      alert('Format de valeur invalide');
      return;
    }

    onSave({
      category,
      key: formData.key,
      value: processedValue,
      type: formData.type,
      description: formData.description,
      is_sensitive: formData.is_sensitive,
      is_readonly: formData.is_readonly
    });

    // Reset form
    setFormData({
      key: '',
      value: '',
      type: 'string',
      description: '',
      is_sensitive: false,
      is_readonly: false
    });

    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Nouveau Paramètre
        </h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Clé *
            </label>
            <input
              type="text"
              value={formData.key}
              onChange={(e) => setFormData(prev => ({ ...prev, key: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              placeholder="nom_du_parametre"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <select
              value={formData.type}
              onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as SystemConfig['type'] }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="string">Texte</option>
              <option value="number">Nombre</option>
              <option value="boolean">Booléen</option>
              <option value="json">JSON</option>
              <option value="password">Mot de passe</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Valeur
            </label>
            {formData.type === 'boolean' ? (
              <select
                value={formData.value}
                onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="true">Activé</option>
                <option value="false">Désactivé</option>
              </select>
            ) : formData.type === 'json' ? (
              <textarea
                value={formData.value}
                onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                placeholder='{"key": "value"}'
              />
            ) : (
              <input
                type={formData.type === 'password' ? 'password' : formData.type === 'number' ? 'number' : 'text'}
                value={formData.value}
                onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="Valeur du paramètre"
              />
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              placeholder="Description du paramètre"
              required
            />
          </div>

          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_sensitive}
                onChange={(e) => setFormData(prev => ({ ...prev, is_sensitive: e.target.checked }))}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">Sensible</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_readonly}
                onChange={(e) => setFormData(prev => ({ ...prev, is_readonly: e.target.checked }))}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">Lecture seule</span>
            </label>
          </div>

          <div className="flex space-x-2 pt-4">
            <Button type="submit" className="flex-1">
              Créer
            </Button>
            <Button type="button" variant="outline" onClick={onClose} className="flex-1">
              Annuler
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SystemSettings;
