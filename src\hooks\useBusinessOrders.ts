import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { OrdersService, RealOrder } from '../services/ordersService';

interface UseBusinessOrdersReturn {
  orders: RealOrder[];
  loading: boolean;
  error: string | null;
  refreshOrders: () => Promise<void>;
  updateOrderStatus: (orderId: string, newStatus: string) => Promise<boolean>;
  addTrackingNumber: (orderId: string, trackingNumber: string) => Promise<boolean>;
  getOrdersByStatus: (status: string) => RealOrder[];
  getOrdersByPeriod: (days: number) => RealOrder[];
  statistics: {
    total: number;
    pending: number;
    processing: number;
    shipped: number;
    delivered: number;
    cancelled: number;
    totalRevenue: number;
    averageOrderValue: number;
  };
}

export const useBusinessOrders = (businessId: string): UseBusinessOrdersReturn => {
  const [orders, setOrders] = useState<RealOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fonction pour charger les commandes
  const loadOrders = useCallback(async () => {
    if (!businessId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const businessOrders = await OrdersService.getBusinessOrders(businessId);
      setOrders(businessOrders);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement des commandes');
      console.error('Erreur dans useBusinessOrders:', err);
    } finally {
      setLoading(false);
    }
  }, [businessId]);

  // Fonction pour actualiser les commandes
  const refreshOrders = useCallback(async () => {
    await loadOrders();
  }, [loadOrders]);

  // Fonction pour mettre à jour le statut d'une commande
  const updateOrderStatus = useCallback(async (orderId: string, newStatus: string): Promise<boolean> => {
    try {
      const success = await OrdersService.updateOrderStatus(orderId, newStatus);
      if (success) {
        // Mettre à jour l'état local immédiatement
        setOrders(prevOrders => 
          prevOrders.map(order => 
            order.id === orderId 
              ? { ...order, status: newStatus as RealOrder['status'], updatedAt: new Date() }
              : order
          )
        );
        
        // Puis recharger pour être sûr d'avoir les dernières données
        await refreshOrders();
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la mise à jour du statut');
      return false;
    }
  }, [refreshOrders]);

  // Fonction pour ajouter un numéro de suivi
  const addTrackingNumber = useCallback(async (orderId: string, trackingNumber: string): Promise<boolean> => {
    try {
      const success = await OrdersService.addTrackingNumber(orderId, trackingNumber);
      if (success) {
        // Mettre à jour l'état local
        setOrders(prevOrders => 
          prevOrders.map(order => 
            order.id === orderId 
              ? { 
                  ...order, 
                  trackingNumber, 
                  status: 'shipped',
                  updatedAt: new Date() 
                }
              : order
          )
        );
        
        // Recharger pour être sûr
        await refreshOrders();
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de l\'ajout du numéro de suivi');
      return false;
    }
  }, [refreshOrders]);

  // Fonction pour filtrer les commandes par statut
  const getOrdersByStatus = useCallback((status: string): RealOrder[] => {
    return orders.filter(order => order.status === status);
  }, [orders]);

  // Fonction pour filtrer les commandes par période
  const getOrdersByPeriod = useCallback((days: number): RealOrder[] => {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    return orders.filter(order => order.createdAt >= startDate);
  }, [orders]);

  // Calcul des statistiques
  const statistics = useCallback(() => {
    const stats = {
      total: orders.length,
      pending: orders.filter(o => o.status === 'pending').length,
      processing: orders.filter(o => o.status === 'processing').length,
      shipped: orders.filter(o => o.status === 'shipped').length,
      delivered: orders.filter(o => o.status === 'delivered').length,
      cancelled: orders.filter(o => o.status === 'cancelled').length,
      totalRevenue: orders.reduce((sum, order) => sum + order.totalAmount, 0),
      averageOrderValue: 0
    };

    stats.averageOrderValue = stats.total > 0 ? stats.totalRevenue / stats.total : 0;
    return stats;
  }, [orders])();

  // Charger les commandes au montage du composant
  useEffect(() => {
    loadOrders();
  }, [loadOrders]);

  // Écouter les changements en temps réel
  useEffect(() => {
    if (!businessId) return;

    // S'abonner aux changements de la table orders
    const subscription = supabase
      .channel('business-orders')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'orders',
          filter: `seller_id=eq.${businessId}`
        },
        (payload) => {
          console.log('Changement détecté dans les commandes:', payload);
          // Recharger les commandes quand il y a un changement
          refreshOrders();
        }
      )
      .subscribe();

    // Nettoyer l'abonnement
    return () => {
      subscription.unsubscribe();
    };
  }, [businessId, refreshOrders]);

  return {
    orders,
    loading,
    error,
    refreshOrders,
    updateOrderStatus,
    addTrackingNumber,
    getOrdersByStatus,
    getOrdersByPeriod,
    statistics
  };
};
