import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import Card, { CardBody } from '../components/ui/Card';
import { 
  Handshake, 
  Clock, 
  CheckCircle, 
  XCircle, 
  MessageCircle, 
  DollarSign,
  Package,
  Calendar,
  User,
  ArrowRight,
  Filter,
  Search,
  TrendingUp,
  AlertCircle
} from 'lucide-react';
import { Navigate } from 'react-router-dom';

// Types pour les négociations
interface Negotiation {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  originalPrice: number;
  proposedPrice: number;
  currentPrice: number;
  status: 'pending' | 'accepted' | 'rejected' | 'counter_offer' | 'expired';
  businessName: string;
  businessId: string;
  createdAt: string;
  updatedAt: string;
  messages: NegotiationMessage[];
  expiresAt: string;
  isInitiatedByMe: boolean;
  counterOfferCount: number;
  maxCounterOffers: number;
}

interface NegotiationMessage {
  id: string;
  senderId: string;
  senderName: string;
  message: string;
  price?: number;
  timestamp: string;
  type: 'message' | 'price_offer' | 'counter_offer' | 'acceptance' | 'rejection';
}

interface NegotiationStats {
  total: number;
  pending: number;
  accepted: number;
  rejected: number;
  expired: number;
  averageDiscount: number;
  totalSavings: number;
}

const NegotiationsPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [negotiations, setNegotiations] = useState<Negotiation[]>([]);
  const [filteredNegotiations, setFilteredNegotiations] = useState<Negotiation[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'accepted' | 'rejected' | 'expired'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [stats, setStats] = useState<NegotiationStats>({
    total: 0,
    pending: 0,
    accepted: 0,
    rejected: 0,
    expired: 0,
    averageDiscount: 0,
    totalSavings: 0
  });

  useEffect(() => {
    if (currentUser) {
      loadNegotiations();
    }
  }, [currentUser]);

  useEffect(() => {
    filterNegotiations();
  }, [negotiations, activeTab, searchQuery]);

  const loadNegotiations = async () => {
    try {
      setLoading(true);
      
      // Simulation de données de négociation
      const mockNegotiations: Negotiation[] = [
        {
          id: 'NEG-001',
          productId: 'PROD-001',
          productName: 'Huile de Beauté Olgane Premium',
          productImage: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=100&h=100&fit=crop',
          originalPrice: 15000,
          proposedPrice: 12000,
          currentPrice: 13000,
          status: 'counter_offer',
          businessName: 'Olgane Cosmetics',
          businessId: 'BUS-001',
          createdAt: '2025-05-28T10:00:00Z',
          updatedAt: '2025-05-28T14:30:00Z',
          expiresAt: '2025-05-30T23:59:59Z',
          isInitiatedByMe: true,
          counterOfferCount: 2,
          maxCounterOffers: 5,
          messages: [
            {
              id: 'MSG-001',
              senderId: currentUser?.id || '',
              senderName: currentUser?.username || '',
              message: 'Bonjour, je suis intéressé par ce produit. Pourriez-vous accepter 12 000 F CFA ?',
              price: 12000,
              timestamp: '2025-05-28T10:00:00Z',
              type: 'price_offer'
            },
            {
              id: 'MSG-002',
              senderId: 'BUS-001',
              senderName: 'Olgane Cosmetics',
              message: 'Merci pour votre intérêt. Je peux vous proposer 13 000 F CFA, c\'est mon meilleur prix.',
              price: 13000,
              timestamp: '2025-05-28T14:30:00Z',
              type: 'counter_offer'
            }
          ]
        },
        {
          id: 'NEG-002',
          productId: 'PROD-002',
          productName: 'Smartphone Samsung Galaxy A54',
          productImage: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=100&h=100&fit=crop',
          originalPrice: 180000,
          proposedPrice: 160000,
          currentPrice: 160000,
          status: 'accepted',
          businessName: 'TechStore CI',
          businessId: 'BUS-002',
          createdAt: '2025-05-27T09:00:00Z',
          updatedAt: '2025-05-27T15:45:00Z',
          expiresAt: '2025-05-29T23:59:59Z',
          isInitiatedByMe: true,
          counterOfferCount: 0,
          maxCounterOffers: 3,
          messages: [
            {
              id: 'MSG-003',
              senderId: currentUser?.id || '',
              senderName: currentUser?.username || '',
              message: 'Acceptez-vous 160 000 F CFA pour ce smartphone ?',
              price: 160000,
              timestamp: '2025-05-27T09:00:00Z',
              type: 'price_offer'
            },
            {
              id: 'MSG-004',
              senderId: 'BUS-002',
              senderName: 'TechStore CI',
              message: 'Offre acceptée ! Merci pour votre achat.',
              timestamp: '2025-05-27T15:45:00Z',
              type: 'acceptance'
            }
          ]
        },
        {
          id: 'NEG-003',
          productId: 'PROD-003',
          productName: 'Chaussures Nike Air Max',
          productImage: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop',
          originalPrice: 85000,
          proposedPrice: 70000,
          currentPrice: 70000,
          status: 'rejected',
          businessName: 'SportZone',
          businessId: 'BUS-003',
          createdAt: '2025-05-26T16:00:00Z',
          updatedAt: '2025-05-26T18:20:00Z',
          expiresAt: '2025-05-28T23:59:59Z',
          isInitiatedByMe: true,
          counterOfferCount: 0,
          maxCounterOffers: 3,
          messages: [
            {
              id: 'MSG-005',
              senderId: currentUser?.id || '',
              senderName: currentUser?.username || '',
              message: 'Bonjour, puis-je avoir ces chaussures à 70 000 F CFA ?',
              price: 70000,
              timestamp: '2025-05-26T16:00:00Z',
              type: 'price_offer'
            },
            {
              id: 'MSG-006',
              senderId: 'BUS-003',
              senderName: 'SportZone',
              message: 'Désolé, ce prix est trop bas pour nous. Le prix minimum est 80 000 F CFA.',
              timestamp: '2025-05-26T18:20:00Z',
              type: 'rejection'
            }
          ]
        }
      ];

      setNegotiations(mockNegotiations);

      // Calculer les statistiques
      const calculatedStats: NegotiationStats = {
        total: mockNegotiations.length,
        pending: mockNegotiations.filter(n => n.status === 'pending' || n.status === 'counter_offer').length,
        accepted: mockNegotiations.filter(n => n.status === 'accepted').length,
        rejected: mockNegotiations.filter(n => n.status === 'rejected').length,
        expired: mockNegotiations.filter(n => n.status === 'expired').length,
        averageDiscount: 15.5,
        totalSavings: 27000
      };

      setStats(calculatedStats);
    } catch (error) {
      console.error('Erreur lors du chargement des négociations:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterNegotiations = () => {
    let filtered = negotiations;

    // Filtrer par statut
    if (activeTab !== 'all') {
      if (activeTab === 'pending') {
        filtered = filtered.filter(n => n.status === 'pending' || n.status === 'counter_offer');
      } else {
        filtered = filtered.filter(n => n.status === activeTab);
      }
    }

    // Filtrer par recherche
    if (searchQuery) {
      filtered = filtered.filter(n => 
        n.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        n.businessName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredNegotiations(filtered);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
      case 'counter_offer':
        return 'text-yellow-700 bg-yellow-100';
      case 'accepted':
        return 'text-green-700 bg-green-100';
      case 'rejected':
        return 'text-red-700 bg-red-100';
      case 'expired':
        return 'text-gray-700 bg-gray-100';
      default:
        return 'text-gray-700 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'counter_offer':
        return 'Contre-offre';
      case 'accepted':
        return 'Acceptée';
      case 'rejected':
        return 'Refusée';
      case 'expired':
        return 'Expirée';
      default:
        return status;
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price) + ' F CFA';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Rediriger si l'utilisateur n'est pas connecté
  if (!currentUser) {
    return <Navigate to="/auth" replace />;
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const tabs = [
    { id: 'all', label: 'Toutes', count: stats.total },
    { id: 'pending', label: 'En cours', count: stats.pending },
    { id: 'accepted', label: 'Acceptées', count: stats.accepted },
    { id: 'rejected', label: 'Refusées', count: stats.rejected },
    { id: 'expired', label: 'Expirées', count: stats.expired }
  ];

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Handshake className="text-blue-600 mr-3" size={32} />
          <h1 className="text-3xl font-bold text-gray-900">Mes Négociations</h1>
        </div>
        <p className="text-lg text-gray-600">
          Gérez vos négociations de prix et suivez vos économies
        </p>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardBody className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <div className="text-sm text-gray-600">Total négociations</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            <div className="text-sm text-gray-600">En cours</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.averageDiscount}%</div>
            <div className="text-sm text-gray-600">Réduction moyenne</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <div className="text-2xl font-bold text-purple-600">{formatPrice(stats.totalSavings)}</div>
            <div className="text-sm text-gray-600">Économies totales</div>
          </CardBody>
        </Card>
      </div>

      {/* Filtres et recherche */}
      <Card className="mb-6">
        <CardBody>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            {/* Onglets */}
            <div className="flex space-x-1">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                  }`}
                >
                  {tab.label} ({tab.count})
                </button>
              ))}
            </div>

            {/* Recherche */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Rechercher un produit ou vendeur..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-80"
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Liste des négociations */}
      <div className="space-y-4">
        {filteredNegotiations.length === 0 ? (
          <Card>
            <CardBody className="text-center py-12">
              <Handshake className="mx-auto text-gray-400 mb-4" size={48} />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchQuery ? 'Aucune négociation trouvée' : 'Aucune négociation'}
              </h3>
              <p className="text-gray-600">
                {searchQuery 
                  ? 'Essayez avec d\'autres mots-clés'
                  : 'Commencez à négocier des prix sur le marketplace'
                }
              </p>
            </CardBody>
          </Card>
        ) : (
          filteredNegotiations.map(negotiation => (
            <Card key={negotiation.id} className="hover:shadow-md transition-shadow">
              <CardBody>
                <div className="flex items-start space-x-4">
                  {/* Image du produit */}
                  <img
                    src={negotiation.productImage}
                    alt={negotiation.productName}
                    className="w-20 h-20 object-cover rounded-lg"
                  />

                  {/* Contenu principal */}
                  <div className="flex-1">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {negotiation.productName}
                        </h3>
                        <p className="text-sm text-gray-600 mb-2">
                          Vendu par <span className="font-medium">{negotiation.businessName}</span>
                        </p>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(negotiation.status)}`}>
                        {getStatusText(negotiation.status)}
                      </span>
                    </div>

                    {/* Prix */}
                    <div className="flex items-center space-x-4 mb-3">
                      <div className="text-sm">
                        <span className="text-gray-500">Prix original:</span>
                        <span className="ml-1 line-through text-gray-400">{formatPrice(negotiation.originalPrice)}</span>
                      </div>
                      <ArrowRight size={16} className="text-gray-400" />
                      <div className="text-sm">
                        <span className="text-gray-500">Prix négocié:</span>
                        <span className="ml-1 font-semibold text-green-600">{formatPrice(negotiation.currentPrice)}</span>
                      </div>
                      <div className="text-sm">
                        <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs font-medium">
                          -{Math.round((1 - negotiation.currentPrice / negotiation.originalPrice) * 100)}%
                        </span>
                      </div>
                    </div>

                    {/* Informations supplémentaires */}
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center space-x-4">
                        <span className="flex items-center">
                          <Calendar size={14} className="mr-1" />
                          {formatDate(negotiation.updatedAt)}
                        </span>
                        <span className="flex items-center">
                          <MessageCircle size={14} className="mr-1" />
                          {negotiation.messages.length} messages
                        </span>
                        {negotiation.status === 'counter_offer' && (
                          <span className="flex items-center text-yellow-600">
                            <Clock size={14} className="mr-1" />
                            Expire le {formatDate(negotiation.expiresAt)}
                          </span>
                        )}
                      </div>
                      <button className="text-blue-600 hover:text-blue-700 font-medium">
                        Voir détails
                      </button>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default NegotiationsPage;
