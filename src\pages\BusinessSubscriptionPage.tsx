import React from 'react';
import { useAuth } from '../context/AuthContext';
import { UserRole } from '../types';
import BusinessSubscription from '../components/business/BusinessSubscription';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import { CreditCard, ArrowLeft } from 'lucide-react';
import Button from '../components/ui/Button';
import { useNavigate } from 'react-router-dom';

const BusinessSubscriptionPage: React.FC = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  // Vérifier si l'utilisateur est une entreprise
  if (!currentUser || currentUser.role !== UserRole.BUSINESS) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <CreditCard className="mr-2" size={24} />
              Accès Restreint
            </h2>
          </CardHeader>
          <CardBody>
            <div className="text-center space-y-4">
              <p className="text-gray-600">
                Cette page est réservée aux comptes entreprise.
              </p>
              <div className="space-y-2">
                <p className="text-sm text-gray-500">
                  <strong>Votre statut :</strong> {currentUser?.role || 'Non connecté'}
                </p>
                <p className="text-sm text-gray-500">
                  <strong>Requis :</strong> Compte entreprise (business)
                </p>
              </div>
              <div className="flex flex-col space-y-2">
                <Button
                  onClick={() => navigate('/')}
                  variant="primary"
                  className="flex items-center justify-center"
                >
                  <ArrowLeft className="mr-2" size={16} />
                  Retour à l'accueil
                </Button>
                <Button
                  onClick={() => navigate('/profile')}
                  variant="outline"
                  className="flex items-center justify-center"
                >
                  Voir mon profil
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* En-tête de la page */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <CreditCard className="mr-3" size={32} />
                Gestion des Abonnements
              </h1>
              <p className="text-gray-600 mt-2">
                Gérez votre abonnement entreprise Customeroom
              </p>
            </div>
            <Button
              onClick={() => navigate('/profile')}
              variant="outline"
              className="flex items-center"
            >
              <ArrowLeft className="mr-2" size={16} />
              Retour au profil
            </Button>
          </div>
        </div>

        {/* Informations de l'entreprise */}
        <Card className="mb-6">
          <CardBody>
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <CreditCard className="text-blue-600" size={24} />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {(currentUser as any).businessName || currentUser.username}
                </h2>
                <p className="text-gray-600">
                  ID: {currentUser.id}
                </p>
                <div className="flex items-center mt-1">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Compte Entreprise
                  </span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Composant d'abonnement */}
        <BusinessSubscription />

        {/* Informations supplémentaires */}
        <Card className="mt-8">
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">
              Besoin d'aide ?
            </h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <p className="text-gray-600">
                Notre équipe support est là pour vous accompagner dans la gestion de votre abonnement.
              </p>
              <div className="flex flex-wrap gap-3">
                <Button
                  onClick={() => navigate('/support')}
                  variant="outline"
                  size="sm"
                >
                  Contacter le support
                </Button>
                <Button
                  onClick={() => navigate('/features')}
                  variant="outline"
                  size="sm"
                >
                  Voir toutes les fonctionnalités
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default BusinessSubscriptionPage;
