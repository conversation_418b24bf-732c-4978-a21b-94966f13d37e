-- Script pour configurer et tester le système de statut d'entreprise
-- Exécutez ce script dans l'éditeur SQL de Supabase après avoir appliqué la migration

-- =====================================================
-- 1. VÉRIFICATION DE L'INSTALLATION
-- =====================================================

-- Vérifier que toutes les tables ont été créées
DO $$
BEGIN
  -- Vérifier business_status_criteria
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'business_status_criteria') THEN
    RAISE EXCEPTION 'Table business_status_criteria non trouvée. Veuillez d''abord exécuter la migration.';
  END IF;
  
  -- Vérifier business_status_history
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'business_status_history') THEN
    RAISE EXCEPTION 'Table business_status_history non trouvée. Veuillez d''abord exécuter la migration.';
  END IF;
  
  -- Vérifier business_verification_documents
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'business_verification_documents') THEN
    RAISE EXCEPTION 'Table business_verification_documents non trouvée. Veuillez d''abord exécuter la migration.';
  END IF;
  
  RAISE NOTICE 'Toutes les tables du système de statut d''entreprise sont présentes.';
END $$;

-- =====================================================
-- 2. FONCTION DE VÉRIFICATION DU STATUT
-- =====================================================

-- Fonction pour vérifier si une entreprise est éligible à un statut
CREATE OR REPLACE FUNCTION check_business_status_eligibility(
  business_uuid UUID,
  target_status TEXT
)
RETURNS JSONB AS $$
DECLARE
  metrics JSONB;
  criteria RECORD;
  missing_criteria TEXT[] := ARRAY[]::TEXT[];
  is_eligible BOOLEAN := true;
BEGIN
  -- Récupérer les métriques de l'entreprise
  metrics := calculate_business_metrics(business_uuid);
  
  -- Récupérer les critères pour le statut cible
  SELECT * INTO criteria
  FROM business_status_criteria
  WHERE status_level = target_status AND is_active = true;
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'eligible', false,
      'error', 'Statut non trouvé: ' || target_status
    );
  END IF;
  
  -- Vérifier chaque critère
  
  -- Profil complet
  IF criteria.profile_complete_required AND NOT (metrics->>'profileComplete')::BOOLEAN THEN
    missing_criteria := array_append(missing_criteria, 'Profil d''entreprise complet requis');
    is_eligible := false;
  END IF;
  
  -- Nombre minimum de produits
  IF (metrics->>'productsCount')::INTEGER < criteria.min_products THEN
    missing_criteria := array_append(missing_criteria, 
      format('Au moins %s produits requis (actuellement: %s)', 
        criteria.min_products, metrics->>'productsCount'));
    is_eligible := false;
  END IF;
  
  -- Nombre minimum de ventes
  IF (metrics->>'salesCount')::INTEGER < criteria.min_sales THEN
    missing_criteria := array_append(missing_criteria, 
      format('Au moins %s ventes requises (actuellement: %s)', 
        criteria.min_sales, metrics->>'salesCount'));
    is_eligible := false;
  END IF;
  
  -- Nombre minimum d'avis
  IF (metrics->>'reviewsCount')::INTEGER < criteria.min_reviews THEN
    missing_criteria := array_append(missing_criteria, 
      format('Au moins %s avis requis (actuellement: %s)', 
        criteria.min_reviews, metrics->>'reviewsCount'));
    is_eligible := false;
  END IF;
  
  -- Note moyenne minimum
  IF (metrics->>'averageRating')::DECIMAL < criteria.min_rating THEN
    missing_criteria := array_append(missing_criteria, 
      format('Note moyenne d''au moins %s/5 requise (actuellement: %s)', 
        criteria.min_rating, metrics->>'averageRating'));
    is_eligible := false;
  END IF;
  
  -- Âge du compte minimum
  IF (metrics->>'accountAgeDays')::INTEGER < criteria.min_account_age_days THEN
    missing_criteria := array_append(missing_criteria, 
      format('Compte actif depuis au moins %s jours (actuellement: %s)', 
        criteria.min_account_age_days, metrics->>'accountAgeDays'));
    is_eligible := false;
  END IF;
  
  -- Vérification requise
  IF criteria.verification_required AND NOT (metrics->>'isVerified')::BOOLEAN THEN
    missing_criteria := array_append(missing_criteria, 'Vérification d''entreprise requise');
    is_eligible := false;
  END IF;
  
  -- Document d'entreprise requis
  IF criteria.business_document_required AND NOT (metrics->>'hasBusinessDocument')::BOOLEAN THEN
    missing_criteria := array_append(missing_criteria, 'Document officiel d''entreprise requis');
    is_eligible := false;
  END IF;
  
  -- Chiffre d'affaires minimum
  IF (metrics->>'totalRevenue')::NUMERIC < criteria.min_revenue THEN
    missing_criteria := array_append(missing_criteria, 
      format('Chiffre d''affaires d''au moins %s F CFA requis', criteria.min_revenue));
    is_eligible := false;
  END IF;
  
  -- Retourner le résultat
  RETURN jsonb_build_object(
    'eligible', is_eligible,
    'missingCriteria', to_jsonb(missing_criteria),
    'currentMetrics', metrics,
    'requiredCriteria', row_to_json(criteria)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 3. FONCTION DE MISE À JOUR AUTOMATIQUE DU STATUT
-- =====================================================

CREATE OR REPLACE FUNCTION update_business_status_if_eligible(business_uuid UUID)
RETURNS JSONB AS $$
DECLARE
  current_status TEXT;
  new_status TEXT;
  status_levels TEXT[] := ARRAY['new', 'active', 'verified', 'premium', 'featured', 'partner'];
  level TEXT;
  eligibility JSONB;
  highest_eligible_status TEXT := 'new';
  result JSONB;
BEGIN
  -- Récupérer le statut actuel
  SELECT business_status INTO current_status
  FROM business_profiles
  WHERE id = business_uuid;
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object('error', 'Entreprise non trouvée');
  END IF;
  
  -- Vérifier l'éligibilité pour chaque niveau (du plus élevé au plus bas)
  FOREACH level IN ARRAY ARRAY['partner', 'featured', 'premium', 'verified', 'active', 'new']
  LOOP
    eligibility := check_business_status_eligibility(business_uuid, level);
    
    IF (eligibility->>'eligible')::BOOLEAN THEN
      highest_eligible_status := level;
      EXIT;
    END IF;
  END LOOP;
  
  -- Si le nouveau statut est différent et supérieur, mettre à jour
  IF highest_eligible_status != current_status THEN
    -- Vérifier que c'est une progression (pas une régression)
    IF array_position(status_levels, highest_eligible_status) > array_position(status_levels, current_status) THEN
      -- Mettre à jour le statut
      UPDATE business_profiles
      SET 
        business_status = highest_eligible_status,
        status_updated_at = NOW()
      WHERE id = business_uuid;
      
      -- Enregistrer dans l'historique
      INSERT INTO business_status_history (
        business_id,
        old_status,
        new_status,
        change_reason,
        change_type,
        metrics_snapshot
      ) VALUES (
        business_uuid,
        current_status,
        highest_eligible_status,
        'Mise à jour automatique basée sur les critères',
        'automatic',
        calculate_business_metrics(business_uuid)
      );
      
      result := jsonb_build_object(
        'updated', true,
        'oldStatus', current_status,
        'newStatus', highest_eligible_status,
        'message', format('Félicitations ! Votre statut est passé de %s à %s !', current_status, highest_eligible_status)
      );
    ELSE
      result := jsonb_build_object(
        'updated', false,
        'message', 'Aucune progression de statut détectée'
      );
    END IF;
  ELSE
    result := jsonb_build_object(
      'updated', false,
      'message', 'Statut déjà à jour'
    );
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 4. TRIGGER POUR MISE À JOUR AUTOMATIQUE
-- =====================================================

-- Fonction trigger pour vérifier le statut après certaines actions
CREATE OR REPLACE FUNCTION trigger_business_status_check()
RETURNS TRIGGER AS $$
BEGIN
  -- Vérifier le statut seulement si auto_status_check_enabled est true
  IF NEW.auto_status_check_enabled THEN
    PERFORM update_business_status_if_eligible(NEW.id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Créer le trigger sur business_profiles
DROP TRIGGER IF EXISTS business_status_auto_update ON business_profiles;
CREATE TRIGGER business_status_auto_update
  AFTER UPDATE OF sales_count, total_revenue, verification_status, has_business_document
  ON business_profiles
  FOR EACH ROW
  EXECUTE FUNCTION trigger_business_status_check();

-- =====================================================
-- 5. DONNÉES DE TEST (OPTIONNEL)
-- =====================================================

-- Mettre à jour quelques entreprises existantes pour les tests
-- (Décommentez si vous voulez des données de test)

/*
-- Exemple : Mettre Dexima en statut vérifié
UPDATE business_profiles 
SET 
  business_status = 'verified',
  verification_status = 'verified',
  has_business_document = true,
  total_revenue = 1000000,
  verification_date = NOW()
WHERE business_name = 'Dexima';

-- Ajouter un historique pour Dexima
INSERT INTO business_status_history (
  business_id,
  old_status,
  new_status,
  change_reason,
  change_type,
  changed_by
) 
SELECT 
  id,
  'active',
  'verified',
  'Mise à jour manuelle pour les tests',
  'manual',
  id
FROM business_profiles 
WHERE business_name = 'Dexima';
*/

-- =====================================================
-- 6. REQUÊTES UTILES POUR ADMINISTRATION
-- =====================================================

-- Voir tous les statuts d'entreprise
-- SELECT business_name, business_status, verification_status, created_at FROM business_profiles ORDER BY created_at DESC;

-- Voir l'historique des changements de statut
-- SELECT bsh.*, bp.business_name FROM business_status_history bsh JOIN business_profiles bp ON bsh.business_id = bp.id ORDER BY bsh.created_at DESC;

-- Vérifier l'éligibilité d'une entreprise pour un statut
-- SELECT check_business_status_eligibility('uuid-de-l-entreprise', 'verified');

-- Calculer les métriques d'une entreprise
-- SELECT calculate_business_metrics('uuid-de-l-entreprise');

-- Mettre à jour le statut d'une entreprise si éligible
-- SELECT update_business_status_if_eligible('uuid-de-l-entreprise');

RAISE NOTICE 'Système de statut d''entreprise configuré avec succès !';
RAISE NOTICE 'Utilisez les fonctions calculate_business_metrics(), check_business_status_eligibility(), et update_business_status_if_eligible() pour gérer les statuts.';
