import React from 'react';
import { Building2 } from 'lucide-react';

interface BusinessNameDisplayProps {
  businessName: string | null | undefined;
  className?: string;
  showIcon?: boolean;
  fallbackText?: string;
}

const BusinessNameDisplay: React.FC<BusinessNameDisplayProps> = ({
  businessName,
  className = '',
  showIcon = true,
  fallbackText = 'Entreprise non spécifiée'
}) => {
  // Nettoyer et valider le nom de l'entreprise
  const cleanBusinessName = businessName?.trim();

  // Debug log pour tracer les problèmes (seulement en développement)
  if (process.env.NODE_ENV === 'development' && !cleanBusinessName) {
    console.log('BusinessNameDisplay - Nom d\'entreprise manquant:', businessName);
  }

  // Déterminer le texte à afficher
  const displayText = cleanBusinessName || fallbackText;
  const isValid = !!cleanBusinessName;

  return (
    <div
      className={`flex items-center ${className}`}
      data-business-name={cleanBusinessName}
      data-is-valid={isValid}
    >
      {showIcon && (
        <Building2
          size={18}
          className={`mr-2 ${isValid ? 'text-blue-600' : 'text-gray-400'}`}
        />
      )}
      <h3
        className={`font-semibold text-lg mb-1 ${
          isValid
            ? 'text-gray-900'
            : 'text-gray-500 italic'
        }`}
      >
        {displayText}
      </h3>
      {!isValid && process.env.NODE_ENV === 'development' && (
        <span className="ml-2 text-xs bg-red-100 text-red-600 px-2 py-1 rounded">
          ⚠️ Nom manquant
        </span>
      )}
    </div>
  );
};

export default BusinessNameDisplay;
