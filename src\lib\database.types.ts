export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      products: {
        Row: {
          id: string
          name: string
          description: string | null
          price: number
          business_id: string
          category: string
          images: string[] | null
          average_rating: number | null
          quality_badge: string | null
          negotiable: boolean | null
          stock: number | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          price: number
          business_id: string
          category: string
          images?: string[] | null
          average_rating?: number | null
          quality_badge?: string | null
          negotiable?: boolean | null
          stock?: number | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          price?: number
          business_id?: string
          category?: string
          images?: string[] | null
          average_rating?: number | null
          quality_badge?: string | null
          negotiable?: boolean | null
          stock?: number | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      negotiations: {
        Row: {
          id: string
          product_id: string
          buyer_id: string
          seller_id: string
          initial_price: number
          current_offer: number
          status: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          product_id: string
          buyer_id: string
          seller_id: string
          initial_price: number
          current_offer: number
          status?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          product_id?: string
          buyer_id?: string
          seller_id?: string
          initial_price?: number
          current_offer?: number
          status?: string
          created_at?: string | null
          updated_at?: string | null
        }
      }
      negotiation_messages: {
        Row: {
          id: string
          negotiation_id: string
          sender_id: string
          content: string
          offer: number | null
          created_at: string | null
        }
        Insert: {
          id?: string
          negotiation_id: string
          sender_id: string
          content: string
          offer?: number | null
          created_at?: string | null
        }
        Update: {
          id?: string
          negotiation_id?: string
          sender_id?: string
          content?: string
          offer?: number | null
          created_at?: string | null
        }
      }
      orders: {
        Row: {
          id: string
          buyer_id: string
          seller_id: string
          product_id: string
          quantity: number
          total_price: number
          status: string
          negotiation_id: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          buyer_id: string
          seller_id: string
          product_id: string
          quantity: number
          total_price: number
          status?: string
          negotiation_id?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          buyer_id?: string
          seller_id?: string
          product_id?: string
          quantity?: number
          total_price?: number
          status?: string
          negotiation_id?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
    }
  }
}