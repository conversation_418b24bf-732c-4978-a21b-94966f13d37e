-- Tables pour le système de suivi de commande

-- Table principale de suivi des commandes
CREATE TABLE IF NOT EXISTS order_tracking (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  tracking_number VARCHAR(50) UNIQUE NOT NULL,
  current_status VARCHAR(50) NOT NULL DEFAULT 'order_confirmed',
  estimated_delivery TIMESTAMPTZ,
  actual_delivery TIMESTAMPTZ,
  carrier VARCHAR(100) DEFAULT 'Livraison Standard',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table des événements de suivi
CREATE TABLE IF NOT EXISTS tracking_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tracking_id UUID NOT NULL REFERENCES order_tracking(id) ON DELETE CASCADE,
  status VARCHAR(50) NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  location VARCHAR(200),
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table des notifications
CREATE TABLE IF NOT EXISTS notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL,
  title VARCHAR(200) NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_order_tracking_order_id ON order_tracking(order_id);
CREATE INDEX IF NOT EXISTS idx_order_tracking_tracking_number ON order_tracking(tracking_number);
CREATE INDEX IF NOT EXISTS idx_tracking_events_tracking_id ON tracking_events(tracking_id);
CREATE INDEX IF NOT EXISTS idx_tracking_events_status ON tracking_events(status);
CREATE INDEX IF NOT EXISTS idx_tracking_events_created_at ON tracking_events(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger pour order_tracking
CREATE TRIGGER update_order_tracking_updated_at 
    BEFORE UPDATE ON order_tracking 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Politiques de sécurité RLS (Row Level Security)

-- order_tracking
ALTER TABLE order_tracking ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own order tracking" ON order_tracking
    FOR SELECT USING (
        order_id IN (
            SELECT id FROM orders 
            WHERE buyer_id = auth.uid() OR seller_id = auth.uid()
        )
    );

CREATE POLICY "System can insert order tracking" ON order_tracking
    FOR INSERT WITH CHECK (true);

CREATE POLICY "System can update order tracking" ON order_tracking
    FOR UPDATE USING (true);

-- tracking_events
ALTER TABLE tracking_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view tracking events for their orders" ON tracking_events
    FOR SELECT USING (
        tracking_id IN (
            SELECT ot.id FROM order_tracking ot
            JOIN orders o ON ot.order_id = o.id
            WHERE o.buyer_id = auth.uid() OR o.seller_id = auth.uid()
        )
    );

CREATE POLICY "System can insert tracking events" ON tracking_events
    FOR INSERT WITH CHECK (true);

-- notifications
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own notifications" ON notifications
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own notifications" ON notifications
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "System can insert notifications" ON notifications
    FOR INSERT WITH CHECK (true);

-- Fonction pour nettoyer les anciennes notifications (optionnel)
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS void AS $$
BEGIN
    DELETE FROM notifications 
    WHERE created_at < NOW() - INTERVAL '30 days' 
    AND read = true;
END;
$$ LANGUAGE plpgsql;

-- Données de test pour les statuts de suivi
INSERT INTO tracking_events (tracking_id, status, title, description, location) VALUES
('00000000-0000-0000-0000-000000000000', 'order_confirmed', 'Commande confirmée', 'Votre commande a été confirmée', 'Abidjan, Côte d''Ivoire')
ON CONFLICT DO NOTHING;
