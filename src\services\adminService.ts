import { supabase } from '../lib/supabase';
import {
  AdminProfile,
  AdminLevel,
  AdminPermission,
  AdminAuditLog,
  AdminAlert,
  AdminDashboardStats,
  UserManagementAction,
  BusinessManagementAction,
  ContentModerationAction,
  AdminFilters,
  AlertPriority,
  AlertStatus
} from '../types/admin';

export class AdminService {
  
  // =====================================================
  // GESTION DES PROFILS ADMINISTRATEUR
  // =====================================================
  
  /**
   * Récupérer le profil admin de l'utilisateur connecté
   */
  static async getCurrentAdminProfile(): Promise<AdminProfile | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      // Requête simplifiée sans jointures problématiques
      const { data: adminProfile, error: adminError } = await supabase
        .from('admin_profiles')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .single();

      if (adminError) {
        console.error('Erreur lors de la récupération du profil admin:', adminError);
        return null;
      }

      if (!adminProfile) return null;

      // Récupérer les données utilisateur séparément
      const { data: userProfile, error: userError } = await supabase
        .from('profiles')
        .select('id, username, email, profile_picture')
        .eq('id', user.id)
        .single();

      if (userError) {
        console.error('Erreur lors de la récupération du profil utilisateur:', userError);
      }

      // Récupérer le superviseur séparément si nécessaire
      let supervisor = null;
      if (adminProfile.supervisor_id) {
        const { data: supervisorData } = await supabase
          .from('admin_profiles')
          .select('id, admin_code, admin_level')
          .eq('id', adminProfile.supervisor_id)
          .single();
        supervisor = supervisorData;
      }

      // Combiner les données
      return {
        ...adminProfile,
        user: userProfile,
        supervisor
      };
    } catch (error) {
      console.error('Erreur dans getCurrentAdminProfile:', error);
      return null;
    }
  }

  /**
   * Vérifier si l'utilisateur a une permission spécifique
   */
  static async hasPermission(permissionCode: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return false;

      const { data, error } = await supabase
        .rpc('check_admin_permission', {
          p_user_id: user.id,
          p_permission_code: permissionCode
        });

      if (error) {
        console.error('Erreur lors de la vérification des permissions:', error);
        return false;
      }

      return data || false;
    } catch (error) {
      console.error('Erreur dans hasPermission:', error);
      return false;
    }
  }

  /**
   * Récupérer tous les administrateurs
   */
  static async getAllAdmins(filters?: AdminFilters): Promise<AdminProfile[]> {
    try {
      // Requête simplifiée sans jointures problématiques
      let query = supabase
        .from('admin_profiles')
        .select('*')
        .order('created_at', { ascending: false });

      // Appliquer les filtres
      if (filters?.adminLevel?.length) {
        query = query.in('admin_level', filters.adminLevel);
      }

      if (filters?.searchTerm) {
        query = query.ilike('admin_code', `%${filters.searchTerm}%`);
      }

      if (filters?.dateRange) {
        query = query
          .gte('created_at', filters.dateRange.start)
          .lte('created_at', filters.dateRange.end);
      }

      const { data: adminProfiles, error } = await query;

      if (error) {
        console.error('Erreur lors de la récupération des admins:', error);
        return [];
      }

      if (!adminProfiles) return [];

      // Récupérer les données utilisateur pour chaque admin séparément
      const adminsWithUsers = await Promise.all(
        adminProfiles.map(async (admin) => {
          const { data: userProfile } = await supabase
            .from('profiles')
            .select('id, username, email, profile_picture')
            .eq('id', admin.user_id)
            .single();

          return {
            ...admin,
            user: userProfile
          };
        })
      );

      return adminsWithUsers;
    } catch (error) {
      console.error('Erreur dans getAllAdmins:', error);
      return [];
    }
  }

  /**
   * Créer un nouvel administrateur
   */
  static async createAdmin(
    userId: string,
    adminLevel: AdminLevel,
    department?: string,
    supervisorId?: string
  ): Promise<AdminProfile | null> {
    try {
      // Générer un code admin unique
      const adminCode = 'ADM' + Math.floor(Math.random() * 1000000).toString().padStart(6, '0');

      const { data, error } = await supabase
        .from('admin_profiles')
        .insert({
          user_id: userId,
          admin_level: adminLevel,
          admin_code: adminCode,
          department,
          supervisor_id: supervisorId,
          is_active: true
        })
        .select()
        .single();

      if (error) {
        console.error('Erreur lors de la création de l\'admin:', error);
        return null;
      }

      // Mettre à jour le rôle dans profiles
      await supabase
        .from('profiles')
        .update({ role: 'admin' })
        .eq('id', userId);

      // Log de l'action
      await this.logAction(
        'CREATE_ADMIN',
        'Création d\'un nouvel administrateur',
        'admin',
        data.id,
        null,
        data
      );

      return data;
    } catch (error) {
      console.error('Erreur dans createAdmin:', error);
      return null;
    }
  }

  /**
   * Mettre à jour un administrateur
   */
  static async updateAdmin(
    adminId: string,
    updates: Partial<AdminProfile>
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('admin_profiles')
        .update({
          admin_level: updates.admin_level,
          department: updates.department,
          supervisor_id: updates.supervisor_id,
          notes: updates.notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', adminId);

      if (error) {
        console.error('Erreur lors de la mise à jour de l\'admin:', error);
        return false;
      }

      // Log de l'action
      await this.logAction(
        'UPDATE_ADMIN',
        'Mise à jour d\'un administrateur',
        'admin',
        adminId,
        null,
        updates
      );

      return true;
    } catch (error) {
      console.error('Erreur dans updateAdmin:', error);
      return false;
    }
  }

  /**
   * Activer/Désactiver un administrateur
   */
  static async toggleAdminStatus(adminId: string, isActive: boolean): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('admin_profiles')
        .update({
          is_active: isActive,
          updated_at: new Date().toISOString(),
          ...(isActive ? { activated_at: new Date().toISOString() } : { deactivated_at: new Date().toISOString() })
        })
        .eq('id', adminId);

      if (error) {
        console.error('Erreur lors du changement de statut:', error);
        return false;
      }

      // Log de l'action
      await this.logAction(
        isActive ? 'ACTIVATE_ADMIN' : 'DEACTIVATE_ADMIN',
        `${isActive ? 'Activation' : 'Désactivation'} d'un administrateur`,
        'admin',
        adminId
      );

      return true;
    } catch (error) {
      console.error('Erreur dans toggleAdminStatus:', error);
      return false;
    }
  }

  /**
   * Supprimer un administrateur
   */
  static async deleteAdmin(adminId: string): Promise<boolean> {
    try {
      // Récupérer l'admin avant suppression pour le log
      const { data: adminToDelete } = await supabase
        .from('admin_profiles')
        .select('user_id')
        .eq('id', adminId)
        .single();

      if (!adminToDelete) {
        console.error('Admin non trouvé');
        return false;
      }

      // Supprimer le profil admin
      const { error: deleteError } = await supabase
        .from('admin_profiles')
        .delete()
        .eq('id', adminId);

      if (deleteError) {
        console.error('Erreur lors de la suppression de l\'admin:', deleteError);
        return false;
      }

      // Remettre le rôle utilisateur à 'standard'
      await supabase
        .from('profiles')
        .update({ role: 'standard' })
        .eq('id', adminToDelete.user_id);

      // Log de l'action
      await this.logAction(
        'DELETE_ADMIN',
        'Suppression d\'un administrateur',
        'admin',
        adminId
      );

      return true;
    } catch (error) {
      console.error('Erreur dans deleteAdmin:', error);
      return false;
    }
  }

  // =====================================================
  // GESTION DES UTILISATEURS
  // =====================================================

  /**
   * Exécuter une action de gestion d'utilisateur
   */
  static async executeUserAction(action: UserManagementAction): Promise<boolean> {
    try {
      const { userId, action: actionType, reason, duration, newRole } = action;

      let updateData: any = {};
      let logDescription = '';

      switch (actionType) {
        case 'suspend':
          updateData = { 
            status: 'suspended',
            suspended_until: duration ? new Date(Date.now() + duration * 24 * 60 * 60 * 1000) : null
          };
          logDescription = `Suspension de l'utilisateur${duration ? ` pour ${duration} jours` : ''}`;
          break;

        case 'activate':
          updateData = { 
            status: 'active',
            suspended_until: null
          };
          logDescription = 'Activation de l\'utilisateur';
          break;

        case 'change_role':
          updateData = { role: newRole };
          logDescription = `Changement de rôle vers ${newRole}`;
          break;

        case 'verify':
          updateData = { is_verified: true };
          logDescription = 'Vérification du compte utilisateur';
          break;

        case 'delete':
          updateData = {
            suspended_until: new Date().toISOString(),
            suspension_reason: `COMPTE SUPPRIMÉ: ${reason}`,
            status: 'deleted'
          };
          logDescription = 'Suppression de l\'utilisateur';
          break;

        default:
          throw new Error(`Action non supportée: ${actionType}`);
      }

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId);

      if (error) {
        console.error('Erreur lors de l\'action utilisateur:', error);
        return false;
      }

      // Log de l'action
      await this.logAction(
        `USER_${actionType.toUpperCase()}`,
        logDescription,
        'user',
        userId,
        null,
        { ...updateData, reason }
      );

      return true;
    } catch (error) {
      console.error('Erreur dans executeUserAction:', error);
      return false;
    }
  }

  // =====================================================
  // GESTION DES ENTREPRISES
  // =====================================================

  /**
   * Exécuter une action de gestion d'entreprise
   */
  static async executeBusinessAction(action: BusinessManagementAction): Promise<boolean> {
    try {
      const { businessId, action: actionType, reason, newStatus } = action;

      let updateData: any = {};
      let logDescription = '';

      switch (actionType) {
        case 'verify':
          updateData = {
            business_verified: true,
            business_status: 'verified'
          };
          logDescription = 'Vérification de l\'entreprise';
          break;

        case 'suspend':
          updateData = {
            business_status: 'suspended',
            suspension_reason: reason || 'Suspension administrative'
          };
          logDescription = 'Suspension de l\'entreprise';
          break;

        case 'activate':
          updateData = {
            business_status: 'verified',
            suspension_reason: null
          };
          logDescription = 'Activation de l\'entreprise';
          break;

        case 'approve_documents':
          updateData = {
            business_verified: true,
            business_status: 'verified'
          };
          logDescription = 'Approbation des documents d\'entreprise';
          break;

        case 'change_status':
          updateData = { business_status: newStatus };
          logDescription = `Changement de statut vers ${newStatus}`;
          break;

        default:
          throw new Error(`Action non supportée: ${actionType}`);
      }

      const { error } = await supabase
        .from('business_profiles')
        .update(updateData)
        .eq('id', businessId);

      if (error) {
        console.error('Erreur lors de l\'action entreprise:', error);
        return false;
      }

      // Log de l'action
      await this.logAction(
        `BUSINESS_${actionType.toUpperCase()}`,
        logDescription,
        'business',
        businessId,
        null,
        { ...updateData, reason }
      );

      return true;
    } catch (error) {
      console.error('Erreur dans executeBusinessAction:', error);
      return false;
    }
  }

  // =====================================================
  // AUDIT ET LOGS
  // =====================================================

  /**
   * Enregistrer une action d'audit
   */
  static async logAction(
    actionType: string,
    description: string,
    targetType?: string,
    targetId?: string,
    oldValues?: any,
    newValues?: any,
    metadata?: any
  ): Promise<void> {
    try {
      const adminProfile = await this.getCurrentAdminProfile();
      if (!adminProfile) return;

      await supabase
        .from('admin_audit_log')
        .insert({
          admin_id: adminProfile.id,
          action_type: actionType,
          action_description: description,
          target_type: targetType,
          target_id: targetId,
          old_values: oldValues,
          new_values: newValues,
          metadata: metadata || {}
        });
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du log:', error);
    }
  }

  /**
   * Récupérer les logs d'audit
   */
  static async getAuditLogs(filters?: AdminFilters): Promise<AdminAuditLog[]> {
    try {
      let query = supabase
        .from('admin_audit_log')
        .select(`
          *,
          admin:admin_profiles!admin_audit_log_admin_id_fkey(
            id,
            admin_code,
            user:profiles!admin_profiles_user_id_fkey(username, email)
          )
        `)
        .order('created_at', { ascending: false })
        .limit(100);

      if (filters?.dateRange) {
        query = query
          .gte('created_at', filters.dateRange.start)
          .lte('created_at', filters.dateRange.end);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Erreur lors de la récupération des logs:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Erreur dans getAuditLogs:', error);
      return [];
    }
  }

  // =====================================================
  // GESTION DES ALERTES
  // =====================================================

  /**
   * Créer une nouvelle alerte
   */
  static async createAlert(
    alertType: string,
    title: string,
    message: string,
    priority: AlertPriority = AlertPriority.MEDIUM,
    assignedTo?: string
  ): Promise<AdminAlert | null> {
    try {
      const adminProfile = await this.getCurrentAdminProfile();
      if (!adminProfile) return null;

      const { data, error } = await supabase
        .from('admin_alerts')
        .insert({
          alert_type: alertType,
          title,
          message,
          priority,
          assigned_to: assignedTo,
          created_by: adminProfile.id,
          status: AlertStatus.OPEN
        })
        .select()
        .single();

      if (error) {
        console.error('Erreur lors de la création de l\'alerte:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Erreur dans createAlert:', error);
      return null;
    }
  }

  /**
   * Récupérer les alertes
   */
  static async getAlerts(status?: AlertStatus): Promise<AdminAlert[]> {
    try {
      // Requête simplifiée sans jointures problématiques
      let query = supabase
        .from('admin_alerts')
        .select('*')
        .order('created_at', { ascending: false });

      if (status) {
        query = query.eq('status', status);
      }

      const { data: alerts, error } = await query;

      if (error) {
        console.error('Erreur lors de la récupération des alertes:', error);
        return [];
      }

      if (!alerts) return [];

      // Récupérer les données des créateurs et assignés séparément
      const alertsWithRelations = await Promise.all(
        alerts.map(async (alert) => {
          let creator = null;
          let assignee = null;

          // Récupérer le créateur si présent
          if (alert.created_by) {
            const { data: creatorProfile } = await supabase
              .from('admin_profiles')
              .select('id, admin_code')
              .eq('id', alert.created_by)
              .single();
            creator = creatorProfile;
          }

          // Récupérer l'assigné si présent
          if (alert.assigned_to) {
            const { data: assigneeProfile } = await supabase
              .from('admin_profiles')
              .select('id, admin_code')
              .eq('id', alert.assigned_to)
              .single();
            assignee = assigneeProfile;
          }

          return {
            ...alert,
            creator,
            assignee
          };
        })
      );

      return alertsWithRelations;
    } catch (error) {
      console.error('Erreur dans getAlerts:', error);
      return [];
    }
  }

  /**
   * Exécuter une action sur une alerte
   */
  static async executeAlertAction(action: {
    alertId: string;
    action: 'assign' | 'resolve' | 'dismiss' | 'escalate' | 'update_priority';
    assigneeId?: string;
    resolutionNotes?: string;
    newPriority?: string;
    reason?: string;
  }): Promise<boolean> {
    try {
      const { alertId, action: actionType, assigneeId, resolutionNotes, newPriority, reason } = action;

      let updateData: any = {
        updated_at: new Date().toISOString()
      };
      let logDescription = '';

      switch (actionType) {
        case 'assign':
          updateData.assigned_to = assigneeId;
          updateData.status = AlertStatus.IN_PROGRESS;
          logDescription = 'Assignation de l\'alerte';
          break;

        case 'resolve':
          updateData.status = AlertStatus.RESOLVED;
          updateData.resolved_at = new Date().toISOString();
          if (resolutionNotes) {
            updateData.resolution_notes = resolutionNotes;
          }
          logDescription = 'Résolution de l\'alerte';
          break;

        case 'dismiss':
          updateData.status = AlertStatus.DISMISSED;
          logDescription = 'Ignorance de l\'alerte';
          break;

        case 'escalate':
        case 'update_priority':
          if (newPriority) {
            updateData.priority = newPriority;
          }
          logDescription = `Changement de priorité vers ${newPriority}`;
          break;

        default:
          throw new Error(`Action non supportée: ${actionType}`);
      }

      // Mettre à jour l'alerte
      const { error } = await supabase
        .from('admin_alerts')
        .update(updateData)
        .eq('id', alertId);

      if (error) {
        console.error('Erreur lors de la mise à jour de l\'alerte:', error);
        return false;
      }

      // Log de l'action
      await this.logAction(
        actionType.toUpperCase(),
        logDescription,
        'alert',
        alertId,
        null,
        { ...updateData, reason }
      );

      return true;
    } catch (error) {
      console.error('Erreur dans executeAlertAction:', error);
      return false;
    }
  }

  /**
   * Mettre à jour le statut d'une alerte
   */
  static async updateAlertStatus(
    alertId: string,
    status: AlertStatus,
    resolutionNotes?: string
  ): Promise<boolean> {
    try {
      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      };

      if (status === AlertStatus.RESOLVED) {
        updateData.resolved_at = new Date().toISOString();
        updateData.resolution_notes = resolutionNotes;
      }

      const { error } = await supabase
        .from('admin_alerts')
        .update(updateData)
        .eq('id', alertId);

      if (error) {
        console.error('Erreur lors de la mise à jour de l\'alerte:', error);
        return false;
      }

      // Log de l'action
      await this.logAction(
        'UPDATE_ALERT',
        `Mise à jour du statut de l'alerte vers ${status}`,
        'alert',
        alertId,
        null,
        updateData
      );

      return true;
    } catch (error) {
      console.error('Erreur dans updateAlertStatus:', error);
      return false;
    }
  }

  // =====================================================
  // STATISTIQUES ET TABLEAU DE BORD
  // =====================================================

  /**
   * Récupérer les statistiques du tableau de bord
   */
  static async getDashboardStats(): Promise<AdminDashboardStats | null> {
    try {
      // Récupérer les statistiques de base
      const [
        usersCount,
        businessesCount,
        postsCount,
        ordersCount,
        activeAdminsCount,
        pendingAlertsCount
      ] = await Promise.all([
        this.getTableCount('profiles'),
        this.getTableCount('business_profiles'),
        this.getTableCount('posts'),
        this.getTableCount('orders'),
        this.getTableCount('admin_profiles', { is_active: true }),
        this.getTableCount('admin_alerts', { status: 'open' })
      ]);

      // Récupérer les inscriptions du jour
      const today = new Date().toISOString().split('T')[0];
      const todayRegistrations = await this.getTableCount('profiles', {
        created_at: `gte.${today}`
      });

      // Récupérer les données de croissance (30 derniers jours)
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();

      const userGrowth = await this.getGrowthData('profiles', thirtyDaysAgo);
      const businessGrowth = await this.getGrowthData('business_profiles', thirtyDaysAgo);

      // Récupérer les alertes par priorité
      const { data: alertsData } = await supabase
        .from('admin_alerts')
        .select('priority')
        .eq('status', 'open');

      const alertsByPriority = Object.values(AlertPriority).map(priority => ({
        priority,
        count: alertsData?.filter(alert => alert.priority === priority).length || 0
      }));

      return {
        totalUsers: usersCount,
        totalBusinesses: businessesCount,
        totalPosts: postsCount,
        totalOrders: ordersCount,
        activeAdmins: activeAdminsCount,
        pendingAlerts: pendingAlertsCount,
        todayRegistrations,
        monthlyRevenue: 0, // À implémenter avec les données de paiement
        userGrowth,
        businessGrowth,
        revenueGrowth: [], // À implémenter
        alertsByPriority
      };
    } catch (error) {
      console.error('Erreur dans getDashboardStats:', error);
      return null;
    }
  }

  /**
   * Compter les enregistrements dans une table
   */
  private static async getTableCount(
    table: string,
    filters?: Record<string, any>
  ): Promise<number> {
    try {
      let query = supabase
        .from(table)
        .select('*', { count: 'exact', head: true });

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (typeof value === 'string' && value.startsWith('gte.')) {
            query = query.gte(key, value.substring(4));
          } else {
            query = query.eq(key, value);
          }
        });
      }

      const { count, error } = await query;

      if (error) {
        console.error(`Erreur lors du comptage de ${table}:`, error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      console.error(`Erreur dans getTableCount pour ${table}:`, error);
      return 0;
    }
  }

  /**
   * Récupérer les données de croissance
   */
  private static async getGrowthData(
    table: string,
    since: string
  ): Promise<Array<{ date: string; count: number }>> {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('created_at')
        .gte('created_at', since)
        .order('created_at');

      if (error) {
        console.error(`Erreur lors de la récupération des données de croissance pour ${table}:`, error);
        return [];
      }

      // Grouper par jour
      const groupedData: Record<string, number> = {};
      data?.forEach(item => {
        const date = item.created_at.split('T')[0];
        groupedData[date] = (groupedData[date] || 0) + 1;
      });

      return Object.entries(groupedData).map(([date, count]) => ({
        date,
        count
      }));
    } catch (error) {
      console.error(`Erreur dans getGrowthData pour ${table}:`, error);
      return [];
    }
  }

  // =====================================================
  // MODÉRATION DE CONTENU
  // =====================================================

  /**
   * Exécuter une action de modération de contenu
   */
  static async executeContentModerationAction(action: ContentModerationAction): Promise<boolean> {
    try {
      const { contentId, contentType, action: actionType, reason, moderatorNotes } = action;

      let updateData: any = {};
      let logDescription = '';

      switch (actionType) {
        case 'approve':
          updateData = {
            is_approved: true,
            moderation_status: 'approved'
          };
          logDescription = `Approbation du ${contentType}`;
          break;

        case 'reject':
          updateData = {
            is_approved: false,
            moderation_status: 'rejected',
            rejection_reason: reason
          };
          logDescription = `Rejet du ${contentType}`;
          break;

        case 'flag':
          updateData = {
            is_flagged: true,
            flag_reason: reason
          };
          logDescription = `Signalement du ${contentType}`;
          break;

        case 'remove':
          updateData = {
            is_removed: true,
            removal_reason: reason
          };
          logDescription = `Suppression du ${contentType}`;
          break;

        default:
          throw new Error(`Action de modération non supportée: ${actionType}`);
      }

      if (moderatorNotes) {
        updateData.moderator_notes = moderatorNotes;
      }

      const tableName = this.getTableNameForContentType(contentType);
      const { error } = await supabase
        .from(tableName)
        .update(updateData)
        .eq('id', contentId);

      if (error) {
        console.error('Erreur lors de l\'action de modération:', error);
        return false;
      }

      // Log de l'action
      await this.logAction(
        `MODERATE_${actionType.toUpperCase()}`,
        logDescription,
        contentType,
        contentId,
        null,
        { ...updateData, reason, moderatorNotes }
      );

      return true;
    } catch (error) {
      console.error('Erreur dans executeContentModerationAction:', error);
      return false;
    }
  }

  /**
   * Obtenir le nom de table pour un type de contenu
   */
  private static getTableNameForContentType(contentType: string): string {
    switch (contentType) {
      case 'post':
        return 'posts';
      case 'comment':
        return 'comments';
      case 'review':
        return 'marketcomen';
      case 'ad':
        return 'ad_campaigns';
      case 'ad_comment':
        return 'ad_comments';
      default:
        throw new Error(`Type de contenu non supporté: ${contentType}`);
    }
  }

  /**
   * Récupérer le contenu à modérer avec filtres
   */
  static async getModerationContent(filters?: {
    type?: string;
    status?: string;
    flagged?: boolean;
    limit?: number;
  }): Promise<any[]> {
    try {
      const allContent: any[] = [];

      // Charger les posts
      if (!filters?.type || filters.type === 'post') {
        const { data: posts } = await supabase
          .from('posts')
          .select(`
            id,
            type,
            description,
            business_name,
            product_name,
            category,
            rating,
            images,
            tags,
            created_at,
            updated_at,
            user_id,
            profiles!posts_user_id_fkey(username, email, profile_picture, role)
          `)
          .order('created_at', { ascending: false })
          .limit(filters?.limit || 50);

        if (posts) {
          posts.forEach(post => {
            allContent.push({
              ...post,
              content_type: 'post',
              content: post.description,
              status: 'approved',
              is_flagged: false
            });
          });
        }
      }

      // Charger les commentaires de publicités
      if (!filters?.type || filters.type === 'ad_comment') {
        const { data: adComments } = await supabase
          .from('ad_comments')
          .select(`
            id,
            content,
            is_approved,
            is_flagged,
            flagged_reason,
            created_at,
            updated_at,
            user_id,
            campaign_id,
            profiles!ad_comments_user_id_fkey(username, email, profile_picture, role),
            ad_campaigns!ad_comments_campaign_id_fkey(title)
          `)
          .order('created_at', { ascending: false })
          .limit(filters?.limit || 50);

        if (adComments) {
          adComments.forEach(comment => {
            allContent.push({
              ...comment,
              content_type: 'ad_comment',
              status: comment.is_approved ? 'approved' : 'pending'
            });
          });
        }
      }

      return allContent;
    } catch (error) {
      console.error('Erreur dans getModerationContent:', error);
      return [];
    }
  }
}
