-- Migration pour ajouter des champs supplémentaires à la table profiles
-- Cette migration permet aux utilisateurs de modifier leur profil avec plus d'informations

-- Ajout de la colonne full_name (les autres colonnes existent déjà)
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS full_name VARCHAR(255);

-- Ajout des colonnes pour les images de profil si elles n'existent pas déjà
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS cover_photo_url TEXT,
ADD COLUMN IF NOT EXISTS country VARCHAR(100),
ADD COLUMN IF NOT EXISTS city VARCHAR(100);

-- Suppression des politiques existantes si elles existent
DROP POLICY IF EXISTS "Les utilisateurs peuvent voir tous les profils" ON profiles;
DROP POLICY IF EXISTS "Les utilisateurs peuvent modifier leur propre profil" ON profiles;

-- Mise à jour des politiques RLS (Row Level Security) pour permettre aux utilisateurs de modifier leur propre profil
CREATE POLICY "Les utilisateurs peuvent voir tous les profils"
ON profiles FOR SELECT
USING (true);

CREATE POLICY "Les utilisateurs peuvent modifier leur propre profil"
ON profiles FOR UPDATE
USING (auth.uid() = id);

-- Création d'une fonction pour mettre à jour le timestamp updated_at lors des modifications
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Création d'un trigger pour appeler la fonction lors des mises à jour
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Création d'une fonction pour gérer le stockage des images
-- Note: Cette fonction suppose que vous avez configuré un bucket de stockage nommé 'profile_images'
CREATE OR REPLACE FUNCTION handle_profile_image_upload()
RETURNS TRIGGER AS $$
BEGIN
    -- Vous pouvez ajouter ici une logique pour valider ou traiter les URLs d'images
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Création d'un trigger pour la gestion des images
DROP TRIGGER IF EXISTS handle_profile_images ON profiles;
CREATE TRIGGER handle_profile_images
BEFORE UPDATE OF profile_picture, cover_photo_url ON profiles
FOR EACH ROW
EXECUTE FUNCTION handle_profile_image_upload();

-- Création du bucket de stockage pour les images de profil
-- Cette commande doit être exécutée avec les privilèges appropriés
INSERT INTO storage.buckets (id, name, public)
VALUES ('profile-images', 'profile-images', true)
ON CONFLICT (id) DO NOTHING;

-- Suppression des politiques existantes pour le stockage
DROP POLICY IF EXISTS "Les images de profil sont accessibles publiquement" ON storage.objects;
DROP POLICY IF EXISTS "Les utilisateurs peuvent télécharger leurs propres images" ON storage.objects;
DROP POLICY IF EXISTS "Les utilisateurs peuvent supprimer leurs propres images" ON storage.objects;

-- Politique pour permettre l'accès public en lecture aux images de profil
CREATE POLICY "Les images de profil sont accessibles publiquement"
ON storage.objects FOR SELECT
USING (bucket_id = 'profile-images');

-- Création d'une nouvelle politique adaptée à la structure de chemin utilisée
CREATE POLICY "Les utilisateurs peuvent télécharger leurs propres images"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'profile-images' AND 
  (
    -- Permet les chemins directs avec l'ID utilisateur comme premier segment
    (storage.foldername(name))[1] = auth.uid()::text
    OR
    -- Permet les chemins avec 'avatars' comme premier segment et l'ID utilisateur comme deuxième segment
    ((storage.foldername(name))[1] = 'avatars' AND (storage.foldername(name))[2] = auth.uid()::text)
    OR
    -- Permet les chemins avec 'covers' comme premier segment et l'ID utilisateur comme deuxième segment
    ((storage.foldername(name))[1] = 'covers' AND (storage.foldername(name))[2] = auth.uid()::text)
  )
);

-- Mise à jour de la politique de suppression pour correspondre à la même structure
CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres images"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'profile-images' AND 
  (
    (storage.foldername(name))[1] = auth.uid()::text
    OR
    ((storage.foldername(name))[1] = 'avatars' AND (storage.foldername(name))[2] = auth.uid()::text)
    OR
    ((storage.foldername(name))[1] = 'covers' AND (storage.foldername(name))[2] = auth.uid()::text)
  )
);