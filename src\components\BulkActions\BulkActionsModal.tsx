import React, { useState } from 'react';
import { 
  X, 
  Package, 
  DollarSign, 
  Tag, 
  BarChart3, 
  Trash2,
  CheckCircle,
  AlertCircle,
  Edit,
  Archive,
  RefreshCw,
  TrendingUp,
  Percent
} from 'lucide-react';
import './BulkActionsModal.css';

interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  category: string;
  stock: number;
  status: 'active' | 'inactive' | 'out_of_stock';
}

interface BulkActionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedProducts: Product[];
  onApplyActions: (action: BulkAction) => void;
}

export interface BulkAction {
  type: 'price' | 'category' | 'status' | 'stock' | 'delete' | 'discount';
  data: any;
}

const BulkActionsModal: React.FC<BulkActionsModalProps> = ({ 
  isOpen, 
  onClose, 
  selectedProducts, 
  onApplyActions 
}) => {
  const [activeAction, setActiveAction] = useState<string>('');
  const [actionData, setActionData] = useState<any>({});
  const [isProcessing, setIsProcessing] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const categories = [
    'Beauté', 'Cosmétiques', 'Hygiène', 'Parfums', 
    'Alimentation', 'Boissons', 'Vêtements', 'Accessoires', 
    'Électronique', 'Maison'
  ];

  const statusOptions = [
    { value: 'active', label: 'Actif', color: 'green' },
    { value: 'inactive', label: 'Inactif', color: 'gray' },
    { value: 'out_of_stock', label: 'Rupture de stock', color: 'red' }
  ];

  const handleActionChange = (action: string) => {
    setActiveAction(action);
    setActionData({});
    setErrors({});
  };

  const validateAction = () => {
    const newErrors: Record<string, string> = {};

    switch (activeAction) {
      case 'price':
        if (!actionData.priceType) {
          newErrors.priceType = 'Type de modification requis';
        }
        if (actionData.priceType === 'fixed' && (!actionData.newPrice || actionData.newPrice <= 0)) {
          newErrors.newPrice = 'Prix valide requis';
        }
        if (actionData.priceType === 'percentage' && (!actionData.percentage || actionData.percentage === 0)) {
          newErrors.percentage = 'Pourcentage valide requis';
        }
        break;
      
      case 'category':
        if (!actionData.newCategory) {
          newErrors.newCategory = 'Catégorie requise';
        }
        break;
      
      case 'status':
        if (!actionData.newStatus) {
          newErrors.newStatus = 'Statut requis';
        }
        break;
      
      case 'stock':
        if (!actionData.stockAction) {
          newErrors.stockAction = 'Action de stock requise';
        }
        if (actionData.stockAction !== 'reset' && (!actionData.stockValue || actionData.stockValue < 0)) {
          newErrors.stockValue = 'Valeur de stock valide requise';
        }
        break;
      
      case 'discount':
        if (!actionData.discountType || !actionData.discountValue || actionData.discountValue <= 0) {
          newErrors.discount = 'Remise valide requise';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleApply = async () => {
    if (!validateAction()) return;

    setIsProcessing(true);
    
    try {
      const bulkAction: BulkAction = {
        type: activeAction as BulkAction['type'],
        data: actionData
      };
      
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulation
      onApplyActions(bulkAction);
      onClose();
    } catch (error) {
      setErrors({ submit: 'Erreur lors de l\'application des actions' });
    } finally {
      setIsProcessing(false);
    }
  };

  const getPreviewText = () => {
    const count = selectedProducts.length;
    
    switch (activeAction) {
      case 'price':
        if (actionData.priceType === 'fixed') {
          return `Définir le prix à ${actionData.newPrice?.toLocaleString()} F CFA pour ${count} produit(s)`;
        } else if (actionData.priceType === 'percentage') {
          const sign = actionData.percentage > 0 ? '+' : '';
          return `${sign}${actionData.percentage}% sur le prix de ${count} produit(s)`;
        }
        break;
      
      case 'category':
        return `Changer la catégorie vers "${actionData.newCategory}" pour ${count} produit(s)`;
      
      case 'status':
        const status = statusOptions.find(s => s.value === actionData.newStatus);
        return `Changer le statut vers "${status?.label}" pour ${count} produit(s)`;
      
      case 'stock':
        if (actionData.stockAction === 'set') {
          return `Définir le stock à ${actionData.stockValue} pour ${count} produit(s)`;
        } else if (actionData.stockAction === 'add') {
          return `Ajouter ${actionData.stockValue} au stock de ${count} produit(s)`;
        } else if (actionData.stockAction === 'subtract') {
          return `Retirer ${actionData.stockValue} du stock de ${count} produit(s)`;
        } else if (actionData.stockAction === 'reset') {
          return `Remettre le stock à 0 pour ${count} produit(s)`;
        }
        break;
      
      case 'discount':
        return `Appliquer une remise de ${actionData.discountValue}${actionData.discountType === 'percentage' ? '%' : ' F CFA'} à ${count} produit(s)`;
      
      case 'delete':
        return `Supprimer définitivement ${count} produit(s)`;
    }
    
    return '';
  };

  return (
    <div className="modal-overlay">
      <div className="modal-container large">
        <div className="modal-header">
          <div className="modal-title">
            <Edit size={24} />
            <h2>Actions en lot</h2>
            <span className="selected-count">{selectedProducts.length} produit(s) sélectionné(s)</span>
          </div>
          <button className="modal-close" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="modal-content">
          {/* Sélection de l'action */}
          <div className="action-selector">
            <h3>Choisir une action :</h3>
            <div className="action-grid">
              <button
                className={`action-card ${activeAction === 'price' ? 'active' : ''}`}
                onClick={() => handleActionChange('price')}
              >
                <DollarSign size={24} />
                <span>Modifier les prix</span>
              </button>
              
              <button
                className={`action-card ${activeAction === 'category' ? 'active' : ''}`}
                onClick={() => handleActionChange('category')}
              >
                <Tag size={24} />
                <span>Changer catégorie</span>
              </button>
              
              <button
                className={`action-card ${activeAction === 'status' ? 'active' : ''}`}
                onClick={() => handleActionChange('status')}
              >
                <BarChart3 size={24} />
                <span>Modifier statut</span>
              </button>
              
              <button
                className={`action-card ${activeAction === 'stock' ? 'active' : ''}`}
                onClick={() => handleActionChange('stock')}
              >
                <Package size={24} />
                <span>Gérer le stock</span>
              </button>
              
              <button
                className={`action-card ${activeAction === 'discount' ? 'active' : ''}`}
                onClick={() => handleActionChange('discount')}
              >
                <Percent size={24} />
                <span>Appliquer remise</span>
              </button>
              
              <button
                className={`action-card danger ${activeAction === 'delete' ? 'active' : ''}`}
                onClick={() => handleActionChange('delete')}
              >
                <Trash2 size={24} />
                <span>Supprimer</span>
              </button>
            </div>
          </div>

          {/* Configuration de l'action */}
          {activeAction && (
            <div className="action-config">
              <h3>Configuration :</h3>
              
              {activeAction === 'price' && (
                <div className="config-section">
                  <div className="form-group">
                    <label>Type de modification :</label>
                    <select
                      value={actionData.priceType || ''}
                      onChange={(e) => setActionData(prev => ({ ...prev, priceType: e.target.value }))}
                      className={errors.priceType ? 'error' : ''}
                    >
                      <option value="">Sélectionner</option>
                      <option value="fixed">Prix fixe</option>
                      <option value="percentage">Pourcentage</option>
                    </select>
                    {errors.priceType && <span className="error-text">{errors.priceType}</span>}
                  </div>
                  
                  {actionData.priceType === 'fixed' && (
                    <div className="form-group">
                      <label>Nouveau prix (F CFA) :</label>
                      <input
                        type="number"
                        value={actionData.newPrice || ''}
                        onChange={(e) => setActionData(prev => ({ ...prev, newPrice: parseFloat(e.target.value) }))}
                        className={errors.newPrice ? 'error' : ''}
                        placeholder="15000"
                        min="0"
                        step="100"
                      />
                      {errors.newPrice && <span className="error-text">{errors.newPrice}</span>}
                    </div>
                  )}
                  
                  {actionData.priceType === 'percentage' && (
                    <div className="form-group">
                      <label>Pourcentage de modification (%) :</label>
                      <input
                        type="number"
                        value={actionData.percentage || ''}
                        onChange={(e) => setActionData(prev => ({ ...prev, percentage: parseFloat(e.target.value) }))}
                        className={errors.percentage ? 'error' : ''}
                        placeholder="+10 ou -15"
                        step="0.1"
                      />
                      {errors.percentage && <span className="error-text">{errors.percentage}</span>}
                      <small>Valeurs positives pour augmenter, négatives pour diminuer</small>
                    </div>
                  )}
                </div>
              )}

              {activeAction === 'category' && (
                <div className="config-section">
                  <div className="form-group">
                    <label>Nouvelle catégorie :</label>
                    <select
                      value={actionData.newCategory || ''}
                      onChange={(e) => setActionData(prev => ({ ...prev, newCategory: e.target.value }))}
                      className={errors.newCategory ? 'error' : ''}
                    >
                      <option value="">Sélectionner une catégorie</option>
                      {categories.map(cat => (
                        <option key={cat} value={cat}>{cat}</option>
                      ))}
                    </select>
                    {errors.newCategory && <span className="error-text">{errors.newCategory}</span>}
                  </div>
                </div>
              )}

              {activeAction === 'status' && (
                <div className="config-section">
                  <div className="form-group">
                    <label>Nouveau statut :</label>
                    <div className="status-options">
                      {statusOptions.map(status => (
                        <button
                          key={status.value}
                          className={`status-option ${actionData.newStatus === status.value ? 'active' : ''} ${status.color}`}
                          onClick={() => setActionData(prev => ({ ...prev, newStatus: status.value }))}
                        >
                          {status.label}
                        </button>
                      ))}
                    </div>
                    {errors.newStatus && <span className="error-text">{errors.newStatus}</span>}
                  </div>
                </div>
              )}

              {activeAction === 'stock' && (
                <div className="config-section">
                  <div className="form-group">
                    <label>Action sur le stock :</label>
                    <select
                      value={actionData.stockAction || ''}
                      onChange={(e) => setActionData(prev => ({ ...prev, stockAction: e.target.value }))}
                      className={errors.stockAction ? 'error' : ''}
                    >
                      <option value="">Sélectionner une action</option>
                      <option value="set">Définir une valeur</option>
                      <option value="add">Ajouter au stock</option>
                      <option value="subtract">Retirer du stock</option>
                      <option value="reset">Remettre à zéro</option>
                    </select>
                    {errors.stockAction && <span className="error-text">{errors.stockAction}</span>}
                  </div>
                  
                  {actionData.stockAction && actionData.stockAction !== 'reset' && (
                    <div className="form-group">
                      <label>Quantité :</label>
                      <input
                        type="number"
                        value={actionData.stockValue || ''}
                        onChange={(e) => setActionData(prev => ({ ...prev, stockValue: parseInt(e.target.value) }))}
                        className={errors.stockValue ? 'error' : ''}
                        placeholder="10"
                        min="0"
                      />
                      {errors.stockValue && <span className="error-text">{errors.stockValue}</span>}
                    </div>
                  )}
                </div>
              )}

              {activeAction === 'discount' && (
                <div className="config-section">
                  <div className="form-grid">
                    <div className="form-group">
                      <label>Type de remise :</label>
                      <select
                        value={actionData.discountType || ''}
                        onChange={(e) => setActionData(prev => ({ ...prev, discountType: e.target.value }))}
                      >
                        <option value="">Sélectionner</option>
                        <option value="percentage">Pourcentage</option>
                        <option value="fixed">Montant fixe</option>
                      </select>
                    </div>
                    
                    <div className="form-group">
                      <label>Valeur de la remise :</label>
                      <input
                        type="number"
                        value={actionData.discountValue || ''}
                        onChange={(e) => setActionData(prev => ({ ...prev, discountValue: parseFloat(e.target.value) }))}
                        placeholder={actionData.discountType === 'percentage' ? '10' : '1000'}
                        min="0"
                        step={actionData.discountType === 'percentage' ? '0.1' : '100'}
                      />
                    </div>
                  </div>
                  {errors.discount && <span className="error-text">{errors.discount}</span>}
                </div>
              )}

              {activeAction === 'delete' && (
                <div className="config-section">
                  <div className="warning-box">
                    <AlertCircle size={20} />
                    <div>
                      <strong>Attention !</strong>
                      <p>Cette action supprimera définitivement {selectedProducts.length} produit(s). Cette action est irréversible.</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Aperçu */}
          {activeAction && getPreviewText() && (
            <div className="preview-section">
              <h3>Aperçu :</h3>
              <div className="preview-box">
                <TrendingUp size={16} />
                <span>{getPreviewText()}</span>
              </div>
            </div>
          )}

          {errors.submit && (
            <div className="error-message">
              <AlertCircle size={16} />
              {errors.submit}
            </div>
          )}

          <div className="modal-actions">
            <button type="button" className="btn-secondary" onClick={onClose}>
              Annuler
            </button>
            <button 
              type="button" 
              className={`btn-primary ${activeAction === 'delete' ? 'btn-danger' : ''}`}
              onClick={handleApply}
              disabled={!activeAction || isProcessing}
            >
              {isProcessing ? (
                <>
                  <RefreshCw size={16} className="spinning" />
                  Application...
                </>
              ) : (
                <>
                  <CheckCircle size={16} />
                  Appliquer
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulkActionsModal;
