import React, { useState, useEffect } from 'react';
import {
  Shield,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Copy,
  RefreshCw,
  Calendar,
  DollarSign,
  Eye,
  Download
} from 'lucide-react';
import Card, { CardBody, CardHeader } from '../ui/Card';
import Button from '../ui/Button';
import { formatAmount } from '../../utils/formatUtils';
import { SubscriptionCode, SubscriptionCodeService } from '../../services/subscriptionCodeService';

interface SubscriptionCodeHistoryProps {
  businessId: string;
  businessName: string;
}

const SubscriptionCodeHistory: React.FC<SubscriptionCodeHistoryProps> = ({
  businessId,
  businessName
}) => {
  const [codes, setCodes] = useState<SubscriptionCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'validated' | 'rejected' | 'expired'>('all');

  useEffect(() => {
    loadCodes();
  }, [businessId]);

  const loadCodes = async () => {
    setLoading(true);
    try {
      // Récupérer tous les codes pour cette entreprise
      const allCodes = await SubscriptionCodeService.getAllCodes();
      const businessCodes = allCodes.filter(code => code.business_id === businessId);
      setCodes(businessCodes);
    } catch (error) {
      console.error('Erreur lors du chargement des codes:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredCodes = codes.filter(code => {
    if (filter === 'all') return true;
    return code.status === filter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'validated':
        return 'text-green-600 bg-green-100';
      case 'rejected':
        return 'text-red-600 bg-red-100';
      case 'expired':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock size={16} />;
      case 'validated':
        return <CheckCircle size={16} />;
      case 'rejected':
        return <XCircle size={16} />;
      case 'expired':
        return <AlertTriangle size={16} />;
      default:
        return <Shield size={16} />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'validated':
        return 'Validé';
      case 'rejected':
        return 'Rejeté';
      case 'expired':
        return 'Expiré';
      default:
        return status;
    }
  };

  const getPlanLabel = (planType: string) => {
    switch (planType) {
      case 'trial':
        return 'Essai Gratuit';
      case 'monthly':
        return 'Mensuel';
      case 'yearly':
        return 'Annuel';
      default:
        return planType;
    }
  };

  const copyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    // Vous pouvez ajouter une notification ici
  };

  const exportCodes = () => {
    const csvContent = [
      ['Code', 'Plan', 'Montant', 'Statut', 'Généré le', 'Expire le', 'Validé le'].join(','),
      ...filteredCodes.map(code => [
        code.code,
        getPlanLabel(code.plan_type),
        code.amount,
        getStatusLabel(code.status),
        new Date(code.generated_at).toLocaleDateString('fr-FR'),
        new Date(code.expires_at).toLocaleDateString('fr-FR'),
        code.validated_at ? new Date(code.validated_at).toLocaleDateString('fr-FR') : 'N/A'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `codes-abonnement-${businessName}-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Chargement de l'historique...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Shield className="text-blue-600" size={24} />
              <div>
                <h3 className="text-xl font-semibold text-gray-900">
                  Historique des Codes d'Abonnement
                </h3>
                <p className="text-gray-600">
                  Tous vos codes de validation générés
                </p>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                leftIcon={<Download size={16} />}
                onClick={exportCodes}
                disabled={filteredCodes.length === 0}
              >
                Exporter
              </Button>
              <Button
                variant="outline"
                leftIcon={<RefreshCw size={16} />}
                onClick={loadCodes}
              >
                Actualiser
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Statistiques */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card className="text-center">
          <CardBody className="p-4">
            <div className="text-2xl font-bold text-gray-900">{codes.length}</div>
            <div className="text-sm text-gray-600">Total</div>
          </CardBody>
        </Card>
        <Card className="text-center">
          <CardBody className="p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {codes.filter(c => c.status === 'pending').length}
            </div>
            <div className="text-sm text-gray-600">En attente</div>
          </CardBody>
        </Card>
        <Card className="text-center">
          <CardBody className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {codes.filter(c => c.status === 'validated').length}
            </div>
            <div className="text-sm text-gray-600">Validés</div>
          </CardBody>
        </Card>
        <Card className="text-center">
          <CardBody className="p-4">
            <div className="text-2xl font-bold text-red-600">
              {codes.filter(c => c.status === 'rejected').length}
            </div>
            <div className="text-sm text-gray-600">Rejetés</div>
          </CardBody>
        </Card>
        <Card className="text-center">
          <CardBody className="p-4">
            <div className="text-2xl font-bold text-gray-600">
              {codes.filter(c => c.status === 'expired').length}
            </div>
            <div className="text-sm text-gray-600">Expirés</div>
          </CardBody>
        </Card>
      </div>

      {/* Filtres */}
      <Card>
        <CardBody className="p-4">
          <div className="flex flex-wrap gap-2">
            {[
              { value: 'all', label: 'Tous' },
              { value: 'pending', label: 'En attente' },
              { value: 'validated', label: 'Validés' },
              { value: 'rejected', label: 'Rejetés' },
              { value: 'expired', label: 'Expirés' }
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => setFilter(option.value as any)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  filter === option.value
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Liste des codes */}
      {filteredCodes.length === 0 ? (
        <Card>
          <CardBody className="p-8 text-center">
            <Shield size={48} className="text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucun code trouvé
            </h3>
            <p className="text-gray-600">
              {filter === 'all' 
                ? 'Vous n\'avez pas encore généré de codes d\'abonnement.'
                : `Aucun code ${getStatusLabel(filter).toLowerCase()} trouvé.`
              }
            </p>
          </CardBody>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredCodes.map((code) => (
            <Card key={code.id} className="hover:shadow-md transition-shadow">
              <CardBody className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Shield className="text-blue-600" size={24} />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-xl font-mono font-bold text-blue-600">
                          {SubscriptionCodeService.formatCode(code.code)}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyCode(code.code)}
                          className="p-1"
                        >
                          <Copy size={14} />
                        </Button>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span className="flex items-center space-x-1">
                          <Calendar size={14} />
                          <span>{getPlanLabel(code.plan_type)}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <DollarSign size={14} />
                          <span>{formatAmount(code.amount)}</span>
                        </span>
                        <span>
                          Généré le {new Date(code.generated_at).toLocaleDateString('fr-FR')}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(code.status)}`}>
                      {getStatusIcon(code.status)}
                      <span className="ml-1">{getStatusLabel(code.status)}</span>
                    </span>
                    {code.status === 'pending' && (
                      <div className="text-sm text-gray-600">
                        {SubscriptionCodeService.isCodeExpired(code) ? (
                          <span className="text-red-600 font-medium">Expiré</span>
                        ) : (
                          <span>Expire dans {SubscriptionCodeService.getTimeUntilExpiration(code)}</span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Détails supplémentaires */}
                {(code.validated_at || code.rejection_reason) && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    {code.validated_at && (
                      <div className="text-sm text-green-700">
                        ✅ Validé le {new Date(code.validated_at).toLocaleDateString('fr-FR')} à {new Date(code.validated_at).toLocaleTimeString('fr-FR')}
                      </div>
                    )}
                    {code.rejection_reason && (
                      <div className="text-sm text-red-700">
                        ❌ Rejeté : {code.rejection_reason}
                      </div>
                    )}
                  </div>
                )}
              </CardBody>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default SubscriptionCodeHistory;
