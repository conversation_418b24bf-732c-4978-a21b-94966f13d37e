-- DÉPLOIEMENT RAPIDE DES ABONNEMENTS ENTREPRISE
-- Exécutez ce script dans Supabase SQL Editor pour déployer rapidement le système

-- =====================================================
-- 1. CRÉER LES TABLES D'ABONNEMENT
-- =====================================================

-- Table des plans d'abonnement
CREATE TABLE IF NOT EXISTS subscription_plans (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'XOF',
    duration_days INTEGER NOT NULL DEFAULT 30,
    features JSONB NOT NULL DEFAULT '[]',
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_trial BOOLEAN NOT NULL DEFAULT false,
    plan_type VARCHAR(20) NOT NULL DEFAULT 'monthly' CHECK (plan_type IN ('trial', 'monthly', 'yearly')),
    original_price DECIMAL(10,2),
    savings_text VARCHAR(100),
    is_popular BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table des abonnements business
CREATE TABLE IF NOT EXISTS business_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    business_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(id),
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_trial BOOLEAN NOT NULL DEFAULT false,
    auto_renew BOOLEAN NOT NULL DEFAULT false,
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les performances
CREATE INDEX IF NOT EXISTS idx_business_subscriptions_business_id ON business_subscriptions(business_id);
CREATE INDEX IF NOT EXISTS idx_business_subscriptions_active ON business_subscriptions(is_active);

-- =====================================================
-- 2. INSÉRER LES PLANS D'ABONNEMENT
-- =====================================================

-- Supprimer les anciens plans s'ils existent
DELETE FROM subscription_plans WHERE id IN ('trial-7days', 'monthly-plan', 'yearly-plan');

-- Plan Essai Gratuit 7 jours
INSERT INTO subscription_plans (
    id, name, description, price, currency, duration_days, 
    plan_type, is_trial, is_popular, features
) VALUES (
    'trial-7days',
    'Essai Gratuit',
    'Découvrez toutes nos fonctionnalités pendant 7 jours',
    0.00,
    'XOF',
    7,
    'trial',
    true,
    false,
    '[
        "Accès complet à toutes les fonctionnalités",
        "Gestion illimitée des avis clients", 
        "Tableaux de bord interactifs",
        "Notifications en temps réel",
        "Support par email",
        "Aucun engagement"
    ]'::jsonb
);

-- Plan Mensuel
INSERT INTO subscription_plans (
    id, name, description, price, currency, duration_days,
    plan_type, is_trial, is_popular, features
) VALUES (
    'monthly-plan',
    'Abonnement Mensuel', 
    'Plan mensuel flexible pour votre entreprise',
    25000.00,
    'XOF',
    30,
    'monthly',
    false,
    false,
    '[
        "Toutes les fonctionnalités incluses",
        "Gestion illimitée des avis",
        "Analytics avancées", 
        "Support prioritaire",
        "Intégrations API",
        "Sauvegarde automatique",
        "Rapports personnalisés"
    ]'::jsonb
);

-- Plan Annuel (avec réduction)
INSERT INTO subscription_plans (
    id, name, description, price, original_price, currency, duration_days,
    plan_type, is_trial, is_popular, savings_text, features
) VALUES (
    'yearly-plan',
    'Abonnement Annuel',
    'Économisez 20% avec notre plan annuel', 
    240000.00,
    300000.00,
    'XOF',
    365,
    'yearly',
    false,
    true,
    'Économisez 60 000 F CFA',
    '[
        "Toutes les fonctionnalités Premium",
        "Gestion illimitée des avis",
        "Analytics avancées + IA",
        "Support prioritaire 24/7", 
        "Intégrations API complètes",
        "Sauvegarde automatique",
        "Rapports personnalisés",
        "Conseiller dédié",
        "Formation personnalisée"
    ]'::jsonb
);

-- =====================================================
-- 3. CRÉER LES FONCTIONS DE BASE
-- =====================================================

-- Fonction pour vérifier si un utilisateur a déjà utilisé l'essai
CREATE OR REPLACE FUNCTION has_used_trial(p_business_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    trial_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO trial_count
    FROM business_subscriptions
    WHERE business_id = p_business_id AND is_trial = true;
    
    RETURN trial_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour créer un nouvel abonnement
CREATE OR REPLACE FUNCTION create_subscription(
    p_business_id UUID,
    p_plan_id VARCHAR(50),
    p_auto_renew BOOLEAN DEFAULT true
)
RETURNS UUID AS $$
DECLARE
    subscription_id UUID;
    plan_duration INTEGER;
    plan_is_trial BOOLEAN;
    start_date TIMESTAMP WITH TIME ZONE;
    end_date TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Récupérer les informations du plan
    SELECT duration_days, is_trial INTO plan_duration, plan_is_trial
    FROM subscription_plans
    WHERE id = p_plan_id AND is_active = true;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Plan d''abonnement non trouvé ou inactif: %', p_plan_id;
    END IF;
    
    -- Vérifier si l'essai a déjà été utilisé
    IF plan_is_trial AND has_used_trial(p_business_id) THEN
        RAISE EXCEPTION 'L''essai gratuit a déjà été utilisé pour cette entreprise';
    END IF;
    
    -- Désactiver l'abonnement actuel s'il existe
    UPDATE business_subscriptions 
    SET is_active = false, updated_at = CURRENT_TIMESTAMP
    WHERE business_id = p_business_id AND is_active = true;
    
    -- Calculer les dates
    start_date := CURRENT_TIMESTAMP;
    end_date := start_date + (plan_duration || ' days')::INTERVAL;
    
    -- Créer le nouvel abonnement
    INSERT INTO business_subscriptions (
        business_id, plan_id, start_date, end_date,
        is_active, is_trial, auto_renew,
        payment_status, payment_method, stripe_subscription_id
    ) VALUES (
        p_business_id, p_plan_id, start_date, end_date,
        true, plan_is_trial, CASE WHEN plan_is_trial THEN false ELSE p_auto_renew END,
        'paid', NULL, NULL
    ) RETURNING id INTO subscription_id;
    
    RETURN subscription_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. CONFIGURER LA SÉCURITÉ RLS
-- =====================================================

-- Activer RLS sur les tables
ALTER TABLE business_subscriptions ENABLE ROW LEVEL SECURITY;

-- Politique pour que les entreprises ne voient que leurs abonnements
DROP POLICY IF EXISTS "business_subscriptions_policy" ON business_subscriptions;
CREATE POLICY "business_subscriptions_policy" ON business_subscriptions
    FOR ALL TO authenticated
    USING (business_id = auth.uid());

-- =====================================================
-- 5. CRÉER DES TRIGGERS POUR L'AUDIT
-- =====================================================

-- Fonction de trigger pour mettre à jour updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers pour updated_at
DROP TRIGGER IF EXISTS update_subscription_plans_updated_at ON subscription_plans;
CREATE TRIGGER update_subscription_plans_updated_at
    BEFORE UPDATE ON subscription_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_business_subscriptions_updated_at ON business_subscriptions;
CREATE TRIGGER update_business_subscriptions_updated_at
    BEFORE UPDATE ON business_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 6. AFFICHER LES RÉSULTATS
-- =====================================================

-- Vérifier les plans créés
SELECT 
    '✅ PLANS D''ABONNEMENT CRÉÉS' as status,
    id,
    name,
    CASE 
        WHEN price = 0 THEN 'Gratuit'
        ELSE price::text || ' ' || currency
    END as prix,
    duration_days || ' jours' as duree,
    plan_type,
    CASE WHEN is_trial THEN 'Oui' ELSE 'Non' END as essai,
    CASE WHEN is_popular THEN 'Oui' ELSE 'Non' END as populaire
FROM subscription_plans 
ORDER BY 
    CASE plan_type 
        WHEN 'trial' THEN 1 
        WHEN 'monthly' THEN 2 
        WHEN 'yearly' THEN 3 
    END;

-- Message de succès
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '🎉 SYSTÈME D''ABONNEMENTS DÉPLOYÉ AVEC SUCCÈS !';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '💳 PLANS DISPONIBLES:';
    RAISE NOTICE '   🎁 Essai Gratuit - 7 jours - 0 F CFA';
    RAISE NOTICE '   📅 Mensuel - 30 jours - 25 000 F CFA';
    RAISE NOTICE '   👑 Annuel - 365 jours - 240 000 F CFA (Populaire)';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 PROCHAINES ÉTAPES:';
    RAISE NOTICE '1. Testez l''interface dans le profil entreprise';
    RAISE NOTICE '2. Cliquez sur l''onglet "Mon abonnement"';
    RAISE NOTICE '3. Testez les 3 types d''abonnements';
    RAISE NOTICE '';
    RAISE NOTICE '🎊 SYSTÈME OPÉRATIONNEL !';
    RAISE NOTICE '';
END $$;
