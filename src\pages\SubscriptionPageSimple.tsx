import React, { useState, useEffect } from 'react';
import {
  CreditCard, Calendar, CheckCircle, XCircle, AlertTriangle,
  Crown, Star, Shield, Gift, Clock, TrendingUp,
  Package, Users, BarChart3, Headphones, RefreshCw
} from 'lucide-react';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import Button from '../components/ui/Button';
import { useAuth } from '../context/AuthContext';

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  duration: number; // en jours
  features: string[];
  icon: React.ReactNode;
  color: string;
  popular?: boolean;
  description: string;
}

interface SubscriptionStatus {
  isActive: boolean;
  plan: string;
  planName: string;
  startDate: string;
  endDate: string;
  daysRemaining: number;
  isTrialPeriod: boolean;
  autoRenew: boolean;
}

const SubscriptionPageSimple: React.FC = () => {
  const { currentUser } = useAuth();
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  // Plans d'abonnement disponibles
  const subscriptionPlans: SubscriptionPlan[] = [
    {
      id: 'free-trial',
      name: 'Essai Gratuit',
      price: 0,
      duration: 7,
      description: 'Découvrez toutes nos fonctionnalités pendant 7 jours',
      icon: <Gift className="text-green-600" size={24} />,
      color: 'green',
      features: [
        'Accès complet à l\'IA Conseiller',
        'Tableaux de bord interactifs',
        'Gestion des avis clients',
        'Notifications automatiques',
        'Support par email',
        'Jusqu\'à 100 avis analysés'
      ]
    },
    {
      id: 'starter',
      name: 'Starter',
      price: 15000, // F CFA
      duration: 30,
      description: 'Parfait pour les petites entreprises qui démarrent',
      icon: <Star className="text-blue-600" size={24} />,
      color: 'blue',
      features: [
        'Toutes les fonctionnalités de l\'essai',
        'Jusqu\'à 500 avis analysés/mois',
        'Recommandations IA illimitées',
        'Export des rapports PDF',
        'Support prioritaire',
        'Intégration API basique'
      ]
    },
    {
      id: 'professional',
      name: 'Professional',
      price: 35000, // F CFA
      duration: 30,
      description: 'Pour les entreprises en croissance',
      icon: <Crown className="text-purple-600" size={24} />,
      color: 'purple',
      popular: true,
      features: [
        'Toutes les fonctionnalités Starter',
        'Avis illimités',
        'Analyses prédictives avancées',
        'Tableaux de bord personnalisés',
        'Support téléphonique 24/7',
        'Intégration API complète',
        'Formation personnalisée',
        'Gestionnaire de compte dédié'
      ]
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: 75000, // F CFA
      duration: 30,
      description: 'Solution complète pour les grandes entreprises',
      icon: <Shield className="text-yellow-600" size={24} />,
      color: 'yellow',
      features: [
        'Toutes les fonctionnalités Professional',
        'Multi-comptes et équipes',
        'Analyses personnalisées',
        'SLA garanti 99.9%',
        'Support dédié premium',
        'Intégrations sur mesure',
        'Formations d\'équipe',
        'Rapports exécutifs mensuels'
      ]
    }
  ];

  useEffect(() => {
    loadSubscriptionStatus();
  }, []);

  const loadSubscriptionStatus = async () => {
    setLoading(true);
    try {
      // Simuler le chargement avec des données de démonstration
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const demoStatus: SubscriptionStatus = {
        isActive: true,
        plan: 'free-trial',
        planName: 'Essai Gratuit',
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        daysRemaining: 7,
        isTrialPeriod: true,
        autoRenew: false
      };
      setSubscriptionStatus(demoStatus);
      
    } catch (error) {
      console.error('Erreur lors du chargement du statut d\'abonnement:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (planId: string) => {
    setSelectedPlan(planId);
    
    try {
      const plan = subscriptionPlans.find(p => p.id === planId);
      if (!plan) {
        alert('Plan non trouvé');
        return;
      }

      // Simuler le processus de souscription
      await new Promise(resolve => setTimeout(resolve, 2000));

      if (planId === 'free-trial') {
        // Pour l'essai gratuit
        const demoStatus: SubscriptionStatus = {
          isActive: true,
          plan: 'free-trial',
          planName: 'Essai Gratuit',
          startDate: new Date().toISOString(),
          endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          daysRemaining: 7,
          isTrialPeriod: true,
          autoRenew: false
        };
        setSubscriptionStatus(demoStatus);
        alert('Essai gratuit de 7 jours activé avec succès !');
      } else {
        // Pour les plans payants
        alert(`Redirection vers le paiement pour le plan ${plan.name} (${formatPrice(plan.price)})...`);
        
        // Simuler un paiement réussi
        const paidStatus: SubscriptionStatus = {
          isActive: true,
          plan: planId,
          planName: plan.name,
          startDate: new Date().toISOString(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          daysRemaining: 30,
          isTrialPeriod: false,
          autoRenew: true
        };
        setSubscriptionStatus(paidStatus);
        alert('Paiement réussi ! Votre abonnement a été activé.');
      }
    } catch (error) {
      console.error('Erreur lors de la souscription:', error);
      alert('Erreur lors de la souscription. Veuillez réessayer.');
    } finally {
      setSelectedPlan(null);
    }
  };

  const getStatusColor = (status: SubscriptionStatus) => {
    if (!status.isActive) return 'red';
    if (status.isTrialPeriod) return 'blue';
    if (status.daysRemaining <= 3) return 'yellow';
    return 'green';
  };

  const getStatusIcon = (status: SubscriptionStatus) => {
    if (!status.isActive) return <XCircle className="text-red-600" size={24} />;
    if (status.isTrialPeriod) return <Clock className="text-blue-600" size={24} />;
    if (status.daysRemaining <= 3) return <AlertTriangle className="text-yellow-600" size={24} />;
    return <CheckCircle className="text-green-600" size={24} />;
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price) + ' F CFA';
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <RefreshCw className="mx-auto text-blue-600 mb-4 animate-spin" size={48} />
            <p className="text-gray-600">Chargement de votre abonnement...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* En-tête */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Mon Abonnement</h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Gérez votre abonnement et accédez aux fonctionnalités avancées de Customeroom Business
        </p>
      </div>

      {/* Statut actuel de l'abonnement */}
      {subscriptionStatus && (
        <Card className={`border-l-4 border-${getStatusColor(subscriptionStatus)}-500`}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getStatusIcon(subscriptionStatus)}
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    Statut de l'abonnement
                  </h2>
                  <p className="text-gray-600">
                    {subscriptionStatus.isTrialPeriod ? 'Période d\'essai gratuite' : 'Abonnement actif'}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-2xl font-bold text-${getStatusColor(subscriptionStatus)}-600`}>
                  {subscriptionStatus.daysRemaining} jours
                </div>
                <div className="text-sm text-gray-600">restants</div>
              </div>
            </div>
          </CardHeader>
          
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Plan actuel</h4>
                <p className="text-gray-600 capitalize">
                  {subscriptionStatus.planName}
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Date d'expiration</h4>
                <p className="text-gray-600">
                  {new Date(subscriptionStatus.endDate).toLocaleDateString('fr-FR')}
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Renouvellement automatique</h4>
                <p className={`font-medium ${subscriptionStatus.autoRenew ? 'text-green-600' : 'text-gray-600'}`}>
                  {subscriptionStatus.autoRenew ? 'Activé' : 'Désactivé'}
                </p>
              </div>
            </div>

            {subscriptionStatus.isTrialPeriod && subscriptionStatus.daysRemaining <= 3 && (
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="text-yellow-600 mt-0.5" size={20} />
                  <div>
                    <h4 className="font-medium text-yellow-800">Votre essai gratuit expire bientôt !</h4>
                    <p className="text-yellow-700 text-sm mt-1">
                      Choisissez un plan pour continuer à bénéficier de toutes nos fonctionnalités.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardBody>
        </Card>
      )}

      {/* Plans d'abonnement */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">
          Choisissez votre plan
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {subscriptionPlans.map((plan) => (
            <Card 
              key={plan.id} 
              className={`relative ${plan.popular ? 'ring-2 ring-purple-500 shadow-lg' : ''} hover:shadow-lg transition-shadow`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                    Plus populaire
                  </span>
                </div>
              )}
              
              <CardHeader className="text-center">
                <div className="flex justify-center mb-3">
                  {plan.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900">{plan.name}</h3>
                <p className="text-gray-600 text-sm mt-1">{plan.description}</p>
                <div className="mt-4">
                  <div className="text-3xl font-bold text-gray-900">
                    {plan.price === 0 ? 'Gratuit' : formatPrice(plan.price)}
                  </div>
                  <div className="text-sm text-gray-600">
                    {plan.price === 0 ? '7 jours' : 'par mois'}
                  </div>
                </div>
              </CardHeader>
              
              <CardBody>
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <CheckCircle className="text-green-500 mt-0.5 flex-shrink-0" size={16} />
                      <span className="text-sm text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button
                  onClick={() => handleSubscribe(plan.id)}
                  variant={plan.popular ? 'primary' : 'outline'}
                  className="w-full"
                  disabled={selectedPlan === plan.id || (subscriptionStatus?.plan === plan.id && subscriptionStatus.isActive)}
                >
                  {selectedPlan === plan.id ? (
                    <>
                      <RefreshCw className="animate-spin mr-2" size={16} />
                      Traitement...
                    </>
                  ) : subscriptionStatus?.plan === plan.id && subscriptionStatus.isActive ? (
                    'Plan actuel'
                  ) : plan.price === 0 ? (
                    'Commencer l\'essai'
                  ) : (
                    'Choisir ce plan'
                  )}
                </Button>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>

      {/* Note de démonstration */}
      <Card className="bg-blue-50 border-blue-200">
        <CardBody>
          <div className="text-center">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">🎯 Mode Démonstration</h3>
            <p className="text-blue-700">
              Cette page fonctionne en mode démonstration avec des données simulées. 
              Tous les processus de paiement sont simulés et aucun vrai paiement n'est effectué.
            </p>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default SubscriptionPageSimple;
