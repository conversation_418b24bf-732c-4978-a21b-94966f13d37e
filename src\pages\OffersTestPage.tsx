import React from 'react';
import { Tag, Zap, Gift, Star, TrendingUp, Users, Calendar, Percent } from 'lucide-react';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import Button from '../components/ui/Button';
import OfferImageDiagnostic from '../components/offers/OfferImageDiagnostic';
import { useNavigate } from 'react-router-dom';

const OffersTestPage: React.FC = () => {
  const navigate = useNavigate();

  const improvements = [
    {
      title: "Images réelles",
      description: "Remplacement des SVG locaux par des images Unsplash",
      icon: <Star className="text-yellow-500" size={20} />,
      status: "✅ Implémenté"
    },
    {
      title: "Design moderne",
      description: "Interface repensée avec Tailwind CSS et composants modernes",
      icon: <Zap className="text-blue-500" size={20} />,
      status: "✅ Implémenté"
    },
    {
      title: "Gestion d'erreur",
      description: "Fallback automatique pour les images qui ne se chargent pas",
      icon: <Gift className="text-green-500" size={20} />,
      status: "✅ Implémenté"
    },
    {
      title: "Animations fluides",
      description: "Effets hover et transitions CSS pour une meilleure UX",
      icon: <TrendingUp className="text-purple-500" size={20} />,
      status: "✅ Implémenté"
    }
  ];

  const features = [
    {
      title: "8 offres de démonstration",
      description: "Variété de catégories et types d'offres",
      value: "8",
      color: "blue"
    },
    {
      title: "Filtres avancés",
      description: "Par catégorie, type et tri personnalisé",
      value: "3",
      color: "green"
    },
    {
      title: "Types d'offres",
      description: "Promotions, offres et publicités",
      value: "3",
      color: "purple"
    },
    {
      title: "Réduction maximale",
      description: "Jusqu'à 50% de réduction disponible",
      value: "50%",
      color: "orange"
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* En-tête */}
      <div className="text-center">
        <div className="flex items-center justify-center space-x-3 mb-4">
          <div className="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-full">
            <Tag className="text-white" size={24} />
          </div>
          <h1 className="text-4xl font-bold text-gray-900">Test Offres et Promotions</h1>
        </div>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Page de test pour vérifier les améliorations apportées à l'onglet "Offres et Promotions"
        </p>
      </div>

      {/* Statistiques des améliorations */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {features.map((feature, index) => (
          <Card key={index}>
            <CardBody className="text-center">
              <div className={`text-3xl font-bold text-${feature.color}-600 mb-2`}>
                {feature.value}
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">{feature.title}</h3>
              <p className="text-sm text-gray-600">{feature.description}</p>
            </CardBody>
          </Card>
        ))}
      </div>

      {/* Améliorations apportées */}
      <Card>
        <CardHeader>
          <h2 className="text-2xl font-bold text-gray-900">🚀 Améliorations Apportées</h2>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {improvements.map((improvement, index) => (
              <div key={index} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  {improvement.icon}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-1">{improvement.title}</h3>
                  <p className="text-gray-600 text-sm mb-2">{improvement.description}</p>
                  <span className="text-green-600 text-sm font-medium">{improvement.status}</span>
                </div>
              </div>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Détails techniques */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <h3 className="text-xl font-semibold text-gray-900">🔧 Corrections Techniques</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="border-l-4 border-red-500 pl-4">
                <h4 className="font-semibold text-red-900">Problème résolu</h4>
                <p className="text-red-700 text-sm">Images SVG locales qui ne s'affichaient pas</p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-semibold text-green-900">Solution appliquée</h4>
                <p className="text-green-700 text-sm">Images Unsplash avec fallback automatique</p>
              </div>
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold text-blue-900">Design modernisé</h4>
                <p className="text-blue-700 text-sm">Interface repensée avec Tailwind CSS</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="text-xl font-semibold text-gray-900">🎨 Nouvelles Fonctionnalités</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Badges de type d'offre avec icônes</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Indicateurs de temps restant</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Effets hover avec zoom d'image</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Boutons d'action (J'aime, Partager)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Statistiques en temps réel</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Filtres et tri améliorés</span>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Images utilisées */}
      <Card>
        <CardHeader>
          <h3 className="text-xl font-semibold text-gray-900">🖼️ Sources d'Images</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Images principales</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded"></div>
                  <span>Cosmétiques - Unsplash</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded"></div>
                  <span>Soins de la peau - Unsplash</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-purple-500 rounded"></div>
                  <span>Produits naturels - Unsplash</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-orange-500 rounded"></div>
                  <span>Électronique - Unsplash</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Gestion d'erreur</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded"></div>
                  <span>Fallback automatique</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded"></div>
                  <span>Image de remplacement</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded"></div>
                  <span>Chargement optimisé</span>
                </div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Actions de test */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
        <CardBody>
          <div className="text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">🧪 Tester les Améliorations</h3>
            <p className="text-gray-600 mb-6">
              Cliquez sur le bouton ci-dessous pour accéder à la page "Offres et Promotions" modernisée
            </p>
            
            <div className="flex flex-wrap justify-center gap-4">
              <Button
                onClick={() => navigate('/offers')}
                size="lg"
                leftIcon={<Tag size={20} />}
                className="font-semibold"
              >
                Voir Offres et Promotions
              </Button>
              
              <Button
                onClick={() => navigate('/features')}
                variant="outline"
                size="lg"
                leftIcon={<Star size={20} />}
              >
                Toutes les Fonctionnalités
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Instructions de test */}
      <Card className="border-l-4 border-blue-500">
        <CardBody>
          <h3 className="font-semibold text-blue-900 mb-3">📋 Instructions de Test</h3>
          <ol className="list-decimal list-inside space-y-2 text-blue-800 text-sm">
            <li>Accédez à la page "Offres et Promotions" via le menu ou le bouton ci-dessus</li>
            <li>Vérifiez que toutes les images se chargent correctement</li>
            <li>Testez les filtres par catégorie et type d'offre</li>
            <li>Observez les effets hover sur les cartes d'offres</li>
            <li>Vérifiez les badges de réduction et de type d'offre</li>
            <li>Testez la recherche avec différents mots-clés</li>
            <li>Observez les indicateurs de temps restant</li>
            <li>Testez les boutons d'action (J'aime, Partager, Voir l'offre)</li>
          </ol>
        </CardBody>
      </Card>

      {/* Diagnostic des images */}
      <OfferImageDiagnostic />

      {/* Statut */}
      <div className="text-center py-8">
        <div className="inline-flex items-center space-x-2 bg-green-100 text-green-800 px-6 py-3 rounded-full">
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          <span className="font-semibold">Page "Offres et Promotions" modernisée et fonctionnelle</span>
        </div>
      </div>
    </div>
  );
};

export default OffersTestPage;
