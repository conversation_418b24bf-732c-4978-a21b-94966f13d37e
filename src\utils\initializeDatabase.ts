import { supabase } from '../lib/supabase';

/**
 * Initialise les tables nécessaires pour le système de suivi de commande
 */
export async function initializeOrderTrackingTables(): Promise<boolean> {
  try {
    console.log('🚀 Initialisation des tables de suivi de commande...');

    // Vérifier si les tables existent déjà
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['order_tracking', 'tracking_events', 'notifications']);

    if (tablesError) {
      console.error('Erreur lors de la vérification des tables:', tablesError);
      return false;
    }

    const existingTables = tables?.map(t => t.table_name) || [];
    console.log('📋 Tables existantes:', existingTables);

    // Si toutes les tables existent, pas besoin de les créer
    if (existingTables.includes('order_tracking') && 
        existingTables.includes('tracking_events') && 
        existingTables.includes('notifications')) {
      console.log('✅ Toutes les tables existent déjà');
      return true;
    }

    // Créer les tables manquantes
    const sqlCommands = [
      // Table order_tracking
      `CREATE TABLE IF NOT EXISTS order_tracking (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        order_id UUID NOT NULL,
        tracking_number VARCHAR(50) UNIQUE NOT NULL,
        current_status VARCHAR(50) NOT NULL DEFAULT 'order_confirmed',
        estimated_delivery TIMESTAMPTZ,
        actual_delivery TIMESTAMPTZ,
        carrier VARCHAR(100) DEFAULT 'Livraison Standard',
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      );`,

      // Table tracking_events
      `CREATE TABLE IF NOT EXISTS tracking_events (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        tracking_id UUID NOT NULL REFERENCES order_tracking(id) ON DELETE CASCADE,
        status VARCHAR(50) NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        location VARCHAR(200),
        metadata JSONB,
        created_at TIMESTAMPTZ DEFAULT NOW()
      );`,

      // Table notifications
      `CREATE TABLE IF NOT EXISTS notifications (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id UUID NOT NULL,
        type VARCHAR(50) NOT NULL,
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        data JSONB,
        read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMPTZ DEFAULT NOW()
      );`,

      // Index pour les performances
      `CREATE INDEX IF NOT EXISTS idx_order_tracking_order_id ON order_tracking(order_id);`,
      `CREATE INDEX IF NOT EXISTS idx_order_tracking_tracking_number ON order_tracking(tracking_number);`,
      `CREATE INDEX IF NOT EXISTS idx_tracking_events_tracking_id ON tracking_events(tracking_id);`,
      `CREATE INDEX IF NOT EXISTS idx_tracking_events_status ON tracking_events(status);`,
      `CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);`,
      `CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);`,

      // Fonction pour updated_at
      `CREATE OR REPLACE FUNCTION update_updated_at_column()
       RETURNS TRIGGER AS $$
       BEGIN
           NEW.updated_at = NOW();
           RETURN NEW;
       END;
       $$ language 'plpgsql';`,

      // Trigger pour order_tracking
      `DROP TRIGGER IF EXISTS update_order_tracking_updated_at ON order_tracking;`,
      `CREATE TRIGGER update_order_tracking_updated_at 
       BEFORE UPDATE ON order_tracking 
       FOR EACH ROW 
       EXECUTE FUNCTION update_updated_at_column();`
    ];

    // Exécuter les commandes SQL
    for (const sql of sqlCommands) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
        if (error) {
          console.warn('⚠️ Commande SQL ignorée (peut-être déjà existante):', error.message);
        }
      } catch (err) {
        console.warn('⚠️ Erreur lors de l\'exécution SQL:', err);
        // Continuer même en cas d'erreur (la table existe peut-être déjà)
      }
    }

    console.log('✅ Initialisation des tables terminée');
    return true;

  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation des tables:', error);
    return false;
  }
}

/**
 * Crée des données de test pour le système de suivi
 */
export async function createTestTrackingData(): Promise<boolean> {
  try {
    console.log('🧪 Création de données de test pour le suivi...');

    // Vérifier si des données de test existent déjà
    const { data: existingTracking, error: checkError } = await supabase
      .from('order_tracking')
      .select('id')
      .limit(1);

    if (checkError) {
      console.error('Erreur lors de la vérification des données:', checkError);
      return false;
    }

    if (existingTracking && existingTracking.length > 0) {
      console.log('✅ Des données de test existent déjà');
      return true;
    }

    // Créer des données de test
    const testTrackingData = [
      {
        order_id: '00000000-0000-0000-0000-000000000001',
        tracking_number: 'TRK123456789',
        current_status: 'delivered',
        estimated_delivery: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
        carrier: 'Livraison Express'
      },
      {
        order_id: '00000000-0000-0000-0000-000000000002',
        tracking_number: 'TRK987654321',
        current_status: 'in_transit',
        estimated_delivery: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
        carrier: 'Livraison Standard'
      }
    ];

    const { data: trackingInserted, error: trackingError } = await supabase
      .from('order_tracking')
      .insert(testTrackingData)
      .select();

    if (trackingError) {
      console.error('Erreur lors de l\'insertion des données de suivi:', trackingError);
      return false;
    }

    // Créer des événements de test
    if (trackingInserted && trackingInserted.length > 0) {
      const testEvents = [
        {
          tracking_id: trackingInserted[0].id,
          status: 'order_confirmed',
          title: 'Commande confirmée',
          description: 'Votre commande a été confirmée et est en cours de traitement',
          location: 'Abidjan, Côte d\'Ivoire'
        },
        {
          tracking_id: trackingInserted[0].id,
          status: 'in_transit',
          title: 'En transit',
          description: 'Votre commande est en route',
          location: 'Centre de tri Abidjan'
        },
        {
          tracking_id: trackingInserted[0].id,
          status: 'delivered',
          title: 'Livrée',
          description: 'Votre commande a été livrée avec succès',
          location: 'Abidjan, Côte d\'Ivoire'
        }
      ];

      const { error: eventsError } = await supabase
        .from('tracking_events')
        .insert(testEvents);

      if (eventsError) {
        console.error('Erreur lors de l\'insertion des événements:', eventsError);
        return false;
      }
    }

    console.log('✅ Données de test créées avec succès');
    return true;

  } catch (error) {
    console.error('❌ Erreur lors de la création des données de test:', error);
    return false;
  }
}

/**
 * Initialise complètement le système de suivi de commande
 */
export async function initializeOrderTrackingSystem(): Promise<boolean> {
  console.log('🎯 Initialisation complète du système de suivi de commande...');
  
  const tablesInitialized = await initializeOrderTrackingTables();
  if (!tablesInitialized) {
    console.error('❌ Échec de l\'initialisation des tables');
    return false;
  }

  const testDataCreated = await createTestTrackingData();
  if (!testDataCreated) {
    console.warn('⚠️ Échec de la création des données de test (non critique)');
  }

  console.log('🎉 Système de suivi de commande initialisé avec succès !');
  return true;
}

/**
 * Vérifie si le système de suivi est correctement configuré
 */
export async function checkOrderTrackingSystem(): Promise<{
  isConfigured: boolean;
  missingTables: string[];
  hasTestData: boolean;
}> {
  try {
    const missingTables: string[] = [];
    let hasTestData = false;

    // Vérifier order_tracking
    try {
      const { data, error } = await supabase
        .from('order_tracking')
        .select('id')
        .limit(1);
      
      if (error) missingTables.push('order_tracking');
      else if (data && data.length > 0) hasTestData = true;
    } catch {
      missingTables.push('order_tracking');
    }

    // Vérifier tracking_events
    try {
      await supabase
        .from('tracking_events')
        .select('id')
        .limit(1);
    } catch {
      missingTables.push('tracking_events');
    }

    // Vérifier notifications
    try {
      await supabase
        .from('notifications')
        .select('id')
        .limit(1);
    } catch {
      missingTables.push('notifications');
    }

    return {
      isConfigured: missingTables.length === 0,
      missingTables,
      hasTestData
    };

  } catch (error) {
    console.error('Erreur lors de la vérification du système:', error);
    return {
      isConfigured: false,
      missingTables: ['order_tracking', 'tracking_events', 'notifications'],
      hasTestData: false
    };
  }
}
