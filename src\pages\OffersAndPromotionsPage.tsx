import React, { useState, useEffect } from 'react';
import {
  Tag, Search, ChevronDown, ArrowDownUp,
  Clock, ExternalLink, Heart, Share2,
  Calendar, Zap, Gift
} from 'lucide-react';
import Card, { CardBody } from '../components/ui/Card';
import Button from '../components/ui/Button';
import { useAuth } from '../context/AuthContext';
import { adInteractionService } from '../services/adInteractionService';
import { offerInteractionService } from '../services/offerInteractionService';
import OfferShareModal from '../components/offers/OfferShareModal';
import { realCameroonianOffers } from '../data/realOffersData';
import { UserRole } from '../types/index';

// Types pour les offres et promotions
interface Offer {
  id: string;
  businessId: string;
  businessName: string;
  businessLogo: string;
  title: string;
  description: string;
  imageUrl: string;
  category: string;
  discount: number;
  startDate: Date;
  endDate: Date;
  isPromotion: boolean;
  isAd: boolean;
}

const OffersAndPromotionsPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [offers, setOffers] = useState<Offer[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'discount'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // États pour les interactions
  const [likedOffers, setLikedOffers] = useState<Set<string>>(new Set());
  const [likeCounts, setLikeCounts] = useState<Record<string, number>>({});
  const [shareCounts, setShareCounts] = useState<Record<string, number>>({});
  const [interactionLoading, setInteractionLoading] = useState<Record<string, boolean>>({});
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [selectedOfferForShare, setSelectedOfferForShare] = useState<Offer | null>(null);

  useEffect(() => {
    // Utiliser les données réelles camerounaises avec des UUIDs valides
    const mockOffers: Offer[] = realCameroonianOffers;

    setOffers(mockOffers);
    setLoading(false);
    
    // Charger les données d'interaction pour chaque offre
    if (currentUser) {
      loadInteractionData(mockOffers);
    }
  }, [currentUser]);

  // Charger les données d'interaction
  const loadInteractionData = async (offersList: Offer[]) => {
    if (!currentUser) return;

    try {
      const likesData: Record<string, number> = {};
      const sharesData: Record<string, number> = {};
      const likedSet = new Set<string>();

      for (const offer of offersList) {
        // Vérifier si l'utilisateur a liké cette offre
        const hasLiked = await offerInteractionService.hasUserLikedOffer(offer.id, currentUser.id);
        if (hasLiked) {
          likedSet.add(offer.id);
        }

        // Récupérer le nombre de likes et partages
        const likesCount = await offerInteractionService.getOfferLikesCount(offer.id);
        const sharesCount = await offerInteractionService.getOfferSharesCount(offer.id);

        likesData[offer.id] = likesCount;
        sharesData[offer.id] = sharesCount;
      }

      setLikedOffers(likedSet);
      setLikeCounts(likesData);
      setShareCounts(sharesData);
      
      console.log('📊 Données d\'interaction chargées:', { likesData, sharesData, likedSet });
    } catch (error) {
      console.error('Erreur lors du chargement des données d\'interaction:', error);
    }
  };

  // Filtrer les offres et promotions
  const filteredOffers = offers.filter(offer => {
    // Filtre par catégorie
    if (filterCategory !== 'all' && offer.category !== filterCategory) {
      return false;
    }

    // Filtre par type (promotion ou offre)
    if (filterType === 'promotions' && !offer.isPromotion) {
      return false;
    }
    if (filterType === 'offers' && offer.isPromotion) {
      return false;
    }
    if (filterType === 'ads' && !offer.isAd) {
      return false;
    }

    // Filtre par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        offer.title.toLowerCase().includes(query) ||
        offer.description.toLowerCase().includes(query) ||
        offer.businessName.toLowerCase().includes(query) ||
        offer.category.toLowerCase().includes(query)
      );
    }

    return true;
  });

  // Trier les offres et promotions
  const sortedOffers = [...filteredOffers].sort((a, b) => {
    if (sortBy === 'date') {
      return sortOrder === 'asc'
        ? a.startDate.getTime() - b.startDate.getTime()
        : b.startDate.getTime() - a.startDate.getTime();
    } else {
      // Tri par pourcentage de réduction
      return sortOrder === 'asc'
        ? a.discount - b.discount
        : b.discount - a.discount;
    }
  });

  // Obtenir les catégories uniques pour le filtre
  const categories = ['all', ...new Set(offers.map(offer => offer.category))];

  // Fonctions utilitaires
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    }).format(date);
  };

  const getDaysRemaining = (endDate: Date) => {
    const now = new Date();
    const diffTime = endDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const isOfferExpired = (endDate: Date) => {
    return getDaysRemaining(endDate) <= 0;
  };

  const isOfferExpiringSoon = (endDate: Date) => {
    const daysRemaining = getDaysRemaining(endDate);
    return daysRemaining > 0 && daysRemaining <= 3;
  };

  // Vérifier les permissions d'interaction selon le type de compte
  const canUserLikeOffers = () => {
    if (!currentUser) return false;
    // Seuls les utilisateurs particuliers peuvent liker les offres
    return currentUser.role === UserRole.STANDARD;
  };

  const canUserShareOffers = () => {
    if (!currentUser) return false;
    // Tous les utilisateurs connectés peuvent partager (particuliers ET entreprises)
    return true;
  };

  const getInteractionMessage = () => {
    if (!currentUser) {
      return "Connectez-vous pour interagir avec les offres";
    }
    if (currentUser.role === UserRole.BUSINESS) {
      return "Les entreprises peuvent partager mais pas liker les offres";
    }
    return null;
  };

  // Gestionnaires d'interactions
  const handleLikeOffer = async (offerId: string) => {
    if (!currentUser || interactionLoading[offerId]) return;

    // Vérifier si l'utilisateur peut liker (seulement les particuliers)
    if (!canUserLikeOffers()) {
      alert('Seuls les utilisateurs particuliers peuvent liker les offres et promotions.');
      return;
    }

    setInteractionLoading(prev => ({ ...prev, [offerId]: true }));

    try {
      const isLiked = likedOffers.has(offerId);
      
      if (isLiked) {
        // Unlike
        const success = await offerInteractionService.unlikeOffer(offerId, currentUser.id);
        if (success) {
          setLikedOffers(prev => {
            const newSet = new Set(prev);
            newSet.delete(offerId);
            return newSet;
          });
          setLikeCounts(prev => ({
            ...prev,
            [offerId]: Math.max(0, (prev[offerId] || 0) - 1)
          }));
        }
      } else {
        // Like
        const success = await offerInteractionService.likeOffer(offerId, currentUser.id);
        if (success) {
          setLikedOffers(prev => new Set(prev).add(offerId));
          setLikeCounts(prev => ({
            ...prev,
            [offerId]: (prev[offerId] || 0) + 1
          }));
        }
      }
    } catch (error) {
      console.error('Erreur lors du like/unlike:', error);
    } finally {
      setInteractionLoading(prev => ({ ...prev, [offerId]: false }));
    }
  };

  const handleShareOffer = (offerId: string, offerTitle: string) => {
    if (!currentUser) return;

    // Vérifier si l'utilisateur peut partager
    if (!canUserShareOffers()) {
      alert('Vous devez être connecté pour partager les offres.');
      return;
    }

    // Trouver l'offre sélectionnée
    const offer = offers.find(o => o.id === offerId);
    if (!offer) return;

    // Ouvrir le modal de partage
    setSelectedOfferForShare(offer);
    setShareModalOpen(true);
  };

  const handleShareComplete = async (platform: string) => {
    if (!currentUser || !selectedOfferForShare) return;

    try {
      // Enregistrer le partage dans la base de données
      await offerInteractionService.shareOffer(selectedOfferForShare.id, currentUser.id, platform as any);

      // Mettre à jour le compteur de partages localement
      setShareCounts(prev => ({
        ...prev,
        [selectedOfferForShare.id]: (prev[selectedOfferForShare.id] || 0) + 1
      }));

    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du partage:', error);
    }
  };

  const getOfferTypeIcon = (offer: Offer) => {
    if (offer.isPromotion) return <Zap className="text-orange-500" size={16} />;
    if (offer.isAd) return <Tag className="text-purple-500" size={16} />;
    return <Gift className="text-blue-500" size={16} />;
  };

  const getOfferTypeLabel = (offer: Offer) => {
    if (offer.isPromotion) return 'Promotion';
    if (offer.isAd) return 'Publicité';
    return 'Offre';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="sr-only">Chargement...</span>
          </div>
          <p className="mt-2">Chargement des offres et promotions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* En-tête moderne */}
      <div className="text-center">
        <div className="flex items-center justify-center space-x-3 mb-4">
          <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full">
            <Tag className="text-white" size={24} />
          </div>
          <h1 className="text-4xl font-bold text-gray-900">Offres et Promotions</h1>
        </div>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Découvrez les meilleures offres et promotions de nos partenaires camerounais.
          Ne manquez aucune opportunité d'économiser !
        </p>

        {/* Statistiques rapides */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8 max-w-4xl mx-auto">
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
            <div className="text-2xl font-bold text-blue-600">{offers.length}</div>
            <div className="text-sm text-blue-700">Offres totales</div>
          </div>
          <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
            <div className="text-2xl font-bold text-green-600">
              {offers.filter(offer => offer.isPromotion).length}
            </div>
            <div className="text-sm text-green-700">Promotions</div>
          </div>
          <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200">
            <div className="text-2xl font-bold text-purple-600">
              {offers.filter(offer => offer.isAd).length}
            </div>
            <div className="text-sm text-purple-700">Publicités</div>
          </div>
          <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-lg border border-orange-200">
            <div className="text-2xl font-bold text-orange-600">
              {Math.max(...offers.map(offer => offer.discount))}%
            </div>
            <div className="text-sm text-orange-700">Max réduction</div>
          </div>
        </div>
      </div>

      {/* Message d'information selon le type de compte */}
      {getInteractionMessage() && (
        <Card className={`border-l-4 ${
          !currentUser ? 'border-blue-500 bg-blue-50' : 'border-orange-500 bg-orange-50'
        }`}>
          <CardBody>
            <div className="flex items-center space-x-3">
              <div className={!currentUser ? 'text-blue-500' : 'text-orange-500'}>
                {!currentUser ? <Heart size={24} /> : <Tag size={24} />}
              </div>
              <div>
                <h3 className={`font-semibold ${
                  !currentUser ? 'text-blue-900' : 'text-orange-900'
                }`}>
                  {!currentUser ? 'Connectez-vous pour interagir' : 'Restrictions pour les entreprises'}
                </h3>
                <p className={`text-sm ${
                  !currentUser ? 'text-blue-700' : 'text-orange-700'
                }`}>
                  {getInteractionMessage()}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      )}



      {/* Affichage des offres */}
      {sortedOffers.length === 0 ? (
        <Card>
          <CardBody>
            <div className="text-center py-12">
              <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <Tag className="text-gray-400" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Aucune offre trouvée</h3>
              <p className="text-gray-600">
                Aucune offre ou promotion ne correspond à vos critères de recherche.
                Essayez de modifier vos filtres.
              </p>
            </div>
          </CardBody>
        </Card>
      ) : (
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          {sortedOffers.map((offer) => (
            <Card key={offer.id} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
              <div className="relative">
                {/* Image de l'offre */}
                <div className="relative h-64 overflow-hidden">
                  <img
                    src={offer.imageUrl}
                    alt={offer.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = `https://images.unsplash.com/photo-1556228720-195a672e8a03?w=600&h=300&fit=crop&crop=center`;
                    }}
                  />

                  {/* Badge de réduction */}
                  <div className="absolute top-4 right-4 bg-gradient-to-r from-red-500 to-pink-600 text-white px-4 py-2 rounded-full font-bold text-base shadow-lg">
                    -{offer.discount}%
                  </div>

                  {/* Badge type d'offre */}
                  <div className="absolute top-4 left-4 flex items-center space-x-2">
                    {getOfferTypeIcon(offer)}
                    <span className={`px-3 py-1.5 rounded-full text-sm font-medium ${
                      offer.isPromotion ? 'bg-orange-100 text-orange-800' :
                      offer.isAd ? 'bg-purple-100 text-purple-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {getOfferTypeLabel(offer)}
                    </span>
                  </div>
                </div>

                <CardBody>
                  {/* Informations de l'entreprise */}
                  <div className="flex items-center space-x-3 mb-4">
                    <img
                      src={offer.businessLogo}
                      alt={offer.businessName}
                      className="w-12 h-12 rounded-full object-cover border-2 border-gray-200"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?w=50&h=50&fit=crop&crop=center`;
                      }}
                    />
                    <div>
                      <h4 className="font-semibold text-lg text-gray-900">{offer.businessName}</h4>
                      <span className="text-base text-gray-500">{offer.category}</span>
                    </div>
                  </div>

                  {/* Titre et description */}
                  <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                    {offer.title}
                  </h3>
                  <p className="text-gray-600 text-base mb-6 line-clamp-3">
                    {offer.description}
                  </p>

                  {/* Détails de l'offre */}
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-3 text-base text-gray-500">
                      <Calendar size={16} />
                      <span>Du {formatDate(offer.startDate)} au {formatDate(offer.endDate)}</span>
                    </div>
                    <div className="flex items-center space-x-3 text-base">
                      <Clock size={16} className="text-gray-400" />
                      <span className={`font-medium ${
                        getDaysRemaining(offer.endDate) <= 0 ? 'text-red-600' :
                        getDaysRemaining(offer.endDate) <= 3 ? 'text-orange-600' :
                        'text-green-600'
                      }`}>
                        {getDaysRemaining(offer.endDate) <= 0 ? 'Expiré' :
                         getDaysRemaining(offer.endDate) === 1 ? 'Expire demain' :
                         `${getDaysRemaining(offer.endDate)} jours restants`}
                      </span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Button
                        variant="outline"
                        size="sm"
                        leftIcon={<Heart size={16} />}
                        className={`transition-colors ${
                          likedOffers.has(offer.id)
                            ? 'text-red-500 border-red-500 bg-red-50'
                            : canUserLikeOffers()
                              ? 'text-gray-600 hover:text-red-500'
                              : 'text-gray-400 cursor-not-allowed'
                        }`}
                        onClick={() => handleLikeOffer(offer.id)}
                        disabled={interactionLoading[offer.id] || !canUserLikeOffers()}
                        title={!canUserLikeOffers() ? 'Seuls les utilisateurs particuliers peuvent liker' : ''}
                      >
                        J'aime ({likeCounts[offer.id] || 0})
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        leftIcon={<Share2 size={16} />}
                        className={`transition-colors ${
                          canUserShareOffers()
                            ? 'text-gray-600 hover:text-blue-500'
                            : 'text-gray-400 cursor-not-allowed'
                        }`}
                        onClick={() => handleShareOffer(offer.id, offer.title)}
                        disabled={!canUserShareOffers()}
                        title={!canUserShareOffers() ? 'Connectez-vous pour partager' : ''}
                      >
                        Partager ({shareCounts[offer.id] || 0})
                      </Button>
                    </div>
                    <Button
                      variant="primary"
                      size="sm"
                      rightIcon={<ExternalLink size={16} />}
                      className="font-medium"
                      onClick={() => window.open(`/offers/${offer.id}`, '_blank')}
                    >
                      Voir l'offre
                    </Button>
                  </div>


                </CardBody>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Modal de partage */}
      {selectedOfferForShare && (
        <OfferShareModal
          offer={selectedOfferForShare}
          isOpen={shareModalOpen}
          onClose={() => {
            setShareModalOpen(false);
            setSelectedOfferForShare(null);
          }}
          onShareComplete={handleShareComplete}
        />
      )}
    </div>
  );
};

export default OffersAndPromotionsPage;
