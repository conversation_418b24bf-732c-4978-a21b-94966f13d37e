import React, { useState, useEffect } from 'react';
import {
  Receipt, Download, Eye, Calendar, CreditCard, CheckCircle,
  XCircle, Clock, Search, Filter, RefreshCw, FileText
} from 'lucide-react';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import Button from '../components/ui/Button';
import PaymentReceipt from '../components/payment/PaymentReceipt';
import { useAuth } from '../context/AuthContext';
import { PaymentService, PaymentReceipt as PaymentReceiptType } from '../services/paymentService';

interface PaymentHistoryItem {
  id: string;
  invoiceNumber: string;
  planName: string;
  amount: number;
  currency: string;
  status: 'paid' | 'refunded' | 'partially_refunded';
  paidAt: string;
  paymentMethod: {
    provider: string;
    type: string;
    last4?: string;
  };
  receiptId: string;
}

const PaymentHistoryPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [payments, setPayments] = useState<PaymentHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'paid' | 'refunded'>('all');
  const [selectedReceipt, setSelectedReceipt] = useState<PaymentReceiptType | null>(null);
  const [showReceiptModal, setShowReceiptModal] = useState(false);

  useEffect(() => {
    loadPaymentHistory();
  }, []);

  const loadPaymentHistory = async () => {
    setLoading(true);
    try {
      if (!currentUser?.id) return;

      // Simuler des données d'historique de paiement
      const mockPayments: PaymentHistoryItem[] = [
        {
          id: 'pay_1',
          invoiceNumber: 'INV-2024-001234',
          planName: 'Professional',
          amount: 35000,
          currency: 'XOF',
          status: 'paid',
          paidAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          paymentMethod: {
            provider: 'Visa',
            type: 'card',
            last4: '4242'
          },
          receiptId: 'receipt_1'
        },
        {
          id: 'pay_2',
          invoiceNumber: 'INV-2024-001123',
          planName: 'Starter',
          amount: 15000,
          currency: 'XOF',
          status: 'paid',
          paidAt: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000).toISOString(),
          paymentMethod: {
            provider: 'Orange Money',
            type: 'mobile_money'
          },
          receiptId: 'receipt_2'
        },
        {
          id: 'pay_3',
          invoiceNumber: 'INV-2024-001012',
          planName: 'Professional',
          amount: 35000,
          currency: 'XOF',
          status: 'refunded',
          paidAt: new Date(Date.now() - 65 * 24 * 60 * 60 * 1000).toISOString(),
          paymentMethod: {
            provider: 'MTN Money',
            type: 'mobile_money'
          },
          receiptId: 'receipt_3'
        }
      ];

      setPayments(mockPayments);
    } catch (error) {
      console.error('Erreur lors du chargement de l\'historique:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewReceipt = async (payment: PaymentHistoryItem) => {
    try {
      // Simuler la récupération du reçu complet
      const mockReceipt: PaymentReceiptType = {
        id: payment.receiptId,
        paymentIntentId: payment.id,
        businessId: currentUser?.id || '',
        planId: 'professional',
        planName: payment.planName,
        amount: payment.amount,
        currency: payment.currency,
        paymentMethod: {
          id: 'pm_1',
          type: payment.paymentMethod.type as any,
          provider: payment.paymentMethod.provider,
          last4: payment.paymentMethod.last4,
          isDefault: true
        },
        transactionId: `txn_${payment.id}`,
        status: payment.status,
        paidAt: payment.paidAt,
        businessInfo: {
          name: currentUser?.role === 'business' ? (currentUser as any).businessName : 'Entreprise',
          email: currentUser?.email || '',
          address: 'Abidjan, Côte d\'Ivoire'
        },
        invoiceNumber: payment.invoiceNumber,
        receiptUrl: `https://customeroom.com/receipts/${payment.id}.pdf`
      };

      setSelectedReceipt(mockReceipt);
      setShowReceiptModal(true);
    } catch (error) {
      console.error('Erreur lors de la récupération du reçu:', error);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price) + ' F CFA';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle size={12} className="mr-1" />
            Payé
          </span>
        );
      case 'refunded':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle size={12} className="mr-1" />
            Remboursé
          </span>
        );
      case 'partially_refunded':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock size={12} className="mr-1" />
            Partiellement remboursé
          </span>
        );
      default:
        return null;
    }
  };

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.planName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || payment.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <RefreshCw className="mx-auto text-blue-600 mb-4 animate-spin" size={48} />
            <p className="text-gray-600">Chargement de l'historique des paiements...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Historique des paiements</h1>
          <p className="text-gray-600 mt-2">Consultez tous vos paiements et téléchargez vos reçus</p>
        </div>
        <Button onClick={loadPaymentHistory} variant="outline">
          <RefreshCw size={16} className="mr-2" />
          Actualiser
        </Button>
      </div>

      {/* Filtres */}
      <Card>
        <CardBody>
          <div className="flex flex-wrap items-center gap-4">
            {/* Recherche */}
            <div className="flex items-center space-x-2 flex-1 max-w-md">
              <Search className="text-gray-400" size={16} />
              <input
                type="text"
                placeholder="Rechercher par numéro de facture ou plan..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex-1 border border-gray-300 rounded px-3 py-2 text-sm"
              />
            </div>

            {/* Filtre par statut */}
            <div className="flex items-center space-x-2">
              <Filter className="text-gray-400" size={16} />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="border border-gray-300 rounded px-3 py-2 text-sm"
              >
                <option value="all">Tous les statuts</option>
                <option value="paid">Payé</option>
                <option value="refunded">Remboursé</option>
              </select>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Liste des paiements */}
      {filteredPayments.length === 0 ? (
        <Card>
          <CardBody className="text-center py-12">
            <Receipt className="mx-auto text-gray-400 mb-4" size={48} />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun paiement trouvé</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== 'all' 
                ? 'Aucun paiement ne correspond à vos critères de recherche.'
                : 'Vous n\'avez effectué aucun paiement pour le moment.'}
            </p>
          </CardBody>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredPayments.map((payment) => (
            <Card key={payment.id} className="hover:shadow-md transition-shadow">
              <CardBody>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 rounded-full bg-blue-100">
                      <Receipt className="text-blue-600" size={20} />
                    </div>
                    <div>
                      <div className="flex items-center space-x-3 mb-1">
                        <h3 className="font-medium text-gray-900">{payment.invoiceNumber}</h3>
                        {getStatusBadge(payment.status)}
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span className="flex items-center">
                          <FileText size={14} className="mr-1" />
                          {payment.planName}
                        </span>
                        <span className="flex items-center">
                          <Calendar size={14} className="mr-1" />
                          {formatDate(payment.paidAt)}
                        </span>
                        <span className="flex items-center">
                          <CreditCard size={14} className="mr-1" />
                          {payment.paymentMethod.provider}
                          {payment.paymentMethod.last4 && ` •••• ${payment.paymentMethod.last4}`}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="text-lg font-semibold text-gray-900">
                        {formatPrice(payment.amount)}
                      </div>
                      <div className="text-sm text-gray-600">{payment.currency}</div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewReceipt(payment)}
                      >
                        <Eye size={14} className="mr-1" />
                        Voir
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewReceipt(payment)}
                      >
                        <Download size={14} className="mr-1" />
                        Télécharger
                      </Button>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      )}

      {/* Résumé */}
      {filteredPayments.length > 0 && (
        <Card className="bg-gray-50">
          <CardBody>
            <div className="text-center text-sm text-gray-600">
              {filteredPayments.length} paiement{filteredPayments.length > 1 ? 's' : ''} affiché{filteredPayments.length > 1 ? 's' : ''}
              {searchTerm && ` • Recherche: "${searchTerm}"`}
              {statusFilter !== 'all' && ` • Statut: ${statusFilter}`}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Modal de reçu */}
      {showReceiptModal && selectedReceipt && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <PaymentReceipt
              receipt={selectedReceipt}
              onClose={() => {
                setShowReceiptModal(false);
                setSelectedReceipt(null);
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentHistoryPage;
