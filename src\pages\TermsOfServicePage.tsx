import React from 'react';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import { FileText, Users, Shield, AlertCircle, Scale, Ban } from 'lucide-react';

const TermsOfServicePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* En-tête */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4 flex items-center justify-center">
            <FileText className="mr-3 text-blue-600" size={40} />
            Conditions d'Utilisation
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Dernière mise à jour : Décembre 2024
          </p>
          <p className="text-sm text-gray-500 mt-2">
            En utilisant Customeroom, vous acceptez ces conditions d'utilisation.
          </p>
        </div>

        {/* Acceptation des Conditions */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900 flex items-center">
              <Scale className="mr-3 text-blue-600" size={24} />
              Acceptation des Conditions
            </h2>
          </CardHeader>
          <CardBody>
            <p className="text-gray-700 leading-relaxed">
              En accédant et en utilisant la plateforme Customeroom, vous acceptez d'être lié par ces 
              conditions d'utilisation. Si vous n'acceptez pas ces conditions, veuillez ne pas utiliser 
              nos services. Ces conditions peuvent être mises à jour périodiquement, et votre utilisation 
              continue constitue votre acceptation des modifications.
            </p>
          </CardBody>
        </Card>

        {/* Description du Service */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900 flex items-center">
              <Users className="mr-3 text-green-600" size={24} />
              Description du Service
            </h2>
          </CardHeader>
          <CardBody>
            <p className="text-gray-700 mb-4">
              Customeroom est une plateforme numérique qui permet :
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Pour les Utilisateurs</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Publier des avis sur les entreprises</li>
                  <li>Partager des expériences client</li>
                  <li>Découvrir de nouvelles entreprises</li>
                  <li>Suivre d'autres utilisateurs</li>
                  <li>Recevoir des recommandations</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Pour les Entreprises</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Gérer leur réputation en ligne</li>
                  <li>Répondre aux avis clients</li>
                  <li>Analyser les retours clients</li>
                  <li>Promouvoir leurs services</li>
                  <li>Accéder à des outils d'analyse</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Comptes Utilisateurs */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900">Comptes Utilisateurs</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Création de Compte</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Vous devez fournir des informations exactes et complètes</li>
                  <li>Vous êtes responsable de la sécurité de votre mot de passe</li>
                  <li>Un seul compte par personne ou entreprise est autorisé</li>
                  <li>Vous devez avoir au moins 16 ans pour créer un compte</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Responsabilités</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Maintenir la confidentialité de vos identifiants</li>
                  <li>Notifier immédiatement tout usage non autorisé</li>
                  <li>Mettre à jour vos informations si nécessaire</li>
                  <li>Respecter les droits des autres utilisateurs</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Règles de Conduite */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900 flex items-center">
              <Shield className="mr-3 text-purple-600" size={24} />
              Règles de Conduite
            </h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Contenu Autorisé</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Avis honnêtes basés sur des expériences réelles</li>
                  <li>Commentaires constructifs et respectueux</li>
                  <li>Informations exactes sur les entreprises</li>
                  <li>Contenu original et non plagié</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 text-red-600">Contenu Interdit</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Contenu diffamatoire, injurieux ou discriminatoire</li>
                  <li>Faux avis ou manipulation de la réputation</li>
                  <li>Spam, publicité non autorisée</li>
                  <li>Contenu violent, pornographique ou illégal</li>
                  <li>Violation de la propriété intellectuelle</li>
                  <li>Harcèlement ou intimidation</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Avis et Commentaires */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900">Avis et Commentaires</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Authenticité</h3>
                <p className="text-gray-700">
                  Tous les avis doivent être basés sur des expériences réelles. Les faux avis, 
                  les avis achetés ou les tentatives de manipulation sont strictement interdits 
                  et peuvent entraîner la suspension du compte.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Modération</h3>
                <p className="text-gray-700">
                  Customeroom se réserve le droit de modérer, modifier ou supprimer tout contenu 
                  qui ne respecte pas ces conditions. Nous utilisons des outils automatisés et 
                  une modération humaine pour maintenir la qualité de la plateforme.
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Abonnements et Paiements */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900">Abonnements et Paiements</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Plans d'Abonnement</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Les prix sont affichés en Francs CFA (XOF)</li>
                  <li>Les abonnements se renouvellent automatiquement sauf annulation</li>
                  <li>L'essai gratuit est limité à une utilisation par entreprise</li>
                  <li>Les fonctionnalités peuvent varier selon le plan choisi</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Remboursements</h3>
                <p className="text-gray-700">
                  Les remboursements sont traités au cas par cas. Contactez notre support 
                  pour toute demande de remboursement dans les 7 jours suivant le paiement.
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Propriété Intellectuelle */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900">Propriété Intellectuelle</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Contenu de Customeroom</h3>
                <p className="text-gray-700">
                  Tous les éléments de la plateforme (design, code, logos, textes) sont protégés 
                  par les droits d'auteur et appartiennent à Customeroom ou à ses partenaires.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Votre Contenu</h3>
                <p className="text-gray-700">
                  Vous conservez la propriété de votre contenu, mais vous accordez à Customeroom 
                  une licence pour l'utiliser, l'afficher et le distribuer sur la plateforme.
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Sanctions */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900 flex items-center">
              <Ban className="mr-3 text-red-600" size={24} />
              Sanctions et Résiliation
            </h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Violations</h3>
                <p className="text-gray-700 mb-3">
                  En cas de violation de ces conditions, nous pouvons :
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Émettre un avertissement</li>
                  <li>Supprimer le contenu en violation</li>
                  <li>Suspendre temporairement le compte</li>
                  <li>Résilier définitivement le compte</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Résiliation</h3>
                <p className="text-gray-700">
                  Vous pouvez résilier votre compte à tout moment. Customeroom peut également 
                  résilier votre accès en cas de violation grave ou répétée des conditions.
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Contact */}
        <Card>
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900 flex items-center">
              <AlertCircle className="mr-3 text-orange-600" size={24} />
              Contact et Support
            </h2>
          </CardHeader>
          <CardBody>
            <p className="text-gray-700 mb-4">
              Pour toute question concernant ces conditions d'utilisation :
            </p>
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-gray-700">
                <strong>Email :</strong> <EMAIL><br />
                <strong>Support :</strong> <EMAIL><br />
                <strong>Téléphone :</strong> +225 01 23 45 67
              </p>
            </div>
            <p className="text-sm text-gray-500 mt-4">
              Ces conditions sont régies par le droit ivoirien. Tout litige sera soumis 
              aux tribunaux compétents d'Abidjan, Côte d'Ivoire.
            </p>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default TermsOfServicePage;
