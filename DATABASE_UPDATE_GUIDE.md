# Guide de mise à jour de la base de données - Profils d'entreprise

## 📋 Résumé des modifications

Cette mise à jour ajoute des champs complets pour les profils d'entreprise, permettant une gestion complète des informations business.

## 🗄️ Nouvelles colonnes ajoutées à `business_profiles`

### Informations de contact
- `business_address` (TEXT) - Adresse physique de l'entreprise
- `business_phone` (VARCHAR(20)) - Numéro de téléphone professionnel
- `business_email` (VARCHAR(255)) - Email professionnel
- `business_website` (TEXT) - Site web de l'entreprise

### Informations légales et administratives
- `business_category` (VARCHAR(100)) - Catégorie d'activité
- `business_license` (VARCHAR(100)) - Numéro de licence/RCCM
- `founded_year` (INTEGER) - Ann<PERSON> de création
- `employee_count` (INTEGER) - Nombre d'employés

### Statistiques et métriques
- `average_rating` (DECIMAL(3,2)) - Note moy<PERSON> (0-5)
- `total_reviews` (INTEGER) - Nombre total d'avis
- `followers_count` (INTEGER) - Nombre de followers
- `following_count` (INTEGER) - Nombre d'abonnements

### Métadonnées
- `cover_photo_url` (TEXT) - Photo de couverture
- `created_at` (TIMESTAMPTZ) - Date de création
- `updated_at` (TIMESTAMPTZ) - Date de dernière modification

## 🔒 Contraintes ajoutées

- **Email** : Format valide requis
- **Téléphone** : Format international accepté
- **Site web** : URL valide (http/https)
- **Année de création** : Entre 1800 et année actuelle
- **Nombre d'employés** : Valeur positive
- **Note moyenne** : Entre 0.0 et 5.0
- **Compteurs** : Valeurs positives

## 📊 Index créés

- `business_profiles_category_idx` - Pour les recherches par catégorie
- `business_profiles_founded_year_idx` - Pour les filtres par année
- `business_profiles_rating_idx` - Pour les tris par note
- `business_profiles_created_at_idx` - Pour les tris chronologiques

## 🚀 Comment appliquer la migration

### Option 1 : Via Supabase CLI (Recommandé)

```bash
# 1. Assurez-vous d'être dans le répertoire du projet
cd /path/to/your/project

# 2. Appliquez la migration
supabase db push

# 3. Vérifiez que la migration s'est bien appliquée
supabase db diff
```

### Option 2 : Via l'interface Supabase

1. Connectez-vous à votre dashboard Supabase
2. Allez dans l'onglet "SQL Editor"
3. Copiez le contenu du fichier `supabase/migrations/20250527165000_enhance_business_profiles.sql`
4. Exécutez le script SQL

### Option 3 : Via psql (Pour les experts)

```bash
# Connectez-vous à votre base de données
psql "postgresql://[username]:[password]@[host]:[port]/[database]"

# Exécutez le fichier de migration
\i supabase/migrations/20250527165000_enhance_business_profiles.sql
```

## 🧪 Vérification de la migration

Après avoir appliqué la migration, vérifiez que tout fonctionne :

```sql
-- Vérifier la structure de la table
\d business_profiles

-- Vérifier les contraintes
SELECT conname, contype, pg_get_constraintdef(oid) 
FROM pg_constraint 
WHERE conrelid = 'business_profiles'::regclass;

-- Vérifier les index
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'business_profiles';
```

## 🔄 Mise à jour du code

### Services mis à jour
- `src/services/businessProfileService.ts` - Nouveau service pour gérer les profils business
- `src/types/index.ts` - Interface IBusinessUser mise à jour

### Composants mis à jour
- `src/components/business/BusinessProfileManager.tsx` - Gestion complète du profil
- `src/pages/BusinessProfilePage.tsx` - Intégration du service

### Nouvelles fonctionnalités
- ✅ Formulaire complet d'édition du profil business
- ✅ Validation des données côté client et serveur
- ✅ Sauvegarde en base de données
- ✅ Gestion des erreurs
- ✅ Interface utilisateur moderne

## 🎯 Fonctionnalités disponibles

### Pour les entreprises
1. **Informations générales** : Nom, catégorie, description
2. **Coordonnées** : Adresse, téléphone, email, site web
3. **Informations légales** : Licence, année de création, nombre d'employés
4. **Statistiques** : Ventes, avis, followers

### Pour les développeurs
1. **Service complet** : CRUD operations pour les profils business
2. **Validation robuste** : Contraintes de base de données et validation TypeScript
3. **Gestion d'erreurs** : Messages d'erreur explicites
4. **Performance** : Index optimisés pour les requêtes fréquentes

## 🚨 Points d'attention

1. **Sauvegarde** : Effectuez une sauvegarde avant d'appliquer la migration
2. **Données existantes** : Les profils business existants conserveront leurs données actuelles
3. **Nouveaux champs** : Les nouveaux champs seront NULL par défaut (optionnels)
4. **Performance** : Les nouveaux index amélioreront les performances des requêtes

## 📞 Support

En cas de problème lors de la migration :
1. Vérifiez les logs de Supabase
2. Consultez la documentation Supabase
3. Vérifiez que vous avez les permissions nécessaires
4. Contactez l'équipe de développement si nécessaire

---

**Date de création** : 27 janvier 2025  
**Version** : 1.0  
**Auteur** : Équipe de développement customeroom
