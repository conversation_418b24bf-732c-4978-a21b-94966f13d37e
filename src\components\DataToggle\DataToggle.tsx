import React, { useState, useEffect } from 'react';
import { Database, Zap, ToggleLeft, ToggleRight, AlertCircle, CheckCircle, Loader } from 'lucide-react';
import './DataToggle.css';

interface DataToggleProps {
  onToggle: (useRealData: boolean) => void;
  initialValue?: boolean;
  hasRealData?: boolean;
  loading?: boolean;
}

const DataToggle: React.FC<DataToggleProps> = ({ 
  onToggle, 
  initialValue = false, 
  hasRealData = false,
  loading = false 
}) => {
  const [useRealData, setUseRealData] = useState(initialValue);
  const [showInfo, setShowInfo] = useState(false);

  useEffect(() => {
    setUseRealData(initialValue);
  }, [initialValue]);

  const handleToggle = () => {
    const newValue = !useRealData;
    setUseRealData(newValue);
    onToggle(newValue);
  };

  return (
    <div className="data-toggle-container">
      <div className="data-toggle-wrapper">
        <div className="data-toggle-info">
          <div className="data-source-indicator">
            {loading ? (
              <Loader size={16} className="loading-icon" />
            ) : useRealData ? (
              <Database size={16} className="real-data-icon" />
            ) : (
              <Zap size={16} className="mock-data-icon" />
            )}
            <span className="data-source-label">
              {loading ? 'Chargement...' : useRealData ? 'Vraies données' : 'Données de démo'}
            </span>
          </div>
          
          <button 
            className="info-button"
            onClick={() => setShowInfo(!showInfo)}
            title="Informations sur les données"
          >
            <AlertCircle size={14} />
          </button>
        </div>

        <div className="toggle-switch-container">
          <span className="toggle-label demo">Démo</span>
          <button 
            className={`toggle-switch ${useRealData ? 'active' : ''} ${!hasRealData ? 'disabled' : ''}`}
            onClick={handleToggle}
            disabled={!hasRealData || loading}
            title={!hasRealData ? 'Aucune vraie donnée disponible' : 'Basculer entre données de démo et vraies données'}
          >
            <div className="toggle-track">
              <div className="toggle-thumb">
                {useRealData ? (
                  <Database size={12} />
                ) : (
                  <Zap size={12} />
                )}
              </div>
            </div>
          </button>
          <span className="toggle-label real">Réel</span>
        </div>

        <div className="data-status">
          {hasRealData ? (
            <div className="status-indicator success">
              <CheckCircle size={14} />
              <span>Connecté</span>
            </div>
          ) : (
            <div className="status-indicator warning">
              <AlertCircle size={14} />
              <span>Démo uniquement</span>
            </div>
          )}
        </div>
      </div>

      {showInfo && (
        <div className="data-info-panel">
          <div className="info-header">
            <h4>Sources de données</h4>
            <button 
              className="close-info"
              onClick={() => setShowInfo(false)}
            >
              ×
            </button>
          </div>
          
          <div className="info-content">
            <div className="info-section">
              <div className="info-icon demo">
                <Zap size={16} />
              </div>
              <div className="info-text">
                <h5>Données de démo</h5>
                <p>Données fictives pour tester l'interface. Parfait pour découvrir les fonctionnalités sans affecter vos vraies données.</p>
                <ul>
                  <li>✅ Toujours disponibles</li>
                  <li>✅ Données cohérentes</li>
                  <li>✅ Pas de limite</li>
                  <li>⚠️ Pas de persistance</li>
                </ul>
              </div>
            </div>

            <div className="info-section">
              <div className="info-icon real">
                <Database size={16} />
              </div>
              <div className="info-text">
                <h5>Vraies données</h5>
                <p>Vos données réelles depuis la base de données. Synchronisation en temps réel avec vos ventes, produits et classements.</p>
                <ul>
                  <li>✅ Données authentiques</li>
                  <li>✅ Synchronisation temps réel</li>
                  <li>✅ Persistance garantie</li>
                  <li>{hasRealData ? '✅' : '❌'} {hasRealData ? 'Disponible' : 'Non disponible'}</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="info-footer">
            <div className="recommendation">
              <strong>Recommandation :</strong> Utilisez les données de démo pour explorer, puis basculez vers les vraies données pour votre activité.
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataToggle;
