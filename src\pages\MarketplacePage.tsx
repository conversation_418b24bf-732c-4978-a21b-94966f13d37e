import React, { useState } from 'react';
import { Search } from 'lucide-react';
import Button from '../components/ui/Button';
import { useAuth } from '../context/AuthContext';
import ProductCard from '../components/marketplace/ProductCard';
import PurchaseConfirmationModal from '../components/marketplace/PurchaseConfirmationModal';
import ProductPreviewModal from '../components/marketplace/ProductPreviewModal';
import OrderTrackingWidget from '../components/orders/OrderTrackingWidget';
import { IProduct } from '../types';
import { REAL_PRODUCTS } from '../data/realProducts';

const MarketplacePage: React.FC = () => {
  // Utiliser useAuth pour une future intégration avec l'authentification
  const { } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [showNegotiationModal, setShowNegotiationModal] = useState(false);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [showTrackingWidget, setShowTrackingWidget] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<IProduct | null>(null);
  const [purchaseQuantity, setPurchaseQuantity] = useState(1);
  const [currentOrderId, setCurrentOrderId] = useState<string | null>(null);

  // Mock categories
  const categories = [
    'Tous', 'Électronique', 'Mode', 'Maison', 'Sport', 'Beauté', 'Auto', 'Services'
  ];

  // Utiliser les données réelles des produits
  const products: IProduct[] = REAL_PRODUCTS;

  const handleNegotiation = (product: IProduct) => {
    setSelectedProduct(product);
    setShowNegotiationModal(true);
  };

  const handleBuyClick = (product: IProduct) => {
    setSelectedProduct(product);
    setPurchaseQuantity(1);
    setShowPurchaseModal(true);
  };

  const handlePreviewClick = (product: IProduct) => {
    setSelectedProduct(product);
    setShowPreviewModal(true);
  };

  const handlePurchaseComplete = (orderId: string) => {
    setCurrentOrderId(orderId);
    setShowPurchaseModal(false);
    setShowTrackingWidget(true);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-10">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Marketplace
            <span className="text-blue-600">.</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Découvrez des produits et services de qualité, négociez les prix et trouvez les meilleures offres
          </p>
          <div className="flex items-center justify-center mt-6 space-x-6">
            <div className="flex items-center text-sm text-gray-500">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span>Marketplace gratuit</span>
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
              <span>0% de commission</span>
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
              <span>Négociation activée</span>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-10 bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Search Bar */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 focus:bg-white transition-colors text-gray-900 placeholder-gray-500"
                placeholder="Rechercher un produit, service ou marque..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* Category Filter */}
          <div className="flex-shrink-0">
            <select
              className="w-full lg:w-52 px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 focus:bg-white transition-colors text-gray-900"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              {categories.map((category) => (
                <option key={category} value={category.toLowerCase()}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          {/* Price Range Filter */}
          <div className="flex-shrink-0">
            <div className="flex items-center gap-3">
              <input
                type="number"
                className="w-28 px-3 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 focus:bg-white transition-colors text-gray-900 placeholder-gray-500"
                placeholder="Min"
                value={priceRange[0]}
                onChange={(e) => setPriceRange([Number(e.target.value), priceRange[1]])}
              />
              <span className="text-gray-400 font-medium">à</span>
              <input
                type="number"
                className="w-28 px-3 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 focus:bg-white transition-colors text-gray-900 placeholder-gray-500"
                placeholder="Max"
                value={priceRange[1]}
                onChange={(e) => setPriceRange([priceRange[0], Number(e.target.value)])}
              />
              <span className="text-sm text-gray-500 whitespace-nowrap">F CFA</span>
            </div>
          </div>
        </div>
      </div>

      {/* Product Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
        {products.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            onNegotiateClick={handleNegotiation}
            onBuyClick={handleBuyClick}
            onPreviewClick={handlePreviewClick}
          />
        ))}
      </div>

      {/* Negotiation Modal */}
      {showNegotiationModal && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
            <h3 className="text-xl font-semibold mb-4">
              Négocier le prix - {selectedProduct.name}
            </h3>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Votre offre
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="number"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Entrez votre offre"
                />
                <span className="text-gray-500">F CFA</span>
              </div>
              <p className="mt-1 text-sm text-gray-500">
                Prix initial: {selectedProduct.price.toLocaleString()} F CFA
              </p>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Message (optionnel)
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder="Expliquez pourquoi vous proposez ce prix..."
              />
            </div>

            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowNegotiationModal(false)}
              >
                Annuler
              </Button>
              <Button variant="primary">
                Envoyer l'offre
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Purchase Confirmation Modal */}
      <PurchaseConfirmationModal
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
        product={selectedProduct}
        quantity={purchaseQuantity}
        onPurchaseComplete={handlePurchaseComplete}
      />

      {/* Product Preview Modal */}
      <ProductPreviewModal
        isOpen={showPreviewModal}
        onClose={() => setShowPreviewModal(false)}
        product={selectedProduct}
        onBuyClick={handleBuyClick}
        onNegotiateClick={handleNegotiation}
      />

      {/* Order Tracking Widget */}
      {showTrackingWidget && currentOrderId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-4 border-b border-gray-100 flex items-center justify-between">
              <h2 className="text-lg font-semibold">Suivi de votre commande</h2>
              <button
                onClick={() => setShowTrackingWidget(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                ✕
              </button>
            </div>
            <div className="p-4">
              <OrderTrackingWidget orderId={currentOrderId} />
              <div className="mt-4 pt-4 border-t border-gray-100">
                <Button
                  variant="primary"
                  fullWidth
                  onClick={() => {
                    setShowTrackingWidget(false);
                    // Rediriger vers la page des commandes
                    window.location.href = '/orders';
                  }}
                >
                  Voir toutes mes commandes
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MarketplacePage;
