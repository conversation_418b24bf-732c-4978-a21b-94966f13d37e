/* AI Chat Component - Styles Ultra-Avancés */
.ai-chat-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 400px;
  height: 600px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  animation: slideInUp 0.3s ease;
  overflow: hidden;
}

.ai-chat-container.minimized {
  height: 60px;
  animation: slideDown 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    height: 600px;
  }
  to {
    height: 60px;
  }
}

/* Header */
.ai-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px 16px 0 0;
}

.chat-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ai-avatar {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.chat-title h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: white;
}

.chat-context {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-top: 2px;
  display: block;
}

.chat-header-actions {
  display: flex;
  gap: 8px;
}

.chat-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.chat-action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.chat-action-btn.close:hover {
  background: #ef4444;
}

/* Messages */
.ai-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: #f8fafc;
}

.message {
  display: flex;
  gap: 12px;
  animation: messageSlide 0.3s ease;
}

@keyframes messageSlide {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 0.875rem;
}

.message.ai .message-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message.user .message-avatar {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.message-content {
  flex: 1;
  max-width: 80%;
}

.message-text {
  padding: 12px 16px;
  border-radius: 16px;
  font-size: 0.875rem;
  line-height: 1.5;
  white-space: pre-wrap;
}

.message.ai .message-text {
  background: white;
  color: #374151;
  border-bottom-left-radius: 4px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message.user .message-text {
  background: #3b82f6;
  color: white;
  border-bottom-right-radius: 4px;
}

.message-time {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 4px;
  text-align: right;
}

.message.ai .message-time {
  text-align: left;
}

/* Suggestions */
.message-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.suggestion-btn {
  padding: 8px 12px;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  font-size: 0.8rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-btn:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  transform: translateY(-1px);
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px 16px;
  background: white;
  border-radius: 16px;
  border-bottom-left-radius: 4px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background: #9ca3af;
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* Input */
.ai-chat-input {
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  background: white;
  border-radius: 0 0 16px 16px;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.chat-textarea {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  font-size: 0.875rem;
  resize: none;
  outline: none;
  transition: all 0.2s ease;
  font-family: inherit;
  max-height: 100px;
  min-height: 40px;
}

.chat-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.send-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: scale(1.05);
}

.send-btn:disabled {
  background: #d1d5db;
  cursor: not-allowed;
  transform: none;
}

/* Scrollbar personnalisé */
.ai-chat-messages::-webkit-scrollbar {
  width: 6px;
}

.ai-chat-messages::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.ai-chat-messages::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.ai-chat-messages::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Responsive Mobile */
@media (max-width: 768px) {
  .ai-chat-container {
    bottom: 10px;
    right: 10px;
    left: 10px;
    width: auto;
    height: 70vh;
    max-height: 500px;
  }
  
  .ai-chat-container.minimized {
    height: 60px;
    left: auto;
    width: 200px;
  }
  
  .ai-chat-messages {
    padding: 16px;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .chat-textarea {
    font-size: 16px; /* Évite le zoom sur iOS */
  }
}

/* Animations d'état */
.ai-chat-container.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  }
  50% {
    box-shadow: 0 20px 60px rgba(59, 130, 246, 0.3);
  }
}

/* Thème sombre (optionnel) */
.ai-chat-container.dark {
  background: #1f2937;
  border-color: #374151;
}

.ai-chat-container.dark .ai-chat-messages {
  background: #111827;
}

.ai-chat-container.dark .message.ai .message-text {
  background: #374151;
  color: #f9fafb;
  border-color: #4b5563;
}

.ai-chat-container.dark .ai-chat-input {
  background: #1f2937;
  border-color: #374151;
}

.ai-chat-container.dark .chat-textarea {
  background: #374151;
  color: #f9fafb;
  border-color: #4b5563;
}

.ai-chat-container.dark .suggestion-btn {
  background: #374151;
  color: #f9fafb;
  border-color: #4b5563;
}

/* États spéciaux */
.ai-chat-container.thinking .ai-avatar {
  animation: thinking 1s infinite;
}

@keyframes thinking {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.ai-chat-container.success .ai-chat-header {
  background: linear-gradient(135deg, #10b981, #059669);
}

.ai-chat-container.warning .ai-chat-header {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.ai-chat-container.error .ai-chat-header {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}
