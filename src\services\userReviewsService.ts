import { supabase } from '../lib/supabase';

// Interface pour les avis utilisateur
export interface UserReview {
  id: string;
  productId: string;
  productName: string;
  businessName: string;
  productImageUrl: string;
  rating: number;
  comment: string;
  date: string;
  likes: number;
  dislikes: number;
  userHasLiked?: boolean;
  userHasDisliked?: boolean;
  replies?: ReviewReply[];
  type: string;
  category: string;
  images: string[];
  tags: string[];
}

export interface ReviewReply {
  id: string;
  businessName: string;
  comment: string;
  date: string;
}

export class UserReviewsService {
  /**
   * Récupère tous les avis d'un utilisateur
   */
  static async getUserReviews(userId: string): Promise<UserReview[]> {
    try {
      const { data: posts, error } = await supabase
        .from('posts_with_author_details')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erreur lors de la récupération des avis utilisateur:', error);
        return [];
      }

      if (!posts || posts.length === 0) {
        return [];
      }

      // Transformer les posts en avis utilisateur
      const reviews: UserReview[] = posts.map(post => ({
        id: post.id,
        productId: post.id, // Utiliser l'ID du post comme productId
        productName: post.product_name || 'Produit non spécifié',
        businessName: post.business_name || 'Entreprise non spécifiée',
        productImageUrl: this.getFirstImageUrl(post.images) || 'https://via.placeholder.com/100x100?text=Produit',
        rating: post.rating || 0,
        comment: post.description || '',
        date: post.created_at,
        likes: Array.isArray(post.likes) ? post.likes.length : 0,
        dislikes: 0, // Les dislikes ne sont pas implémentés dans le système actuel
        userHasLiked: false, // À implémenter si nécessaire
        userHasDisliked: false,
        replies: [], // À implémenter avec les commentaires
        type: post.type || '',
        category: post.category || '',
        images: Array.isArray(post.images) ? post.images : [],
        tags: Array.isArray(post.tags) ? post.tags : []
      }));

      return reviews;
    } catch (error) {
      console.error('Erreur dans getUserReviews:', error);
      return [];
    }
  }

  /**
   * Récupère les statistiques des avis d'un utilisateur
   */
  static async getUserReviewsStats(userId: string): Promise<{
    totalReviews: number;
    averageRating: number;
    positiveReviews: number;
    neutralReviews: number;
    negativeReviews: number;
  }> {
    try {
      const reviews = await this.getUserReviews(userId);

      const totalReviews = reviews.length;
      const ratingsSum = reviews.reduce((sum, review) => sum + review.rating, 0);
      const averageRating = totalReviews > 0 ? ratingsSum / totalReviews : 0;

      const positiveReviews = reviews.filter(review => review.rating >= 4).length;
      const neutralReviews = reviews.filter(review => review.rating === 3).length;
      const negativeReviews = reviews.filter(review => review.rating <= 2).length;

      return {
        totalReviews,
        averageRating,
        positiveReviews,
        neutralReviews,
        negativeReviews
      };
    } catch (error) {
      console.error('Erreur dans getUserReviewsStats:', error);
      return {
        totalReviews: 0,
        averageRating: 0,
        positiveReviews: 0,
        neutralReviews: 0,
        negativeReviews: 0
      };
    }
  }

  /**
   * Supprime un avis utilisateur
   */
  static async deleteUserReview(reviewId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('posts')
        .delete()
        .eq('id', reviewId)
        .eq('user_id', userId);

      if (error) {
        console.error('Erreur lors de la suppression de l\'avis:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erreur dans deleteUserReview:', error);
      return false;
    }
  }

  /**
   * Met à jour un avis utilisateur (sans modifier la note)
   */
  static async updateUserReview(
    reviewId: string,
    userId: string,
    updates: {
      description?: string;
      rating?: number; // Ignoré - les notes ne peuvent pas être modifiées
      tags?: string[];
    }
  ): Promise<boolean> {
    try {
      // Préparer les données de mise à jour sans la note
      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      // Ajouter seulement les champs modifiables
      if (updates.description !== undefined) {
        updateData.description = updates.description;
      }

      if (updates.tags !== undefined) {
        updateData.tags = updates.tags;
      }

      // Note : La note (rating) n'est volontairement pas incluse pour empêcher sa modification

      const { error } = await supabase
        .from('posts')
        .update(updateData)
        .eq('id', reviewId)
        .eq('user_id', userId);

      if (error) {
        console.error('Erreur lors de la mise à jour de l\'avis:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erreur dans updateUserReview:', error);
      return false;
    }
  }

  /**
   * Extrait la première URL d'image d'un tableau d'images
   */
  private static getFirstImageUrl(images: any): string | null {
    if (!images) return null;

    if (typeof images === 'string') {
      try {
        const parsed = JSON.parse(images);
        return Array.isArray(parsed) && parsed.length > 0 ? parsed[0] : null;
      } catch {
        return null;
      }
    }

    if (Array.isArray(images) && images.length > 0) {
      return images[0];
    }

    return null;
  }

  /**
   * Formate la date pour l'affichage
   */
  static formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Obtient le texte du type d'avis
   */
  static getReviewTypeText(type: string): string {
    switch (type) {
      case 'coup_de_coeur':
        return 'Coup de cœur';
      case 'coup_de_gueule':
        return 'Coup de gueule';
      case 'review':
        return 'Avis';
      case 'favorite':
        return 'Favori';
      default:
        return 'Avis';
    }
  }

  /**
   * Obtient la couleur du badge selon le type d'avis
   */
  static getReviewTypeBadgeColor(type: string): string {
    switch (type) {
      case 'coup_de_coeur':
        return 'bg-green-100 text-green-800';
      case 'coup_de_gueule':
        return 'bg-red-100 text-red-800';
      case 'review':
        return 'bg-blue-100 text-blue-800';
      case 'favorite':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
}
