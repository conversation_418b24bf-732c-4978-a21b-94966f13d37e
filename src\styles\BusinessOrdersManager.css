/* Gestionnaire de commandes d'entreprise */
.business-orders-manager {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem;
  background: #f8fafc;
  min-height: 100vh;
}

/* En-tête */
.orders-header {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-left .page-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-title h1 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.page-title p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.refresh-btn, .export-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
}

/* Statistiques */
.orders-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  color: white;
}

.stat-icon.total { background: #3b82f6; }
.stat-icon.pending { background: #f59e0b; }
.stat-icon.processing { background: #8b5cf6; }
.stat-icon.shipped { background: #10b981; }
.stat-icon.revenue { background: #ef4444; }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Filtres */
.orders-filters {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filters-row {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-box svg {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
}

.search-box input {
  width: 100%;
  padding: 0.5rem 0.75rem 0.5rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
}

.search-box input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-group, .sort-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.filter-group select, .sort-group select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
}

.sort-order-btn {
  padding: 0.5rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-weight: bold;
}

/* Contenu des commandes */
.orders-content {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.loading-state, .empty-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.loading-state svg.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.empty-state h3,
.error-state h3 {
  margin: 1rem 0 0.5rem 0;
  color: #374151;
}

.empty-state p,
.error-state p {
  margin: 0 0 1rem 0;
  color: #6b7280;
}

.error-state svg {
  color: #ef4444;
}

.error-state h3 {
  color: #ef4444;
}

/* Table des commandes */
.orders-table-container {
  overflow-x: auto;
}

.orders-table {
  width: 100%;
  border-collapse: collapse;
}

.orders-table th {
  background: #f9fafb;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.orders-table td {
  padding: 1rem 0.75rem;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
}

.order-row:hover {
  background: #f9fafb;
}

.order-row.selected {
  background: #eff6ff;
}

/* Cellules spécifiques */
.order-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.order-number {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.tracking-number {
  font-size: 0.75rem;
  color: #6b7280;
}

.customer-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.customer-name {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.875rem;
}

.customer-contact {
  font-size: 0.75rem;
  color: #6b7280;
}

.products-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.products-count {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.875rem;
}

.main-product {
  font-size: 0.75rem;
  color: #6b7280;
}

.amount-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: flex-end;
}

.total-amount {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.payment-badge {
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.payment-badge.paid {
  background: #d1fae5;
  color: #065f46;
}

.payment-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

.payment-badge.failed {
  background: #fee2e2;
  color: #991b1b;
}

.payment-badge.refunded {
  background: #e0e7ff;
  color: #3730a3;
}

/* Badges de statut */
.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.confirmed {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge.processing {
  background: #ede9fe;
  color: #6b21a8;
}

.status-badge.shipped {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.delivered {
  background: #dcfce7;
  color: #166534;
}

.status-badge.cancelled {
  background: #fee2e2;
  color: #991b1b;
}

/* Badges de priorité */
.priority-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.priority-badge.urgent {
  background: #fee2e2;
  color: #991b1b;
}

.priority-badge.high {
  background: #fed7aa;
  color: #9a3412;
}

.priority-badge.normal {
  background: #fef3c7;
  color: #92400e;
}

.priority-badge.low {
  background: #d1fae5;
  color: #065f46;
}

/* Informations de date */
.date-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.created-date {
  font-size: 0.875rem;
  color: #1f2937;
}

.delivery-date {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Actions */
.order-actions {
  display: flex;
  gap: 0.25rem;
}

.order-actions button {
  padding: 0.25rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

/* Responsive */
@media (max-width: 768px) {
  .business-orders-manager {
    padding: 0.5rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .orders-stats {
    grid-template-columns: 1fr;
  }

  .filters-row {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    min-width: auto;
  }

  .filter-group, .sort-group {
    justify-content: space-between;
  }

  .orders-table-container {
    font-size: 0.75rem;
  }

  .orders-table th,
  .orders-table td {
    padding: 0.5rem 0.25rem;
  }
}
