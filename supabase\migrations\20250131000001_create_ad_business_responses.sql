-- Migration pour créer la table des réponses des entreprises aux commentaires publicitaires
-- Cette migration permet aux entreprises de répondre aux commentaires sur leurs publicités

-- =====================================================
-- 1. TABLE DES RÉPONSES BUSINESS AUX COMMENTAIRES PUBLICITAIRES
-- =====================================================

CREATE TABLE IF NOT EXISTS ad_business_responses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  comment_id uuid NOT NULL REFERENCES ad_comments(id) ON DELETE CASCADE,
  business_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  
  -- Contenu de la réponse
  content TEXT NOT NULL CHECK (length(content) > 0 AND length(content) <= 1000),
  
  -- Modération
  is_approved BOOLEAN DEFAULT true,
  is_flagged BOOLEAN DEFAULT false,
  flagged_reason TEXT,
  
  -- Métadonnées
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  
  -- Contraintes
  UNIQUE(comment_id, business_id) -- Une seule réponse par entreprise par commentaire
);

-- =====================================================
-- 2. INDEX POUR AMÉLIORER LES PERFORMANCES
-- =====================================================

-- Index pour récupérer rapidement les réponses d'une entreprise
CREATE INDEX IF NOT EXISTS idx_ad_business_responses_business_id 
ON ad_business_responses(business_id);

-- Index pour récupérer rapidement les réponses à un commentaire
CREATE INDEX IF NOT EXISTS idx_ad_business_responses_comment_id 
ON ad_business_responses(comment_id);

-- Index pour les requêtes par date
CREATE INDEX IF NOT EXISTS idx_ad_business_responses_created_at 
ON ad_business_responses(created_at DESC);

-- =====================================================
-- 3. POLITIQUES RLS (ROW LEVEL SECURITY)
-- =====================================================

-- Activer RLS sur la table
ALTER TABLE ad_business_responses ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre aux entreprises de créer des réponses
CREATE POLICY "businesses_can_create_responses" ON ad_business_responses
  FOR INSERT TO authenticated
  WITH CHECK (
    -- Vérifier que l'utilisateur est une entreprise
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'business'
    )
    AND
    -- Vérifier que l'entreprise répond à un commentaire sur sa propre publicité
    EXISTS (
      SELECT 1 FROM ad_comments ac
      JOIN ad_campaigns camp ON ac.campaign_id = camp.id
      WHERE ac.id = comment_id 
      AND camp.business_id = auth.uid()
    )
  );

-- Politique pour permettre aux entreprises de lire leurs propres réponses
CREATE POLICY "businesses_can_read_own_responses" ON ad_business_responses
  FOR SELECT TO authenticated
  USING (business_id = auth.uid());

-- Politique pour permettre à tous de lire les réponses approuvées
CREATE POLICY "anyone_can_read_approved_responses" ON ad_business_responses
  FOR SELECT TO authenticated
  USING (is_approved = true);

-- Politique pour permettre aux entreprises de modifier leurs propres réponses
CREATE POLICY "businesses_can_update_own_responses" ON ad_business_responses
  FOR UPDATE TO authenticated
  USING (business_id = auth.uid())
  WITH CHECK (business_id = auth.uid());

-- Politique pour permettre aux entreprises de supprimer leurs propres réponses
CREATE POLICY "businesses_can_delete_own_responses" ON ad_business_responses
  FOR DELETE TO authenticated
  USING (business_id = auth.uid());

-- =====================================================
-- 4. FONCTION DE VALIDATION
-- =====================================================

-- Fonction pour vérifier qu'une entreprise ne peut répondre qu'aux commentaires sur ses propres publicités
CREATE OR REPLACE FUNCTION validate_business_response()
RETURNS TRIGGER AS $$
BEGIN
  -- Vérifier que l'entreprise répond à un commentaire sur sa propre publicité
  IF NOT EXISTS (
    SELECT 1 FROM ad_comments ac
    JOIN ad_campaigns camp ON ac.campaign_id = camp.id
    WHERE ac.id = NEW.comment_id 
    AND camp.business_id = NEW.business_id
  ) THEN
    RAISE EXCEPTION 'Une entreprise ne peut répondre qu''aux commentaires sur ses propres publicités';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour valider les réponses
CREATE TRIGGER validate_business_response_trigger
  BEFORE INSERT OR UPDATE ON ad_business_responses
  FOR EACH ROW
  EXECUTE FUNCTION validate_business_response();

-- =====================================================
-- 5. FONCTION DE MISE À JOUR AUTOMATIQUE
-- =====================================================

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_ad_business_responses_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour la mise à jour automatique
CREATE TRIGGER update_ad_business_responses_updated_at_trigger
  BEFORE UPDATE ON ad_business_responses
  FOR EACH ROW
  EXECUTE FUNCTION update_ad_business_responses_updated_at();

-- =====================================================
-- 6. VUE POUR FACILITER LES REQUÊTES
-- =====================================================

-- Vue pour récupérer les réponses avec les informations des commentaires et campagnes
CREATE OR REPLACE VIEW ad_responses_with_context AS
SELECT
  abr.*,
  ac.content as comment_content,
  ac.created_at as comment_created_at,
  p_commenter.username as commenter_username,
  p_commenter.profile_picture as commenter_profile_picture,
  p_business.username as business_username,
  p_business.full_name as business_name,
  camp.title as campaign_title,
  camp.id as campaign_id
FROM ad_business_responses abr
JOIN ad_comments ac ON abr.comment_id = ac.id
JOIN profiles p_commenter ON ac.user_id = p_commenter.id
JOIN profiles p_business ON abr.business_id = p_business.id
JOIN ad_campaigns camp ON ac.campaign_id = camp.id
WHERE abr.is_approved = true
ORDER BY abr.created_at DESC;

-- =====================================================
-- 7. FONCTION POUR NOTIFIER L'ENTREPRISE
-- =====================================================

-- Fonction pour créer une notification quand un commentaire est ajouté sur une publicité
CREATE OR REPLACE FUNCTION notify_business_of_ad_comment()
RETURNS TRIGGER AS $$
DECLARE
  business_id_var uuid;
  campaign_title_var text;
BEGIN
  -- Récupérer l'ID de l'entreprise et le titre de la campagne
  SELECT camp.business_id, camp.title
  INTO business_id_var, campaign_title_var
  FROM ad_campaigns camp
  WHERE camp.id = NEW.campaign_id;
  
  -- Créer une notification pour l'entreprise (à implémenter selon votre système de notifications)
  -- Pour l'instant, on log juste l'événement
  RAISE NOTICE 'Nouveau commentaire sur la publicité "%" de l''entreprise %', campaign_title_var, business_id_var;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour notifier l'entreprise
CREATE TRIGGER notify_business_of_ad_comment_trigger
  AFTER INSERT ON ad_comments
  FOR EACH ROW
  EXECUTE FUNCTION notify_business_of_ad_comment();

-- =====================================================
-- 8. COMMENTAIRES POUR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE ad_business_responses IS 'Table pour stocker les réponses des entreprises aux commentaires sur leurs publicités';
COMMENT ON COLUMN ad_business_responses.comment_id IS 'ID du commentaire auquel l''entreprise répond';
COMMENT ON COLUMN ad_business_responses.business_id IS 'ID de l''entreprise qui répond';
COMMENT ON COLUMN ad_business_responses.content IS 'Contenu de la réponse (max 1000 caractères)';
COMMENT ON COLUMN ad_business_responses.is_approved IS 'Indique si la réponse est approuvée pour affichage public';

COMMENT ON FUNCTION validate_business_response() IS 'Valide qu''une entreprise ne peut répondre qu''aux commentaires sur ses propres publicités';
COMMENT ON FUNCTION notify_business_of_ad_comment() IS 'Notifie l''entreprise quand un nouveau commentaire est ajouté sur ses publicités';

COMMENT ON VIEW ad_responses_with_context IS 'Vue enrichie des réponses avec le contexte des commentaires et campagnes';

-- =====================================================
-- 9. DONNÉES DE TEST (OPTIONNEL)
-- =====================================================

-- Insérer quelques réponses de test si des campagnes et commentaires existent
DO $$
DECLARE
  test_comment_id uuid;
  test_business_id uuid;
BEGIN
  -- Récupérer un commentaire de test et l'entreprise correspondante
  SELECT ac.id, camp.business_id
  INTO test_comment_id, test_business_id
  FROM ad_comments ac
  JOIN ad_campaigns camp ON ac.campaign_id = camp.id
  LIMIT 1;
  
  -- Insérer une réponse de test si des données existent
  IF test_comment_id IS NOT NULL AND test_business_id IS NOT NULL THEN
    INSERT INTO ad_business_responses (comment_id, business_id, content)
    VALUES (
      test_comment_id,
      test_business_id,
      'Merci pour votre commentaire ! Nous sommes ravis que notre produit vous plaise. N''hésitez pas à nous contacter si vous avez des questions.'
    )
    ON CONFLICT (comment_id, business_id) DO NOTHING;
    
    RAISE NOTICE 'Réponse de test créée pour l''entreprise %', test_business_id;
  END IF;
END $$;

-- =====================================================
-- 10. MESSAGE DE SUCCÈS
-- =====================================================

SELECT 
  '✅ TABLE AD_BUSINESS_RESPONSES CRÉÉE AVEC SUCCÈS !' as message,
  '📝 Les entreprises peuvent maintenant répondre aux commentaires sur leurs publicités' as note,
  '🔒 Politiques RLS configurées pour la sécurité' as security,
  '🎯 Utilisez BusinessAdsManager pour gérer les réponses' as usage;
