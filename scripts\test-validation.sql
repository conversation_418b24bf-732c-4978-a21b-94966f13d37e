-- SCRIPT DE TEST DE LA VALIDATION (CORRIGÉ)
-- Exécutez ce script pour tester la validation manuellement

-- =====================================================
-- 0. DÉCOUVRIR LA STRUCTURE DES TABLES
-- =====================================================

-- Vérifier les colonnes de business_subscriptions
SELECT
    '📋 COLONNES BUSINESS_SUBSCRIPTIONS' as section,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'business_subscriptions'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Vérifier les colonnes de subscription_codes
SELECT
    '🔐 COLONNES SUBSCRIPTION_CODES' as section,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'subscription_codes'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- =====================================================
-- 1. VÉRIFIER L'ÉTAT ACTUEL DU CODE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 VÉRIFICATION DE L''ÉTAT ACTUEL';
    RAISE NOTICE '================================';
    RAISE NOTICE '';
END $$;

-- Voir le code existant
SELECT
    '📋 CODE ACTUEL' as section,
    id,
    code,
    business_name,
    business_id,
    plan_type,
    amount,
    status,
    generated_at,
    expires_at
FROM subscription_codes
WHERE code = '02500076';

-- Voir l'abonnement existant (toutes les colonnes)
SELECT
    '🏢 ABONNEMENT EXISTANT' as section,
    *
FROM business_subscriptions
WHERE business_id = 'f22bf671-e198-4890-9cd7-029ab3596180';

-- =====================================================
-- 2. SIMULER LA VALIDATION MANUELLE
-- =====================================================

-- Mettre à jour le code pour le valider
UPDATE subscription_codes
SET
    status = 'validated',
    validated_at = NOW(),
    validated_by = NULL, -- Ou utiliser un UUID valide si nécessaire
    admin_notes = 'Validation manuelle de test',
    updated_at = NOW()
WHERE code = '02500076';

-- Vérifier la mise à jour
SELECT 
    '✅ CODE APRÈS VALIDATION' as section,
    id,
    code,
    business_name,
    business_id,
    plan_type,
    amount,
    status,
    validated_at,
    validated_by,
    admin_notes
FROM subscription_codes 
WHERE code = '02500076';

-- =====================================================
-- 3. CRÉER L'ABONNEMENT MANUELLEMENT
-- =====================================================

-- Désactiver les abonnements existants
UPDATE business_subscriptions 
SET 
    is_active = false,
    updated_at = NOW()
WHERE business_id = 'f22bf671-e198-4890-9cd7-029ab3596180' 
AND is_active = true;

-- Créer le nouvel abonnement (colonnes minimales)
-- Utilisons seulement les colonnes de base qui existent certainement

INSERT INTO business_subscriptions (
    business_id,
    plan_id,
    is_active,
    start_date,
    end_date,
    created_at,
    updated_at,
    payment_status,
    payment_method,
    stripe_subscription_id
) VALUES (
    'f22bf671-e198-4890-9cd7-029ab3596180',
    'monthly',
    true,
    NOW(),
    NOW() + INTERVAL '1 month',
    NOW(),
    NOW(),
    'paid',
    NULL,
    NULL
);

-- =====================================================
-- 4. VÉRIFIER LE RÉSULTAT FINAL
-- =====================================================

-- Vérifier le code validé
SELECT 
    '✅ VÉRIFICATION FINALE - CODE' as section,
    code,
    business_name,
    status,
    validated_at,
    admin_notes
FROM subscription_codes 
WHERE code = '02500076';

-- Vérifier l'abonnement créé
SELECT
    '✅ VÉRIFICATION FINALE - ABONNEMENT' as section,
    id,
    business_id,
    plan_type,
    is_active,
    start_date,
    end_date,
    amount,
    payment_method,
    created_at
FROM business_subscriptions
WHERE business_id = 'f22bf671-e198-4890-9cd7-029ab3596180'
AND is_active = true;

-- =====================================================
-- 5. STATISTIQUES FINALES
-- =====================================================

DO $$
DECLARE
    code_status text;
    has_active_subscription boolean;
    subscription_count integer;
BEGIN
    -- Vérifier le statut du code
    SELECT status INTO code_status 
    FROM subscription_codes 
    WHERE code = '02500076';
    
    -- Vérifier s'il y a un abonnement actif
    SELECT EXISTS(
        SELECT 1 FROM business_subscriptions 
        WHERE business_id = 'f22bf671-e198-4890-9cd7-029ab3596180' 
        AND is_active = true
    ) INTO has_active_subscription;
    
    -- Compter les abonnements
    SELECT COUNT(*) INTO subscription_count
    FROM business_subscriptions 
    WHERE business_id = 'f22bf671-e198-4890-9cd7-029ab3596180';
    
    RAISE NOTICE '';
    RAISE NOTICE '📊 RÉSULTATS DU TEST:';
    RAISE NOTICE '====================';
    RAISE NOTICE '🔐 Statut du code: %', COALESCE(code_status, 'NON TROUVÉ');
    RAISE NOTICE '✅ Abonnement actif: %', CASE WHEN has_active_subscription THEN 'OUI' ELSE 'NON' END;
    RAISE NOTICE '📈 Total abonnements: %', subscription_count;
    RAISE NOTICE '';
    
    IF code_status = 'validated' AND has_active_subscription THEN
        RAISE NOTICE '🎉 SUCCÈS: Code validé et abonnement créé !';
        RAISE NOTICE '';
        RAISE NOTICE '🔄 PROCHAINES ÉTAPES:';
        RAISE NOTICE '1. Rechargez l''interface admin';
        RAISE NOTICE '2. Vérifiez que le code apparaît comme "Validé"';
        RAISE NOTICE '3. Vérifiez l''abonnement côté entreprise';
    ELSE
        RAISE NOTICE '❌ PROBLÈME: Validation incomplète';
        RAISE NOTICE 'Code: % | Abonnement: %', code_status, has_active_subscription;
    END IF;
    RAISE NOTICE '';
END $$;
