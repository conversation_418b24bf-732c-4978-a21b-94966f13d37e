import React, { useState, useEffect } from 'react';
import {
  Building,
  Search,
  Filter,
  MoreVertical,
  Shield,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  Trash2,
  Download,
  Plus,
  Calendar,
  MapPin,
  Mail,
  Phone,
  Globe,
  Star,
  TrendingUp,
  AlertTriangle,
  Clock,
  Award,
  Target,
  FileText,
  DollarSign,
  Users,
  Package,
  BarChart3
} from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import VerificationBadge from '../ui/VerificationBadge';
import { AdminService } from '../../services/adminService';
import { BusinessManagementAction } from '../../types/admin';
import { UserRole, BusinessStatus } from '../../types';
import { supabase } from '../../lib/supabase';

// Fonction pour formater les dates
const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'il y a quelques secondes';
  if (diffInSeconds < 3600) return `il y a ${Math.floor(diffInSeconds / 60)} minutes`;
  if (diffInSeconds < 86400) return `il y a ${Math.floor(diffInSeconds / 3600)} heures`;
  if (diffInSeconds < 2592000) return `il y a ${Math.floor(diffInSeconds / 86400)} jours`;
  if (diffInSeconds < 31536000) return `il y a ${Math.floor(diffInSeconds / 2592000)} mois`;
  return `il y a ${Math.floor(diffInSeconds / 31536000)} ans`;
};

interface Business {
  id: string;
  business_name: string;
  business_description?: string;
  business_category?: string;
  business_status: BusinessStatus;
  business_verified: boolean;
  created_at: string;
  updated_at: string;
  // Données du profil utilisateur associé
  username: string;
  email: string;
  profile_picture?: string;
  country?: string;
  city?: string;
  phone?: string;
  website?: string;
  is_verified: boolean;
  // Métriques business
  total_products?: number;
  total_sales?: number;
  total_revenue?: number;
  average_rating?: number;
  total_reviews?: number;
  last_activity?: string;
}

const BusinessManagement: React.FC = () => {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<BusinessStatus | 'all'>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedVerification, setSelectedVerification] = useState<string>('all');
  const [selectedBusinesses, setSelectedBusinesses] = useState<string[]>([]);
  const [showActionModal, setShowActionModal] = useState(false);
  const [showBusinessDetailModal, setShowBusinessDetailModal] = useState(false);
  const [showBulkActionModal, setShowBulkActionModal] = useState(false);
  const [actionBusiness, setActionBusiness] = useState<Business | null>(null);
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({ start: '', end: '' });
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [stats, setStats] = useState({
    total: 0,
    verified: 0,
    active: 0,
    premium: 0,
    newToday: 0,
    totalRevenue: 0
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadBusinesses();

    // Gestionnaire pour ouvrir la modal de détails depuis d'autres composants
    const handleOpenBusinessDetails = (event: CustomEvent) => {
      const business = event.detail;
      setDetailBusiness(business);
      setShowBusinessDetailModal(true);
    };

    window.addEventListener('openBusinessDetails', handleOpenBusinessDetails as EventListener);

    return () => {
      window.removeEventListener('openBusinessDetails', handleOpenBusinessDetails as EventListener);
    };
  }, []);

  const loadBusinesses = async () => {
    try {
      setLoading(true);
      setError(null);

      // Charger les entreprises avec seulement les colonnes de base d'abord
      const { data: businessData, error: businessError } = await supabase
        .from('business_profiles')
        .select(`
          id,
          business_name,
          business_description,
          business_category,
          business_status,
          business_verified,
          created_at,
          updated_at
        `)
        .order('created_at', { ascending: false });

      if (businessError) {
        console.error('Erreur lors du chargement des entreprises:', businessError);
        setError(`Erreur lors du chargement des entreprises: ${businessError.message}`);
        return;
      }

      console.log('Données business chargées:', businessData?.length || 0, 'entreprises');

      // Charger les données utilisateur associées
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select(`
          id,
          username,
          email,
          profile_picture,
          country,
          city,
          phone,
          website,
          is_verified
        `)
        .eq('role', 'business');

      if (userError) {
        console.error('Erreur lors du chargement des données utilisateur:', userError);
      }

      // Fusionner les données
      const enrichedBusinesses = (businessData || []).map(business => {
        const userInfo = userData?.find(u => u.id === business.id);
        return {
          ...business,
          username: userInfo?.username || 'Inconnu',
          email: userInfo?.email || '',
          profile_picture: userInfo?.profile_picture,
          country: userInfo?.country,
          city: userInfo?.city,
          phone: userInfo?.phone,
          website: userInfo?.website,
          is_verified: userInfo?.is_verified || false,
          // Valeurs par défaut pour les métriques
          total_products: 0,
          total_sales: 0,
          total_revenue: 0,
          average_rating: null,
          total_reviews: 0,
          last_activity: business.updated_at
        };
      });

      console.log('Entreprises enrichies:', enrichedBusinesses);

      setBusinesses(enrichedBusinesses);

      // Calculer les statistiques
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const newStats = {
        total: enrichedBusinesses.length,
        verified: enrichedBusinesses.filter(b => b.business_verified).length,
        active: enrichedBusinesses.filter(b => b.business_status === BusinessStatus.VERIFIED).length,
        premium: enrichedBusinesses.filter(b => b.business_status === BusinessStatus.PREMIUM).length,
        newToday: enrichedBusinesses.filter(b => new Date(b.created_at) >= today).length,
        totalRevenue: enrichedBusinesses.reduce((sum, b) => sum + (b.total_revenue || 0), 0)
      };

      setStats(newStats);

    } catch (error) {
      console.error('Erreur dans loadBusinesses:', error);
      setError(`Erreur inattendue: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    } finally {
      setLoading(false);
    }
  };

  const filteredBusinesses = businesses.filter(business => {
    const matchesSearch = business.business_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         business.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         business.username.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = selectedStatus === 'all' || business.business_status === selectedStatus;
    const matchesCategory = selectedCategory === 'all' || business.business_category === selectedCategory;
    const matchesVerification = selectedVerification === 'all' ||
                               (selectedVerification === 'verified' && business.business_verified) ||
                               (selectedVerification === 'unverified' && !business.business_verified);

    let matchesDateRange = true;
    if (dateRange.start && dateRange.end) {
      const businessDate = new Date(business.created_at);
      const startDate = new Date(dateRange.start);
      const endDate = new Date(dateRange.end);
      matchesDateRange = businessDate >= startDate && businessDate <= endDate;
    }

    return matchesSearch && matchesStatus && matchesCategory && matchesVerification && matchesDateRange;
  });

  // Pagination
  const totalPages = Math.ceil(filteredBusinesses.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedBusinesses = filteredBusinesses.slice(startIndex, startIndex + itemsPerPage);

  const handleBusinessAction = async (action: BusinessManagementAction) => {
    try {
      const success = await AdminService.executeBusinessAction(action);
      if (success) {
        await loadBusinesses();
        setShowActionModal(false);
        setActionBusiness(null);
      }
    } catch (error) {
      console.error('Erreur lors de l\'action entreprise:', error);
    }
  };

  const getBusinessStatusLabel = (status: BusinessStatus): string => {
    const labels: Record<BusinessStatus, string> = {
      [BusinessStatus.NEW]: 'Nouveau',
      [BusinessStatus.VERIFIED]: 'Vérifié',
      [BusinessStatus.PREMIUM]: 'Premium',
      [BusinessStatus.FEATURED]: 'Mis en avant',
      [BusinessStatus.PARTNER]: 'Partenaire'
    };
    return labels[status] || status;
  };

  const getBusinessStatusColor = (status: BusinessStatus): string => {
    const colors: Record<BusinessStatus, string> = {
      [BusinessStatus.NEW]: 'bg-gray-100 text-gray-800',
      [BusinessStatus.VERIFIED]: 'bg-blue-100 text-blue-800',
      [BusinessStatus.PREMIUM]: 'bg-purple-100 text-purple-800',
      [BusinessStatus.FEATURED]: 'bg-yellow-100 text-yellow-800',
      [BusinessStatus.PARTNER]: 'bg-green-100 text-green-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const handleSelectBusiness = (businessId: string) => {
    setSelectedBusinesses(prev =>
      prev.includes(businessId)
        ? prev.filter(id => id !== businessId)
        : [...prev, businessId]
    );
  };

  const handleSelectAll = () => {
    if (selectedBusinesses.length === paginatedBusinesses.length) {
      setSelectedBusinesses([]);
    } else {
      setSelectedBusinesses(paginatedBusinesses.map(business => business.id));
    }
  };

  const exportBusinesses = () => {
    const csvContent = [
      ['ID', 'Nom Entreprise', 'Email', 'Statut', 'Catégorie', 'Vérifié', 'Pays', 'Ville', 'Inscription', 'Chiffre d\'affaires'],
      ...filteredBusinesses.map(business => [
        business.id,
        business.business_name,
        business.email,
        business.business_status,
        business.business_category || '',
        business.business_verified ? 'Oui' : 'Non',
        business.country || '',
        business.city || '',
        new Date(business.created_at).toLocaleDateString('fr-FR'),
        business.total_revenue || 0
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `entreprises_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XAF',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gestion des Entreprises</h2>
          <p className="text-gray-600 mt-1">Gérer les comptes entreprises de la plateforme</p>
        </div>
        <div className="flex space-x-2">
          {selectedBusinesses.length > 0 && (
            <Button
              variant="outline"
              leftIcon={<Target size={16} />}
              onClick={() => setShowBulkActionModal(true)}
              className="text-orange-600 border-orange-600 hover:bg-orange-50"
            >
              Actions ({selectedBusinesses.length})
            </Button>
          )}
          <Button
            variant="outline"
            leftIcon={<Download size={16} />}
            onClick={exportBusinesses}
          >
            Exporter
          </Button>
          <Button
            onClick={() => loadBusinesses()}
            leftIcon={<Building size={16} />}
          >
            Actualiser
          </Button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Building size={20} className="text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-lg font-semibold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Shield size={20} className="text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Vérifiées</p>
                <p className="text-lg font-semibold text-gray-900">{stats.verified}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Award size={20} className="text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Premium</p>
                <p className="text-lg font-semibold text-gray-900">{stats.premium}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <TrendingUp size={20} className="text-yellow-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Nouvelles</p>
                <p className="text-lg font-semibold text-gray-900">{stats.newToday}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign size={20} className="text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">CA Total</p>
                <p className="text-lg font-semibold text-gray-900">{formatCurrency(stats.totalRevenue)}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <CheckCircle size={20} className="text-indigo-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Actives</p>
                <p className="text-lg font-semibold text-gray-900">{stats.active}</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Filtres et recherche */}
      <Card>
        <CardBody className="p-4">
          <div className="space-y-4">
            {/* Première ligne : Recherche et filtres principaux */}
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Rechercher par nom d'entreprise, email ou utilisateur..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value as BusinessStatus | 'all')}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Tous les statuts</option>
                  <option value={BusinessStatus.NEW}>Nouveau</option>
                  <option value={BusinessStatus.VERIFIED}>Vérifié</option>
                  <option value={BusinessStatus.PREMIUM}>Premium</option>
                  <option value={BusinessStatus.FEATURED}>Mis en avant</option>
                  <option value={BusinessStatus.PARTNER}>Partenaire</option>
                </select>

                <select
                  value={selectedVerification}
                  onChange={(e) => setSelectedVerification(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Tous (vérifiés/non vérifiés)</option>
                  <option value="verified">Vérifiées uniquement</option>
                  <option value="unverified">Non vérifiées uniquement</option>
                </select>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Affichage des erreurs */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardBody className="p-4">
            <div className="flex items-center space-x-2 text-red-700">
              <AlertTriangle size={20} />
              <div>
                <h3 className="font-medium">Erreur de chargement</h3>
                <p className="text-sm">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => {
                    setError(null);
                    loadBusinesses();
                  }}
                >
                  Réessayer
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Liste des entreprises */}
      <Card>
        <CardBody className="p-0">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Chargement des entreprises...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <AlertTriangle size={48} className="mx-auto text-red-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Erreur de chargement</h3>
              <p className="text-gray-600 mb-4">Impossible de charger les entreprises</p>
              <Button onClick={() => loadBusinesses()}>
                Réessayer
              </Button>
            </div>
          ) : paginatedBusinesses.length === 0 ? (
            <div className="text-center py-12">
              <Building size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune entreprise trouvée</h3>
              <p className="text-gray-600">Aucune entreprise ne correspond à vos critères de recherche.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectedBusinesses.length === paginatedBusinesses.length && paginatedBusinesses.length > 0}
                        onChange={handleSelectAll}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Entreprise
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statut & Vérification
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Localisation
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Performance
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Inscription
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedBusinesses.map((business) => (
                    <tr key={business.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedBusinesses.includes(business.id)}
                          onChange={() => handleSelectBusiness(business.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <img
                              className="h-10 w-10 rounded-full object-cover"
                              src={business.profile_picture || '/default-business.png'}
                              alt={business.business_name}
                            />
                          </div>
                          <div className="ml-4">
                            <div className="flex items-center space-x-2">
                              <div className="text-sm font-medium text-gray-900">
                                {business.business_name}
                              </div>
                              <VerificationBadge
                                isVerified={business.is_verified || false}
                                userRole={UserRole.BUSINESS}
                                businessVerified={business.business_verified || false}
                                size="xs"
                                variant="minimal"
                                showTooltip={true}
                              />
                            </div>
                            <div className="text-sm text-gray-500">{business.email}</div>
                            <div className="text-xs text-gray-400">@{business.username}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getBusinessStatusColor(business.business_status)}`}>
                            {getBusinessStatusLabel(business.business_status)}
                          </span>
                          <br />
                          {business.business_category && (
                            <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-700">
                              {business.business_category}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          {business.country && (
                            <div className="flex items-center">
                              <MapPin size={12} className="mr-1 text-gray-400" />
                              {business.country}
                            </div>
                          )}
                          {business.city && (
                            <div className="text-xs text-gray-500">{business.city}</div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="space-y-1">
                          <div className="flex items-center">
                            <Package size={12} className="mr-1 text-gray-400" />
                            {business.total_products || 0} produits
                          </div>
                          <div className="flex items-center">
                            <DollarSign size={12} className="mr-1 text-gray-400" />
                            {formatCurrency(business.total_revenue || 0)}
                          </div>
                          {business.average_rating && (
                            <div className="flex items-center">
                              <Star size={12} className="mr-1 text-yellow-400" />
                              {business.average_rating.toFixed(1)} ({business.total_reviews || 0})
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>
                          {new Date(business.created_at).toLocaleDateString('fr-FR')}
                        </div>
                        <div className="text-xs">
                          {formatTimeAgo(business.created_at)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setActionBusiness(business);
                              setShowBusinessDetailModal(true);
                            }}
                            title="Voir les détails"
                          >
                            <Eye size={14} />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setActionBusiness(business);
                              setShowActionModal(true);
                            }}
                            title="Actions"
                          >
                            <MoreVertical size={14} />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Page {currentPage} sur {totalPages} • {filteredBusinesses.length} résultat(s)
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Précédent
                </Button>

                {/* Pages */}
                <div className="flex space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? "primary" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                        className="w-8 h-8 p-0"
                      >
                        {page}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Suivant
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Modal d'actions */}
      {showActionModal && actionBusiness && (
        <BusinessActionModal
          business={actionBusiness}
          onClose={() => {
            setShowActionModal(false);
            setActionBusiness(null);
          }}
          onAction={handleBusinessAction}
        />
      )}

      {/* Modal de détails entreprise */}
      {showBusinessDetailModal && actionBusiness && (
        <BusinessDetailModal
          business={actionBusiness}
          onClose={() => {
            setShowBusinessDetailModal(false);
            setActionBusiness(null);
          }}
        />
      )}

      {/* Modal d'actions en lot */}
      {showBulkActionModal && (
        <BulkBusinessActionModal
          selectedCount={selectedBusinesses.length}
          onClose={() => setShowBulkActionModal(false)}
          onAction={(action) => {
            // Implémenter les actions en lot
            console.log('Action en lot:', action, selectedBusinesses);
            setShowBulkActionModal(false);
          }}
        />
      )}
    </div>
  );
};

// Modal d'actions entreprise
interface BusinessActionModalProps {
  business: Business;
  onClose: () => void;
  onAction: (action: BusinessManagementAction) => void;
}

const BusinessActionModal: React.FC<BusinessActionModalProps> = ({ business, onClose, onAction }) => (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg p-6 w-full max-w-md">
      <h3 className="text-lg font-semibold mb-4">Actions pour {business.business_name}</h3>
      <div className="space-y-2">
        <Button
          variant="outline"
          className="w-full justify-start text-blue-600 hover:text-blue-700"
          leftIcon={<Eye size={16} />}
          onClick={() => {
            // Fermer cette modal et ouvrir la modal de détails
            onClose();
            // Déclencher l'ouverture de la modal de détails
            setTimeout(() => {
              const event = new CustomEvent('openBusinessDetails', { detail: business });
              window.dispatchEvent(event);
            }, 100);
          }}
        >
          Voir les détails complets
        </Button>

        {!business.business_verified && (
          <Button
            variant="outline"
            className="w-full justify-start text-blue-600 hover:text-blue-700"
            leftIcon={<Shield size={16} />}
            onClick={() => {
              const verificationDetails = `🛡️ VÉRIFICATION D'ENTREPRISE 🛡️

Entreprise: ${business.business_name}
Email: ${business.email}
Statut actuel: ${business.business_status}
Inscrite le: ${new Date(business.created_at).toLocaleDateString('fr-FR')}

La vérification va:
✅ Marquer l'entreprise comme vérifiée
✅ Lui donner le badge de vérification
✅ Améliorer sa visibilité sur la plateforme
✅ Changer son statut vers "verified" si nécessaire

Confirmez-vous la vérification ?`;

              if (window.confirm(verificationDetails)) {
                const reason = prompt(
                  'Raison de la vérification (optionnel):',
                  'Vérification manuelle - Documents et informations validés'
                );

                onAction({
                  businessId: business.id,
                  action: 'verify',
                  reason: reason || 'Vérification manuelle par administrateur'
                });
                onClose();
              }
            }}
          >
            🛡️ Vérifier l'entreprise
          </Button>
        )}

        <Button
          variant="outline"
          className="w-full justify-start text-green-600 hover:text-green-700"
          leftIcon={<CheckCircle size={16} />}
          onClick={() => {
            // Créer une modal de sélection de statut plus professionnelle
            const statusOptions = [
              { value: 'new', label: '🆕 Nouveau', description: 'Entreprise récemment inscrite' },
              { value: 'verified', label: '✅ Vérifié', description: 'Entreprise validée et vérifiée' },
              { value: 'premium', label: '💎 Premium', description: 'Abonnement premium actif' },
              { value: 'featured', label: '⭐ Mis en avant', description: 'Entreprise promue et mise en avant' },
              { value: 'partner', label: '🤝 Partenaire', description: 'Partenaire officiel de la plateforme' }
            ];

            const currentStatus = business.business_status;
            const statusList = statusOptions.map(opt =>
              `${opt.value === currentStatus ? '→ ' : '  '}${opt.label} - ${opt.description}`
            ).join('\n');

            const newStatus = prompt(
              `Statut actuel: ${currentStatus}\n\nChoisissez un nouveau statut:\n\n${statusList}\n\nEntrez le statut (new, verified, premium, featured, partner):`,
              currentStatus
            );

            if (newStatus && ['new', 'verified', 'premium', 'featured', 'partner'].includes(newStatus) && newStatus !== currentStatus) {
              const reason = prompt(`Raison du changement vers "${newStatus}":`, `Changement de statut administratif`);
              if (reason) {
                onAction({
                  businessId: business.id,
                  action: 'change_status',
                  newStatus,
                  reason
                });
                onClose();
              }
            } else if (newStatus === currentStatus) {
              alert('Le statut sélectionné est déjà le statut actuel.');
            }
          }}
        >
          Changer le statut
        </Button>

        <Button
          variant="outline"
          className="w-full justify-start text-orange-600 hover:text-orange-700"
          leftIcon={<AlertTriangle size={16} />}
          onClick={() => {
            const suspensionReasons = [
              'Violation des conditions d\'utilisation',
              'Contenu inapproprié ou spam',
              'Activité suspecte ou frauduleuse',
              'Non-respect des règles commerciales',
              'Plaintes répétées de clients',
              'Documents de vérification invalides',
              'Autre (préciser)'
            ];

            const reasonsList = suspensionReasons.map((r, i) => `${i + 1}. ${r}`).join('\n');
            const reasonChoice = prompt(
              `⚠️ SUSPENSION D'ENTREPRISE ⚠️\n\nEntreprise: ${business.business_name}\n\nRaisons courantes:\n${reasonsList}\n\nEntrez la raison de la suspension:`,
              ''
            );

            if (reasonChoice && reasonChoice.trim()) {
              const confirmSuspension = confirm(
                `🚨 CONFIRMATION DE SUSPENSION 🚨\n\nEntreprise: ${business.business_name}\nRaison: ${reasonChoice}\n\nCette action suspendra immédiatement l'entreprise.\nÊtes-vous sûr de vouloir continuer ?`
              );

              if (confirmSuspension) {
                onAction({
                  businessId: business.id,
                  action: 'suspend',
                  reason: reasonChoice.trim()
                });
                onClose();
              }
            }
          }}
        >
          Suspendre l'entreprise
        </Button>

        {business.business_status === 'suspended' && (
          <Button
            variant="outline"
            className="w-full justify-start text-green-600 hover:text-green-700"
            leftIcon={<CheckCircle size={16} />}
            onClick={() => {
              const reactivationConfirm = window.confirm(
                `▶️ RÉACTIVATION D'ENTREPRISE\n\nEntreprise: ${business.business_name}\nStatut actuel: Suspendue\n\nCette action va:\n✅ Réactiver l'entreprise\n✅ Changer le statut vers "verified"\n✅ Supprimer la raison de suspension\n\nConfirmez-vous la réactivation ?`
              );

              if (reactivationConfirm) {
                const reason = prompt(
                  'Raison de la réactivation:',
                  'Réactivation après résolution des problèmes'
                );

                if (reason) {
                  onAction({
                    businessId: business.id,
                    action: 'activate',
                    reason: reason.trim()
                  });
                  onClose();
                }
              }
            }}
          >
            ▶️ Réactiver l'entreprise
          </Button>
        )}

        <Button
          variant="outline"
          className="w-full justify-start text-gray-600 hover:text-gray-700"
          leftIcon={<Globe size={16} />}
          onClick={() => {
            // Vérifier si l'entreprise a un username valide
            if (business.username && business.username !== 'Inconnu') {
              const profileUrl = `/business/${business.username}`;
              const confirmOpen = window.confirm(
                `🌐 PROFIL PUBLIC\n\nOuvrir le profil public de ${business.business_name} ?\n\nURL: ${window.location.origin}${profileUrl}\n\nCela ouvrira une nouvelle fenêtre.`
              );

              if (confirmOpen) {
                window.open(profileUrl, '_blank');
              }
            } else {
              alert(`❌ Impossible d'ouvrir le profil public\n\nL'entreprise ${business.business_name} n'a pas de nom d'utilisateur valide.\nUsername actuel: ${business.username || 'Non défini'}`);
            }
          }}
        >
          🌐 Voir profil public
        </Button>
      </div>

      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 mb-3">
          <strong>Informations rapides:</strong><br/>
          • Inscrite le: {new Date(business.created_at).toLocaleDateString('fr-FR')}<br/>
          • Statut: <span className={`font-medium ${
            business.business_status === 'premium' ? 'text-purple-600' :
            business.business_status === 'verified' ? 'text-green-600' :
            business.business_status === 'featured' ? 'text-yellow-600' :
            business.business_status === 'partner' ? 'text-blue-600' :
            'text-gray-600'
          }`}>{business.business_status}</span><br/>
          • Produits: {business.total_products || 0} | CA: {business.total_revenue ? new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF' }).format(business.total_revenue) : '0 F CFA'}<br/>
          {business.country && `• Localisation: ${business.city ? business.city + ', ' : ''}${business.country}`}<br/>
          {business.business_category && `• Catégorie: ${business.business_category}`}<br/>
          • Vérifiée: <span className={`font-medium ${business.business_verified ? 'text-green-600' : 'text-red-600'}`}>
            {business.business_verified ? '✅ Oui' : '❌ Non'}
          </span>
        </div>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1"
          >
            Fermer
          </Button>
          <Button
            onClick={() => {
              onClose();
              setTimeout(() => {
                const event = new CustomEvent('openBusinessDetails', { detail: business });
                window.dispatchEvent(event);
              }, 100);
            }}
            className="flex-1"
          >
            Voir détails complets
          </Button>
        </div>
      </div>
    </div>
  </div>
);

// Modal de détails entreprise
interface BusinessDetailModalProps {
  business: Business;
  onClose: () => void;
}

const BusinessDetailModal: React.FC<BusinessDetailModalProps> = ({ business, onClose }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <img
              src={business.profile_picture || '/default-business.png'}
              alt={business.business_name}
              className="w-16 h-16 rounded-full object-cover"
            />
            <div>
              <div className="flex items-center space-x-3">
                <h2 className="text-2xl font-bold text-gray-900">{business.business_name}</h2>
                <VerificationBadge
                  isVerified={business.is_verified || false}
                  userRole={UserRole.BUSINESS}
                  businessVerified={business.business_verified || false}
                  size="md"
                  variant="default"
                  showTooltip={true}
                />
              </div>
              <p className="text-gray-600">{business.email}</p>
              <p className="text-sm text-gray-500">@{business.username}</p>
            </div>
          </div>
          <Button variant="outline" onClick={onClose}>
            <XCircle size={20} />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Informations de base */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations Entreprise</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Building className="w-5 h-5 text-gray-400" />
                  <span className="text-sm text-gray-600">Nom:</span>
                  <span className="text-sm font-medium text-gray-900">{business.business_name}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="w-5 h-5 text-gray-400" />
                  <span className="text-sm text-gray-600">Email:</span>
                  <span className="text-sm font-medium text-gray-900">{business.email}</span>
                </div>
                {business.business_category && (
                  <div className="flex items-center space-x-3">
                    <Award className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-600">Catégorie:</span>
                    <span className="text-sm font-medium text-gray-900">{business.business_category}</span>
                  </div>
                )}
                {(business.city || business.country) && (
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-600">Localisation:</span>
                    <span className="text-sm font-medium text-gray-900">
                      {business.city && business.country ? `${business.city}, ${business.country}` : business.country || business.city}
                    </span>
                  </div>
                )}
                {business.phone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-600">Téléphone:</span>
                    <span className="text-sm font-medium text-gray-900">{business.phone}</span>
                  </div>
                )}
                {business.website && (
                  <div className="flex items-center space-x-3">
                    <Globe className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-600">Site web:</span>
                    <a
                      href={business.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm font-medium text-blue-600 hover:underline"
                    >
                      {business.website}
                    </a>
                  </div>
                )}
              </div>
            </div>

            {/* Statut et vérification */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Statut et Vérification</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${business.business_verified ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                    {business.business_verified ? 'Entreprise Vérifiée' : 'Non Vérifiée'}
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${business.business_status === 'verified' ? 'bg-blue-100 text-blue-800' : business.business_status === 'premium' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'}`}>
                    Statut: {business.business_status}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Métriques et performance */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Package className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">Produits</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-600 mt-1">{business.total_products || 0}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="w-5 h-5 text-green-600" />
                    <span className="text-sm font-medium text-green-900">Ventes</span>
                  </div>
                  <p className="text-2xl font-bold text-green-600 mt-1">{business.total_sales || 0}</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="w-5 h-5 text-purple-600" />
                    <span className="text-sm font-medium text-purple-900">Chiffre d'affaires</span>
                  </div>
                  <p className="text-lg font-bold text-purple-600 mt-1">
                    {business.total_revenue ? new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF' }).format(business.total_revenue) : '0 F CFA'}
                  </p>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Star className="w-5 h-5 text-yellow-600" />
                    <span className="text-sm font-medium text-yellow-900">Note moyenne</span>
                  </div>
                  <p className="text-2xl font-bold text-yellow-600 mt-1">
                    {business.average_rating ? business.average_rating.toFixed(1) : 'N/A'}
                    {business.total_reviews && (
                      <span className="text-sm font-normal"> ({business.total_reviews} avis)</span>
                    )}
                  </p>
                </div>
              </div>
            </div>

            {/* Dates importantes */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Historique</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Calendar className="w-5 h-5 text-gray-400" />
                  <span className="text-sm text-gray-600">Inscription:</span>
                  <span className="text-sm font-medium text-gray-900">{formatDate(business.created_at)}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="w-5 h-5 text-gray-400" />
                  <span className="text-sm text-gray-600">Dernière mise à jour:</span>
                  <span className="text-sm font-medium text-gray-900">{formatDate(business.updated_at)}</span>
                </div>
                {business.last_activity && (
                  <div className="flex items-center space-x-3">
                    <TrendingUp className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-600">Dernière activité:</span>
                    <span className="text-sm font-medium text-gray-900">{formatDate(business.last_activity)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Description */}
        {business.business_description && (
          <div className="px-6 pb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
            <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">{business.business_description}</p>
          </div>
        )}

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <Button onClick={onClose}>
            Fermer
          </Button>
        </div>
      </div>
    </div>
  );
};

// Modal d'actions en lot
interface BulkBusinessActionModalProps {
  selectedCount: number;
  onClose: () => void;
  onAction: (action: string) => void;
}

const BulkBusinessActionModal: React.FC<BulkBusinessActionModalProps> = ({ selectedCount, onClose, onAction }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Actions en lot ({selectedCount} entreprises)</h3>

        <div className="space-y-2">
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => onAction('verify')}
          >
            <Shield size={16} className="mr-2" />
            Vérifier toutes les entreprises
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => onAction('activate')}
          >
            <CheckCircle size={16} className="mr-2" />
            Activer toutes les entreprises
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => onAction('suspend')}
          >
            <AlertTriangle size={16} className="mr-2" />
            Suspendre toutes les entreprises
          </Button>
        </div>

        <div className="flex justify-end space-x-2 mt-6">
          <Button variant="outline" onClick={onClose}>
            Annuler
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BusinessManagement;
