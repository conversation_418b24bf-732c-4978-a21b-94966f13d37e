.business-rankings-container {
  width: 100%;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

/* États de chargement */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  text-align: center;
}

.loading-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 16px 0 8px 0;
}

.loading-content p {
  color: #6b7280;
  margin-bottom: 24px;
}

.loading-spinner {
  margin-bottom: 16px;
}

.spinning-trophy {
  color: #f59e0b;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.loading-step {
  padding: 8px 16px;
  background-color: #f3f4f6;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #4b5563;
}

/* Styles pour BusinessRankingsContent */
.business-rankings-content {
  width: 100%;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: white;
  font-size: 0.875rem;
  color: #374151;
  min-width: 150px;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.no-top-products {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 24px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px dashed #d1d5db;
}

/* Styles modernes pour les rangs et notes */
.ranking-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.875rem;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  transform: scale(1);
  transition: all 0.3s ease;
  z-index: 10;
}

.ranking-badge.top-3 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 4px 15px rgba(245, 87, 108, 0.5);
  animation: pulse-glow 2s infinite;
}

.ranking-badge:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 4px 15px rgba(245, 87, 108, 0.5);
  }
  50% {
    box-shadow: 0 6px 25px rgba(245, 87, 108, 0.8);
  }
}

.rank-position {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  font-size: 1.25rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rank-trend {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 8px;
  transition: all 0.3s ease;
}

.rank-trend.trend-up {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.rank-trend.trend-down {
  background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.rank-trend.trend-stable {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(100, 116, 139, 0.3);
}

.rating-value {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  font-size: 1.125rem;
  margin-right: 6px;
}

.rating-stars {
  color: #fbbf24;
  font-size: 1rem;
  text-shadow: 0 1px 2px rgba(251, 191, 36, 0.3);
  margin-right: 6px;
}

.rating-count {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

.top-product-rank {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 800;
  font-size: 0.875rem;
  box-shadow: 0 4px 12px rgba(245, 87, 108, 0.4);
  border: 3px solid white;
  z-index: 10;
  animation: bounce-in 0.6s ease-out;
}

@keyframes bounce-in {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.trend-indicator {
  position: absolute;
  top: -8px;
  left: -8px;
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.4);
  border: 2px solid white;
  z-index: 10;
  animation: slide-in 0.5s ease-out;
}

@keyframes slide-in {
  0% {
    transform: translateX(-20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.rankings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.rankings-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.rankings-subtitle {
  font-size: 1rem;
  color: #6b7280;
}

/* Styles modernes pour les cartes de statistiques */
.rankings-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 2rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 4px;
  line-height: 1;
}

.stat-label {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Styles pour les sections d'explication */
.rankings-explanation {
  margin-bottom: 32px;
}

.explanation-card {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.explanation-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.explanation-icon {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  padding: 8px;
  border-radius: 8px;
}

.explanation-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.explanation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.explanation-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.explanation-number {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.explanation-text h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.explanation-text p {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
}

/* Styles pour le guide de lecture */
.ranking-guide {
  margin-bottom: 32px;
  background: linear-gradient(135deg, #fef7cd 0%, #fef3c7 100%);
  border-radius: 16px;
  padding: 24px;
  border: 2px solid #f59e0b;
  box-shadow: 0 4px 20px rgba(245, 158, 11, 0.15);
}

.guide-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #92400e;
  margin: 0 0 20px 0;
  text-align: center;
}

.guide-content {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.guide-example {
  display: grid;
  grid-template-columns: 1fr auto 2fr;
  gap: 24px;
  align-items: center;
}

.example-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  position: relative;
}

.example-product {
  display: flex;
  align-items: center;
  gap: 12px;
}

.example-rank-badge {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  font-weight: 700;
  font-size: 1rem;
  padding: 8px 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.example-product-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.example-product-info p {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.example-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.example-score {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1f2937;
}

.example-reviews {
  font-size: 0.875rem;
  color: #6b7280;
}

.explanation-arrow {
  font-size: 2rem;
  color: #f59e0b;
  font-weight: bold;
  text-align: center;
}

.explanation-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.explanation-point {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #f59e0b;
}

.point-indicator {
  background: #f59e0b;
  color: white;
  font-weight: 700;
  font-size: 0.875rem;
  padding: 4px 8px;
  border-radius: 6px;
  flex-shrink: 0;
  min-width: 32px;
  text-align: center;
}

.point-text strong {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 4px;
}

.point-text p {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0;
  line-height: 1.4;
}

/* Responsive pour le guide */
@media (max-width: 768px) {
  .guide-example {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .explanation-arrow {
    transform: rotate(90deg);
    font-size: 1.5rem;
  }
}

/* Styles pour la légende */
.rankings-legend {
  margin-bottom: 32px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.legend-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-title::before {
  content: '📖';
  font-size: 1.25rem;
}

.legend-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.legend-item:hover {
  background-color: #f8fafc;
}

.legend-sample {
  flex-shrink: 0;
}

.legend-item span {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

/* Styles pour la FAQ */
.rankings-faq {
  margin-bottom: 32px;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-radius: 16px;
  padding: 24px;
  border: 2px solid #3b82f6;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);
}

.faq-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e40af;
  margin: 0 0 20px 0;
  text-align: center;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.faq-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.faq-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.faq-question {
  margin-bottom: 8px;
}

.faq-question strong {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
}

.faq-answer p {
  font-size: 0.8rem;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
}

.faq-answer strong {
  color: #3b82f6;
  font-weight: 600;
}

/* ===== SECTION ANALYTICS ===== */
.analytics-section {
  margin-bottom: 40px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 20px;
  padding: 32px;
  border: 2px solid #0ea5e9;
  box-shadow: 0 8px 32px rgba(14, 165, 233, 0.15);
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.analytics-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.75rem;
  font-weight: 700;
  color: #0c4a6e;
  margin: 0;
}

.analytics-actions {
  display: flex;
  gap: 12px;
}

.analytics-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: white;
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  color: #0ea5e9;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.analytics-btn:hover {
  background: #0ea5e9;
  color: white;
  transform: translateY(-1px);
}

.analytics-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 24px;
}

.analytics-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

/* Graphique d'évolution */
.evolution-chart {
  grid-row: span 2;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  color: #64748b;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.primary {
  background: #3b82f6;
}

.legend-color.secondary {
  background: #10b981;
}

.chart-container {
  height: 300px;
  position: relative;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.chart-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  animation: pulse-point 2s infinite;
}

@keyframes pulse-point {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.chart-axes {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  pointer-events: none;
}

.y-axis {
  position: absolute;
  left: 8px;
  top: 8px;
  bottom: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #64748b;
}

.x-axis {
  position: absolute;
  bottom: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #64748b;
}

/* Analyse concurrentielle */
.competitor-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.competitor-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.competitor-item:hover {
  background: #f1f5f9;
}

.competitor-item.current {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
}

.competitor-rank {
  width: 32px;
  height: 32px;
  background: #e5e7eb;
  color: #4b5563;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
}

.competitor-item.current .competitor-rank {
  background: #f59e0b;
  color: white;
}

.competitor-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.competitor-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.competitor-stats {
  font-size: 0.75rem;
  color: #64748b;
}

.gap-indicator {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  background: #fee2e2;
  color: #dc2626;
}

.gap-indicator.current {
  background: #fef3c7;
  color: #d97706;
}

/* Prédictions IA */
.predictions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.prediction-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  border-left: 4px solid;
}

.prediction-item.positive {
  background: #f0fdf4;
  border-left-color: #22c55e;
}

.prediction-item.neutral {
  background: #fefce8;
  border-left-color: #eab308;
}

.prediction-item.warning {
  background: #fef2f2;
  border-left-color: #ef4444;
}

.prediction-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.prediction-item.positive .prediction-icon {
  background: #dcfce7;
  color: #16a34a;
}

.prediction-item.neutral .prediction-icon {
  background: #fef3c7;
  color: #ca8a04;
}

.prediction-item.warning .prediction-icon {
  background: #fee2e2;
  color: #dc2626;
}

.prediction-content strong {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 4px;
}

.prediction-content p {
  font-size: 0.8rem;
  color: #64748b;
  line-height: 1.4;
  margin: 0;
}

/* Styles pour les tooltips informatifs */
.info-tooltip-trigger {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  cursor: help;
  transition: all 0.2s ease;
}

.info-tooltip-trigger:hover {
  background: rgba(59, 130, 246, 0.2);
  transform: scale(1.1);
}

.info-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.875rem;
  line-height: 1.4;
  white-space: nowrap;
  max-width: 280px;
  white-space: normal;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-bottom: 8px;
  animation: tooltip-appear 0.2s ease-out;
}

.info-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: #1f2937;
}

@keyframes tooltip-appear {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(4px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Amélioration des détails de classement */
.detail-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-label span {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
}

/* Styles pour les sections de titre */
.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title::before {
  content: '🏆';
  font-size: 1.75rem;
}

.top-category-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.top-category-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

/* Amélioration des cartes de produits top */
.top-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.top-product-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
}

.top-product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.top-product-card.trending {
  border: 2px solid #22c55e;
  box-shadow: 0 4px 20px rgba(34, 197, 94, 0.2);
}

.top-product-image {
  width: 100%;
  height: 160px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.top-product-card:hover .top-product-image {
  transform: scale(1.05);
}

.top-product-info {
  padding: 16px;
}

.top-product-info h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.top-product-info p {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.top-product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
}

.top-product-trend {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #22c55e;
  font-weight: 600;
  font-size: 0.875rem;
}

.rankings-filters {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-input {
  width: 100%;
  padding: 10px 10px 10px 40px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.filter-container {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-dropdown, .sort-dropdown {
  position: relative;
  display: inline-block;
}

.filter-button, .sort-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.filter-button:hover, .sort-button:hover {
  background-color: #f3f4f6;
}

.filter-dropdown-content, .sort-dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  min-width: 180px;
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 4px;
}

.filter-dropdown:hover .filter-dropdown-content,
.sort-dropdown:hover .sort-dropdown-content {
  display: block;
}

.filter-option, .sort-option {
  padding: 8px 16px;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
  transition: background-color 0.2s;
}

.filter-option:hover, .sort-option:hover {
  background-color: #f9fafb;
}

.filter-option.selected, .sort-option.selected {
  background-color: #eff6ff;
  color: #3b82f6;
  font-weight: 500;
}

.no-rankings {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: #6b7280;
  text-align: center;
}

.no-rankings svg {
  margin-bottom: 16px;
  color: #d1d5db;
}

.rankings-table-container {
  overflow-x: auto;
}

.rankings-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.rankings-table th,
.rankings-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.rankings-table th {
  font-weight: 600;
  color: #4b5563;
  background-color: #f9fafb;
  position: sticky;
  top: 0;
  z-index: 1;
}

.rankings-table th:first-child {
  border-top-left-radius: 6px;
}

.rankings-table th:last-child {
  border-top-right-radius: 6px;
}

.rankings-table tr:last-child td {
  border-bottom: none;
}

.rankings-table tr:hover {
  background-color: #f9fafb;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
}

.product-name {
  font-weight: 500;
  color: #111827;
}

.rank-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #f3f4f6;
  color: #4b5563;
  font-weight: 600;
  font-size: 0.875rem;
}

.rank-badge.top-3 {
  background-color: #fef3c7;
  color: #d97706;
}

.rank-badge.top-10 {
  background-color: #e0f2fe;
  color: #0284c7;
}

.trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  font-size: 0.875rem;
}

.trend-up {
  color: #10b981;
}

.trend-down {
  color: #ef4444;
}

.trend-stable {
  color: #6b7280;
}

.percentile {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.percentile-top10 {
  background-color: #dcfce7;
  color: #15803d;
}

.percentile-top25 {
  background-color: #e0f2fe;
  color: #0284c7;
}

.percentile-top50 {
  background-color: #fef3c7;
  color: #d97706;
}

.percentile-bottom50 {
  background-color: #fee2e2;
  color: #b91c1c;
}

.rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.rating-value {
  font-weight: 500;
}

.rating-count {
  font-size: 0.75rem;
  color: #6b7280;
}

.info-tooltip {
  position: relative;
  display: inline-block;
  margin-left: 4px;
  cursor: help;
}

.info-tooltip-content {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #1f2937;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 10;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 8px;
}

.info-tooltip-content::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #1f2937 transparent transparent transparent;
}

/* Styles pour la section des produits Top */
.top-products-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.top-category {
  margin-bottom: 24px;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.top-category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.top-category-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.top-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 16px;
}

.top-product-card {
  position: relative;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.top-product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.top-product-rank {
  position: absolute;
  top: 8px;
  left: 8px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 4px 8px;
  border-radius: 4px;
  z-index: 1;
}

.trend-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #10b981;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 4px 8px;
  border-radius: 4px;
  z-index: 1;
}

.top-product-image {
  width: 100%;
  height: 160px;
  object-fit: cover;
}

.top-product-info {
  padding: 12px;
}

.top-product-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.top-product-info p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.top-product-rating, .top-product-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
}

.top-product-rating .stars {
  color: #f59e0b;
}

.top-product-rating .review-count {
  color: #6b7280;
}

.top-product-trend {
  color: #10b981;
}

.top-product-card.trending {
  border: 1px solid #10b981;
}

.no-top-products {
  text-align: center;
  padding: 16px;
  color: #6b7280;
  font-style: italic;
}

/* Styles modernes pour la grille de classements */
.rankings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.ranking-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
}

.ranking-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.ranking-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.ranking-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.ranking-card:hover .ranking-image img {
  transform: scale(1.05);
}

.ranking-content {
  padding: 20px;
}

.ranking-product-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
  line-height: 1.3;
}

.ranking-category {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ranking-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.ranking-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.ranking-detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-value {
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ranking-progress {
  margin-top: 16px;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
}

.progress-bar-container {
  width: 100%;
  height: 8px;
  background-color: #f1f5f9;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.8s ease;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Styles pour les percentiles */
.percentile-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.percentile-top10 {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #15803d;
  box-shadow: 0 2px 4px rgba(21, 128, 61, 0.2);
}

.percentile-top25 {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1d4ed8;
  box-shadow: 0 2px 4px rgba(29, 78, 216, 0.2);
}

.percentile-top50 {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #d97706;
  box-shadow: 0 2px 4px rgba(217, 119, 6, 0.2);
}

.percentile-bottom50 {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #dc2626;
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
}

/* Dropdown d'export */
.export-dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.export-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 100;
  min-width: 280px;
  animation: dropdownSlideIn 0.2s ease;
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.export-option {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: white;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f3f4f6;
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
}

.export-option:last-child {
  border-bottom: none;
  border-radius: 0 0 8px 8px;
}

.export-option:first-child {
  border-radius: 8px 8px 0 0;
}

.export-option:hover {
  background: #f8fafc;
}

.export-option:active {
  background: #f1f5f9;
}

.export-desc {
  font-size: 0.75rem;
  color: #64748b;
  margin-top: 2px;
  font-weight: 400;
}
