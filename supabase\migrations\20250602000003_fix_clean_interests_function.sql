-- Migration pour corriger la fonction clean_interests manquante
-- Cette fonction est nécessaire pour l'inscription des utilisateurs

-- =====================================================
-- 0. NETTOYAGE PRÉALABLE
-- =====================================================

-- Supprimer les triggers existants
DROP TRIGGER IF EXISTS clean_interests_trigger ON profiles;
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;

-- Supprimer les fonctions existantes pour éviter les conflits
DROP FUNCTION IF EXISTS clean_interests(text);
DROP FUNCTION IF EXISTS trigger_clean_interests();
DROP FUNCTION IF EXISTS update_profiles_updated_at();

-- =====================================================
-- 1. CRÉER LA FONCTION CLEAN_INTERESTS
-- =====================================================

-- Fonction pour valider et nettoyer les centres d'intérêt
CREATE FUNCTION clean_interests(input_interests TEXT)
RETURNS TEXT AS $$
BEGIN
  -- Si l'input est NULL ou vide, retourner NULL
  IF input_interests IS NULL OR trim(input_interests) = '' THEN
    RETURN NULL;
  END IF;
  
  -- Nettoyer les espaces en trop et normaliser les virgules
  RETURN trim(regexp_replace(input_interests, '\s*,\s*', ', ', 'g'));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- =====================================================
-- 2. CRÉER LA FONCTION TRIGGER POUR NETTOYER LES INTÉRÊTS
-- =====================================================

-- Fonction trigger pour nettoyer automatiquement les centres d'intérêt
CREATE FUNCTION trigger_clean_interests()
RETURNS TRIGGER AS $$
BEGIN
  NEW.interests = clean_interests(NEW.interests);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. CRÉER LE TRIGGER SUR LA TABLE PROFILES
-- =====================================================

-- Supprimer le trigger existant s'il existe
DROP TRIGGER IF EXISTS clean_interests_trigger ON profiles;

-- Créer le trigger pour nettoyer les intérêts lors des insertions et mises à jour
CREATE TRIGGER clean_interests_trigger
  BEFORE INSERT OR UPDATE OF interests ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION trigger_clean_interests();

-- =====================================================
-- 4. VÉRIFIER ET CORRIGER LA FONCTION HANDLE_NEW_USER
-- =====================================================

-- S'assurer que la fonction handle_new_user existe et fonctionne correctement
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  -- Insérer le profil utilisateur de base
  INSERT INTO public.profiles (
    id, 
    username, 
    email, 
    role,
    created_at,
    updated_at
  )
  VALUES (
    new.id,
    new.raw_user_meta_data->>'username',
    new.email,
    COALESCE(new.raw_user_meta_data->>'role', 'standard'),
    now(),
    now()
  );

  -- Si l'utilisateur est une entreprise, créer le profil business
  IF (new.raw_user_meta_data->>'role' = 'business') THEN
    INSERT INTO public.business_profiles (
      id,
      business_name,
      business_description,
      created_at,
      updated_at
    )
    VALUES (
      new.id,
      new.raw_user_meta_data->>'businessName',
      COALESCE(new.raw_user_meta_data->>'businessDescription', ''),
      now(),
      now()
    );
  END IF;

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 5. VÉRIFIER LE TRIGGER DE CRÉATION D'UTILISATEUR
-- =====================================================

-- Supprimer et recréer le trigger pour s'assurer qu'il fonctionne
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Créer le trigger pour créer le profil après inscription
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW 
  EXECUTE FUNCTION public.handle_new_user();

-- =====================================================
-- 6. FONCTIONS UTILITAIRES POUR LES PROFILS
-- =====================================================

-- Fonction pour mettre à jour le timestamp updated_at
CREATE FUNCTION update_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour updated_at automatiquement
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_profiles_updated_at();

-- =====================================================
-- 7. NETTOYER LES DONNÉES EXISTANTES
-- =====================================================

-- Appliquer la fonction de nettoyage aux intérêts existants
UPDATE profiles 
SET interests = clean_interests(interests) 
WHERE interests IS NOT NULL AND interests != '';

-- =====================================================
-- 8. VÉRIFICATIONS ET TESTS
-- =====================================================

-- Tester la fonction clean_interests
DO $$
DECLARE
    test_result TEXT;
BEGIN
    -- Test avec des espaces en trop
    SELECT clean_interests('  sport  ,   musique,voyage   ,  ') INTO test_result;
    
    IF test_result = 'sport, musique, voyage' THEN
        RAISE NOTICE '✅ Fonction clean_interests fonctionne correctement';
    ELSE
        RAISE NOTICE '❌ Problème avec clean_interests: %', test_result;
    END IF;
    
    -- Test avec NULL
    SELECT clean_interests(NULL) INTO test_result;
    
    IF test_result IS NULL THEN
        RAISE NOTICE '✅ Gestion des valeurs NULL correcte';
    ELSE
        RAISE NOTICE '❌ Problème avec la gestion des NULL';
    END IF;
END $$;

-- Vérifier que les triggers existent
SELECT 
    trigger_name,
    event_object_table,
    action_timing,
    event_manipulation
FROM information_schema.triggers 
WHERE trigger_schema = 'public' 
AND (trigger_name = 'clean_interests_trigger' OR trigger_name = 'on_auth_user_created')
ORDER BY trigger_name;

-- Vérifier que les fonctions existent
SELECT 
    routine_name,
    routine_type,
    data_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('clean_interests', 'trigger_clean_interests', 'handle_new_user')
ORDER BY routine_name;

-- =====================================================
-- 9. PERMISSIONS ET SÉCURITÉ
-- =====================================================

-- S'assurer que les permissions sont correctes
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO anon;

-- Permissions pour les fonctions
GRANT EXECUTE ON FUNCTION clean_interests(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION clean_interests(TEXT) TO anon;

-- =====================================================
-- 10. MESSAGE DE CONFIRMATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CORRECTION DE LA FONCTION CLEAN_INTERESTS TERMINÉE ===';
    RAISE NOTICE '✅ Fonction clean_interests créée et testée';
    RAISE NOTICE '✅ Trigger clean_interests_trigger activé';
    RAISE NOTICE '✅ Fonction handle_new_user corrigée';
    RAISE NOTICE '✅ Trigger on_auth_user_created vérifié';
    RAISE NOTICE '';
    RAISE NOTICE 'L''inscription des utilisateurs devrait maintenant fonctionner correctement.';
    RAISE NOTICE 'Testez en créant un nouveau compte utilisateur.';
    RAISE NOTICE '';
END $$;
