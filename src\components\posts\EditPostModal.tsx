import React, { useState, useEffect } from 'react';
import { X, Image, Star } from 'lucide-react';
import { IPost, PostType, UserRole } from '../../types/index';
import { useAuth } from '../../context/AuthContext';
import { usePosts } from '../../context/PostsContext';
import Button from '../ui/Button';
import Card, { CardBody, CardFooter } from '../ui/Card';

interface EditPostModalProps {
  post: IPost;
  isOpen: boolean;
  onClose: () => void;
}

const EditPostModal: React.FC<EditPostModalProps> = ({ post, isOpen, onClose }) => {
  const { currentUser } = useAuth();
  const { updatePost } = usePosts();

  // États du formulaire
  const [businessName, setBusinessName] = useState(post.businessName);
  const [productName, setProductName] = useState(post.productName);
  const [category, setCategory] = useState(post.category);
  const [description, setDescription] = useState(post.description);
  const [rating, setRating] = useState(post.rating || 0);
  const [postType, setPostType] = useState(post.type);
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>(post.images || []);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Réinitialiser le formulaire quand le post change
  useEffect(() => {
    setBusinessName(post.businessName);
    setProductName(post.productName);
    setCategory(post.category);
    setDescription(post.description);
    setRating(post.rating || 0);
    setPostType(post.type);
    setImageFiles([]);
    setImagePreviews(post.images || []);
  }, [post]);

  if (!isOpen) return null;

  const handleImageFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setImageFiles(files);

    // Créer les aperçus
    const previews = files.map(file => URL.createObjectURL(file));
    setImagePreviews(previews);
  };

  const handleRemoveImage = (index: number) => {
    const newFiles = imageFiles.filter((_, i) => i !== index);
    const newPreviews = imagePreviews.filter((_, i) => i !== index);
    setImageFiles(newFiles);
    setImagePreviews(newPreviews);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validation selon le rôle
      if (currentUser?.role === UserRole.BUSINESS) {
        if (!businessName || !productName || !category || !description) {
          alert('Veuillez remplir tous les champs obligatoires.');
          return;
        }
      } else {
        if (!businessName || !productName || !category || !description || rating === 0) {
          alert('Veuillez remplir tous les champs obligatoires et donner une note.');
          return;
        }
      }

      await updatePost(post.id, {
        businessName,
        productName,
        category,
        description,
        rating: currentUser?.role === UserRole.BUSINESS ? undefined : rating,
        type: postType,
        images: imageFiles.length > 0 ? imageFiles : undefined, // Seulement si de nouvelles images
        userId: currentUser?.id,
        username: currentUser?.username,
        userProfilePicture: currentUser?.profilePicture,
        authorRole: currentUser?.role
      });

      onClose();
    } catch (error) {
      console.error('Erreur lors de la modification:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <Card>
          <div className="p-4 border-b border-gray-200 bg-blue-50">
            <div className="flex justify-between items-center">
              <h3 className="text-xl font-medium text-blue-900">
                Modifier le post
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
              >
                <X size={18} />
              </Button>
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <CardBody className="p-4">
              {/* Type de post (seulement pour les utilisateurs standards) */}
              {currentUser?.role === UserRole.STANDARD && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Type de post *
                  </label>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value={PostType.FAVORITE}
                        checked={postType === PostType.FAVORITE}
                        onChange={(e) => setPostType(e.target.value as PostType)}
                        className="mr-2"
                      />
                      Coup de coeur
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value={PostType.COMPLAINT}
                        checked={postType === PostType.COMPLAINT}
                        onChange={(e) => setPostType(e.target.value as PostType)}
                        className="mr-2"
                      />
                      Coup de gueule
                    </label>
                  </div>
                </div>
              )}

              {/* Nom de l'entreprise */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nom de l'entreprise *
                </label>
                <input
                  type="text"
                  value={businessName}
                  onChange={(e) => setBusinessName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              {/* Nom du produit/service */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Produit ou Service *
                </label>
                <input
                  type="text"
                  value={productName}
                  onChange={(e) => setProductName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              {/* Catégorie */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Catégorie *
                </label>
                <select
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Sélectionnez une catégorie</option>
                  <option value="Technologie">Technologie</option>
                  <option value="Mode">Mode</option>
                  <option value="Alimentation">Alimentation</option>
                  <option value="Services">Services</option>
                  <option value="Santé">Santé</option>
                  <option value="Éducation">Éducation</option>
                  <option value="Autre">Autre</option>
                </select>
              </div>

              {/* Note (seulement pour les utilisateurs standards) */}
              {currentUser?.role === UserRole.STANDARD && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Note *
                  </label>
                  <div className="flex items-center space-x-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        type="button"
                        onClick={() => setRating(star)}
                        className={`p-1 ${star <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
                      >
                        <Star size={20} fill={star <= rating ? 'currentColor' : 'none'} />
                      </button>
                    ))}
                    <span className="ml-2 text-sm text-gray-600">
                      {rating > 0 ? `${rating}/5` : 'Aucune note'}
                    </span>
                  </div>
                </div>
              )}

              {/* Description */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              {/* Images */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Images
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageFileChange}
                    className="hidden"
                    id="edit-image-upload"
                  />
                  <label
                    htmlFor="edit-image-upload"
                    className="cursor-pointer flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    <Image size={18} className="mr-2" />
                    {imageFiles.length > 0 ? 'Changer les images' : 'Garder les images actuelles'}
                  </label>
                </div>

                {/* Aperçus des images */}
                {imagePreviews.length > 0 && (
                  <div className="mt-3 grid grid-cols-3 gap-2">
                    {imagePreviews.map((preview, index) => (
                      <div key={index} className="relative">
                        <img
                          src={preview}
                          alt={`Preview ${index + 1}`}
                          className="w-full h-24 object-cover rounded-md"
                        />
                        {imageFiles.length > 0 && (
                          <button
                            type="button"
                            onClick={() => handleRemoveImage(index)}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                          >
                            <X size={12} />
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardBody>

            <CardFooter className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Annuler
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={isSubmitting}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isSubmitting ? 'Modification...' : 'Modifier le post'}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default EditPostModal;
