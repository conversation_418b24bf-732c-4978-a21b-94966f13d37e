-- Script pour ajouter des données de test pour le tableau de bord business
-- À exécuter dans Supabase SQL Editor si nécessaire

-- 1. Ajouter des produits de test pour l'entreprise Dexima
INSERT INTO products (name, description, price, category, stock, business_id, created_at) VALUES
('iPhone 15 Pro', 'Smartphone Apple dernière génération', 650000, 'Électronique', 25, 'dexima-business-id', NOW() - INTERVAL '30 days'),
('Samsung Galaxy S24', 'Smartphone Samsung haut de gamme', 580000, 'Électronique', 18, 'dexima-business-id', NOW() - INTERVAL '25 days'),
('MacBook Air M3', 'Ordinateur portable Apple', 850000, 'Informatique', 8, 'dexima-business-id', NOW() - INTERVAL '20 days'),
('AirPods Pro', 'Écouteurs sans fil Apple', 180000, 'Audio', 45, 'dexima-business-id', NOW() - INTERVAL '15 days'),
('iPad Air', 'Tablette Apple', 420000, '<PERSON>lectronique', 12, 'dexima-business-id', NOW() - INTERVAL '10 days'),
('Dell XPS 13', 'Ordinateur portable Dell', 720000, 'Informatique', 6, 'dexima-business-id', NOW() - INTERVAL '8 days'),
('Sony WH-1000XM5', 'Casque audio Sony', 220000, 'Audio', 15, 'dexima-business-id', NOW() - INTERVAL '5 days'),
('Nintendo Switch', 'Console de jeu portable', 280000, 'Jeux', 20, 'dexima-business-id', NOW() - INTERVAL '3 days')
ON CONFLICT (name, business_id) DO NOTHING;

-- 2. Ajouter des commandes de test
INSERT INTO orders (product_id, buyer_id, seller_id, quantity, total_price, status, created_at) VALUES
-- Commandes récentes (7 derniers jours)
((SELECT id FROM products WHERE name = 'iPhone 15 Pro' LIMIT 1), 'test-buyer-1', 'dexima-business-id', 2, 1300000, 'completed', NOW() - INTERVAL '1 day'),
((SELECT id FROM products WHERE name = 'AirPods Pro' LIMIT 1), 'test-buyer-2', 'dexima-business-id', 1, 180000, 'completed', NOW() - INTERVAL '2 days'),
((SELECT id FROM products WHERE name = 'MacBook Air M3' LIMIT 1), 'test-buyer-3', 'dexima-business-id', 1, 850000, 'completed', NOW() - INTERVAL '3 days'),
((SELECT id FROM products WHERE name = 'Samsung Galaxy S24' LIMIT 1), 'test-buyer-4', 'dexima-business-id', 1, 580000, 'pending', NOW() - INTERVAL '4 days'),
((SELECT id FROM products WHERE name = 'iPad Air' LIMIT 1), 'test-buyer-5', 'dexima-business-id', 1, 420000, 'completed', NOW() - INTERVAL '5 days'),

-- Commandes moyennes (30 derniers jours)
((SELECT id FROM products WHERE name = 'iPhone 15 Pro' LIMIT 1), 'test-buyer-6', 'dexima-business-id', 1, 650000, 'completed', NOW() - INTERVAL '10 days'),
((SELECT id FROM products WHERE name = 'Sony WH-1000XM5' LIMIT 1), 'test-buyer-7', 'dexima-business-id', 2, 440000, 'completed', NOW() - INTERVAL '12 days'),
((SELECT id FROM products WHERE name = 'Dell XPS 13' LIMIT 1), 'test-buyer-8', 'dexima-business-id', 1, 720000, 'completed', NOW() - INTERVAL '15 days'),
((SELECT id FROM products WHERE name = 'Nintendo Switch' LIMIT 1), 'test-buyer-9', 'dexima-business-id', 1, 280000, 'completed', NOW() - INTERVAL '18 days'),
((SELECT id FROM products WHERE name = 'AirPods Pro' LIMIT 1), 'test-buyer-10', 'dexima-business-id', 3, 540000, 'completed', NOW() - INTERVAL '20 days'),

-- Commandes anciennes (90 derniers jours)
((SELECT id FROM products WHERE name = 'Samsung Galaxy S24' LIMIT 1), 'test-buyer-11', 'dexima-business-id', 2, 1160000, 'completed', NOW() - INTERVAL '35 days'),
((SELECT id FROM products WHERE name = 'MacBook Air M3' LIMIT 1), 'test-buyer-12', 'dexima-business-id', 1, 850000, 'completed', NOW() - INTERVAL '40 days'),
((SELECT id FROM products WHERE name = 'iPad Air' LIMIT 1), 'test-buyer-13', 'dexima-business-id', 2, 840000, 'completed', NOW() - INTERVAL '45 days'),
((SELECT id FROM products WHERE name = 'iPhone 15 Pro' LIMIT 1), 'test-buyer-14', 'dexima-business-id', 1, 650000, 'completed', NOW() - INTERVAL '50 days'),
((SELECT id FROM products WHERE name = 'Sony WH-1000XM5' LIMIT 1), 'test-buyer-15', 'dexima-business-id', 1, 220000, 'completed', NOW() - INTERVAL '60 days'),

-- Commandes très anciennes (1 an)
((SELECT id FROM products WHERE name = 'Dell XPS 13' LIMIT 1), 'test-buyer-16', 'dexima-business-id', 1, 720000, 'completed', NOW() - INTERVAL '120 days'),
((SELECT id FROM products WHERE name = 'Nintendo Switch' LIMIT 1), 'test-buyer-17', 'dexima-business-id', 2, 560000, 'completed', NOW() - INTERVAL '150 days'),
((SELECT id FROM products WHERE name = 'AirPods Pro' LIMIT 1), 'test-buyer-18', 'dexima-business-id', 1, 180000, 'completed', NOW() - INTERVAL '200 days'),
((SELECT id FROM products WHERE name = 'iPhone 15 Pro' LIMIT 1), 'test-buyer-19', 'dexima-business-id', 1, 650000, 'completed', NOW() - INTERVAL '250 days'),
((SELECT id FROM products WHERE name = 'Samsung Galaxy S24' LIMIT 1), 'test-buyer-20', 'dexima-business-id', 1, 580000, 'completed', NOW() - INTERVAL '300 days')
ON CONFLICT DO NOTHING;

-- 3. Ajouter des avis/posts de test mentionnant Dexima
INSERT INTO posts (content, author_id, rating, business_name, created_at) VALUES
('Excellent service chez Dexima ! iPhone 15 Pro livré rapidement et en parfait état. Je recommande vivement !', 'test-user-1', 5, 'Dexima', NOW() - INTERVAL '1 day'),
('Très satisfait de mon achat MacBook Air chez Dexima. Équipe professionnelle et prix compétitifs.', 'test-user-2', 5, 'Dexima', NOW() - INTERVAL '3 days'),
('Bonne expérience avec Dexima pour l\'achat de mes AirPods Pro. Livraison rapide.', 'test-user-3', 4, 'Dexima', NOW() - INTERVAL '5 days'),
('Service client réactif chez Dexima. Petit problème résolu rapidement. Merci !', 'test-user-4', 4, 'Dexima', NOW() - INTERVAL '8 days'),
('Dexima propose de bons produits électroniques. Samsung Galaxy S24 conforme à mes attentes.', 'test-user-5', 4, 'Dexima', NOW() - INTERVAL '12 days'),
('Première commande chez Dexima, plutôt satisfait. iPad Air de qualité.', 'test-user-6', 4, 'Dexima', NOW() - INTERVAL '15 days'),
('Dexima a un bon catalogue de produits tech. Dell XPS 13 parfait pour le travail.', 'test-user-7', 5, 'Dexima', NOW() - INTERVAL '18 days'),
('Expérience correcte avec Dexima. Sony WH-1000XM5 excellent, livraison un peu lente.', 'test-user-8', 3, 'Dexima', NOW() - INTERVAL '22 days'),
('Très content de mon Nintendo Switch acheté chez Dexima. Emballage soigné.', 'test-user-9', 5, 'Dexima', NOW() - INTERVAL '25 days'),
('Dexima reste une référence pour l\'électronique. Service après-vente au top !', 'test-user-10', 5, 'Dexima', NOW() - INTERVAL '30 days')
ON CONFLICT DO NOTHING;

-- 4. Mettre à jour les stocks pour créer des alertes
UPDATE products 
SET stock = 3 
WHERE name = 'Dell XPS 13' AND business_id = 'dexima-business-id';

UPDATE products 
SET stock = 5 
WHERE name = 'iPad Air' AND business_id = 'dexima-business-id';

UPDATE products 
SET stock = 8 
WHERE name = 'MacBook Air M3' AND business_id = 'dexima-business-id';

-- 5. Ajouter une commande en attente depuis plus de 24h pour les alertes
INSERT INTO orders (product_id, buyer_id, seller_id, quantity, total_price, status, created_at) VALUES
((SELECT id FROM products WHERE name = 'iPhone 15 Pro' LIMIT 1), 'test-buyer-pending', 'dexima-business-id', 1, 650000, 'pending', NOW() - INTERVAL '2 days')
ON CONFLICT DO NOTHING;

-- 6. Vérification des données ajoutées
SELECT 
    'Produits ajoutés' as type,
    COUNT(*) as count
FROM products 
WHERE business_id = 'dexima-business-id'

UNION ALL

SELECT 
    'Commandes ajoutées' as type,
    COUNT(*) as count
FROM orders 
WHERE seller_id = 'dexima-business-id'

UNION ALL

SELECT 
    'Avis ajoutés' as type,
    COUNT(*) as count
FROM posts 
WHERE business_name = 'Dexima' AND rating IS NOT NULL

UNION ALL

SELECT 
    'Produits stock faible' as type,
    COUNT(*) as count
FROM products 
WHERE business_id = 'dexima-business-id' AND stock < 10

UNION ALL

SELECT 
    'Commandes en attente' as type,
    COUNT(*) as count
FROM orders 
WHERE seller_id = 'dexima-business-id' 
AND status = 'pending' 
AND created_at < NOW() - INTERVAL '1 day';

-- 7. Calculs de vérification pour le dashboard
SELECT 
    'Métriques Dashboard' as section,
    SUM(total_price) as chiffre_affaires_total,
    COUNT(*) as nombre_commandes,
    AVG(CASE WHEN p.rating IS NOT NULL THEN p.rating END) as note_moyenne,
    COUNT(CASE WHEN p.rating IS NOT NULL THEN 1 END) as nombre_avis
FROM orders o
LEFT JOIN posts p ON p.business_name = 'Dexima'
WHERE o.seller_id = 'dexima-business-id'
AND o.created_at >= NOW() - INTERVAL '30 days';

-- Note: Remplacer 'dexima-business-id' par l'ID réel de l'entreprise Dexima
-- Note: Remplacer les 'test-buyer-X' et 'test-user-X' par des IDs d'utilisateurs réels si disponibles
