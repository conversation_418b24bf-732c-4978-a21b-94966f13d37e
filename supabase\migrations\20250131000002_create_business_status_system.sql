/*
  # Système de Statut d'Entreprise
  
  1. Nouvelles tables
    - `business_status_criteria` - Critères pour chaque niveau de statut
    - `business_status_history` - Historique des changements de statut
    - `business_verification_documents` - Documents de vérification
    
  2. Améliorations
    - Mise à jour de la table `business_profiles` avec nouveaux statuts
    - Ajout de colonnes pour le tracking des métriques
    
  3. Sécurité
    - Activation RLS sur toutes les nouvelles tables
    - Politiques appropriées pour chaque table
*/

-- =====================================================
-- 1. MISE À JOUR DE LA TABLE BUSINESS_PROFILES
-- =====================================================

-- Mettre à jour les statuts d'entreprise pour inclure tous les niveaux
ALTER TABLE business_profiles 
DROP CONSTRAINT IF EXISTS valid_business_status;

ALTER TABLE business_profiles 
ADD CONSTRAINT valid_business_status 
CHECK (business_status IN ('new', 'pending', 'active', 'inactive', 'verified', 'premium', 'featured', 'partner'));

-- Ajouter des colonnes pour le tracking des métriques de statut
ALTER TABLE business_profiles
ADD COLUMN IF NOT EXISTS total_revenue NUMERIC DEFAULT 0 CHECK (total_revenue >= 0),
ADD COLUMN IF NOT EXISTS verification_status TEXT DEFAULT 'unverified' CHECK (verification_status IN ('unverified', 'pending', 'verified', 'rejected')),
ADD COLUMN IF NOT EXISTS verification_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS has_business_document BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS status_updated_at TIMESTAMPTZ DEFAULT now(),
ADD COLUMN IF NOT EXISTS status_updated_by UUID REFERENCES profiles(id),
ADD COLUMN IF NOT EXISTS auto_status_check_enabled BOOLEAN DEFAULT true;

-- =====================================================
-- 2. TABLE DES CRITÈRES DE STATUT D'ENTREPRISE
-- =====================================================

CREATE TABLE IF NOT EXISTS business_status_criteria (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  status_level TEXT NOT NULL CHECK (status_level IN ('new', 'active', 'verified', 'premium', 'featured', 'partner')),
  
  -- Critères numériques
  min_products INTEGER DEFAULT 0,
  min_sales INTEGER DEFAULT 0,
  min_reviews INTEGER DEFAULT 0,
  min_rating DECIMAL(3,2) DEFAULT 0.0,
  min_revenue NUMERIC DEFAULT 0,
  min_account_age_days INTEGER DEFAULT 0,
  
  -- Critères booléens
  profile_complete_required BOOLEAN DEFAULT false,
  verification_required BOOLEAN DEFAULT false,
  business_document_required BOOLEAN DEFAULT false,
  
  -- Exigences spéciales (JSON)
  special_requirements JSONB DEFAULT '[]'::jsonb,
  
  -- Avantages (JSON)
  benefits JSONB DEFAULT '[]'::jsonb,
  
  -- Configuration
  is_active BOOLEAN DEFAULT true,
  auto_upgrade_enabled BOOLEAN DEFAULT true,
  
  -- Métadonnées
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  
  -- Contraintes
  UNIQUE(status_level)
);

-- =====================================================
-- 3. TABLE DE L'HISTORIQUE DES STATUTS
-- =====================================================

CREATE TABLE IF NOT EXISTS business_status_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID NOT NULL REFERENCES business_profiles(id) ON DELETE CASCADE,
  
  -- Changement de statut
  old_status TEXT,
  new_status TEXT NOT NULL,
  change_reason TEXT,
  change_type TEXT DEFAULT 'automatic' CHECK (change_type IN ('automatic', 'manual', 'admin')),
  
  -- Métriques au moment du changement
  metrics_snapshot JSONB DEFAULT '{}'::jsonb,
  
  -- Qui a effectué le changement
  changed_by UUID REFERENCES profiles(id),
  admin_notes TEXT,
  
  -- Métadonnées
  created_at TIMESTAMPTZ DEFAULT now(),
  
  -- Index pour les requêtes fréquentes
  INDEX idx_business_status_history_business_id (business_id),
  INDEX idx_business_status_history_created_at (created_at)
);

-- =====================================================
-- 4. TABLE DES DOCUMENTS DE VÉRIFICATION
-- =====================================================

CREATE TABLE IF NOT EXISTS business_verification_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID NOT NULL REFERENCES business_profiles(id) ON DELETE CASCADE,
  
  -- Type de document
  document_type TEXT NOT NULL CHECK (document_type IN ('business_license', 'tax_certificate', 'bank_statement', 'identity_card', 'other')),
  document_name TEXT NOT NULL,
  
  -- Stockage
  file_url TEXT NOT NULL,
  file_size INTEGER,
  file_type TEXT,
  
  -- Statut de vérification
  verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'approved', 'rejected', 'expired')),
  verified_by UUID REFERENCES profiles(id),
  verified_at TIMESTAMPTZ,
  rejection_reason TEXT,
  
  -- Expiration (pour certains documents)
  expires_at TIMESTAMPTZ,
  
  -- Métadonnées
  uploaded_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  
  -- Index
  INDEX idx_verification_docs_business_id (business_id),
  INDEX idx_verification_docs_status (verification_status)
);

-- =====================================================
-- 5. ACTIVATION DE LA SÉCURITÉ RLS
-- =====================================================

-- Activer RLS sur toutes les nouvelles tables
ALTER TABLE business_status_criteria ENABLE ROW LEVEL SECURITY;
ALTER TABLE business_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE business_verification_documents ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 6. POLITIQUES DE SÉCURITÉ
-- =====================================================

-- Politiques pour business_status_criteria (lecture publique, écriture admin)
CREATE POLICY "Anyone can view business status criteria"
  ON business_status_criteria
  FOR SELECT
  USING (true);

CREATE POLICY "Only admins can modify business status criteria"
  ON business_status_criteria
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Politiques pour business_status_history
CREATE POLICY "Businesses can view their own status history"
  ON business_status_history
  FOR SELECT
  USING (
    business_id IN (
      SELECT id FROM business_profiles 
      WHERE id = auth.uid()
    )
  );

CREATE POLICY "System can insert status history"
  ON business_status_history
  FOR INSERT
  WITH CHECK (true);

-- Politiques pour business_verification_documents
CREATE POLICY "Businesses can manage their own verification documents"
  ON business_verification_documents
  FOR ALL
  USING (
    business_id IN (
      SELECT id FROM business_profiles 
      WHERE id = auth.uid()
    )
  );

CREATE POLICY "Admins can view all verification documents"
  ON business_verification_documents
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- =====================================================
-- 7. INSERTION DES CRITÈRES PAR DÉFAUT
-- =====================================================

INSERT INTO business_status_criteria (status_level, min_products, min_sales, min_reviews, min_rating, min_revenue, min_account_age_days, profile_complete_required, verification_required, business_document_required, special_requirements, benefits) VALUES

-- Statut NEW
('new', 0, 0, 0, 0.0, 0, 0, false, false, false, 
 '[]'::jsonb,
 '["Accès aux fonctionnalités de base", "Création de profil entreprise gratuite", "Possibilité d''ajouter des produits"]'::jsonb),

-- Statut ACTIVE  
('active', 1, 0, 0, 0.0, 0, 7, true, false, false,
 '[]'::jsonb,
 '["Badge entreprise active", "Visibilité dans les recherches", "Accès au marketplace", "Possibilité de recevoir des avis", "Statistiques de base"]'::jsonb),

-- Statut VERIFIED
('verified', 5, 10, 5, 3.5, 0, 30, true, true, true,
 '[]'::jsonb,
 '["Badge vérifié bleu", "Confiance client renforcée", "Priorité dans les résultats de recherche", "Accès aux outils marketing avancés", "Support client prioritaire", "Statistiques détaillées"]'::jsonb),

-- Statut PREMIUM
('premium', 20, 100, 50, 4.0, 500000, 90, true, true, true,
 '[]'::jsonb,
 '["Badge premium doré", "Mise en avant dans les résultats", "Outils d''analyse avancés", "Campagnes publicitaires privilégiées", "Support dédié", "Accès aux événements exclusifs", "Commission réduite sur les ventes"]'::jsonb),

-- Statut FEATURED
('featured', 50, 500, 200, 4.5, 2000000, 180, true, true, true,
 '["Excellence du service client", "Innovation dans les produits", "Contribution à la communauté"]'::jsonb,
 '["Badge featured violet", "Mise en avant sur la page d''accueil", "Partenariats exclusifs", "Accès aux données de marché", "Programme d''ambassadeur", "Événements VIP", "Commission minimale"]'::jsonb),

-- Statut PARTNER
('partner', 100, 2000, 1000, 4.8, 10000000, 365, true, true, true,
 '["Partenariat stratégique signé", "Leadership dans le secteur", "Contribution exceptionnelle à la plateforme", "Respect exemplaire des valeurs"]'::jsonb,
 '["Badge partenaire rouge couronne", "Statut de partenaire officiel", "Co-marketing avec Customeroom", "Accès aux nouvelles fonctionnalités en avant-première", "Participation aux décisions stratégiques", "Revenus de parrainage", "Aucune commission sur les ventes"]'::jsonb);

-- =====================================================
-- 8. FONCTIONS UTILITAIRES
-- =====================================================

-- Fonction pour calculer les métriques d'une entreprise
CREATE OR REPLACE FUNCTION calculate_business_metrics(business_uuid UUID)
RETURNS JSONB AS $$
DECLARE
  result JSONB;
  products_count INTEGER;
  sales_count INTEGER;
  reviews_count INTEGER;
  avg_rating DECIMAL(3,2);
  account_age_days INTEGER;
  total_revenue NUMERIC;
  profile_complete BOOLEAN;
  is_verified BOOLEAN;
  has_document BOOLEAN;
BEGIN
  -- Récupérer les métriques de base
  SELECT 
    bp.sales_count,
    bp.total_revenue,
    bp.verification_status = 'verified',
    bp.has_business_document,
    EXTRACT(DAYS FROM (NOW() - bp.created_at))::INTEGER,
    (bp.business_name IS NOT NULL AND bp.business_description IS NOT NULL AND bp.business_category IS NOT NULL)
  INTO sales_count, total_revenue, is_verified, has_document, account_age_days, profile_complete
  FROM business_profiles bp
  WHERE bp.id = business_uuid;
  
  -- Compter les produits
  SELECT COUNT(*) INTO products_count
  FROM products p
  WHERE p.business_id = business_uuid;
  
  -- Compter les avis et calculer la note moyenne
  SELECT COUNT(*), COALESCE(AVG(rating), 0)
  INTO reviews_count, avg_rating
  FROM posts p
  WHERE p.business_name = (SELECT business_name FROM business_profiles WHERE id = business_uuid);
  
  -- Construire le résultat JSON
  result := jsonb_build_object(
    'profileComplete', profile_complete,
    'productsCount', products_count,
    'salesCount', sales_count,
    'reviewsCount', reviews_count,
    'averageRating', avg_rating,
    'accountAgeDays', account_age_days,
    'isVerified', is_verified,
    'hasBusinessDocument', has_document,
    'totalRevenue', total_revenue
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 9. COMMENTAIRES POUR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE business_status_criteria IS 'Critères requis pour chaque niveau de statut d''entreprise';
COMMENT ON TABLE business_status_history IS 'Historique des changements de statut des entreprises';
COMMENT ON TABLE business_verification_documents IS 'Documents de vérification uploadés par les entreprises';

COMMENT ON COLUMN business_profiles.total_revenue IS 'Chiffre d''affaires total de l''entreprise en F CFA';
COMMENT ON COLUMN business_profiles.verification_status IS 'Statut de vérification de l''entreprise';
COMMENT ON COLUMN business_profiles.has_business_document IS 'Indique si l''entreprise a fourni un document officiel';
COMMENT ON COLUMN business_profiles.auto_status_check_enabled IS 'Active la vérification automatique du statut';
