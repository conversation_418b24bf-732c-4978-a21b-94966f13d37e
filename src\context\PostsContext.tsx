import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { IPost, PostType, IComment, UserRole, UserStatus } from '../types/index';
import { useAuth } from './AuthContext';
import { NotificationType, useNotifications } from './NotificationsContext';
import { auditPostTypes } from '../utils/testPostTypes';
import { processImages } from '../utils/imageUtils';

// Define a type for post creation data that can include File objects for images
export type PostCreationData = Omit<Partial<IPost>, 'images'> & {
  images?: File[]; // Images are now File objects
};

interface PostsContextType {
  posts: IPost[];
  loading: boolean;
  createPost: (postData: PostCreationData) => Promise<void>;
  updatePost: (postId: string, postData: Partial<PostCreationData>) => Promise<void>;
  deletePost: (postId: string) => Promise<void>;
  likePost: (postId: string, userId: string) => Promise<void>;
  toggleRecommendPost: (postId: string, userId: string) => Promise<void>;
  addComment: (postId: string, comment: Partial<IComment>) => Promise<void>;
  sharePost: (postId: string, userId: string, platform: string) => Promise<void>;
  getPostsByUser: (userId: string) => IPost[];
  getPostsByBusiness: (businessName: string) => IPost[];
  refreshPosts: () => Promise<void>;
}

const PostsContext = createContext<PostsContextType>({
  posts: [],
  loading: true,
  createPost: async () => {},
  updatePost: async () => {},
  deletePost: async () => {},
  likePost: async () => {},
  toggleRecommendPost: async () => {},
  addComment: async () => {},
  sharePost: async () => {},
  getPostsByUser: () => [],
  getPostsByBusiness: () => [],
  refreshPosts: async () => {},
});

export const usePosts = () => useContext(PostsContext);

export const PostsProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [posts, setPosts] = useState<IPost[]>([]);
  const [loading, setLoading] = useState(true);
  const auth = useAuth(); // Get auth context
  const { createNotification: createNotificationFromHook } = useNotifications(); // Hook pour les notifications

  const fetchPosts = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('posts_with_author_details')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error("Error fetching posts from view:", error);
        setPosts([]);
        return;
      }

      if (data) {
        // Récupérer les rôles des utilisateurs depuis la table profiles
        const userIds = [...new Set(data.map(post => post.user_id))];
        const { data: profilesData, error: profilesError } = await supabase
          .from('profiles')
          .select('id, role')
          .in('id', userIds);

        if (profilesError) {
          console.error("Error fetching user roles:", profilesError);
        }

        // Créer un map des rôles par user_id
        const userRolesMap = new Map();
        if (profilesData) {
          profilesData.forEach(profile => {
            userRolesMap.set(profile.id, profile.role);
          });
        }

        console.log('🔍 Rôles récupérés:', userRolesMap);

        const fetchedPosts: IPost[] = data.map((post: any) => {
          // Map database values to TypeScript enum
          let mappedType: PostType;
          if (post.type === 'coup_de_coeur') {
            mappedType = PostType.FAVORITE;
          } else if (post.type === 'coup_de_gueule') {
            mappedType = PostType.COMPLAINT;
          } else if (post.type === 'demande_avis') {
            mappedType = PostType.REVIEW;
          } else {
            console.warn('Unknown post type from DB:', post.type);
            mappedType = PostType.FAVORITE; // fallback
          }

          // Validation et nettoyage des données
          const businessName = post.business_name?.trim() || 'Entreprise non spécifiée';
          const productName = post.product_name?.trim() || 'Produit non spécifié';
          const category = post.category?.trim() || 'Non spécifiée';

          // Debug log pour tracer les problèmes de données
          if (process.env.NODE_ENV === 'development') {
            if (!post.business_name?.trim()) {
              console.warn('Post sans nom d\'entreprise:', post.id, post);
            }

            // Debug spécifique pour les rôles
            const retrievedRole = userRolesMap.get(post.user_id);
            if (!retrievedRole) {
              console.warn('🚨 Rôle non trouvé pour l\'utilisateur:', {
                postId: post.id,
                userId: post.user_id,
                authorUsername: post.author_username,
                businessName: post.business_name,
                userRolesMapSize: userRolesMap.size
              });
            } else {
              console.log('✅ Rôle récupéré:', {
                postId: post.id,
                userId: post.user_id,
                authorUsername: post.author_username,
                retrievedRole: retrievedRole
              });
            }
          }

          // Utiliser la fonction robuste pour traiter les images
          const imageArray = processImages(post.images);

          return {
          id: post.id,
          title: post.title,
          type: mappedType,
          businessName: businessName,
          productName: productName,
          category: category,
          description: post.description,
          images: imageArray,
          rating: post.rating,
          tags: post.tags || [],
          userId: post.user_id,
          username: post.author_username || 'Unknown User',
          userProfilePicture: post.author_profile_picture,
          authorStatus: post.author_status as UserStatus || UserStatus.NEWBIE,
          authorRole: userRolesMap.get(post.user_id) as UserRole || UserRole.STANDARD, // Récupérer le rôle depuis la table profiles
          businessStatus: post.mentioned_business_status as BusinessStatus || undefined, // Nouveau: statut de l'entreprise mentionnée

          // Debug spécifique pour Dexima
          ...(process.env.NODE_ENV === 'development' && (post.author_username === 'dexima' || post.business_name?.toLowerCase().includes('dexima')) &&
            console.log('🔍 DEBUG DEXIMA:', {
              postId: post.id,
              authorUsername: post.author_username,
              businessName: post.business_name,
              authorRole: post.author_role,
              userId: post.user_id,
              mentionedBusinessStatus: post.mentioned_business_status,
              rawPost: post
            })
          ),
          likes: post.likes || [],
          recommendations: post.recommendations || [],
          shares: post.shares || [],
          comments: Array.isArray(post.comments) ? post.comments.map((comment: any) => ({
            id: comment.id,
            userId: comment.userId,
            username: comment.username,
            profilePicture: comment.profilePicture,
            content: comment.content,
            hasUsedProduct: comment.hasUsedProduct,
            rating: comment.rating,
            createdAt: new Date(comment.createdAt),
            authorRole: comment.authorRole,
            businessName: comment.businessName,
          })) : [],
          createdAt: new Date(post.created_at),
        };
        });
        setPosts(fetchedPosts);

        // Audit des types de posts en mode développement
        if (process.env.NODE_ENV === 'development') {
          auditPostTypes(fetchedPosts);
        }
      } else {
        setPosts([]);
      }
    } catch (err) {
      console.error("Error in fetchPosts function:", err);
      setPosts([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPosts();

    // Importer dynamiquement le contexte des notifications pour éviter les dépendances circulaires
    /* const importNotificationsContext = async () => {
      try {
        const { useNotifications } = await import('./NotificationsContext');
        setNotificationsContext({ useNotifications });
      } catch (err) {
        console.error("Erreur lors de l'importation du contexte des notifications:", err);
      }
    };

    importNotificationsContext(); */
  }, []);

  // Effect to update authorStatus on posts when currentUser's status changes
  useEffect(() => {
    if (auth.currentUser && auth.currentUser.status && posts.length > 0) {
      setPosts(prevPosts =>
        prevPosts.map(post =>
          post.userId === auth.currentUser!.id && post.authorStatus !== auth.currentUser!.status
            ? { ...post, authorStatus: auth.currentUser!.status }
            : post
        )
      );
    }
  }, [auth.currentUser?.status, posts.length]); // Rerun if status or initial posts load changes

  const uploadPostImages = async (userId: string, files: File[]): Promise<string[]> => {
    console.log(`📤 Début upload de ${files.length} images pour l'utilisateur ${userId}`);
    const uploadedImageUrls: string[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`📷 Upload image ${i + 1}/${files.length}:`, {
        name: file.name,
        size: file.size,
        type: file.type
      });

      // Vérifier que le fichier est défini avant d'accéder à ses propriétés
      if (!file) {
        console.error('Error: Undefined file object in uploadPostImages');
        continue;
      }

      const fileExt = file.name.split('.').pop();
      const fileName = `posts/${userId}/${Date.now()}_${i}.${fileExt}`;
      console.log(`📁 Nom de fichier généré: ${fileName}`);

      // Utiliser le bucket correct 'post-images'
      const { error: uploadError } = await supabase.storage
        .from('post-images')
        .upload(fileName, file);

      if (uploadError) {
        console.error('❌ Erreur upload image:', JSON.stringify(uploadError, null, 2));
        continue;
      }

      // Utiliser le bucket correct pour l'URL publique
      const { data: { publicUrl } } = supabase.storage.from('post-images').getPublicUrl(fileName);
      console.log(`✅ Image uploadée avec succès: ${publicUrl}`);

      if (publicUrl) {
        uploadedImageUrls.push(publicUrl);
      }
    }

    console.log(`🎉 Upload terminé. ${uploadedImageUrls.length} images uploadées avec succès:`, uploadedImageUrls);
    return uploadedImageUrls;
  };

  const createPost = async (postData: PostCreationData) => {
    setLoading(true);
    try {
      // Validation des rôles et types de posts
      if (auth.currentUser) {
        const userRole = auth.currentUser.role;
        const postType = postData.type;

        // Entreprises : peuvent uniquement créer des "Demande d'avis"
        if (userRole === UserRole.BUSINESS && postType !== PostType.REVIEW) {
          alert('Les entreprises peuvent uniquement créer des demandes d\'avis.');
          setLoading(false);
          return;
        }

        // Utilisateurs standards : peuvent uniquement créer des "Coup de coeur" et "Coup de gueule"
        if (userRole === UserRole.STANDARD && postType === PostType.REVIEW) {
          alert('Les utilisateurs standards ne peuvent pas créer de demandes d\'avis.');
          setLoading(false);
          return;
        }

        // Log de validation en mode développement
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ Validation rôle/type de post:', {
            userRole,
            postType,
            username: auth.currentUser.username,
            isValid: true
          });
        }
      }
      let imageUrls: string[] = [];

      // Debug des images reçues
      if (process.env.NODE_ENV === 'development') {
        console.log('🖼️ Images reçues dans createPost:', {
          hasImages: !!postData.images,
          imagesLength: postData.images?.length || 0,
          imagesType: typeof postData.images,
          firstImageType: postData.images?.[0] ? typeof postData.images[0] : 'N/A',
          isFile: postData.images?.[0] instanceof File,
          userId: postData.userId
        });
      }

      if (postData.images && postData.images.length > 0 && postData.userId) {
        if (postData.images[0] instanceof File) {
          console.log('📤 Upload des images en cours...');
          imageUrls = await uploadPostImages(postData.userId, postData.images as File[]);
          console.log('✅ Images uploadées:', imageUrls);
        } else {
          imageUrls = (postData.images as unknown as string[]);
          console.log('🔗 Images déjà en URL:', imageUrls);
        }
      } else {
        console.log('⚠️ Aucune image à traiter');
      }
      // --- PATCH: Map post type to DB value ---
      let dbType: string = 'coup_de_coeur';
      if (postData.type === PostType.FAVORITE) dbType = 'coup_de_coeur';
      else if (postData.type === PostType.COMPLAINT) dbType = 'coup_de_gueule';
      else if (postData.type === PostType.REVIEW) dbType = 'demande_avis'; // Fixed: use correct DB value
      // --- END PATCH ---
      // --- PATCH: Ensure valid rating based on post type ---
      let safeRating: number | undefined = postData.rating;
      if (dbType === 'coup_de_coeur' || dbType === 'coup_de_gueule') {
        // Pour les coups de coeur/gueule, rating obligatoire
        if (typeof safeRating !== 'number' || safeRating < 1 || safeRating > 5) {
          safeRating = 1;
        }
      } else if (dbType === 'demande_avis') {
        // Pour les demandes d'avis, pas de rating (les utilisateurs donneront leur avis)
        safeRating = undefined;
      }
      // --- END PATCH ---
      // --- PATCH: Stringify images and tags for JSONB columns ---
      const imagesJson = JSON.stringify(imageUrls || []);
      const tagsJson = JSON.stringify(postData.tags || []);
      // --- END PATCH ---
      // --- PATCH: Validate required fields ---
      if (!postData.userId || !postData.businessName || !postData.productName || !postData.category || !postData.description) {
        alert('Tous les champs obligatoires doivent être remplis.');
        setLoading(false);
        return;
      }
      // --- END PATCH ---
      const postToInsert: any = {
        user_id: postData.userId,
        type: dbType,
        business_name: postData.businessName,
        product_name: postData.productName,
        category: postData.category,
        description: postData.description,
        images: imagesJson,
        rating: safeRating,
        tags: tagsJson,
        recommendations: [],
      };
      Object.keys(postToInsert).forEach(key => {
        if (postToInsert[key] === undefined) delete postToInsert[key];
      });
      // --- PATCH: Log payload before insert ---
      console.log('Payload for Supabase insert:', postToInsert);
      // --- END PATCH ---
      // Insert into 'posts' table, then select from the view to get joined data
      const { data: insertedPostData, error: insertError } = await supabase
        .from('posts')
        .insert([postToInsert])
        .select('id')
        .single();
      if (insertError) {
        console.error('Error inserting post into Supabase:', JSON.stringify(insertError, null, 2));
        alert(`Failed to create post: ${insertError.message}`);
        throw insertError;
      }
      if (!insertedPostData || !insertedPostData.id) {
        throw new Error('No ID returned after post insertion.');
      }

      // Fetch the newly created post from the view to get all details including author info
      const { data: newPostFromView, error: fetchError } = await supabase
        .from('posts_with_author_details')
        .select('*')
        .eq('id', insertedPostData.id)
        .single();

      // Récupérer le rôle de l'utilisateur depuis la table profiles
      const { data: userProfile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', postData.userId)
        .single();

      if (profileError) {
        console.error("Error fetching user role for new post:", profileError);
      } else {
        console.log('🔍 Rôle récupéré pour le nouveau post:', {
          userId: postData.userId,
          username: postData.username,
          role: userProfile?.role
        });
      }

      if (fetchError || !newPostFromView) {
        console.error('Error fetching newly created post from view:', fetchError);
        // Add user feedback for view fetch errors
        alert(`Post created but failed to load details: ${fetchError?.message || 'Unknown error'}`);

        // Fallback: construct with available data, author info might be missing/stale
        const fallbackCreatedPost: IPost = {
          id: insertedPostData.id,
          title: postData.title,
          type: postToInsert.type,
          businessName: postToInsert.business_name,
          productName: postToInsert.product_name,
          category: postToInsert.category,
          description: postToInsert.description,
          images: imageUrls,
          rating: postToInsert.rating,
          tags: postToInsert.tags || [],
          userId: postToInsert.user_id,
          username: postData.username || 'Anonyme',
          userProfilePicture: postData.userProfilePicture,
          authorRole: postData.authorRole || UserRole.STANDARD, // Added role for fallback
          businessStatus: undefined, // Pas de statut business disponible dans le fallback
          likes: [],
          recommendations: [],
          comments: [],
          createdAt: new Date(), // This will be slightly off from DB, but okay for optimistic
        };
        setPosts(prev => [fallbackCreatedPost, ...prev]);
        return; // Don't throw error after handling with fallback
      }

      // Map database type back to TypeScript enum
      let mappedCreatedType: PostType;
      if (newPostFromView.type === 'coup_de_coeur') {
        mappedCreatedType = PostType.FAVORITE;
      } else if (newPostFromView.type === 'coup_de_gueule') {
        mappedCreatedType = PostType.COMPLAINT;
      } else if (newPostFromView.type === 'demande_avis') {
        mappedCreatedType = PostType.REVIEW;
      } else {
        console.warn('Unknown post type from DB in created post:', newPostFromView.type);
        mappedCreatedType = PostType.FAVORITE; // fallback
      }

      // Validation et nettoyage des données pour le post créé
      const createdBusinessName = newPostFromView.business_name?.trim() || 'Entreprise non spécifiée';
      const createdProductName = newPostFromView.product_name?.trim() || 'Produit non spécifié';
      const createdCategory = newPostFromView.category?.trim() || 'Non spécifiée';

      // Utiliser la fonction robuste pour traiter les images du post créé
      const createdImageArray = processImages(newPostFromView.images);

      const createdPost: IPost = {
        id: newPostFromView.id,
        title: newPostFromView.title,
        type: mappedCreatedType,
        businessName: createdBusinessName,
        productName: createdProductName,
        category: createdCategory,
        description: newPostFromView.description,
        images: createdImageArray,
        rating: newPostFromView.rating,
        tags: newPostFromView.tags || [],
        userId: newPostFromView.user_id,
        username: newPostFromView.author_username || 'Anonyme',
        userProfilePicture: newPostFromView.author_profile_picture,
        authorStatus: newPostFromView.author_status as UserStatus || UserStatus.NEWBIE, // Added mapping
        authorRole: userProfile?.role as UserRole || UserRole.STANDARD, // Utiliser le rôle récupéré depuis profiles
        businessStatus: newPostFromView.mentioned_business_status as BusinessStatus || undefined, // Nouveau: statut de l'entreprise mentionnée
        likes: newPostFromView.likes || [],
        recommendations: newPostFromView.recommendations || [],
        comments: newPostFromView.comments || [],
        createdAt: new Date(newPostFromView.created_at),
      };
      setPosts(prev => [createdPost, ...prev]);

      // After successfully creating post and updating local state, update user stats
      if (auth.currentUser) { // Ensure currentUser is available
        auth.updateUserStatsAndStatus('new_post');
        // Déclencher la vérification automatique du statut
        auth.checkAndUpdateUserStatus('post_created');
      }

      // Détecter les mentions d'entreprises et envoyer des notifications
      if (notificationsContext && postData.businessName) {
        try {
          // Rechercher l'entreprise mentionnée dans la base de données
          console.log(`🔍 Recherche de l'entreprise lors de la création: ${postData.businessName}`);

          const { data: businessData, error: businessError } = await supabase
            .from('profiles')  // ✅ Correction: utiliser 'profiles' au lieu de 'users'
            .select('id, username, role')
            .eq('role', UserRole.BUSINESS)
            .ilike('username', postData.businessName)  // ✅ Utiliser ilike pour ignorer la casse
            .single();

          console.log(`🔍 Résultat recherche entreprise lors création:`, { businessData, businessError });

          if (businessError || !businessData) {
            console.error("Erreur lors de la recherche de l'entreprise mentionnée:", businessError);
          } else {


            // Déterminer le type de notification en fonction du type de post
            const notificationType = postData.type === PostType.REVIEW ?
              NotificationType.REVIEW : NotificationType.MENTION;

            // Créer un message approprié selon le type de notification
            let message = '';
            if (notificationType === NotificationType.REVIEW) {
              const ratingText = postData.rating ? ` avec une note de ${postData.rating}/5` : '';
              message = `${postData.username || 'Un utilisateur'} a publié un avis sur ${postData.productName || 'votre produit'}${ratingText} : "${postData.description?.substring(0, 50)}${postData.description && postData.description.length > 50 ? '...' : ''}"`;
            } else {
              message = `${postData.username || 'Un utilisateur'} a mentionné votre entreprise dans un post : "${postData.description?.substring(0, 50)}${postData.description && postData.description.length > 50 ? '...' : ''}"`;
            }

            // Créer une notification pour l'entreprise mentionnée
            await createNotificationFromHook({
              userId: businessData.id,
              senderId: postData.userId,
              senderName: postData.username || 'Utilisateur',
              senderProfilePicture: postData.userProfilePicture,
              type: notificationType,
              message,
              relatedItemId: createdPost.id,
              relatedItemType: 'post'
            });
          }
        } catch (err) {
          console.error("Erreur lors de l'envoi de la notification:", err);
        }
      }
    } catch (error) {
      console.error("Error creating post:", error);
    } finally {
      setLoading(false);
    }
  };

  const updatePost = async (postId: string, postData: Partial<PostCreationData>) => {
    setLoading(true);
    try {
      // Vérifier que l'utilisateur est l'auteur du post
      const postToUpdate = posts.find(p => p.id === postId);
      if (!postToUpdate) {
        alert('Post non trouvé.');
        setLoading(false);
        return;
      }

      if (postToUpdate.userId !== auth.currentUser?.id) {
        alert('Vous ne pouvez modifier que vos propres posts.');
        setLoading(false);
        return;
      }

      // Gérer l'upload des nouvelles images si nécessaire
      let imageUrls: string[] = postToUpdate.images; // Garder les images existantes par défaut

      if (postData.images && postData.images.length > 0) {
        if (postData.images[0] instanceof File) {
          console.log('📤 Upload des nouvelles images...');
          imageUrls = await uploadPostImages(postData.userId || postToUpdate.userId, postData.images as File[]);
          console.log('✅ Nouvelles images uploadées:', imageUrls);
        } else {
          imageUrls = (postData.images as unknown as string[]);
        }
      }

      // Mapper le type de post vers la valeur DB
      let dbType: string = postToUpdate.type;
      if (postData.type === PostType.FAVORITE) dbType = 'coup_de_coeur';
      else if (postData.type === PostType.COMPLAINT) dbType = 'coup_de_gueule';
      else if (postData.type === PostType.REVIEW) dbType = 'demande_avis';

      // Préparer les données pour la mise à jour
      const updateData: any = {};
      if (postData.businessName !== undefined) updateData.business_name = postData.businessName;
      if (postData.productName !== undefined) updateData.product_name = postData.productName;
      if (postData.category !== undefined) updateData.category = postData.category;
      if (postData.description !== undefined) updateData.description = postData.description;
      if (postData.rating !== undefined) updateData.rating = postData.rating;
      if (postData.type !== undefined) updateData.type = dbType;
      if (imageUrls !== postToUpdate.images) updateData.images = JSON.stringify(imageUrls);
      if (postData.tags !== undefined) updateData.tags = JSON.stringify(postData.tags || []);

      updateData.updated_at = new Date().toISOString();

      console.log('📝 Mise à jour du post:', updateData);

      // Mettre à jour dans la base de données
      const { error: updateError } = await supabase
        .from('posts')
        .update(updateData)
        .eq('id', postId);

      if (updateError) {
        console.error('Erreur lors de la mise à jour du post:', updateError);
        alert(`Erreur lors de la mise à jour: ${updateError.message}`);
        throw updateError;
      }

      // Mettre à jour le post localement
      setPosts(prevPosts => prevPosts.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            businessName: postData.businessName || post.businessName,
            productName: postData.productName || post.productName,
            category: postData.category || post.category,
            description: postData.description || post.description,
            rating: postData.rating !== undefined ? postData.rating : post.rating,
            type: postData.type || post.type,
            images: imageUrls,
            tags: postData.tags || post.tags,
          };
        }
        return post;
      }));

      console.log('✅ Post mis à jour avec succès');
    } catch (error) {
      console.error("Erreur lors de la mise à jour du post:", error);
    } finally {
      setLoading(false);
    }
  };

  const deletePost = async (postId: string) => {
    setLoading(true);
    try {
      // Vérifier que l'utilisateur est l'auteur du post
      const postToDelete = posts.find(p => p.id === postId);
      if (!postToDelete) {
        alert('Post non trouvé.');
        setLoading(false);
        return;
      }

      if (postToDelete.userId !== auth.currentUser?.id) {
        alert('Vous ne pouvez supprimer que vos propres posts.');
        setLoading(false);
        return;
      }

      // Confirmer la suppression
      if (!window.confirm('Êtes-vous sûr de vouloir supprimer ce post ? Cette action est irréversible.')) {
        setLoading(false);
        return;
      }

      console.log('🗑️ Suppression du post:', postId);

      // Supprimer de la base de données
      const { error: deleteError } = await supabase
        .from('posts')
        .delete()
        .eq('id', postId);

      if (deleteError) {
        console.error('Erreur lors de la suppression du post:', deleteError);
        alert(`Erreur lors de la suppression: ${deleteError.message}`);
        throw deleteError;
      }

      // Supprimer localement
      setPosts(prevPosts => prevPosts.filter(post => post.id !== postId));

      console.log('✅ Post supprimé avec succès');
    } catch (error) {
      console.error("Erreur lors de la suppression du post:", error);
    } finally {
      setLoading(false);
    }
  };

  const likePost = async (postId: string, userId: string) => {
    const postToUpdate = posts.find(p => p.id === postId);
    if (!postToUpdate) return;
    const currentLikes = postToUpdate.likes || [];
    const updatedLikes = currentLikes.includes(userId)
      ? currentLikes.filter(id => id !== userId)
      : [...currentLikes, userId];
    setPosts(prevPosts => prevPosts.map(p => p.id === postId ? { ...p, likes: updatedLikes } : p));
    try {
      const { error } = await supabase.from('posts').update({ likes: updatedLikes }).eq('id', postId);
      if (error) throw error;

      // Si un like a été ajouté (pas retiré), déclencher la vérification du statut pour l'auteur du post
      if (updatedLikes.length > currentLikes.length && postToUpdate.userId !== userId) {
        // Déclencher la vérification automatique du statut pour l'auteur du post qui a reçu le like
        if (auth.currentUser && postToUpdate.userId) {
          // Note: Ici on pourrait créer une fonction séparée pour vérifier le statut d'un autre utilisateur
          // Pour l'instant, on se contente de vérifier si c'est l'utilisateur actuel
          if (postToUpdate.userId === auth.currentUser.id) {
            auth.checkAndUpdateUserStatus('like_received');
          }
        }

        // 👍 NOTIFICATION POUR L'AUTEUR DU POST (LIKE)
        try {
          // Récupérer les infos de l'utilisateur qui like
          const { data: likerProfile, error: likerError } = await supabase
            .from('profiles')
            .select('username, profile_picture')
            .eq('id', userId)
            .single();

          if (!likerError && likerProfile) {
            await createNotificationFromHook({
              userId: postToUpdate.userId, // Auteur du post
              senderId: userId, // Utilisateur qui like
              senderName: likerProfile.username,
              senderProfilePicture: likerProfile.profile_picture,
              type: NotificationType.LIKE,
              message: `${likerProfile.username} a aimé votre post`,
              relatedItemId: postId,
              relatedItemType: 'post'
            });

            console.log(`👍 Notification de like envoyée à l'auteur du post: ${postToUpdate.username}`);
          }
        } catch (notifError) {
          console.error("Erreur lors de l'envoi de la notification de like:", notifError);
        }
      }
    } catch (err) {
      console.error("Error updating likes:", err);
      setPosts(prevPosts => prevPosts.map(p => p.id === postId ? { ...p, likes: currentLikes } : p));
    }
  };

  const toggleRecommendPost = async (postId: string, userId: string) => {
    const postToUpdate = posts.find(p => p.id === postId);
    if (!postToUpdate) return;
    const currentRecommendations = postToUpdate.recommendations || [];
    const isRecommending = !currentRecommendations.includes(userId);
    const updatedRecommendations = currentRecommendations.includes(userId)
      ? currentRecommendations.filter(id => id !== userId)
      : [...currentRecommendations, userId];
    setPosts(prevPosts => prevPosts.map(p => p.id === postId ? { ...p, recommendations: updatedRecommendations } : p));

    try {
      const { error } = await supabase.from('posts').update({ recommendations: updatedRecommendations }).eq('id', postId);
      if (error) throw error;

      // 🎯 NOTIFICATION POUR L'AUTEUR DU POST (RECOMMANDATION)
      if (isRecommending && postToUpdate.userId !== userId) {
        try {
          // Récupérer les infos de l'utilisateur qui recommande
          const { data: recommenderProfile, error: recommenderError } = await supabase
            .from('profiles')
            .select('username, profile_picture')
            .eq('id', userId)
            .single();

          if (!recommenderError && recommenderProfile) {
            await createNotificationFromHook({
              userId: postToUpdate.userId, // Auteur du post
              senderId: userId, // Utilisateur qui recommande
              senderName: recommenderProfile.username,
              senderProfilePicture: recommenderProfile.profile_picture,
              type: NotificationType.RECOMMENDATION,
              message: `${recommenderProfile.username} a recommandé votre post`,
              relatedItemId: postId,
              relatedItemType: 'post'
            });

            console.log(`🎯 Notification de recommandation envoyée à l'auteur du post: ${postToUpdate.username}`);
          }
        } catch (notifError) {
          console.error("Erreur lors de l'envoi de la notification de recommandation:", notifError);
        }
      }
    } catch (err) {
      console.error("Error updating recommendations:", err);
      setPosts(prevPosts => prevPosts.map(p => p.id === postId ? { ...p, recommendations: currentRecommendations } : p));
    }
  };

  const addComment = async (postId: string, comment: Partial<IComment>) => {
    console.log('Début de la fonction addComment avec postId:', postId, 'et comment:', comment);

    const postToUpdate = posts.find(p => p.id === postId);
    if (!postToUpdate) {
      console.error('Post non trouvé avec ID:', postId);
      return;
    }

    const commentToAdd: IComment = {
      id: `comment-${Date.now()}`, // Temporary ID, will be replaced by DB
      userId: comment.userId || '',
      username: comment.username || '',
      profilePicture: comment.profilePicture || '',
      content: comment.content || '',
      hasUsedProduct: comment.hasUsedProduct || false,
      rating: comment.rating,
      createdAt: new Date(),
      authorRole: comment.authorRole,
      businessName: comment.businessName,
    };

    console.log('Commentaire préparé pour ajout:', commentToAdd);

    // Optimistic update
    const updatedComments = [...(postToUpdate.comments || []), commentToAdd];
    setPosts(prevPosts => prevPosts.map(p => p.id === postId ? { ...p, comments: updatedComments } : p));
    console.log('Mise à jour optimiste effectuée');

    try {
      // Prepare the comment for DB insertion
      const commentForDB = {
        post_id: postId,
        user_id: commentToAdd.userId,
        content: commentToAdd.content,
        has_used_product: commentToAdd.hasUsedProduct,
        rating: commentToAdd.rating,
        author_role: commentToAdd.authorRole || 'standard',
        business_name: commentToAdd.businessName || null,
      };

      console.log('Commentaire préparé pour la BD:', commentForDB);

      // Insert the comment
      console.log('Tentative d\'insertion dans la table comments...');
      const { data, error } = await supabase
        .from('comments')
        .insert([commentForDB])
        .select('id, created_at');

      console.log('Réponse de Supabase:', { data, error });

      if (error) {
        console.error('Erreur Supabase lors de l\'insertion du commentaire:', error);
        throw error;
      }

      if (data && data[0]) {
        console.log('Commentaire inséré avec succès, ID:', data[0].id);

        // Update our local state with the real DB ID and timestamp
        const realComment = { ...commentToAdd, id: data[0].id, createdAt: new Date(data[0].created_at) };
        setPosts(prevPosts => prevPosts.map(p => {
          if (p.id === postId) {
            const updatedComments = p.comments.map(c =>
              c.id === commentToAdd.id ? realComment : c
            );
            return { ...p, comments: updatedComments };
          }
          return p;
        }));
        console.log('État local mis à jour avec l\'ID réel du commentaire');

        // Mettre également à jour la table posts pour refléter le nouveau commentaire
        try {
          console.log('Mise à jour du compteur de commentaires dans la table posts...');
          const { error: updateError } = await supabase
            .rpc('increment_comment_count', { post_id: postId });

          if (updateError) {
            console.error('Erreur lors de la mise à jour du compteur de commentaires:', updateError);
            // Temporairement ignorer l'erreur si la fonction n'existe pas encore
            if (updateError.code === 'PGRST202') {
              console.warn('⚠️ Fonction increment_comment_count non trouvée - migration en attente');
            }
          } else {
            console.log('Compteur de commentaires mis à jour avec succès');
          }
        } catch (updateErr) {
          console.error('Exception lors de la mise à jour du compteur de commentaires:', updateErr);
        }

        // Update user stats for commenting
        if (auth.currentUser) {
          console.log('Mise à jour des statistiques utilisateur...');
          auth.updateUserStatsAndStatus('new_comment');
          // Déclencher la vérification automatique du statut
          auth.checkAndUpdateUserStatus('comment_created');
        }

        // Envoyer des notifications
        try {

          // 1. NOTIFICATION POUR L'AUTEUR DU POST (Soro Chantal)
          // Ne pas notifier si l'auteur commente son propre post
          if (postToUpdate.userId !== commentToAdd.userId) {
            const authorNotificationMessage = commentToAdd.hasUsedProduct && commentToAdd.rating
              ? `${commentToAdd.username} a laissé un avis avec une note de ${commentToAdd.rating}/5 sur votre post : "${commentToAdd.content.substring(0, 50)}${commentToAdd.content.length > 50 ? '...' : ''}"`
              : `${commentToAdd.username} a commenté votre post : "${commentToAdd.content.substring(0, 50)}${commentToAdd.content.length > 50 ? '...' : ''}"`;

            await createNotificationFromHook({
              userId: postToUpdate.userId, // ID de l'auteur du post (Soro Chantal)
              senderId: commentToAdd.userId, // ID du commentateur (Kouassi Ange)
              senderName: commentToAdd.username,
              senderProfilePicture: commentToAdd.profilePicture,
              type: NotificationType.COMMENT,
              message: authorNotificationMessage,
              relatedItemId: postId,
              relatedItemType: 'post'
            });

            console.log(`📢 Notification envoyée à l'auteur du post: ${postToUpdate.username}`);
          }

          // 2. NOTIFICATION POUR L'ENTREPRISE MENTIONNÉE (si différente de l'auteur)
          if (postToUpdate.businessName) {
            // Rechercher l'entreprise mentionnée dans la base de données
            console.log(`🔍 Recherche de l'entreprise: ${postToUpdate.businessName}`);

            const { data: businessData, error: businessError } = await supabase
              .from('profiles')  // ✅ Correction: utiliser 'profiles' au lieu de 'users'
              .select('id, username, role')
              .eq('role', UserRole.BUSINESS)
              .ilike('username', postToUpdate.businessName)  // ✅ Utiliser ilike pour ignorer la casse
              .single();

            console.log(`🔍 Résultat recherche entreprise:`, { businessData, businessError });

            if (businessError || !businessData) {
              console.error("Erreur lors de la recherche de l'entreprise mentionnée:", businessError);
            } else {
              // Ne pas notifier l'entreprise si c'est elle qui commente
              // ou si elle est déjà l'auteur du post
              if (businessData.id !== commentToAdd.userId && businessData.id !== postToUpdate.userId) {
                // Déterminer le type de notification en fonction du type de commentaire
                let notificationType = NotificationType.COMMENT;
                let message = '';

                if (commentToAdd.hasUsedProduct && commentToAdd.rating) {
                  notificationType = NotificationType.REVIEW;
                  message = `${commentToAdd.username} a laissé un avis sur votre produit avec une note de ${commentToAdd.rating}/5 : "${commentToAdd.content.substring(0, 50)}${commentToAdd.content.length > 50 ? '...' : ''}"`;
                } else {
                  message = `${commentToAdd.username} a commenté un post concernant votre entreprise : "${commentToAdd.content.substring(0, 50)}${commentToAdd.content.length > 50 ? '...' : ''}"`;
                }

                // Créer une notification pour l'entreprise mentionnée
                await createNotificationFromHook({
                  userId: businessData.id,
                  senderId: commentToAdd.userId,
                  senderName: commentToAdd.username,
                  senderProfilePicture: commentToAdd.profilePicture,
                  type: notificationType,
                  message,
                  relatedItemId: postId,
                  relatedItemType: 'post'
                });

                console.log(`📢 Notification envoyée à l'entreprise: ${businessData.username}`);
              }
            }
          }
        } catch (err) {
          console.error("Erreur lors de l'envoi des notifications:", err);
        }
      }
    } catch (err) {
      console.error("Error adding comment:", err);
      alert("Une erreur s'est produite lors de l'ajout du commentaire. Veuillez réessayer.");

      // Revert optimistic update on error
      setPosts(prevPosts => prevPosts.map(p => {
        if (p.id === postId) {
          const revertedComments = p.comments.filter(c => c.id !== commentToAdd.id);
          return { ...p, comments: revertedComments };
        }
        return p;
      }));
      console.log('Mise à jour optimiste annulée suite à une erreur');
    } finally {
      console.log('Fonction addComment terminée');
    }
  };

  // 🔄 FONCTION DE PARTAGE AVEC NOTIFICATION
  const sharePost = async (postId: string, userId: string, platform: string) => {
    try {
      const postToShare = posts.find(p => p.id === postId);
      if (!postToShare) {
        console.error('Post non trouvé pour le partage:', postId);
        return;
      }

      // 🔄 NOTIFICATION POUR L'AUTEUR DU POST (PARTAGE)
      if (postToShare.userId !== userId) {
        try {

          // Récupérer les infos de l'utilisateur qui partage
          const { data: sharerProfile, error: sharerError } = await supabase
            .from('profiles')
            .select('username, profile_picture')
            .eq('id', userId)
            .single();

          if (!sharerError && sharerProfile) {
            const platformNames: { [key: string]: string } = {
              'facebook': 'Facebook',
              'whatsapp': 'WhatsApp',
              'twitter': 'Twitter',
              'copy_link': 'lien copié',
              'other': 'réseaux sociaux'
            };

            const platformName = platformNames[platform] || 'réseaux sociaux';

            await createNotificationFromHook({
              userId: postToShare.userId, // Auteur du post
              senderId: userId, // Utilisateur qui partage
              senderName: sharerProfile.username,
              senderProfilePicture: sharerProfile.profile_picture,
              type: NotificationType.MENTION, // Utiliser MENTION pour les partages
              message: `${sharerProfile.username} a partagé votre post sur ${platformName}`,
              relatedItemId: postId,
              relatedItemType: 'post'
            });

            console.log(`🔄 Notification de partage envoyée à l'auteur du post: ${postToShare.username} (plateforme: ${platformName})`);
          }
        } catch (notifError) {
          console.error("Erreur lors de l'envoi de la notification de partage:", notifError);
        }
      }

      console.log(`🔄 Post ${postId} partagé par ${userId} sur ${platform}`);
    } catch (error) {
      console.error("Erreur lors du partage du post:", error);
    }
  };

  const getPostsByUser = (userId: string): IPost[] => {
    return posts.filter(post => post.userId === userId);
  };

  const getPostsByBusiness = (businessName: string): IPost[] => {
    return posts.filter(post => post.businessName === businessName);
  };

  return (
    <PostsContext.Provider value={{
      posts,
      loading,
      createPost,
      updatePost,
      deletePost,
      likePost,
      toggleRecommendPost,
      addComment,
      sharePost,
      getPostsByUser,
      getPostsByBusiness,
      refreshPosts: fetchPosts
    }}>
      {children}
    </PostsContext.Provider>
  );
};
