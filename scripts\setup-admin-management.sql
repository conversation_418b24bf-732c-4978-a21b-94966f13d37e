-- CONFIGURATION COMPLÈTE DE LA GESTION DES ADMINISTRATEURS
-- Exécutez ce script dans Supabase SQL Editor

-- =====================================================
-- 1. CRÉER UNE ALERTE DE DÉPLOIEMENT
-- =====================================================

DO $$
DECLARE
    admin_profile_id uuid;
BEGIN
    SELECT id INTO admin_profile_id FROM admin_profiles LIMIT 1;
    
    IF admin_profile_id IS NOT NULL THEN
        INSERT INTO admin_alerts (
            alert_type, 
            priority, 
            title, 
            message, 
            details, 
            created_by, 
            status
        ) VALUES (
            'system', 
            'high', 
            'Gestion des Administrateurs Déployée', 
            'Le module complet de gestion des administrateurs est maintenant disponible avec toutes les fonctionnalités.', 
            '{"module": "admin_management", "features": ["create", "edit", "delete", "toggle_status", "permissions", "hierarchy"]}'::jsonb, 
            admin_profile_id, 
            'open'
        );
        
        RAISE NOTICE '✅ Alerte de déploiement créée';
    END IF;
END $$;

-- =====================================================
-- 2. CRÉER DES DONNÉES DE TEST (OPTIONNEL)
-- =====================================================

-- Créer quelques utilisateurs de test pour démonstration
DO $$
DECLARE
    test_user_id uuid;
    admin_profile_id uuid;
BEGIN
    -- Récupérer un profil admin existant pour les logs
    SELECT id INTO admin_profile_id FROM admin_profiles LIMIT 1;
    
    -- Créer un utilisateur de test pour démonstration
    INSERT INTO profiles (
        username, 
        email, 
        role, 
        status,
        created_at,
        updated_at
    ) VALUES (
        'demo_moderator',
        '<EMAIL>',
        'standard',
        'active',
        now(),
        now()
    ) 
    ON CONFLICT (email) DO NOTHING
    RETURNING id INTO test_user_id;
    
    -- Si l'utilisateur a été créé, créer un profil admin de démonstration
    IF test_user_id IS NOT NULL THEN
        INSERT INTO admin_profiles (
            user_id,
            admin_level,
            admin_code,
            department,
            permissions,
            is_active,
            created_by,
            created_at,
            updated_at,
            activated_at
        ) VALUES (
            test_user_id,
            'content_moderator',
            'ADM' || LPAD((RANDOM() * 999999)::int::text, 6, '0'),
            'Modération',
            '{"content_moderator": true, "manage_content": true, "view_reports": true}'::jsonb,
            true,
            admin_profile_id,
            now(),
            now(),
            now()
        );
        
        RAISE NOTICE '✅ Utilisateur de démonstration créé: demo_moderator';
    ELSE
        RAISE NOTICE 'ℹ️  Utilisateur demo_moderator existe déjà';
    END IF;
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE '⚠️  Erreur lors de la création des données de test: %', SQLERRM;
END $$;

-- =====================================================
-- 3. VÉRIFIER LES PERMISSIONS ET FONCTIONS
-- =====================================================

-- Vérifier que toutes les fonctions nécessaires existent
DO $$
DECLARE
    function_exists boolean;
BEGIN
    -- Vérifier la fonction check_admin_permission
    SELECT EXISTS (
        SELECT 1 FROM pg_proc 
        WHERE proname = 'check_admin_permission'
    ) INTO function_exists;
    
    IF function_exists THEN
        RAISE NOTICE '✅ Fonction check_admin_permission existe';
    ELSE
        RAISE NOTICE '❌ Fonction check_admin_permission manquante';
    END IF;
    
    -- Vérifier les contraintes de clés étrangères
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'admin_profiles_user_id_fkey'
    ) THEN
        RAISE NOTICE '✅ Contrainte admin_profiles_user_id_fkey existe';
    ELSE
        RAISE NOTICE '❌ Contrainte admin_profiles_user_id_fkey manquante';
    END IF;
END $$;

-- =====================================================
-- 4. CRÉER UNE VUE POUR LES STATISTIQUES ADMIN
-- =====================================================

-- Vue pour les statistiques des administrateurs
CREATE OR REPLACE VIEW admin_statistics AS
SELECT 
    COUNT(*) as total_admins,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_admins,
    COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_admins,
    COUNT(CASE WHEN admin_level = 'super_admin' THEN 1 END) as super_admins,
    COUNT(CASE WHEN admin_level = 'platform_admin' THEN 1 END) as platform_admins,
    COUNT(CASE WHEN admin_level = 'content_moderator' THEN 1 END) as content_moderators,
    COUNT(CASE WHEN admin_level = 'support_admin' THEN 1 END) as support_admins,
    COUNT(CASE WHEN admin_level = 'business_admin' THEN 1 END) as business_admins,
    COUNT(CASE WHEN admin_level = 'user_admin' THEN 1 END) as user_admins,
    COUNT(CASE WHEN admin_level = 'analytics_admin' THEN 1 END) as analytics_admins,
    COUNT(CASE WHEN last_login IS NOT NULL THEN 1 END) as admins_with_login,
    AVG(login_count) as avg_login_count
FROM admin_profiles;

-- Donner les permissions sur la vue
GRANT SELECT ON admin_statistics TO authenticated;

-- =====================================================
-- 5. CRÉER UNE FONCTION POUR GÉNÉRER DES CODES ADMIN UNIQUES
-- =====================================================

CREATE OR REPLACE FUNCTION generate_unique_admin_code()
RETURNS text AS $$
DECLARE
    new_code text;
    code_exists boolean;
BEGIN
    LOOP
        -- Générer un nouveau code
        new_code := 'ADM' || LPAD((RANDOM() * 999999)::int::text, 6, '0');
        
        -- Vérifier s'il existe déjà
        SELECT EXISTS (
            SELECT 1 FROM admin_profiles WHERE admin_code = new_code
        ) INTO code_exists;
        
        -- Si le code n'existe pas, on peut l'utiliser
        IF NOT code_exists THEN
            RETURN new_code;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. CRÉER UNE FONCTION POUR VALIDER LES PERMISSIONS ADMIN
-- =====================================================

CREATE OR REPLACE FUNCTION validate_admin_permissions(
    p_admin_level admin_level,
    p_permissions jsonb
)
RETURNS boolean AS $$
DECLARE
    required_permissions text[];
    permission_key text;
BEGIN
    -- Définir les permissions requises selon le niveau
    CASE p_admin_level
        WHEN 'super_admin' THEN
            required_permissions := ARRAY['all'];
        WHEN 'platform_admin' THEN
            required_permissions := ARRAY['manage_system', 'manage_users', 'manage_businesses'];
        WHEN 'content_moderator' THEN
            required_permissions := ARRAY['manage_content', 'view_reports'];
        WHEN 'support_admin' THEN
            required_permissions := ARRAY['manage_support', 'view_tickets'];
        WHEN 'business_admin' THEN
            required_permissions := ARRAY['manage_businesses', 'view_business_reports'];
        WHEN 'user_admin' THEN
            required_permissions := ARRAY['manage_users', 'view_user_reports'];
        WHEN 'analytics_admin' THEN
            required_permissions := ARRAY['view_analytics', 'generate_reports'];
        ELSE
            required_permissions := ARRAY[]::text[];
    END CASE;
    
    -- Vérifier que toutes les permissions requises sont présentes
    FOREACH permission_key IN ARRAY required_permissions
    LOOP
        IF NOT (p_permissions ? permission_key AND (p_permissions->>permission_key)::boolean = true) THEN
            RETURN false;
        END IF;
    END LOOP;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. MESSAGE FINAL
-- =====================================================

DO $$
DECLARE
    total_admins integer;
    active_admins integer;
    admin_levels text;
BEGIN
    -- Récupérer les statistiques
    SELECT 
        total_admins, 
        active_admins
    INTO total_admins, active_admins
    FROM admin_statistics;
    
    -- Récupérer les niveaux disponibles
    SELECT string_agg(enumlabel, ', ' ORDER BY enumsortorder) 
    INTO admin_levels
    FROM pg_enum e
    JOIN pg_type t ON e.enumtypid = t.oid
    WHERE t.typname = 'admin_level';
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '🎉 GESTION DES ADMINISTRATEURS DÉPLOYÉE !';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '📊 STATISTIQUES ACTUELLES:';
    RAISE NOTICE '   - Total administrateurs: %', total_admins;
    RAISE NOTICE '   - Administrateurs actifs: %', active_admins;
    RAISE NOTICE '';
    RAISE NOTICE '🔧 NIVEAUX DISPONIBLES:';
    RAISE NOTICE '   %', admin_levels;
    RAISE NOTICE '';
    RAISE NOTICE '✅ Vue admin_statistics créée';
    RAISE NOTICE '✅ Fonction generate_unique_admin_code créée';
    RAISE NOTICE '✅ Fonction validate_admin_permissions créée';
    RAISE NOTICE '✅ Données de test créées (optionnel)';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 FONCTIONNALITÉS DISPONIBLES:';
    RAISE NOTICE '   - Créer de nouveaux administrateurs';
    RAISE NOTICE '   - Modifier les niveaux et permissions';
    RAISE NOTICE '   - Activer/Désactiver des comptes admin';
    RAISE NOTICE '   - Supprimer des administrateurs';
    RAISE NOTICE '   - Gérer la hiérarchie (superviseurs)';
    RAISE NOTICE '   - Filtrer et rechercher';
    RAISE NOTICE '   - Statistiques en temps réel';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 ALLEZ DANS L''ONGLET "GESTION ADMINS" !';
    RAISE NOTICE '';
END $$;
