import React from 'react';
import { CheckCircle, Shield, Award, Crown } from 'lucide-react';
import { UserRole } from '../../types';

interface VerificationBadgeProps {
  isVerified?: boolean;
  userRole?: UserRole;
  businessVerified?: boolean;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'detailed';
  showTooltip?: boolean;
  className?: string;
}

const VerificationBadge: React.FC<VerificationBadgeProps> = ({
  isVerified = false,
  userRole = UserRole.STANDARD,
  businessVerified = false,
  size = 'sm',
  variant = 'default',
  showTooltip = true,
  className = ''
}) => {
  // Ne rien afficher si pas vérifié
  if (!isVerified && !businessVerified) {
    return null;
  }

  // Définir les tailles
  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  // Définir les styles selon le rôle et le type de vérification
  const getBadgeConfig = () => {
    if (userRole === UserRole.ADMIN) {
      return {
        icon: Crown,
        color: 'text-red-500',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        tooltip: 'Administrateur Vérifié',
        label: 'Admin'
      };
    }

    if (userRole === UserRole.BUSINESS) {
      if (businessVerified) {
        return {
          icon: Shield,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          tooltip: 'Entreprise Vérifiée - Documents officiels validés',
          label: 'Entreprise Vérifiée'
        };
      } else if (isVerified) {
        return {
          icon: CheckCircle,
          color: 'text-blue-500',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          tooltip: 'Compte Business Vérifié',
          label: 'Business'
        };
      }
    }

    // Utilisateur standard vérifié
    return {
      icon: CheckCircle,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      tooltip: 'Compte Vérifié - Identité confirmée',
      label: 'Vérifié'
    };
  };

  const config = getBadgeConfig();
  const IconComponent = config.icon;

  // Rendu selon la variante
  if (variant === 'minimal') {
    return (
      <div 
        className={`inline-flex items-center ${className}`}
        title={showTooltip ? config.tooltip : undefined}
      >
        <IconComponent 
          className={`${sizeClasses[size]} ${config.color}`}
        />
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <div 
        className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${config.bgColor} ${config.color} ${config.borderColor} border ${className}`}
        title={showTooltip ? config.tooltip : undefined}
      >
        <IconComponent className={sizeClasses[size]} />
        <span>{config.label}</span>
      </div>
    );
  }

  // Variante par défaut
  return (
    <div 
      className={`inline-flex items-center ${className}`}
      title={showTooltip ? config.tooltip : undefined}
    >
      <div className={`rounded-full p-0.5 ${config.bgColor} ${config.borderColor} border`}>
        <IconComponent 
          className={`${sizeClasses[size]} ${config.color}`}
        />
      </div>
    </div>
  );
};

export default VerificationBadge;
