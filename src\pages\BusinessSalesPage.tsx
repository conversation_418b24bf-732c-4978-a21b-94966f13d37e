import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import {
  TrendingUp,
  DollarSign,
  ShoppingCart,
  Users,
  Calendar,
  Download,
  Filter,
  Search,
  BarChart3,
  <PERSON>Chart,
  Target,
  Award,
  ArrowUpRight,
  ArrowDownRight,
  Eye,
  Edit,
  Trash2,
  ChevronDown,
  RefreshCw,
  Bell,
  Mail,
  FileText,
  Star,
  TrendingDown,
  Package,
  CreditCard,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  MoreHorizontal,
  ExternalLink,
  Zap,
  Brain,
  Lightbulb,
  Activity
} from 'lucide-react';
import { AIChatButton } from '../components/AIChat';
import { SalesEvolutionChart, CategoryDistributionChart, MonthlyComparisonChart } from '../components/Charts/CSSCharts';
import { DataToggle } from '../components/DataToggle';
import { useRealSalesData } from '../hooks/useRealData';
import { formatAmount } from '../utils/formatUtils';
import '../styles/BusinessSales.css';

// Types pour les ventes avancées
interface SaleProduct {
  name: string;
  quantity: number;
  price: number;
  category: string;
}

interface Sale {
  id: string;
  orderId: string;
  client: string;
  clientEmail: string;
  date: string;
  products: SaleProduct[];
  totalAmount: number;
  commission: number;
  netAmount: number;
  status: 'completed' | 'pending' | 'cancelled' | 'refunded';
  paymentMethod: string;
  category: string;
  rating?: number;
  notes?: string;
}

interface SalesStats {
  totalRevenue: number;
  totalCommissions: number;
  totalSales: number;
  averageOrderValue: number;
  monthlyGrowth: number;
  topProduct: string;
  topClient: string;
  conversionRate: number;
  repeatCustomers: number;
  newCustomers: number;
}

interface SalesInsight {
  type: 'positive' | 'warning' | 'info';
  title: string;
  description: string;
  action?: string;
  impact?: string;
}

const BusinessSalesPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [sales, setSales] = useState<Sale[]>([]);
  const [filteredSales, setFilteredSales] = useState<Sale[]>([]);
  const [salesStats, setSalesStats] = useState<SalesStats>({
    totalRevenue: 0,
    totalCommissions: 0,
    totalSales: 0,
    averageOrderValue: 0,
    monthlyGrowth: 0,
    topProduct: '',
    topClient: '',
    conversionRate: 0,
    repeatCustomers: 0,
    newCustomers: 0
  });

  // États pour les filtres et la recherche
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('month');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showFilters, setShowFilters] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);

  // États pour les modals et fonctionnalités avancées
  const [showSaleDetails, setShowSaleDetails] = useState<string | null>(null);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showReportScheduler, setShowReportScheduler] = useState(false);
  const [showInsights, setShowInsights] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState('month');

  // États pour le Chat IA
  const [showAIChat, setShowAIChat] = useState(false);
  const [chatMessages, setChatMessages] = useState([
    {
      id: 1,
      type: 'ai',
      message: 'Bonjour ! Je suis votre assistant IA pour les ventes. Comment puis-je vous aider aujourd\'hui ?',
      timestamp: new Date()
    }
  ]);
  const [chatInput, setChatInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  // États pour les graphiques interactifs
  const [selectedChart, setSelectedChart] = useState('evolution');
  const [chartTimeframe, setChartTimeframe] = useState('3months');
  const [showAdvancedCharts, setShowAdvancedCharts] = useState(true);

  // États pour les vraies données
  const [useRealData, setUseRealData] = useState(false);
  const realSalesData = useRealSalesData();
  const [dataLoading, setDataLoading] = useState(false);

  // Insights et recommandations
  const [salesInsights, setSalesInsights] = useState<SalesInsight[]>([]);

  // Données simulées pour démonstration
  useEffect(() => {
    const mockSales: Sale[] = [
      {
        id: 'SALE-001',
        orderId: 'ORD-001',
        client: 'Jean Dupont',
        clientEmail: '<EMAIL>',
        date: '2025-01-10',
        products: [
          { name: 'Olgane - Huile de beauté', quantity: 2, price: 15000, category: 'Beauté' }
        ],
        totalAmount: 30000,
        commission: 1500,
        netAmount: 28500,
        status: 'completed',
        paymentMethod: 'Mobile Money',
        category: 'Beauté',
        rating: 4.8,
        notes: 'Client satisfait, commande livrée rapidement'
      },
      {
        id: 'SALE-002',
        orderId: 'ORD-002',
        client: 'Marie Koné',
        clientEmail: '<EMAIL>',
        date: '2025-01-08',
        products: [
          { name: 'Olgane - Huile de beauté', quantity: 1, price: 15000, category: 'Beauté' },
          { name: 'Crème hydratante visage', quantity: 1, price: 12500, category: 'Cosmétiques' }
        ],
        totalAmount: 27500,
        commission: 1375,
        netAmount: 26125,
        status: 'completed',
        paymentMethod: 'Carte bancaire',
        category: 'Beauté',
        rating: 4.5,
        notes: 'Commande multiple, client fidèle'
      },
      {
        id: 'SALE-003',
        orderId: 'ORD-003',
        client: 'Paul Traoré',
        clientEmail: '<EMAIL>',
        date: '2025-01-01',
        products: [
          { name: 'Crème hydratante visage', quantity: 3, price: 12500, category: 'Cosmétiques' }
        ],
        totalAmount: 37500,
        commission: 1875,
        netAmount: 35625,
        status: 'completed',
        paymentMethod: 'Virement',
        category: 'Cosmétiques',
        rating: 4.9,
        notes: 'Grosse commande, excellent client'
      },
      {
        id: 'SALE-004',
        orderId: 'ORD-005',
        client: 'Luc Kouassi',
        clientEmail: '<EMAIL>',
        date: '2025-01-05',
        products: [
          { name: 'Crème hydratante visage', quantity: 2, price: 12500, category: 'Cosmétiques' }
        ],
        totalAmount: 25000,
        commission: 1250,
        netAmount: 23750,
        status: 'pending',
        paymentMethod: 'Mobile Money',
        category: 'Cosmétiques',
        rating: 4.2
      },
      {
        id: 'SALE-005',
        orderId: 'ORD-006',
        client: 'Aminata Diallo',
        clientEmail: '<EMAIL>',
        date: '2024-12-20',
        products: [
          { name: 'Olgane - Huile de beauté', quantity: 1, price: 15000, category: 'Beauté' }
        ],
        totalAmount: 15000,
        commission: 750,
        netAmount: 14250,
        status: 'completed',
        paymentMethod: 'Carte bancaire',
        category: 'Beauté',
        rating: 4.7,
        notes: 'Première commande, très satisfaite'
      },
      {
        id: 'SALE-006',
        orderId: 'ORD-007',
        client: 'Fatou Camara',
        clientEmail: '<EMAIL>',
        date: '2025-01-12',
        products: [
          { name: 'Sérum anti-âge', quantity: 1, price: 22000, category: 'Cosmétiques' },
          { name: 'Masque purifiant', quantity: 2, price: 8500, category: 'Soins' }
        ],
        totalAmount: 39000,
        commission: 1950,
        netAmount: 37050,
        status: 'completed',
        paymentMethod: 'Mobile Money',
        category: 'Cosmétiques',
        rating: 5.0,
        notes: 'Cliente VIP, commande premium'
      },
      {
        id: 'SALE-007',
        orderId: 'ORD-008',
        client: 'Ibrahim Sanogo',
        clientEmail: '<EMAIL>',
        date: '2025-01-09',
        products: [
          { name: 'Gel douche naturel', quantity: 3, price: 6500, category: 'Hygiène' }
        ],
        totalAmount: 19500,
        commission: 975,
        netAmount: 18525,
        status: 'cancelled',
        paymentMethod: 'Carte bancaire',
        category: 'Hygiène',
        notes: 'Annulé par le client - problème de livraison'
      }
    ];

    setSales(mockSales);
    setFilteredSales(mockSales);

    // Calculer les statistiques avancées
    const completedSales = mockSales.filter(sale => sale.status === 'completed');
    const totalRevenue = completedSales.reduce((sum, sale) => sum + sale.totalAmount, 0);
    const totalCommissions = completedSales.reduce((sum, sale) => sum + sale.commission, 0);
    const totalSales = completedSales.length;
    const averageOrderValue = totalSales > 0 ? totalRevenue / totalSales : 0;

    // Analyser les clients
    const clientCounts = new Map();
    completedSales.forEach(sale => {
      clientCounts.set(sale.client, (clientCounts.get(sale.client) || 0) + 1);
    });
    const repeatCustomers = Array.from(clientCounts.values()).filter(count => count > 1).length;
    const newCustomers = clientCounts.size - repeatCustomers;

    // Trouver le top produit et client
    const productCounts = new Map();
    const clientTotals = new Map();

    completedSales.forEach(sale => {
      sale.products.forEach(product => {
        productCounts.set(product.name, (productCounts.get(product.name) || 0) + product.quantity);
      });
      clientTotals.set(sale.client, (clientTotals.get(sale.client) || 0) + sale.totalAmount);
    });

    const topProduct = Array.from(productCounts.entries())
      .sort((a, b) => b[1] - a[1])[0]?.[0] || '';
    const topClient = Array.from(clientTotals.entries())
      .sort((a, b) => b[1] - a[1])[0]?.[0] || '';

    const stats: SalesStats = {
      totalRevenue,
      totalCommissions,
      totalSales,
      averageOrderValue,
      monthlyGrowth: 15.3,
      topProduct,
      topClient,
      conversionRate: 78.5,
      repeatCustomers,
      newCustomers
    };

    setSalesStats(stats);

    // Générer des insights intelligents
    const insights: SalesInsight[] = [
      {
        type: 'positive',
        title: 'Croissance exceptionnelle',
        description: `Vos ventes ont augmenté de ${stats.monthlyGrowth}% ce mois`,
        action: 'Continuez sur cette lancée !',
        impact: 'Tendance très positive'
      },
      {
        type: 'info',
        title: 'Produit star identifié',
        description: `"${topProduct}" est votre produit le plus vendu`,
        action: 'Optimisez son stock et sa promotion',
        impact: 'Opportunité de croissance'
      },
      {
        type: 'warning',
        title: 'Fidélisation à améliorer',
        description: `${newCustomers} nouveaux clients vs ${repeatCustomers} fidèles`,
        action: 'Mettez en place un programme de fidélité',
        impact: 'Potentiel de revenus récurrents'
      }
    ];

    setSalesInsights(insights);
  }, []);

  // Fonction de filtrage et recherche
  useEffect(() => {
    let filtered = [...sales];

    // Recherche
    if (searchTerm) {
      filtered = filtered.filter(sale =>
        sale.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sale.orderId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sale.products.some(product =>
          product.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Filtre par statut
    if (statusFilter !== 'all') {
      filtered = filtered.filter(sale => sale.status === statusFilter);
    }

    // Filtre par catégorie
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(sale => sale.category === categoryFilter);
    }

    // Filtre par date
    if (dateFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();

      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          filtered = filtered.filter(sale => new Date(sale.date) >= filterDate);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          filtered = filtered.filter(sale => new Date(sale.date) >= filterDate);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          filtered = filtered.filter(sale => new Date(sale.date) >= filterDate);
          break;
      }
    }

    // Tri
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'date':
          aValue = new Date(a.date);
          bValue = new Date(b.date);
          break;
        case 'amount':
          aValue = a.totalAmount;
          bValue = b.totalAmount;
          break;
        case 'client':
          aValue = a.client;
          bValue = b.client;
          break;
        default:
          aValue = a.date;
          bValue = b.date;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredSales(filtered);
  }, [sales, searchTerm, statusFilter, categoryFilter, dateFilter, sortBy, sortOrder]);



  // Fonction pour formater les dates
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Fonction pour obtenir la couleur du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'status-completed';
      case 'pending': return 'status-pending';
      case 'cancelled': return 'status-cancelled';
      case 'refunded': return 'status-refunded';
      default: return 'status-pending';
    }
  };

  // Fonction pour obtenir le texte du statut
  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Terminé';
      case 'pending': return 'En attente';
      case 'cancelled': return 'Annulé';
      case 'refunded': return 'Remboursé';
      default: return status;
    }
  };

  // Fonctions d'export avancées
  const handleExportPDF = () => {
    // Simulation d'export PDF avec données complètes
    const reportData = {
      title: 'Rapport de Ventes - Janvier 2025',
      period: 'Du 1er au 31 janvier 2025',
      summary: {
        totalRevenue: salesStats.totalRevenue,
        totalSales: salesStats.totalSales,
        totalCommissions: salesStats.totalCommissions,
        averageOrder: salesStats.averageOrderValue,
        growth: salesStats.monthlyGrowth
      },
      sales: filteredSales,
      insights: salesInsights,
      charts: {
        evolution: [45000, 73000, 118000],
        categories: { beauty: 45, cosmetics: 35, hygiene: 20 }
      }
    };

    // Dans une vraie app, on utiliserait jsPDF ou une API
    console.log('📄 Export PDF généré:', reportData);

    // Simulation du téléchargement
    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rapport-ventes-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    setShowExportMenu(false);
    alert('📄 Rapport PDF généré avec succès ! (Simulation avec fichier JSON)');
  };

  const handleExportExcel = () => {
    // Simulation d'export Excel avec données tabulaires
    const excelData = filteredSales.map(sale => ({
      'ID Vente': sale.id,
      'Client': sale.client,
      'Email': sale.clientEmail,
      'Date': formatDate(sale.date),
      'Produits': sale.products.map(p => p.name).join(', '),
      'Montant Total': sale.totalAmount,
      'Commission': sale.commission,
      'Montant Net': sale.netAmount,
      'Statut': getStatusText(sale.status),
      'Catégorie': sale.category,
      'Note Client': sale.rating || 'N/A'
    }));

    // Dans une vraie app, on utiliserait xlsx ou SheetJS
    console.log('📊 Export Excel généré:', excelData);

    // Simulation du téléchargement CSV
    const csvContent = [
      Object.keys(excelData[0]).join(','),
      ...excelData.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `donnees-ventes-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    setShowExportMenu(false);
    alert('📊 Données Excel exportées avec succès ! (Simulation avec fichier CSV)');
  };

  // Fonctions du Chat IA
  const handleSendMessage = () => {
    if (!chatInput.trim()) return;

    const userMessage = {
      id: chatMessages.length + 1,
      type: 'user',
      message: chatInput,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsTyping(true);

    // Simulation de réponse IA
    setTimeout(() => {
      const aiResponse = generateAIResponse(chatInput);
      const aiMessage = {
        id: chatMessages.length + 2,
        type: 'ai',
        message: aiResponse,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, aiMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const generateAIResponse = (userInput: string) => {
    const input = userInput.toLowerCase();

    if (input.includes('vente') || input.includes('chiffre')) {
      return `📊 Vos ventes sont excellentes ! Vous avez généré ${formatAmount(salesStats.totalRevenue)} ce mois, soit +${salesStats.monthlyGrowth}% par rapport au mois dernier. Votre meilleur produit est "Olgane - Huile de beauté" avec 3 ventes.`;
    }

    if (input.includes('client') || input.includes('fidélisation')) {
      return `👥 Vous avez ${sales.length} clients ce mois. Je recommande de créer un programme de fidélité pour augmenter la rétention. Votre taux de clients récurrents est de 67%, ce qui peut être amélioré.`;
    }

    if (input.includes('recommandation') || input.includes('conseil')) {
      return `💡 Mes recommandations : 1) Optimisez les prix de vos crèmes hydratantes (+20% de ventes potentielles), 2) Lancez une campagne pour les produits de beauté (votre catégorie la plus performante), 3) Contactez vos clients inactifs avec une offre spéciale.`;
    }

    if (input.includes('objectif') || input.includes('target')) {
      return `🎯 Vous avez dépassé votre objectif mensuel de 18% ! Pour le trimestre, vous êtes à 45% de l'objectif. Au rythme actuel, vous devriez atteindre 95% de votre objectif trimestriel.`;
    }

    if (input.includes('export') || input.includes('rapport')) {
      return `📄 Je peux vous aider à générer des rapports personnalisés. Utilisez les boutons d'export pour télécharger vos données en PDF (avec graphiques) ou Excel (données brutes). Vous pouvez aussi programmer des rapports automatiques.`;
    }

    return `🤖 Je suis là pour vous aider avec vos ventes ! Vous pouvez me demander des analyses, des recommandations, ou des informations sur vos performances. Que souhaitez-vous savoir ?`;
  };

  // Données pour graphiques interactifs
  const getChartData = () => {
    switch (selectedChart) {
      case 'evolution':
        return {
          labels: ['Octobre', 'Novembre', 'Décembre', 'Janvier'],
          data: [32000, 41000, 45000, 118000],
          color: '#3b82f6'
        };
      case 'categories':
        return {
          labels: ['Beauté', 'Cosmétiques', 'Hygiène', 'Soins'],
          data: [45, 35, 15, 5],
          colors: ['#10b981', '#f59e0b', '#ef4444', '#8b5cf6']
        };
      case 'clients':
        return {
          labels: ['Nouveaux', 'Récurrents', 'VIP'],
          data: [40, 50, 10],
          colors: ['#3b82f6', '#10b981', '#f59e0b']
        };
      default:
        return { labels: [], data: [], color: '#3b82f6' };
    }
  };

  // Fonction pour basculer entre données fictives et vraies données
  const handleDataToggle = (useReal: boolean) => {
    setUseRealData(useReal);
    setDataLoading(true);

    if (useReal && realSalesData.sales && realSalesData.salesStats) {
      // Convertir les vraies données au format attendu
      const convertedSales: Sale[] = realSalesData.sales.map(realSale => ({
        id: realSale.id,
        orderId: realSale.order_id,
        client: realSale.buyer?.username || 'Client inconnu',
        clientEmail: realSale.buyer?.email || '',
        date: new Date(realSale.created_at).toISOString().split('T')[0],
        products: [{
          name: realSale.product?.name || 'Produit inconnu',
          quantity: realSale.quantity,
          price: realSale.unit_price,
          category: realSale.product?.category || 'Autre'
        }],
        totalAmount: realSale.total_price,
        commission: realSale.commission,
        netAmount: realSale.net_amount,
        status: realSale.status,
        paymentMethod: realSale.payment_method,
        category: realSale.product?.category || 'Autre',
        rating: Math.random() * 2 + 3, // Simulation
        notes: 'Vente réelle depuis la base de données'
      }));

      const convertedStats: SalesStats = {
        totalRevenue: realSalesData.salesStats.totalRevenue,
        totalCommissions: realSalesData.salesStats.totalCommissions,
        totalSales: realSalesData.salesStats.totalSales,
        averageOrderValue: realSalesData.salesStats.averageOrderValue,
        monthlyGrowth: realSalesData.salesStats.monthlyGrowth,
        topProduct: realSalesData.salesStats.topProduct,
        topClient: realSalesData.salesStats.topClient,
        conversionRate: realSalesData.salesStats.conversionRate,
        repeatCustomers: realSalesData.salesStats.repeatCustomers,
        newCustomers: realSalesData.salesStats.newCustomers
      };

      setSales(convertedSales);
      setSalesStats(convertedStats);
    }

    setTimeout(() => setDataLoading(false), 500);
  };

  // Données pour les graphiques Chart.js avancés
  const getSalesEvolutionData = () => {
    const last7Days = [];
    const today = new Date();

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);

      // Simuler des données de vente réalistes
      const baseRevenue = 15000 + Math.random() * 10000;
      const baseCount = 2 + Math.floor(Math.random() * 4);

      last7Days.push({
        date: date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' }),
        revenue: Math.round(baseRevenue),
        count: baseCount
      });
    }

    return last7Days;
  };

  const getCategoryDistributionData = () => {
    // Calculer la répartition réelle des ventes par catégorie
    const categoryTotals = new Map();

    filteredSales.forEach(sale => {
      const current = categoryTotals.get(sale.category) || 0;
      categoryTotals.set(sale.category, current + sale.totalAmount);
    });

    return Array.from(categoryTotals.entries()).map(([category, value]) => ({
      category,
      value
    }));
  };

  const getMonthlyComparisonData = () => {
    return [
      { month: 'Oct', current: 32000, previous: 28000 },
      { month: 'Nov', current: 41000, previous: 35000 },
      { month: 'Déc', current: 45000, previous: 42000 },
      { month: 'Jan', current: 118000, previous: 45000 }
    ];
  };

  return (
    <div className="business-sales-page">
      <div className="sales-header">
        <div className="header-content">
          <h1 className="page-title">
            <ShoppingCart size={28} />
            Mes Ventes
          </h1>
          <p className="page-subtitle">
            Analysez vos performances commerciales et optimisez vos revenus
          </p>
        </div>

        <div className="header-actions">
          <button
            className="action-btn notification-btn"
            onClick={() => setShowNotifications(!showNotifications)}
          >
            <Bell size={16} />
            Alertes
          </button>
          <button
            className="action-btn refresh-btn"
            onClick={() => window.location.reload()}
          >
            <RefreshCw size={16} />
            Actualiser
          </button>
        </div>
      </div>

      {/* Toggle Données Réelles / Fictives */}
      <DataToggle
        onToggle={handleDataToggle}
        initialValue={useRealData}
        hasRealData={realSalesData.sales && realSalesData.sales.length > 0}
        loading={dataLoading || realSalesData.loading}
      />

      {/* KPIs Dashboard */}
      <div className="sales-kpis">
        <div className="kpi-card revenue">
          <div className="kpi-icon">
            <DollarSign size={24} />
          </div>
          <div className="kpi-content">
            <h3>Chiffre d'Affaires</h3>
            <p className="kpi-value">{formatAmount(salesStats.totalRevenue)}</p>
            <span className="kpi-trend positive">
              <ArrowUpRight size={16} />
              +{salesStats.monthlyGrowth}% ce mois
            </span>
          </div>
        </div>

        <div className="kpi-card commissions">
          <div className="kpi-icon">
            <Target size={24} />
          </div>
          <div className="kpi-content">
            <h3>Commissions</h3>
            <p className="kpi-value">{formatAmount(salesStats.totalCommissions)}</p>
            <span className="kpi-trend positive">
              <ArrowUpRight size={16} />
              +12.8% ce mois
            </span>
          </div>
        </div>

        <div className="kpi-card sales-count">
          <div className="kpi-icon">
            <ShoppingCart size={24} />
          </div>
          <div className="kpi-content">
            <h3>Nombre de Ventes</h3>
            <p className="kpi-value">{salesStats.totalSales}</p>
            <span className="kpi-trend positive">
              <ArrowUpRight size={16} />
              +8 cette semaine
            </span>
          </div>
        </div>

        <div className="kpi-card avg-order">
          <div className="kpi-icon">
            <TrendingUp size={24} />
          </div>
          <div className="kpi-content">
            <h3>Panier Moyen</h3>
            <p className="kpi-value">{formatAmount(salesStats.averageOrderValue)}</p>
            <span className="kpi-trend positive">
              <ArrowUpRight size={16} />
              +5.2% ce mois
            </span>
          </div>
        </div>
      </div>

      {/* Insights Section */}
      {showInsights && salesInsights.length > 0 && (
        <div className="sales-insights">
          <div className="insights-header">
            <h2 className="insights-title">
              <Brain size={20} />
              Insights & Recommandations
            </h2>
            <button
              className="action-btn"
              onClick={() => setShowInsights(false)}
            >
              Masquer
            </button>
          </div>

          <div className="insights-list">
            {salesInsights.map((insight, index) => (
              <div key={index} className={`insight-item ${insight.type}`}>
                <div className="insight-icon">
                  {insight.type === 'positive' && <TrendingUp size={20} />}
                  {insight.type === 'warning' && <AlertCircle size={20} />}
                  {insight.type === 'info' && <Lightbulb size={20} />}
                </div>
                <div className="insight-content">
                  <h4>{insight.title}</h4>
                  <p>{insight.description}</p>
                  {insight.action && <p className="insight-action">{insight.action}</p>}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Section Analytics & Graphiques */}
      <div className="sales-analytics">
        <div className="analytics-header">
          <h2 className="analytics-title">
            <BarChart3 size={20} />
            Analytics & Graphiques
          </h2>
          <div className="analytics-actions">
            <div className="export-dropdown">
              <button
                className="analytics-btn"
                onClick={() => setShowExportMenu(!showExportMenu)}
              >
                <Download size={16} />
                Exporter rapport
                <ChevronDown size={14} />
              </button>
              {showExportMenu && (
                <div className="export-menu">
                  <div className="export-section">
                    <h4>Format d'export</h4>
                    <button
                      className="export-option pdf"
                      onClick={() => handleExportPDF()}
                    >
                      <FileText size={16} />
                      <div>
                        <span>Rapport PDF</span>
                        <small>Rapport complet avec graphiques</small>
                      </div>
                    </button>
                    <button
                      className="export-option excel"
                      onClick={() => handleExportExcel()}
                    >
                      <Package size={16} />
                      <div>
                        <span>Données Excel</span>
                        <small>Tableau détaillé des ventes</small>
                      </div>
                    </button>
                  </div>

                  <div className="export-section">
                    <h4>Période d'export</h4>
                    <select className="export-period">
                      <option value="month">Ce mois</option>
                      <option value="quarter">Ce trimestre</option>
                      <option value="year">Cette année</option>
                      <option value="custom">Période personnalisée</option>
                    </select>
                  </div>

                  <div className="export-section">
                    <h4>Options avancées</h4>
                    <label className="export-checkbox">
                      <input type="checkbox" defaultChecked />
                      <span>Inclure les graphiques</span>
                    </label>
                    <label className="export-checkbox">
                      <input type="checkbox" defaultChecked />
                      <span>Détails des clients</span>
                    </label>
                    <label className="export-checkbox">
                      <input type="checkbox" />
                      <span>Analyses prédictives</span>
                    </label>
                  </div>
                </div>
              )}
            </div>

            <div className="schedule-dropdown">
              <button
                className="analytics-btn"
                onClick={() => setShowReportScheduler(!showReportScheduler)}
              >
                <Calendar size={16} />
                Planifier rapport
              </button>
              {showReportScheduler && (
                <div className="schedule-menu">
                  <h4>Rapports automatiques</h4>
                  <div className="schedule-option">
                    <label>Fréquence</label>
                    <select>
                      <option>Hebdomadaire</option>
                      <option>Mensuel</option>
                      <option>Trimestriel</option>
                    </select>
                  </div>
                  <div className="schedule-option">
                    <label>Email de destination</label>
                    <input type="email" placeholder="<EMAIL>" />
                  </div>
                  <div className="schedule-option">
                    <label>Jour d'envoi</label>
                    <select>
                      <option>Lundi</option>
                      <option>Vendredi</option>
                      <option>1er du mois</option>
                    </select>
                  </div>
                  <button className="schedule-save">
                    <Mail size={16} />
                    Programmer l'envoi
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sélecteur de graphiques interactifs */}
        <div className="chart-selector">
          <div className="chart-tabs">
            <button
              className={`chart-tab ${selectedChart === 'evolution' ? 'active' : ''}`}
              onClick={() => setSelectedChart('evolution')}
            >
              <TrendingUp size={16} />
              Évolution
            </button>
            <button
              className={`chart-tab ${selectedChart === 'categories' ? 'active' : ''}`}
              onClick={() => setSelectedChart('categories')}
            >
              <PieChart size={16} />
              Catégories
            </button>
            <button
              className={`chart-tab ${selectedChart === 'clients' ? 'active' : ''}`}
              onClick={() => setSelectedChart('clients')}
            >
              <Users size={16} />
              Clients
            </button>
          </div>

          <div className="chart-timeframe">
            <select
              value={chartTimeframe}
              onChange={(e) => setChartTimeframe(e.target.value)}
              className="timeframe-select"
            >
              <option value="1month">1 mois</option>
              <option value="3months">3 mois</option>
              <option value="6months">6 mois</option>
              <option value="1year">1 an</option>
            </select>
          </div>
        </div>

        <div className="analytics-grid">
          <div className="chart-card interactive">
            <div className="chart-header">
              <h3>
                {selectedChart === 'evolution' && 'Évolution des ventes'}
                {selectedChart === 'categories' && 'Répartition par catégorie'}
                {selectedChart === 'clients' && 'Analyse des clients'}
              </h3>
              <div className="chart-actions">
                <button className="chart-action" title="Plein écran">
                  <ExternalLink size={14} />
                </button>
                <button className="chart-action" title="Télécharger">
                  <Download size={14} />
                </button>
              </div>
            </div>

            {selectedChart === 'evolution' && (
              <div className="chart-placeholder evolution">
                <div className="chart-bars interactive">
                  {getChartData().data.map((value, index) => (
                    <div
                      key={index}
                      className="bar interactive"
                      style={{height: `${(value / Math.max(...getChartData().data)) * 100}%`}}
                      title={`${getChartData().labels[index]}: ${formatAmount(value)}`}
                      onClick={() => alert(`Détails pour ${getChartData().labels[index]}: ${formatAmount(value)}`)}
                    ></div>
                  ))}
                </div>
                <div className="chart-labels">
                  {getChartData().labels.map((label, index) => (
                    <span key={index}>{label}</span>
                  ))}
                </div>
              </div>
            )}

            {selectedChart === 'categories' && (
              <div className="pie-chart-container">
                <div className="pie-chart interactive">
                  <div className="pie-segment beauty" title="Beauté: 45%"></div>
                  <div className="pie-segment cosmetics" title="Cosmétiques: 35%"></div>
                  <div className="pie-segment hygiene" title="Hygiène: 15%"></div>
                  <div className="pie-segment soins" title="Soins: 5%"></div>
                </div>
                <div className="pie-legend">
                  {getChartData().labels.map((label, index) => (
                    <div key={index} className="legend-item interactive">
                      <span
                        className={`legend-color ${label.toLowerCase()}`}
                        style={{backgroundColor: getChartData().colors[index]}}
                      ></span>
                      <span>{label} ({getChartData().data[index]}%)</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {selectedChart === 'clients' && (
              <div className="clients-chart">
                <div className="client-segments">
                  {getChartData().labels.map((label, index) => (
                    <div key={index} className="client-segment">
                      <div className="segment-header">
                        <span className="segment-label">{label}</span>
                        <span className="segment-value">{getChartData().data[index]}%</span>
                      </div>
                      <div className="segment-bar">
                        <div
                          className="segment-fill"
                          style={{
                            width: `${getChartData().data[index]}%`,
                            backgroundColor: getChartData().colors[index]
                          }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="chart-card insights">
            <h3>Insights en temps réel</h3>
            <div className="real-time-insights">
              <div className="insight-metric">
                <span className="metric-icon">📈</span>
                <div>
                  <span className="metric-title">Tendance actuelle</span>
                  <span className="metric-desc">Croissance de +23% ce mois</span>
                </div>
              </div>

              <div className="insight-metric">
                <span className="metric-icon">🎯</span>
                <div>
                  <span className="metric-title">Prédiction</span>
                  <span className="metric-desc">Objectif atteint à 95%</span>
                </div>
              </div>

              <div className="insight-metric">
                <span className="metric-icon">💡</span>
                <div>
                  <span className="metric-title">Recommandation</span>
                  <span className="metric-desc">Optimiser les prix beauté</span>
                </div>
              </div>

              <button
                className="ai-chat-trigger"
                onClick={() => setShowAIChat(true)}
              >
                <Brain size={16} />
                Demander à l'IA
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Section Graphiques Avancés Chart.js */}
      {showAdvancedCharts && (
        <div className="advanced-charts-section">
          <div className="charts-header">
            <h2 className="charts-title">
              <BarChart3 size={20} />
              Graphiques Avancés
            </h2>
            <div className="charts-actions">
              <button
                className="charts-btn"
                onClick={() => setShowAdvancedCharts(false)}
              >
                Masquer
              </button>
            </div>
          </div>

          <div className="charts-grid">
            <div className="chart-container sales-chart">
              <SalesEvolutionChart salesData={getSalesEvolutionData()} />
            </div>

            <div className="chart-container products-chart">
              <CategoryDistributionChart categoryData={getCategoryDistributionData()} />
            </div>

            <div className="chart-container analytics-chart">
              <MonthlyComparisonChart monthlyData={getMonthlyComparisonData()} />
            </div>
          </div>

          <div className="charts-insights">
            <div className="chart-insight">
              <h4>📈 Tendance détectée</h4>
              <p>Croissance constante sur les 7 derniers jours avec un pic le weekend</p>
            </div>
            <div className="chart-insight">
              <h4>🎯 Recommandation</h4>
              <p>Optimisez vos campagnes marketing pour les vendredis et samedis</p>
            </div>
            <div className="chart-insight">
              <h4>💡 Opportunité</h4>
              <p>La catégorie "Beauté" représente 45% des ventes - potentiel d'expansion</p>
            </div>
          </div>
        </div>
      )}

      {/* Section Tableau des ventes */}
      <div className="sales-table-section">
        <div className="table-header">
          <h2 className="table-title">
            <Package size={20} />
            Historique des ventes
          </h2>
          <div className="table-actions">
            <div className="search-container">
              <Search size={16} />
              <input
                type="text"
                placeholder="Rechercher une vente..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
            <button
              className="filter-btn"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter size={16} />
              Filtres
            </button>
          </div>
        </div>

        {/* Filtres avancés */}
        {showFilters && (
          <div className="filters-panel">
            <div className="filter-group">
              <label>Statut</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="filter-select"
              >
                <option value="all">Tous les statuts</option>
                <option value="completed">Terminé</option>
                <option value="pending">En attente</option>
                <option value="cancelled">Annulé</option>
                <option value="refunded">Remboursé</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Période</label>
              <select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="filter-select"
              >
                <option value="all">Toutes les périodes</option>
                <option value="today">Aujourd'hui</option>
                <option value="week">Cette semaine</option>
                <option value="month">Ce mois</option>
                <option value="year">Cette année</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Catégorie</label>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="filter-select"
              >
                <option value="all">Toutes les catégories</option>
                <option value="Beauté">Beauté</option>
                <option value="Cosmétiques">Cosmétiques</option>
                <option value="Hygiène">Hygiène</option>
                <option value="Soins">Soins</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Tri</label>
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  setSortBy(field);
                  setSortOrder(order as 'asc' | 'desc');
                }}
                className="filter-select"
              >
                <option value="date-desc">Date (récent)</option>
                <option value="date-asc">Date (ancien)</option>
                <option value="amount-desc">Montant (élevé)</option>
                <option value="amount-asc">Montant (faible)</option>
                <option value="client-asc">Client (A-Z)</option>
                <option value="client-desc">Client (Z-A)</option>
              </select>
            </div>
          </div>
        )}

        {/* Tableau des ventes */}
        <div className="sales-table-container">
          {filteredSales.length === 0 ? (
            <div className="no-sales">
              <Package size={48} />
              <h3>Aucune vente trouvée</h3>
              <p>Aucune vente ne correspond à vos critères de recherche.</p>
            </div>
          ) : (
            <table className="sales-table">
              <thead>
                <tr>
                  <th>ID Vente</th>
                  <th>Client</th>
                  <th>Date</th>
                  <th>Produits</th>
                  <th>Montant</th>
                  <th>Commission</th>
                  <th>Net</th>
                  <th>Statut</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredSales.map((sale) => (
                  <tr key={sale.id}>
                    <td className="sale-id">{sale.id}</td>
                    <td className="client-info">
                      <div className="client-name">{sale.client}</div>
                      <div className="client-email">{sale.clientEmail}</div>
                    </td>
                    <td className="sale-date">{formatDate(sale.date)}</td>
                    <td className="products-info">
                      {sale.products.length === 1 ? (
                        <span>{sale.products[0].name}</span>
                      ) : (
                        <span>{sale.products[0].name} +{sale.products.length - 1} autre(s)</span>
                      )}
                    </td>
                    <td className="amount">{formatAmount(sale.totalAmount)}</td>
                    <td className="commission">{formatAmount(sale.commission)}</td>
                    <td className="net-amount">{formatAmount(sale.netAmount)}</td>
                    <td className="status">
                      <span className={`status-badge ${getStatusColor(sale.status)}`}>
                        {getStatusText(sale.status)}
                      </span>
                    </td>
                    <td className="actions">
                      <button
                        className="action-btn view-btn"
                        onClick={() => setShowSaleDetails(sale.id)}
                        title="Voir détails"
                      >
                        <Eye size={16} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>

        {/* Pagination */}
        <div className="table-footer">
          <div className="results-info">
            Affichage de {filteredSales.length} vente(s) sur {sales.length} au total
          </div>
          <div className="pagination">
            <button className="page-btn" disabled>
              Précédent
            </button>
            <span className="page-info">Page 1 sur 1</span>
            <button className="page-btn" disabled>
              Suivant
            </button>
          </div>
        </div>
      </div>

      {/* Section Notifications & Alertes */}
      {showNotifications && (
        <div className="notifications-panel">
          <div className="notifications-header">
            <h2 className="notifications-title">
              <Bell size={20} />
              Centre de notifications
            </h2>
            <button
              className="close-notifications"
              onClick={() => setShowNotifications(false)}
            >
              ✕
            </button>
          </div>

          <div className="notifications-content">
            <div className="notification-item success">
              <div className="notification-icon">
                <CheckCircle size={20} />
              </div>
              <div className="notification-content">
                <h4>Objectif mensuel atteint !</h4>
                <p>Félicitations ! Vous avez dépassé votre objectif de 100 000 F CFA ce mois.</p>
                <span className="notification-time">Il y a 2 heures</span>
              </div>
            </div>

            <div className="notification-item warning">
              <div className="notification-icon">
                <AlertCircle size={20} />
              </div>
              <div className="notification-content">
                <h4>Baisse des ventes détectée</h4>
                <p>Les ventes ont diminué de 15% cette semaine. Consultez les recommandations.</p>
                <span className="notification-time">Il y a 1 jour</span>
              </div>
            </div>

            <div className="notification-item info">
              <div className="notification-icon">
                <Lightbulb size={20} />
              </div>
              <div className="notification-content">
                <h4>Recommandation IA</h4>
                <p>Optimisez vos prix sur "Crème hydratante" pour augmenter les ventes de 20%.</p>
                <span className="notification-time">Il y a 3 jours</span>
              </div>
            </div>

            <div className="notification-item success">
              <div className="notification-icon">
                <Star size={20} />
              </div>
              <div className="notification-content">
                <h4>Nouveau client fidèle</h4>
                <p>Jean Dupont a effectué sa 5ème commande. Envoyez-lui une offre spéciale !</p>
                <span className="notification-time">Il y a 1 semaine</span>
              </div>
            </div>
          </div>

          <div className="notifications-footer">
            <button className="mark-all-read">
              Marquer tout comme lu
            </button>
            <button className="view-all-notifications">
              Voir toutes les notifications
            </button>
          </div>
        </div>
      )}

      {/* Section Objectifs & Performances */}
      <div className="performance-section">
        <div className="performance-header">
          <h2 className="performance-title">
            <Target size={20} />
            Objectifs & Performances
          </h2>
          <div className="performance-actions">
            <button className="performance-btn">
              <Activity size={16} />
              Définir objectifs
            </button>
          </div>
        </div>

        <div className="objectives-grid">
          <div className="objective-card monthly">
            <div className="objective-header">
              <h3>Objectif mensuel</h3>
              <span className="objective-status achieved">Atteint</span>
            </div>
            <div className="objective-progress">
              <div className="progress-bar">
                <div className="progress-fill" style={{width: '118%'}}></div>
              </div>
              <div className="progress-text">
                <span className="current">118 000 F</span>
                <span className="target">/ 100 000 F</span>
              </div>
            </div>
            <div className="objective-details">
              <span className="achievement">+18% au-dessus de l'objectif</span>
            </div>
          </div>

          <div className="objective-card weekly">
            <div className="objective-header">
              <h3>Objectif hebdomadaire</h3>
              <span className="objective-status in-progress">En cours</span>
            </div>
            <div className="objective-progress">
              <div className="progress-bar">
                <div className="progress-fill" style={{width: '75%'}}></div>
              </div>
              <div className="progress-text">
                <span className="current">18 750 F</span>
                <span className="target">/ 25 000 F</span>
              </div>
            </div>
            <div className="objective-details">
              <span className="remaining">6 250 F restants (2 jours)</span>
            </div>
          </div>

          <div className="objective-card quarterly">
            <div className="objective-header">
              <h3>Objectif trimestriel</h3>
              <span className="objective-status on-track">En bonne voie</span>
            </div>
            <div className="objective-progress">
              <div className="progress-bar">
                <div className="progress-fill" style={{width: '45%'}}></div>
              </div>
              <div className="progress-text">
                <span className="current">135 000 F</span>
                <span className="target">/ 300 000 F</span>
              </div>
            </div>
            <div className="objective-details">
              <span className="projection">Projection: 285 000 F (-5%)</span>
            </div>
          </div>
        </div>

        <div className="performance-insights">
          <h3>Insights de performance</h3>
          <div className="insights-grid">
            <div className="insight-card">
              <div className="insight-metric">
                <span className="metric-value">+23%</span>
                <span className="metric-label">Croissance vs mois dernier</span>
              </div>
              <div className="insight-trend positive">
                <TrendingUp size={16} />
                Excellente progression
              </div>
            </div>

            <div className="insight-card">
              <div className="insight-metric">
                <span className="metric-value">4.8/5</span>
                <span className="metric-label">Satisfaction client moyenne</span>
              </div>
              <div className="insight-trend positive">
                <Star size={16} />
                Très satisfaisant
              </div>
            </div>

            <div className="insight-card">
              <div className="insight-metric">
                <span className="metric-value">67%</span>
                <span className="metric-label">Taux de clients récurrents</span>
              </div>
              <div className="insight-trend warning">
                <Users size={16} />
                À améliorer
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Chat IA intégré */}
      {showAIChat && (
        <div className="ai-chat-overlay">
          <div className="ai-chat-container">
            <div className="ai-chat-header">
              <div className="ai-chat-title">
                <Brain size={20} />
                <span>Assistant IA Ventes</span>
                <div className="ai-status online">
                  <span className="status-dot"></span>
                  En ligne
                </div>
              </div>
              <button
                className="close-chat"
                onClick={() => setShowAIChat(false)}
              >
                ✕
              </button>
            </div>

            <div className="ai-chat-messages">
              {chatMessages.map((message) => (
                <div key={message.id} className={`message ${message.type}`}>
                  <div className="message-avatar">
                    {message.type === 'ai' ? '🤖' : '👤'}
                  </div>
                  <div className="message-content">
                    <div className="message-text">{message.message}</div>
                    <div className="message-time">
                      {message.timestamp.toLocaleTimeString('fr-FR', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                </div>
              ))}

              {isTyping && (
                <div className="message ai typing">
                  <div className="message-avatar">🤖</div>
                  <div className="message-content">
                    <div className="typing-indicator">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="ai-chat-input">
              <div className="quick-questions">
                <button
                  className="quick-btn"
                  onClick={() => setChatInput('Comment améliorer mes ventes ?')}
                >
                  💡 Conseils ventes
                </button>
                <button
                  className="quick-btn"
                  onClick={() => setChatInput('Analyse de mes performances')}
                >
                  📊 Performances
                </button>
                <button
                  className="quick-btn"
                  onClick={() => setChatInput('Prédictions pour le mois prochain')}
                >
                  🔮 Prédictions
                </button>
              </div>

              <div className="input-container">
                <input
                  type="text"
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="Posez votre question à l'IA..."
                  className="chat-input"
                />
                <button
                  className="send-btn"
                  onClick={handleSendMessage}
                  disabled={!chatInput.trim()}
                >
                  <ArrowUpRight size={16} />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bouton Chat IA flottant */}
      {!showAIChat && (
        <button
          className="ai-chat-fab"
          onClick={() => setShowAIChat(true)}
          title="Ouvrir l'assistant IA"
        >
          <Brain size={24} />
          <span className="fab-notification">3</span>
        </button>
      )}

      {/* Chat IA Assistant */}
      <AIChatButton
        context="sales"
        currentData={{
          stats: salesStats,
          sales: filteredSales,
          insights: salesInsights
        }}
      />
    </div>
  );
};

export default BusinessSalesPage;
