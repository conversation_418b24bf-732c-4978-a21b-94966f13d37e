// Service de données simulées pour tester l'IA Conseiller Business

export const MOCK_BUSINESS_POSTS = [
  {
    id: '1',
    user_id: 'user1',
    business_id: 'business1',
    username: '<PERSON>',
    product_name: '<PERSON><PERSON><PERSON>',
    business_name: '<PERSON><PERSON>',
    category: '<PERSON><PERSON>',
    rating: 5,
    description: 'Excellente crème ! Ma peau n\'a jamais été aussi douce. Je recommande vivement ce produit.',
    created_at: '2024-01-15T10:30:00Z',
    price: 15000
  },
  {
    id: '2',
    user_id: 'user2',
    business_id: 'business1',
    username: '<PERSON>',
    product_name: 'Sérum Anti-âge',
    business_name: '<PERSON><PERSON>',
    category: '<PERSON><PERSON>',
    rating: 2,
    description: 'Déçu par ce produit. La qualité n\'est pas au rendez-vous et le prix est trop élevé pour ce que c\'est.',
    created_at: '2024-01-10T14:20:00Z',
    price: 25000
  },
  {
    id: '3',
    user_id: 'user3',
    business_id: 'business1',
    username: '<PERSON>',
    product_name: '<PERSON><PERSON>',
    business_name: '<PERSON><PERSON>',
    category: 'Bien-être',
    rating: 4,
    description: 'Bonne huile essentielle, mais j\'aimerais qu\'il y ait plus de variétés disponibles. Manque d\'eucalyptus.',
    created_at: '2024-01-08T16:45:00Z',
    price: 8000
  },
  {
    id: '4',
    user_id: 'user4',
    business_id: 'business1',
    username: 'Pierre Durand',
    product_name: 'Crème Hydratante Olgane',
    business_name: 'Dexima',
    category: 'Beauté',
    rating: 5,
    description: 'Parfait ! Exactement ce que je cherchais. Livraison rapide et produit de qualité.',
    created_at: '2024-01-05T09:15:00Z',
    price: 15000
  },
  {
    id: '5',
    user_id: 'user5',
    business_id: 'business1',
    username: 'Amélie Rousseau',
    product_name: 'Masque Purifiant',
    business_name: 'Dexima',
    category: 'Beauté',
    rating: 3,
    description: 'Produit correct mais l\'emballage était abîmé à la livraison. Besoin d\'améliorer la protection.',
    created_at: '2024-01-03T11:30:00Z',
    price: 12000
  },
  {
    id: '6',
    user_id: 'user6',
    business_id: 'business1',
    username: 'Thomas Moreau',
    product_name: 'Complément Alimentaire Vitamine C',
    business_name: 'Dexima',
    category: 'Santé',
    rating: 4,
    description: 'Bon produit, mais je souhaiterais qu\'il y ait une version bio disponible.',
    created_at: '2024-01-01T13:20:00Z',
    price: 18000
  },
  {
    id: '7',
    user_id: 'user7',
    business_id: 'business1',
    username: 'Lucie Bernard',
    product_name: 'Shampoing Naturel',
    business_name: 'Dexima',
    category: 'Beauté',
    rating: 1,
    description: 'Très déçue ! Le produit a causé des irritations. Problème de qualité majeur.',
    created_at: '2023-12-28T15:45:00Z',
    price: 10000
  },
  {
    id: '8',
    user_id: 'user8',
    business_id: 'business1',
    username: 'Nicolas Petit',
    product_name: 'Crème Hydratante Olgane',
    business_name: 'Dexima',
    category: 'Beauté',
    rating: 5,
    description: 'Troisième achat ! Ce produit est devenu indispensable dans ma routine beauté.',
    created_at: '2023-12-25T08:30:00Z',
    price: 15000
  },
  {
    id: '9',
    user_id: 'user9',
    business_id: 'business1',
    username: 'Isabelle Leroy',
    product_name: 'Gel Douche Relaxant',
    business_name: 'Dexima',
    category: 'Bien-être',
    rating: 4,
    description: 'Très agréable à utiliser. Seul bémol : le parfum pourrait être plus durable.',
    created_at: '2023-12-20T12:10:00Z',
    price: 7000
  },
  {
    id: '10',
    user_id: 'user10',
    business_id: 'business1',
    username: 'François Garnier',
    product_name: 'Baume Réparateur',
    business_name: 'Dexima',
    category: 'Beauté',
    rating: 2,
    description: 'Le produit ne correspond pas à la description. Texture trop grasse et efficacité limitée.',
    created_at: '2023-12-18T14:55:00Z',
    price: 20000
  }
];

export const MOCK_MARKETPLACE_PRODUCTS = [
  {
    id: 'prod1',
    business_id: 'business1',
    name: 'Crème Hydratante Olgane',
    category: 'Beauté',
    price: 15000,
    description: 'Crème hydratante naturelle pour tous types de peau',
    marketcomments: [
      {
        id: 'comment1',
        product_id: 'prod1',
        user_id: 'user1',
        comment: 'Excellente crème, très hydratante',
        rating: 5,
        created_at: '2024-01-15T10:30:00Z'
      },
      {
        id: 'comment2',
        product_id: 'prod1',
        user_id: 'user4',
        comment: 'Parfait pour ma peau sensible',
        rating: 5,
        created_at: '2024-01-05T09:15:00Z'
      }
    ],
    marketrating: [
      { id: 'rating1', product_id: 'prod1', user_id: 'user1', rating: 5 },
      { id: 'rating2', product_id: 'prod1', user_id: 'user4', rating: 5 },
      { id: 'rating3', product_id: 'prod1', user_id: 'user8', rating: 5 }
    ]
  },
  {
    id: 'prod2',
    business_id: 'business1',
    name: 'Sérum Anti-âge',
    category: 'Beauté',
    price: 25000,
    description: 'Sérum anti-âge aux actifs naturels',
    marketcomments: [
      {
        id: 'comment3',
        product_id: 'prod2',
        user_id: 'user2',
        comment: 'Déçu par la qualité, prix trop élevé',
        rating: 2,
        created_at: '2024-01-10T14:20:00Z'
      }
    ],
    marketrating: [
      { id: 'rating4', product_id: 'prod2', user_id: 'user2', rating: 2 }
    ]
  },
  {
    id: 'prod3',
    business_id: 'business1',
    name: 'Shampoing Naturel',
    category: 'Beauté',
    price: 10000,
    description: 'Shampoing naturel sans sulfates',
    marketcomments: [
      {
        id: 'comment4',
        product_id: 'prod3',
        user_id: 'user7',
        comment: 'A causé des irritations, problème de qualité',
        rating: 1,
        created_at: '2023-12-28T15:45:00Z'
      }
    ],
    marketrating: [
      { id: 'rating5', product_id: 'prod3', user_id: 'user7', rating: 1 }
    ]
  }
];

export const MOCK_BUSINESS_RATINGS = [
  { rating: 5, created_at: '2024-01-15T10:30:00Z', user_id: 'user1', category: 'Beauté' },
  { rating: 2, created_at: '2024-01-10T14:20:00Z', user_id: 'user2', category: 'Beauté' },
  { rating: 4, created_at: '2024-01-08T16:45:00Z', user_id: 'user3', category: 'Bien-être' },
  { rating: 5, created_at: '2024-01-05T09:15:00Z', user_id: 'user4', category: 'Beauté' },
  { rating: 3, created_at: '2024-01-03T11:30:00Z', user_id: 'user5', category: 'Beauté' },
  { rating: 4, created_at: '2024-01-01T13:20:00Z', user_id: 'user6', category: 'Santé' },
  { rating: 1, created_at: '2023-12-28T15:45:00Z', user_id: 'user7', category: 'Beauté' },
  { rating: 5, created_at: '2023-12-25T08:30:00Z', user_id: 'user8', category: 'Beauté' },
  { rating: 4, created_at: '2023-12-20T12:10:00Z', user_id: 'user9', category: 'Bien-être' },
  { rating: 2, created_at: '2023-12-18T14:55:00Z', user_id: 'user10', category: 'Beauté' }
];

// Fonction pour simuler les appels à la base de données
export const getMockBusinessData = (businessId: string) => {
  return {
    posts: MOCK_BUSINESS_POSTS.filter(post => post.business_id === businessId),
    products: MOCK_MARKETPLACE_PRODUCTS.filter(product => product.business_id === businessId),
    ratings: MOCK_BUSINESS_RATINGS
  };
};

// Fonction pour générer des recommandations de test
export const generateMockRecommendations = (businessId: string) => {
  return [
    {
      id: 'rec1',
      type: 'product_improvement' as const,
      priority: 'urgent' as const,
      title: 'Améliorer le Sérum Anti-âge',
      description: 'Le Sérum Anti-âge présente des problèmes de satisfaction client avec une note moyenne de 2.0/5. Les clients se plaignent du rapport qualité-prix.',
      expectedImpact: 'Augmentation potentielle de la satisfaction de 2-3 points et réduction des retours de 40-60%',
      implementationDifficulty: 'medium' as const,
      estimatedCost: 'medium' as const,
      timeframe: '2-3 mois',
      kpis: ['Note moyenne du produit', 'Taux de retour', 'NPS'],
      actionSteps: [
        'Analyser en détail les problèmes de qualité signalés',
        'Revoir la formulation du produit',
        'Ajuster la stratégie de prix',
        'Tester les améliorations avec un groupe pilote'
      ],
      dataSupport: {
        customerFeedback: 85,
        marketDemand: 70,
        competitiveAdvantage: 60
      },
      confidence: 88,
      category: 'Beauté'
    },
    {
      id: 'rec2',
      type: 'new_product' as const,
      priority: 'high' as const,
      title: 'Lancer une gamme Bio',
      description: 'Forte demande détectée pour des produits bio dans les commentaires clients. Opportunité de marché identifiée.',
      expectedImpact: 'Augmentation des ventes de 25-40% et attraction de nouveaux segments clients',
      implementationDifficulty: 'hard' as const,
      estimatedCost: 'high' as const,
      timeframe: '6-9 mois',
      kpis: ['CA gamme bio', 'Nouveaux clients', 'Part de marché bio'],
      actionSteps: [
        'Étudier la faisabilité de certification bio',
        'Développer 3-5 produits pilotes',
        'Tester le marché avec un lancement limité',
        'Déployer la gamme complète'
      ],
      dataSupport: {
        customerFeedback: 75,
        marketDemand: 90,
        competitiveAdvantage: 80
      },
      confidence: 82,
      category: 'Beauté'
    },
    {
      id: 'rec3',
      type: 'customer_retention' as const,
      priority: 'medium' as const,
      title: 'Programme de fidélisation',
      description: 'Plusieurs clients fidèles identifiés (comme Nicolas Petit avec 3 achats). Opportunité de créer un programme de fidélisation.',
      expectedImpact: 'Augmentation de la rétention de 30% et de la valeur vie client de 20%',
      implementationDifficulty: 'easy' as const,
      estimatedCost: 'low' as const,
      timeframe: '1-2 mois',
      kpis: ['Taux de rétention', 'Fréquence d\'achat', 'Valeur panier'],
      actionSteps: [
        'Identifier les clients les plus fidèles',
        'Créer un système de points/récompenses',
        'Lancer des offres exclusives',
        'Mesurer l\'impact sur la fidélité'
      ],
      dataSupport: {
        customerFeedback: 80,
        marketDemand: 70,
        competitiveAdvantage: 75
      },
      confidence: 78,
      category: 'Fidélisation'
    }
  ];
};

// Fonction pour générer des propositions de nouveaux produits
export const generateMockProductProposals = (businessId: string) => {
  return [
    {
      id: 'newprod1',
      proposedName: 'Gamme Bio Essentielle',
      category: 'Beauté',
      targetMarket: ['Clients soucieux de l\'environnement', 'Segment premium', 'Nouvelles générations'],
      keyFeatures: [
        'Certification bio officielle',
        'Ingrédients 100% naturels',
        'Emballage recyclable',
        'Formules hypoallergéniques'
      ],
      uniqueSellingPoints: [
        'Premier sur le marché local avec certification complète',
        'Traçabilité totale des ingrédients',
        'Prix accessible pour du bio premium'
      ],
      estimatedDemand: 85,
      suggestedPriceRange: { min: 18000, max: 35000 },
      developmentComplexity: 'high' as const,
      marketGap: 'Manque de produits bio accessibles et certifiés dans la région',
      customerNeedsSatisfied: [
        'Besoin de produits naturels sans chimiques',
        'Demande de transparence sur les ingrédients',
        'Souhait de soutenir l\'écologie'
      ],
      competitiveAdvantages: [
        'Connaissance approfondie des clients locaux',
        'Réseau de distribution établi',
        'Réputation de qualité existante'
      ],
      riskFactors: [
        'Coût de certification bio élevé',
        'Concurrence des grandes marques',
        'Approvisionnement en ingrédients bio'
      ],
      successProbability: 78,
      investmentRequired: '200 000 - 500 000 F CFA',
      timeToMarket: '6-9 mois',
      targetRevenue: '2 500 000 F CFA/an'
    },
    {
      id: 'newprod2',
      proposedName: 'Kit Découverte Bien-être',
      category: 'Bien-être',
      targetMarket: ['Nouveaux clients', 'Cadeaux d\'entreprise', 'Marché des coffrets'],
      keyFeatures: [
        'Sélection de 5 produits phares',
        'Format voyage pratique',
        'Guide d\'utilisation inclus',
        'Emballage cadeau premium'
      ],
      uniqueSellingPoints: [
        'Point d\'entrée idéal pour découvrir la marque',
        'Excellent rapport qualité-prix',
        'Parfait pour les cadeaux'
      ],
      estimatedDemand: 65,
      suggestedPriceRange: { min: 25000, max: 40000 },
      developmentComplexity: 'low' as const,
      marketGap: 'Absence d\'offre découverte accessible pour nouveaux clients',
      customerNeedsSatisfied: [
        'Tester plusieurs produits sans gros investissement',
        'Trouver des idées cadeaux originales',
        'Découvrir de nouvelles routines bien-être'
      ],
      competitiveAdvantages: [
        'Utilisation des produits existants',
        'Marge attractive sur les coffrets',
        'Acquisition de nouveaux clients'
      ],
      riskFactors: [
        'Cannibalisation des ventes individuelles',
        'Complexité logistique des coffrets',
        'Saisonnalité des ventes'
      ],
      successProbability: 72,
      investmentRequired: '50 000 - 150 000 F CFA',
      timeToMarket: '2-3 mois',
      targetRevenue: '1 200 000 F CFA/an'
    }
  ];
};

// Fonction pour générer des insights clients
export const generateMockCustomerInsights = (businessId: string) => {
  return {
    totalCustomers: 10,
    averageSatisfaction: 3.6,
    loyaltyDistribution: {
      'Nouveaux (1 interaction)': 6,
      'Occasionnels (2-3 interactions)': 3,
      'Réguliers (4-7 interactions)': 1,
      'Fidèles (8+ interactions)': 0
    },
    topComplaints: [
      { issue: 'qualité', frequency: 3 },
      { issue: 'prix', frequency: 2 },
      { issue: 'emballage', frequency: 1 }
    ],
    emergingTrends: [
      'Forte demande pour des produits bio et naturels',
      'Intérêt croissant pour les huiles essentielles',
      'Besoin de formats voyage et découverte'
    ],
    churnRisk: 25,
    growthOpportunities: [
      'Développer l\'offre en produits bio (forte satisfaction, demande croissante)',
      'Créer des coffrets découverte pour attirer nouveaux clients',
      'Améliorer l\'emballage pour réduire les problèmes de livraison'
    ]
  };
};
