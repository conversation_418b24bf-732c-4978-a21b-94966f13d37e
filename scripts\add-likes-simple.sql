-- Script simplifié pour ajouter la fonctionnalité de likes aux posts
-- Version sans modification de la vue pour éviter les erreurs SQL

-- =====================================================
-- 1. AJOUTER LES COLONNES LIKES ET SHARES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Ajout des colonnes likes et shares à la table posts...';
    
    -- Ajout de la colonne likes
    ALTER TABLE posts
    ADD COLUMN IF NOT EXISTS likes UUID[] DEFAULT ARRAY[]::UUID[];
    
    -- Ajout de la colonne shares
    ALTER TABLE posts
    ADD COLUMN IF NOT EXISTS shares UUID[] DEFAULT ARRAY[]::UUID[];
    
    RAISE NOTICE '✅ Colonnes likes et shares ajoutées avec succès';
END $$;

-- =====================================================
-- 2. AJOUTER LES COMMENTAIRES EXPLICATIFS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📝 Ajout des commentaires explicatifs...';
    
    -- Commentaires sur les colonnes
    COMMENT ON COLUMN posts.likes IS 'Liste des UUIDs des utilisateurs qui ont aimé ce post';
    COMMENT ON COLUMN posts.shares IS 'Liste des UUIDs des utilisateurs qui ont partagé ce post';
    
    RAISE NOTICE '✅ Commentaires ajoutés';
END $$;

-- =====================================================
-- 3. CRÉER LES INDEX POUR LES PERFORMANCES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🚀 Création des index pour optimiser les performances...';
    
    -- Index GIN pour les likes (recherches rapides dans les tableaux)
    CREATE INDEX IF NOT EXISTS posts_likes_idx ON posts USING GIN (likes);
    
    -- Index GIN pour les recommendations (si pas déjà créé)
    CREATE INDEX IF NOT EXISTS posts_recommendations_idx ON posts USING GIN (recommendations);
    
    -- Index GIN pour les shares
    CREATE INDEX IF NOT EXISTS posts_shares_idx ON posts USING GIN (shares);
    
    RAISE NOTICE '✅ Index créés avec succès';
END $$;

-- =====================================================
-- 4. INITIALISER LES DONNÉES EXISTANTES
-- =====================================================

DO $$
DECLARE
    posts_updated INTEGER;
BEGIN
    RAISE NOTICE '🔄 Initialisation des données existantes...';
    
    -- Initialiser les colonnes pour tous les posts existants
    UPDATE posts 
    SET 
        likes = COALESCE(likes, ARRAY[]::UUID[]),
        shares = COALESCE(shares, ARRAY[]::UUID[])
    WHERE likes IS NULL OR shares IS NULL;
    
    GET DIAGNOSTICS posts_updated = ROW_COUNT;
    
    RAISE NOTICE '✅ % posts mis à jour avec les nouvelles colonnes', posts_updated;
END $$;

-- =====================================================
-- 5. VÉRIFICATION ET TESTS
-- =====================================================

DO $$
DECLARE
    total_posts INTEGER;
    posts_with_likes INTEGER;
    posts_with_shares INTEGER;
    sample_post RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 VÉRIFICATION FINALE';
    RAISE NOTICE '====================';
    
    -- Compter le total de posts
    SELECT COUNT(*) INTO total_posts FROM posts;
    
    -- Compter les posts avec la colonne likes initialisée
    SELECT COUNT(*) INTO posts_with_likes FROM posts WHERE likes IS NOT NULL;
    
    -- Compter les posts avec la colonne shares initialisée
    SELECT COUNT(*) INTO posts_with_shares FROM posts WHERE shares IS NOT NULL;
    
    RAISE NOTICE 'Total posts: %', total_posts;
    RAISE NOTICE 'Posts avec likes: %', posts_with_likes;
    RAISE NOTICE 'Posts avec shares: %', posts_with_shares;
    
    -- Prendre un échantillon pour vérifier la structure
    SELECT id, likes, shares, recommendations 
    INTO sample_post
    FROM posts 
    LIMIT 1;
    
    IF sample_post.id IS NOT NULL THEN
        RAISE NOTICE '';
        RAISE NOTICE '📋 ÉCHANTILLON DE POST:';
        RAISE NOTICE 'ID: %', sample_post.id;
        RAISE NOTICE 'Likes: %', sample_post.likes;
        RAISE NOTICE 'Shares: %', sample_post.shares;
        RAISE NOTICE 'Recommendations: %', sample_post.recommendations;
    END IF;
    
    -- Vérification finale
    IF posts_with_likes = total_posts AND posts_with_shares = total_posts THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 MIGRATION RÉUSSIE !';
        RAISE NOTICE '=====================';
        RAISE NOTICE '✅ Tous les posts ont les nouvelles colonnes';
        RAISE NOTICE '✅ Les index sont créés';
        RAISE NOTICE '✅ Le système de likes est opérationnel';
        RAISE NOTICE '';
        RAISE NOTICE '🚀 Vous pouvez maintenant tester le bouton "J''aime" !';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '⚠️ ATTENTION: Certains posts n''ont pas été mis à jour correctement';
    END IF;
END $$;

-- =====================================================
-- 6. INSTRUCTIONS FINALES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📋 PROCHAINES ÉTAPES:';
    RAISE NOTICE '====================';
    RAISE NOTICE '1. Redémarrez votre application React (npm run dev)';
    RAISE NOTICE '2. Connectez-vous avec un compte utilisateur standard';
    RAISE NOTICE '3. Cliquez sur le bouton "J''aime" d''un post';
    RAISE NOTICE '4. Vérifiez que le compteur se met à jour';
    RAISE NOTICE '5. Consultez les logs dans la console du navigateur';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 Le bouton "J''aime" devrait maintenant fonctionner !';
END $$;
