-- SCRIPT DE NETTOYAGE ET REDÉPLOIEMENT
-- À utiliser si vous avez des erreurs de contraintes
-- Exécutez ce script dans Supabase SQL Editor

-- =====================================================
-- 1. NETTOYAGE SÉCURISÉ DES DONNÉES EXISTANTES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🧹 Début du nettoyage des données existantes...';
    
    -- Désactiver temporairement les contraintes de clés étrangères
    SET session_replication_role = replica;
    
    -- Supprimer les données dans l'ordre correct (enfants d'abord)
    
    -- 1. Supprimer les codes d'abonnement
    DROP TABLE IF EXISTS subscription_codes CASCADE;
    RAISE NOTICE '🗑️ Table subscription_codes supprimée';
    
    -- 2. Supprimer les abonnements business
    DROP TABLE IF EXISTS business_subscriptions CASCADE;
    RAISE NOTICE '🗑️ Table business_subscriptions supprimée';
    
    -- 3. Supprimer les plans d'abonnement
    DROP TABLE IF EXISTS subscription_plans CASCADE;
    RAISE NOTICE '🗑️ Table subscription_plans supprimée';
    
    -- Supprimer les fonctions existantes
    DROP FUNCTION IF EXISTS has_used_trial(UUID) CASCADE;
    DROP FUNCTION IF EXISTS get_current_subscription(UUID) CASCADE;
    DROP FUNCTION IF EXISTS create_subscription(UUID, VARCHAR, BOOLEAN) CASCADE;
    DROP FUNCTION IF EXISTS cancel_subscription(UUID, TEXT) CASCADE;
    DROP FUNCTION IF EXISTS generate_subscription_code() CASCADE;
    DROP FUNCTION IF EXISTS create_subscription_code(UUID, VARCHAR, VARCHAR, INTEGER) CASCADE;
    DROP FUNCTION IF EXISTS validate_subscription_code(UUID, UUID, TEXT) CASCADE;
    DROP FUNCTION IF EXISTS reject_subscription_code(UUID, UUID, TEXT) CASCADE;
    DROP FUNCTION IF EXISTS cleanup_expired_subscription_codes() CASCADE;
    DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
    RAISE NOTICE '🗑️ Fonctions supprimées';
    
    -- Supprimer les vues
    DROP VIEW IF EXISTS active_subscriptions CASCADE;
    DROP VIEW IF EXISTS subscription_stats CASCADE;
    DROP VIEW IF EXISTS subscription_codes_stats CASCADE;
    DROP VIEW IF EXISTS subscription_codes_detailed CASCADE;
    RAISE NOTICE '🗑️ Vues supprimées';
    
    -- Réactiver les contraintes
    SET session_replication_role = DEFAULT;
    
    RAISE NOTICE '✅ Nettoyage terminé avec succès';
    
END $$;

-- =====================================================
-- 2. RECRÉATION COMPLÈTE DES TABLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🏗️ Recréation des tables...';
    
    -- Table des plans d'abonnement
    CREATE TABLE subscription_plans (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        currency VARCHAR(3) NOT NULL DEFAULT 'XOF',
        duration_days INTEGER NOT NULL DEFAULT 30,
        features JSONB NOT NULL DEFAULT '[]',
        is_active BOOLEAN NOT NULL DEFAULT true,
        is_trial BOOLEAN NOT NULL DEFAULT false,
        plan_type VARCHAR(20) NOT NULL DEFAULT 'monthly' CHECK (plan_type IN ('trial', 'monthly', 'yearly')),
        original_price DECIMAL(10,2),
        savings_text VARCHAR(100),
        is_popular BOOLEAN NOT NULL DEFAULT false,
        max_reviews INTEGER,
        max_products INTEGER,
        has_ai_advisor BOOLEAN NOT NULL DEFAULT false,
        has_advanced_analytics BOOLEAN NOT NULL DEFAULT false,
        has_api_access BOOLEAN NOT NULL DEFAULT false,
        has_priority_support BOOLEAN NOT NULL DEFAULT false,
        has_phone_support BOOLEAN NOT NULL DEFAULT false,
        has_dedicated_manager BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Table des abonnements business
    CREATE TABLE business_subscriptions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        business_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
        plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(id),
        start_date TIMESTAMP WITH TIME ZONE NOT NULL,
        end_date TIMESTAMP WITH TIME ZONE NOT NULL,
        is_active BOOLEAN NOT NULL DEFAULT true,
        is_trial BOOLEAN NOT NULL DEFAULT false,
        auto_renew BOOLEAN NOT NULL DEFAULT false,
        trial_used BOOLEAN NOT NULL DEFAULT false,
        cancelled_at TIMESTAMP WITH TIME ZONE,
        cancellation_reason TEXT,
        payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
        payment_method VARCHAR(50),
        stripe_subscription_id VARCHAR(100),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Table des codes d'abonnement
    CREATE TABLE subscription_codes (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        code VARCHAR(8) NOT NULL UNIQUE,
        business_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
        business_name VARCHAR(255) NOT NULL,
        plan_type VARCHAR(20) NOT NULL CHECK (plan_type IN ('trial', 'monthly', 'yearly')),
        amount INTEGER NOT NULL DEFAULT 0,
        status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'validated', 'rejected', 'expired')),
        generated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        validated_at TIMESTAMP WITH TIME ZONE,
        validated_by UUID,
        rejection_reason TEXT,
        admin_notes TEXT,
        payment_reference VARCHAR(100),
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
    );

    -- Index pour les performances
    CREATE INDEX idx_business_subscriptions_business_id ON business_subscriptions(business_id);
    CREATE INDEX idx_business_subscriptions_active ON business_subscriptions(is_active);
    CREATE INDEX idx_business_subscriptions_trial ON business_subscriptions(is_trial);
    CREATE INDEX idx_business_subscriptions_end_date ON business_subscriptions(end_date);
    CREATE INDEX idx_subscription_codes_business_id ON subscription_codes(business_id);
    CREATE INDEX idx_subscription_codes_status ON subscription_codes(status);
    CREATE INDEX idx_subscription_codes_expires_at ON subscription_codes(expires_at);
    CREATE INDEX idx_subscription_codes_code ON subscription_codes(code);
    CREATE INDEX idx_subscription_codes_generated_at ON subscription_codes(generated_at);
    
    RAISE NOTICE '✅ Tables recréées avec succès';
    
END $$;

-- =====================================================
-- 3. INSERTION DES PLANS D'ABONNEMENT
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📋 Insertion des plans d''abonnement...';
    
    -- Plan Essai Gratuit 7 jours
    INSERT INTO subscription_plans (
        id, name, description, price, currency, duration_days, 
        plan_type, is_trial, is_popular, features,
        has_ai_advisor, has_advanced_analytics, has_api_access, 
        has_priority_support, has_phone_support, has_dedicated_manager
    ) VALUES (
        'trial-7days',
        'Essai Gratuit',
        'Découvrez toutes nos fonctionnalités pendant 7 jours',
        0.00,
        'XOF',
        7,
        'trial',
        true,
        false,
        '[
            "Accès complet à toutes les fonctionnalités",
            "Gestion illimitée des avis clients", 
            "Tableaux de bord interactifs",
            "Notifications en temps réel",
            "Support par email",
            "Aucun engagement"
        ]'::jsonb,
        true, true, false, false, false, false
    );
    
    -- Plan Mensuel
    INSERT INTO subscription_plans (
        id, name, description, price, currency, duration_days,
        plan_type, is_trial, is_popular, features,
        has_ai_advisor, has_advanced_analytics, has_api_access,
        has_priority_support, has_phone_support, has_dedicated_manager
    ) VALUES (
        'monthly-plan',
        'Abonnement Mensuel', 
        'Plan mensuel flexible pour votre entreprise',
        25000.00,
        'XOF',
        30,
        'monthly',
        false,
        false,
        '[
            "Toutes les fonctionnalités incluses",
            "Gestion illimitée des avis",
            "Analytics avancées", 
            "Support prioritaire",
            "Intégrations API",
            "Sauvegarde automatique",
            "Rapports personnalisés"
        ]'::jsonb,
        true, true, true, true, false, false
    );
    
    -- Plan Annuel (avec réduction)
    INSERT INTO subscription_plans (
        id, name, description, price, original_price, currency, duration_days,
        plan_type, is_trial, is_popular, savings_text, features,
        has_ai_advisor, has_advanced_analytics, has_api_access,
        has_priority_support, has_phone_support, has_dedicated_manager
    ) VALUES (
        'yearly-plan',
        'Abonnement Annuel',
        'Économisez 20% avec notre plan annuel', 
        240000.00,
        300000.00,
        'XOF',
        365,
        'yearly',
        false,
        true,
        'Économisez 60 000 F CFA',
        '[
            "Toutes les fonctionnalités Premium",
            "Gestion illimitée des avis",
            "Analytics avancées + IA",
            "Support prioritaire 24/7", 
            "Intégrations API complètes",
            "Sauvegarde automatique",
            "Rapports personnalisés",
            "Conseiller dédié",
            "Formation personnalisée"
        ]'::jsonb,
        true, true, true, true, true, true
    );
    
    RAISE NOTICE '✅ Plans d''abonnement insérés';
    
END $$;

-- =====================================================
-- 4. CRÉATION DES FONCTIONS ESSENTIELLES
-- =====================================================

-- Fonction pour générer un code d'abonnement unique
CREATE OR REPLACE FUNCTION generate_subscription_code()
RETURNS VARCHAR(8) AS $$
DECLARE
    new_code VARCHAR(8);
    code_exists BOOLEAN;
BEGIN
    LOOP
        -- Générer un code de 8 chiffres
        new_code := LPAD(FLOOR(RANDOM() * 100000000)::TEXT, 8, '0');
        
        -- Vérifier si le code existe déjà
        SELECT EXISTS(SELECT 1 FROM subscription_codes WHERE code = new_code) INTO code_exists;
        
        -- Si le code n'existe pas, on peut l'utiliser
        IF NOT code_exists THEN
            EXIT;
        END IF;
    END LOOP;
    
    RETURN new_code;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour créer un code d'abonnement
CREATE OR REPLACE FUNCTION create_subscription_code(
    p_business_id UUID,
    p_business_name VARCHAR(255),
    p_plan_type VARCHAR(20),
    p_amount INTEGER
)
RETURNS TABLE(
    code_id UUID,
    code VARCHAR(8),
    expires_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    new_code VARCHAR(8);
    expiration_time TIMESTAMP WITH TIME ZONE;
    code_id UUID;
BEGIN
    -- Générer un nouveau code unique
    new_code := generate_subscription_code();
    
    -- Définir l'expiration (24 heures)
    expiration_time := NOW() + INTERVAL '24 hours';
    
    -- Insérer le code dans la base de données
    INSERT INTO subscription_codes (
        business_id, business_name, plan_type, amount, code, expires_at
    ) VALUES (
        p_business_id, p_business_name, p_plan_type, p_amount, new_code, expiration_time
    ) RETURNING id INTO code_id;
    
    -- Retourner les informations du code créé
    RETURN QUERY SELECT code_id, new_code, expiration_time;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. MESSAGE DE SUCCÈS FINAL
-- =====================================================

DO $$
DECLARE
    total_plans integer;
    total_codes integer;
BEGIN
    SELECT COUNT(*) INTO total_plans FROM subscription_plans;
    SELECT COUNT(*) INTO total_codes FROM subscription_codes;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '🎉 NETTOYAGE ET REDÉPLOIEMENT RÉUSSI !';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '💳 CONFIGURATION PROPRE:';
    RAISE NOTICE '   📋 Plans d''abonnement: %', total_plans;
    RAISE NOTICE '   🔐 Codes d''abonnement: %', total_codes;
    RAISE NOTICE '';
    RAISE NOTICE '✅ Toutes les contraintes résolues';
    RAISE NOTICE '✅ Tables recréées proprement';
    RAISE NOTICE '✅ Fonctions opérationnelles';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 SYSTÈME PRÊT POUR LES TESTS !';
    RAISE NOTICE '';
    RAISE NOTICE '📝 PROCHAINES ÉTAPES:';
    RAISE NOTICE '1. Tester la génération de codes';
    RAISE NOTICE '2. Tester la validation admin';
    RAISE NOTICE '3. Vérifier les interfaces';
    RAISE NOTICE '';
END $$;
