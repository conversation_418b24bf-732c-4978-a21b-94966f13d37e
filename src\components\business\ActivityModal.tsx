import React, { useState, useEffect } from 'react';
import {
  X, Activity, ShoppingBag, Star, Package, MessageSquare, DollarSign,
  Calendar, Filter, Search, Download, RefreshCw, Eye, Clock,
  TrendingUp, AlertTriangle, CheckCircle, Info
} from 'lucide-react';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';
import { formatAmount } from '../../utils/formatUtils';
import { RecentActivity } from '../../services/businessDashboardService';

interface ActivityModalProps {
  isOpen: boolean;
  onClose: () => void;
  activities: RecentActivity[];
  businessName: string;
  onRefresh: () => void;
}

const ActivityModal: React.FC<ActivityModalProps> = ({
  isOpen,
  onClose,
  activities,
  businessName,
  onRefresh
}) => {
  const [filteredActivities, setFilteredActivities] = useState<RecentActivity[]>(activities);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'order' | 'review' | 'product' | 'message'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'success' | 'warning' | 'info' | 'error'>('all');
  const [sortBy, setSortBy] = useState<'date' | 'type' | 'status'>('date');

  useEffect(() => {
    let filtered = activities;

    // Filtrer par recherche
    if (searchTerm) {
      filtered = filtered.filter(activity =>
        activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtrer par type
    if (typeFilter !== 'all') {
      filtered = filtered.filter(activity => activity.type === typeFilter);
    }

    // Filtrer par statut
    if (statusFilter !== 'all') {
      filtered = filtered.filter(activity => activity.status === statusFilter);
    }

    // Trier
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
        case 'type':
          return a.type.localeCompare(b.type);
        case 'status':
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });

    setFilteredActivities(filtered);
  }, [activities, searchTerm, typeFilter, statusFilter, sortBy]);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order':
        return <ShoppingBag size={16} className="text-green-600" />;
      case 'review':
        return <Star size={16} className="text-yellow-600" />;
      case 'product':
        return <Package size={16} className="text-blue-600" />;
      case 'message':
        return <MessageSquare size={16} className="text-purple-600" />;
      default:
        return <Activity size={16} className="text-gray-600" />;
    }
  };

  const getActivityBgColor = (type: string) => {
    switch (type) {
      case 'order':
        return 'bg-green-100';
      case 'review':
        return 'bg-yellow-100';
      case 'product':
        return 'bg-blue-100';
      case 'message':
        return 'bg-purple-100';
      default:
        return 'bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle size={14} className="text-green-600" />;
      case 'warning':
        return <AlertTriangle size={14} className="text-yellow-600" />;
      case 'error':
        return <X size={14} className="text-red-600" />;
      case 'info':
      default:
        return <Info size={14} className="text-blue-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      success: { bg: 'bg-green-100', text: 'text-green-800', label: 'Succès' },
      warning: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Attention' },
      info: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Info' },
      error: { bg: 'bg-red-100', text: 'text-red-800', label: 'Erreur' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.info;
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {getStatusIcon(status)}
        <span className="ml-1">{config.label}</span>
      </span>
    );
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Il y a moins d\'une heure';
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `Il y a ${diffInDays}j`;
    const diffInWeeks = Math.floor(diffInDays / 7);
    return `Il y a ${diffInWeeks} semaine${diffInWeeks > 1 ? 's' : ''}`;
  };

  const exportActivities = () => {
    const csvContent = [
      ['Date', 'Type', 'Titre', 'Description', 'Statut', 'Montant'],
      ...filteredActivities.map(activity => [
        activity.timestamp.toLocaleDateString('fr-FR'),
        activity.type,
        activity.title,
        activity.description,
        activity.status,
        activity.amount ? formatAmount(activity.amount) : ''
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `activite-${businessName}-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Activity size={24} />
              <div>
                <h2 className="text-xl font-bold">Activité récente - {businessName}</h2>
                <p className="text-blue-100 text-sm">
                  {filteredActivities.length} activité{filteredActivities.length > 1 ? 's' : ''} trouvée{filteredActivities.length > 1 ? 's' : ''}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={onRefresh}
                size="sm"
                className="bg-white/10 hover:bg-white/20 border-white/20 text-white"
              >
                <RefreshCw size={16} />
              </Button>
              <Button
                onClick={exportActivities}
                size="sm"
                className="bg-white/10 hover:bg-white/20 border-white/20 text-white"
              >
                <Download size={16} />
              </Button>
              <Button
                onClick={onClose}
                size="sm"
                className="bg-white/10 hover:bg-white/20 border-white/20 text-white"
              >
                <X size={16} />
              </Button>
            </div>
          </div>
        </div>

        {/* Filtres */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Recherche */}
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Filtre par type */}
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Tous les types</option>
              <option value="order">Commandes</option>
              <option value="review">Avis</option>
              <option value="product">Produits</option>
              <option value="message">Messages</option>
            </select>

            {/* Filtre par statut */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Tous les statuts</option>
              <option value="success">Succès</option>
              <option value="warning">Attention</option>
              <option value="info">Info</option>
              <option value="error">Erreur</option>
            </select>

            {/* Tri */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="date">Trier par date</option>
              <option value="type">Trier par type</option>
              <option value="status">Trier par statut</option>
            </select>
          </div>
        </div>

        {/* Liste des activités */}
        <div className="flex-1 overflow-y-auto p-6" style={{ maxHeight: 'calc(90vh - 200px)' }}>
          {filteredActivities.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Activity className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune activité trouvée</h3>
              <p className="text-sm text-gray-500">
                Essayez de modifier vos filtres ou d'actualiser les données.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredActivities.map((activity, index) => (
                <Card key={activity.id} className="hover:shadow-md transition-shadow">
                  <CardBody className="p-4">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className={`w-12 h-12 ${getActivityBgColor(activity.type)} rounded-full flex items-center justify-center`}>
                          {getActivityIcon(activity.type)}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h4 className="text-sm font-medium text-gray-900">{activity.title}</h4>
                              {getStatusBadge(activity.status)}
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{activity.description}</p>
                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <div className="flex items-center space-x-1">
                                <Clock size={12} />
                                <span>{formatTimeAgo(activity.timestamp)}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Calendar size={12} />
                                <span>{activity.timestamp.toLocaleDateString('fr-FR')}</span>
                              </div>
                            </div>
                          </div>
                          {activity.amount && (
                            <div className="text-right">
                              <p className="text-sm font-medium text-green-600 flex items-center">
                                <DollarSign size={12} className="mr-1" />
                                {formatAmount(activity.amount)}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ActivityModal;
