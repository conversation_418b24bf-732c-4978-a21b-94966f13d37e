import React, { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Download } from 'lucide-react';
import Button from '../components/ui/Button';
import OrderStatsCards from '../components/orders/OrderStatsCards';
import OrderCard from '../components/orders/OrderCard';
import OrderFilters from '../components/orders/OrderFilters';
import EmptyOrdersState from '../components/orders/EmptyOrdersState';
import OrderInsights from '../components/orders/OrderInsights';

// Type pour les commandes
interface Order {
  id: string;
  date: string;
  total: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  items: OrderItem[];
  trackingNumber?: string;
  estimatedDelivery?: string;
  shippingCost: number;
  paymentMethod: string;
  shippingAddress: {
    name: string;
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
  trackingHistory?: TrackingEvent[];
  canCancel: boolean;
  canReturn: boolean;
  hasReview: boolean;
}

// Type pour les articles de commande
interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  imageUrl: string;
  businessName: string;
  businessId: string;
  category: string;
  sku?: string;
}

// Type pour l'historique de suivi
interface TrackingEvent {
  date: string;
  status: string;
  description: string;
  location?: string;
}

// Type pour les statistiques
interface OrderStats {
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  pendingOrders: number;
  deliveredOrders: number;
}

const OrdersPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState<Order[]>([]);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [expandedOrderId, setExpandedOrderId] = useState<string | null>(null);
  const [stats, setStats] = useState<OrderStats | null>(null);

  useEffect(() => {
    // Si l'utilisateur est connecté, récupérer ses commandes
    if (currentUser) {
      // Dans une vraie application, nous récupérerions les commandes depuis la base de données
      // Pour cette simulation, nous créons des commandes fictives
      const mockOrders: Order[] = [
        {
          id: 'ORD-001',
          date: '2025-05-10T14:30:00Z',
          total: 35000,
          status: 'delivered',
          shippingCost: 2000,
          paymentMethod: 'Orange Money',
          estimatedDelivery: '2025-05-12T18:00:00Z',
          canCancel: false,
          canReturn: true,
          hasReview: false,
          items: [
            {
              id: 'ITEM-001',
              name: 'Huile de Beauté Olgane',
              quantity: 2,
              price: 15000,
              imageUrl: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=100&h=100&fit=crop',
              businessName: 'Olgane Cosmetics',
              businessId: 'BUS-001',
              category: 'Beauté',
              sku: 'OLG-HB-001'
            },
            {
              id: 'ITEM-002',
              name: 'Crème Hydratante Visage',
              quantity: 1,
              price: 5000,
              imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=100&h=100&fit=crop',
              businessName: 'Olgane Cosmetics',
              businessId: 'BUS-001',
              category: 'Cosmétiques',
              sku: 'OLG-CH-002'
            }
          ],
          trackingNumber: 'TRK123456789',
          trackingHistory: [
            {
              date: '2025-05-10T14:30:00Z',
              status: 'Commande confirmée',
              description: 'Votre commande a été confirmée et est en cours de préparation',
              location: 'Abidjan, Côte d\'Ivoire'
            },
            {
              date: '2025-05-11T09:15:00Z',
              status: 'Expédiée',
              description: 'Votre commande a été expédiée',
              location: 'Centre de tri Abidjan'
            },
            {
              date: '2025-05-12T16:45:00Z',
              status: 'Livrée',
              description: 'Commande livrée avec succès',
              location: 'Abidjan, Côte d\'Ivoire'
            }
          ],
          shippingAddress: {
            name: 'Kouassi Ange',
            street: '123 Rue Principale',
            city: 'Abidjan',
            postalCode: '00225',
            country: 'Côte d\'Ivoire'
          }
        },
        {
          id: 'ORD-002',
          date: '2025-05-05T10:15:00Z',
          total: 25000,
          status: 'processing',
          shippingCost: 1500,
          paymentMethod: 'MTN Money',
          estimatedDelivery: '2025-05-08T18:00:00Z',
          canCancel: true,
          canReturn: false,
          hasReview: false,
          items: [
            {
              id: 'ITEM-003',
              name: 'Savon Naturel au Karité',
              quantity: 3,
              price: 5000,
              imageUrl: 'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=100&h=100&fit=crop',
              businessName: 'Savonnerie Traditionnelle',
              businessId: 'BUS-002',
              category: 'Hygiène',
              sku: 'SAV-KAR-003'
            },
            {
              id: 'ITEM-004',
              name: 'Huile Essentielle de Citronnelle',
              quantity: 2,
              price: 5000,
              imageUrl: 'https://images.unsplash.com/photo-1594035910387-fea47794261f?w=100&h=100&fit=crop',
              businessName: 'Herbes et Traditions',
              businessId: 'BUS-003',
              category: 'Parfums',
              sku: 'HER-CIT-004'
            }
          ],
          trackingNumber: 'TRK987654321',
          trackingHistory: [
            {
              date: '2025-05-05T10:15:00Z',
              status: 'Commande confirmée',
              description: 'Votre commande a été confirmée',
              location: 'Abidjan, Côte d\'Ivoire'
            },
            {
              date: '2025-05-06T14:30:00Z',
              status: 'En préparation',
              description: 'Votre commande est en cours de préparation',
              location: 'Entrepôt Abidjan'
            }
          ],
          shippingAddress: {
            name: 'Kouassi Ange',
            street: '123 Rue Principale',
            city: 'Abidjan',
            postalCode: '00225',
            country: 'Côte d\'Ivoire'
          }
        },
        {
          id: 'ORD-003',
          date: '2025-04-28T16:20:00Z',
          total: 18000,
          status: 'shipped',
          shippingCost: 1000,
          paymentMethod: 'Visa',
          estimatedDelivery: '2025-05-02T18:00:00Z',
          canCancel: false,
          canReturn: false,
          hasReview: false,
          items: [
            {
              id: 'ITEM-005',
              name: 'Bracelet en Perles Traditionnelles',
              quantity: 1,
              price: 12000,
              imageUrl: 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=100&h=100&fit=crop',
              businessName: 'Artisanat Ivoirien',
              businessId: 'BUS-004',
              category: 'Bijoux',
              sku: 'ART-BRA-005'
            },
            {
              id: 'ITEM-006',
              name: 'Boucles d\'Oreilles Dorées',
              quantity: 1,
              price: 5000,
              imageUrl: 'https://images.unsplash.com/photo-1535632066927-ab7c9ab60908?w=100&h=100&fit=crop',
              businessName: 'Artisanat Ivoirien',
              businessId: 'BUS-004',
              category: 'Bijoux',
              sku: 'ART-BOU-006'
            }
          ],
          trackingNumber: 'TRK456789123',
          trackingHistory: [
            {
              date: '2025-04-28T16:20:00Z',
              status: 'Commande confirmée',
              description: 'Votre commande a été confirmée',
              location: 'Abidjan, Côte d\'Ivoire'
            },
            {
              date: '2025-04-29T11:30:00Z',
              status: 'Expédiée',
              description: 'Votre commande a été expédiée et est en route',
              location: 'Centre de tri Abidjan'
            }
          ],
          shippingAddress: {
            name: 'Kouassi Ange',
            street: '123 Rue Principale',
            city: 'Abidjan',
            postalCode: '00225',
            country: 'Côte d\'Ivoire'
          }
        }
      ];

      setOrders(mockOrders);

      // Calculer les statistiques
      const orderStats: OrderStats = {
        totalOrders: mockOrders.length,
        totalSpent: mockOrders.reduce((sum, order) => sum + order.total, 0),
        averageOrderValue: mockOrders.length > 0 ? mockOrders.reduce((sum, order) => sum + order.total, 0) / mockOrders.length : 0,
        pendingOrders: mockOrders.filter(order => order.status === 'pending').length,
        deliveredOrders: mockOrders.filter(order => order.status === 'delivered').length
      };

      setStats(orderStats);
      setLoading(false);
    }
  }, [currentUser]);

  // Rediriger si l'utilisateur n'est pas connecté
  if (!currentUser) {
    return <Navigate to="/auth" replace />;
  }





  // Filtrer les commandes en fonction des critères
  const filteredOrders = orders.filter(order => {
    // Filtre par statut
    if (filterStatus !== 'all' && order.status !== filterStatus) {
      return false;
    }

    // Filtre par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        order.id.toLowerCase().includes(query) ||
        order.items.some(item =>
          item.name.toLowerCase().includes(query) ||
          item.businessName.toLowerCase().includes(query)
        )
      );
    }

    return true;
  });

  // Afficher un message de chargement pendant la récupération des données
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement de vos commandes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header avec titre et statistiques */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Mes commandes</h1>
            <p className="text-gray-600 mt-1">Suivez et gérez toutes vos commandes</p>
          </div>
          <Button variant="outline" className="flex items-center space-x-2">
            <Download size={16} />
            <span>Exporter</span>
          </Button>
        </div>

        {/* Statistiques */}
        {stats && <OrderStatsCards stats={stats} />}
      </div>

      {/* Insights personnalisés */}
      {stats && orders.length > 0 && (
        <OrderInsights
          totalOrders={stats.totalOrders}
          totalSpent={stats.totalSpent}
          averageOrderValue={stats.averageOrderValue}
          deliveredOrders={stats.deliveredOrders}
        />
      )}

      {/* Filtres et recherche */}
      <OrderFilters
        filterStatus={filterStatus}
        searchQuery={searchQuery}
        onFilterStatusChange={setFilterStatus}
        onSearchQueryChange={setSearchQuery}
        onRefresh={() => window.location.reload()}
      />

      {/* Liste des commandes */}
      <div className="space-y-6">
        {filteredOrders.length === 0 ? (
          <EmptyOrdersState
            hasFilters={filterStatus !== 'all' || searchQuery !== ''}
            onClearFilters={() => {
              setFilterStatus('all');
              setSearchQuery('');
            }}
            onStartShopping={() => window.location.href = '/marketplace'}
          />
        ) : (
          filteredOrders.map(order => (
            <OrderCard
              key={order.id}
              order={order}
              isExpanded={expandedOrderId === order.id}
              onToggleExpand={() => setExpandedOrderId(expandedOrderId === order.id ? null : order.id)}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default OrdersPage;
