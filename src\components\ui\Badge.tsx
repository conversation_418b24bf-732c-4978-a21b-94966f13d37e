import React from 'react';
import { QualityBadge } from '../../types';

interface BadgeProps {
  label: string;
  variant?: 'default' | 'outline' | 'success' | 'warning' | 'error' | 'gold' | 'silver' | 'bronze';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const Badge: React.FC<BadgeProps> = ({
  label,
  variant = 'default',
  size = 'md',
  className = '',
}) => {
  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-full';
  
  const variantStyles = {
    default: 'bg-blue-100 text-blue-800',
    outline: 'bg-transparent border border-gray-300 text-gray-700',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800',
    gold: 'bg-amber-100 text-amber-800 border border-amber-300',
    silver: 'bg-gray-100 text-gray-800 border border-gray-300',
    bronze: 'bg-orange-100 text-orange-800 border border-orange-300'
  };
  
  const sizeStyles = {
    sm: 'text-xs px-2 py-0.5',
    md: 'text-sm px-2.5 py-0.5',
    lg: 'text-base px-3 py-1'
  };

  return (
    <span
      className={`
        ${baseStyles}
        ${variantStyles[variant]}
        ${sizeStyles[size]}
        ${className}
      `}
    >
      {label}
    </span>
  );
};

export interface QualityBadgeProps {
  type: QualityBadge;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const ProductQualityBadge: React.FC<QualityBadgeProps> = ({
  type,
  size = 'md',
  className = '',
}) => {
  if (type === QualityBadge.NONE) return null;
  
  const labels = {
    [QualityBadge.GOLD]: '🏆 Gold',
    [QualityBadge.SILVER]: '🥈 Silver',
    [QualityBadge.BRONZE]: '🥉 Bronze'
  };
  
  const variants = {
    [QualityBadge.GOLD]: 'gold',
    [QualityBadge.SILVER]: 'silver',
    [QualityBadge.BRONZE]: 'bronze'
  };
  
  return (
    <Badge 
      label={labels[type]} 
      variant={variants[type] as any} 
      size={size} 
      className={className}
    />
  );
};

export default Badge;