import React from 'react';
import { User, MessageSquare, Star, ThumbsUp } from 'lucide-react';

interface Tab {
  id: string;
  label: string;
  icon: React.ReactNode;
}

interface ProfileTabsProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const ProfileTabs: React.FC<ProfileTabsProps> = ({ activeTab, onTabChange }) => {
  const tabs: Tab[] = [
    {
      id: 'statut',
      label: 'Statut',
      icon: <User className="w-5 h-5" />
    },
    {
      id: 'publications',
      label: 'Publications',
      icon: <MessageSquare className="w-5 h-5" />
    },
    {
      id: 'recommandations',
      label: 'Recommandations',
      icon: <Star className="w-5 h-5" />
    },
    {
      id: 'avis',
      label: 'Avis',
      icon: <ThumbsUp className="w-5 h-5" />
    }
  ];

  return (
    <div className="bg-white border-b border-gray-200">
      <nav className="flex space-x-8 px-4">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === tab.id
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {tab.icon}
            <span className="ml-2">{tab.label}</span>
          </button>
        ))}
      </nav>
    </div>
  );
};

export default ProfileTabs;