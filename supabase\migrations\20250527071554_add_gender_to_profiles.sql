-- Ajouter la colonne gender à la table profiles
ALTER TABLE profiles
ADD COLUMN gender VARCHAR(10);

-- Ajouter une contrainte pour s'assurer que le genre est valide
ALTER TABLE profiles
ADD CONSTRAINT gender_check CHECK (gender IS NULL OR gender IN ('Homme', 'Femme'));

-- Ajouter un commentaire pour documenter la colonne
COMMENT ON COLUMN profiles.gender IS 'Genre de l''utilisateur (Homme/Femme, optionnel, utilisé pour le ciblage publicitaire)';