import React from 'react';
import {
  CreditCard, CheckCircle, Star, Crown, Shield, Gift,
  BarChart3, MessageSquare, Users, TrendingUp, Bell,
  Download, Print, Mail, Eye, Settings, Package
} from 'lucide-react';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import Button from '../components/ui/Button';
import { useNavigate } from 'react-router-dom';

const FeaturesOverviewPage: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      category: "💳 Système d'Abonnement",
      description: "Gestion complète des abonnements avec essai gratuit de 7 jours",
      items: [
        "Essai gratuit de 7 jours automatique",
        "4 plans d'abonnement (Gratuit, Starter, Professional, Enterprise)",
        "Processus de paiement sécurisé en 4 étapes",
        "Méthodes de paiement multiples (Cartes, Mobile Money, Virements)",
        "Reçus de paiement professionnels",
        "Historique complet des transactions",
        "Badge d'abonnement dans le profil",
        "Alertes d'expiration automatiques"
      ],
      actions: [
        { label: "Voir les Plans", path: "/subscription" },
        { label: "Démo Paiement", path: "/payment-demo" }
      ]
    },
    {
      category: "🤖 IA Conseiller Business",
      description: "Intelligence artificielle avancée pour analyser et optimiser votre business",
      items: [
        "Analyse des avis clients en temps réel",
        "Recommandations personnalisées basées sur les données",
        "Détection des tendances et problèmes récurrents",
        "Suggestions d'amélioration de produits",
        "Propositions de nouveaux produits",
        "Tableaux de bord interactifs avec graphiques",
        "Notifications automatiques d'insights",
        "Export PDF des rapports d'analyse"
      ],
      actions: [
        { label: "Accéder à l'IA", path: "/business-profile" }
      ]
    },
    {
      category: "📝 Gestion des Avis",
      description: "Centralisation et analyse de tous les avis clients",
      items: [
        "Onglet 'Mes avis' dédié dans le profil business",
        "Filtres avancés par type d'avis",
        "Recherche dans les avis et produits",
        "Tri par date, note, engagement",
        "Statistiques détaillées des avis",
        "Analyse de sentiment automatique",
        "Métriques d'engagement (likes, partages, commentaires)",
        "Identification des produits les plus/moins appréciés"
      ],
      actions: [
        { label: "Voir Mes Avis", path: "/business-profile" }
      ]
    },
    {
      category: "💼 Profil Business Complet",
      description: "Interface complète pour la gestion d'entreprise",
      items: [
        "Tableau de bord avec métriques clés",
        "Gestion des produits et services",
        "Suivi des commandes et ventes",
        "Paramètres et configuration",
        "Badge de statut business visible",
        "Badge d'abonnement avec jours restants",
        "Navigation par onglets intuitive",
        "Design moderne et responsive"
      ],
      actions: [
        { label: "Mon Profil", path: "/business-profile" }
      ]
    },
    {
      category: "🔒 Sécurité et Conformité",
      description: "Système sécurisé et conforme aux standards",
      items: [
        "Chiffrement SSL 256 bits pour tous les paiements",
        "Tokenisation des données de paiement",
        "Audit trail complet des transactions",
        "Numérotation unique des factures",
        "Documents légaux générés automatiquement",
        "Conformité PCI DSS (simulation)",
        "Notifications de sécurité automatiques",
        "Sauvegarde et récupération des données"
      ],
      actions: []
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* En-tête */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          🎉 Fonctionnalités Développées
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Découvrez toutes les fonctionnalités avancées développées pour Customeroom Business
        </p>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <Card>
          <CardBody className="text-center">
            <CreditCard className="mx-auto text-blue-600 mb-3" size={32} />
            <div className="text-2xl font-bold text-gray-900">4</div>
            <div className="text-sm text-gray-600">Plans d'abonnement</div>
          </CardBody>
        </Card>
        
        <Card>
          <CardBody className="text-center">
            <MessageSquare className="mx-auto text-green-600 mb-3" size={32} />
            <div className="text-2xl font-bold text-gray-900">3</div>
            <div className="text-sm text-gray-600">Types d'avis gérés</div>
          </CardBody>
        </Card>
        
        <Card>
          <CardBody className="text-center">
            <BarChart3 className="mx-auto text-purple-600 mb-3" size={32} />
            <div className="text-2xl font-bold text-gray-900">8</div>
            <div className="text-sm text-gray-600">Onglets de gestion</div>
          </CardBody>
        </Card>
        
        <Card>
          <CardBody className="text-center">
            <Shield className="mx-auto text-orange-600 mb-3" size={32} />
            <div className="text-2xl font-bold text-gray-900">100%</div>
            <div className="text-sm text-gray-600">Sécurisé SSL</div>
          </CardBody>
        </Card>
      </div>

      {/* Fonctionnalités détaillées */}
      <div className="space-y-8">
        {features.map((feature, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{feature.category}</h2>
                  <p className="text-gray-600 mt-1">{feature.description}</p>
                </div>
                {feature.actions.length > 0 && (
                  <div className="flex space-x-3">
                    {feature.actions.map((action, actionIndex) => (
                      <Button
                        key={actionIndex}
                        onClick={() => navigate(action.path)}
                        variant={actionIndex === 0 ? 'primary' : 'outline'}
                        size="sm"
                      >
                        {action.label}
                      </Button>
                    ))}
                  </div>
                )}
              </div>
            </CardHeader>
            
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {feature.items.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex items-start space-x-3">
                    <CheckCircle className="text-green-500 mt-0.5 flex-shrink-0" size={16} />
                    <span className="text-gray-700">{item}</span>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      {/* Plans d'abonnement en aperçu */}
      <Card>
        <CardHeader>
          <h2 className="text-2xl font-bold text-gray-900">Plans d'Abonnement Disponibles</h2>
        </CardHeader>
        
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center p-4 border border-green-200 rounded-lg bg-green-50">
              <Gift className="mx-auto text-green-600 mb-3" size={32} />
              <h3 className="font-bold text-gray-900">Essai Gratuit</h3>
              <div className="text-2xl font-bold text-green-600">0 F CFA</div>
              <div className="text-sm text-gray-600">7 jours</div>
            </div>
            
            <div className="text-center p-4 border border-blue-200 rounded-lg">
              <Star className="mx-auto text-blue-600 mb-3" size={32} />
              <h3 className="font-bold text-gray-900">Starter</h3>
              <div className="text-2xl font-bold text-blue-600">15,000 F CFA</div>
              <div className="text-sm text-gray-600">par mois</div>
            </div>
            
            <div className="text-center p-4 border border-purple-200 rounded-lg bg-purple-50">
              <Crown className="mx-auto text-purple-600 mb-3" size={32} />
              <h3 className="font-bold text-gray-900">Professional</h3>
              <div className="text-2xl font-bold text-purple-600">35,000 F CFA</div>
              <div className="text-sm text-gray-600">par mois</div>
              <div className="text-xs text-purple-600 mt-1">Plus populaire</div>
            </div>
            
            <div className="text-center p-4 border border-yellow-200 rounded-lg">
              <Shield className="mx-auto text-yellow-600 mb-3" size={32} />
              <h3 className="font-bold text-gray-900">Enterprise</h3>
              <div className="text-2xl font-bold text-yellow-600">75,000 F CFA</div>
              <div className="text-sm text-gray-600">par mois</div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Actions rapides */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
        <CardBody>
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Testez Toutes les Fonctionnalités</h2>
            <p className="text-gray-600 mb-6">
              Explorez l'ensemble du système développé pour Customeroom Business
            </p>
            
            <div className="flex flex-wrap justify-center gap-4">
              <Button onClick={() => navigate('/subscription')} size="lg">
                <CreditCard className="mr-2" size={20} />
                Mon Abonnement
              </Button>
              
              <Button onClick={() => navigate('/payment-demo')} variant="outline" size="lg">
                <Eye className="mr-2" size={20} />
                Démo Paiement
              </Button>
              
              <Button onClick={() => navigate('/business-profile')} variant="outline" size="lg">
                <Users className="mr-2" size={20} />
                Profil Business
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Note technique */}
      <Card className="border-l-4 border-blue-500 bg-blue-50">
        <CardBody>
          <div className="flex items-start space-x-3">
            <Settings className="text-blue-600 mt-1" size={20} />
            <div>
              <h3 className="font-semibold text-blue-900 mb-2">Mode Démonstration</h3>
              <p className="text-blue-800 text-sm">
                Toutes les fonctionnalités sont actuellement en mode démonstration avec des données simulées. 
                Les processus de paiement sont entièrement simulés et aucun vrai paiement n'est effectué. 
                Le système est prêt pour l'intégration avec de vraies APIs de paiement et bases de données.
              </p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default FeaturesOverviewPage;
