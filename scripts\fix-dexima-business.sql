-- SCRIPT POUR CORRIGER LE PROFIL BUSINESS DE DEXIMA
-- Exécutez ce script dans Supabase SQL Editor

-- =====================================================
-- 1. RECHERCHER DEXIMA
-- =====================================================

DO $$
DECLARE
    dexima_user record;
    business_exists boolean := false;
BEGIN
    RAISE NOTICE '🔍 Recherche de Dexima...';
    
    -- Chercher Dexima dans les profils
    SELECT * INTO dexima_user
    FROM profiles 
    WHERE username ILIKE '%dexima%' 
       OR email ILIKE '%dexima%'
       OR bio ILIKE '%dexima%'
    LIMIT 1;
    
    IF dexima_user.id IS NOT NULL THEN
        RAISE NOTICE '✅ Dexima trouvé:';
        RAISE NOTICE '   ID: %', dexima_user.id;
        RAISE NOTICE '   Username: %', dexima_user.username;
        RAISE NOTICE '   Email: %', dexima_user.email;
        RAISE NOTICE '   Rôle: %', dexima_user.role;
        
        -- Vérifier s'il a un profil business
        SELECT EXISTS(SELECT 1 FROM business_profiles WHERE id = dexima_user.id) INTO business_exists;
        
        IF business_exists THEN
            RAISE NOTICE '✅ Dexima a déjà un profil business';
        ELSE
            RAISE NOTICE '⚠️ Dexima n''a pas de profil business';
            
            -- Mettre à jour le rôle si nécessaire
            IF dexima_user.role != 'business' THEN
                UPDATE profiles 
                SET role = 'business' 
                WHERE id = dexima_user.id;
                RAISE NOTICE '✅ Rôle mis à jour vers "business"';
            END IF;
            
            -- Créer le profil business
            INSERT INTO business_profiles (
                id,
                business_name,
                business_description,
                business_category,
                business_status,
                business_verified,
                created_at,
                updated_at
            ) VALUES (
                dexima_user.id,
                COALESCE(dexima_user.username, 'Dexima'),
                COALESCE(dexima_user.bio, 'Entreprise Dexima'),
                'Technology',
                'verified',
                true,
                dexima_user.created_at,
                NOW()
            );
            
            RAISE NOTICE '✅ Profil business créé pour Dexima';
        END IF;
        
    ELSE
        RAISE NOTICE '❌ Dexima non trouvé dans les profils';
        RAISE NOTICE 'Vérifiez que l''utilisateur existe bien';
    END IF;
END $$;

-- =====================================================
-- 2. CRÉER TOUS LES PROFILS BUSINESS MANQUANTS
-- =====================================================

DO $$
DECLARE
    missing_count integer;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔧 Création des profils business manquants...';
    
    -- Compter les profils manquants
    SELECT COUNT(*) INTO missing_count
    FROM profiles p
    LEFT JOIN business_profiles bp ON p.id = bp.id
    WHERE p.role = 'business' AND bp.id IS NULL;
    
    RAISE NOTICE 'Profils business manquants: %', missing_count;
    
    IF missing_count > 0 THEN
        -- Créer les profils manquants
        INSERT INTO business_profiles (
            id,
            business_name,
            business_description,
            business_category,
            business_status,
            business_verified,
            created_at,
            updated_at
        )
        SELECT 
            p.id,
            COALESCE(p.username, 'Entreprise'),
            COALESCE(p.bio, 'Profil business créé automatiquement'),
            'General',
            'new',
            false,
            p.created_at,
            NOW()
        FROM profiles p
        LEFT JOIN business_profiles bp ON p.id = bp.id
        WHERE p.role = 'business' 
          AND bp.id IS NULL;
        
        RAISE NOTICE '✅ % profils business créés', missing_count;
    ELSE
        RAISE NOTICE '✅ Tous les utilisateurs business ont déjà un profil business';
    END IF;
END $$;

-- =====================================================
-- 3. VÉRIFICATION FINALE
-- =====================================================

DO $$
DECLARE
    total_profiles integer;
    total_business_users integer;
    total_business_profiles integer;
    dexima_found boolean := false;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📊 VÉRIFICATION FINALE:';
    RAISE NOTICE '======================';
    
    -- Compter les totaux
    SELECT COUNT(*) INTO total_profiles FROM profiles;
    SELECT COUNT(*) INTO total_business_users FROM profiles WHERE role = 'business';
    SELECT COUNT(*) INTO total_business_profiles FROM business_profiles;
    
    RAISE NOTICE 'Total profils: %', total_profiles;
    RAISE NOTICE 'Utilisateurs business: %', total_business_users;
    RAISE NOTICE 'Profils business: %', total_business_profiles;
    
    -- Vérifier Dexima spécifiquement
    SELECT EXISTS(
        SELECT 1 FROM business_profiles bp
        JOIN profiles p ON bp.id = p.id
        WHERE p.username ILIKE '%dexima%' 
           OR p.email ILIKE '%dexima%'
           OR bp.business_name ILIKE '%dexima%'
    ) INTO dexima_found;
    
    IF dexima_found THEN
        RAISE NOTICE '✅ Dexima trouvé dans business_profiles';
    ELSE
        RAISE NOTICE '❌ Dexima toujours pas trouvé';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎯 PROCHAINES ÉTAPES:';
    RAISE NOTICE '1. Rechargez la page de gestion des entreprises';
    RAISE NOTICE '2. Vérifiez la console du navigateur pour les erreurs';
    RAISE NOTICE '3. Si le problème persiste, vérifiez les permissions RLS';
END $$;

-- =====================================================
-- 4. LISTER TOUTES LES ENTREPRISES POUR VÉRIFICATION
-- =====================================================

SELECT 
    'ENTREPRISES DISPONIBLES' as titre,
    bp.business_name,
    p.username,
    p.email,
    bp.business_status,
    bp.business_verified,
    bp.created_at
FROM business_profiles bp
JOIN profiles p ON bp.id = p.id
ORDER BY bp.created_at DESC
LIMIT 10;

-- =====================================================
-- 5. DÉSACTIVER RLS SI NÉCESSAIRE
-- =====================================================

-- Si les données n'apparaissent toujours pas, cela peut être dû à RLS
-- Décommentez ces lignes si nécessaire :

-- ALTER TABLE business_profiles DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '⚠️ Si les entreprises n''apparaissent toujours pas:';
    RAISE NOTICE '1. Vérifiez que RLS n''est pas activé sur business_profiles';
    RAISE NOTICE '2. Vérifiez les permissions de l''utilisateur admin';
    RAISE NOTICE '3. Consultez les logs de la console navigateur';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 Pour désactiver RLS temporairement:';
    RAISE NOTICE 'ALTER TABLE business_profiles DISABLE ROW LEVEL SECURITY;';
    RAISE NOTICE 'ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;';
END $$;
