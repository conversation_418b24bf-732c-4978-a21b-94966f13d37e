import { useEffect, useState } from 'react';
import { SubscriptionService, SubscriptionStatus } from '../services/subscriptionService';
import { supabase } from '../lib/supabase';

export function useBusinessSubscriptionStatus() {
  const [status, setStatus] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    async function fetchStatus() {
      setLoading(true);
      setError(null);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          setError('Utilisateur non connecté');
          setStatus(null);
          setLoading(false);
          return;
        }
        const result = await SubscriptionService.getBusinessSubscriptionStatus(user.id);
        if (isMounted) setStatus(result);
      } catch (e) {
        setError('Erreur lors du chargement du statut d\'abonnement');
        setStatus(null);
      } finally {
        setLoading(false);
      }
    }
    fetchStatus();
    return () => { isMounted = false; };
  }, []);

  return { status, loading, error, refetch: async () => {
    setLoading(true);
    setError(null);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setError('Utilisateur non connecté');
        setStatus(null);
        setLoading(false);
        return;
      }
      const result = await SubscriptionService.getBusinessSubscriptionStatus(user.id);
      setStatus(result);
    } catch (e) {
      setError('Erreur lors du chargement du statut d\'abonnement');
      setStatus(null);
    } finally {
      setLoading(false);
    }
  }};
}
