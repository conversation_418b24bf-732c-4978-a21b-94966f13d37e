import { useEffect, useState, useCallback } from 'react';
import { SubscriptionService, SubscriptionStatus } from '../services/subscriptionService';
import { supabase } from '../lib/supabase';

export function useBusinessSubscriptionStatus() {
  const [status, setStatus] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [justActivated, setJustActivated] = useState(false);

  const fetchStatus = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setError('Utilisateur non connecté');
        setStatus(null);
        setLoading(false);
        return;
      }

      const result = await SubscriptionService.getBusinessSubscriptionStatus(user.id);

      // Détecter si l'abonnement vient d'être activé
      setStatus(prevStatus => {
        if (prevStatus && !prevStatus.isActive && result && result.isActive) {
          setJustActivated(true);
          // Réinitialiser après 5 secondes
          setTimeout(() => setJustActivated(false), 5000);
        }
        return result;
      });
    } catch (e) {
      setError('Erreur lors du chargement du statut d\'abonnement');
      setStatus(null);
    } finally {
      setLoading(false);
    }
  }, []); // Pas de dépendances pour éviter la boucle infinie

  useEffect(() => {
    let isMounted = true;
    let subscription: any = null;

    const setupSubscription = async () => {
      await fetchStatus();

      // Écouter les changements d'abonnement en temps réel
      const { data: { user } } = await supabase.auth.getUser();
      if (user && isMounted) {
        subscription = supabase
          .channel('subscription-changes')
          .on('postgres_changes', {
            event: '*',
            schema: 'public',
            table: 'business_subscriptions',
            filter: `business_id=eq.${user.id}`
          }, (payload) => {
            console.log('🔄 Changement d\'abonnement détecté:', payload);
            if (isMounted) {
              fetchStatus();
            }
          })
          .subscribe();
      }
    };

    setupSubscription();

    return () => {
      isMounted = false;
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, []); // Pas de dépendances - s'exécute une seule fois au montage

  const refetch = useCallback(async () => {
    await fetchStatus();
  }, [fetchStatus]);

  return {
    status,
    loading,
    error,
    justActivated,
    refetch
  };
}
