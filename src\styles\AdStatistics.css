.ad-statistics-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

.ad-statistics-modal {
  background-color: white;
  border-radius: 0.5rem;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
}

.ad-statistics-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.ad-statistics-modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.ad-statistics-modal-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.25rem 0 0 0;
}

.ad-statistics-modal-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.ad-statistics-modal-close:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.ad-statistics-modal-content {
  padding: 1.5rem;
  flex: 1;
}

.ad-statistics-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.ad-statistics-card {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.ad-statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.ad-statistics-card-icon {
  background-color: #e0f2fe;
  color: #0ea5e9;
  width: 3rem;
  height: 3rem;
  border-radius: 0.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.ad-statistics-card:nth-child(2) .ad-statistics-card-icon {
  background-color: #dcfce7;
  color: #22c55e;
}

.ad-statistics-card:nth-child(3) .ad-statistics-card-icon {
  background-color: #fef3c7;
  color: #f59e0b;
}

.ad-statistics-card:nth-child(4) .ad-statistics-card-icon {
  background-color: #fee2e2;
  color: #ef4444;
}

.ad-statistics-card-content {
  flex: 1;
}

.ad-statistics-card-content h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin: 0 0 0.25rem 0;
}

.ad-statistics-card-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.ad-statistics-chart-container {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.25rem;
  margin-bottom: 2rem;
}

.ad-statistics-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ad-statistics-section-icon {
  color: #6b7280;
}

.ad-statistics-chart {
  height: 250px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.ad-statistics-chart-bars {
  display: flex;
  align-items: flex-end;
  height: 200px;
  gap: 0.5rem;
}

.ad-statistics-chart-bar-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.ad-statistics-chart-bar {
  width: 100%;
  background-color: #3b82f6;
  border-radius: 0.25rem 0.25rem 0 0;
  transition: height 0.3s ease;
}

.ad-statistics-chart-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

.ad-statistics-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.ad-statistics-details-section {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.25rem;
}

.ad-statistics-details-value {
  font-size: 0.875rem;
  color: #4b5563;
  margin: 0 0 1rem 0;
}

.ad-statistics-demographics {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.ad-statistics-demographic-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.ad-statistics-demographic-label {
  font-size: 0.75rem;
  color: #6b7280;
}

.ad-statistics-demographic-bar-container {
  height: 0.5rem;
  background-color: #f3f4f6;
  border-radius: 0.25rem;
  overflow: hidden;
  position: relative;
}

.ad-statistics-demographic-bar {
  height: 100%;
  background-color: #3b82f6;
  border-radius: 0.25rem;
}

.ad-statistics-demographic-value {
  font-size: 0.75rem;
  color: #4b5563;
  position: absolute;
  right: 0;
  top: 0.75rem;
}

.ad-statistics-budget-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.ad-statistics-budget-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: #4b5563;
}

.ad-statistics-budget-progress {
  height: 0.5rem;
  background-color: #f3f4f6;
  border-radius: 0.25rem;
  overflow: hidden;
}

.ad-statistics-budget-progress-bar {
  height: 100%;
  background-color: #3b82f6;
  border-radius: 0.25rem;
}

.ad-statistics-budget-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #6b7280;
}

.ad-statistics-conversions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.ad-statistics-conversion-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: #4b5563;
}

.ad-statistics-modal-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 1.5rem;
  border-top: 1px solid #e5e7eb;
}

/* Animation pour les barres du graphique */
@keyframes grow-bar {
  from { height: 0; }
  to { height: var(--bar-height); }
}

.ad-statistics-chart-bar {
  animation: grow-bar 0.5s ease-out forwards;
}

.ad-statistics-chart-bar:nth-child(1) { animation-delay: 0.1s; }
.ad-statistics-chart-bar:nth-child(2) { animation-delay: 0.2s; }
.ad-statistics-chart-bar:nth-child(3) { animation-delay: 0.3s; }
.ad-statistics-chart-bar:nth-child(4) { animation-delay: 0.4s; }
.ad-statistics-chart-bar:nth-child(5) { animation-delay: 0.5s; }
.ad-statistics-chart-bar:nth-child(6) { animation-delay: 0.6s; }
.ad-statistics-chart-bar:nth-child(7) { animation-delay: 0.7s; }
