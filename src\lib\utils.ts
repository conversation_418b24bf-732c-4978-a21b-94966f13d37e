import { IPost } from '../types/index';

/**
 * Calculates the average rating and count of ratings for a given post.
 * Considers the post's initial rating and ratings from verified comments.
 */
export const getAverageRatingForPost = (post: IPost): { average: number; count: number } => {
  const commentRatings = post.comments
    ?.filter(comment => comment.hasUsedProduct && typeof comment.rating === 'number' && comment.rating > 0 && comment.rating <= 5)
    .map(comment => comment.rating!); // Non-null assertion due to filter
  
  // Ensure post.rating is a valid number between 1 and 5 if it exists
  const initialRating = (typeof post.rating === 'number' && post.rating >= 0 && post.rating <= 5) ? [post.rating] : [];
  
  const allRatings = [...initialRating, ...(commentRatings || [])];

  if (allRatings.length === 0) {
    return { average: 0, count: 0 };
  }
  const sum = allRatings.reduce((acc, curr) => acc + curr, 0);
  return { 
    average: parseFloat((sum / allRatings.length).toFixed(1)), // Keep one decimal place
    count: allRatings.length 
  };
};

/**
 * Calculates the rank of a post within its category based on average rating.
 */
export const getCategoryRankForPost = (
  currentPost: IPost,
  allPosts: IPost[]
): { rank: number; totalInCategory: number } => {
  if (!currentPost.category || !allPosts || allPosts.length === 0) {
    return { rank: 0, totalInCategory: 0 };
  }

  const postsInCategory = allPosts.filter(p => p.category === currentPost.category);
  
  if (postsInCategory.length === 0) {
    return { rank: 0, totalInCategory: 0 };
  }

  const ratedPostsInCategory = postsInCategory.map(p => {
    const ratingInfo = getAverageRatingForPost(p);
    return { 
      ...p, 
      calculatedAverageRating: ratingInfo.average, 
      ratingCount: ratingInfo.count 
    };
  });

  // Sort by average rating (desc), then by rating count (desc) as a tie-breaker
  ratedPostsInCategory.sort((a, b) => {
    if (b.calculatedAverageRating !== a.calculatedAverageRating) {
      return b.calculatedAverageRating - a.calculatedAverageRating;
    }
    return b.ratingCount - a.ratingCount; // Higher rating count wins ties
  });

  const rank = ratedPostsInCategory.findIndex(p => p.id === currentPost.id) + 1;
  
  return { rank, totalInCategory: postsInCategory.length };
};
