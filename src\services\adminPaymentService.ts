import { supabase } from '../lib/supabase';

import { AdminService } from './adminService';

// Types pour la gestion administrative des paiements
export interface AdminPaymentStats {
  totalRevenue: number;
  monthlyRevenue: number;
  activeSubscriptions: number;
  expiredSubscriptions: number;
  pendingPayments: number;
  refundRequests: number;
  adRevenue: number;
  subscriptionRevenue: number;
}

export interface AdminSubscription {
  id: string;
  businessId: string;
  businessName: string;
  planType: 'trial' | 'monthly' | 'yearly';
  status: 'active' | 'expired' | 'cancelled' | 'pending';
  startDate: string;
  endDate: string;
  amount: number;
  paymentMethod: string;
  autoRenewal: boolean;
  lastPayment?: string;
  nextPayment?: string;
}

export interface AdminAdPayment {
  id: string;
  businessId: string;
  businessName: string;
  adId: string;
  adTitle: string;
  amount: number;
  duration: number;
  status: 'active' | 'completed' | 'cancelled' | 'pending';
  startDate: string;
  endDate: string;
  paymentDate: string;
  impressions: number;
  clicks: number;
}

export interface AdminTransaction {
  id: string;
  type: 'subscription' | 'advertisement';
  businessId: string;
  businessName: string;
  amount: number;
  status: 'completed' | 'pending' | 'failed' | 'refunded';
  paymentMethod: string;
  transactionDate: string;
  description: string;
  reference: string;
}

export class AdminPaymentService {
  
  // =====================================================
  // STATISTIQUES ADMINISTRATEUR
  // =====================================================
  
  static async getAdminPaymentStats(): Promise<AdminPaymentStats> {
    try {
      // Données mockées pour la démonstration
      // En production, ces données viendraient de la base de données
      return {
        totalRevenue: 15750000, // 15,750,000 XOF
        monthlyRevenue: 2850000, // 2,850,000 XOF
        activeSubscriptions: 127,
        expiredSubscriptions: 23,
        pendingPayments: 8,
        refundRequests: 2,
        adRevenue: 4200000, // 4,200,000 XOF
        subscriptionRevenue: 11550000 // 11,550,000 XOF
      };
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques de paiement:', error);
      throw error;
    }
  }
  
  // =====================================================
  // GESTION DES ABONNEMENTS
  // =====================================================
  
  static async getAdminSubscriptions(filters?: {
    status?: string;
    searchTerm?: string;
    dateFilter?: string;
  }): Promise<AdminSubscription[]> {
    try {
      // Données mockées pour la démonstration
      const mockSubscriptions: AdminSubscription[] = [
        {
          id: 'sub_001',
          businessId: 'bus_001',
          businessName: 'Dexima Cosmétiques',
          planType: 'yearly',
          status: 'active',
          startDate: '2024-01-15',
          endDate: '2025-01-15',
          amount: 240000,
          paymentMethod: 'Mobile Money',
          autoRenewal: true,
          lastPayment: '2024-01-15',
          nextPayment: '2025-01-15'
        },
        {
          id: 'sub_002',
          businessId: 'bus_002',
          businessName: 'Pharmacie Centrale',
          planType: 'monthly',
          status: 'active',
          startDate: '2024-11-01',
          endDate: '2024-12-01',
          amount: 25000,
          paymentMethod: 'Carte Bancaire',
          autoRenewal: true,
          lastPayment: '2024-11-01',
          nextPayment: '2024-12-01'
        },
        {
          id: 'sub_003',
          businessId: 'bus_003',
          businessName: 'Restaurant Le Palais',
          planType: 'trial',
          status: 'expired',
          startDate: '2024-10-20',
          endDate: '2024-10-27',
          amount: 0,
          paymentMethod: 'Gratuit',
          autoRenewal: false
        },
        {
          id: 'sub_004',
          businessId: 'bus_004',
          businessName: 'Boutique Mode Africaine',
          planType: 'monthly',
          status: 'pending',
          startDate: '2024-11-28',
          endDate: '2024-12-28',
          amount: 25000,
          paymentMethod: 'Mobile Money',
          autoRenewal: true
        },
        {
          id: 'sub_005',
          businessId: 'bus_005',
          businessName: 'Garage Auto Plus',
          planType: 'yearly',
          status: 'cancelled',
          startDate: '2024-06-01',
          endDate: '2024-08-15',
          amount: 240000,
          paymentMethod: 'Virement Bancaire',
          autoRenewal: false,
          lastPayment: '2024-06-01'
        }
      ];

      // Appliquer les filtres
      let filteredSubscriptions = mockSubscriptions;

      if (filters?.status && filters.status !== 'all') {
        filteredSubscriptions = filteredSubscriptions.filter(sub => sub.status === filters.status);
      }

      if (filters?.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase();
        filteredSubscriptions = filteredSubscriptions.filter(sub =>
          sub.businessName.toLowerCase().includes(searchLower) ||
          sub.businessId.toLowerCase().includes(searchLower)
        );
      }

      return filteredSubscriptions;
    } catch (error) {
      console.error('Erreur lors de la récupération des abonnements:', error);
      throw error;
    }
  }

  static async updateSubscriptionStatus(
    subscriptionId: string,
    newStatus: 'active' | 'expired' | 'cancelled' | 'pending',
    reason?: string
  ): Promise<boolean> {
    try {
      console.log(`Mise à jour du statut de l'abonnement ${subscriptionId} vers ${newStatus}`);
      
      // En production, mettre à jour en base de données
      // const { error } = await supabase
      //   .from('business_subscriptions')
      //   .update({ status: newStatus, updated_at: new Date().toISOString() })
      //   .eq('id', subscriptionId);

      // Simuler la mise à jour
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Enregistrer l'action dans les logs d'audit
      await this.logAdminAction({
        action: 'subscription_status_update',
        targetId: subscriptionId,
        details: { newStatus, reason }
      });

      return true;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut d\'abonnement:', error);
      return false;
    }
  }

  // =====================================================
  // GESTION DES PUBLICITÉS
  // =====================================================
  
  static async getAdminAdPayments(filters?: {
    status?: string;
    searchTerm?: string;
  }): Promise<AdminAdPayment[]> {
    try {
      // Données mockées pour la démonstration
      const mockAdPayments: AdminAdPayment[] = [
        {
          id: 'ad_001',
          businessId: 'bus_001',
          businessName: 'Dexima Cosmétiques',
          adId: 'ad_promo_001',
          adTitle: 'Promotion Huiles de Beauté',
          amount: 50000,
          duration: 7,
          status: 'active',
          startDate: '2024-11-20',
          endDate: '2024-11-27',
          paymentDate: '2024-11-20',
          impressions: 15420,
          clicks: 342
        },
        {
          id: 'ad_002',
          businessId: 'bus_002',
          businessName: 'Pharmacie Centrale',
          adId: 'ad_promo_002',
          adTitle: 'Médicaments Génériques',
          amount: 75000,
          duration: 14,
          status: 'completed',
          startDate: '2024-11-01',
          endDate: '2024-11-15',
          paymentDate: '2024-11-01',
          impressions: 28750,
          clicks: 567
        },
        {
          id: 'ad_003',
          businessId: 'bus_003',
          businessName: 'Restaurant Le Palais',
          adId: 'ad_promo_003',
          adTitle: 'Menu Spécial Fêtes',
          amount: 35000,
          duration: 5,
          status: 'pending',
          startDate: '2024-12-01',
          endDate: '2024-12-06',
          paymentDate: '2024-11-28',
          impressions: 0,
          clicks: 0
        }
      ];

      // Appliquer les filtres
      let filteredAds = mockAdPayments;

      if (filters?.status && filters.status !== 'all') {
        filteredAds = filteredAds.filter(ad => ad.status === filters.status);
      }

      if (filters?.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase();
        filteredAds = filteredAds.filter(ad =>
          ad.businessName.toLowerCase().includes(searchLower) ||
          ad.adTitle.toLowerCase().includes(searchLower)
        );
      }

      return filteredAds;
    } catch (error) {
      console.error('Erreur lors de la récupération des paiements publicitaires:', error);
      throw error;
    }
  }

  static async updateAdPaymentStatus(
    adPaymentId: string,
    newStatus: 'active' | 'completed' | 'cancelled' | 'pending',
    reason?: string
  ): Promise<boolean> {
    try {
      console.log(`Mise à jour du statut de la publicité ${adPaymentId} vers ${newStatus}`);
      
      // Simuler la mise à jour
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Enregistrer l'action dans les logs d'audit
      await this.logAdminAction({
        action: 'ad_payment_status_update',
        targetId: adPaymentId,
        details: { newStatus, reason }
      });

      return true;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut de publicité:', error);
      return false;
    }
  }

  // =====================================================
  // GESTION DES TRANSACTIONS
  // =====================================================
  
  static async getAdminTransactions(filters?: {
    status?: string;
    type?: string;
    searchTerm?: string;
  }): Promise<AdminTransaction[]> {
    try {
      // Données mockées pour la démonstration
      const mockTransactions: AdminTransaction[] = [
        {
          id: 'txn_001',
          type: 'subscription',
          businessId: 'bus_001',
          businessName: 'Dexima Cosmétiques',
          amount: 240000,
          status: 'completed',
          paymentMethod: 'Mobile Money',
          transactionDate: '2024-01-15',
          description: 'Abonnement annuel',
          reference: 'MM_240115_001'
        },
        {
          id: 'txn_002',
          type: 'advertisement',
          businessId: 'bus_001',
          businessName: 'Dexima Cosmétiques',
          amount: 50000,
          status: 'completed',
          paymentMethod: 'Mobile Money',
          transactionDate: '2024-11-20',
          description: 'Publicité - Promotion Huiles',
          reference: 'MM_241120_002'
        },
        {
          id: 'txn_003',
          type: 'subscription',
          businessId: 'bus_002',
          businessName: 'Pharmacie Centrale',
          amount: 25000,
          status: 'pending',
          paymentMethod: 'Carte Bancaire',
          transactionDate: '2024-11-28',
          description: 'Abonnement mensuel',
          reference: 'CB_241128_003'
        },
        {
          id: 'txn_004',
          type: 'advertisement',
          businessId: 'bus_002',
          businessName: 'Pharmacie Centrale',
          amount: 75000,
          status: 'completed',
          paymentMethod: 'Carte Bancaire',
          transactionDate: '2024-11-01',
          description: 'Publicité - Médicaments Génériques',
          reference: 'CB_241101_004'
        },
        {
          id: 'txn_005',
          type: 'subscription',
          businessId: 'bus_005',
          businessName: 'Garage Auto Plus',
          amount: 120000,
          status: 'refunded',
          paymentMethod: 'Virement Bancaire',
          transactionDate: '2024-08-15',
          description: 'Remboursement partiel abonnement',
          reference: 'VB_240815_005'
        }
      ];

      // Appliquer les filtres
      let filteredTransactions = mockTransactions;

      if (filters?.status && filters.status !== 'all') {
        filteredTransactions = filteredTransactions.filter(txn => txn.status === filters.status);
      }

      if (filters?.type && filters.type !== 'all') {
        filteredTransactions = filteredTransactions.filter(txn => txn.type === filters.type);
      }

      if (filters?.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase();
        filteredTransactions = filteredTransactions.filter(txn =>
          txn.businessName.toLowerCase().includes(searchLower) ||
          txn.reference.toLowerCase().includes(searchLower) ||
          txn.description.toLowerCase().includes(searchLower)
        );
      }

      return filteredTransactions;
    } catch (error) {
      console.error('Erreur lors de la récupération des transactions:', error);
      throw error;
    }
  }

  static async processRefund(
    transactionId: string,
    reason: string,
    amount?: number
  ): Promise<boolean> {
    try {
      console.log(`Traitement du remboursement pour la transaction ${transactionId}`);
      
      // Simuler le traitement du remboursement
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Enregistrer l'action dans les logs d'audit
      await this.logAdminAction({
        action: 'transaction_refund',
        targetId: transactionId,
        details: { reason, refundAmount: amount }
      });

      return true;
    } catch (error) {
      console.error('Erreur lors du remboursement:', error);
      return false;
    }
  }

  // =====================================================
  // LOGS D'AUDIT
  // =====================================================
  
  private static async logAdminAction(action: {
    action: string;
    targetId: string;
    adminId?: string;
    details: Record<string, any>;
  }): Promise<void> {
    try {
      console.log('📝 Enregistrement de l\'action admin:', action);
      
      // Vérifier que la cible existe
      if (!action.targetId) {
        console.error('Erreur: ID de la cible manquant');
        return;
      }
      
      // Vérifier que l'action est valide
      if (!action.action) {
        console.error('Erreur: Type d\'action manquant');
        return;
      }
      
      // Si un adminId est fourni, vérifier qu'il existe
      if (action.adminId) {
        const { data: adminExists } = await supabase
          .from('admin_profiles')
          .select('id')
          .eq('id', action.adminId)
          .single();
          
        if (!adminExists) {
          console.error(`Erreur: Administrateur avec ID ${action.adminId} non trouvé`);
          return;
        }
      }
      
      // Utiliser le service AdminService pour centraliser l'enregistrement des logs
      await AdminService.logAction(
        action.action,
        `Action de paiement: ${action.action}`,
        'payment',
        action.targetId,
        null,
        action.details
      );
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du log d\'audit:', error);
    }
  }
}
