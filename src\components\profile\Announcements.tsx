import React from 'react';
import { Megaphone, ExternalLink } from 'lucide-react';

interface Announcement {
  id: string;
  title: string;
  description: string;
  image?: string;
  link?: string;
  sponsored?: boolean;
}

interface AnnouncementsProps {
  className?: string;
}

// Données mockées pour les annonces
const mockAnnouncements: Announcement[] = [
  {
    id: '1',
    title: 'Campagne Savon Naturel',
    description: 'Découvrez notre nouvelle gamme de savons naturels bio',
    image: '/api/placeholder/300/150',
    link: '#',
    sponsored: true
  }
];

const Announcements: React.FC<AnnouncementsProps> = ({ className = '' }) => {
  return (
    <div className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden ${className}`}>
      <div className="bg-gradient-to-r from-purple-500 to-pink-600 px-4 py-3">
        <div className="flex items-center text-white">
          <div className="bg-white/20 rounded-lg p-1.5 mr-2">
            <Megaphone size={16} />
          </div>
          <h3 className="font-semibold text-sm">Annonces</h3>
          <span className="ml-auto text-xs bg-white/20 px-2 py-1 rounded">Sponsorisé</span>
        </div>
      </div>

      <div className="p-4">
        <div className="space-y-4">
          {mockAnnouncements.map((announcement) => (
            <div key={announcement.id} className="group cursor-pointer">
              <div className="relative overflow-hidden rounded-lg mb-3 border border-gray-200">
                <img
                  src={announcement.image}
                  alt={announcement.title}
                  className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-200"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/api/placeholder/300/150';
                  }}
                />
                {announcement.sponsored && (
                  <div className="absolute top-2 right-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs px-2 py-1 rounded-full font-medium">
                    Sponsorisé
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold text-gray-900 text-sm group-hover:text-purple-600 transition-colors">
                  {announcement.title}
                </h4>
                <p className="text-xs text-gray-600 line-clamp-2 leading-relaxed">
                  {announcement.description}
                </p>

                {announcement.link && (
                  <div className="flex items-center text-purple-600 text-xs hover:text-purple-700 transition-colors font-medium">
                    <span>En savoir plus</span>
                    <ExternalLink size={12} className="ml-1" />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-4 pt-3 border-t border-gray-100">
          <button className="w-full text-center text-sm text-purple-600 hover:text-purple-700 font-semibold transition-colors bg-purple-50 hover:bg-purple-100 py-2 rounded-lg">
            Voir toutes les annonces
          </button>
        </div>
      </div>
    </div>
  );
};

export default Announcements;
