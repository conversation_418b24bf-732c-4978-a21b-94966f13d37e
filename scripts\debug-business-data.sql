-- SCRIPT DE DEBUG POUR LES DONNÉES BUSINESS
-- Exécutez ce script pour diagnostiquer pourquoi les entreprises n'apparaissent pas

-- =====================================================
-- 1. VÉRIFIER LES TABLES ET COLONNES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 DIAGNOSTIC DES DONNÉES BUSINESS';
    RAISE NOTICE '==================================';
    RAISE NOTICE '';
END $$;

-- Vérifier si la table business_profiles existe
SELECT 
    'Table business_profiles' as verification,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'business_profiles'
    ) THEN '✅ Existe' ELSE '❌ N''existe pas' END as status;

-- Vérifier les colonnes de business_profiles
SELECT 
    'Colonnes business_profiles' as verification,
    string_agg(column_name, ', ' ORDER BY ordinal_position) as colonnes_disponibles
FROM information_schema.columns 
WHERE table_name = 'business_profiles';

-- =====================================================
-- 2. COMPTER LES DONNÉES
-- =====================================================

-- Compter les profils business
SELECT 
    'Profils business' as type,
    COUNT(*) as total
FROM business_profiles;

-- Compter les utilisateurs business
SELECT 
    'Utilisateurs business' as type,
    COUNT(*) as total
FROM profiles 
WHERE role = 'business';

-- =====================================================
-- 3. LISTER TOUTES LES ENTREPRISES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📋 LISTE DE TOUTES LES ENTREPRISES:';
    RAISE NOTICE '================================';
END $$;

-- Lister toutes les entreprises avec leurs données de base
SELECT 
    bp.id,
    bp.business_name,
    p.username,
    p.email,
    bp.business_status,
    bp.business_verified,
    bp.created_at
FROM business_profiles bp
LEFT JOIN profiles p ON bp.id = p.id
ORDER BY bp.created_at DESC;

-- =====================================================
-- 4. RECHERCHER DEXIMA SPÉCIFIQUEMENT
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 RECHERCHE SPÉCIFIQUE DE DEXIMA:';
    RAISE NOTICE '=================================';
END $$;

-- Rechercher Dexima dans business_profiles
SELECT 
    'business_profiles' as table_name,
    id,
    business_name,
    business_status,
    business_verified,
    created_at
FROM business_profiles 
WHERE business_name ILIKE '%dexima%' 
   OR business_description ILIKE '%dexima%';

-- Rechercher Dexima dans profiles
SELECT 
    'profiles' as table_name,
    id,
    username,
    email,
    role,
    is_verified,
    created_at
FROM profiles 
WHERE username ILIKE '%dexima%' 
   OR email ILIKE '%dexima%'
   OR bio ILIKE '%dexima%';

-- =====================================================
-- 5. VÉRIFIER LES UTILISATEURS BUSINESS SANS PROFIL BUSINESS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '⚠️ UTILISATEURS BUSINESS SANS PROFIL BUSINESS:';
    RAISE NOTICE '============================================';
END $$;

-- Utilisateurs avec role='business' mais sans profil business
SELECT 
    p.id,
    p.username,
    p.email,
    p.created_at,
    'Manque profil business' as probleme
FROM profiles p
LEFT JOIN business_profiles bp ON p.id = bp.id
WHERE p.role = 'business' 
  AND bp.id IS NULL
ORDER BY p.created_at DESC;

-- =====================================================
-- 6. CRÉER LE PROFIL BUSINESS MANQUANT POUR DEXIMA
-- =====================================================

DO $$
DECLARE
    dexima_user_id uuid;
    dexima_username text;
    dexima_email text;
BEGIN
    -- Chercher l'utilisateur Dexima
    SELECT id, username, email INTO dexima_user_id, dexima_username, dexima_email
    FROM profiles 
    WHERE username ILIKE '%dexima%' 
       OR email ILIKE '%dexima%'
    LIMIT 1;
    
    IF dexima_user_id IS NOT NULL THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎯 DEXIMA TROUVÉ:';
        RAISE NOTICE 'ID: %', dexima_user_id;
        RAISE NOTICE 'Username: %', dexima_username;
        RAISE NOTICE 'Email: %', dexima_email;
        
        -- Vérifier s'il a déjà un profil business
        IF NOT EXISTS (SELECT 1 FROM business_profiles WHERE id = dexima_user_id) THEN
            RAISE NOTICE '';
            RAISE NOTICE '🔧 CRÉATION DU PROFIL BUSINESS POUR DEXIMA...';
            
            -- Créer le profil business manquant
            INSERT INTO business_profiles (
                id,
                business_name,
                business_description,
                business_category,
                business_status,
                business_verified,
                created_at,
                updated_at
            ) VALUES (
                dexima_user_id,
                COALESCE(dexima_username, 'Dexima'),
                'Profil business créé automatiquement',
                'Technology',
                'new',
                false,
                NOW(),
                NOW()
            );
            
            RAISE NOTICE '✅ Profil business créé pour Dexima';
            
            -- Mettre à jour le rôle si nécessaire
            UPDATE profiles 
            SET role = 'business' 
            WHERE id = dexima_user_id AND role != 'business';
            
        ELSE
            RAISE NOTICE '✅ Dexima a déjà un profil business';
        END IF;
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '❌ DEXIMA NON TROUVÉ dans la table profiles';
        RAISE NOTICE 'Vérifiez que l''utilisateur existe bien';
    END IF;
END $$;

-- =====================================================
-- 7. VÉRIFICATION FINALE
-- =====================================================

DO $$
DECLARE
    total_businesses integer;
    total_business_users integer;
    missing_profiles integer;
BEGIN
    SELECT COUNT(*) INTO total_businesses FROM business_profiles;
    SELECT COUNT(*) INTO total_business_users FROM profiles WHERE role = 'business';
    SELECT COUNT(*) INTO missing_profiles 
    FROM profiles p
    LEFT JOIN business_profiles bp ON p.id = bp.id
    WHERE p.role = 'business' AND bp.id IS NULL;
    
    RAISE NOTICE '';
    RAISE NOTICE '📊 RÉSUMÉ FINAL:';
    RAISE NOTICE '===============';
    RAISE NOTICE 'Profils business: %', total_businesses;
    RAISE NOTICE 'Utilisateurs business: %', total_business_users;
    RAISE NOTICE 'Profils manquants: %', missing_profiles;
    RAISE NOTICE '';
    
    IF missing_profiles > 0 THEN
        RAISE NOTICE '⚠️ Il y a % utilisateurs business sans profil business', missing_profiles;
        RAISE NOTICE 'Exécutez ce script pour les créer automatiquement';
    ELSE
        RAISE NOTICE '✅ Tous les utilisateurs business ont un profil business';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎯 PROCHAINES ÉTAPES:';
    RAISE NOTICE '1. Rechargez la page de gestion des entreprises';
    RAISE NOTICE '2. Vérifiez que Dexima apparaît maintenant';
    RAISE NOTICE '3. Si le problème persiste, vérifiez la console du navigateur';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- 8. CRÉER TOUS LES PROFILS BUSINESS MANQUANTS
-- =====================================================

-- Créer automatiquement les profils business pour tous les utilisateurs business qui n'en ont pas
INSERT INTO business_profiles (
    id,
    business_name,
    business_description,
    business_category,
    business_status,
    business_verified,
    created_at,
    updated_at
)
SELECT 
    p.id,
    COALESCE(p.username, 'Entreprise'),
    COALESCE(p.bio, 'Profil business créé automatiquement'),
    'General',
    'new',
    false,
    p.created_at,
    NOW()
FROM profiles p
LEFT JOIN business_profiles bp ON p.id = bp.id
WHERE p.role = 'business' 
  AND bp.id IS NULL
ON CONFLICT (id) DO NOTHING;

-- Afficher le résultat
SELECT 
    'Profils business créés' as action,
    COUNT(*) as nombre
FROM business_profiles bp
JOIN profiles p ON bp.id = p.id
WHERE p.role = 'business'
  AND bp.updated_at >= NOW() - INTERVAL '1 minute';
