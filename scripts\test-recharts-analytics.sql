-- SCRIPT DE TEST POUR LES GRAPHIQUES RECHARTS DANS ANALYTICS
-- Exécutez ce script pour créer des données réalistes pour les graphiques

-- =====================================================
-- 1. CRÉER DES DONNÉES RÉALISTES POUR LES GRAPHIQUES
-- =====================================================

DO $$
DECLARE
    i integer;
    target_date date;
    base_users integer := 1000;
    base_businesses integer := 200;
    base_posts integer := 500;
    user_id uuid;
BEGIN
    RAISE NOTICE '📊 CRÉATION DE DONNÉES RÉALISTES POUR RECHARTS';
    RAISE NOTICE '==============================================';

    -- <PERSON><PERSON>yer les anciennes données de test
    DELETE FROM daily_metrics WHERE date >= CURRENT_DATE - INTERVAL '60 days';
    DELETE FROM analytics_events WHERE event_name LIKE '%recharts_test%';

    -- Générer des métriques quotidiennes réalistes pour 60 jours
    FOR i IN 0..59 LOOP
        target_date := CURRENT_DATE - INTERVAL '59 days' + (i || ' days')::INTERVAL;
        
        -- Simuler une croissance réaliste avec variations
        INSERT INTO daily_metrics (
            date,
            total_users,
            active_users,
            new_users,
            total_businesses,
            verified_businesses,
            total_posts,
            total_engagements,
            page_views,
            sessions,
            avg_session_duration,
            bounce_rate,
            conversion_rate
        ) VALUES (
            target_date,
            base_users + (i * 15) + FLOOR(RANDOM() * 50), -- Croissance utilisateurs
            FLOOR((base_users + (i * 15)) * 0.6) + FLOOR(RANDOM() * 100), -- 60% actifs
            FLOOR(10 + RANDOM() * 20), -- 10-30 nouveaux par jour
            base_businesses + (i * 3) + FLOOR(RANDOM() * 10), -- Croissance entreprises
            FLOOR((base_businesses + (i * 3)) * 0.4) + FLOOR(RANDOM() * 5), -- 40% vérifiées
            base_posts + (i * 8) + FLOOR(RANDOM() * 20), -- Croissance posts
            FLOOR((base_posts + (i * 8)) * 2.5) + FLOOR(RANDOM() * 100), -- Engagements
            FLOOR(5000 + (i * 100) + RANDOM() * 1000), -- Page views
            FLOOR(800 + (i * 20) + RANDOM() * 200), -- Sessions
            INTERVAL '5 minutes' + (RANDOM() * 10 || ' minutes')::INTERVAL, -- Durée session
            20 + RANDOM() * 30, -- Bounce rate 20-50%
            2 + RANDOM() * 8 -- Conversion rate 2-10%
        );
    END LOOP;
    
    RAISE NOTICE '✅ 60 jours de métriques quotidiennes créées';
    
    -- Récupérer un utilisateur pour les événements
    SELECT id INTO user_id FROM profiles LIMIT 1;
    
    IF user_id IS NOT NULL THEN
        -- Créer des événements d'analytics variés pour les graphiques
        FOR i IN 1..500 LOOP
            -- Événements de page vue avec distribution réaliste
            PERFORM track_analytics_event(
                'page_view',
                'recharts_test_page_view',
                CASE WHEN RANDOM() < 0.7 THEN user_id ELSE NULL END,
                'session_recharts_' || FLOOR(RANDOM() * 100),
                FORMAT('{"page": "analytics", "test": true, "hour": %s, "day": %s}', 
                       FLOOR(RANDOM() * 24), 
                       FLOOR(RANDOM() * 7))::jsonb,
                '/admin/analytics',
                CASE WHEN RANDOM() < 0.3 THEN '/admin/dashboard' ELSE NULL END
            );
            
            -- Événements d'engagement avec patterns réalistes
            IF RANDOM() < 0.4 THEN
                PERFORM track_analytics_event(
                    'engagement',
                    'recharts_test_engagement',
                    user_id,
                    'session_recharts_' || FLOOR(RANDOM() * 100),
                    FORMAT('{"action": "%s", "test": true, "value": %s}', 
                           CASE FLOOR(RANDOM() * 3)
                               WHEN 0 THEN 'like'
                               WHEN 1 THEN 'comment'
                               ELSE 'share'
                           END,
                           FLOOR(RANDOM() * 100))::jsonb
                );
            END IF;
            
            -- Événements de conversion moins fréquents
            IF RANDOM() < 0.05 THEN
                PERFORM track_analytics_event(
                    'conversion',
                    'recharts_test_conversion',
                    user_id,
                    'session_recharts_' || FLOOR(RANDOM() * 100),
                    FORMAT('{"type": "%s", "test": true, "value": %s}', 
                           CASE FLOOR(RANDOM() * 3)
                               WHEN 0 THEN 'signup'
                               WHEN 1 THEN 'subscription'
                               ELSE 'purchase'
                           END,
                           FLOOR(RANDOM() * 1000))::jsonb
                );
            END IF;
        END LOOP;
        
        RAISE NOTICE '✅ 500+ événements d''analytics créés';
    END IF;
    
    -- Créer des métriques de performance avec variations réalistes
    FOR i IN 1..100 LOOP
        INSERT INTO performance_metrics (metric_name, metric_value, metric_unit, details) VALUES
        ('page_load_time_recharts', 0.8 + RANDOM() * 2.5, 'seconds', 
         FORMAT('{"page": "analytics", "browser": "%s", "test": true}', 
                CASE FLOOR(RANDOM() * 3) WHEN 0 THEN 'chrome' WHEN 1 THEN 'firefox' ELSE 'safari' END)::jsonb),
        ('api_response_time_recharts', 50 + RANDOM() * 300, 'milliseconds',
         FORMAT('{"endpoint": "/api/analytics", "method": "GET", "test": true}')::jsonb),
        ('memory_usage_recharts', 40 + RANDOM() * 40, 'percentage',
         FORMAT('{"server": "analytics", "timestamp": "%s", "test": true}', NOW())::jsonb),
        ('cpu_usage_recharts', 10 + RANDOM() * 60, 'percentage',
         FORMAT('{"server": "analytics", "cores": 4, "test": true}')::jsonb);
    END LOOP;
    
    RAISE NOTICE '✅ 400 métriques de performance créées';
    
END $$;

-- =====================================================
-- 2. CRÉER DES DONNÉES SPÉCIFIQUES POUR CHAQUE GRAPHIQUE
-- =====================================================

-- Données pour les rôles utilisateurs
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '👥 SIMULATION DES RÔLES UTILISATEURS';
    RAISE NOTICE '==================================';
    
    -- Mettre à jour quelques profils avec des rôles variés pour les tests
    UPDATE profiles SET role = 'business' WHERE id IN (
        SELECT id FROM profiles WHERE role = 'user' LIMIT 20
    );
    
    UPDATE profiles SET role = 'premium' WHERE id IN (
        SELECT id FROM profiles WHERE role = 'user' LIMIT 10
    );
    
    UPDATE profiles SET role = 'moderator' WHERE id IN (
        SELECT id FROM profiles WHERE role = 'user' LIMIT 3
    );
    
    RAISE NOTICE '✅ Rôles utilisateurs diversifiés pour les graphiques';
    
END $$;

-- Données pour les catégories d'entreprises
DO $$
DECLARE
    categories text[] := ARRAY['Restaurant', 'Commerce', 'Services', 'Technologie', 'Santé', 'Éducation', 'Immobilier', 'Transport'];
    category text;
    i integer;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🏢 SIMULATION DES CATÉGORIES D''ENTREPRISES';
    RAISE NOTICE '=========================================';
    
    -- Mettre à jour les catégories d'entreprises
    i := 1;
    FOR category IN SELECT unnest(categories) LOOP
        UPDATE business_profiles 
        SET business_category = category 
        WHERE id IN (
            SELECT id FROM business_profiles 
            WHERE business_category IS NULL OR business_category = '' 
            LIMIT 5 + FLOOR(RANDOM() * 10)
        );
        i := i + 1;
    END LOOP;
    
    RAISE NOTICE '✅ Catégories d''entreprises assignées pour les graphiques';
    
END $$;

-- =====================================================
-- 3. VÉRIFIER LES DONNÉES POUR LES GRAPHIQUES
-- =====================================================

-- Vérification des métriques quotidiennes
SELECT 
    '📈 MÉTRIQUES QUOTIDIENNES (derniers 7 jours)' as info,
    date,
    total_users,
    new_users,
    total_businesses,
    total_posts,
    total_engagements
FROM daily_metrics 
WHERE date >= CURRENT_DATE - INTERVAL '7 days'
ORDER BY date DESC;

-- Vérification des rôles utilisateurs
SELECT 
    '👥 RÉPARTITION DES RÔLES' as info,
    role,
    COUNT(*) as count,
    ROUND((COUNT(*)::numeric / (SELECT COUNT(*) FROM profiles) * 100), 1) as percentage
FROM profiles 
GROUP BY role 
ORDER BY count DESC;

-- Vérification des catégories d'entreprises
SELECT 
    '🏢 CATÉGORIES D''ENTREPRISES' as info,
    business_category,
    COUNT(*) as count
FROM business_profiles 
WHERE business_category IS NOT NULL AND business_category != ''
GROUP BY business_category 
ORDER BY count DESC;

-- Vérification des événements d'analytics
SELECT 
    '🎯 ÉVÉNEMENTS D''ANALYTICS' as info,
    event_type,
    COUNT(*) as count,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(DISTINCT session_id) as unique_sessions
FROM analytics_events 
WHERE event_name LIKE '%recharts_test%'
GROUP BY event_type 
ORDER BY count DESC;

-- Vérification des métriques de performance
SELECT 
    '⚡ MÉTRIQUES DE PERFORMANCE' as info,
    metric_name,
    COUNT(*) as count,
    ROUND(AVG(metric_value), 2) as avg_value,
    metric_unit
FROM performance_metrics 
WHERE metric_name LIKE '%recharts%'
GROUP BY metric_name, metric_unit 
ORDER BY count DESC;

-- =====================================================
-- 4. GUIDE DE TEST DES GRAPHIQUES RECHARTS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎯 GUIDE DE TEST DES GRAPHIQUES RECHARTS';
    RAISE NOTICE '=======================================';
    RAISE NOTICE '';
    RAISE NOTICE '📊 GRAPHIQUES À TESTER:';
    RAISE NOTICE '';
    RAISE NOTICE '1️⃣ VUE D''ENSEMBLE:';
    RAISE NOTICE '   • Graphique composé (Area + Line + Bar)';
    RAISE NOTICE '   • Données: Utilisateurs, Entreprises, Publications';
    RAISE NOTICE '   • Fonctionnalités: Tooltip, Légende, Axes multiples';
    RAISE NOTICE '';
    RAISE NOTICE '2️⃣ ONGLET UTILISATEURS:';
    RAISE NOTICE '   • Graphique en secteurs (Pie Chart) - Répartition par rôle';
    RAISE NOTICE '   • Graphique en aires (Area Chart) - Croissance utilisateurs';
    RAISE NOTICE '   • Fonctionnalités: Labels personnalisés, Couleurs, Tooltips';
    RAISE NOTICE '';
    RAISE NOTICE '3️⃣ ONGLET ENTREPRISES:';
    RAISE NOTICE '   • Graphique en barres (Bar Chart) - Catégories d''entreprises';
    RAISE NOTICE '   • Graphique linéaire (Line Chart) - Croissance entreprises';
    RAISE NOTICE '   • Fonctionnalités: Rotation labels, Points, Couleurs';
    RAISE NOTICE '';
    RAISE NOTICE '4️⃣ ONGLET CONTENU:';
    RAISE NOTICE '   • Graphique en secteurs - Types de publications';
    RAISE NOTICE '   • Graphique en aires - Évolution du contenu';
    RAISE NOTICE '   • Fonctionnalités: Empilage, Légendes, Animations';
    RAISE NOTICE '';
    RAISE NOTICE '5️⃣ ONGLET REVENUS:';
    RAISE NOTICE '   • Graphique linéaire - Évolution des revenus';
    RAISE NOTICE '   • Graphique en secteurs - Revenus par source';
    RAISE NOTICE '   • Fonctionnalités: Formatage devise, Pourcentages';
    RAISE NOTICE '';
    RAISE NOTICE '6️⃣ ONGLET ENGAGEMENT:';
    RAISE NOTICE '   • Graphique en aires empilées - Tendances engagement';
    RAISE NOTICE '   • Graphique en barres - Heures de pic';
    RAISE NOTICE '   • Fonctionnalités: Empilage, Formatage heures';
    RAISE NOTICE '';
    RAISE NOTICE '🔍 POINTS À VÉRIFIER:';
    RAISE NOTICE '   ✅ Graphiques s''affichent correctement';
    RAISE NOTICE '   ✅ Tooltips fonctionnent au survol';
    RAISE NOTICE '   ✅ Légendes sont cliquables';
    RAISE NOTICE '   ✅ Animations fluides au chargement';
    RAISE NOTICE '   ✅ Responsive sur différentes tailles';
    RAISE NOTICE '   ✅ Couleurs cohérentes et lisibles';
    RAISE NOTICE '   ✅ Formatage des données correct';
    RAISE NOTICE '   ✅ Axes et labels bien positionnés';
    RAISE NOTICE '';
    RAISE NOTICE '🎊 TESTEZ MAINTENANT LES GRAPHIQUES !';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- 5. STATISTIQUES FINALES
-- =====================================================

DO $$
DECLARE
    total_metrics integer;
    total_events integer;
    total_performance integer;
    date_range text;
BEGIN
    SELECT COUNT(*) INTO total_metrics FROM daily_metrics;
    SELECT COUNT(*) INTO total_events FROM analytics_events WHERE event_name LIKE '%recharts_test%';
    SELECT COUNT(*) INTO total_performance FROM performance_metrics WHERE metric_name LIKE '%recharts%';
    
    SELECT 
        MIN(date)::text || ' → ' || MAX(date)::text 
    INTO date_range 
    FROM daily_metrics;
    
    RAISE NOTICE '';
    RAISE NOTICE '📊 RÉSUMÉ DES DONNÉES CRÉÉES POUR RECHARTS';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '';
    RAISE NOTICE '📈 DONNÉES DISPONIBLES:';
    RAISE NOTICE '   • Métriques quotidiennes: % (période: %)', total_metrics, date_range;
    RAISE NOTICE '   • Événements de test: %', total_events;
    RAISE NOTICE '   • Métriques de performance: %', total_performance;
    RAISE NOTICE '';
    RAISE NOTICE '🎯 GRAPHIQUES PRÊTS:';
    RAISE NOTICE '   ✅ 8+ graphiques recharts interactifs';
    RAISE NOTICE '   ✅ Données réalistes sur 60 jours';
    RAISE NOTICE '   ✅ Variations et tendances simulées';
    RAISE NOTICE '   ✅ Tous types de graphiques (Pie, Bar, Line, Area, Composed)';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 ALLEZ TESTER LES ANALYTICS AVEC RECHARTS !';
    RAISE NOTICE '';
END $$;
