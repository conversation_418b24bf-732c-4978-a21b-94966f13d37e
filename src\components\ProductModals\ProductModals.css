/* Product Modals - Styles Ultra-Avancés */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.modal-container.large {
  max-width: 900px;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-title h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.modal-close:hover {
  background: #e5e7eb;
  color: #374151;
  transform: scale(1.05);
}

.modal-content {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

/* Form Sections */
.form-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f3f4f6;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

/* Form Elements */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group input:disabled,
.form-group select:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.error-text {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 4px;
}

/* Image Upload */
.image-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: all 0.2s ease;
  margin-bottom: 20px;
}

.image-upload-area:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.upload-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.image-previews {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  margin-top: 16px;
}

.image-preview {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-image:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #991b1b;
  font-size: 0.875rem;
  margin-bottom: 20px;
}

/* Modal Actions */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
  margin-top: 24px;
}

.btn-primary,
.btn-secondary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 0.875rem;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.btn-danger {
  background: #ef4444;
  color: white;
  border: 1px solid #dc2626;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
}

.btn-danger:disabled {
  background: #9ca3af;
  border-color: #6b7280;
  cursor: not-allowed;
  transform: none;
}

/* Animations */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-container {
    max-height: 95vh;
  }

  .modal-header {
    padding: 16px;
  }

  .modal-content {
    padding: 16px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .modal-actions {
    flex-direction: column-reverse;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    justify-content: center;
  }

  .image-previews {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .modal-container.large {
    max-width: 100%;
  }

  .section-title {
    font-size: 0.9rem;
  }

  .form-group label {
    font-size: 0.8rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 10px 12px;
    font-size: 0.8rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .modal-container {
    background: #1f2937;
  }

  .modal-header {
    background: #374151;
    border-color: #4b5563;
  }

  .modal-title h2 {
    color: #f9fafb;
  }

  .modal-close {
    background: #4b5563;
    color: #d1d5db;
  }

  .modal-close:hover {
    background: #6b7280;
    color: #f9fafb;
  }

  .section-title {
    color: #f9fafb;
    border-color: #4b5563;
  }

  .form-group label {
    color: #d1d5db;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    border-color: #3b82f6;
  }

  .image-upload-area {
    border-color: #4b5563;
    background: #374151;
  }

  .image-upload-area:hover {
    border-color: #3b82f6;
    background: #4b5563;
  }
}

/* Accessibility */
.modal-container:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.btn-primary:focus,
.btn-secondary:focus,
.modal-close:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Product Details Modal Styles */
.product-header {
  margin-bottom: 32px;
}

.product-main-info {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.product-image-gallery {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.main-product-image {
  width: 100%;
  aspect-ratio: 1;
  object-fit: cover;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.placeholder-image {
  width: 100%;
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  color: #9ca3af;
  gap: 8px;
}

.image-thumbnails {
  display: flex;
  gap: 8px;
}

.thumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s ease;
}

.thumbnail:hover {
  border-color: #3b82f6;
  transform: scale(1.05);
}

.more-images {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.product-title-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.product-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-badge,
.feature-badge,
.trending-badge,
.sale-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.status-active {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.status-inactive {
  background: #f3f4f6;
  color: #6b7280;
}

.status-badge.status-out-of-stock {
  background: #fee2e2;
  color: #991b1b;
}

.feature-badge {
  background: #fef3c7;
  color: #92400e;
}

.trending-badge {
  background: #dbeafe;
  color: #1e40af;
}

.sale-badge {
  background: #fecaca;
  color: #991b1b;
}

.product-pricing {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 16px 0;
}

.current-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.original-price {
  font-size: 1rem;
  color: #9ca3af;
  text-decoration: line-through;
}

.discount-percentage {
  padding: 4px 8px;
  background: #ef4444;
  color: white;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stars {
  display: flex;
  gap: 2px;
}

.rating-text {
  font-size: 0.875rem;
  color: #6b7280;
}

.product-description {
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

.product-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.detail-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.detail-section .section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.detail-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.detail-item .label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  min-width: 120px;
}

.detail-item .value {
  font-size: 0.875rem;
  color: #1f2937;
  font-weight: 500;
  text-align: right;
}

.stock-value.stock-good {
  color: #059669;
}

.stock-value.stock-low {
  color: #d97706;
}

.stock-value.stock-empty {
  color: #dc2626;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag {
  padding: 2px 8px;
  background: #e5e7eb;
  color: #374151;
  border-radius: 4px;
  font-size: 0.75rem;
}

.text-muted {
  color: #9ca3af !important;
  font-style: italic;
}

/* Responsive pour les détails */
@media (max-width: 768px) {
  .product-main-info {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .product-details-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .detail-item .label {
    min-width: auto;
  }

  .detail-item .value {
    text-align: left;
  }
}

/* Delete Modal Styles */
.warning-section {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 12px;
  margin-bottom: 24px;
}

.warning-icon {
  flex-shrink: 0;
}

.warning-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #991b1b;
  margin: 0 0 8px 0;
}

.warning-content p {
  color: #7f1d1d;
  margin: 0;
  line-height: 1.5;
}

.impact-section,
.alternatives-section,
.confirmation-section {
  margin-bottom: 24px;
}

.impact-section h4,
.alternatives-section h4,
.confirmation-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.impact-items,
.alternatives-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.impact-item,
.alternative-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
}

.impact-item.warning {
  background: #fef3c7;
  color: #92400e;
}

.impact-item.info {
  background: #dbeafe;
  color: #1e40af;
}

.alternative-item {
  background: #f0fdf4;
  color: #166534;
}

.confirmation-section p {
  color: #6b7280;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.confirmation-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  transition: all 0.2s ease;
}

.confirmation-input:focus {
  outline: none;
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.confirmation-input.valid {
  border-color: #10b981;
  background: #f0fdf4;
  color: #166534;
}

.confirmation-input.valid:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.confirmation-error {
  margin-top: 8px;
  font-size: 0.75rem;
  color: #ef4444;
  text-align: center;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .modal-overlay,
  .modal-container,
  .upload-button,
  .btn-primary,
  .btn-secondary,
  .spinning,
  .thumbnail {
    animation: none;
    transition: none;
  }

  .upload-button:hover,
  .btn-primary:hover,
  .modal-close:hover,
  .thumbnail:hover {
    transform: none;
  }
}
