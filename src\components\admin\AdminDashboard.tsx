import React, { useState, useEffect } from 'react';
import {
  Users,
  Building2,
  FileText,
  ShoppingCart,
  Shield,
  AlertTriangle,
  TrendingUp,
  DollarSign,
  Activity,
  Clock,
  CheckCircle,
  XCircle,
  CreditCard
} from 'lucide-react';
import Card, { CardBody } from '../ui/Card';
import Button from '../ui/Button';
import { AdminService } from '../../services/adminService';
import { AdminDashboardStats, AdminAlert, AlertPriority, AlertStatus } from '../../types/admin';
import { formatAmount } from '../../utils/formatUtils';

interface AdminDashboardProps {
  onNavigateToSection: (section: string) => void;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ onNavigateToSection }) => {
  const [stats, setStats] = useState<AdminDashboardStats | null>(null);
  const [alerts, setAlerts] = useState<AdminAlert[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [dashboardStats, pendingAlerts] = await Promise.all([
        AdminService.getDashboardStats(),
        AdminService.getAlerts(AlertStatus.OPEN)
      ]);

      setStats(dashboardStats);
      setAlerts(pendingAlerts.slice(0, 5)); // Afficher seulement les 5 premières
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPriorityColor = (priority: AlertPriority) => {
    switch (priority) {
      case AlertPriority.CRITICAL:
        return 'text-red-600 bg-red-100';
      case AlertPriority.HIGH:
        return 'text-orange-600 bg-orange-100';
      case AlertPriority.MEDIUM:
        return 'text-yellow-600 bg-yellow-100';
      case AlertPriority.LOW:
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: number | string;
    icon: React.ReactNode;
    color: string;
    onClick?: () => void;
    trend?: { value: number; isPositive: boolean };
  }> = ({ title, value, icon, color, onClick, trend }) => (
    <Card className={`cursor-pointer hover:shadow-lg transition-shadow ${onClick ? 'hover:bg-gray-50' : ''}`} onClick={onClick}>
      <CardBody className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-3xl font-bold text-gray-900">{value}</p>
            {trend && (
              <div className={`flex items-center mt-2 text-sm ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                <TrendingUp size={16} className={`mr-1 ${trend.isPositive ? '' : 'rotate-180'}`} />
                {trend.value}%
              </div>
            )}
          </div>
          <div className={`p-3 rounded-full ${color}`}>
            {icon}
          </div>
        </div>
      </CardBody>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Tableau de Bord Administrateur</h1>
          <p className="text-gray-600 mt-2">Vue d'ensemble de la plateforme Customeroom</p>
        </div>
        <Button
          onClick={() => loadDashboardData()}
          leftIcon={<Activity size={16} />}
          variant="outline"
        >
          Actualiser
        </Button>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Utilisateurs Total"
          value={stats?.totalUsers || 0}
          icon={<Users size={24} className="text-white" />}
          color="bg-blue-500"
          onClick={() => onNavigateToSection('users')}
        />
        <StatCard
          title="Entreprises"
          value={stats?.totalBusinesses || 0}
          icon={<Building2 size={24} className="text-white" />}
          color="bg-green-500"
          onClick={() => onNavigateToSection('businesses')}
        />
        <StatCard
          title="Publications"
          value={stats?.totalPosts || 0}
          icon={<FileText size={24} className="text-white" />}
          color="bg-purple-500"
          onClick={() => onNavigateToSection('content')}
        />
        <StatCard
          title="Commandes"
          value={stats?.totalOrders || 0}
          icon={<ShoppingCart size={24} className="text-white" />}
          color="bg-orange-500"
          onClick={() => onNavigateToSection('orders')}
        />
      </div>

      {/* Métriques secondaires */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Admins Actifs"
          value={stats?.activeAdmins || 0}
          icon={<Shield size={24} className="text-white" />}
          color="bg-indigo-500"
          onClick={() => onNavigateToSection('admins')}
        />
        <StatCard
          title="Alertes en Attente"
          value={stats?.pendingAlerts || 0}
          icon={<AlertTriangle size={24} className="text-white" />}
          color="bg-red-500"
          onClick={() => onNavigateToSection('alerts')}
        />
        <StatCard
          title="Inscriptions Aujourd'hui"
          value={stats?.todayRegistrations || 0}
          icon={<Clock size={24} className="text-white" />}
          color="bg-teal-500"
        />
        <StatCard
          title="Revenus Mensuels"
          value={formatAmount(stats?.monthlyRevenue || 0)}
          icon={<DollarSign size={24} className="text-white" />}
          color="bg-emerald-500"
          onClick={() => onNavigateToSection('finance')}
        />
      </div>

      {/* Alertes récentes et actions rapides */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Alertes récentes */}
        <Card>
          <CardBody className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Alertes Récentes</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onNavigateToSection('alerts')}
              >
                Voir tout
              </Button>
            </div>
            <div className="space-y-3">
              {alerts.length > 0 ? (
                alerts.map((alert) => (
                  <div key={alert.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className={`p-1 rounded-full ${getPriorityColor(alert.priority)}`}>
                      <AlertTriangle size={16} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {alert.title}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {new Date(alert.created_at).toLocaleDateString('fr-FR')}
                      </p>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(alert.priority)}`}>
                      {alert.priority}
                    </span>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <CheckCircle size={48} className="mx-auto mb-2 text-green-500" />
                  <p>Aucune alerte en attente</p>
                </div>
              )}
            </div>
          </CardBody>
        </Card>

        {/* Actions rapides */}
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions Rapides</h3>
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                className="h-20 flex-col"
                onClick={() => onNavigateToSection('users')}
              >
                <Users size={24} className="mb-2" />
                <span className="text-sm">Gérer Utilisateurs</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex-col"
                onClick={() => onNavigateToSection('businesses')}
              >
                <Building2 size={24} className="mb-2" />
                <span className="text-sm">Gérer Entreprises</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex-col"
                onClick={() => onNavigateToSection('content')}
              >
                <FileText size={24} className="mb-2" />
                <span className="text-sm">Modérer Contenu</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex-col"
                onClick={() => onNavigateToSection('payments')}
              >
                <CreditCard size={24} className="mb-2" />
                <span className="text-sm">Gérer Paiements</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex-col"
                onClick={() => onNavigateToSection('analytics')}
              >
                <TrendingUp size={24} className="mb-2" />
                <span className="text-sm">Analytics</span>
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Graphiques de croissance */}
      {stats && (stats.userGrowth.length > 0 || stats.businessGrowth.length > 0) && (
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Croissance (30 derniers jours)</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Graphique utilisateurs */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Nouveaux Utilisateurs</h4>
                <div className="h-32 bg-gray-100 rounded-lg flex items-end justify-center p-4">
                  <p className="text-gray-500">Graphique à implémenter</p>
                </div>
              </div>
              {/* Graphique entreprises */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Nouvelles Entreprises</h4>
                <div className="h-32 bg-gray-100 rounded-lg flex items-end justify-center p-4">
                  <p className="text-gray-500">Graphique à implémenter</p>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default AdminDashboard;
