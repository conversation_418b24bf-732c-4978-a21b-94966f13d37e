-- Migration pour améliorer la table profiles avec des colonnes supplémentaires

-- Add status column to profiles if it doesn't exist, with default 'newbie' 
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'newbie'; 
 
-- Add post_count column if it doesn't exist 
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS post_count INTEGER DEFAULT 0; 
 
-- Add comment_count column if it doesn't exist 
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS comment_count INTEGER DEFAULT 0; 
 
-- Add profile_completed_at column if it doesn't exist 
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS profile_completed_at TIMESTAMPTZ; 
 
-- Ensure existing users have a default status if the column was just added and is null for them 
UPDATE public.profiles 
SET status = 'newbie' 
WHERE status IS NULL; 

-- Add comment to explain the purpose of these columns
COMMENT ON COLUMN public.profiles.status IS 'Status de l''utilisateur (newbie, regular, expert, etc.)';
COMMENT ON COLUMN public.profiles.post_count IS 'Nombre total de posts créés par l''utilisateur';
COMMENT ON COLUMN public.profiles.comment_count IS 'Nombre total de commentaires créés par l''utilisateur';
COMMENT ON COLUMN public.profiles.profile_completed_at IS 'Date à laquelle l''utilisateur a complété son profil';