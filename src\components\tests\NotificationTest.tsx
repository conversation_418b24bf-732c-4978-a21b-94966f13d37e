import React, { useState } from 'react';
import { usePosts } from '../../context/PostsContext';
import { useAuth } from '../../context/AuthContext';
import { useNotifications } from '../../context/NotificationsContext';
import { PostType } from '../../types';
import Button from '../ui/Button';
import Card, { CardBody } from '../ui/Card';

const NotificationTest: React.FC = () => {
  const { currentUser } = useAuth();
  const { createPost } = usePosts();
  const { notifications } = useNotifications();
  const [businessName, setBusinessName] = useState('');
  const [productName, setProductName] = useState('');
  const [description, setDescription] = useState('');
  const [rating, setRating] = useState<number>(5);
  const [postType, setPostType] = useState<PostType>(PostType.REVIEW);
  const [testResult, setTestResult] = useState<string>('');

  const handleCreateTestPost = async () => {
    if (!currentUser) {
      setTestResult('Vous devez être connecté pour créer un post de test');
      return;
    }

    if (!businessName) {
      setTestResult('Veuillez entrer un nom d\'entreprise');
      return;
    }

    try {
      setTestResult('Création du post de test...');
      
      await createPost({
        userId: currentUser.id,
        username: currentUser.username,
        userProfilePicture: currentUser.profilePicture,
        type: postType,
        businessName,
        productName: productName || 'Produit test',
        category: 'Test',
        description: description || `Ceci est un post de test mentionnant l'entreprise ${businessName}`,
        rating: rating,
        tags: ['test', 'notification']
      });
      
      setTestResult(`Post de test créé avec succès! Une notification devrait être envoyée à l'entreprise ${businessName}`);
    } catch (error) {
      console.error('Erreur lors de la création du post de test:', error);
      setTestResult(`Erreur lors de la création du post de test: ${error}`);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Test du système de notifications pour entreprises</h1>
      
      <Card className="mb-6">
        <CardBody>
          <h2 className="text-lg font-semibold mb-3">Créer un post de test</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nom de l'entreprise
              </label>
              <input
                type="text"
                value={businessName}
                onChange={(e) => setBusinessName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Entrez le nom exact d'une entreprise"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nom du produit
              </label>
              <input
                type="text"
                value={productName}
                onChange={(e) => setProductName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Nom du produit (optionnel)"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                rows={3}
                placeholder={`Ceci est un post de test mentionnant l'entreprise ${businessName || '[nom de l\'entreprise]'}`}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type de post
              </label>
              <select
                value={postType}
                onChange={(e) => setPostType(e.target.value as PostType)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value={PostType.REVIEW}>Avis (Review)</option>
                <option value={PostType.FAVORITE}>Coup de coeur</option>
                <option value={PostType.COMPLAINT}>Coup de gueule</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Note (1-5)
              </label>
              <input
                type="number"
                min="1"
                max="5"
                value={rating}
                onChange={(e) => setRating(Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            
            <Button 
              onClick={handleCreateTestPost}
              className="w-full"
            >
              Créer un post de test
            </Button>
            
            {testResult && (
              <div className={`p-3 rounded-md ${testResult.includes('succès') ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                {testResult}
              </div>
            )}
          </div>
        </CardBody>
      </Card>
      
      <Card>
        <CardBody>
          <h2 className="text-lg font-semibold mb-3">Notifications récentes ({notifications.length})</h2>
          
          {notifications.length === 0 ? (
            <div className="text-center text-gray-500 py-4">
              Aucune notification pour le moment
            </div>
          ) : (
            <div className="space-y-3">
              {notifications.map(notification => (
                <div key={notification.id} className={`p-3 border rounded-md ${!notification.read ? 'bg-blue-50 border-blue-200' : 'border-gray-200'}`}>
                  <div className="flex items-start">
                    <div className="flex-1">
                      <p className="text-sm font-medium">{notification.type}</p>
                      <p className="text-sm text-gray-700">{notification.message}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        {new Date(notification.createdAt).toLocaleString()}
                      </p>
                    </div>
                    {!notification.read && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

export default NotificationTest;
