import { RealBusinessDataService } from './realBusinessDataService';

/**
 * Service d'analyse IA utilisant les vraies données business
 */
export class RealAIAnalysisService {

  /**
   * Analyse les vraies données pour générer des recommandations stratégiques
   * Intègre maintenant les avis des utilisateurs pour une analyse plus complète
   */
  static async generateStrategicRecommendations(businessId: string) {
    try {
      console.log('🤖 Génération des recommandations stratégiques pour:', businessId);
      console.log('📊 Intégration des avis utilisateurs dans l\'analyse IA...');

      // Vérifier la qualité des données
      const dataQuality = await RealBusinessDataService.hasEnoughDataForAnalysis(businessId);

      if (!dataQuality.hasEnoughData) {
        // Utiliser des données de test pour la démonstration
        console.log('📊 Utilisation des données de test pour la démonstration');
        const { MockBusinessDataService } = await import('./mockBusinessDataService');
        const mockRecommendations = MockBusinessDataService.generateMockRecommendations();

        return {
          recommendations: mockRecommendations,
          dataQuality: {
            ...dataQuality,
            hasEnoughData: true, // Pour la démo
            dataQuality: 'fair' as const,
            recommendations: ['Données de test utilisées pour la démonstration']
          },
          lastAnalysis: new Date().toISOString(),
          usingMockData: true
        };
      }

      // Récupérer les données complètes
      const businessData = await RealBusinessDataService.getCompleteBusinessData(businessId);

      console.log('📊 Données business récupérées:', {
        posts: businessData.posts.length,
        products: businessData.products.length,
        ratings: businessData.ratings.length,
        totalDataPoints: businessData.metadata.totalDataPoints
      });

      // Si nous avons des vraies données, les analyser
      if (businessData.metadata.totalDataPoints > 0) {
        console.log('🔍 Analyse des vraies données de Dexima...');
        console.log('📝 Analyse approfondie des avis utilisateurs...');

        // Analyser spécifiquement les avis pour des insights plus précis
        const reviewsAnalysis = this.analyzeUserReviews(businessData.posts);
        console.log('✅ Analyse des avis terminée:', reviewsAnalysis);

        const recommendations = await this.analyzeBusinessData(businessData, reviewsAnalysis);

        return {
          recommendations,
          dataQuality,
          reviewsAnalysis,
          lastAnalysis: new Date().toISOString(),
          dataPoints: businessData.metadata.totalDataPoints,
          usingRealData: true
        };
      }

      // Sinon, utiliser les données de test
      console.log('📊 Pas assez de vraies données, utilisation des données de test');
      const { MockBusinessDataService } = await import('./mockBusinessDataService');
      const mockRecommendations = MockBusinessDataService.generateMockRecommendations();

      return {
        recommendations: mockRecommendations,
        dataQuality: {
          ...dataQuality,
          hasEnoughData: true,
          dataQuality: 'fair' as const,
          recommendations: ['Données de test utilisées pour la démonstration']
        },
        lastAnalysis: new Date().toISOString(),
        usingMockData: true
      };
    } catch (error) {
      console.error('❌ Erreur dans generateStrategicRecommendations:', error);
      return {
        recommendations: [],
        dataQuality: { hasEnoughData: false, dataQuality: 'poor' as const, recommendations: [], dataPoints: { posts: 0, products: 0, ratings: 0, total: 0 } },
        lastAnalysis: new Date().toISOString(),
        error: 'Erreur lors de l\'analyse'
      };
    }
  }

  /**
   * Analyse les données business pour identifier les opportunités
   */
  private static async analyzeBusinessData(data: any) {
    const recommendations: any[] = [];

    // Analyse des ratings
    const ratingsAnalysis = this.analyzeRatings(data.ratings);
    if (ratingsAnalysis.recommendation) {
      recommendations.push(ratingsAnalysis.recommendation);
    }

    // Analyse des produits
    const productsAnalysis = this.analyzeProducts(data.products);
    if (productsAnalysis.recommendation) {
      recommendations.push(productsAnalysis.recommendation);
    }

    // Analyse des posts/avis
    const postsAnalysis = this.analyzePosts(data.posts);
    if (postsAnalysis.recommendations) {
      recommendations.push(...postsAnalysis.recommendations);
    } else if (postsAnalysis.recommendation) {
      recommendations.push(postsAnalysis.recommendation);
    }

    // Analyse des tendances
    const trendsAnalysis = this.analyzeTrends(data);
    recommendations.push(...trendsAnalysis);

    return recommendations;
  }

  /**
   * Analyse les ratings pour identifier les problèmes de satisfaction
   */
  private static analyzeRatings(ratings: any[]) {
    if (ratings.length === 0) {
      return {
        recommendation: {
          id: 'collect-ratings',
          title: 'Collectez Plus d\'Avis Clients',
          description: 'Vous n\'avez pas encore d\'avis clients. Encouragez vos clients à noter vos produits.',
          priority: 'high' as const,
          confidence: 95,
          impact: 'high' as const,
          effort: 'medium' as const,
          category: 'customer-satisfaction' as const,
          actionPlan: [
            'Envoyez des emails de suivi après chaque vente',
            'Offrez des incitations pour les avis (réductions, points fidélité)',
            'Simplifiez le processus de notation',
            'Répondez rapidement aux avis existants'
          ],
          estimatedROI: '25-40%',
          timeframe: '2-4 semaines'
        }
      };
    }

    const averageRating = ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length;
    const lowRatings = ratings.filter(r => r.rating <= 2).length;
    const highRatings = ratings.filter(r => r.rating >= 4).length;

    if (averageRating < 3.5) {
      return {
        recommendation: {
          id: 'improve-satisfaction',
          title: 'Améliorer la Satisfaction Client',
          description: `Votre note moyenne (${averageRating.toFixed(1)}/5) indique des problèmes de satisfaction. ${lowRatings} avis négatifs détectés.`,
          priority: 'urgent' as const,
          confidence: 90,
          impact: 'high' as const,
          effort: 'high' as const,
          category: 'customer-satisfaction' as const,
          actionPlan: [
            'Analysez les avis négatifs pour identifier les problèmes récurrents',
            'Contactez directement les clients insatisfaits',
            'Améliorez la qualité des produits/services problématiques',
            'Formez votre équipe au service client',
            'Mettez en place un système de suivi qualité'
          ],
          estimatedROI: '30-50%',
          timeframe: '4-8 semaines'
        }
      };
    }

    if (averageRating >= 4.5 && highRatings > lowRatings * 3) {
      return {
        recommendation: {
          id: 'leverage-satisfaction',
          title: 'Exploitez Votre Excellente Réputation',
          description: `Excellente note moyenne (${averageRating.toFixed(1)}/5) ! Utilisez cette force pour développer votre business.`,
          priority: 'medium' as const,
          confidence: 85,
          impact: 'medium' as const,
          effort: 'low' as const,
          category: 'marketing' as const,
          actionPlan: [
            'Mettez en avant vos avis positifs sur votre site',
            'Créez des témoignages clients vidéo',
            'Demandez des recommandations à vos clients satisfaits',
            'Utilisez les avis dans vos campagnes marketing',
            'Proposez un programme de parrainage'
          ],
          estimatedROI: '15-25%',
          timeframe: '2-3 semaines'
        }
      };
    }

    return { recommendation: null };
  }

  /**
   * Analyse les produits pour identifier les opportunités
   */
  private static analyzeProducts(products: any[]) {
    if (products.length === 0) {
      return {
        recommendation: {
          id: 'add-products',
          title: 'Développez Votre Catalogue Produits',
          description: 'Vous n\'avez pas encore de produits sur le marketplace. C\'est une opportunité manquée !',
          priority: 'high' as const,
          confidence: 95,
          impact: 'high' as const,
          effort: 'medium' as const,
          category: 'product-development' as const,
          actionPlan: [
            'Identifiez vos 3-5 meilleurs produits/services',
            'Créez des descriptions détaillées et attractives',
            'Prenez des photos professionnelles',
            'Définissez une stratégie de prix compétitive',
            'Lancez avec une offre promotionnelle'
          ],
          estimatedROI: '40-60%',
          timeframe: '1-2 semaines'
        }
      };
    }

    // Analyser les performances des produits
    const productsWithRatings = products.filter(p => p.average_rating > 0);
    const lowPerformingProducts = products.filter(p => p.average_rating < 3.5);
    const highPerformingProducts = products.filter(p => p.average_rating >= 4.5);

    if (lowPerformingProducts.length > 0) {
      return {
        recommendation: {
          id: 'improve-products',
          title: 'Améliorez Vos Produits Sous-Performants',
          description: `${lowPerformingProducts.length} produit(s) ont des notes faibles. Analysez et améliorez-les.`,
          priority: 'high' as const,
          confidence: 85,
          impact: 'medium' as const,
          effort: 'medium' as const,
          category: 'product-development' as const,
          actionPlan: [
            'Analysez les commentaires négatifs sur ces produits',
            'Identifiez les défauts récurrents',
            'Améliorez la qualité ou reformulez le produit',
            'Ajustez les prix si nécessaire',
            'Relancez avec une communication sur les améliorations'
          ],
          estimatedROI: '20-35%',
          timeframe: '3-6 semaines'
        }
      };
    }

    return { recommendation: null };
  }

  /**
   * Analyse les posts pour identifier les tendances selon la structure définie :
   * - Demande d'avis : Posts d'entreprise pour recueillir des avis
   * - Coup de cœur : Posts de consommateurs satisfaits partageant leur expérience positive
   * - Coup de gueule : Posts de consommateurs insatisfaits partageant leur expérience négative
   */
  private static analyzePosts(posts: any[]) {
    if (posts.length === 0) {
      return { recommendations: [] };
    }

    const recentPosts = posts.filter(p => {
      const postDate = new Date(p.created_at);
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return postDate > thirtyDaysAgo;
    });

    // Analyser les types de posts selon la structure définie
    const coupDeGueule = posts.filter(p => p.type === 'coup_de_gueule'); // Consommateurs insatisfaits
    const coupDeCoeur = posts.filter(p => p.type === 'coup_de_coeur'); // Consommateurs satisfaits
    const demandeAvis = posts.filter(p => p.type === 'demande_avis' || p.source === 'business_post'); // Posts d'entreprise

    // Analyser l'engagement selon les types d'interactions définis :
    // - J'aime, Commentaires (favorables/défavorables), Partages, Recommandations
    const totalLikes = posts.reduce((sum, p) => sum + (p.likesCount || 0), 0);
    const totalShares = posts.reduce((sum, p) => sum + (p.sharesCount || 0), 0);
    const totalRecommendations = posts.reduce((sum, p) => sum + (p.recommendationsCount || 0), 0);
    const totalComments = posts.reduce((sum, p) => sum + (p.commentsCount || 0), 0);

    const totalEngagement = totalLikes + totalShares + totalRecommendations + totalComments;
    const averageEngagement = posts.length > 0 ? totalEngagement / posts.length : 0;

    // Analyser les commentaires favorables vs défavorables
    const favorableComments = posts.reduce((sum, p) => {
      // Estimation basée sur les posts positifs vs négatifs
      return sum + (p.type === 'coup_de_coeur' ? (p.commentsCount || 0) * 0.8 : 0);
    }, 0);

    const unfavorableComments = posts.reduce((sum, p) => {
      return sum + (p.type === 'coup_de_gueule' ? (p.commentsCount || 0) * 0.8 : 0);
    }, 0);

    // Recommandations basées sur l'analyse
    const recommendations = [];

    // 1. Gestion des expériences négatives (coups de gueule)
    if (coupDeGueule.length > coupDeCoeur.length) {
      recommendations.push({
        id: 'address-complaints',
        title: 'Gérez les Expériences Négatives',
        description: `Plus de "coups de gueule" (${coupDeGueule.length}) que de "coups de cœur" (${coupDeCoeur.length}). Les consommateurs insatisfaits partagent leurs mauvaises expériences.`,
        priority: 'urgent' as const,
        confidence: 85,
        impact: 'high' as const,
        effort: 'medium' as const,
        category: 'customer-service' as const,
        actionPlan: [
          'Répondez rapidement à tous les "coups de gueule"',
          'Analysez les produits/services les plus critiqués',
          'Contactez directement les consommateurs insatisfaits',
          'Mettez en place des actions correctives spécifiques',
          'Transformez les expériences négatives en positives',
          'Encouragez les clients satisfaits à partager leurs "coups de cœur"'
        ],
        estimatedROI: '25-40%',
        timeframe: '2-4 semaines'
      });
    }

    // 2. Améliorer l'engagement (j'aime, commentaires, partages, recommandations)
    if (averageEngagement < 5) {
      recommendations.push({
        id: 'boost-engagement',
        title: 'Augmentez l\'Engagement Client',
        description: `Engagement moyen faible (${averageEngagement.toFixed(1)} interactions/post). Peu de j'aime, commentaires, partages et recommandations.`,
        priority: 'medium' as const,
        confidence: 75,
        impact: 'medium' as const,
        effort: 'medium' as const,
        category: 'marketing' as const,
        actionPlan: [
          'Encouragez les clients satisfaits à partager leurs "coups de cœur"',
          'Répondez à tous les commentaires (favorables et défavorables)',
          'Créez du contenu qui incite aux recommandations',
          'Organisez des événements pour générer des partages',
          'Remerciez publiquement les clients qui recommandent vos produits'
        ],
        estimatedROI: '15-30%',
        timeframe: '3-6 semaines'
      });
    }

    // 3. Optimiser les demandes d'avis
    if (demandeAvis.length > 0) {
      const demandeAvisEngagement = demandeAvis.reduce((sum, p) => {
        return sum + (p.commentsCount || 0);
      }, 0) / demandeAvis.length;

      if (demandeAvisEngagement < 3) {
        recommendations.push({
          id: 'improve-review-requests',
          title: 'Optimisez vos Demandes d\'Avis',
          description: `Vos demandes d'avis génèrent peu de réponses (${demandeAvisEngagement.toFixed(1)} avis/post en moyenne). Les consommateurs ne réagissent pas assez.`,
          priority: 'medium' as const,
          confidence: 70,
          impact: 'medium' as const,
          effort: 'low' as const,
          category: 'customer-engagement' as const,
          actionPlan: [
            'Personnalisez vos demandes d\'avis par produit/service',
            'Offrez des incitations pour encourager les avis (réductions, points)',
            'Posez des questions spécifiques dans vos demandes',
            'Ciblez les clients qui ont récemment acheté',
            'Remerciez et répondez à tous les avis reçus',
            'Partagez les avis positifs pour encourager d\'autres à participer'
          ],
          estimatedROI: '20-35%',
          timeframe: '2-4 semaines'
        });
      }
    }

    // 4. Analyser les recommandations de produits
    if (totalRecommendations > 0) {
      const recommendationRate = totalRecommendations / posts.length;
      if (recommendationRate > 0.3) {
        recommendations.push({
          id: 'leverage-recommendations',
          title: 'Exploitez vos Recommandations',
          description: `Excellent taux de recommandations (${(recommendationRate * 100).toFixed(1)}%). Vos produits sont recommandés par les consommateurs !`,
          priority: 'low' as const,
          confidence: 90,
          impact: 'high' as const,
          effort: 'low' as const,
          category: 'marketing' as const,
          actionPlan: [
            'Mettez en avant les produits les plus recommandés',
            'Créez une section "Produits Recommandés" sur votre profil',
            'Contactez les clients qui recommandent pour des témoignages',
            'Utilisez ces recommandations dans votre communication marketing',
            'Récompensez les clients qui recommandent vos produits'
          ],
          estimatedROI: '30-50%',
          timeframe: '1-2 semaines'
        });
      }
    }

    return { recommendations };
  }

  /**
   * Analyse spécifique des avis utilisateurs pour des insights plus précis
   */
  private static analyzeUserReviews(posts: any[]) {
    console.log('🔍 Analyse détaillée des avis utilisateurs...');

    const analysis = {
      totalReviews: posts.length,
      byType: {
        coupDeCoeur: posts.filter(p => p.type === 'coup_de_coeur'),
        coupDeGueule: posts.filter(p => p.type === 'coup_de_gueule'),
        demandeAvis: posts.filter(p => p.type === 'demande_avis')
      },
      sentimentAnalysis: {
        positive: 0,
        negative: 0,
        neutral: 0
      },
      productMentions: new Map<string, { positive: number; negative: number; total: number }>(),
      categoryAnalysis: new Map<string, { positive: number; negative: number; total: number }>(),
      commonIssues: [] as string[],
      positiveAspects: [] as string[],
      engagementMetrics: {
        averageLikes: 0,
        averageShares: 0,
        averageRecommendations: 0,
        averageComments: 0
      }
    };

    // Analyser chaque avis
    posts.forEach(post => {
      // Analyse de sentiment basée sur le type
      if (post.type === 'coup_de_coeur') {
        analysis.sentimentAnalysis.positive++;
      } else if (post.type === 'coup_de_gueule') {
        analysis.sentimentAnalysis.negative++;
      } else {
        analysis.sentimentAnalysis.neutral++;
      }

      // Analyser les mentions de produits
      const productName = post.product_name || 'Produit général';
      if (!analysis.productMentions.has(productName)) {
        analysis.productMentions.set(productName, { positive: 0, negative: 0, total: 0 });
      }
      const productData = analysis.productMentions.get(productName)!;
      productData.total++;
      if (post.type === 'coup_de_coeur') productData.positive++;
      if (post.type === 'coup_de_gueule') productData.negative++;

      // Analyser les catégories
      const category = post.category || 'Général';
      if (!analysis.categoryAnalysis.has(category)) {
        analysis.categoryAnalysis.set(category, { positive: 0, negative: 0, total: 0 });
      }
      const categoryData = analysis.categoryAnalysis.get(category)!;
      categoryData.total++;
      if (post.type === 'coup_de_coeur') categoryData.positive++;
      if (post.type === 'coup_de_gueule') categoryData.negative++;

      // Analyser les problèmes récurrents dans les avis négatifs
      if (post.type === 'coup_de_gueule' && post.description) {
        const description = post.description.toLowerCase();
        if (description.includes('lent') || description.includes('délai')) {
          analysis.commonIssues.push('Délais de livraison/service');
        }
        if (description.includes('cher') || description.includes('prix')) {
          analysis.commonIssues.push('Prix élevés');
        }
        if (description.includes('qualité') || description.includes('défaut')) {
          analysis.commonIssues.push('Problèmes de qualité');
        }
        if (description.includes('service') || description.includes('accueil')) {
          analysis.commonIssues.push('Service client');
        }
      }

      // Analyser les aspects positifs
      if (post.type === 'coup_de_coeur' && post.description) {
        const description = post.description.toLowerCase();
        if (description.includes('rapide') || description.includes('vite')) {
          analysis.positiveAspects.push('Rapidité du service');
        }
        if (description.includes('qualité') || description.includes('excellent')) {
          analysis.positiveAspects.push('Excellente qualité');
        }
        if (description.includes('prix') || description.includes('abordable')) {
          analysis.positiveAspects.push('Prix attractifs');
        }
        if (description.includes('service') || description.includes('accueil')) {
          analysis.positiveAspects.push('Excellent service client');
        }
      }

      // Calculer les métriques d'engagement
      analysis.engagementMetrics.averageLikes += (post.likesCount || 0);
      analysis.engagementMetrics.averageShares += (post.sharesCount || 0);
      analysis.engagementMetrics.averageRecommendations += (post.recommendationsCount || 0);
      analysis.engagementMetrics.averageComments += (post.commentsCount || 0);
    });

    // Finaliser les moyennes d'engagement
    if (posts.length > 0) {
      analysis.engagementMetrics.averageLikes /= posts.length;
      analysis.engagementMetrics.averageShares /= posts.length;
      analysis.engagementMetrics.averageRecommendations /= posts.length;
      analysis.engagementMetrics.averageComments /= posts.length;
    }

    // Convertir les Maps en objets pour faciliter l'utilisation
    const result = {
      ...analysis,
      productMentions: Object.fromEntries(analysis.productMentions),
      categoryAnalysis: Object.fromEntries(analysis.categoryAnalysis),
      // Compter les occurrences des problèmes et aspects positifs
      commonIssues: this.countOccurrences(analysis.commonIssues),
      positiveAspects: this.countOccurrences(analysis.positiveAspects)
    };

    console.log('📊 Résultats de l\'analyse des avis:', result);
    return result;
  }

  /**
   * Compte les occurrences d'éléments dans un tableau
   */
  private static countOccurrences(items: string[]) {
    const counts: { [key: string]: number } = {};
    items.forEach(item => {
      counts[item] = (counts[item] || 0) + 1;
    });
    return Object.entries(counts)
      .map(([item, count]) => ({ item, count }))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Analyse les tendances générales
   */
  private static analyzeTrends(data: any) {
    const recommendations: any[] = [];

    // Tendance d'activité
    const recentActivity = this.calculateRecentActivity(data);
    if (recentActivity.isDecreasing) {
      recommendations.push({
        id: 'boost-engagement',
        title: 'Relancez l\'Engagement Client',
        description: 'Votre activité client a diminué récemment. Il est temps de réengager votre audience.',
        priority: 'medium' as const,
        confidence: 75,
        impact: 'medium' as const,
        effort: 'medium' as const,
        category: 'marketing' as const,
        actionPlan: [
          'Lancez une campagne de réengagement',
          'Proposez des offres spéciales',
          'Créez du contenu engageant sur les réseaux sociaux',
          'Organisez un événement ou une promotion',
          'Contactez vos anciens clients'
        ],
        estimatedROI: '15-30%',
        timeframe: '2-3 semaines'
      });
    }

    return recommendations;
  }

  /**
   * Calcule l'activité récente pour détecter les tendances
   */
  private static calculateRecentActivity(data: any) {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

    const recentActivity = [
      ...data.posts.filter((p: any) => new Date(p.created_at) > thirtyDaysAgo),
      ...data.ratings.filter((r: any) => new Date(r.created_at) > thirtyDaysAgo)
    ].length;

    const previousActivity = [
      ...data.posts.filter((p: any) => {
        const date = new Date(p.created_at);
        return date > sixtyDaysAgo && date <= thirtyDaysAgo;
      }),
      ...data.ratings.filter((r: any) => {
        const date = new Date(r.created_at);
        return date > sixtyDaysAgo && date <= thirtyDaysAgo;
      })
    ].length;

    return {
      recentActivity,
      previousActivity,
      isDecreasing: recentActivity < previousActivity * 0.7 // 30% de baisse
    };
  }

  /**
   * Génère des propositions de nouveaux produits basées sur les données
   */
  static async generateProductSuggestions(businessId: string) {
    try {
      console.log('💡 Génération de suggestions de produits pour:', businessId);

      const businessData = await RealBusinessDataService.getCompleteBusinessData(businessId);

      // Si pas assez de données, utiliser les suggestions de test
      if (businessData.metadata.totalDataPoints < 10) {
        console.log('📊 Utilisation des suggestions de produits de test');
        const { MockBusinessDataService } = await import('./mockBusinessDataService');
        const mockSuggestions = MockBusinessDataService.generateMockProductSuggestions();

        return {
          suggestions: mockSuggestions,
          analysis: {
            categoryAnalysis: [],
            demandAnalysis: { totalDemands: 0, demandTexts: [], commonThemes: [] }
          },
          lastAnalysis: new Date().toISOString(),
          usingMockData: true
        };
      }

      // Analyser les catégories populaires dans les posts
      const categoryAnalysis = this.analyzeCategoriesFromPosts(businessData.posts);

      // Analyser les demandes non satisfaites dans les commentaires
      const demandAnalysis = this.analyzeUnmetDemands(businessData);

      // Générer des suggestions basées sur l'analyse
      const suggestions = this.generateProductSuggestionsFromAnalysis(categoryAnalysis, demandAnalysis);

      return {
        suggestions: suggestions || [],
        analysis: {
          categoryAnalysis: categoryAnalysis || [],
          demandAnalysis: demandAnalysis || { totalDemands: 0, demandTexts: [], commonThemes: [] }
        },
        lastAnalysis: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Erreur dans generateProductSuggestions:', error);
      return {
        suggestions: [],
        analysis: null,
        lastAnalysis: new Date().toISOString(),
        error: 'Erreur lors de l\'analyse'
      };
    }
  }

  /**
   * Analyse les catégories populaires dans les posts
   */
  private static analyzeCategoriesFromPosts(posts: any[]) {
    const categoryCount: { [key: string]: number } = {};
    const categoryRatings: { [key: string]: number[] } = {};

    posts.forEach(post => {
      if (post.category) {
        categoryCount[post.category] = (categoryCount[post.category] || 0) + 1;
        if (post.rating) {
          if (!categoryRatings[post.category]) {
            categoryRatings[post.category] = [];
          }
          categoryRatings[post.category].push(post.rating);
        }
      }
    });

    return Object.entries(categoryCount).map(([category, count]) => ({
      category,
      mentions: count,
      averageRating: categoryRatings[category]
        ? categoryRatings[category].reduce((a, b) => a + b, 0) / categoryRatings[category].length
        : 0,
      popularity: count / posts.length
    })).sort((a, b) => b.mentions - a.mentions);
  }

  /**
   * Analyse les demandes non satisfaites
   */
  private static analyzeUnmetDemands(data: any) {
    // Analyser les commentaires pour identifier les demandes
    const demands: string[] = [];

    // Mots-clés indiquant une demande
    const demandKeywords = [
      'j\'aimerais', 'je voudrais', 'il faudrait', 'manque', 'absent',
      'proposez-vous', 'avez-vous', 'serait bien', 'devriez', 'pourriez'
    ];

    [...data.posts, ...data.products.flatMap((p: any) => p.marketcomments || [])].forEach((item: any) => {
      const text = (item.description || item.comment || '').toLowerCase();
      demandKeywords.forEach(keyword => {
        if (text.includes(keyword)) {
          demands.push(text);
        }
      });
    });

    return {
      totalDemands: demands.length,
      demandTexts: demands.slice(0, 10), // Garder les 10 premiers
      commonThemes: this.extractCommonThemes(demands)
    };
  }

  /**
   * Extrait les thèmes communs des demandes
   */
  private static extractCommonThemes(demands: string[]) {
    // Simplification : rechercher des mots-clés de produits/services
    const themes: { [key: string]: number } = {};
    const productKeywords = [
      'livraison', 'service', 'qualité', 'prix', 'variété', 'choix',
      'rapidité', 'disponibilité', 'stock', 'promotion', 'réduction'
    ];

    demands.forEach(demand => {
      productKeywords.forEach(keyword => {
        if (demand.includes(keyword)) {
          themes[keyword] = (themes[keyword] || 0) + 1;
        }
      });
    });

    return Object.entries(themes)
      .map(([theme, count]) => ({ theme, mentions: count }))
      .sort((a, b) => b.mentions - a.mentions)
      .slice(0, 5);
  }

  /**
   * Génère des suggestions de produits basées sur l'analyse
   */
  private static generateProductSuggestionsFromAnalysis(categoryAnalysis: any, demandAnalysis: any) {
    const suggestions: any[] = [];

    // Suggestions basées sur les catégories populaires
    categoryAnalysis.slice(0, 3).forEach((cat: any, index: number) => {
      if (cat.averageRating >= 4) {
        suggestions.push({
          id: `category-${index}`,
          title: `Nouveau Produit ${cat.category}`,
          description: `Développez votre gamme ${cat.category} qui a une excellente réputation (${cat.averageRating.toFixed(1)}/5).`,
          category: cat.category,
          confidence: Math.min(95, 60 + cat.mentions * 5),
          marketDemand: cat.popularity > 0.3 ? 'high' : cat.popularity > 0.15 ? 'medium' : 'low',
          estimatedROI: cat.popularity > 0.3 ? '30-50%' : '20-35%',
          developmentTime: '4-8 semaines',
          investmentRequired: 'Moyen',
          riskLevel: 'Faible',
          targetAudience: 'Clients existants satisfaits',
          keyFeatures: [
            `Qualité supérieure dans la catégorie ${cat.category}`,
            'Prix compétitif',
            'Service client excellent'
          ]
        });
      }
    });

    // Suggestions basées sur les demandes non satisfaites
    demandAnalysis.commonThemes.slice(0, 2).forEach((theme: any, index: number) => {
      suggestions.push({
        id: `demand-${index}`,
        title: `Service ${theme.theme.charAt(0).toUpperCase() + theme.theme.slice(1)}`,
        description: `Répondez à la demande client pour améliorer ${theme.theme} (${theme.mentions} mentions).`,
        category: 'Service',
        confidence: Math.min(90, 50 + theme.mentions * 10),
        marketDemand: theme.mentions > 3 ? 'high' : 'medium',
        estimatedROI: '25-40%',
        developmentTime: '2-4 semaines',
        investmentRequired: 'Faible à Moyen',
        riskLevel: 'Faible',
        targetAudience: 'Clients ayant exprimé cette demande',
        keyFeatures: [
          `Amélioration directe de ${theme.theme}`,
          'Réponse aux attentes clients',
          'Avantage concurrentiel'
        ]
      });
    });

    return suggestions;
  }
}
