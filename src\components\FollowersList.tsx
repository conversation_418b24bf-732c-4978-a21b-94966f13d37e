import React, { useState, useEffect } from 'react';
import { useFollow } from '../context/FollowContext'; 

interface FollowersListProps {
  userId: string;
}

interface Follower {
  id: string;
  username: string;
  profile_picture?: string;
  role?: string;
  businessName?: string;
}

const FollowersList: React.FC<FollowersListProps> = ({ userId }) => { 
  const { getFollowersDetails } = useFollow(); 
  const [followers, setFollowers] = useState<Follower[]>([]); 
  const [loading, setLoading] = useState(true); 

  useEffect(() => { 
    const loadFollowers = async () => { 
      setLoading(true); 
      const data = await getFollowersDetails(userId); 
      setFollowers(data); 
      setLoading(false); 
    }; 

    loadFollowers(); 
  }, [userId, getFollowersDetails]); 

  if (loading) return <p>Chargement des abonnés...</p>; 

  return ( 
    <div className="followers-list"> 
      <h3>Abonnés ({followers.length})</h3> 
      {followers.length > 0 ? ( 
        followers.map(follower => (
          <div key={follower.id} className="follower-item"> 
            <img 
              src={follower.profile_picture || '/default-avatar.png'} 
              alt={follower.role === 'business' && follower.businessName ? follower.businessName : follower.username} 
              className="follower-avatar" 
            /> 
            <span className="follower-username">{follower.role === 'business' && follower.businessName ? follower.businessName : follower.username}</span> 
          </div> 
        )) 
      ) : ( 
        <p>Aucun abonné pour le moment</p> 
      )} 
    </div> 
  ); 
};

export default FollowersList;