-- Script pour réparer uniquement la table subscription_plans
-- Exécuter ce script dans l'éditeur SQL de Supabase

-- 1. Ajouter les colonnes manquantes à subscription_plans
DO $$ 
BEGIN
  -- Ajouter la colonne duration si elle n'existe pas
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'subscription_plans' 
                 AND column_name = 'duration') THEN
    ALTER TABLE public.subscription_plans ADD COLUMN duration integer NOT NULL DEFAULT 30;
    RAISE NOTICE 'Colonne duration ajoutée à subscription_plans';
  ELSE
    RAISE NOTICE 'Colonne duration existe déjà dans subscription_plans';
  END IF;
  
  -- Ajouter la colonne features si elle n'existe pas
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                 WHERE table_name = 'subscription_plans'
                 AND column_name = 'features') THEN
    ALTER TABLE public.subscription_plans ADD COLUMN features jsonb DEFAULT '[]'::jsonb;
    RAISE NOTICE 'Colonne features ajoutée à subscription_plans';
  ELSE
    RAISE NOTICE 'Colonne features existe déjà dans subscription_plans';
  END IF;
END $$;

-- 2. <PERSON><PERSON>er la table subscription_codes si elle n'existe pas
CREATE TABLE IF NOT EXISTS public.subscription_codes (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  code text NOT NULL UNIQUE,
  business_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  business_name text NOT NULL,
  plan_type text NOT NULL CHECK (plan_type IN ('trial', 'monthly', 'yearly')),
  amount integer NOT NULL,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'validated', 'rejected', 'expired')),
  generated_at timestamptz DEFAULT now(),
  expires_at timestamptz NOT NULL,
  validated_at timestamptz,
  validated_by uuid REFERENCES auth.users(id),
  rejection_reason text,
  admin_notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 3. Créer les index
CREATE INDEX IF NOT EXISTS idx_subscription_plans_is_active ON public.subscription_plans(is_active);
CREATE INDEX IF NOT EXISTS idx_subscription_codes_business_id ON public.subscription_codes(business_id);
CREATE INDEX IF NOT EXISTS idx_subscription_codes_code ON public.subscription_codes(code);
CREATE INDEX IF NOT EXISTS idx_subscription_codes_status ON public.subscription_codes(status);

-- 4. Activer RLS
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_codes ENABLE ROW LEVEL SECURITY;

-- 5. Supprimer les anciennes politiques si elles existent
DROP POLICY IF EXISTS "Authenticated users can view active plans" ON public.subscription_plans;
DROP POLICY IF EXISTS "Users can view their own subscription codes" ON public.subscription_codes;
DROP POLICY IF EXISTS "System can manage subscription codes" ON public.subscription_codes;

-- 6. Créer les politiques RLS pour subscription_plans
CREATE POLICY "Authenticated users can view active plans" ON public.subscription_plans
  FOR SELECT USING (is_active = true);

-- 7. Créer les politiques RLS pour subscription_codes
CREATE POLICY "Users can view their own subscription codes" ON public.subscription_codes
  FOR SELECT USING (auth.uid() = business_id);

CREATE POLICY "System can manage subscription codes" ON public.subscription_codes
  FOR ALL WITH CHECK (true);

-- 8. Insérer les plans par défaut (en utilisant jsonb pour features)
INSERT INTO public.subscription_plans (id, name, description, price, duration, features, is_active) VALUES
  ('trial', 'Essai Gratuit', 'Essai gratuit de 7 jours', 0, 7, '["basic-ai", "basic-dashboard", "basic-reviews"]'::jsonb, true),
  ('monthly', 'Plan Mensuel', 'Abonnement mensuel', 2500, 30, '["basic-ai", "basic-dashboard", "basic-reviews", "pdf-export"]'::jsonb, true),
  ('yearly', 'Plan Annuel', 'Abonnement annuel avec réduction', 25000, 365, '["advanced-ai", "custom-dashboard", "unlimited-reviews", "api-access"]'::jsonb, true)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  price = EXCLUDED.price,
  duration = EXCLUDED.duration,
  features = EXCLUDED.features,
  is_active = EXCLUDED.is_active,
  updated_at = now();

-- 9. Donner les permissions
GRANT SELECT ON public.subscription_plans TO authenticated;
GRANT SELECT, INSERT ON public.subscription_codes TO authenticated;

-- Permissions pour service_role
GRANT ALL ON public.subscription_plans TO service_role;
GRANT ALL ON public.subscription_codes TO service_role;

-- 10. Vérifier que tout fonctionne
SELECT 'Tables réparées avec succès!' as message;
SELECT 'Plans disponibles:' as info;
SELECT id, name, price, duration FROM public.subscription_plans ORDER BY price;
