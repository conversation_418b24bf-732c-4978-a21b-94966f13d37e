import { IPost, IComment, IUser, IProduct } from '../types';
import { supabase } from '../lib/supabase';

// Interface pour l'analyse comportementale
export interface IUserBehaviorProfile {
  userId: string;
  preferences: {
    categories: { [category: string]: number }; // Score de préférence par catégorie
    priceRanges: { [range: string]: number }; // Préférence de gamme de prix
    brands: { [brand: string]: number }; // Préférence de marques
    qualities: { [quality: string]: number }; // Préférence de qualité
  };
  sentimentAnalysis: {
    positiveKeywords: string[]; // Mots-clés positifs utilisés
    negativeKeywords: string[]; // Mots-clés négatifs utilisés
    overallSentiment: 'positive' | 'neutral' | 'negative'; // Sentiment général
  };
  interactionPatterns: {
    mostActiveTimeOfDay: string; // Heure d'activité principale
    averageSessionDuration: number; // Durée moyenne des sessions
    preferredContentTypes: string[]; // Types de contenu préférés
    engagementLevel: 'low' | 'medium' | 'high'; // Niveau d'engagement
  };
  socialBehavior: {
    followsBusinesses: boolean; // Suit des entreprises
    sharesContent: boolean; // Partage du contenu
    commentsFrequently: boolean; // Commente souvent
    reactsToContent: boolean; // Réagit au contenu
  };
  purchaseIntent: {
    score: number; // Score d'intention d'achat (0-100)
    indicators: string[]; // Indicateurs d'intention d'achat
    lastPurchaseSignal: Date | null; // Dernier signal d'achat
  };
}

// Interface pour l'analyse de sentiment
export interface ISentimentAnalysis {
  score: number; // -1 (très négatif) à 1 (très positif)
  magnitude: number; // Intensité du sentiment
  keywords: string[]; // Mots-clés détectés
  category: 'positive' | 'neutral' | 'negative';
}

// Interface pour les signaux d'achat
export interface IPurchaseSignal {
  type: 'comment' | 'reaction' | 'share' | 'view' | 'search';
  productId?: string;
  category?: string;
  sentiment: ISentimentAnalysis;
  timestamp: Date;
  weight: number; // Poids du signal (0-1)
}

export class UserBehaviorAnalysisService {
  
  // Mots-clés pour l'analyse de sentiment
  private static readonly POSITIVE_KEYWORDS = [
    'excellent', 'parfait', 'génial', 'super', 'fantastique', 'recommande', 
    'qualité', 'satisfait', 'content', 'heureux', 'bon', 'bien', 'top',
    'magnifique', 'beau', 'efficace', 'rapide', 'pratique', 'utile',
    'adoré', 'aimé', 'impressionnant', 'incroyable', 'merveilleux'
  ];

  private static readonly NEGATIVE_KEYWORDS = [
    'mauvais', 'nul', 'décevant', 'problème', 'défaut', 'cassé', 'cher',
    'lent', 'compliqué', 'difficile', 'frustrant', 'énervant', 'horrible',
    'terrible', 'catastrophe', 'arnaque', 'déçu', 'regret', 'erreur',
    'perte', 'inutile', 'inefficace', 'fragile', 'laid'
  ];

  private static readonly PURCHASE_INTENT_KEYWORDS = [
    'acheter', 'commander', 'prendre', 'veux', 'besoin', 'intéressé',
    'prix', 'coût', 'budget', 'disponible', 'stock', 'livraison',
    'garantie', 'retour', 'échange', 'paiement', 'promotion', 'offre'
  ];

  // Analyser le sentiment d'un texte
  static analyzeSentiment(text: string): ISentimentAnalysis {
    const words = text.toLowerCase().split(/\s+/);
    let positiveScore = 0;
    let negativeScore = 0;
    const detectedKeywords: string[] = [];

    // Compter les mots positifs et négatifs
    words.forEach(word => {
      if (this.POSITIVE_KEYWORDS.some(keyword => word.includes(keyword))) {
        positiveScore++;
        detectedKeywords.push(word);
      }
      if (this.NEGATIVE_KEYWORDS.some(keyword => word.includes(keyword))) {
        negativeScore++;
        detectedKeywords.push(word);
      }
    });

    // Calculer le score final
    const totalWords = words.length;
    const score = totalWords > 0 ? (positiveScore - negativeScore) / totalWords : 0;
    const magnitude = (positiveScore + negativeScore) / totalWords;

    let category: 'positive' | 'neutral' | 'negative' = 'neutral';
    if (score > 0.1) category = 'positive';
    else if (score < -0.1) category = 'negative';

    return {
      score: Math.max(-1, Math.min(1, score * 5)), // Normaliser entre -1 et 1
      magnitude: Math.min(1, magnitude * 3), // Normaliser magnitude
      keywords: detectedKeywords,
      category
    };
  }

  // Analyser les commentaires d'un utilisateur
  static async analyzeUserComments(userId: string): Promise<ISentimentAnalysis[]> {
    try {
      // Récupérer les commentaires de l'utilisateur depuis Supabase
      const { data: comments, error } = await supabase
        .from('comments')
        .select('content, created_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(50); // Analyser les 50 derniers commentaires

      if (error || !comments) {
        console.error('Erreur lors de la récupération des commentaires:', error);
        return [];
      }

      return comments.map(comment => this.analyzeSentiment(comment.content));
    } catch (error) {
      console.error('Erreur lors de l\'analyse des commentaires:', error);
      return [];
    }
  }

  // Analyser les réactions d'un utilisateur
  static async analyzeUserReactions(userId: string): Promise<{ [reactionType: string]: number }> {
    try {
      const { data: reactions, error } = await supabase
        .from('post_reactions')
        .select('reaction_type, created_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(100);

      if (error || !reactions) {
        console.error('Erreur lors de la récupération des réactions:', error);
        return {};
      }

      // Compter les types de réactions
      const reactionCounts: { [key: string]: number } = {};
      reactions.forEach(reaction => {
        reactionCounts[reaction.reaction_type] = (reactionCounts[reaction.reaction_type] || 0) + 1;
      });

      return reactionCounts;
    } catch (error) {
      console.error('Erreur lors de l\'analyse des réactions:', error);
      return {};
    }
  }

  // Détecter les signaux d'intention d'achat
  static detectPurchaseSignals(text: string, type: IPurchaseSignal['type']): IPurchaseSignal | null {
    const sentiment = this.analyzeSentiment(text);
    const words = text.toLowerCase().split(/\s+/);
    
    // Vérifier la présence de mots-clés d'intention d'achat
    const hasPurchaseIntent = words.some(word => 
      this.PURCHASE_INTENT_KEYWORDS.some(keyword => word.includes(keyword))
    );

    if (!hasPurchaseIntent) return null;

    // Calculer le poids du signal
    let weight = 0.3; // Poids de base
    if (sentiment.category === 'positive') weight += 0.4;
    if (sentiment.magnitude > 0.5) weight += 0.2;
    if (type === 'comment') weight += 0.1; // Les commentaires ont plus de poids

    return {
      type,
      sentiment,
      timestamp: new Date(),
      weight: Math.min(1, weight)
    };
  }

  // Analyser les préférences de catégories
  static async analyzeCategoryPreferences(userId: string): Promise<{ [category: string]: number }> {
    try {
      // Analyser les interactions avec les posts par catégorie
      const { data: interactions, error } = await supabase
        .from('post_reactions')
        .select(`
          reaction_type,
          posts!inner(category, created_at)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(200);

      if (error || !interactions) {
        console.error('Erreur lors de l\'analyse des catégories:', error);
        return {};
      }

      const categoryScores: { [category: string]: number } = {};
      
      interactions.forEach((interaction: any) => {
        const category = interaction.posts?.category;
        if (!category) return;

        // Pondérer selon le type de réaction
        let score = 1;
        switch (interaction.reaction_type) {
          case 'love': score = 3; break;
          case 'like': score = 2; break;
          case 'wow': score = 2; break;
          case 'sad': score = -1; break;
          case 'angry': score = -2; break;
          default: score = 1;
        }

        categoryScores[category] = (categoryScores[category] || 0) + score;
      });

      // Normaliser les scores
      const maxScore = Math.max(...Object.values(categoryScores));
      if (maxScore > 0) {
        Object.keys(categoryScores).forEach(category => {
          categoryScores[category] = categoryScores[category] / maxScore;
        });
      }

      return categoryScores;
    } catch (error) {
      console.error('Erreur lors de l\'analyse des préférences:', error);
      return {};
    }
  }

  // Générer le profil comportemental complet
  static async generateUserBehaviorProfile(userId: string): Promise<IUserBehaviorProfile> {
    try {
      // Analyser tous les aspects du comportement
      const [
        commentSentiments,
        reactions,
        categoryPreferences
      ] = await Promise.all([
        this.analyzeUserComments(userId),
        this.analyzeUserReactions(userId),
        this.analyzeCategoryPreferences(userId)
      ]);

      // Analyser le sentiment général
      const overallSentiment = this.calculateOverallSentiment(commentSentiments);
      
      // Extraire les mots-clés positifs et négatifs
      const positiveKeywords = commentSentiments
        .filter(s => s.category === 'positive')
        .flatMap(s => s.keywords)
        .slice(0, 10);
      
      const negativeKeywords = commentSentiments
        .filter(s => s.category === 'negative')
        .flatMap(s => s.keywords)
        .slice(0, 10);

      // Calculer le score d'intention d'achat
      const purchaseIntentScore = this.calculatePurchaseIntentScore(commentSentiments, reactions);

      // Analyser les patterns d'interaction
      const interactionPatterns = await this.analyzeInteractionPatterns(userId);

      return {
        userId,
        preferences: {
          categories: categoryPreferences,
          priceRanges: {}, // À implémenter selon les données disponibles
          brands: {}, // À implémenter selon les données disponibles
          qualities: {} // À implémenter selon les données disponibles
        },
        sentimentAnalysis: {
          positiveKeywords,
          negativeKeywords,
          overallSentiment
        },
        interactionPatterns,
        socialBehavior: {
          followsBusinesses: Object.keys(reactions).length > 0,
          sharesContent: false, // À implémenter
          commentsFrequently: commentSentiments.length > 10,
          reactsToContent: Object.keys(reactions).length > 5
        },
        purchaseIntent: {
          score: purchaseIntentScore,
          indicators: this.generatePurchaseIndicators(commentSentiments, reactions),
          lastPurchaseSignal: new Date() // À implémenter
        }
      };
    } catch (error) {
      console.error('Erreur lors de la génération du profil:', error);
      throw error;
    }
  }

  // Calculer le sentiment général
  private static calculateOverallSentiment(sentiments: ISentimentAnalysis[]): 'positive' | 'neutral' | 'negative' {
    if (sentiments.length === 0) return 'neutral';
    
    const averageScore = sentiments.reduce((sum, s) => sum + s.score, 0) / sentiments.length;
    
    if (averageScore > 0.2) return 'positive';
    if (averageScore < -0.2) return 'negative';
    return 'neutral';
  }

  // Calculer le score d'intention d'achat
  private static calculatePurchaseIntentScore(
    sentiments: ISentimentAnalysis[], 
    reactions: { [key: string]: number }
  ): number {
    let score = 0;
    
    // Score basé sur le sentiment positif
    const positiveCount = sentiments.filter(s => s.category === 'positive').length;
    score += (positiveCount / Math.max(1, sentiments.length)) * 40;
    
    // Score basé sur les réactions positives
    const positiveReactions = (reactions['love'] || 0) + (reactions['like'] || 0);
    const totalReactions = Object.values(reactions).reduce((sum, count) => sum + count, 0);
    if (totalReactions > 0) {
      score += (positiveReactions / totalReactions) * 30;
    }
    
    // Score basé sur l'activité
    const activityScore = Math.min(30, sentiments.length * 2 + totalReactions);
    score += activityScore;
    
    return Math.min(100, Math.max(0, score));
  }

  // Analyser les patterns d'interaction
  private static async analyzeInteractionPatterns(userId: string): Promise<IUserBehaviorProfile['interactionPatterns']> {
    // Implémentation simplifiée - à enrichir avec de vraies données
    return {
      mostActiveTimeOfDay: '18:00-22:00',
      averageSessionDuration: 25, // minutes
      preferredContentTypes: ['produits', 'avis', 'recommandations'],
      engagementLevel: 'medium'
    };
  }

  // Générer les indicateurs d'intention d'achat
  private static generatePurchaseIndicators(
    sentiments: ISentimentAnalysis[], 
    reactions: { [key: string]: number }
  ): string[] {
    const indicators: string[] = [];
    
    if (sentiments.filter(s => s.category === 'positive').length > 5) {
      indicators.push('Commentaires positifs fréquents');
    }
    
    if (reactions['love'] && reactions['love'] > 3) {
      indicators.push('Réactions "love" sur produits');
    }
    
    if (sentiments.some(s => s.keywords.some(k => this.PURCHASE_INTENT_KEYWORDS.includes(k)))) {
      indicators.push('Mots-clés d\'intention d\'achat détectés');
    }
    
    return indicators;
  }
}
