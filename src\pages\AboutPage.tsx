import React from 'react';
import Card, { CardBody, CardHeader } from '../components/ui/Card';
import { Heart, Users, Target, Award, Globe, Zap } from 'lucide-react';

const AboutPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* En-tête */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            À propos de Customeroom
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            La plateforme qui révolutionne la relation entre entreprises et clients en Afrique de l'Ouest
          </p>
        </div>

        {/* Notre Mission */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900 flex items-center">
              <Target className="mr-3 text-blue-600" size={28} />
              Notre Mission
            </h2>
          </CardHeader>
          <CardBody>
            <p className="text-gray-700 text-lg leading-relaxed">
              Customeroom a pour mission de créer un écosystème numérique où les entreprises 
              africaines peuvent prospérer grâce aux retours authentiques de leurs clients. 
              Nous croyons que chaque avis compte et que la transparence est la clé du succès commercial.
            </p>
          </CardBody>
        </Card>

        {/* Notre Vision */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900 flex items-center">
              <Globe className="mr-3 text-green-600" size={28} />
              Notre Vision
            </h2>
          </CardHeader>
          <CardBody>
            <p className="text-gray-700 text-lg leading-relaxed">
              Devenir la référence en matière d'avis clients et de recommandations en Afrique de l'Ouest, 
              en aidant les entreprises locales à améliorer leurs services et en permettant aux consommateurs 
              de faire des choix éclairés.
            </p>
          </CardBody>
        </Card>

        {/* Nos Valeurs */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900 flex items-center">
              <Heart className="mr-3 text-red-600" size={28} />
              Nos Valeurs
            </h2>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Users className="text-blue-600" size={16} />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Authenticité</h3>
                  <p className="text-gray-600">
                    Nous privilégions les avis authentiques et vérifiés pour garantir la confiance.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Zap className="text-green-600" size={16} />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Innovation</h3>
                  <p className="text-gray-600">
                    Nous développons des solutions technologiques adaptées au marché africain.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Award className="text-purple-600" size={16} />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Excellence</h3>
                  <p className="text-gray-600">
                    Nous visons l'excellence dans chaque interaction et fonctionnalité.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Globe className="text-orange-600" size={16} />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Inclusion</h3>
                  <p className="text-gray-600">
                    Nous rendons notre plateforme accessible à toutes les entreprises.
                  </p>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Notre Histoire */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900">Notre Histoire</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              <div className="border-l-4 border-blue-500 pl-6">
                <h3 className="font-semibold text-gray-900 mb-2">2024 - Lancement</h3>
                <p className="text-gray-700">
                  Création de Customeroom avec pour objectif de digitaliser les avis clients 
                  en Afrique de l'Ouest et d'aider les entreprises locales à prospérer.
                </p>
              </div>
              
              <div className="border-l-4 border-green-500 pl-6">
                <h3 className="font-semibold text-gray-900 mb-2">Vision Future</h3>
                <p className="text-gray-700">
                  Expansion dans toute l'Afrique de l'Ouest avec des fonctionnalités avancées 
                  d'intelligence artificielle pour l'analyse des sentiments et des recommandations personnalisées.
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Statistiques */}
        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900">Customeroom en Chiffres</h2>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-3xl font-bold text-blue-600 mb-2">1000+</div>
                <div className="text-gray-600">Entreprises</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-green-600 mb-2">5000+</div>
                <div className="text-gray-600">Avis Clients</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-600 mb-2">15000+</div>
                <div className="text-gray-600">Utilisateurs</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-orange-600 mb-2">8</div>
                <div className="text-gray-600">Pays Couverts</div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Contact */}
        <Card>
          <CardHeader>
            <h2 className="text-2xl font-semibold text-gray-900">Nous Contacter</h2>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Siège Social</h3>
                <p className="text-gray-700">
                  Abidjan, Côte d'Ivoire<br />
                  Plateau, Rue des Jardins<br />
                  01 BP 1234 Abidjan 01
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Contact</h3>
                <p className="text-gray-700">
                  Email: <EMAIL><br />
                  Téléphone: +225 01 23 45 67<br />
                  Support: <EMAIL>
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default AboutPage;
