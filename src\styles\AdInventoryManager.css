/* Gestionnaire d'inventaire publicitaire */
.ad-inventory-manager {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.inventory-header {
  text-align: center;
  margin-bottom: 1rem;
}

.inventory-header h3 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.inventory-header p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

/* Résumé du budget */
.budget-summary {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 1px solid #d1d5db;
}

.budget-info {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 1rem;
}

.budget-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #374151;
}

/* Grille des emplacements */
.placements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1rem;
}

.placement-card {
  border: 2px solid #e5e7eb;
  transition: all 0.2s;
  cursor: pointer;
}

.placement-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.placement-card.selected {
  border-color: #3b82f6;
  background: #f8fafc;
}

.placement-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.placement-info h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
  font-weight: 600;
}

.placement-info p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.availability-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Statistiques de l'emplacement */
.placement-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 8px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
}

/* Contrôles de l'emplacement */
.placement-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.placement-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
}

.placement-toggle input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  accent-color: #3b82f6;
}

/* Contrôles d'enchère */
.bid-controls {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.bid-controls label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.bid-controls input {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.bid-controls input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.bid-recommendation {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #059669;
  font-weight: 500;
}

/* Recommandations */
.recommendations {
  background: #fef3c7;
  border: 1px solid #f59e0b;
}

.recommendations h4 {
  margin: 0 0 1rem 0;
  color: #92400e;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.recommendations h4::before {
  content: "💡";
  font-size: 1.25rem;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.recommendation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #ffffff;
  border: 1px solid #fed7aa;
  border-radius: 8px;
}

.rec-placement {
  font-weight: 600;
  color: #92400e;
}

.rec-details {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #78350f;
}

/* Responsive */
@media (max-width: 768px) {
  .placements-grid {
    grid-template-columns: 1fr;
  }

  .budget-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .recommendation-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .rec-details {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bid-controls {
  animation: slideIn 0.3s ease-out;
}

.recommendations {
  animation: slideIn 0.3s ease-out;
}

/* États des emplacements */
.placement-card.high-competition {
  border-left: 4px solid #ef4444;
}

.placement-card.medium-competition {
  border-left: 4px solid #f59e0b;
}

.placement-card.low-competition {
  border-left: 4px solid #10b981;
}
