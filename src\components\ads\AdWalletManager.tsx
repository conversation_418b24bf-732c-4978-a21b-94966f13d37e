import React, { useState, useEffect } from 'react';
import { useAdBilling } from '../../context/AdBillingContext';
import { 
  Wallet, 
  CreditCard, 
  Plus, 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown,
  Eye,
  EyeOff,
  RefreshCw,
  Download,
  Bell,
  X
} from 'lucide-react';
import Button from '../ui/Button';
import '../../styles/AdWalletManager.css';

interface AdWalletManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

const AdWalletManager: React.FC<AdWalletManagerProps> = ({ isOpen, onClose }) => {
  const {
    wallet,
    transactions,
    paymentMethods,
    billingAlerts,
    loading,
    error,
    rechargeWallet,
    addPaymentMethod,
    markAlertAsRead,
    clearAllAlerts,
    canAffordCampaign
  } = useAdBilling();

  const [showBalance, setShowBalance] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'recharge' | 'transactions' | 'methods'>('overview');
  const [rechargeAmount, setRechargeAmount] = useState('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
  const [showAddPaymentMethod, setShowAddPaymentMethod] = useState(false);

  // Données pour le formulaire de nouvelle méthode de paiement
  const [newPaymentMethod, setNewPaymentMethod] = useState({
    type: 'mobile_money' as const,
    provider: '',
    accountNumber: '',
    accountName: '',
    isDefault: false
  });

  useEffect(() => {
    if (paymentMethods.length > 0 && !selectedPaymentMethod) {
      const defaultMethod = paymentMethods.find(m => m.isDefault);
      setSelectedPaymentMethod(defaultMethod?.id || paymentMethods[0].id);
    }
  }, [paymentMethods, selectedPaymentMethod]);

  if (!isOpen) return null;

  const handleRecharge = async () => {
    if (!rechargeAmount || !selectedPaymentMethod) return;

    try {
      await rechargeWallet(parseFloat(rechargeAmount), selectedPaymentMethod);
      setRechargeAmount('');
      alert('Recharge effectuée avec succès !');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Erreur lors de la recharge');
    }
  };

  const handleAddPaymentMethod = async () => {
    try {
      await addPaymentMethod({
        ...newPaymentMethod,
        isActive: true
      });
      setNewPaymentMethod({
        type: 'mobile_money',
        provider: '',
        accountNumber: '',
        accountName: '',
        isDefault: false
      });
      setShowAddPaymentMethod(false);
      alert('Méthode de paiement ajoutée avec succès !');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Erreur lors de l\'ajout');
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('fr-FR').format(amount);
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'recharge': return <TrendingUp className="text-green-500" size={16} />;
      case 'spend': return <TrendingDown className="text-red-500" size={16} />;
      case 'refund': return <TrendingUp className="text-blue-500" size={16} />;
      case 'bonus': return <TrendingUp className="text-purple-500" size={16} />;
      default: return <Wallet size={16} />;
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'recharge': return 'text-green-600';
      case 'spend': return 'text-red-600';
      case 'refund': return 'text-blue-600';
      case 'bonus': return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  const popularProviders = {
    mobile_money: ['Orange Money', 'MTN Money', 'Moov Money'],
    bank_card: ['Visa', 'Mastercard', 'American Express'],
    bank_transfer: ['Ecobank', 'SGBCI', 'BICICI', 'UBA'],
    crypto: ['Bitcoin', 'Ethereum', 'USDT']
  };

  return (
    <div className="ad-wallet-overlay">
      <div className="ad-wallet-modal">
        <div className="ad-wallet-header">
          <div className="header-left">
            <Wallet size={24} />
            <h2>Portefeuille Publicitaire</h2>
          </div>
          <button className="close-button" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        {/* Alertes de facturation */}
        {billingAlerts.length > 0 && (
          <div className="billing-alerts">
            <div className="alerts-header">
              <Bell size={16} />
              <span>Alertes ({billingAlerts.length})</span>
              <button onClick={clearAllAlerts} className="clear-all-btn">
                Tout effacer
              </button>
            </div>
            <div className="alerts-list">
              {billingAlerts.slice(0, 3).map(alert => (
                <div key={alert.id} className={`alert alert-${alert.type}`}>
                  <AlertTriangle size={16} />
                  <span>{alert.message}</span>
                  <button onClick={() => markAlertAsRead(alert.id)}>
                    <X size={14} />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="wallet-tabs">
          <button
            className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            <Wallet size={16} />
            Vue d'ensemble
          </button>
          <button
            className={`tab ${activeTab === 'recharge' ? 'active' : ''}`}
            onClick={() => setActiveTab('recharge')}
          >
            <Plus size={16} />
            Recharger
          </button>
          <button
            className={`tab ${activeTab === 'transactions' ? 'active' : ''}`}
            onClick={() => setActiveTab('transactions')}
          >
            <RefreshCw size={16} />
            Transactions
          </button>
          <button
            className={`tab ${activeTab === 'methods' ? 'active' : ''}`}
            onClick={() => setActiveTab('methods')}
          >
            <CreditCard size={16} />
            Méthodes
          </button>
        </div>

        <div className="wallet-content">
          {/* Vue d'ensemble */}
          {activeTab === 'overview' && (
            <div className="overview-tab">
              <div className="balance-card">
                <div className="balance-header">
                  <h3>Solde actuel</h3>
                  <button
                    className="toggle-balance"
                    onClick={() => setShowBalance(!showBalance)}
                  >
                    {showBalance ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
                <div className="balance-amount">
                  {showBalance ? (
                    <span className="amount">{formatAmount(wallet?.balance || 0)} F CFA</span>
                  ) : (
                    <span className="amount-hidden">••••••</span>
                  )}
                </div>
                <div className="balance-status">
                  {wallet && wallet.balance < 10000 ? (
                    <div className="status-warning">
                      <AlertTriangle size={16} />
                      <span>Solde faible - Rechargez votre compte</span>
                    </div>
                  ) : (
                    <div className="status-good">
                      <span>Solde suffisant pour vos campagnes</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="stats-grid">
                <div className="stat-card">
                  <div className="stat-icon">
                    <TrendingUp className="text-green-500" />
                  </div>
                  <div className="stat-content">
                    <h4>Total rechargé</h4>
                    <p>{formatAmount(wallet?.totalRecharged || 0)} F CFA</p>
                  </div>
                </div>
                <div className="stat-card">
                  <div className="stat-icon">
                    <TrendingDown className="text-red-500" />
                  </div>
                  <div className="stat-content">
                    <h4>Total dépensé</h4>
                    <p>{formatAmount(wallet?.totalSpent || 0)} F CFA</p>
                  </div>
                </div>
                <div className="stat-card">
                  <div className="stat-icon">
                    <RefreshCw className="text-blue-500" />
                  </div>
                  <div className="stat-content">
                    <h4>Transactions</h4>
                    <p>{transactions.length}</p>
                  </div>
                </div>
              </div>

              <div className="recent-transactions">
                <h4>Transactions récentes</h4>
                <div className="transactions-list">
                  {transactions.slice(0, 5).map(transaction => (
                    <div key={transaction.id} className="transaction-item">
                      <div className="transaction-icon">
                        {getTransactionIcon(transaction.type)}
                      </div>
                      <div className="transaction-details">
                        <p className="transaction-description">{transaction.description}</p>
                        <span className="transaction-date">
                          {transaction.createdAt.toLocaleDateString('fr-FR')}
                        </span>
                      </div>
                      <div className={`transaction-amount ${getTransactionColor(transaction.type)}`}>
                        {transaction.type === 'spend' ? '-' : '+'}
                        {formatAmount(transaction.amount)} F CFA
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Recharge */}
          {activeTab === 'recharge' && (
            <div className="recharge-tab">
              <div className="recharge-form">
                <h3>Recharger votre portefeuille</h3>
                
                <div className="quick-amounts">
                  <h4>Montants suggérés</h4>
                  <div className="amount-buttons">
                    {[10000, 25000, 50000, 100000, 250000].map(amount => (
                      <button
                        key={amount}
                        className={`amount-btn ${rechargeAmount === amount.toString() ? 'selected' : ''}`}
                        onClick={() => setRechargeAmount(amount.toString())}
                      >
                        {formatAmount(amount)} F CFA
                      </button>
                    ))}
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="recharge-amount">Montant personnalisé</label>
                  <input
                    type="number"
                    id="recharge-amount"
                    value={rechargeAmount}
                    onChange={(e) => setRechargeAmount(e.target.value)}
                    placeholder="Entrez le montant"
                    min="1000"
                    step="1000"
                  />
                  <span className="input-hint">Minimum : 1 000 F CFA</span>
                </div>

                <div className="form-group">
                  <label htmlFor="payment-method">Méthode de paiement</label>
                  <select
                    id="payment-method"
                    value={selectedPaymentMethod}
                    onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                  >
                    <option value="">Sélectionnez une méthode</option>
                    {paymentMethods.map(method => (
                      <option key={method.id} value={method.id}>
                        {method.provider} - {method.accountNumber}
                        {method.isDefault && ' (Par défaut)'}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="recharge-summary">
                  <div className="summary-row">
                    <span>Montant à recharger :</span>
                    <span>{formatAmount(parseFloat(rechargeAmount) || 0)} F CFA</span>
                  </div>
                  <div className="summary-row">
                    <span>Frais de transaction :</span>
                    <span>0 F CFA</span>
                  </div>
                  <div className="summary-row total">
                    <span>Total à payer :</span>
                    <span>{formatAmount(parseFloat(rechargeAmount) || 0)} F CFA</span>
                  </div>
                </div>

                <Button
                  onClick={handleRecharge}
                  disabled={!rechargeAmount || !selectedPaymentMethod || loading}
                  className="recharge-btn"
                >
                  {loading ? 'Traitement...' : 'Recharger maintenant'}
                </Button>
              </div>
            </div>
          )}

          {/* Transactions */}
          {activeTab === 'transactions' && (
            <div className="transactions-tab">
              <div className="transactions-header">
                <h3>Historique des transactions</h3>
                <Button variant="outline" size="sm">
                  <Download size={16} />
                  Exporter
                </Button>
              </div>

              <div className="transactions-filters">
                <select className="filter-select">
                  <option value="all">Tous les types</option>
                  <option value="recharge">Recharges</option>
                  <option value="spend">Dépenses</option>
                  <option value="refund">Remboursements</option>
                </select>
                <select className="filter-select">
                  <option value="all">Toutes les périodes</option>
                  <option value="today">Aujourd'hui</option>
                  <option value="week">Cette semaine</option>
                  <option value="month">Ce mois</option>
                </select>
              </div>

              <div className="transactions-table">
                <div className="table-header">
                  <div>Date</div>
                  <div>Type</div>
                  <div>Description</div>
                  <div>Montant</div>
                  <div>Statut</div>
                </div>
                <div className="table-body">
                  {transactions.map(transaction => (
                    <div key={transaction.id} className="table-row">
                      <div>{transaction.createdAt.toLocaleDateString('fr-FR')}</div>
                      <div className="transaction-type">
                        {getTransactionIcon(transaction.type)}
                        <span>{transaction.type}</span>
                      </div>
                      <div>{transaction.description}</div>
                      <div className={getTransactionColor(transaction.type)}>
                        {transaction.type === 'spend' ? '-' : '+'}
                        {formatAmount(transaction.amount)} F CFA
                      </div>
                      <div>
                        <span className={`status status-${transaction.status}`}>
                          {transaction.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Méthodes de paiement */}
          {activeTab === 'methods' && (
            <div className="methods-tab">
              <div className="methods-header">
                <h3>Méthodes de paiement</h3>
                <Button
                  onClick={() => setShowAddPaymentMethod(true)}
                  size="sm"
                >
                  <Plus size={16} />
                  Ajouter
                </Button>
              </div>

              <div className="payment-methods-list">
                {paymentMethods.map(method => (
                  <div key={method.id} className="payment-method-card">
                    <div className="method-icon">
                      <CreditCard size={20} />
                    </div>
                    <div className="method-details">
                      <h4>{method.provider}</h4>
                      <p>{method.accountNumber}</p>
                      <span className="method-type">{method.type}</span>
                    </div>
                    <div className="method-actions">
                      {method.isDefault && (
                        <span className="default-badge">Par défaut</span>
                      )}
                      <button className="action-btn">Modifier</button>
                      <button className="action-btn danger">Supprimer</button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Formulaire d'ajout de méthode de paiement */}
              {showAddPaymentMethod && (
                <div className="add-method-form">
                  <h4>Ajouter une méthode de paiement</h4>
                  
                  <div className="form-group">
                    <label>Type de paiement</label>
                    <select
                      value={newPaymentMethod.type}
                      onChange={(e) => setNewPaymentMethod({
                        ...newPaymentMethod,
                        type: e.target.value as any,
                        provider: ''
                      })}
                    >
                      <option value="mobile_money">Mobile Money</option>
                      <option value="bank_card">Carte bancaire</option>
                      <option value="bank_transfer">Virement bancaire</option>
                      <option value="crypto">Cryptomonnaie</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Fournisseur</label>
                    <select
                      value={newPaymentMethod.provider}
                      onChange={(e) => setNewPaymentMethod({
                        ...newPaymentMethod,
                        provider: e.target.value
                      })}
                    >
                      <option value="">Sélectionnez un fournisseur</option>
                      {popularProviders[newPaymentMethod.type].map(provider => (
                        <option key={provider} value={provider}>{provider}</option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Numéro de compte</label>
                    <input
                      type="text"
                      value={newPaymentMethod.accountNumber}
                      onChange={(e) => setNewPaymentMethod({
                        ...newPaymentMethod,
                        accountNumber: e.target.value
                      })}
                      placeholder="Ex: +225 07 XX XX XX XX"
                    />
                  </div>

                  <div className="form-group">
                    <label>Nom du titulaire</label>
                    <input
                      type="text"
                      value={newPaymentMethod.accountName}
                      onChange={(e) => setNewPaymentMethod({
                        ...newPaymentMethod,
                        accountName: e.target.value
                      })}
                      placeholder="Nom complet"
                    />
                  </div>

                  <div className="form-group">
                    <label className="checkbox-label">
                      <input
                        type="checkbox"
                        checked={newPaymentMethod.isDefault}
                        onChange={(e) => setNewPaymentMethod({
                          ...newPaymentMethod,
                          isDefault: e.target.checked
                        })}
                      />
                      Définir comme méthode par défaut
                    </label>
                  </div>

                  <div className="form-actions">
                    <Button
                      variant="outline"
                      onClick={() => setShowAddPaymentMethod(false)}
                    >
                      Annuler
                    </Button>
                    <Button onClick={handleAddPaymentMethod}>
                      Ajouter
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdWalletManager;
