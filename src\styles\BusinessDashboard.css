/* Business Dashboard - Styles Ultra-Modernes */
.business-dashboard {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem;
  background: #f8fafc;
  min-height: 100vh;
}

/* État de chargement */
.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dashboard-loading h3 {
  margin: 1rem 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
}

.dashboard-loading p {
  margin: 0;
  color: #6b7280;
}

/* En-tête du dashboard */
.dashboard-header {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h1 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
  font-size: 1.75rem;
  font-weight: 700;
}

.header-left p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.period-selector {
  position: relative;
}

.period-select {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.period-select:hover {
  border-color: #3b82f6;
}

.period-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.refresh-btn, .export-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.refresh-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.refresh-btn:hover {
  background: #e5e7eb;
}

.export-btn {
  background: #3b82f6;
  color: white;
  border: 1px solid #3b82f6;
}

.export-btn:hover {
  background: #2563eb;
}

/* Alertes */
.dashboard-alerts {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #ef4444;
}

.dashboard-alerts h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.alert-item {
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid;
  transition: all 0.2s;
}

.alert-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.alert-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.alert-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alert-title {
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}

.alert-time {
  font-size: 0.75rem;
  color: #6b7280;
}

.alert-message {
  margin: 0;
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.5;
}

.alert-action {
  align-self: flex-start;
  margin-top: 0.5rem;
}

/* Métriques */
.dashboard-metrics {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.metric-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metric-card.revenue {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.metric-card.orders {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.metric-card.products {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.metric-card.rating {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.metric-card.reviews {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.metric-card.conversion {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  color: white;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.metric-growth {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.metric-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.metric-content p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.9;
  font-weight: 500;
}

/* Contenu principal */
.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
}

/* Activité récente */
.recent-activity-card {
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.card-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
  transition: all 0.2s;
}

.activity-item:hover {
  background: #f3f4f6;
  transform: translateX(4px);
}

.activity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.activity-title {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.activity-time {
  font-size: 0.75rem;
  color: #6b7280;
  flex-shrink: 0;
}

.activity-description {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.4;
}

.activity-amount {
  display: inline-block;
  margin-top: 0.5rem;
  padding: 0.25rem 0.5rem;
  background: #dbeafe;
  color: #1e40af;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.activity-status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
  flex-shrink: 0;
}

/* Actions rapides */
.quick-actions-card {
  height: fit-content;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem 1rem;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: #374151;
}

.quick-action-btn:hover {
  background: white;
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  color: #3b82f6;
}

.quick-action-btn span {
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
}

.quick-action-btn.products:hover {
  border-color: #8b5cf6;
  color: #8b5cf6;
}

.quick-action-btn.orders:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.quick-action-btn.ai:hover {
  border-color: #f59e0b;
  color: #f59e0b;
}

.quick-action-btn.analytics:hover {
  border-color: #10b981;
  color: #10b981;
}

.quick-action-btn.ads:hover {
  border-color: #ef4444;
  color: #ef4444;
}

.quick-action-btn.settings:hover {
  border-color: #6b7280;
  color: #6b7280;
}

/* Responsive */
@media (max-width: 1024px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .business-dashboard {
    padding: 0.5rem;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: space-between;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .activity-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .activity-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.25rem;
  }
  
  .activity-time {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 1rem;
  }
  
  .dashboard-metrics {
    padding: 1rem;
  }
  
  .metric-content h3 {
    font-size: 1.5rem;
  }
  
  .quick-action-btn {
    padding: 1rem 0.75rem;
  }
}
