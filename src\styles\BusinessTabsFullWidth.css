/* Styles pour les onglets pleine largeur */
.profile-tabs-container {
  width: 100%;
  margin-bottom: 0;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
}

.business-tabs-container {
  width: 100%;
  margin: 0;
  padding: 0;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
}

.business-tabs-nav {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 0;
  margin: 0;
  flex-wrap: nowrap;
}

.business-tab {
  flex: 1 1 33.33%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 5px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  color: #6b7280;
  font-weight: 500;
  min-width: 0;
  max-width: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  overflow: visible;
  white-space: nowrap;
}

.business-tab:hover {
  color: #4b5563;
  background-color: #f9fafb;
}

.business-tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  font-weight: 600;
}

.tab-icon {
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
}

.tab-label {
  font-size: 14px;
  white-space: nowrap;
}

/* Style pour le contenu des onglets */
.profile-tab-content {
  background-color: #ffffff;
  border-radius: 0;
  padding: 25px;
  width: 100%;
  min-height: 400px;
}

/* Styles spécifiques pour l'affichage sur les écrans plus larges */
@media (min-width: 768px) {
  .profile-main-content {
    width: 100%;
    max-width: 100%;
  }
  
  .business-tab {
    padding: 25px 15px;
  }
}

/* Style pour les écrans plus petits */
@media (max-width: 640px) {
  .business-tab {
    padding: 15px 5px;
  }
  
  .tab-label {
    font-size: 12px;
  }
}
