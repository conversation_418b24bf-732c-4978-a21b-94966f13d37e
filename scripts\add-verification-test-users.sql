-- SCRIPT POUR TESTER L'AFFICHAGE DES BADGES DE VÉRIFICATION
-- Exécutez ce script dans Supabase SQL Editor

-- =====================================================
-- 1. CRÉER DES UTILISATEURS DE TEST AVEC DIFFÉRENTS STATUTS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🧪 Création d''utilisateurs de test pour les badges de vérification...';
END $$;

-- Utilisateur standard vérifié
INSERT INTO profiles (
    id, username, email, role, status, is_verified, 
    profile_picture, country, city, bio, created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'marie_verified',
    '<EMAIL>',
    'standard',
    'contributor',
    true,
    'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
    'France',
    'Paris',
    'Utilisatrice vérifiée passionnée de tech',
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- Utilisateur standard non vérifié
INSERT INTO profiles (
    id, username, email, role, status, is_verified, 
    profile_picture, country, city, bio, created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'jean_unverified',
    '<EMAIL>',
    'standard',
    'member',
    false,
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
    'France',
    'Lyon',
    'Nouvel utilisateur en cours de vérification',
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- Administrateur vérifié
INSERT INTO profiles (
    id, username, email, role, status, is_verified, 
    profile_picture, country, city, bio, created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'admin_verified',
    '<EMAIL>',
    'admin',
    'leader',
    true,
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
    'France',
    'Marseille',
    'Administrateur de la plateforme',
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- =====================================================
-- 2. CRÉER DES ENTREPRISES AVEC DIFFÉRENTS STATUTS
-- =====================================================

-- Entreprise vérifiée (compte + business)
DO $$
DECLARE
    business_user_id uuid := gen_random_uuid();
BEGIN
    -- Créer le profil utilisateur business
    INSERT INTO profiles (
        id, username, email, role, status, is_verified, 
        profile_picture, country, city, bio, created_at, updated_at
    ) VALUES (
        business_user_id,
        'techcorp_verified',
        '<EMAIL>',
        'business',
        'contributor',
        true,
        'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=150',
        'France',
        'Nice',
        'Entreprise technologique certifiée',
        NOW(),
        NOW()
    ) ON CONFLICT (email) DO NOTHING;

    -- Créer le profil business vérifié
    INSERT INTO business_profiles (
        id, business_name, business_description, business_category,
        business_status, business_verified, created_at, updated_at
    ) VALUES (
        business_user_id,
        'TechCorp Solutions',
        'Solutions technologiques innovantes pour entreprises',
        'Technology',
        'verified',
        true,
        NOW(),
        NOW()
    ) ON CONFLICT (id) DO NOTHING;
END $$;

-- Entreprise non vérifiée (compte vérifié mais business non vérifié)
DO $$
DECLARE
    business_user_id uuid := gen_random_uuid();
BEGIN
    -- Créer le profil utilisateur business
    INSERT INTO profiles (
        id, username, email, role, status, is_verified, 
        profile_picture, country, city, bio, created_at, updated_at
    ) VALUES (
        business_user_id,
        'startup_unverified',
        '<EMAIL>',
        'business',
        'member',
        true,
        'https://images.unsplash.com/photo-1549923746-c502d488b3ea?w=150',
        'France',
        'Toulouse',
        'Startup en cours de vérification',
        NOW(),
        NOW()
    ) ON CONFLICT (email) DO NOTHING;

    -- Créer le profil business non vérifié
    INSERT INTO business_profiles (
        id, business_name, business_description, business_category,
        business_status, business_verified, created_at, updated_at
    ) VALUES (
        business_user_id,
        'Startup Innovation',
        'Jeune startup pleine d''ambition',
        'Startup',
        'new',
        false,
        NOW(),
        NOW()
    ) ON CONFLICT (id) DO NOTHING;
END $$;

-- Entreprise complètement non vérifiée
DO $$
DECLARE
    business_user_id uuid := gen_random_uuid();
BEGIN
    -- Créer le profil utilisateur business
    INSERT INTO profiles (
        id, username, email, role, status, is_verified, 
        profile_picture, country, city, bio, created_at, updated_at
    ) VALUES (
        business_user_id,
        'newbiz_unverified',
        '<EMAIL>',
        'business',
        'newbie',
        false,
        'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=150',
        'France',
        'Bordeaux',
        'Nouvelle entreprise sur la plateforme',
        NOW(),
        NOW()
    ) ON CONFLICT (email) DO NOTHING;

    -- Créer le profil business non vérifié
    INSERT INTO business_profiles (
        id, business_name, business_description, business_category,
        business_status, business_verified, created_at, updated_at
    ) VALUES (
        business_user_id,
        'NewBiz Corp',
        'Nouvelle entreprise en développement',
        'Services',
        'new',
        false,
        NOW(),
        NOW()
    ) ON CONFLICT (id) DO NOTHING;
END $$;

-- =====================================================
-- 3. CRÉER UNE ALERTE POUR INFORMER DU DÉPLOIEMENT
-- =====================================================

DO $$
DECLARE
    admin_profile_id uuid;
BEGIN
    SELECT id INTO admin_profile_id FROM admin_profiles LIMIT 1;
    
    IF admin_profile_id IS NOT NULL THEN
        INSERT INTO admin_alerts (
            alert_type, 
            priority, 
            title, 
            message, 
            details, 
            created_by, 
            status
        ) VALUES (
            'feature', 
            'medium', 
            'Badges de Vérification Déployés', 
            'Le système de badges de vérification est maintenant actif sur tous les profils utilisateurs.', 
            '{"feature": "verification_badges", "locations": ["profile_banner", "user_cards", "dropdown_menu", "admin_panel"], "test_users": ["marie_verified", "jean_unverified", "admin_verified", "techcorp_verified", "startup_unverified", "newbiz_unverified"]}'::jsonb, 
            admin_profile_id, 
            'open'
        );
        
        RAISE NOTICE '✅ Alerte de déploiement créée';
    END IF;
END $$;

-- =====================================================
-- 4. RÉSUMÉ ET INSTRUCTIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '🎉 BADGES DE VÉRIFICATION DÉPLOYÉS !';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Utilisateurs de test créés:';
    RAISE NOTICE '   • marie_verified (Standard vérifié)';
    RAISE NOTICE '   • jean_unverified (Standard non vérifié)';
    RAISE NOTICE '   • admin_verified (Admin vérifié)';
    RAISE NOTICE '   • techcorp_verified (Business complètement vérifié)';
    RAISE NOTICE '   • startup_unverified (Business partiellement vérifié)';
    RAISE NOTICE '   • newbiz_unverified (Business non vérifié)';
    RAISE NOTICE '';
    RAISE NOTICE '🔍 BADGES VISIBLES DANS:';
    RAISE NOTICE '   ✅ Bannières de profil (ProfileBanner)';
    RAISE NOTICE '   ✅ Cartes utilisateur (UserCard)';
    RAISE NOTICE '   ✅ Menu déroulant utilisateur (UserDropdownMenu)';
    RAISE NOTICE '   ✅ Gestion des utilisateurs admin (UserManagement)';
    RAISE NOTICE '';
    RAISE NOTICE '🎨 TYPES DE BADGES:';
    RAISE NOTICE '   🔵 Badge bleu: Utilisateur vérifié';
    RAISE NOTICE '   🛡️ Badge bleu foncé: Entreprise vérifiée';
    RAISE NOTICE '   👑 Badge rouge: Administrateur';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 TESTEZ MAINTENANT:';
    RAISE NOTICE '1. Allez sur les profils des utilisateurs de test';
    RAISE NOTICE '2. Vérifiez les badges dans le menu utilisateur';
    RAISE NOTICE '3. Consultez la gestion des utilisateurs admin';
    RAISE NOTICE '4. Testez la vérification manuelle d''un compte';
    RAISE NOTICE '';
    RAISE NOTICE '💡 Les badges s''affichent automatiquement selon:';
    RAISE NOTICE '   - is_verified (profil utilisateur)';
    RAISE NOTICE '   - business_verified (profil business)';
    RAISE NOTICE '   - role (admin, business, standard)';
    RAISE NOTICE '';
END $$;
