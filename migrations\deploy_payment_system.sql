-- =====================================================
-- SCRIPT DE DÉPLOIEMENT COMPLET
-- Système de Paiement et d'Abonnement Customeroom
-- Version: 1.0.0
-- Date: 2024-12-19
-- =====================================================

-- Commencer une transaction pour s'assurer de la cohérence
BEGIN;

-- =====================================================
-- VÉRIFICATIONS PRÉALABLES
-- =====================================================

-- Vérifier la version de PostgreSQL (minimum 12)
DO $$
BEGIN
    IF current_setting('server_version_num')::integer < 120000 THEN
        RAISE EXCEPTION 'PostgreSQL version 12 ou supérieure requise. Version actuelle: %', 
                        current_setting('server_version');
    END IF;
    RAISE NOTICE 'Version PostgreSQL: % ✓', current_setting('server_version');
END $$;

-- Vérifier les extensions requises
DO $$
BEGIN
    -- Vérifier uuid-ossp
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'uuid-ossp') THEN
        RAISE NOTICE 'Installation de l''extension uuid-ossp...';
        CREATE EXTENSION "uuid-ossp";
    END IF;
    RAISE NOTICE 'Extension uuid-ossp: ✓';
    
    -- Vérifier pgcrypto (optionnel pour le chiffrement)
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pgcrypto') THEN
        RAISE NOTICE 'Installation de l''extension pgcrypto...';
        CREATE EXTENSION pgcrypto;
    END IF;
    RAISE NOTICE 'Extension pgcrypto: ✓';
END $$;

-- =====================================================
-- CRÉATION D'UN SCHÉMA DÉDIÉ (OPTIONNEL)
-- =====================================================

-- Créer un schéma pour le système de paiement si souhaité
-- CREATE SCHEMA IF NOT EXISTS payment_system;
-- SET search_path TO payment_system, public;

-- =====================================================
-- EXÉCUTION DES MIGRATIONS
-- =====================================================

RAISE NOTICE '🚀 Début du déploiement du système de paiement...';

-- Migration 1: Création des tables principales
RAISE NOTICE '📋 Exécution de la migration 001: Création des tables...';
\i 001_create_payment_system.sql

-- Migration 2: Procédures stockées
RAISE NOTICE '⚙️ Exécution de la migration 002: Procédures stockées...';
\i 002_payment_procedures.sql

-- Migration 3: Optimisations de performance
RAISE NOTICE '🚀 Exécution de la migration 003: Optimisations de performance...';
\i 003_payment_performance.sql

-- =====================================================
-- VÉRIFICATIONS POST-DÉPLOIEMENT
-- =====================================================

RAISE NOTICE '🔍 Vérifications post-déploiement...';

-- Vérifier que toutes les tables ont été créées
DO $$
DECLARE
    table_count INTEGER;
    expected_tables TEXT[] := ARRAY[
        'subscription_plans',
        'business_subscriptions', 
        'payment_methods',
        'payment_intents',
        'payment_receipts',
        'payment_events',
        'payment_webhooks',
        'subscription_coupons',
        'coupon_usages'
    ];
    table_name TEXT;
BEGIN
    FOREACH table_name IN ARRAY expected_tables
    LOOP
        SELECT COUNT(*) INTO table_count
        FROM information_schema.tables 
        WHERE table_name = table_name
        AND table_schema = current_schema();
        
        IF table_count = 0 THEN
            RAISE EXCEPTION 'Table manquante: %', table_name;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Toutes les tables créées: ✓';
END $$;

-- Vérifier que les plans d'abonnement ont été insérés
DO $$
DECLARE
    plan_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO plan_count FROM subscription_plans;
    
    IF plan_count < 4 THEN
        RAISE EXCEPTION 'Plans d''abonnement manquants. Trouvés: %, Attendus: 4', plan_count;
    END IF;
    
    RAISE NOTICE 'Plans d''abonnement insérés: % ✓', plan_count;
END $$;

-- Vérifier les fonctions principales
DO $$
DECLARE
    function_count INTEGER;
    expected_functions TEXT[] := ARRAY[
        'create_free_trial',
        'process_payment',
        'fail_payment',
        'cancel_subscription',
        'apply_coupon',
        'get_subscription_status',
        'generate_invoice_number',
        'can_use_coupon',
        'calculate_coupon_discount'
    ];
    function_name TEXT;
BEGIN
    FOREACH function_name IN ARRAY expected_functions
    LOOP
        SELECT COUNT(*) INTO function_count
        FROM information_schema.routines 
        WHERE routine_name = function_name
        AND routine_schema = current_schema();
        
        IF function_count = 0 THEN
            RAISE EXCEPTION 'Fonction manquante: %', function_name;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Toutes les fonctions créées: ✓';
END $$;

-- Vérifier les vues matérialisées
DO $$
DECLARE
    view_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO view_count
    FROM pg_matviews 
    WHERE schemaname = current_schema();
    
    IF view_count < 2 THEN
        RAISE EXCEPTION 'Vues matérialisées manquantes. Trouvées: %, Attendues: 2', view_count;
    END IF;
    
    RAISE NOTICE 'Vues matérialisées créées: % ✓', view_count;
END $$;

-- =====================================================
-- TESTS DE FONCTIONNEMENT
-- =====================================================

RAISE NOTICE '🧪 Tests de fonctionnement...';

-- Test 1: Génération de numéro de facture
DO $$
DECLARE
    invoice_num TEXT;
BEGIN
    SELECT generate_invoice_number() INTO invoice_num;
    
    IF invoice_num IS NULL OR NOT invoice_num ~ '^INV-\d{4}-\d{6}$' THEN
        RAISE EXCEPTION 'Format de numéro de facture invalide: %', invoice_num;
    END IF;
    
    RAISE NOTICE 'Test génération facture: % ✓', invoice_num;
END $$;

-- Test 2: Vérification des coupons
DO $$
DECLARE
    can_use BOOLEAN;
BEGIN
    -- Test avec un coupon inexistant (doit retourner false)
    SELECT can_use_coupon('INEXISTANT', uuid_generate_v4(), 'starter', 15000) INTO can_use;
    
    IF can_use THEN
        RAISE EXCEPTION 'La vérification de coupon devrait échouer pour un coupon inexistant';
    END IF;
    
    RAISE NOTICE 'Test vérification coupon: ✓';
END $$;

-- Test 3: Calcul de réduction
DO $$
DECLARE
    discount DECIMAL;
BEGIN
    -- Test avec un coupon inexistant (doit retourner 0)
    SELECT calculate_coupon_discount('INEXISTANT', 15000) INTO discount;
    
    IF discount != 0 THEN
        RAISE EXCEPTION 'Le calcul de réduction devrait retourner 0 pour un coupon inexistant';
    END IF;
    
    RAISE NOTICE 'Test calcul réduction: ✓';
END $$;

-- =====================================================
-- DONNÉES DE TEST (OPTIONNEL)
-- =====================================================

-- Insérer quelques coupons de test
INSERT INTO subscription_coupons (
    id, code, name, description, discount_type, discount_value,
    usage_limit, valid_from, valid_until, is_active
) VALUES 
(
    'welcome10', 'WELCOME10', 'Bienvenue 10%', 
    'Réduction de 10% pour les nouveaux clients',
    'percentage', 10.00, 100,
    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days',
    true
),
(
    'save5000', 'SAVE5000', 'Économie 5000 F CFA',
    'Réduction fixe de 5000 F CFA',
    'fixed_amount', 5000.00, 50,
    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '60 days',
    true
) ON CONFLICT (id) DO NOTHING;

RAISE NOTICE 'Coupons de test insérés: ✓';

-- =====================================================
-- CONFIGURATION DES PERMISSIONS
-- =====================================================

-- Créer un rôle pour l'application
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'customeroom_app') THEN
        CREATE ROLE customeroom_app LOGIN;
        RAISE NOTICE 'Rôle customeroom_app créé';
    END IF;
END $$;

-- Accorder les permissions nécessaires
GRANT USAGE ON SCHEMA public TO customeroom_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO customeroom_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO customeroom_app;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO customeroom_app;

-- Permissions pour les vues matérialisées
GRANT SELECT ON monthly_revenue_stats TO customeroom_app;
GRANT SELECT ON subscription_metrics TO customeroom_app;

RAISE NOTICE 'Permissions accordées: ✓';

-- =====================================================
-- FINALISATION
-- =====================================================

-- Rafraîchir les statistiques
ANALYZE;

-- Rafraîchir les vues matérialisées
SELECT refresh_payment_stats();

-- Valider la transaction
COMMIT;

-- =====================================================
-- RAPPORT FINAL
-- =====================================================

DO $$
DECLARE
    table_count INTEGER;
    function_count INTEGER;
    view_count INTEGER;
    plan_count INTEGER;
    coupon_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO table_count FROM information_schema.tables 
    WHERE table_schema = current_schema() AND table_name LIKE '%payment%' OR table_name LIKE '%subscription%' OR table_name LIKE '%coupon%';
    
    SELECT COUNT(*) INTO function_count FROM information_schema.routines 
    WHERE routine_schema = current_schema() AND routine_type = 'FUNCTION';
    
    SELECT COUNT(*) INTO view_count FROM pg_matviews 
    WHERE schemaname = current_schema();
    
    SELECT COUNT(*) INTO plan_count FROM subscription_plans;
    SELECT COUNT(*) INTO coupon_count FROM subscription_coupons;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 DÉPLOIEMENT TERMINÉ AVEC SUCCÈS!';
    RAISE NOTICE '=====================================';
    RAISE NOTICE 'Tables créées: %', table_count;
    RAISE NOTICE 'Fonctions créées: %', function_count;
    RAISE NOTICE 'Vues matérialisées: %', view_count;
    RAISE NOTICE 'Plans d''abonnement: %', plan_count;
    RAISE NOTICE 'Coupons de test: %', coupon_count;
    RAISE NOTICE '';
    RAISE NOTICE '📋 PROCHAINES ÉTAPES:';
    RAISE NOTICE '1. Configurer les variables d''environnement de l''application';
    RAISE NOTICE '2. Tester les endpoints de paiement';
    RAISE NOTICE '3. Configurer les webhooks des processeurs de paiement';
    RAISE NOTICE '4. Planifier les tâches de maintenance (daily_maintenance)';
    RAISE NOTICE '5. Configurer la surveillance (detect_payment_anomalies)';
    RAISE NOTICE '';
    RAISE NOTICE '🔗 FONCTIONS UTILES:';
    RAISE NOTICE '- SELECT * FROM get_subscription_status(''<business_id>'');';
    RAISE NOTICE '- SELECT * FROM create_free_trial(''<business_id>'');';
    RAISE NOTICE '- SELECT * FROM daily_maintenance();';
    RAISE NOTICE '- SELECT * FROM detect_payment_anomalies();';
    RAISE NOTICE '';
END $$;
