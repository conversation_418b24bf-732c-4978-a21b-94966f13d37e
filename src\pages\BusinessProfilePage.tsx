import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { usePosts } from '../context/PostsContext';
import { useFollow } from '../context/FollowContext';
import { useSearchParams } from 'react-router-dom';
import { MapPin, Calendar, Link as LinkIcon, Package, Wallet, ShoppingBag, TrendingUp, Star, Award, Edit, Brain, Home, MessageSquare, Megaphone, Users, UserPlus, CreditCard } from 'lucide-react';
import Avatar from '../components/ui/Avatar';
import Button from '../components/ui/Button';
import Card, { CardBody } from '../components/ui/Card';
import EditBusinessProfileForm from '../components/business/EditBusinessProfileForm';
import BusinessRankingsContent from '../components/business/BusinessRankingsContent';
import BusinessAIAdvisor from '../components/business/BusinessAIAdvisor';

import BusinessDashboard from '../components/business/BusinessDashboard';
import BusinessOrdersManager from '../components/business/BusinessOrdersManager';
import BusinessSalesManager from '../components/business/BusinessSalesManager';
import BusinessProfileManager from '../components/business/BusinessProfileManager';
import BusinessReviewsManager from '../components/business/BusinessReviewsManager';
import BusinessAdsManager from '../components/business/BusinessAdsManager';
import SubscriptionBadgeSimple from '../components/business/SubscriptionBadgeSimple';
import BusinessSubscription from '../components/business/BusinessSubscription';
import BusinessStatusCriteria from '../components/business/BusinessStatusCriteria';
import { BusinessProfileService } from '../services/businessProfileService';
import { UserRole, BusinessStatus, IBusinessUser } from '../types';

const BusinessProfilePage: React.FC = () => {
  const { currentUser } = useAuth();
  const { getPostsByBusiness } = usePosts();
  const { getFollowersDetails, getFollowingDetails } = useFollow();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(() => {
    // Lire le paramètre tab de l'URL, par défaut 'dashboard'
    return searchParams.get('tab') || 'dashboard';
  });
  const [showEditForm, setShowEditForm] = useState(false);
  const [coverImageError, setCoverImageError] = useState(false);
  const [followersCount, setFollowersCount] = useState(0);
  const [followingCount, setFollowingCount] = useState(0);
  const [loadingStats, setLoadingStats] = useState(true);

  // Mettre à jour l'onglet actif quand l'URL change
  useEffect(() => {
    const tabFromUrl = searchParams.get('tab');
    if (tabFromUrl && tabFromUrl !== activeTab) {
      setActiveTab(tabFromUrl);
    }
  }, [searchParams, activeTab]);

  // Récupérer les statistiques d'abonnés
  useEffect(() => {
    const fetchFollowStats = async () => {
      if (currentUser && currentUser.role === UserRole.BUSINESS) {
        try {
          setLoadingStats(true);
          const [followers, following] = await Promise.all([
            getFollowersDetails(currentUser.id),
            getFollowingDetails(currentUser.id)
          ]);

          setFollowersCount(followers.length);
          setFollowingCount(following.length);
        } catch (error) {
          console.error('Erreur lors de la récupération des statistiques de suivi:', error);
        } finally {
          setLoadingStats(false);
        }
      }
    };

    fetchFollowStats();
  }, [currentUser, getFollowersDetails, getFollowingDetails]);

  // Afficher la page même si pas connecté en tant qu'entreprise pour les tests
  if (!currentUser || currentUser.role !== UserRole.BUSINESS) {
    return (
      <div className="w-full max-w-5xl mx-auto px-4 py-6">


        <Card>
          <CardBody className="text-center py-12">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Accès Entreprise Requis
            </h2>
            <p className="text-gray-600 mb-6">
              Cette page est réservée aux comptes entreprise. Veuillez vous connecter avec un compte entreprise.
            </p>
          </CardBody>
        </Card>
      </div>
    );
  }

  const businessUser = currentUser as IBusinessUser;
  const businessPosts = getPostsByBusiness(businessUser.businessName);

  // Écouter les événements de changement d'onglet depuis le dashboard
  useEffect(() => {
    const handleTabChange = (event: CustomEvent) => {
      setActiveTab(event.detail);
    };

    window.addEventListener('changeBusinessTab', handleTabChange as EventListener);

    return () => {
      window.removeEventListener('changeBusinessTab', handleTabChange as EventListener);
    };
  }, []);

  const tabs = [
    { id: 'dashboard', label: 'Tableau de bord', icon: <Home size={20} /> },
    { id: 'ai-recommendations', label: 'IA Conseiller', icon: <Brain size={20} /> },
    { id: 'reviews', label: 'Mes avis', icon: <MessageSquare size={20} /> },
    { id: 'subscription', label: 'Mon abonnement', icon: <CreditCard size={20} /> },
    { id: 'wallet', label: 'Portefeuille', icon: <Wallet size={20} /> },
    { id: 'status-criteria', label: 'Critères de Statut', icon: <Award size={20} /> },
  ];

  const getStatusBadgeColor = (status: BusinessStatus) => {
    switch (status) {
      case BusinessStatus.NEW:
        return 'bg-blue-100 text-blue-800';
      case BusinessStatus.ACTIVE:
        return 'bg-green-100 text-green-800';
      case BusinessStatus.VERIFIED:
        return 'bg-purple-100 text-purple-800';
      case BusinessStatus.PREMIUM:
        return 'bg-yellow-100 text-yellow-800';
      case BusinessStatus.FEATURED:
        return 'bg-orange-100 text-orange-800';
      case BusinessStatus.PARTNER:
        return 'bg-red-100 text-red-800';
      case BusinessStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800';
      case BusinessStatus.INACTIVE:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: BusinessStatus) => {
    switch (status) {
      case BusinessStatus.NEW:
        return 'Nouveau';
      case BusinessStatus.ACTIVE:
        return 'Actif';
      case BusinessStatus.VERIFIED:
        return 'Vérifié';
      case BusinessStatus.PREMIUM:
        return 'Premium';
      case BusinessStatus.FEATURED:
        return 'Featured';
      case BusinessStatus.PARTNER:
        return 'Partenaire';
      case BusinessStatus.PENDING:
        return 'En attente de vérification';
      case BusinessStatus.INACTIVE:
        return 'Inactif';
      default:
        return 'Inconnu';
    }
  };

  const handleEditProfile = () => {
    setShowEditForm(true);
  };

  const handleCloseEditForm = () => {
    setShowEditForm(false);
  };

  const handleProfileUpdate = async (updatedData: Partial<IBusinessUser>) => {
    try {
      const result = await BusinessProfileService.updateBusinessProfile(businessUser.id, updatedData);

      if (result.success) {
        console.log('Profil mis à jour avec succès');
        // Ici, vous pourriez recharger les données ou mettre à jour le contexte
        // Pour l'instant, on ferme juste le formulaire
        setShowEditForm(false);
      } else {
        console.error('Erreur lors de la mise à jour:', result.error);
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil:', error);
    }
  };

  const handleCoverError = () => {
    console.log('Cover image failed to load, using gradient fallback');
    setCoverImageError(true);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <BusinessDashboard businessId={businessUser.id} />;
      case 'ai-recommendations':
        return <BusinessAIAdvisor businessId={businessUser.id} />;
      case 'reviews':
        return <BusinessReviewsManager businessId={businessUser.id} businessName={businessUser.businessName} />;

      case 'subscription':
        return <BusinessSubscription />;

      case 'wallet':
        return (
          <div className="space-y-6">
            <Card>
              <CardBody>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Solde actuel</h3>
                    <p className="text-3xl font-bold text-gray-900 mt-2">{businessUser.wallet?.toLocaleString()} F CFA</p>
                  </div>
                  <Button variant="outline">Retirer des fonds</Button>
                </div>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Historique des transactions</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between py-2 border-b">
                    <div>
                      <p className="font-medium text-gray-900">Vente #12345</p>
                      <p className="text-sm text-gray-500">12 mars 2025</p>
                    </div>
                    <span className="text-green-600 font-medium">+25 000 F CFA</span>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        );

      case 'status-criteria':
        return <BusinessStatusCriteria businessUser={businessUser} />;

      default:
        return (
          <div className="text-center py-8">
            <h3 className="text-lg font-medium text-gray-900">Contenu en développement</h3>
            <p className="text-gray-500 mt-2">Cette fonctionnalité sera bientôt disponible.</p>
          </div>
        );
    }
  };

  return (
    <div className="w-full max-w-5xl mx-auto px-4 py-6">

      {/* Profile Header */}
      <Card className="mb-6">
        <CardBody className="p-0">
          <div className="relative">
            {/* Cover Image - Clean without text */}
            <div className="h-32 rounded-t-lg overflow-hidden">
              {!coverImageError && businessUser.coverPhotoUrl ? (
                <img
                  src={businessUser.coverPhotoUrl}
                  alt="Photo de couverture"
                  className="w-full h-full object-cover"
                  onError={handleCoverError}
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500" />
              )}
            </div>

            {/* Profile Content - All below cover image */}
            <div className="px-6 py-6">
              {/* Profile Picture Row */}
              <div className="flex flex-col md:flex-row items-start md:items-center justify-between -mt-12 md:-mt-16 mb-6">
                <div className="flex items-end space-x-4">
                  <Avatar
                    src={businessUser.profilePicture}
                    alt={businessUser.businessName}
                    size="xl"
                    className="ring-4 ring-white"
                  />
                </div>

                {/* Action Buttons */}
                <div className="mt-4 md:mt-0">
                  <Button
                    variant="primary"
                    leftIcon={<Edit size={16} />}
                    onClick={handleEditProfile}
                  >
                    Éditer profil
                  </Button>
                </div>
              </div>

              {/* Business Info Section */}
              <div className="space-y-4">
                {/* Name and Status */}
                <div>
                  <div className="flex items-center space-x-3 mb-2">
                    <h1 className="text-2xl font-bold text-gray-900">{businessUser.businessName}</h1>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(businessUser.businessStatus || BusinessStatus.NEW)}`}>
                      {getStatusLabel(businessUser.businessStatus || BusinessStatus.NEW)}
                    </span>
                    <SubscriptionBadgeSimple
                      businessId={businessUser.id}
                      size="md"
                      showDetails={true}
                    />
                  </div>
                  <p className="text-gray-600 text-lg">{businessUser.businessDescription}</p>
                </div>

                {/* Business Meta Info */}
                <div className="flex flex-wrap gap-6 text-sm text-gray-600">
                  <span className="flex items-center">
                    <MapPin size={16} className="mr-2" />
                    Paris, France
                  </span>
                  <span className="flex items-center">
                    <LinkIcon size={16} className="mr-2" />
                    customeroom.com/business/{businessUser.businessName.toLowerCase()}
                  </span>
                  <span className="flex items-center">
                    <Calendar size={16} className="mr-2" />
                    Membre depuis {new Date(businessUser.createdAt).toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' })}
                  </span>
                  <span className="flex items-center">
                    <ShoppingBag size={16} className="mr-2" />
                    {businessUser.salesCount} ventes
                  </span>
                  <span className="flex items-center">
                    <Users size={16} className="mr-2" />
                    {loadingStats ? '...' : followersCount} abonnés
                  </span>
                  <span className="flex items-center">
                    <UserPlus size={16} className="mr-2" />
                    {loadingStats ? '...' : followingCount} abonnements
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Business Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardBody>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <Package size={20} />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">{businessUser.catalog?.length || 0}</h3>
                <p className="text-sm text-gray-500">Produits</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <ShoppingBag size={20} />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">{businessUser.salesCount}</h3>
                <p className="text-sm text-gray-500">Ventes</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                <Star size={20} />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">{businessPosts.length}</h3>
                <p className="text-sm text-gray-500">Avis clients</p>
              </div>
            </div>
          </CardBody>
        </Card>



      </div>

      {/* Tabs Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                flex items-center px-1 py-4 text-sm font-medium border-b-2 whitespace-nowrap
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}
              `}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {renderTabContent()}

      {/* Modal d'édition du profil */}
      {showEditForm && (
        <EditBusinessProfileForm
          businessData={businessUser}
          onClose={handleCloseEditForm}
          onUpdate={handleProfileUpdate}
        />
      )}
    </div>
  );
};

export default BusinessProfilePage;