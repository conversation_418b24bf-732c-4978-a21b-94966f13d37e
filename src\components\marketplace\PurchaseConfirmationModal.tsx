import React, { useState } from 'react';
import { 
  X, 
  ShoppingCart, 
  CreditCard, 
  MapPin, 
  Package, 
  CheckCircle,
  AlertCircle,
  Loader
} from 'lucide-react';
import { IProduct } from '../../types';
import { useMarketplace } from '../../context/MarketplaceContext';
import { formatAmount } from '../../utils/formatUtils';
import Button from '../ui/Button';

interface PurchaseConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: IProduct | null;
  quantity: number;
  onPurchaseComplete: (orderId: string) => void;
}

const PurchaseConfirmationModal: React.FC<PurchaseConfirmationModalProps> = ({
  isOpen,
  onClose,
  product,
  quantity,
  onPurchaseComplete
}) => {
  const { createOrder } = useMarketplace();
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'confirm' | 'processing' | 'success' | 'error'>('confirm');
  const [error, setError] = useState<string | null>(null);
  const [orderId, setOrderId] = useState<string | null>(null);

  const [shippingInfo, setShippingInfo] = useState({
    name: '',
    address: '',
    city: 'Abidjan',
    phone: ''
  });

  const [paymentMethod, setPaymentMethod] = useState<'orange_money' | 'mtn_money' | 'visa'>('orange_money');

  if (!isOpen || !product) return null;

  const totalPrice = product.price * quantity;
  const shippingCost = 2000; // Frais de livraison fixes
  const finalTotal = totalPrice + shippingCost;

  const handlePurchase = async () => {
    if (!shippingInfo.name || !shippingInfo.address || !shippingInfo.phone) {
      setError('Veuillez remplir toutes les informations de livraison');
      return;
    }

    setLoading(true);
    setStep('processing');
    setError(null);

    try {
      // Simuler le traitement du paiement
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Créer la commande (ceci déclenchera automatiquement le suivi)
      await createOrder(product.id, quantity);

      // Simuler la récupération de l'ID de commande
      // Dans une vraie application, createOrder retournerait l'ID
      const mockOrderId = `ORD-${Date.now()}`;
      setOrderId(mockOrderId);
      setStep('success');

      // Notifier le parent
      setTimeout(() => {
        onPurchaseComplete(mockOrderId);
      }, 2000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la création de la commande');
      setStep('error');
    } finally {
      setLoading(false);
    }
  };

  const renderConfirmStep = () => (
    <div className="space-y-6">
      {/* Résumé du produit */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center space-x-4">
          <img
            src={product.imageUrl || 'https://via.placeholder.com/80x80'}
            alt={product.name}
            className="w-16 h-16 object-cover rounded-lg"
          />
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900">{product.name}</h3>
            <p className="text-sm text-gray-600">Quantité: {quantity}</p>
            <p className="text-lg font-bold text-blue-600">{formatAmount(totalPrice)}</p>
          </div>
        </div>
      </div>

      {/* Informations de livraison */}
      <div>
        <h4 className="font-medium text-gray-900 mb-3 flex items-center">
          <MapPin className="w-4 h-4 mr-2" />
          Informations de livraison
        </h4>
        <div className="space-y-3">
          <input
            type="text"
            placeholder="Nom complet"
            value={shippingInfo.name}
            onChange={(e) => setShippingInfo(prev => ({ ...prev, name: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <input
            type="text"
            placeholder="Adresse de livraison"
            value={shippingInfo.address}
            onChange={(e) => setShippingInfo(prev => ({ ...prev, address: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <div className="grid grid-cols-2 gap-3">
            <select
              value={shippingInfo.city}
              onChange={(e) => setShippingInfo(prev => ({ ...prev, city: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="Abidjan">Abidjan</option>
              <option value="Bouaké">Bouaké</option>
              <option value="Daloa">Daloa</option>
              <option value="Yamoussoukro">Yamoussoukro</option>
            </select>
            <input
              type="tel"
              placeholder="Téléphone"
              value={shippingInfo.phone}
              onChange={(e) => setShippingInfo(prev => ({ ...prev, phone: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Méthode de paiement */}
      <div>
        <h4 className="font-medium text-gray-900 mb-3 flex items-center">
          <CreditCard className="w-4 h-4 mr-2" />
          Méthode de paiement
        </h4>
        <div className="space-y-2">
          {[
            { id: 'orange_money', name: 'Orange Money', icon: '🟠' },
            { id: 'mtn_money', name: 'MTN Money', icon: '🟡' },
            { id: 'visa', name: 'Carte Visa', icon: '💳' }
          ].map((method) => (
            <label key={method.id} className="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
              <input
                type="radio"
                name="paymentMethod"
                value={method.id}
                checked={paymentMethod === method.id}
                onChange={(e) => setPaymentMethod(e.target.value as any)}
                className="mr-3"
              />
              <span className="mr-2">{method.icon}</span>
              <span>{method.name}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Résumé des coûts */}
      <div className="bg-blue-50 rounded-lg p-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Sous-total</span>
            <span>{formatAmount(totalPrice)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Frais de livraison</span>
            <span>{formatAmount(shippingCost)}</span>
          </div>
          <div className="border-t border-blue-200 pt-2">
            <div className="flex justify-between font-semibold">
              <span>Total</span>
              <span className="text-blue-600">{formatAmount(finalTotal)}</span>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-center">
            <AlertCircle className="w-4 h-4 text-red-500 mr-2" />
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        </div>
      )}
    </div>
  );

  const renderProcessingStep = () => (
    <div className="text-center py-8">
      <Loader className="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">Traitement en cours...</h3>
      <p className="text-gray-600">Nous traitons votre commande et initialisons le suivi.</p>
    </div>
  );

  const renderSuccessStep = () => (
    <div className="text-center py-8">
      <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
      <h3 className="text-xl font-semibold text-gray-900 mb-2">Commande confirmée !</h3>
      <p className="text-gray-600 mb-4">
        Votre commande #{orderId} a été créée avec succès.
        <br />
        Le suivi automatique a été activé.
      </p>
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-left">
        <h4 className="font-medium text-green-900 mb-2">Prochaines étapes :</h4>
        <ul className="text-sm text-green-700 space-y-1">
          <li>• Vous recevrez une notification de confirmation</li>
          <li>• Le suivi en temps réel est maintenant actif</li>
          <li>• Vous serez notifié à chaque étape de livraison</li>
        </ul>
      </div>
    </div>
  );

  const renderErrorStep = () => (
    <div className="text-center py-8">
      <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
      <h3 className="text-xl font-semibold text-gray-900 mb-2">Erreur</h3>
      <p className="text-gray-600 mb-4">{error}</p>
      <Button variant="outline" onClick={() => setStep('confirm')}>
        Réessayer
      </Button>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <ShoppingCart className="w-5 h-5 mr-2" />
            {step === 'confirm' ? 'Confirmer l\'achat' : 
             step === 'processing' ? 'Traitement...' :
             step === 'success' ? 'Commande confirmée' : 'Erreur'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'confirm' && renderConfirmStep()}
          {step === 'processing' && renderProcessingStep()}
          {step === 'success' && renderSuccessStep()}
          {step === 'error' && renderErrorStep()}
        </div>

        {/* Footer */}
        {step === 'confirm' && (
          <div className="p-6 border-t border-gray-100">
            <div className="flex space-x-3">
              <Button variant="outline" onClick={onClose} fullWidth>
                Annuler
              </Button>
              <Button 
                variant="primary" 
                onClick={handlePurchase}
                disabled={loading}
                fullWidth
              >
                {loading ? 'Traitement...' : `Confirmer - ${formatAmount(finalTotal)}`}
              </Button>
            </div>
          </div>
        )}

        {step === 'success' && (
          <div className="p-6 border-t border-gray-100">
            <Button variant="primary" onClick={onClose} fullWidth>
              Voir mes commandes
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default PurchaseConfirmationModal;
